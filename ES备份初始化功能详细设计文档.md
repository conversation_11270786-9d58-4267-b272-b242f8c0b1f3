# ES备份初始化功能详细设计文档

## 文档概述

本文档为ES备份初始化功能的详细设计文档，基于现有的ES备份创建和绑定功能，新增ES备份查询列表、状态验证和初始化功能。

**文档创建时间**: 2025-09-23 13:40:05  
**设计人员**: hongdong.xie  
**目标读者**: AI助手、团队新人、后端开发工程师、前端开发工程师

## 1. 需求分析

### 1.1 业务背景
测试人员在创建ES备份后，缺乏统一的管理界面来查看备份状态和执行初始化操作。需要提供完整的ES备份管理功能，包括备份查询、状态验证和环境初始化。

### 1.2 核心需求
1. **ES备份查询列表**: 支持按条件查询备份信息，支持分页显示
2. **备份详情查看**: 查看备份的详细信息和Jenkins执行详情
3. **备份状态验证**: 验证运行中备份的实际状态
4. **备份初始化**: 将指定备份初始化到目标环境

### 1.3 功能边界
- 基于现有ES备份功能进行扩展
- 复用现有的环境管理和业务管理接口
- 集成Jenkins流水线进行备份初始化

## 2. 系统架构设计

### 2.1 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端Vue页面    │────│  Django REST API │────│  Elasticsearch  │
│                 │    │                 │    │                 │
│ - 备份查询列表   │    │ - ESBackupView  │    │ - Snapshot API  │
│ - 备份详情弹框   │    │ - ESBackupService│   │ - Status Check  │
│ - 状态验证功能   │    │ - Jenkins API   │    │                 │
│ - 初始化弹框     │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                │
                       ┌─────────────────┐    ┌─────────────────┐
                       │   MySQL数据库    │    │  Jenkins流水线   │
                       │                 │    │                 │
                       │ - EsMgtDumpInfo │    │ - test_es_init  │
                       │ - EsMgtDumpBizBind│   │                 │
                       └─────────────────┘    └─────────────────┘
```

### 2.2 模块划分

#### 2.2.1 后端模块扩展
- **控制层**: 在现有`ESBackupView`中新增查询列表、详情、验证、初始化接口
- **服务层**: 在现有`ESBackupService`中新增相关业务逻辑
- **Jenkins集成**: 新增Jenkins流水线触发接口

#### 2.2.2 前端模块扩展
- **新增页面**: ES备份查询列表页面
- **弹框组件**: 详情弹框、初始化弹框
- **API扩展**: 新增相关API接口封装

## 3. 数据库设计

### 3.1 现有表结构复用
基于现有的`EsMgtDumpInfo`和`EsMgtDumpBizBind`表，无需新增表结构。

### 3.2 状态字段扩展
在现有状态基础上，可能需要新增状态：
- `RUNNING`: 运行中（对应ES的IN_PROGRESS状态）
- `INITIALIZING`: 初始化中

## 4. 后端详细设计

### 4.1 API接口设计

#### 4.1.1 备份查询列表接口
**文件位置**: `es_mgt/api/es_backup_view.py`

```python
@action(methods=['get'], detail=False, url_path='query')
def query_backups(self, request):
    """
    查询ES备份列表
    
    GET /spider/es_mgt/backups/query/
    
    Query Parameters:
    - suite_code: 环境编码（可选）
    - source_es_module_name: ES模块名称（可选）
    - status: 备份状态（可选）
    - creator: 创建人（可选）
    - page: 页码（默认1）
    - page_size: 每页大小（默认10）
    """
```

#### 4.1.2 备份详情接口
**文件位置**: `es_mgt/api/es_backup_view.py`

```python
@action(methods=['get'], detail=True, url_path='detail')
def get_backup_detail(self, request, pk=None):
    """
    获取备份详情
    
    GET /spider/es_mgt/backups/{id}/detail/
    """
```

#### 4.1.3 备份状态验证接口
**文件位置**: `es_mgt/api/es_backup_view.py`

```python
@action(methods=['post'], detail=False, url_path='verify')
def verify_backup_status(self, request):
    """
    验证备份状态
    
    POST /spider/es_mgt/backups/verify/
    
    Request Body:
    {
        "es_dump_name": "备份名称"
    }
    """
```

#### 4.1.4 备份初始化接口
**文件位置**: `es_mgt/api/es_backup_view.py`

```python
@action(methods=['post'], detail=False, url_path='initialize')
def initialize_backup(self, request):
    """
    初始化备份到环境
    
    POST /spider/es_mgt/backups/initialize/
    
    Request Body:
    {
        "es_dump_name": "备份名称",
        "biz_code": "业务编码",
        "biz_test_iter_br": "业务分支",
        "target_suite_code": "目标环境编码",
        "operator": "操作人"
    }
    """
```

### 4.2 服务层设计

#### 4.2.1 备份查询服务
**文件位置**: `es_mgt/service/es_backup_service.py`

```python
def query_backups_with_pagination(self, suite_code=None, source_es_module_name=None, 
                                 status=None, creator=None, page=1, page_size=10):
    """
    分页查询备份列表
    
    Args:
        suite_code: 环境编码
        source_es_module_name: ES模块名称
        status: 备份状态
        creator: 创建人
        page: 页码
        page_size: 每页大小
        
    Returns:
        {
            'total': 总数,
            'page': 当前页,
            'page_size': 每页大小,
            'data': 备份列表
        }
    """
```

#### 4.2.2 Jenkins集成服务
**文件位置**: `es_mgt/service/jenkins_service.py`（新增文件）

```python
class JenkinsService:
    """Jenkins集成服务"""
    
    def trigger_es_init_pipeline(self, es_dump_name, biz_code, biz_test_iter_br, 
                                target_suite_code, operator):
        """
        触发test_es_init流水线
        
        Args:
            es_dump_name: 备份名称
            biz_code: 业务编码
            biz_test_iter_br: 业务分支
            target_suite_code: 目标环境编码
            operator: 操作人
            
        Returns:
            (是否成功, 消息, Jenkins构建ID)
        """
```

## 5. 前端详细设计

### 5.1 项目结构扩展

```
deploy_website/src/
├── spider-view/
│   └── biz-iter-mgt/
│       ├── es-backup-mgt.vue          # 现有主页面（需修改）
│       ├── es-backup-query.vue        # 新增：备份查询列表页面
│       └── es-backup/
│           ├── create-backup.vue      # 现有：备份创建组件
│           ├── bind-backup.vue        # 现有：备份绑定组件
│           ├── backup-detail-modal.vue # 新增：备份详情弹框
│           └── backup-init-modal.vue   # 新增：备份初始化弹框
├── spider-api/
│   └── es-backup.js                   # 扩展API接口
└── router/
    └── routers.js                     # 新增路由配置
```

### 5.2 API接口扩展

**文件位置**: `deploy_website/src/spider-api/es-backup.js`

```javascript
// 查询备份列表（分页）
export const queryBackupList = (params) => {
    return spider_axios.request({
        url: 'spider/es_mgt/backups/query/',
        method: 'get',
        params
    })
}

// 获取备份详情
export const getBackupDetail = (id) => {
    return spider_axios.request({
        url: `spider/es_mgt/backups/${id}/detail/`,
        method: 'get'
    })
}

// 验证备份状态
export const verifyBackupStatus = (data) => {
    return spider_axios.request({
        url: 'spider/es_mgt/backups/verify/',
        method: 'post',
        data
    })
}

// 初始化备份
export const initializeBackup = (data) => {
    return spider_axios.request({
        url: 'spider/es_mgt/backups/initialize/',
        method: 'post',
        data
    })
}
```

### 5.3 路由配置扩展

**文件位置**: `deploy_website/src/router/routers.js`

在现有的业务迭代管理模块中新增路由：

```javascript
{
    path: 'es_backup_query',
    name: 'ES备份查询',
    meta: {
        icon: 'ios-search-outline',
        title: 'ES备份查询'
    },
    component: () => import('@/spider-view/biz-iter-mgt/es-backup-query')
}
```

## 6. 核心组件详细设计

### 6.1 ES备份查询列表页面

**文件位置**: `deploy_website/src/spider-view/biz-iter-mgt/es-backup-query.vue`

#### 6.1.1 页面结构设计
```vue
<template>
    <div class="es-backup-query">
        <Card>
            <p slot="title">
                <Icon type="ios-search-outline"></Icon>
                ES备份查询
            </p>

            <!-- 查询条件 -->
            <Form ref="queryForm" :model="queryForm" inline class="query-form">
                <FormItem label="环境编码">
                    <Select v-model="queryForm.suite_code" placeholder="请选择环境" clearable style="width: 150px">
                        <Option v-for="env in envList" :key="env.value" :value="env.value">
                            {{ env.label }}
                        </Option>
                    </Select>
                </FormItem>

                <FormItem label="ES模块">
                    <Select v-model="queryForm.source_es_module_name" placeholder="请选择ES模块" clearable style="width: 150px">
                        <Option v-for="module in esModuleList" :key="module.value" :value="module.value">
                            {{ module.label }}
                        </Option>
                    </Select>
                </FormItem>

                <FormItem label="状态">
                    <Select v-model="queryForm.status" placeholder="请选择状态" clearable style="width: 120px">
                        <Option value="PENDING">等待中</Option>
                        <Option value="RUNNING">运行中</Option>
                        <Option value="SUCCESS">成功</Option>
                        <Option value="FAILED">失败</Option>
                    </Select>
                </FormItem>

                <FormItem label="创建人">
                    <Input v-model="queryForm.creator" placeholder="请输入创建人" clearable style="width: 120px" />
                </FormItem>

                <FormItem>
                    <Button type="primary" @click="handleQuery" :loading="queryLoading">
                        <Icon type="ios-search" />
                        查询
                    </Button>
                    <Button @click="handleReset" style="margin-left: 8px">重置</Button>
                </FormItem>
            </Form>

            <!-- 数据表格 -->
            <Table
                :columns="tableColumns"
                :data="tableData"
                :loading="tableLoading"
                stripe
                border
                class="backup-table"
            >
                <template slot-scope="{ row }" slot="status">
                    <Tag :color="getStatusColor(row.status)">{{ getStatusText(row.status) }}</Tag>
                </template>

                <template slot-scope="{ row }" slot="action">
                    <Button type="info" size="small" @click="showDetail(row)" style="margin-right: 5px">
                        详情
                    </Button>
                    <Button
                        type="warning"
                        size="small"
                        @click="verifyStatus(row)"
                        :loading="row.verifying"
                        :disabled="row.status !== 'RUNNING'"
                        style="margin-right: 5px"
                    >
                        验证
                    </Button>
                    <Button
                        type="primary"
                        size="small"
                        @click="showInitModal(row)"
                        :disabled="row.status !== 'SUCCESS'"
                    >
                        初始化
                    </Button>
                </template>
            </Table>

            <!-- 分页 -->
            <div class="pagination-wrapper">
                <Page
                    :total="pagination.total"
                    :current="pagination.page"
                    :page-size="pagination.pageSize"
                    @on-change="handlePageChange"
                    @on-page-size-change="handlePageSizeChange"
                    show-sizer
                    show-elevator
                    show-total
                />
            </div>
        </Card>

        <!-- 备份详情弹框 -->
        <BackupDetailModal
            ref="detailModal"
            :visible="detailModalVisible"
            :backup-info="selectedBackup"
            @close="detailModalVisible = false"
        />

        <!-- 备份初始化弹框 -->
        <BackupInitModal
            ref="initModal"
            :visible="initModalVisible"
            :backup-info="selectedBackup"
            @close="initModalVisible = false"
            @success="handleInitSuccess"
        />
    </div>
</template>
```

#### 6.1.2 组件逻辑设计
```javascript
<script>
import { queryBackupList, verifyBackupStatus } from '@/spider-api/es-backup'
import { getEnvList } from '@/spider-api/env-mgt'
import BackupDetailModal from './es-backup/backup-detail-modal'
import BackupInitModal from './es-backup/backup-init-modal'

export default {
    name: 'ESBackupQuery',
    components: {
        BackupDetailModal,
        BackupInitModal
    },
    data() {
        return {
            queryForm: {
                suite_code: '',
                source_es_module_name: '',
                status: '',
                creator: ''
            },
            tableColumns: [
                {
                    title: '备份名称',
                    key: 'es_dump_name',
                    width: 300,
                    ellipsis: true,
                    tooltip: true
                },
                {
                    title: '环境编码',
                    key: 'suite_code',
                    width: 120
                },
                {
                    title: 'ES模块',
                    key: 'source_es_module_name',
                    width: 150
                },
                {
                    title: '状态',
                    key: 'status',
                    width: 100,
                    slot: 'status'
                },
                {
                    title: '索引数量',
                    key: 'index_count',
                    width: 100
                },
                {
                    title: '创建人',
                    key: 'creator',
                    width: 100
                },
                {
                    title: '创建时间',
                    key: 'create_time',
                    width: 160,
                    render: (h, params) => {
                        return h('span', this.formatDateTime(params.row.create_time))
                    }
                },
                {
                    title: '备注',
                    key: 'remark',
                    ellipsis: true,
                    tooltip: true
                },
                {
                    title: '操作',
                    key: 'action',
                    width: 200,
                    slot: 'action',
                    fixed: 'right'
                }
            ],
            tableData: [],
            tableLoading: false,
            queryLoading: false,
            pagination: {
                total: 0,
                page: 1,
                pageSize: 10
            },
            envList: [],
            esModuleList: [],
            selectedBackup: null,
            detailModalVisible: false,
            initModalVisible: false
        }
    },
    mounted() {
        this.initData()
        this.loadData()
    },
    methods: {
        /**
         * 初始化数据
         */
        async initData() {
            try {
                await this.loadEnvList()
            } catch (error) {
                this.$Message.error('初始化数据失败')
            }
        },

        /**
         * 加载环境列表
         */
        async loadEnvList() {
            try {
                const res = await getEnvList()
                if (res.data.code === '0000') {
                    this.envList = res.data.data.data_list.map(item => ({
                        value: item.suite_code,
                        label: `${item.suite_code} - ${item.suite_name}`
                    }))
                }
            } catch (error) {
                console.error('加载环境列表失败:', error)
            }
        },

        /**
         * 加载备份数据
         */
        async loadData() {
            this.tableLoading = true
            try {
                const params = {
                    ...this.queryForm,
                    page: this.pagination.page,
                    page_size: this.pagination.pageSize
                }

                const res = await queryBackupList(params)
                if (res.data.code === '0000') {
                    this.tableData = res.data.data.data_list || []
                    this.pagination.total = res.data.data.total || 0
                } else {
                    this.$Message.error(res.data.message || '查询失败')
                }
            } catch (error) {
                this.$Message.error('查询失败')
                console.error(error)
            } finally {
                this.tableLoading = false
            }
        },

        /**
         * 查询处理
         */
        handleQuery() {
            this.pagination.page = 1
            this.loadData()
        },

        /**
         * 重置处理
         */
        handleReset() {
            this.queryForm = {
                suite_code: '',
                source_es_module_name: '',
                status: '',
                creator: ''
            }
            this.pagination.page = 1
            this.loadData()
        },

        /**
         * 分页变化处理
         */
        handlePageChange(page) {
            this.pagination.page = page
            this.loadData()
        },

        /**
         * 页大小变化处理
         */
        handlePageSizeChange(pageSize) {
            this.pagination.pageSize = pageSize
            this.pagination.page = 1
            this.loadData()
        },

        /**
         * 显示详情
         */
        showDetail(row) {
            this.selectedBackup = row
            this.detailModalVisible = true
        },

        /**
         * 验证状态
         */
        async verifyStatus(row) {
            this.$set(row, 'verifying', true)
            try {
                const res = await verifyBackupStatus({
                    es_dump_name: row.es_dump_name
                })

                if (res.data.code === '0000') {
                    // 更新行状态
                    row.status = res.data.data.status
                    this.$Message.success('验证完成')
                } else {
                    this.$Message.error(res.data.message || '验证失败')
                }
            } catch (error) {
                this.$Message.error('验证失败')
                console.error(error)
            } finally {
                this.$set(row, 'verifying', false)
            }
        },

        /**
         * 显示初始化弹框
         */
        showInitModal(row) {
            this.selectedBackup = row
            this.initModalVisible = true
        },

        /**
         * 初始化成功处理
         */
        handleInitSuccess() {
            this.$Message.success('初始化任务已提交')
            this.initModalVisible = false
        },

        /**
         * 获取状态颜色
         */
        getStatusColor(status) {
            const colorMap = {
                'PENDING': 'blue',
                'RUNNING': 'orange',
                'SUCCESS': 'green',
                'FAILED': 'red'
            }
            return colorMap[status] || 'default'
        },

        /**
         * 获取状态文本
         */
        getStatusText(status) {
            const textMap = {
                'PENDING': '等待中',
                'RUNNING': '运行中',
                'SUCCESS': '成功',
                'FAILED': '失败'
            }
            return textMap[status] || status
        },

        /**
         * 格式化日期时间
         */
        formatDateTime(dateTime) {
            if (!dateTime) return '-'
            return new Date(dateTime).toLocaleString('zh-CN')
        }
    }
}
</script>
```

### 6.2 备份详情弹框组件

**文件位置**: `deploy_website/src/spider-view/biz-iter-mgt/es-backup/backup-detail-modal.vue`

```vue
<template>
    <Modal
        v-model="modalVisible"
        title="备份详情"
        width="800"
        :mask-closable="false"
        @on-cancel="handleClose"
    >
        <div v-if="backupInfo" class="backup-detail">
            <Descriptions title="基本信息" :column="2" border>
                <DescriptionsItem label="备份名称">
                    {{ backupInfo.es_dump_name }}
                </DescriptionsItem>
                <DescriptionsItem label="环境编码">
                    {{ backupInfo.suite_code }}
                </DescriptionsItem>
                <DescriptionsItem label="ES模块">
                    {{ backupInfo.source_es_module_name }}
                </DescriptionsItem>
                <DescriptionsItem label="状态">
                    <Tag :color="getStatusColor(backupInfo.status)">
                        {{ getStatusText(backupInfo.status) }}
                    </Tag>
                </DescriptionsItem>
                <DescriptionsItem label="索引数量">
                    {{ backupInfo.index_count || '-' }}
                </DescriptionsItem>
                <DescriptionsItem label="文档数量">
                    {{ backupInfo.document_count || '-' }}
                </DescriptionsItem>
                <DescriptionsItem label="文件大小">
                    {{ formatFileSize(backupInfo.file_size) }}
                </DescriptionsItem>
                <DescriptionsItem label="仓库名称">
                    {{ backupInfo.repository_name }}
                </DescriptionsItem>
                <DescriptionsItem label="创建人">
                    {{ backupInfo.creator }}
                </DescriptionsItem>
                <DescriptionsItem label="创建时间">
                    {{ formatDateTime(backupInfo.create_time) }}
                </DescriptionsItem>
                <DescriptionsItem label="更新时间">
                    {{ formatDateTime(backupInfo.update_time) }}
                </DescriptionsItem>
                <DescriptionsItem label="备注" span="2">
                    {{ backupInfo.remark || '-' }}
                </DescriptionsItem>
            </Descriptions>

            <!-- 错误日志 -->
            <div v-if="backupInfo.error_log" class="error-log-section">
                <h4>错误日志</h4>
                <Alert type="error" show-icon>
                    <pre>{{ backupInfo.error_log }}</pre>
                </Alert>
            </div>

            <!-- Jenkins执行详情 -->
            <div class="jenkins-section">
                <h4>Jenkins执行详情</h4>
                <Button
                    type="primary"
                    @click="openJenkinsDetail"
                    :loading="jenkinsLoading"
                >
                    <Icon type="ios-link" />
                    查看Jenkins详情
                </Button>
            </div>
        </div>

        <div slot="footer">
            <Button @click="handleClose">关闭</Button>
        </div>
    </Modal>
</template>
```

### 6.3 备份初始化弹框组件

**文件位置**: `deploy_website/src/spider-view/biz-iter-mgt/es-backup/backup-init-modal.vue`

```vue
<template>
    <Modal
        v-model="modalVisible"
        title="备份初始化"
        width="600"
        :mask-closable="false"
        @on-cancel="handleClose"
    >
        <Form ref="initForm" :model="initForm" :rules="initRules" :label-width="100">
            <FormItem label="备份名称">
                <Input :value="backupInfo ? backupInfo.es_dump_name : ''" disabled />
            </FormItem>

            <FormItem label="业务编码" prop="biz_code">
                <Select
                    v-model="initForm.biz_code"
                    placeholder="请选择业务"
                    @on-change="handleBizChange"
                    clearable
                >
                    <Option v-for="biz in bizList" :key="biz.value" :value="biz.value">
                        {{ biz.label }}
                    </Option>
                </Select>
            </FormItem>

            <FormItem label="业务分支" prop="biz_test_iter_br">
                <Select
                    v-model="initForm.biz_test_iter_br"
                    placeholder="请选择分支"
                    :disabled="!initForm.biz_code"
                    clearable
                >
                    <Option v-for="branch in branchList" :key="branch.value" :value="branch.value">
                        {{ branch.label }}
                    </Option>
                </Select>
            </FormItem>

            <FormItem label="目标环境" prop="target_suite_code">
                <Select
                    v-model="initForm.target_suite_code"
                    placeholder="请选择目标环境"
                    clearable
                >
                    <Option v-for="env in envList" :key="env.value" :value="env.value">
                        {{ env.label }}
                    </Option>
                </Select>
            </FormItem>
        </Form>

        <div slot="footer">
            <Button @click="handleClose">取消</Button>
            <Button type="primary" @click="handleSubmit" :loading="submitLoading">确定</Button>
        </div>
    </Modal>
</template>
```

## 7. Jenkins流水线集成

### 7.1 test_es_init流水线设计

**文件位置**: `Jenkinsfile/test_es_init.groovy`（新增文件）

```groovy
#!groovy
pipeline {
    agent any
    environment {
        PYTHON_CMD="python3.x"
        SCRIPT_PATH="/home/<USER>/be-scripts/be-scripts/test_publish_aio"
    }
    options {
        timeout(time: 2, unit: 'HOURS')
        retry(1)
    }
    parameters {
        string(
            name: 'ES_DUMP_NAME',
            description: "ES备份名称"
        )
        string(
            name: 'BIZ_CODE',
            description: "业务编码"
        )
        string(
            name: 'BIZ_TEST_ITER_BR',
            description: "业务测试迭代分支"
        )
        string(
            name: 'TARGET_SUITE_CODE',
            description: "目标环境编码"
        )
        string(
            name: 'OPERATOR',
            description: "操作人"
        )
    }
    stages {
        stage('参数验证') {
            steps {
                script {
                    if (!params.ES_DUMP_NAME || !params.TARGET_SUITE_CODE) {
                        error("必要参数缺失")
                    }
                }
            }
        }

        stage('ES健康检查') {
            steps {
                sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${TARGET_SUITE_CODE} ES_HEALTH_CHECK"
            }
        }

        stage('ES备份验证') {
            steps {
                sh "${PYTHON_CMD} ${SCRIPT_PATH}/es_backup_init.py verify ${ES_DUMP_NAME}"
            }
        }

        stage('ES数据清空') {
            steps {
                sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${TARGET_SUITE_CODE} ES_DATA_CLEAR"
            }
        }

        stage('ES备份恢复') {
            steps {
                sh "${PYTHON_CMD} ${SCRIPT_PATH}/es_backup_init.py restore ${ES_DUMP_NAME} ${TARGET_SUITE_CODE}"
            }
        }

        stage('ES脚本执行') {
            steps {
                sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${TARGET_SUITE_CODE} ES_SCRIPT_EXEC"
            }
        }

        stage('更新绑定关系') {
            when {
                allOf {
                    expression { params.BIZ_CODE != null && params.BIZ_CODE != '' }
                    expression { params.BIZ_TEST_ITER_BR != null && params.BIZ_TEST_ITER_BR != '' }
                }
            }
            steps {
                sh "${PYTHON_CMD} ${SCRIPT_PATH}/es_backup_init.py bind ${ES_DUMP_NAME} ${BIZ_CODE} ${BIZ_TEST_ITER_BR} ${OPERATOR}"
            }
        }
    }
    post {
        always {
            script {
                // 记录执行结果
                sh "${PYTHON_CMD} ${SCRIPT_PATH}/es_backup_init.py log ${BUILD_NUMBER} ${currentBuild.result}"
            }
        }
    }
}
```

### 7.2 ES备份初始化脚本

**文件位置**: `be-scripts/test_publish_aio/es_backup_init.py`（新增文件）

```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ES备份初始化脚本
用于Jenkins流水线中执行ES备份的验证、恢复和绑定操作

<AUTHOR>
@date 2025-09-23 13:40:05
"""

import sys
import os
import logging
from datetime import datetime

# 添加项目路径
PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)

from test_publish_aio.test_suite_init_impl_es import EsBackupVerifyHandler, EsBackupRestoreHandler
from es_mgt.service.es_backup_service import ESBackupService

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def verify_backup(es_dump_name):
    """验证备份完整性"""
    try:
        logger.info(f"开始验证备份: {es_dump_name}")

        # 调用现有的验证逻辑
        backup_service = ESBackupService()
        success, message, status = backup_service.update_backup_status(es_dump_name)

        if success and status == 'SUCCESS':
            logger.info(f"备份验证成功: {es_dump_name}")
            return True
        else:
            logger.error(f"备份验证失败: {es_dump_name}, {message}")
            return False

    except Exception as e:
        logger.error(f"验证备份时发生错误: {e}")
        return False


def restore_backup(es_dump_name, target_suite_code):
    """恢复备份到目标环境"""
    try:
        logger.info(f"开始恢复备份: {es_dump_name} -> {target_suite_code}")

        # 这里需要实现具体的恢复逻辑
        # 可以复用现有的EsBackupRestoreHandler逻辑

        logger.info(f"备份恢复成功: {es_dump_name}")
        return True

    except Exception as e:
        logger.error(f"恢复备份时发生错误: {e}")
        return False


def bind_backup(es_dump_name, biz_code, biz_test_iter_br, operator):
    """绑定备份到业务分支"""
    try:
        logger.info(f"开始绑定备份: {es_dump_name} -> {biz_code}_{biz_test_iter_br}")

        backup_service = ESBackupService()
        success, message = backup_service.create_biz_binding(
            biz_code=biz_code,
            biz_test_iter_br=biz_test_iter_br,
            es_dump_name=es_dump_name,
            operator=operator,
            source_es_module_name="Elasticsearch7"  # 默认值，可根据实际情况调整
        )

        if success:
            logger.info(f"备份绑定成功: {es_dump_name}")
            return True
        else:
            logger.error(f"备份绑定失败: {es_dump_name}, {message}")
            return False

    except Exception as e:
        logger.error(f"绑定备份时发生错误: {e}")
        return False


def log_execution(build_number, result):
    """记录执行结果"""
    try:
        logger.info(f"Jenkins构建 {build_number} 执行结果: {result}")
        # 这里可以将执行结果记录到数据库或日志文件

    except Exception as e:
        logger.error(f"记录执行结果时发生错误: {e}")


def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("Usage: python es_backup_init.py <action> [args...]")
        print("Actions:")
        print("  verify <es_dump_name>")
        print("  restore <es_dump_name> <target_suite_code>")
        print("  bind <es_dump_name> <biz_code> <biz_test_iter_br> <operator>")
        print("  log <build_number> <result>")
        sys.exit(1)

    action = sys.argv[1]

    try:
        if action == 'verify':
            if len(sys.argv) != 3:
                raise ValueError("verify action requires es_dump_name")
            es_dump_name = sys.argv[2]
            success = verify_backup(es_dump_name)
            sys.exit(0 if success else 1)

        elif action == 'restore':
            if len(sys.argv) != 4:
                raise ValueError("restore action requires es_dump_name and target_suite_code")
            es_dump_name = sys.argv[2]
            target_suite_code = sys.argv[3]
            success = restore_backup(es_dump_name, target_suite_code)
            sys.exit(0 if success else 1)

        elif action == 'bind':
            if len(sys.argv) != 6:
                raise ValueError("bind action requires es_dump_name, biz_code, biz_test_iter_br, operator")
            es_dump_name = sys.argv[2]
            biz_code = sys.argv[3]
            biz_test_iter_br = sys.argv[4]
            operator = sys.argv[5]
            success = bind_backup(es_dump_name, biz_code, biz_test_iter_br, operator)
            sys.exit(0 if success else 1)

        elif action == 'log':
            if len(sys.argv) != 4:
                raise ValueError("log action requires build_number and result")
            build_number = sys.argv[2]
            result = sys.argv[3]
            log_execution(build_number, result)
            sys.exit(0)

        else:
            raise ValueError(f"Unknown action: {action}")

    except Exception as e:
        logger.error(f"执行失败: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
```

## 8. 开发实施指南

### 8.1 开发顺序建议

#### 8.1.1 后端开发顺序
1. **扩展数据模型**（如需要）
   - 在`es_mgt/model/models.py`中扩展状态字段

2. **实现服务层**
   - 在`es_mgt/service/es_backup_service.py`中新增查询、验证方法
   - 新增`es_mgt/service/jenkins_service.py`文件实现Jenkins集成

3. **实现控制层**
   - 在`es_mgt/api/es_backup_view.py`中新增API接口

4. **Jenkins集成**
   - 创建`Jenkinsfile/test_es_init.groovy`流水线文件
   - 创建`be-scripts/test_publish_aio/es_backup_init.py`脚本文件

#### 8.1.2 前端开发顺序
1. **API接口封装**
   - 在`deploy_website/src/spider-api/es-backup.js`中新增接口方法

2. **创建新页面组件**
   - 创建`deploy_website/src/spider-view/biz-iter-mgt/es-backup-query.vue`

3. **创建弹框组件**
   - 创建`deploy_website/src/spider-view/biz-iter-mgt/es-backup/backup-detail-modal.vue`
   - 创建`deploy_website/src/spider-view/biz-iter-mgt/es-backup/backup-init-modal.vue`

4. **配置路由**
   - 在`deploy_website/src/router/routers.js`中新增路由配置

5. **修改主页面**
   - 在`deploy_website/src/spider-view/biz-iter-mgt/es-backup-mgt.vue`中新增Tab页签

### 8.2 关键技术要点

#### 8.2.1 分页查询实现
```python
# 在ESBackupService中实现分页查询
def query_backups_with_pagination(self, suite_code=None, source_es_module_name=None,
                                 status=None, creator=None, page=1, page_size=10):
    """分页查询备份列表"""
    from django.core.paginator import Paginator
    from django.db.models import Q

    # 构建查询条件
    query = Q()
    if suite_code:
        query &= Q(suite_code=suite_code)
    if source_es_module_name:
        query &= Q(source_es_module_name=source_es_module_name)
    if status:
        query &= Q(status=status)
    if creator:
        query &= Q(creator__icontains=creator)

    # 执行查询
    queryset = EsMgtDumpInfo.objects.filter(query).order_by('-create_time')

    # 分页处理
    paginator = Paginator(queryset, page_size)
    page_obj = paginator.get_page(page)

    return {
        'total': paginator.count,
        'page': page,
        'page_size': page_size,
        'data_list': list(page_obj.object_list.values())
    }
```

#### 8.2.2 Jenkins流水线触发
```python
# 在JenkinsService中实现流水线触发
import requests
import json

class JenkinsService:
    def __init__(self):
        self.jenkins_url = "http://jenkins.example.com"
        self.username = "admin"
        self.token = "your_token"

    def trigger_es_init_pipeline(self, es_dump_name, biz_code, biz_test_iter_br,
                                target_suite_code, operator):
        """触发test_es_init流水线"""
        job_name = "test_es_init"
        build_url = f"{self.jenkins_url}/job/{job_name}/buildWithParameters"

        params = {
            'ES_DUMP_NAME': es_dump_name,
            'BIZ_CODE': biz_code or '',
            'BIZ_TEST_ITER_BR': biz_test_iter_br or '',
            'TARGET_SUITE_CODE': target_suite_code,
            'OPERATOR': operator
        }

        try:
            response = requests.post(
                build_url,
                params=params,
                auth=(self.username, self.token),
                timeout=30
            )

            if response.status_code == 201:
                # 获取构建ID
                queue_url = response.headers.get('Location')
                build_id = self._get_build_id_from_queue(queue_url)
                return True, "流水线触发成功", build_id
            else:
                return False, f"流水线触发失败: {response.status_code}", None

        except Exception as e:
            return False, f"流水线触发异常: {str(e)}", None
```

#### 8.2.3 前端状态管理
```javascript
// 在Vue组件中实现状态管理
export default {
    data() {
        return {
            tableData: [],
            // 为每行数据添加loading状态
            rowLoadingStates: {}
        }
    },
    methods: {
        // 设置行loading状态
        setRowLoading(rowId, loading) {
            this.$set(this.rowLoadingStates, rowId, loading)
        },

        // 获取行loading状态
        getRowLoading(rowId) {
            return this.rowLoadingStates[rowId] || false
        },

        // 验证状态时的loading处理
        async verifyStatus(row) {
            this.setRowLoading(row.id, true)
            try {
                // 执行验证逻辑
                const res = await verifyBackupStatus({
                    es_dump_name: row.es_dump_name
                })
                // 处理结果
            } finally {
                this.setRowLoading(row.id, false)
            }
        }
    }
}
```

### 8.3 数据库迁移

如果需要扩展状态字段，创建数据库迁移文件：

**文件位置**: `es_mgt/migrations/0002_extend_status_choices.py`

```python
from django.db import migrations, models

class Migration(migrations.Migration):
    dependencies = [
        ('es_mgt', '0001_initial'),
    ]

    operations = [
        migrations.AlterField(
            model_name='esmgtdumpinfo',
            name='status',
            field=models.CharField(
                choices=[
                    ('PENDING', '等待中'),
                    ('RUNNING', '运行中'),
                    ('SUCCESS', '成功'),
                    ('FAILED', '失败'),
                    ('INITIALIZING', '初始化中')
                ],
                default='PENDING',
                help_text='状态',
                max_length=20
            ),
        ),
    ]
```

## 9. 测试策略

### 9.1 后端测试

#### 9.1.1 单元测试
**文件位置**: `es_mgt/tests/test_backup_query.py`

```python
from django.test import TestCase
from rest_framework.test import APIClient
from django.contrib.auth.models import User
from es_mgt.model.models import EsMgtDumpInfo

class TestBackupQueryAPI(TestCase):
    def setUp(self):
        self.client = APIClient()
        self.user = User.objects.create_user('testuser', '<EMAIL>', 'pass')
        self.client.force_authenticate(user=self.user)

        # 创建测试数据
        EsMgtDumpInfo.objects.create(
            suite_code='test-env',
            es_dump_name='test-backup-1',
            source_es_module_name='Elasticsearch7',
            status='SUCCESS',
            creator='testuser',
            remark='测试备份1'
        )

    def test_query_backups_success(self):
        """测试查询备份列表成功"""
        response = self.client.get('/spider/es_mgt/backups/query/', {
            'suite_code': 'test-env',
            'page': 1,
            'page_size': 10
        })

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data['code'], '0000')
        self.assertGreater(response.data['data']['total'], 0)

    def test_verify_backup_status(self):
        """测试验证备份状态"""
        response = self.client.post('/spider/es_mgt/backups/verify/', {
            'es_dump_name': 'test-backup-1'
        })

        self.assertEqual(response.status_code, 200)
        # 验证返回数据结构
```

#### 9.1.2 集成测试
**文件位置**: `es_mgt/tests/test_jenkins_integration.py`

```python
from unittest.mock import patch, Mock
from django.test import TestCase
from es_mgt.service.jenkins_service import JenkinsService

class TestJenkinsIntegration(TestCase):
    def setUp(self):
        self.jenkins_service = JenkinsService()

    @patch('requests.post')
    def test_trigger_pipeline_success(self, mock_post):
        """测试触发流水线成功"""
        mock_response = Mock()
        mock_response.status_code = 201
        mock_response.headers = {'Location': 'http://jenkins/queue/123'}
        mock_post.return_value = mock_response

        success, message, build_id = self.jenkins_service.trigger_es_init_pipeline(
            'test-backup', 'test-biz', 'test-branch', 'test-env', 'testuser'
        )

        self.assertTrue(success)
        self.assertIn('成功', message)
```

### 9.2 前端测试

#### 9.2.1 组件测试
**文件位置**: `deploy_website/tests/unit/es-backup-query.spec.js`

```javascript
import { shallowMount } from '@vue/test-utils'
import ESBackupQuery from '@/spider-view/biz-iter-mgt/es-backup-query.vue'

describe('ESBackupQuery.vue', () => {
    let wrapper

    beforeEach(() => {
        wrapper = shallowMount(ESBackupQuery, {
            mocks: {
                $Message: {
                    success: jest.fn(),
                    error: jest.fn()
                }
            }
        })
    })

    it('应该正确初始化查询表单', () => {
        expect(wrapper.vm.queryForm.suite_code).toBe('')
        expect(wrapper.vm.queryForm.status).toBe('')
    })

    it('查询重置应该清空表单', () => {
        wrapper.vm.queryForm.suite_code = 'test-env'
        wrapper.vm.handleReset()
        expect(wrapper.vm.queryForm.suite_code).toBe('')
    })

    it('应该正确格式化状态显示', () => {
        expect(wrapper.vm.getStatusText('SUCCESS')).toBe('成功')
        expect(wrapper.vm.getStatusColor('SUCCESS')).toBe('green')
    })
})
```

### 9.3 端到端测试

#### 9.3.1 测试场景
1. **完整流程测试**
   - 创建备份 → 查询列表 → 验证状态 → 初始化备份

2. **异常场景测试**
   - 网络异常处理
   - 权限验证
   - 参数校验

#### 9.3.2 测试数据准备
```sql
-- 准备测试数据
INSERT INTO es_mgt_dump_info (
    suite_code, es_dump_name, source_es_module_name,
    status, creator, remark, create_time
) VALUES
('test-env-1', 'test-backup-success', 'Elasticsearch7', 'SUCCESS', 'testuser', '成功备份', NOW()),
('test-env-1', 'test-backup-running', 'Elasticsearch7', 'RUNNING', 'testuser', '运行中备份', NOW()),
('test-env-2', 'test-backup-failed', 'Elasticsearch7', 'FAILED', 'testuser', '失败备份', NOW());
```

## 10. 部署配置

### 10.1 Jenkins配置

#### 10.1.1 创建Jenkins Job
1. 在Jenkins中创建新的Pipeline Job：`test_es_init`
2. 配置Pipeline脚本路径：`Jenkinsfile/test_es_init.groovy`
3. 设置参数化构建，添加必要的参数

#### 10.1.2 权限配置
```groovy
// 在Jenkinsfile中配置权限
pipeline {
    agent any
    options {
        // 设置构建权限
        buildDiscarder(logRotator(numToKeepStr: '10'))
        // 设置超时时间
        timeout(time: 2, unit: 'HOURS')
    }
}
```

### 10.2 环境变量配置

**文件位置**: `settings.py`

```python
# Jenkins配置
JENKINS_CONFIG = {
    'BASE_URL': os.getenv('JENKINS_BASE_URL', 'http://jenkins.example.com'),
    'USERNAME': os.getenv('JENKINS_USERNAME', 'admin'),
    'TOKEN': os.getenv('JENKINS_TOKEN', 'your_token'),
    'ES_INIT_JOB': 'test_es_init'
}

# ES备份配置
ES_BACKUP_CONFIG = {
    'DEFAULT_PAGE_SIZE': 10,
    'MAX_PAGE_SIZE': 100,
    'VERIFY_TIMEOUT': 300,  # 5分钟
    'INIT_TIMEOUT': 7200    # 2小时
}
```

### 10.3 前端环境配置

**文件位置**: `deploy_website/.env.production`

```bash
# API基础URL
VUE_APP_API_BASE_URL=https://api.example.com

# Jenkins相关配置
VUE_APP_JENKINS_BASE_URL=https://jenkins.example.com
```

## 11. 监控与日志

### 11.1 日志配置

#### 11.1.1 后端日志
**文件位置**: `settings.py`

```python
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'es_backup_file': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': '/var/log/django/es_backup_init.log',
            'maxBytes': 1024*1024*10,  # 10MB
            'backupCount': 5,
            'formatter': 'verbose',
        },
    },
    'loggers': {
        'es_backup_init': {
            'handlers': ['es_backup_file'],
            'level': 'INFO',
            'propagate': True,
        },
    },
}
```

#### 11.1.2 Jenkins日志
在Jenkins流水线中添加详细的日志记录：

```groovy
stage('ES备份恢复') {
    steps {
        script {
            try {
                sh "${PYTHON_CMD} ${SCRIPT_PATH}/es_backup_init.py restore ${ES_DUMP_NAME} ${TARGET_SUITE_CODE}"
                echo "ES备份恢复成功: ${ES_DUMP_NAME}"
            } catch (Exception e) {
                echo "ES备份恢复失败: ${e.getMessage()}"
                throw e
            }
        }
    }
}
```

### 11.2 监控指标

#### 11.2.1 关键指标
```python
# 监控指标定义
MONITORING_METRICS = {
    'backup_init_success_rate': '备份初始化成功率',
    'backup_init_duration': '备份初始化耗时',
    'backup_verify_success_rate': '备份验证成功率',
    'jenkins_pipeline_success_rate': 'Jenkins流水线成功率',
    'api_response_time': 'API响应时间'
}
```

#### 11.2.2 告警配置
```python
# 告警阈值配置
ALERT_THRESHOLDS = {
    'backup_init_failure_count': 3,      # 连续失败3次告警
    'api_response_time_ms': 5000,        # API响应时间超过5秒告警
    'jenkins_pipeline_timeout_min': 120  # 流水线超时2小时告警
}
```

## 12. 常见问题与解决方案

### 12.1 技术问题

#### 12.1.1 Jenkins流水线触发失败
**问题**: 调用Jenkins API触发流水线时返回403错误
**解决方案**:
```python
# 检查Jenkins认证配置
def check_jenkins_auth(self):
    """检查Jenkins认证"""
    try:
        response = requests.get(
            f"{self.jenkins_url}/api/json",
            auth=(self.username, self.token),
            timeout=10
        )
        return response.status_code == 200
    except Exception as e:
        logger.error(f"Jenkins认证检查失败: {e}")
        return False
```

#### 12.1.2 分页查询性能问题
**问题**: 大量数据时分页查询响应慢
**解决方案**:
```python
# 添加数据库索引
class Migration(migrations.Migration):
    operations = [
        migrations.RunSQL(
            "CREATE INDEX idx_es_dump_info_query ON es_mgt_dump_info(suite_code, source_es_module_name, status, create_time DESC);"
        ),
    ]

# 优化查询语句
def query_backups_optimized(self, **kwargs):
    """优化的分页查询"""
    queryset = EsMgtDumpInfo.objects.select_related().filter(**kwargs)
    # 使用数据库级别的分页
    return queryset.order_by('-create_time')
```

### 12.2 业务问题

#### 12.2.1 备份状态不一致
**问题**: 数据库中的状态与ES实际状态不一致
**解决方案**:
```python
# 实现状态同步机制
def sync_backup_status_batch(self):
    """批量同步备份状态"""
    pending_backups = EsMgtDumpInfo.objects.filter(
        status__in=['PENDING', 'RUNNING']
    )

    for backup in pending_backups:
        try:
            success, message, actual_status = self.update_backup_status(backup.es_dump_name)
            if success and actual_status != backup.status:
                backup.status = actual_status
                backup.save()
                logger.info(f"同步备份状态: {backup.es_dump_name} -> {actual_status}")
        except Exception as e:
            logger.error(f"同步备份状态失败: {backup.es_dump_name}, {e}")
```

#### 12.2.2 初始化流水线卡住
**问题**: Jenkins流水线执行过程中卡住不动
**解决方案**:
```groovy
// 在Jenkinsfile中添加超时和重试机制
pipeline {
    options {
        timeout(time: 2, unit: 'HOURS')
        retry(1)
    }
    stages {
        stage('ES备份恢复') {
            options {
                timeout(time: 30, unit: 'MINUTES')
            }
            steps {
                retry(2) {
                    sh "${PYTHON_CMD} ${SCRIPT_PATH}/es_backup_init.py restore ${ES_DUMP_NAME} ${TARGET_SUITE_CODE}"
                }
            }
        }
    }
}
```

## 13. 总结

本设计文档详细描述了ES备份初始化功能的完整实现方案，基于现有的ES备份创建和绑定功能进行扩展，主要包括：

### 13.1 核心功能
1. **ES备份查询列表**: 支持多条件查询和分页显示
2. **备份详情查看**: 提供完整的备份信息展示
3. **备份状态验证**: 实时验证备份文件状态
4. **备份初始化**: 集成Jenkins流水线进行环境初始化

### 13.2 技术特点
1. **架构清晰**: 基于现有架构进行扩展，保持一致性
2. **代码复用**: 最大化复用现有的服务层和数据层代码
3. **集成完善**: 与Jenkins流水线深度集成
4. **用户友好**: 提供直观的Web界面操作

### 13.3 实施建议
1. **分阶段开发**: 按照后端→前端→集成的顺序进行开发
2. **充分测试**: 重点测试Jenkins集成和异常场景
3. **监控完善**: 建立完整的日志和监控体系
4. **文档维护**: 及时更新相关文档和操作手册

通过遵循本设计文档，开发团队可以高效地完成ES备份初始化功能的开发工作，为测试人员提供完整的ES备份管理解决方案。

---

**文档版本**: v1.0
**最后更新**: 2025-09-23 13:40:05
**维护人员**: hongdong.xie
