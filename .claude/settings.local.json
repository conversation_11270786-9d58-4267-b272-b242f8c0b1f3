{"permissions": {"allow": ["Read(/Users/<USER>/work/workspace/PA/spider/spider/**)", "Read(/Users/<USER>/work/workspace/PA/continuous-testing-frontend/**)", "Read(/Users/<USER>/work/workspace/PA/hm-scripts/**)", "Read(/Users/<USER>/work/workspace/PA/be-scripts/**)", "Read(/Users/<USER>/work/workspace/PA/spider/app_mgt/**)", "Read(/Users/<USER>/work/workspace/PA/spider/app_mgt/**)", "Read(/Users/<USER>/work/workspace/PA/spider/app_mgt/**)", "Read(/Users/<USER>/work/workspace/PA/**)", "Read(/Users/<USER>/work/workspace/PA/continuous-testing-frontend/src/**)", "Read(/Users/<USER>/work/workspace/PA/continuous-testing-frontend/**)", "Read(/Users/<USER>/work/workspace/PA/howbuy-mantis-web-frontend/src/**)", "Read(/Users/<USER>/work/workspace/PA/howbuy-mantis-web-frontend/public/**)", "Read(/Users/<USER>/work/workspace/PA/continuous-testing-frontend/src/**)", "Read(/Users/<USER>/work/workspace/PA/continuous-testing-frontend/src/router/routes/**)", "Read(/Users/<USER>/work/workspace/PA/continuous-testing-frontend/src/views/**)", "Read(/Users/<USER>/work/workspace/PA/spider/iter_mgt/**)", "Read(/Users/<USER>/work/workspace/PA/spider/**)", "Read(/Users/<USER>/work/workspace/PA/spider/**)", "Read(/Users/<USER>/work/workspace/PA/spider/**)", "Read(/Users/<USER>/work/workspace/PA/spider/iter_mgt/**)", "Read(/Users/<USER>/work/workspace/PA/spider/**)", "Read(/Users/<USER>/work/workspace/PA/spider/iter_mgt/**)", "Read(/Users/<USER>/work/workspace/PA/spider/spider/**)"], "deny": [], "ask": [], "additionalDirectories": ["/Users/<USER>/work/workspace/PA/spider-common-utils/"]}}