# Spider项目开发规范

**文档创建时间：** 2025-08-25 19:24:46  
**作者：** hongdong.xie  
**版本：** v1.0

基于py_pipeline_mgt标准模块制定的项目开发规范，所有新模块开发必须严格遵循此规范。

## 1. 项目架构规范

### 1.1 模块目录结构（标准四层架构）

```
module_name/
├── __init__.py
├── apps.py                     # Django应用配置
├── urls.py                     # 路由配置
├── api/                        # API控制层
│   ├── __init__.py
│   └── module_name_api.py
├── service/                    # 业务逻辑层
│   ├── __init__.py
│   └── module_name_service.py
├── dao/                        # 数据访问层
│   ├── __init__.py
│   └── module_name_dao.py
├── model/                      # 数据模型层
│   ├── __init__.py
│   └── models.py
├── common/                     # 通用组件
│   ├── __init__.py
│   └── constants.py
└── utils/                      # 工具类
    ├── __init__.py
    └── common_utils.py
```

### 1.2 层级职责划分

- **API层（api/）**：接收HTTP请求，参数验证，调用Service层，返回响应
- **Service层（service/）**：业务逻辑处理，事务控制，调用DAO层
- **DAO层（dao/）**：数据库访问，SQL操作，数据转换
- **Model层（model/）**：数据模型定义，数据库表结构

## 2. API层开发规范

### 2.1 API类命名规范

```python
class ModuleNameApi(viewsets.ViewSet):
    """
    模块功能说明
    <AUTHOR>
    @date 2025-08-25 19:24:46
    """
    business_name = "模块业务名称"
    action_item = "操作项标识"
```

### 2.2 API方法规范

```python
def create(self, req, *args, **kwargs):
    """
    创建操作接口
    <AUTHOR>
    @date 2025-08-25 19:24:46
    """
    if isinstance(req.user, str):
        user = req.user
    else:
        user = req.user.username
    
    logger.info("请求参数: {}".format(req.data))
    
    try:
        # 参数验证
        required_params = ['param1', 'param2']
        for param in required_params:
            if not req.data.get(param):
                return Response(data=NewApiResult.failed_dict(f'参数错误: {param}不能为空'))
        
        # 调用Service层
        result = ModuleNameService.create_data(user, req.data)
        
        return Response(data=NewApiResult.success_dict(message="操作成功", data=result))
        
    except Exception as ex:
        logger.error("操作失败: {}".format(str(ex)))
        return Response(data=NewApiResult.failed_dict(message=f'操作失败: {str(ex)}'))
```

### 2.3 响应格式统一

- 成功响应：`NewApiResult.success_dict(message="成功消息", data=数据)`
- 失败响应：`NewApiResult.failed_dict(msg="失败消息", data=数据)`

## 3. Service层开发规范

### 3.1 Service类命名

```python
class ModuleNameService:
    """
    模块名称业务逻辑层
    <AUTHOR>
    @date 2025-08-25 19:24:46
    """
    
    @staticmethod
    def create_data(user, data):
        """
        创建数据业务逻辑
        <AUTHOR>
        @date 2025-08-25 19:24:46
        """
        # 业务逻辑处理
        # 调用DAO层
        pass
```

### 3.2 事务处理规范

```python
from django.db import transaction

@transaction.atomic
def update_data_with_transaction(user, data):
    """
    带事务的数据更新
    <AUTHOR>
    @date 2025-08-25 19:24:46
    """
    # 事务操作
    pass
```

## 4. DAO层开发规范

### 4.1 数据访问方法命名

```python
def get_data_by_condition(condition):
    """
    根据条件查询数据
    @param condition: 查询条件
    @return: 查询结果列表
    <AUTHOR>
    @date 2025-08-25 19:24:46
    """
    sql = """
    SELECT col1, col2, col3
    FROM table_name
    WHERE condition_field = %s
    """
    
    with connection.cursor() as cursor:
        cursor.execute(sql, [condition])
        column_names = ["col1", "col2", "col3"]
        return [dict(zip(column_names, record)) for record in cursor.fetchall()]
```

### 4.2 SQL规范

- 使用参数化查询防止SQL注入
- 复杂SQL使用多行格式，保持可读性
- 字段名使用驼峰命名返回给前端
- 统一使用`with connection.cursor()`方式执行SQL

## 5. Model层开发规范

### 5.1 模型类定义

```python
class ModuleNameInfo(models.Model):
    """
    模块信息表
    <AUTHOR>
    @date 2025-08-25 19:24:46
    """
    name = models.CharField(verbose_name='名称', max_length=100)
    status = models.BooleanField(verbose_name='状态：0-禁用，1-启用')
    create_user = models.CharField(verbose_name='创建人', max_length=20)
    create_time = models.DateTimeField(verbose_name='创建时间')
    update_user = models.CharField(verbose_name='修改人', max_length=20)
    update_time = models.DateTimeField(verbose_name='修改时间')

    class Meta:
        db_table = 'module_name_info'
        verbose_name = '模块信息表'
```

### 5.2 字段命名规范

- 时间字段：`create_time`、`update_time`
- 用户字段：`create_user`、`update_user`
- 状态字段：`status`、`is_active`
- ID字段：使用关联表名+`_id`，如`app_id`、`module_id`

## 6. URL路由规范

### 6.1 路由配置

```python
from django.urls import path, include
from rest_framework.routers import DefaultRouter
from module_name.api import module_name_api

router = DefaultRouter()
router.register('module_name_api', module_name_api.ModuleNameApi, basename="module_name_api")

urlpatterns = [
    path("", include(router.urls))
]
```

### 6.2 API接口命名规范

- RESTful风格接口使用ViewSet
- 自定义接口使用`@action`装饰器
- URL路径使用下划线命名：`get_data_by_id`

## 7. 代码规范

### 7.1 注释规范

- 所有类、方法必须添加文档注释
- 注释作者统一为：`hongdong.xie`
- 注释日期格式：`YYYY-MM-DD HH:MM:SS`
- 不允许更新或覆盖现有注释中的`@author`和`@date`

### 7.2 异常处理规范

```python
try:
    # 业务逻辑
    pass
except SpecificException as ex:
    logger.error("具体异常处理: {}".format(str(ex)))
    # 具体异常处理
except Exception as ex:
    logger.error("通用异常处理: {}".format(str(ex)))
    # 通用异常处理
```

### 7.3 日志记录规范

```python
from spider.settings import logger

logger.info("信息日志: {}".format(data))
logger.error("错误日志: {}".format(error))
logger.debug("调试日志: {}".format(debug_info))
```

### 7.4 导包规范

```python
# 标准库导入
import datetime

# Django相关导入
from django.db import models
from rest_framework import viewsets
from rest_framework.response import Response

# 项目内部导入
from spider.settings import NewApiResult, logger
from app_mgt.models import AppModule
```

## 8. 数据库规范

### 8.1 表命名规范

- 格式：`模块名_表用途`
- 示例：`app_mgt_app_info`、`env_mgt_suite`、`py_pipeline_mgt_config`

### 8.2 字段规范

- 主键：使用`BigAutoField`
- 外键：使用`IntegerField`，命名为`关联表_id`
- 布尔值：使用`BooleanField`，注释说明0和1的含义
- 字符串：根据实际需要设置`max_length`，常用长度20、100、255、999

## 9. 文件大小限制

- 单个Python文件不超过1000行
- 超过1000行的文件需要拆分为多个文件
- 复杂业务逻辑需要按功能模块拆分

## 10. 测试规范

### 10.1 单元测试

```python
from django.test import TestCase

class ModuleNameTestCase(TestCase):
    """
    模块名称测试用例
    <AUTHOR>
    @date 2025-08-25 19:24:46
    """
    
    def test_create_data(self):
        """
        测试创建数据功能
        """
        pass
```

## 11. 版本控制规范

### 11.1 分支命名

- 功能分支：`feature/模块名-功能描述`
- 修复分支：`bugfix/模块名-问题描述`
- 发布分支：`release/版本号`

### 11.2 提交信息格式

```
[模块名] 操作类型: 简短描述

详细描述（可选）

- 相关问题或需求编号
```

示例：
```
[py_pipeline_mgt] feat: 新增Python流水线管理API

实现Python项目的编译和部署功能
- 支持Zeus配置同步
- 支持Jenkins任务调用
- 关联TAPD需求 #12345
```

## 12. 部署规范

### 12.1 配置文件管理

- 敏感信息存储在`settings.ini`中
- 不同环境使用不同的配置文件
- 配置项使用大写字母和下划线

### 12.2 日志配置

- 使用`log.ini`配置日志格式和输出
- 生产环境日志级别设置为INFO
- 开发环境日志级别可设置为DEBUG

## 13. 性能规范

### 13.1 数据库查询优化

- 避免N+1查询问题
- 使用`select_related`和`prefetch_related`优化关联查询
- 复杂查询使用原生SQL
- 大数据量操作使用分页

### 13.2 缓存使用

- 频繁查询的数据使用Redis缓存
- 缓存键命名规范：`模块名:功能:参数`
- 设置合适的缓存过期时间

## 14. 安全规范

### 14.1 权限控制

- API接口需要进行用户认证
- 敏感操作需要权限验证
- 使用JWT进行身份验证

### 14.2 数据验证

- 前端传入参数必须进行验证
- 使用参数化查询防止SQL注入
- 敏感信息不记录在日志中

---

**注意事项：**

1. 本规范基于py_pipeline_mgt标准模块制定，所有新模块必须严格遵循
2. 现有模块逐步重构时也应遵循此规范
3. 如需修改规范，需经过团队讨论并更新本文档
4. 违反规范的代码不允许合并到主分支

---

**参考模块：**

- `py_pipeline_mgt/` - 标准模块架构参考
- `es_mgt/` - 简单模块架构参考
- `app_mgt/` - 复杂模块架构参考