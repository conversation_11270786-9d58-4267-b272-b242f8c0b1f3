# PA平台开发规范统一调度指引

**创建时间：** 2025-08-25 14:30:00  
**作者：** hongdong.xie

## 概述

本文档为AI助手提供PA平台开发规范的统一调度指引，帮助AI在不同开发场景下自动选择和应用合适的规范文件。

## 规范文件体系结构

### 核心规范文件列表

| 文件名 | 适用场景 | 描述 |
|--------|----------|------|
| `00_global_rules.md` | 所有开发场景 | 全局开发标准和通用原则 |
| `01_django_app_rules.md` | Django应用开发 | Model、View、Serializer等应用层规范 |
| `02_api_interface_rules.md` | API接口设计 | REST API设计和开发标准 |
| `03_database_rules.md` | 数据库操作 | 数据库设计、ORM优化、迁移规范 |
| `04_testing_rules.md` | 测试开发 | 单元测试、集成测试、E2E测试规范 |
| `05_frontend_vue_rules.md` | Vue前端开发 | Vue组件、状态管理、API集成规范 |
| `06_python_script_rules.md` | Python脚本开发 | 自动化脚本、工具开发规范 |
| `07_jenkins_pipeline_rules.md` | CI/CD流水线 | Jenkins Pipeline、Groovy脚本规范 |

## 场景识别与规范调度规则

### 1. 文件路径识别模式

#### Django应用开发 (`01_django_app_rules.md`)
```
匹配路径模式：
- */spider/*.py
- */models.py
- */views.py  
- */serializers.py
- */admin.py
- */urls.py
```

#### API接口设计 (`02_api_interface_rules.md`)
```
匹配路径模式：
- */api/*.py
- */views/*.py
- */serializers.py

匹配内容特征：
- @api_view装饰器
- APIView类继承
- ViewSet类定义
- Response对象使用
```

#### 数据库操作 (`03_database_rules.md`)
```
匹配路径模式：
- */migrations/*.py
- */models.py
- *.sql

匹配内容特征：
- CREATE TABLE语句
- class XXX(models.Model)
- ForeignKey定义
- 数据库迁移代码
```

#### 测试开发 (`04_testing_rules.md`)
```
匹配路径模式：
- */tests/*.py
- */test_*.py
- *_test.py

匹配内容特征：
- import unittest
- from django.test
- class XXX(TestCase)
- def test_方法
```

#### Vue前端开发 (`05_frontend_vue_rules.md`)
```
匹配路径模式：
- *.vue
- *.ts (前端目录下)
- *.js (前端目录下)
- */frontend/*
- */src/*.vue

匹配内容特征：
- <template>标签
- <script setup>
- Vue组件定义
- import from 'vue'
```

#### Python脚本开发 (`06_python_script_rules.md`)
```
匹配路径模式：
- */scripts/*.py
- */hm-scripts/*.py
- */be-scripts/*.py
- */qa-scripts/*.py

匹配内容特征：
- #!/usr/bin/env python
- subprocess调用
- requests库使用
- 执行器类定义
```

#### Jenkins流水线 (`07_jenkins_pipeline_rules.md`)
```
匹配路径模式：
- *Jenkinsfile*
- *.groovy
- */pipeline/*.groovy

匹配内容特征：
- pipeline {}
- agent {}
- stages {}
- @Library()
```

### 2. 任务描述关键词识别

#### 关键词映射表

| 开发场景 | 关键词 |
|----------|---------|
| Django应用 | 模型、model、序列化、serializer、视图、view、django应用 |
| API接口 | api、接口、restful、endpoint、路由 |
| 数据库 | 数据库、database、表、table、迁移、migration、sql |
| 测试 | 测试、test、单元测试、集成测试、pytest、unittest |
| Vue前端 | 前端、frontend、vue、组件、component、页面 |
| Python脚本 | 脚本、script、自动化、automation、构建、build |
| Jenkins流水线 | 流水线、pipeline、ci/cd、jenkins、构建、部署 |

## AI助手调度指令

### 基本调度逻辑

1. **总是优先读取全局规范** (`00_global_rules.md`)
2. **根据场景识别结果，额外读取对应的专项规范**
3. **多场景匹配时，读取所有相关规范文件**
4. **未匹配到特定场景时，仅使用全局规范**

### 调度决策流程

```mermaid
flowchart TD
    A[开始调度] --> B[读取全局规范]
    B --> C[分析输入信息]
    C --> D{识别开发场景}
    
    D -->|Django开发| E[+读取Django规范]
    D -->|API开发| F[+读取API规范]
    D -->|数据库操作| G[+读取数据库规范]
    D -->|测试开发| H[+读取测试规范]
    D -->|前端开发| I[+读取Vue规范]
    D -->|脚本开发| J[+读取Python脚本规范]
    D -->|CI/CD| K[+读取Jenkins规范]
    D -->|未识别| L[仅使用全局规范]
    
    E --> M[应用综合规范]
    F --> M
    G --> M
    H --> M
    I --> M
    J --> M
    K --> M
    L --> M
    
    M --> N[开始开发任务]
```

### 实际调度示例

#### 示例1：Django Model开发
```
输入：编辑 spider/app_mgt/models.py 文件
识别：文件路径包含 "models.py" → Django应用开发
调度：读取 00_global_rules.md + 01_django_app_rules.md
```

#### 示例2：Vue组件开发  
```
输入：创建用户管理页面组件
识别：关键词 "页面组件" → Vue前端开发
调度：读取 00_global_rules.md + 05_frontend_vue_rules.md
```

#### 示例3：API测试
```
输入：为用户注册API编写单元测试
识别：关键词 "API" + "单元测试" → API接口设计 + 测试开发
调度：读取 00_global_rules.md + 02_api_interface_rules.md + 04_testing_rules.md
```

#### 示例4：数据库迁移
```
输入：处理 migrations/0001_initial.py 文件
识别：文件路径包含 "migrations/" → 数据库操作
调度：读取 00_global_rules.md + 03_database_rules.md
```

## AI助手使用指南

### 调度触发条件

AI助手应在以下情况下进行规范调度：

1. **文件操作时**：根据文件路径和内容自动识别场景
2. **任务描述时**：根据任务关键词识别开发场景
3. **代码审查时**：根据代码特征选择相应规范进行检查
4. **问题解答时**：根据问题领域选择相关规范进行参考

### 调度优先级

1. **全局规范**：始终适用，优先级最高
2. **专项规范**：根据场景匹配，按相关度排序
3. **组合规范**：多场景时，合并应用所有相关规范

### 特殊场景处理

#### PA平台特定模块
- `app_mgt/`：应用管理模块 → Django应用规范
- `env_mgt/`：环境管理模块 → Django应用规范  
- `iter_mgt/`：迭代管理模块 → Django应用规范
- `pipeline/`：流水线模块 → Django应用规范 + Jenkins规范
- `publish/`：发布模块 → Django应用规范
- `task_mgt/`：任务管理模块 → Django应用规范 + Python脚本规范

#### 跨技术栈场景
- 全栈功能开发：Django规范 + Vue规范 + API规范
- CI/CD开发：Jenkins规范 + Python脚本规范
- 数据库相关开发：数据库规范 + Django规范

## 调度验证清单

在应用规范前，AI助手应验证：

- [ ] 全局规范文件是否存在且可读
- [ ] 识别的专项规范文件是否存在
- [ ] 多个规范文件间是否存在冲突
- [ ] 规范内容是否与当前开发任务相符

## 更新维护

本调度指引应与规范文件保持同步更新：

1. **新增规范文件时**：更新文件列表和识别规则
2. **修改识别规则时**：更新场景匹配模式
3. **PA平台架构变化时**：更新特定模块映射关系

---

**使用说明**：AI助手在处理PA平台开发任务时，应严格按照本指引进行规范调度，确保在正确的开发场景下应用合适的开发规范，提高代码质量和开发效率。