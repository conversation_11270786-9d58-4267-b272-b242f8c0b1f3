# API接口层开发规范

**文档创建时间：** 2025-08-25 15:40:00  
**作者：** hongdong.xie

## 适用场景
- REST API接口设计和开发
- API文档生成和维护
- 接口版本管理
- 外部系统集成接口

## API设计原则

### 1. RESTful设计规范
```python
# 资源URL设计规范
GET     /api/v1/apps/              # 获取应用列表
POST    /api/v1/apps/              # 创建应用
GET     /api/v1/apps/{id}/         # 获取单个应用
PUT     /api/v1/apps/{id}/         # 更新应用（全量）
PATCH   /api/v1/apps/{id}/         # 更新应用（部分）
DELETE  /api/v1/apps/{id}/         # 删除应用

# 嵌套资源
GET     /api/v1/apps/{id}/modules/ # 获取应用的模块列表
POST    /api/v1/apps/{id}/modules/ # 为应用创建模块
```

### 2. 统一响应格式
```python
from rest_framework.response import Response
from typing import Any, Optional, Dict, Union

class APIResponse:
    """
    统一API响应格式
    <AUTHOR>
    @date 2025-08-25 15:40:00
    """
    
    @staticmethod
    def success(data: Any = None, message: str = "操作成功") -> Dict[str, Any]:
        """成功响应"""
        return {
            "code": 0,
            "message": message,
            "data": data,
            "timestamp": int(time.time() * 1000)
        }
    
    @staticmethod
    def error(message: str = "操作失败", code: int = -1, 
              data: Any = None) -> Dict[str, Any]:
        """错误响应"""
        return {
            "code": code,
            "message": message,
            "data": data,
            "timestamp": int(time.time() * 1000)
        }

# 使用示例
class AppViewSet(viewsets.ModelViewSet):
    
    def list(self, request):
        """获取应用列表"""
        queryset = self.get_queryset()
        serializer = self.get_serializer(queryset, many=True)
        return Response(APIResponse.success(
            data=serializer.data,
            message="获取应用列表成功"
        ))
```

### 3. 分页响应格式
```python
from rest_framework.pagination import PageNumberPagination

class StandardPageNumberPagination(PageNumberPagination):
    """
    标准分页器
    <AUTHOR>
    @date 2025-08-25 15:40:00
    """
    page_size = 20
    page_size_query_param = 'page_size'
    max_page_size = 100
    
    def get_paginated_response(self, data):
        """自定义分页响应格式"""
        return Response(APIResponse.success(
            data={
                'items': data,
                'pagination': {
                    'current_page': self.page.number,
                    'page_size': self.page_size,
                    'total_pages': self.page.paginator.num_pages,
                    'total_items': self.page.paginator.count,
                    'has_next': self.page.has_next(),
                    'has_previous': self.page.has_previous()
                }
            }
        ))
```

## API版本管理

### 1. URL版本控制
```python
# urls.py
from django.urls import path, include

urlpatterns = [
    path('api/v1/', include('app_mgt.urls.v1')),
    path('api/v2/', include('app_mgt.urls.v2')),
]

# app_mgt/urls/v1.py
from rest_framework.routers import DefaultRouter
from ..views import v1

router = DefaultRouter()
router.register(r'apps', v1.AppViewSet)

urlpatterns = router.urls
```

### 2. 版本兼容性处理
```python
class AppViewSetV1(viewsets.ModelViewSet):
    """
    应用管理API v1版本
    <AUTHOR>
    @date 2025-08-25 15:40:00
    """
    serializer_class = AppSerializerV1
    
    def get_serializer_class(self):
        """根据版本选择序列化器"""
        version = getattr(self.request, 'version', 'v1')
        if version == 'v2':
            return AppSerializerV2
        return AppSerializerV1

class AppSerializerV1(serializers.ModelSerializer):
    """应用序列化器 v1版本"""
    class Meta:
        model = AppInfo
        fields = ['id', 'app_name', 'app_code']

class AppSerializerV2(serializers.ModelSerializer):
    """应用序列化器 v2版本（新增字段）"""
    class Meta:
        model = AppInfo
        fields = ['id', 'app_name', 'app_code', 'description', 'status']
```

## 参数验证和序列化

### 1. 输入参数验证
```python
from rest_framework import serializers
from rest_framework.validators import ValidationError
from typing import Dict, Any, List

class AppCreateSerializer(serializers.Serializer):
    """
    应用创建序列化器
    <AUTHOR>
    @date 2025-08-25 15:40:00
    """
    app_name = serializers.CharField(
        max_length=100,
        required=True,
        error_messages={
            'required': '应用名称不能为空',
            'max_length': '应用名称不能超过100个字符'
        }
    )
    app_code = serializers.CharField(
        max_length=50,
        required=True,
        validators=[validate_app_code]
    )
    description = serializers.CharField(
        max_length=500,
        required=False,
        allow_blank=True
    )
    team_id = serializers.IntegerField(required=True)
    
    def validate_app_code(self, value: str) -> str:
        """验证应用编码"""
        if not re.match(r'^[a-z0-9-]+$', value):
            raise ValidationError('应用编码只能包含小写字母、数字和连字符')
        
        if AppInfo.objects.filter(app_code=value).exists():
            raise ValidationError('应用编码已存在')
        
        return value
    
    def validate(self, attrs: Dict[str, Any]) -> Dict[str, Any]:
        """全局验证"""
        team_id = attrs.get('team_id')
        if not Team.objects.filter(id=team_id).exists():
            raise ValidationError({'team_id': '团队不存在'})
        
        return attrs

def validate_app_code(value: str) -> str:
    """应用编码验证器"""
    if len(value) < 3:
        raise ValidationError('应用编码至少3个字符')
    return value
```

### 2. 查询参数处理
```python
class AppListSerializer(serializers.Serializer):
    """
    应用列表查询参数序列化器
    <AUTHOR>
    @date 2025-08-25 15:40:00
    """
    keyword = serializers.CharField(required=False, help_text="关键字搜索")
    team_id = serializers.IntegerField(required=False, help_text="团队ID")
    status = serializers.ChoiceField(
        choices=['active', 'inactive'],
        required=False,
        help_text="应用状态"
    )
    created_start = serializers.DateTimeField(required=False, help_text="创建时间开始")
    created_end = serializers.DateTimeField(required=False, help_text="创建时间结束")
    
    page = serializers.IntegerField(default=1, min_value=1)
    page_size = serializers.IntegerField(default=20, min_value=1, max_value=100)

class AppViewSet(viewsets.ModelViewSet):
    
    def list(self, request):
        """获取应用列表"""
        # 验证查询参数
        query_serializer = AppListSerializer(data=request.query_params)
        query_serializer.is_valid(raise_exception=True)
        
        # 构建查询条件
        queryset = self.get_queryset()
        filters = {}
        
        if query_serializer.validated_data.get('keyword'):
            keyword = query_serializer.validated_data['keyword']
            queryset = queryset.filter(
                Q(app_name__icontains=keyword) | 
                Q(description__icontains=keyword)
            )
        
        # 分页和序列化
        page = self.paginate_queryset(queryset)
        serializer = self.get_serializer(page, many=True)
        
        return self.get_paginated_response(serializer.data)
```

## 权限和认证

### 1. JWT认证
```python
from rest_framework_simplejwt.authentication import JWTAuthentication
from rest_framework.permissions import IsAuthenticated
from django.contrib.auth.models import User

class CustomJWTAuthentication(JWTAuthentication):
    """
    自定义JWT认证
    <AUTHOR>
    @date 2025-08-25 15:40:00
    """
    
    def get_user(self, validated_token):
        """获取用户信息"""
        user = super().get_user(validated_token)
        
        # 检查用户状态
        if not user.is_active:
            raise AuthenticationFailed('用户已被禁用')
        
        # 记录访问日志
        logger.info(f"用户 {user.username} 通过JWT认证")
        
        return user
```

### 2. 权限控制
```python
from rest_framework.permissions import BasePermission
from rest_framework.request import Request
from rest_framework.views import APIView

class IsAppOwnerOrAdmin(BasePermission):
    """
    应用所有者或管理员权限
    <AUTHOR>
    @date 2025-08-25 15:40:00
    """
    
    def has_permission(self, request: Request, view: APIView) -> bool:
        """检查是否有访问权限"""
        return request.user and request.user.is_authenticated
    
    def has_object_permission(self, request: Request, view: APIView, obj) -> bool:
        """检查对象级权限"""
        # 管理员拥有所有权限
        if request.user.is_staff:
            return True
        
        # 只读操作允许团队成员访问
        if request.method in ['GET', 'HEAD', 'OPTIONS']:
            return obj.team.members.filter(id=request.user.id).exists()
        
        # 写操作只允许应用负责人
        return obj.owner == request.user

class AppViewSet(viewsets.ModelViewSet):
    permission_classes = [IsAuthenticated, IsAppOwnerOrAdmin]
```

### 3. API限流
```python
from rest_framework.throttling import UserRateThrottle

class CustomUserRateThrottle(UserRateThrottle):
    """
    自定义用户限流
    <AUTHOR>
    @date 2025-08-25 15:40:00
    """
    scope = 'user'
    
    def get_cache_key(self, request, view):
        """自定义缓存键"""
        if request.user and request.user.is_authenticated:
            ident = request.user.id
        else:
            ident = self.get_ident(request)
        
        return self.cache_format % {
            'scope': self.scope,
            'ident': ident
        }

# settings.py
REST_FRAMEWORK = {
    'DEFAULT_THROTTLE_CLASSES': [
        'rest_framework.throttling.UserRateThrottle'
    ],
    'DEFAULT_THROTTLE_RATES': {
        'user': '1000/hour',
        'anon': '100/hour',
    }
}
```

## 错误处理和日志

### 1. 异常处理
```python
import logging
from rest_framework.views import exception_handler
from rest_framework import status
from django.core.exceptions import ValidationError

logger = logging.getLogger(__name__)

class APIException(Exception):
    """
    API自定义异常
    <AUTHOR>
    @date 2025-08-25 15:40:00
    """
    def __init__(self, message: str, code: int = -1, status_code: int = 400):
        self.message = message
        self.code = code
        self.status_code = status_code
        super().__init__(message)

def custom_exception_handler(exc, context):
    """自定义异常处理器"""
    response = exception_handler(exc, context)
    
    # 获取视图和请求信息
    view = context.get('view')
    request = context.get('request')
    
    # 记录异常日志
    logger.error(
        f"API异常: {type(exc).__name__}: {str(exc)}",
        extra={
            'view': view.__class__.__name__ if view else None,
            'method': request.method if request else None,
            'path': request.path if request else None,
            'user': request.user.username if request and request.user.is_authenticated else 'anonymous'
        },
        exc_info=True
    )
    
    if response is not None:
        # 自定义异常响应格式
        if isinstance(exc, APIException):
            response.data = APIResponse.error(
                message=exc.message,
                code=exc.code
            )
        else:
            response.data = APIResponse.error(
                message="系统错误，请稍后重试",
                code=-1
            )
    
    return response
```

### 2. 请求日志中间件
```python
import time
import json
import logging
from django.utils.deprecation import MiddlewareMixin

logger = logging.getLogger('api_access')

class APILoggingMiddleware(MiddlewareMixin):
    """
    API访问日志中间件
    <AUTHOR>
    @date 2025-08-25 15:40:00
    """
    
    def process_request(self, request):
        """请求开始处理"""
        request.start_time = time.time()
        
        # 记录请求信息
        if request.path.startswith('/api/'):
            try:
                request_body = json.loads(request.body) if request.body else {}
            except:
                request_body = {}
            
            logger.info(f"API请求开始", extra={
                'method': request.method,
                'path': request.path,
                'query_params': dict(request.GET),
                'request_body': request_body,
                'user': request.user.username if hasattr(request, 'user') and request.user.is_authenticated else 'anonymous',
                'ip': self.get_client_ip(request)
            })
    
    def process_response(self, request, response):
        """请求处理结束"""
        if hasattr(request, 'start_time') and request.path.startswith('/api/'):
            duration = time.time() - request.start_time
            
            # 记录响应信息
            logger.info(f"API请求完成", extra={
                'method': request.method,
                'path': request.path,
                'status_code': response.status_code,
                'duration_ms': round(duration * 1000, 2),
                'user': request.user.username if hasattr(request, 'user') and request.user.is_authenticated else 'anonymous'
            })
        
        return response
    
    def get_client_ip(self, request):
        """获取客户端IP"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip
```

## API文档生成

### 1. 使用drf-spectacular生成OpenAPI文档
```python
from drf_spectacular.utils import extend_schema, OpenApiParameter
from drf_spectacular.types import OpenApiTypes

class AppViewSet(viewsets.ModelViewSet):
    """
    应用管理API
    <AUTHOR>
    @date 2025-08-25 15:40:00
    """
    
    @extend_schema(
        summary="获取应用列表",
        description="获取应用列表，支持关键字搜索和筛选",
        parameters=[
            OpenApiParameter(
                name='keyword',
                type=OpenApiTypes.STR,
                location=OpenApiParameter.QUERY,
                description='关键字搜索'
            ),
            OpenApiParameter(
                name='team_id',
                type=OpenApiTypes.INT,
                location=OpenApiParameter.QUERY,
                description='团队ID'
            ),
        ],
        responses={
            200: AppSerializer(many=True),
            400: "参数错误",
            401: "未授权访问"
        },
        tags=["应用管理"]
    )
    def list(self, request):
        """获取应用列表"""
        pass
    
    @extend_schema(
        summary="创建应用",
        description="创建新的应用",
        request=AppCreateSerializer,
        responses={
            201: AppSerializer,
            400: "创建失败"
        },
        tags=["应用管理"]
    )
    def create(self, request):
        """创建应用"""
        pass
```

### 2. API文档配置
```python
# settings.py
INSTALLED_APPS = [
    'drf_spectacular',
]

REST_FRAMEWORK = {
    'DEFAULT_SCHEMA_CLASS': 'drf_spectacular.openapi.AutoSchema',
}

SPECTACULAR_SETTINGS = {
    'TITLE': 'PA平台API文档',
    'DESCRIPTION': '企业级DevOps自动化平台API接口文档',
    'VERSION': '1.0.0',
    'SERVE_INCLUDE_SCHEMA': False,
    'COMPONENT_SPLIT_REQUEST': True,
    'SCHEMA_PATH_PREFIX': '/api/v[0-9]',
}

# urls.py
from drf_spectacular.views import (
    SpectacularAPIView,
    SpectacularRedocView,
    SpectacularSwaggerView,
)

urlpatterns = [
    path('api/schema/', SpectacularAPIView.as_view(), name='schema'),
    path('api/docs/', SpectacularSwaggerView.as_view(url_name='schema'), name='swagger-ui'),
    path('api/redoc/', SpectacularRedocView.as_view(url_name='schema'), name='redoc'),
]
```

## API测试规范

### 1. 接口测试
```python
from rest_framework.test import APITestCase
from rest_framework import status
from django.contrib.auth.models import User
from django.urls import reverse

class AppAPITest(APITestCase):
    """
    应用管理API测试
    <AUTHOR>
    @date 2025-08-25 15:40:00
    """
    
    def setUp(self):
        """测试前置条件"""
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
        self.client.force_authenticate(user=self.user)
        
        self.app_data = {
            'app_name': '测试应用',
            'app_code': 'test-app',
            'description': '这是测试应用'
        }
    
    def test_create_app_success(self):
        """测试创建应用成功"""
        url = reverse('app-list')
        response = self.client.post(url, self.app_data)
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['code'], 0)
        self.assertEqual(response.data['message'], '创建成功')
        
    def test_create_app_duplicate_code(self):
        """测试重复应用编码"""
        # 先创建一个应用
        self.client.post(reverse('app-list'), self.app_data)
        
        # 再次创建相同编码的应用
        response = self.client.post(reverse('app-list'), self.app_data)
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['code'], -1)
    
    def test_list_apps_with_pagination(self):
        """测试分页获取应用列表"""
        # 创建多个应用
        for i in range(25):
            data = self.app_data.copy()
            data['app_code'] = f'test-app-{i}'
            data['app_name'] = f'测试应用{i}'
            self.client.post(reverse('app-list'), data)
        
        # 测试分页
        url = reverse('app-list')
        response = self.client.get(url, {'page': 1, 'page_size': 10})
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['data']['items']), 10)
        self.assertEqual(response.data['data']['pagination']['total_items'], 25)
```

## 性能优化

### 1. 数据库查询优化
```python
class AppViewSet(viewsets.ModelViewSet):
    
    def get_queryset(self):
        """优化查询集"""
        return AppInfo.objects.select_related(
            'team', 'owner'
        ).prefetch_related(
            'modules', 'deployment_envs'
        ).filter(is_deleted=False)
```

### 2. 响应缓存
```python
from django.core.cache import cache
from rest_framework.decorators import action

class AppViewSet(viewsets.ModelViewSet):
    
    @action(detail=False, methods=['get'])
    def statistics(self, request):
        """获取应用统计（带缓存）"""
        cache_key = f"app_statistics_{request.user.id}"
        stats = cache.get(cache_key)
        
        if stats is None:
            stats = {
                'total_count': AppInfo.objects.count(),
                'my_apps_count': AppInfo.objects.filter(owner=request.user).count(),
                'team_apps_count': AppInfo.objects.filter(
                    team__members=request.user
                ).count()
            }
            cache.set(cache_key, stats, 300)  # 缓存5分钟
        
        return Response(APIResponse.success(data=stats))
```

---

**注意：API接口开发必须严格遵循上述规范，确保接口的一致性、安全性和可维护性。**