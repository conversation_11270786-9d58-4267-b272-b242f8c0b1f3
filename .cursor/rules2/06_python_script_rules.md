# Python脚本开发规范

**文档创建时间：** 2025-08-25 16:05:00  
**作者：** hongdong.xie

## 适用场景
- 自动化脚本开发（be-scripts、hm-scripts、qa-scripts）
- DevOps工具和实用程序
- 数据处理和分析脚本
- 测试辅助脚本
- 系统管理和监控脚本

## Python代码基础规范

### 1. 文件结构和模板
```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
脚本功能描述

这个脚本用于...（详细说明脚本的目的和功能）

使用方法:
    python script_name.py [options]

示例:
    python app_deploy.py --app-name test-app --env prod
    python data_migration.py --source db1 --target db2

作者: hongdong.xie
创建时间: 2025-08-25 16:05:00
版本: 1.0.0
"""

import os
import sys
import argparse
import logging
import json
from pathlib import Path
from typing import Optional, List, Dict, Any
from dataclasses import dataclass
from datetime import datetime

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent))

# 导入项目模块
from common.config import load_config
from common.logger import setup_logger
from common.exceptions import ScriptException


@dataclass
class ScriptConfig:
    """
    脚本配置类
    
    <AUTHOR>
    @date 2025-08-25 16:05:00
    """
    app_name: str
    environment: str
    debug: bool = False
    dry_run: bool = False
    timeout: int = 300
    
    @classmethod
    def from_args(cls, args: argparse.Namespace) -> 'ScriptConfig':
        """从命令行参数创建配置"""
        return cls(
            app_name=args.app_name,
            environment=args.environment,
            debug=args.debug,
            dry_run=args.dry_run,
            timeout=args.timeout
        )


class ScriptExecutor:
    """
    脚本执行器基类
    
    提供脚本执行的通用功能和生命周期管理
    
    <AUTHOR>
    @date 2025-08-25 16:05:00
    """
    
    def __init__(self, config: ScriptConfig):
        """
        初始化脚本执行器
        
        Args:
            config: 脚本配置
        """
        self.config = config
        self.logger = setup_logger(
            name=self.__class__.__name__,
            level=logging.DEBUG if config.debug else logging.INFO
        )
        self.start_time = datetime.now()
        
    def pre_execute(self) -> bool:
        """
        执行前置检查
        
        Returns:
            bool: 检查是否通过
        """
        self.logger.info(f"开始执行脚本: {self.__class__.__name__}")
        self.logger.info(f"配置参数: {self.config}")
        
        # 检查必要的环境和权限
        if not self._check_prerequisites():
            return False
            
        if self.config.dry_run:
            self.logger.info("运行在DRY-RUN模式，不会执行实际操作")
        
        return True
    
    def execute(self) -> bool:
        """
        执行主要逻辑（子类需要实现）
        
        Returns:
            bool: 执行是否成功
        """
        raise NotImplementedError("子类必须实现execute方法")
    
    def post_execute(self, success: bool) -> None:
        """
        执行后处理
        
        Args:
            success: 执行是否成功
        """
        duration = datetime.now() - self.start_time
        
        if success:
            self.logger.info(f"脚本执行成功，耗时: {duration}")
        else:
            self.logger.error(f"脚本执行失败，耗时: {duration}")
        
        # 清理资源
        self._cleanup()
    
    def run(self) -> int:
        """
        运行脚本
        
        Returns:
            int: 退出码，0表示成功，非0表示失败
        """
        try:
            if not self.pre_execute():
                return 1
            
            success = self.execute()
            self.post_execute(success)
            
            return 0 if success else 1
            
        except KeyboardInterrupt:
            self.logger.warning("脚本被用户中断")
            return 130
        except Exception as e:
            self.logger.error(f"脚本执行异常: {str(e)}", exc_info=True)
            return 1
    
    def _check_prerequisites(self) -> bool:
        """检查前置条件"""
        return True
    
    def _cleanup(self) -> None:
        """清理资源"""
        pass


def create_argument_parser() -> argparse.ArgumentParser:
    """
    创建命令行参数解析器
    
    Returns:
        argparse.ArgumentParser: 参数解析器
    """
    parser = argparse.ArgumentParser(
        description='脚本描述',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  %(prog)s --app-name test-app --env prod
  %(prog)s --app-name test-app --env test --dry-run
        """
    )
    
    # 必需参数
    parser.add_argument(
        '--app-name',
        required=True,
        help='应用名称'
    )
    
    parser.add_argument(
        '--environment', '--env',
        required=True,
        choices=['dev', 'test', 'staging', 'prod'],
        help='目标环境'
    )
    
    # 可选参数
    parser.add_argument(
        '--config-file',
        type=Path,
        help='配置文件路径'
    )
    
    parser.add_argument(
        '--timeout',
        type=int,
        default=300,
        help='超时时间（秒），默认300秒'
    )
    
    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='干跑模式，不执行实际操作'
    )
    
    parser.add_argument(
        '--debug',
        action='store_true',
        help='调试模式'
    )
    
    parser.add_argument(
        '--version',
        action='version',
        version='%(prog)s 1.0.0'
    )
    
    return parser


def main():
    """主函数"""
    parser = create_argument_parser()
    args = parser.parse_args()
    
    # 创建配置
    config = ScriptConfig.from_args(args)
    
    # 创建并运行脚本执行器
    executor = ConcreteScriptExecutor(config)
    exit_code = executor.run()
    
    sys.exit(exit_code)


class ConcreteScriptExecutor(ScriptExecutor):
    """
    具体的脚本执行器实现
    
    <AUTHOR>
    @date 2025-08-25 16:05:00
    """
    
    def execute(self) -> bool:
        """执行具体的业务逻辑"""
        try:
            # 具体的业务逻辑实现
            self.logger.info(f"开始处理应用: {self.config.app_name}")
            
            # 示例：执行部署步骤
            steps = [
                self._validate_app,
                self._prepare_environment,
                self._deploy_application,
                self._verify_deployment
            ]
            
            for i, step in enumerate(steps, 1):
                self.logger.info(f"执行步骤 {i}/{len(steps)}: {step.__name__}")
                
                if not step():
                    self.logger.error(f"步骤 {step.__name__} 执行失败")
                    return False
                    
                self.logger.info(f"步骤 {step.__name__} 执行成功")
            
            return True
            
        except Exception as e:
            self.logger.error(f"执行过程中发生错误: {str(e)}")
            return False
    
    def _validate_app(self) -> bool:
        """验证应用配置"""
        # 实现应用验证逻辑
        return True
    
    def _prepare_environment(self) -> bool:
        """准备环境"""
        # 实现环境准备逻辑
        return True
    
    def _deploy_application(self) -> bool:
        """部署应用"""
        # 实现部署逻辑
        return True
    
    def _verify_deployment(self) -> bool:
        """验证部署结果"""
        # 实现验证逻辑
        return True


if __name__ == '__main__':
    main()
```

### 2. 通用模块和工具类
```python
# common/config.py
"""
配置管理模块

<AUTHOR>
@date 2025-08-25 16:05:00
"""

import os
import json
import yaml
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass


@dataclass
class DatabaseConfig:
    """数据库配置"""
    host: str
    port: int
    database: str
    username: str
    password: str
    charset: str = 'utf8mb4'


@dataclass
class APIConfig:
    """API配置"""
    base_url: str
    timeout: int = 30
    auth_token: Optional[str] = None


class ConfigManager:
    """
    配置管理器
    
    支持从环境变量、配置文件等多种来源加载配置
    
    <AUTHOR>
    @date 2025-08-25 16:05:00
    """
    
    def __init__(self, config_file: Optional[Path] = None):
        """
        初始化配置管理器
        
        Args:
            config_file: 配置文件路径
        """
        self.config_file = config_file or Path('settings.ini')
        self._config_data = {}
        self._load_config()
    
    def _load_config(self) -> None:
        """加载配置"""
        # 从环境变量加载
        self._load_from_env()
        
        # 从配置文件加载
        if self.config_file.exists():
            self._load_from_file()
    
    def _load_from_env(self) -> None:
        """从环境变量加载配置"""
        env_mappings = {
            'DB_HOST': 'database.host',
            'DB_PORT': 'database.port',
            'DB_NAME': 'database.name',
            'DB_USER': 'database.username',
            'DB_PASSWORD': 'database.password',
            'API_BASE_URL': 'api.base_url',
            'API_TOKEN': 'api.auth_token'
        }
        
        for env_key, config_key in env_mappings.items():
            value = os.getenv(env_key)
            if value:
                self._set_nested_value(config_key, value)
    
    def _load_from_file(self) -> None:
        """从配置文件加载"""
        try:
            if self.config_file.suffix.lower() == '.json':
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
            elif self.config_file.suffix.lower() in ['.yml', '.yaml']:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    data = yaml.safe_load(f)
            else:
                # INI格式
                import configparser
                config = configparser.ConfigParser()
                config.read(self.config_file, encoding='utf-8')
                data = {section: dict(config[section]) for section in config.sections()}
            
            self._merge_config(data)
            
        except Exception as e:
            raise RuntimeError(f"加载配置文件失败: {e}")
    
    def _set_nested_value(self, key: str, value: Any) -> None:
        """设置嵌套键值"""
        keys = key.split('.')
        current = self._config_data
        
        for k in keys[:-1]:
            if k not in current:
                current[k] = {}
            current = current[k]
        
        current[keys[-1]] = value
    
    def _merge_config(self, data: Dict[str, Any]) -> None:
        """合并配置数据"""
        def merge_dict(target: dict, source: dict):
            for key, value in source.items():
                if key in target and isinstance(target[key], dict) and isinstance(value, dict):
                    merge_dict(target[key], value)
                else:
                    target[key] = value
        
        merge_dict(self._config_data, data)
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key: 配置键，支持点号分隔的嵌套键
            default: 默认值
            
        Returns:
            配置值
        """
        keys = key.split('.')
        current = self._config_data
        
        try:
            for k in keys:
                current = current[k]
            return current
        except (KeyError, TypeError):
            return default
    
    def get_database_config(self) -> DatabaseConfig:
        """获取数据库配置"""
        db_config = self.get('database', {})
        return DatabaseConfig(
            host=db_config.get('host', 'localhost'),
            port=int(db_config.get('port', 3306)),
            database=db_config.get('name', 'spider'),
            username=db_config.get('username', 'root'),
            password=db_config.get('password', '')
        )
    
    def get_api_config(self) -> APIConfig:
        """获取API配置"""
        api_config = self.get('api', {})
        return APIConfig(
            base_url=api_config.get('base_url', 'http://localhost:9011/api'),
            timeout=int(api_config.get('timeout', 30)),
            auth_token=api_config.get('auth_token')
        )


def load_config(config_file: Optional[Path] = None) -> ConfigManager:
    """
    加载配置
    
    Args:
        config_file: 配置文件路径
        
    Returns:
        ConfigManager: 配置管理器实例
    """
    return ConfigManager(config_file)
```

```python
# common/logger.py
"""
日志管理模块

<AUTHOR>
@date 2025-08-25 16:05:00
"""

import os
import logging
import logging.handlers
from pathlib import Path
from typing import Optional
from datetime import datetime


class ColoredFormatter(logging.Formatter):
    """带颜色的日志格式化器"""
    
    COLORS = {
        'DEBUG': '\033[36m',      # 青色
        'INFO': '\033[32m',       # 绿色
        'WARNING': '\033[33m',    # 黄色
        'ERROR': '\033[31m',      # 红色
        'CRITICAL': '\033[35m',   # 紫色
        'RESET': '\033[0m'        # 重置
    }
    
    def format(self, record):
        """格式化日志记录"""
        log_message = super().format(record)
        
        # 只在终端输出时添加颜色
        if hasattr(self, '_use_colors') and self._use_colors:
            color = self.COLORS.get(record.levelname, self.COLORS['RESET'])
            reset = self.COLORS['RESET']
            log_message = f"{color}{log_message}{reset}"
        
        return log_message


def setup_logger(name: str, 
                 level: int = logging.INFO,
                 log_file: Optional[Path] = None,
                 console: bool = True) -> logging.Logger:
    """
    设置日志记录器
    
    Args:
        name: 日志记录器名称
        level: 日志级别
        log_file: 日志文件路径
        console: 是否输出到控制台
        
    Returns:
        logging.Logger: 配置好的日志记录器
    """
    logger = logging.getLogger(name)
    logger.setLevel(level)
    
    # 避免重复添加处理器
    if logger.handlers:
        return logger
    
    # 日志格式
    formatter = logging.Formatter(
        fmt='%(asctime)s [%(levelname)8s] %(name)s: %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # 控制台输出
    if console:
        console_handler = logging.StreamHandler()
        console_handler.setLevel(level)
        
        # 为控制台添加颜色
        colored_formatter = ColoredFormatter(
            fmt='%(asctime)s [%(levelname)8s] %(name)s: %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        colored_formatter._use_colors = os.isatty(console_handler.stream.fileno())
        console_handler.setFormatter(colored_formatter)
        
        logger.addHandler(console_handler)
    
    # 文件输出
    if log_file:
        log_file = Path(log_file)
        log_file.parent.mkdir(parents=True, exist_ok=True)
        
        # 使用RotatingFileHandler支持日志轮转
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=10 * 1024 * 1024,  # 10MB
            backupCount=5,
            encoding='utf-8'
        )
        file_handler.setLevel(level)
        file_handler.setFormatter(formatter)
        
        logger.addHandler(file_handler)
    
    return logger


class ScriptLogger:
    """
    脚本专用日志记录器
    
    提供结构化的日志记录功能
    
    <AUTHOR>
    @date 2025-08-25 16:05:00
    """
    
    def __init__(self, name: str, log_dir: Optional[Path] = None):
        """
        初始化脚本日志记录器
        
        Args:
            name: 脚本名称
            log_dir: 日志目录
        """
        self.name = name
        self.log_dir = log_dir or Path('logs')
        
        # 创建日志文件名
        timestamp = datetime.now().strftime('%Y%m%d')
        log_file = self.log_dir / f'{name}_{timestamp}.log'
        
        self.logger = setup_logger(
            name=name,
            log_file=log_file,
            console=True
        )
        
        # 记录脚本开始
        self.logger.info(f"=== {name} 脚本开始执行 ===")
    
    def info(self, message: str, **kwargs) -> None:
        """记录信息日志"""
        self._log_with_context('info', message, **kwargs)
    
    def warning(self, message: str, **kwargs) -> None:
        """记录警告日志"""
        self._log_with_context('warning', message, **kwargs)
    
    def error(self, message: str, **kwargs) -> None:
        """记录错误日志"""
        self._log_with_context('error', message, **kwargs)
    
    def debug(self, message: str, **kwargs) -> None:
        """记录调试日志"""
        self._log_with_context('debug', message, **kwargs)
    
    def step(self, step_name: str, message: str = None) -> None:
        """记录执行步骤"""
        step_message = f"【{step_name}】"
        if message:
            step_message += f" {message}"
        
        self.logger.info(step_message)
    
    def result(self, success: bool, message: str = None) -> None:
        """记录执行结果"""
        status = "成功" if success else "失败"
        result_message = f"执行结果: {status}"
        
        if message:
            result_message += f" - {message}"
        
        if success:
            self.logger.info(result_message)
        else:
            self.logger.error(result_message)
    
    def _log_with_context(self, level: str, message: str, **kwargs) -> None:
        """带上下文信息的日志记录"""
        if kwargs:
            context_info = ', '.join([f"{k}={v}" for k, v in kwargs.items()])
            message = f"{message} [{context_info}]"
        
        getattr(self.logger, level)(message)
    
    def finalize(self, success: bool) -> None:
        """结束脚本日志记录"""
        status = "成功" if success else "失败"
        self.logger.info(f"=== {self.name} 脚本执行{status} ===")
```

### 3. 数据库操作封装
```python
# common/database.py
"""
数据库操作封装

<AUTHOR>
@date 2025-08-25 16:05:00
"""

import pymysql
import logging
from contextlib import contextmanager
from typing import Dict, List, Any, Optional, Generator, Tuple
from dataclasses import dataclass
from common.config import DatabaseConfig


@dataclass
class QueryResult:
    """查询结果"""
    rows: List[Dict[str, Any]]
    affected_rows: int = 0
    insert_id: Optional[int] = None


class DatabaseManager:
    """
    数据库管理器
    
    提供数据库连接池和基础操作封装
    
    <AUTHOR>
    @date 2025-08-25 16:05:00
    """
    
    def __init__(self, config: DatabaseConfig):
        """
        初始化数据库管理器
        
        Args:
            config: 数据库配置
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        self._connection = None
    
    def connect(self) -> None:
        """建立数据库连接"""
        try:
            self._connection = pymysql.connect(
                host=self.config.host,
                port=self.config.port,
                database=self.config.database,
                user=self.config.username,
                password=self.config.password,
                charset=self.config.charset,
                autocommit=False,
                cursorclass=pymysql.cursors.DictCursor
            )
            self.logger.info(f"数据库连接成功: {self.config.host}:{self.config.port}/{self.config.database}")
        except Exception as e:
            self.logger.error(f"数据库连接失败: {e}")
            raise
    
    def disconnect(self) -> None:
        """关闭数据库连接"""
        if self._connection:
            self._connection.close()
            self._connection = None
            self.logger.info("数据库连接已关闭")
    
    @contextmanager
    def get_cursor(self) -> Generator[pymysql.cursors.Cursor, None, None]:
        """获取数据库游标（上下文管理器）"""
        if not self._connection or not self._connection.open:
            self.connect()
        
        cursor = self._connection.cursor()
        try:
            yield cursor
        finally:
            cursor.close()
    
    def execute_query(self, sql: str, params: Tuple = None) -> QueryResult:
        """
        执行查询SQL
        
        Args:
            sql: SQL语句
            params: 参数
            
        Returns:
            QueryResult: 查询结果
        """
        with self.get_cursor() as cursor:
            try:
                affected_rows = cursor.execute(sql, params)
                rows = cursor.fetchall()
                
                self.logger.debug(f"执行查询: {sql[:100]}... 影响行数: {affected_rows}")
                
                return QueryResult(
                    rows=rows or [],
                    affected_rows=affected_rows
                )
            except Exception as e:
                self.logger.error(f"查询执行失败: {e}\nSQL: {sql}")
                raise
    
    def execute_update(self, sql: str, params: Tuple = None, commit: bool = True) -> QueryResult:
        """
        执行更新SQL
        
        Args:
            sql: SQL语句
            params: 参数
            commit: 是否自动提交
            
        Returns:
            QueryResult: 执行结果
        """
        with self.get_cursor() as cursor:
            try:
                affected_rows = cursor.execute(sql, params)
                insert_id = cursor.lastrowid
                
                if commit:
                    self._connection.commit()
                
                self.logger.debug(f"执行更新: {sql[:100]}... 影响行数: {affected_rows}")
                
                return QueryResult(
                    rows=[],
                    affected_rows=affected_rows,
                    insert_id=insert_id if insert_id else None
                )
            except Exception as e:
                self.logger.error(f"更新执行失败: {e}\nSQL: {sql}")
                if commit:
                    self._connection.rollback()
                raise
    
    def execute_batch(self, sql: str, params_list: List[Tuple], commit: bool = True) -> QueryResult:
        """
        批量执行SQL
        
        Args:
            sql: SQL语句
            params_list: 参数列表
            commit: 是否自动提交
            
        Returns:
            QueryResult: 执行结果
        """
        with self.get_cursor() as cursor:
            try:
                affected_rows = cursor.executemany(sql, params_list)
                
                if commit:
                    self._connection.commit()
                
                self.logger.debug(f"批量执行: {sql[:100]}... 批次数: {len(params_list)}, 影响行数: {affected_rows}")
                
                return QueryResult(
                    rows=[],
                    affected_rows=affected_rows
                )
            except Exception as e:
                self.logger.error(f"批量执行失败: {e}\nSQL: {sql}")
                if commit:
                    self._connection.rollback()
                raise
    
    @contextmanager
    def transaction(self) -> Generator[None, None, None]:
        """事务上下文管理器"""
        if not self._connection or not self._connection.open:
            self.connect()
        
        try:
            self._connection.begin()
            yield
            self._connection.commit()
            self.logger.debug("事务提交成功")
        except Exception as e:
            self._connection.rollback()
            self.logger.error(f"事务回滚: {e}")
            raise
    
    def get_table_info(self, table_name: str) -> List[Dict[str, Any]]:
        """
        获取表结构信息
        
        Args:
            table_name: 表名
            
        Returns:
            表结构信息列表
        """
        sql = "DESCRIBE `{}`".format(table_name)
        result = self.execute_query(sql)
        return result.rows
    
    def table_exists(self, table_name: str) -> bool:
        """
        检查表是否存在
        
        Args:
            table_name: 表名
            
        Returns:
            bool: 表是否存在
        """
        sql = """
        SELECT COUNT(*) as count
        FROM information_schema.tables
        WHERE table_schema = %s AND table_name = %s
        """
        result = self.execute_query(sql, (self.config.database, table_name))
        return result.rows[0]['count'] > 0
    
    def __enter__(self):
        """进入上下文管理器"""
        self.connect()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """退出上下文管理器"""
        self.disconnect()
```

### 4. HTTP客户端封装
```python
# common/http_client.py
"""
HTTP客户端封装

<AUTHOR>
@date 2025-08-25 16:05:00
"""

import requests
import json
import time
import logging
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from urllib.parse import urljoin, urlparse
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry


@dataclass
class APIResponse:
    """API响应"""
    status_code: int
    data: Any
    headers: Dict[str, str]
    success: bool
    error_message: Optional[str] = None


class HTTPClient:
    """
    HTTP客户端
    
    封装了常用的HTTP请求功能，支持重试、超时、认证等
    
    <AUTHOR>
    @date 2025-08-25 16:05:00
    """
    
    def __init__(self, 
                 base_url: str,
                 timeout: int = 30,
                 auth_token: Optional[str] = None,
                 max_retries: int = 3):
        """
        初始化HTTP客户端
        
        Args:
            base_url: 基础URL
            timeout: 超时时间（秒）
            auth_token: 认证令牌
            max_retries: 最大重试次数
        """
        self.base_url = base_url.rstrip('/')
        self.timeout = timeout
        self.auth_token = auth_token
        self.logger = logging.getLogger(__name__)
        
        # 创建session
        self.session = requests.Session()
        
        # 设置重试策略
        retry_strategy = Retry(
            total=max_retries,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["HEAD", "GET", "OPTIONS", "POST", "PUT", "PATCH", "DELETE"]
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
        
        # 设置默认头部
        self.session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': 'PA-Script/1.0'
        })
        
        # 设置认证
        if auth_token:
            self.session.headers.update({
                'Authorization': f'Bearer {auth_token}'
            })
    
    def _build_url(self, endpoint: str) -> str:
        """构建完整URL"""
        return urljoin(self.base_url + '/', endpoint.lstrip('/'))
    
    def _make_request(self, method: str, endpoint: str, **kwargs) -> APIResponse:
        """
        发送HTTP请求
        
        Args:
            method: HTTP方法
            endpoint: API端点
            **kwargs: 其他请求参数
            
        Returns:
            APIResponse: 响应结果
        """
        url = self._build_url(endpoint)
        
        # 设置默认超时
        if 'timeout' not in kwargs:
            kwargs['timeout'] = self.timeout
        
        start_time = time.time()
        
        try:
            self.logger.debug(f"发送请求: {method} {url}")
            
            response = self.session.request(method, url, **kwargs)
            
            elapsed_time = time.time() - start_time
            self.logger.debug(f"请求完成: {response.status_code} 耗时: {elapsed_time:.2f}s")
            
            # 解析响应数据
            try:
                if response.content:
                    data = response.json()
                else:
                    data = None
            except ValueError:
                data = response.text
            
            return APIResponse(
                status_code=response.status_code,
                data=data,
                headers=dict(response.headers),
                success=response.status_code < 400
            )
            
        except requests.exceptions.Timeout:
            error_message = f"请求超时: {url}"
            self.logger.error(error_message)
            return APIResponse(
                status_code=0,
                data=None,
                headers={},
                success=False,
                error_message=error_message
            )
        except requests.exceptions.ConnectionError:
            error_message = f"连接失败: {url}"
            self.logger.error(error_message)
            return APIResponse(
                status_code=0,
                data=None,
                headers={},
                success=False,
                error_message=error_message
            )
        except Exception as e:
            error_message = f"请求异常: {str(e)}"
            self.logger.error(error_message)
            return APIResponse(
                status_code=0,
                data=None,
                headers={},
                success=False,
                error_message=error_message
            )
    
    def get(self, endpoint: str, params: Optional[Dict] = None) -> APIResponse:
        """发送GET请求"""
        return self._make_request('GET', endpoint, params=params)
    
    def post(self, endpoint: str, data: Optional[Dict] = None, json_data: Optional[Dict] = None) -> APIResponse:
        """发送POST请求"""
        kwargs = {}
        if json_data:
            kwargs['json'] = json_data
        elif data:
            kwargs['data'] = data
        
        return self._make_request('POST', endpoint, **kwargs)
    
    def put(self, endpoint: str, data: Optional[Dict] = None, json_data: Optional[Dict] = None) -> APIResponse:
        """发送PUT请求"""
        kwargs = {}
        if json_data:
            kwargs['json'] = json_data
        elif data:
            kwargs['data'] = data
        
        return self._make_request('PUT', endpoint, **kwargs)
    
    def patch(self, endpoint: str, data: Optional[Dict] = None, json_data: Optional[Dict] = None) -> APIResponse:
        """发送PATCH请求"""
        kwargs = {}
        if json_data:
            kwargs['json'] = json_data
        elif data:
            kwargs['data'] = data
        
        return self._make_request('PATCH', endpoint, **kwargs)
    
    def delete(self, endpoint: str) -> APIResponse:
        """发送DELETE请求"""
        return self._make_request('DELETE', endpoint)
    
    def upload_file(self, endpoint: str, file_path: str, field_name: str = 'file') -> APIResponse:
        """
        上传文件
        
        Args:
            endpoint: API端点
            file_path: 文件路径
            field_name: 表单字段名
            
        Returns:
            APIResponse: 响应结果
        """
        url = self._build_url(endpoint)
        
        try:
            with open(file_path, 'rb') as f:
                files = {field_name: f}
                response = self.session.post(url, files=files, timeout=self.timeout)
            
            return APIResponse(
                status_code=response.status_code,
                data=response.json() if response.content else None,
                headers=dict(response.headers),
                success=response.status_code < 400
            )
            
        except Exception as e:
            error_message = f"文件上传失败: {str(e)}"
            self.logger.error(error_message)
            return APIResponse(
                status_code=0,
                data=None,
                headers={},
                success=False,
                error_message=error_message
            )


class SPiderAPIClient(HTTPClient):
    """
    Spider平台API客户端
    
    封装了Spider平台的常用API调用
    
    <AUTHOR>
    @date 2025-08-25 16:05:00
    """
    
    def get_app_info(self, app_id: int) -> APIResponse:
        """获取应用信息"""
        return self.get(f'/apps/{app_id}')
    
    def get_app_list(self, params: Optional[Dict] = None) -> APIResponse:
        """获取应用列表"""
        return self.get('/apps', params=params)
    
    def create_app(self, app_data: Dict[str, Any]) -> APIResponse:
        """创建应用"""
        return self.post('/apps', json_data=app_data)
    
    def update_app(self, app_id: int, app_data: Dict[str, Any]) -> APIResponse:
        """更新应用"""
        return self.patch(f'/apps/{app_id}', json_data=app_data)
    
    def delete_app(self, app_id: int) -> APIResponse:
        """删除应用"""
        return self.delete(f'/apps/{app_id}')
    
    def get_deployment_status(self, app_id: int, env: str) -> APIResponse:
        """获取部署状态"""
        return self.get(f'/apps/{app_id}/deployments', params={'env': env})
    
    def trigger_deployment(self, app_id: int, env: str, version: str) -> APIResponse:
        """触发部署"""
        data = {
            'app_id': app_id,
            'environment': env,
            'version': version
        }
        return self.post('/deployments', json_data=data)
```

### 5. 脚本执行示例
```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
应用部署脚本

自动化应用部署流程，支持多环境部署和回滚

作者: hongdong.xie
创建时间: 2025-08-25 16:05:00
"""

import sys
import argparse
from pathlib import Path
from typing import List, Dict, Any
from dataclasses import dataclass

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from common.config import load_config
from common.database import DatabaseManager
from common.http_client import SPiderAPIClient
from common.logger import ScriptLogger
from common.exceptions import ScriptException


@dataclass
class DeploymentConfig:
    """部署配置"""
    app_name: str
    environment: str
    version: str
    rollback_version: Optional[str] = None
    health_check_url: Optional[str] = None
    timeout: int = 600


class AppDeploymentScript:
    """
    应用部署脚本
    
    <AUTHOR>
    @date 2025-08-25 16:05:00
    """
    
    def __init__(self, config: DeploymentConfig, dry_run: bool = False):
        """
        初始化部署脚本
        
        Args:
            config: 部署配置
            dry_run: 是否为干跑模式
        """
        self.config = config
        self.dry_run = dry_run
        self.logger = ScriptLogger('app_deployment')
        
        # 加载系统配置
        self.sys_config = load_config()
        
        # 初始化客户端
        api_config = self.sys_config.get_api_config()
        self.api_client = SPiderAPIClient(
            base_url=api_config.base_url,
            auth_token=api_config.auth_token
        )
        
        # 初始化数据库
        db_config = self.sys_config.get_database_config()
        self.db = DatabaseManager(db_config)
    
    def run(self) -> bool:
        """
        运行部署脚本
        
        Returns:
            bool: 部署是否成功
        """
        try:
            self.logger.info(f"开始部署应用", 
                           app_name=self.config.app_name,
                           environment=self.config.environment,
                           version=self.config.version)
            
            if self.dry_run:
                self.logger.info("运行在DRY-RUN模式")
            
            # 执行部署步骤
            steps = [
                ('验证应用信息', self._validate_app),
                ('检查部署权限', self._check_permissions),
                ('准备部署环境', self._prepare_environment),
                ('执行部署', self._deploy_application),
                ('健康检查', self._health_check),
                ('更新部署记录', self._update_deployment_record)
            ]
            
            for step_name, step_func in steps:
                self.logger.step(step_name)
                
                if not step_func():
                    self.logger.error(f"步骤失败: {step_name}")
                    return False
            
            self.logger.result(True, "应用部署成功")
            return True
            
        except Exception as e:
            self.logger.error(f"部署过程中发生异常: {str(e)}")
            self._rollback_on_failure()
            return False
        finally:
            self.logger.finalize(True)
    
    def _validate_app(self) -> bool:
        """验证应用信息"""
        response = self.api_client.get_app_list(params={
            'app_name': self.config.app_name
        })
        
        if not response.success:
            self.logger.error("获取应用信息失败", error=response.error_message)
            return False
        
        apps = response.data.get('data', {}).get('items', [])
        if not apps:
            self.logger.error("应用不存在", app_name=self.config.app_name)
            return False
        
        self.app_info = apps[0]
        self.logger.info("应用验证成功", 
                        app_id=self.app_info['id'],
                        app_code=self.app_info['app_code'])
        return True
    
    def _check_permissions(self) -> bool:
        """检查部署权限"""
        # 检查当前用户是否有部署权限
        # 这里可以调用权限检查API或数据库查询
        self.logger.info("权限检查通过")
        return True
    
    def _prepare_environment(self) -> bool:
        """准备部署环境"""
        if self.dry_run:
            self.logger.info("DRY-RUN: 跳过环境准备")
            return True
        
        # 检查目标环境状态
        response = self.api_client.get_deployment_status(
            self.app_info['id'], 
            self.config.environment
        )
        
        if response.success:
            deployment_info = response.data.get('data', {})
            current_version = deployment_info.get('current_version')
            
            if current_version:
                self.logger.info("当前部署版本", version=current_version)
                self.config.rollback_version = current_version
        
        self.logger.info("环境准备完成")
        return True
    
    def _deploy_application(self) -> bool:
        """执行应用部署"""
        if self.dry_run:
            self.logger.info("DRY-RUN: 跳过实际部署")
            return True
        
        # 触发部署
        response = self.api_client.trigger_deployment(
            self.app_info['id'],
            self.config.environment,
            self.config.version
        )
        
        if not response.success:
            self.logger.error("部署触发失败", error=response.error_message)
            return False
        
        deployment_id = response.data.get('data', {}).get('deployment_id')
        self.logger.info("部署已触发", deployment_id=deployment_id)
        
        # 等待部署完成
        return self._wait_for_deployment_complete(deployment_id)
    
    def _wait_for_deployment_complete(self, deployment_id: str) -> bool:
        """等待部署完成"""
        import time
        
        timeout = self.config.timeout
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            # 检查部署状态
            # 这里应该调用实际的部署状态查询API
            time.sleep(10)  # 每10秒检查一次
            
            # 模拟部署完成
            if time.time() - start_time > 30:  # 假设30秒后部署完成
                self.logger.info("部署完成")
                return True
        
        self.logger.error("部署超时")
        return False
    
    def _health_check(self) -> bool:
        """健康检查"""
        if not self.config.health_check_url:
            self.logger.info("跳过健康检查")
            return True
        
        if self.dry_run:
            self.logger.info("DRY-RUN: 跳过健康检查")
            return True
        
        # 执行健康检查
        import requests
        try:
            response = requests.get(self.config.health_check_url, timeout=30)
            if response.status_code == 200:
                self.logger.info("健康检查通过")
                return True
            else:
                self.logger.error("健康检查失败", status_code=response.status_code)
                return False
        except Exception as e:
            self.logger.error("健康检查异常", error=str(e))
            return False
    
    def _update_deployment_record(self) -> bool:
        """更新部署记录"""
        if self.dry_run:
            self.logger.info("DRY-RUN: 跳过记录更新")
            return True
        
        # 在数据库中记录部署信息
        with self.db:
            sql = """
            INSERT INTO deployment_history 
            (app_id, environment, version, status, created_at)
            VALUES (%s, %s, %s, %s, NOW())
            """
            
            self.db.execute_update(sql, (
                self.app_info['id'],
                self.config.environment,
                self.config.version,
                'success'
            ))
        
        self.logger.info("部署记录已更新")
        return True
    
    def _rollback_on_failure(self) -> None:
        """失败时执行回滚"""
        if not self.config.rollback_version or self.dry_run:
            self.logger.info("跳过回滚")
            return
        
        self.logger.info("开始回滚", version=self.config.rollback_version)
        
        # 执行回滚逻辑
        response = self.api_client.trigger_deployment(
            self.app_info['id'],
            self.config.environment,
            self.config.rollback_version
        )
        
        if response.success:
            self.logger.info("回滚成功")
        else:
            self.logger.error("回滚失败", error=response.error_message)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='应用部署脚本')
    
    parser.add_argument('--app-name', required=True, help='应用名称')
    parser.add_argument('--env', required=True, help='目标环境')
    parser.add_argument('--version', required=True, help='部署版本')
    parser.add_argument('--health-check-url', help='健康检查URL')
    parser.add_argument('--timeout', type=int, default=600, help='超时时间（秒）')
    parser.add_argument('--dry-run', action='store_true', help='干跑模式')
    
    args = parser.parse_args()
    
    # 创建配置
    config = DeploymentConfig(
        app_name=args.app_name,
        environment=args.env,
        version=args.version,
        health_check_url=args.health_check_url,
        timeout=args.timeout
    )
    
    # 执行部署
    script = AppDeploymentScript(config, dry_run=args.dry_run)
    success = script.run()
    
    sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()
```

## 脚本测试和质量保证

### 1. 单元测试
```python
# tests/test_deployment_script.py
import unittest
from unittest.mock import Mock, patch, MagicMock
import sys
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from scripts.app_deployment import AppDeploymentScript, DeploymentConfig


class TestAppDeploymentScript(unittest.TestCase):
    """
    应用部署脚本测试
    
    <AUTHOR>
    @date 2025-08-25 16:05:00
    """
    
    def setUp(self):
        """测试前置条件"""
        self.config = DeploymentConfig(
            app_name='test-app',
            environment='test',
            version='1.0.0'
        )
        
        # Mock外部依赖
        with patch('scripts.app_deployment.load_config'), \
             patch('scripts.app_deployment.SPiderAPIClient'), \
             patch('scripts.app_deployment.DatabaseManager'):
            self.script = AppDeploymentScript(self.config, dry_run=True)
    
    def test_validate_app_success(self):
        """测试应用验证成功"""
        # Mock API响应
        mock_response = Mock()
        mock_response.success = True
        mock_response.data = {
            'data': {
                'items': [{'id': 1, 'app_code': 'test-app'}]
            }
        }
        
        self.script.api_client.get_app_list.return_value = mock_response
        
        # 执行测试
        result = self.script._validate_app()
        
        # 验证结果
        self.assertTrue(result)
        self.assertEqual(self.script.app_info['id'], 1)
    
    def test_validate_app_not_found(self):
        """测试应用不存在"""
        # Mock API响应
        mock_response = Mock()
        mock_response.success = True
        mock_response.data = {'data': {'items': []}}
        
        self.script.api_client.get_app_list.return_value = mock_response
        
        # 执行测试
        result = self.script._validate_app()
        
        # 验证结果
        self.assertFalse(result)
    
    @patch('scripts.app_deployment.time.sleep')
    def test_deploy_application_success(self, mock_sleep):
        """测试部署成功"""
        # Mock部署触发响应
        mock_response = Mock()
        mock_response.success = True
        mock_response.data = {'data': {'deployment_id': 'dep-123'}}
        
        self.script.api_client.trigger_deployment.return_value = mock_response
        
        # Mock等待部署完成
        with patch.object(self.script, '_wait_for_deployment_complete', return_value=True):
            result = self.script._deploy_application()
        
        # 验证结果
        self.assertTrue(result)


if __name__ == '__main__':
    unittest.main()
```

### 2. 脚本集成测试
```bash
#!/bin/bash
# 脚本集成测试

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

echo "开始集成测试..."

# 设置测试环境变量
export DB_HOST="localhost"
export DB_PORT="3306"
export DB_NAME="spider_test"
export DB_USER="test_user"
export DB_PASSWORD="test_pass"
export API_BASE_URL="http://localhost:9011/api"

# 运行脚本测试
python3 "$PROJECT_DIR/scripts/app_deployment.py" \
    --app-name "test-app" \
    --env "test" \
    --version "1.0.0" \
    --dry-run

echo "集成测试完成"
```

---

**注意：Python脚本开发必须严格遵循上述规范，确保脚本的可靠性、可维护性和安全性。**