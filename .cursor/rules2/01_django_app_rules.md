# Django应用层开发规范

**文档创建时间：** 2025-08-25 15:35:00  
**作者：** hongdong.xie

## 适用场景
- Django应用模块开发（如app_mgt、env_mgt、iter_mgt等）
- 模型定义和数据库操作
- 视图函数和类视图开发
- 序列化器开发

## 项目架构分层

### 1. 模型层（Models）
```python
from django.db import models
from django.contrib.auth.models import AbstractUser
from typing import Optional

class BaseModel(models.Model):
    """
    基础模型类，所有模型都应该继承此类
    <AUTHOR>
    @date 2025-08-25 15:35:00
    """
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")
    is_deleted = models.BooleanField(default=False, verbose_name="是否删除")
    
    class Meta:
        abstract = True
        
    def soft_delete(self):
        """软删除方法"""
        self.is_deleted = True
        self.save()

class AppInfo(BaseModel):
    """
    应用信息模型
    <AUTHOR>
    @date 2025-08-25 15:35:00
    """
    app_name = models.CharField(max_length=100, verbose_name="应用名称")
    app_code = models.CharField(max_length=50, unique=True, verbose_name="应用编码")
    description = models.TextField(blank=True, verbose_name="应用描述")
    
    class Meta:
        db_table = 'app_mgt_app_info'
        verbose_name = '应用信息'
        verbose_name_plural = '应用信息'
        
    def __str__(self):
        return f"{self.app_name}({self.app_code})"
```

#### 模型规范要点
- 所有模型必须继承`BaseModel`或直接使用适当的抽象基类
- 字段必须有`verbose_name`
- 必须定义`__str__`方法
- 使用`db_table`明确指定表名
- 外键关系必须设置`on_delete`参数
- 敏感字段使用软删除而非物理删除

### 2. 序列化器层（Serializers）
```python
from rest_framework import serializers
from rest_framework.validators import ValidationError
from typing import Dict, Any
from .models import AppInfo

class AppInfoSerializer(serializers.ModelSerializer):
    """
    应用信息序列化器
    <AUTHOR>
    @date 2025-08-25 15:35:00
    """
    
    class Meta:
        model = AppInfo
        fields = ['id', 'app_name', 'app_code', 'description', 'created_at']
        read_only_fields = ['id', 'created_at']
        
    def validate_app_code(self, value: str) -> str:
        """验证应用编码格式"""
        if not value.isalnum():
            raise ValidationError("应用编码只能包含字母和数字")
        return value.lower()
        
    def create(self, validated_data: Dict[str, Any]) -> AppInfo:
        """创建应用信息"""
        return AppInfo.objects.create(**validated_data)
```

#### 序列化器规范要点
- 继承`serializers.ModelSerializer`
- 明确指定`fields`和`read_only_fields`
- 自定义验证方法使用`validate_<field_name>`格式
- 复杂业务逻辑写在`create`或`update`方法中
- 使用类型注解

### 3. 视图层（Views）
```python
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.db import transaction
from typing import Dict, Any, List
from .models import AppInfo
from .serializers import AppInfoSerializer

class AppInfoViewSet(viewsets.ModelViewSet):
    """
    应用信息视图集
    <AUTHOR>
    @date 2025-08-25 15:35:00
    """
    queryset = AppInfo.objects.filter(is_deleted=False)
    serializer_class = AppInfoSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        """获取查询集，支持过滤"""
        queryset = super().get_queryset()
        app_name = self.request.query_params.get('app_name')
        if app_name:
            queryset = queryset.filter(app_name__icontains=app_name)
        return queryset
        
    @transaction.atomic
    def create(self, request, *args, **kwargs):
        """创建应用信息，使用事务保证一致性"""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        try:
            app_info = serializer.save()
            return Response({
                'code': 0,
                'message': '创建成功',
                'data': AppInfoSerializer(app_info).data
            }, status=status.HTTP_201_CREATED)
        except Exception as e:
            return Response({
                'code': -1,
                'message': f'创建失败：{str(e)}',
                'data': None
            }, status=status.HTTP_400_BAD_REQUEST)
            
    @action(detail=False, methods=['get'])
    def statistics(self, request):
        """获取应用统计信息"""
        total_count = self.get_queryset().count()
        return Response({
            'code': 0,
            'message': '获取成功',
            'data': {'total_count': total_count}
        })
```

#### 视图规范要点
- 使用ViewSet而非函数视图
- 重要操作必须使用`@transaction.atomic`装饰器
- 统一的响应格式：`{code, message, data}`
- 自定义权限检查
- 使用`@action`装饰器添加自定义端点
- 异常处理要详细记录日志

### 4. URL路由配置
```python
from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

# 创建路由器
router = DefaultRouter()
router.register(r'apps', views.AppInfoViewSet)

# URL配置
app_name = 'app_mgt'
urlpatterns = [
    path('api/', include(router.urls)),
    # 自定义URL
    path('api/apps/export/', views.AppExportView.as_view(), name='app-export'),
]
```

## 数据库操作规范

### 1. QuerySet优化
```python
# 优化查询，避免N+1问题
apps = AppInfo.objects.select_related('team').prefetch_related('modules')

# 使用索引字段查询
apps = AppInfo.objects.filter(app_code='test-app').first()

# 批量操作
AppInfo.objects.bulk_create([
    AppInfo(app_name='App1', app_code='app1'),
    AppInfo(app_name='App2', app_code='app2'),
])
```

### 2. 事务管理
```python
from django.db import transaction

# 方法级事务
@transaction.atomic
def create_app_with_modules(app_data, modules_data):
    app = AppInfo.objects.create(**app_data)
    for module_data in modules_data:
        module_data['app'] = app
        AppModule.objects.create(**module_data)
    return app

# 代码块级事务
def complex_operation():
    with transaction.atomic():
        # 复杂的数据库操作
        pass
```

## 异常处理和日志

### 1. 异常处理
```python
import logging
from rest_framework.views import exception_handler
from rest_framework.response import Response

logger = logging.getLogger(__name__)

def custom_exception_handler(exc, context):
    """自定义异常处理器"""
    response = exception_handler(exc, context)
    
    if response is not None:
        logger.error(f"API异常: {exc}", exc_info=True)
        custom_response_data = {
            'code': -1,
            'message': '操作失败',
            'data': None,
            'errors': response.data
        }
        response.data = custom_response_data
    
    return response
```

### 2. 业务日志
```python
import logging
from django.contrib.auth.models import User

logger = logging.getLogger(__name__)

class AppManagementService:
    """应用管理服务类"""
    
    def create_app(self, app_data: Dict[str, Any], user: User) -> AppInfo:
        """
        创建应用
        <AUTHOR>
        @date 2025-08-25 15:35:00
        """
        try:
            logger.info(f"用户 {user.username} 开始创建应用: {app_data.get('app_name')}")
            
            app = AppInfo.objects.create(**app_data)
            
            logger.info(f"应用创建成功: {app.app_name} (ID: {app.id})")
            return app
            
        except Exception as e:
            logger.error(f"创建应用失败: {str(e)}", exc_info=True)
            raise
```

## 测试规范

### 1. 模型测试
```python
from django.test import TestCase
from django.core.exceptions import ValidationError
from .models import AppInfo

class AppInfoModelTest(TestCase):
    """
    应用信息模型测试
    <AUTHOR>
    @date 2025-08-25 15:35:00
    """
    
    def setUp(self):
        """测试前置条件"""
        self.app_data = {
            'app_name': '测试应用',
            'app_code': 'test-app',
            'description': '这是一个测试应用'
        }
    
    def test_create_app(self):
        """测试创建应用"""
        app = AppInfo.objects.create(**self.app_data)
        self.assertEqual(app.app_name, '测试应用')
        self.assertEqual(app.app_code, 'test-app')
        self.assertFalse(app.is_deleted)
    
    def test_app_str_method(self):
        """测试字符串表示方法"""
        app = AppInfo.objects.create(**self.app_data)
        self.assertEqual(str(app), '测试应用(test-app)')
```

### 2. API测试
```python
from rest_framework.test import APITestCase
from rest_framework import status
from django.contrib.auth.models import User
from .models import AppInfo

class AppInfoAPITest(APITestCase):
    """
    应用信息API测试
    <AUTHOR>
    @date 2025-08-25 15:35:00
    """
    
    def setUp(self):
        """测试前置条件"""
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
        self.client.force_authenticate(user=self.user)
    
    def test_create_app(self):
        """测试创建应用API"""
        data = {
            'app_name': '测试应用',
            'app_code': 'test-app',
            'description': '这是一个测试应用'
        }
        response = self.client.post('/api/apps/', data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['code'], 0)
```

## 性能优化规范

### 1. 数据库查询优化
- 使用`select_related`处理一对一和多对一关系
- 使用`prefetch_related`处理多对多和反向外键关系
- 避免在循环中执行数据库查询
- 大数据量查询必须使用分页

### 2. 缓存策略
```python
from django.core.cache import cache
from django.views.decorators.cache import cache_page
from rest_framework.decorators import action

class AppInfoViewSet(viewsets.ModelViewSet):
    
    @cache_page(60 * 15)  # 缓存15分钟
    @action(detail=False, methods=['get'])
    def statistics(self, request):
        """获取应用统计信息（带缓存）"""
        cache_key = 'app_statistics'
        stats = cache.get(cache_key)
        
        if stats is None:
            stats = {
                'total_count': AppInfo.objects.count(),
                'active_count': AppInfo.objects.filter(is_deleted=False).count()
            }
            cache.set(cache_key, stats, 60 * 15)  # 缓存15分钟
        
        return Response({'code': 0, 'data': stats})
```

## 代码质量检查

### 1. 必须通过的检查
- Python代码必须通过pylint检查
- 类型注解覆盖率不低于80%
- 单元测试覆盖率不低于70%
- 没有明显的性能问题

### 2. 代码审查清单
- [ ] 模型定义规范
- [ ] 序列化器验证完整
- [ ] 视图权限控制正确
- [ ] 异常处理完善
- [ ] 日志记录详细
- [ ] 测试用例充分

---

**注意：Django应用开发必须严格遵循上述规范，确保代码质量和系统稳定性。**