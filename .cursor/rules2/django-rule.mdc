---

description:
globs:
  - '*.py'
alwaysApply: true

---

# Python Django 最佳实践指南

## 核心原则
- **清晰且专业的响应**：在响应中提供精确的 Django 示例。
- **充分利用 Django 的内置功能**：通过使用其内置工具和功能，发挥 Django 的全部能力。
- **可读性和可维护性**：遵循 Django 的编码风格指南（符合 PEP 8 标准），并优先考虑代码的可读性。
- **描述性命名**：使用符合命名规范的描述性变量和函数名（例如，函数和变量使用下划线分隔的小写形式）。
- **模块化项目结构**：使用 Django 应用以模块化方式构建项目，促进代码复用和关注点分离。

## Django/Python 最佳实践
- **基于类的视图（CBVs）与基于函数的视图（FBVs）**：复杂视图使用 CBVs，简单逻辑使用 FBVs。
- **Django ORM**：利用 Django 的 ORM 进行数据库交互；除非出于性能考虑，否则避免使用原始 SQL 查询。
- **用户管理**：使用 Django 的内置用户模型和认证框架。
- **表单和模型表单**：利用 Django 的表单和模型表单类进行表单处理和验证。
- **MVT 模式**：严格遵循模型-视图-模板模式，实现清晰的关注点分离。
- **中间件**：审慎使用中间件处理跨领域问题，如认证、日志记录和缓存。

## 错误处理与验证
- **视图级错误处理**：使用 Django 的内置机制在视图层实现错误处理。
- **验证框架**：使用 Django 的验证框架处理表单和模型数据。
- **异常处理**：在业务逻辑和视图中优先使用 try-except 块处理异常。
- **自定义错误页面**：自定义错误页面（如 404、500）以提升用户体验。
- **Django 信号**：使用 Django 信号将错误处理和日志记录与核心业务逻辑解耦。

## 依赖项
- **核心库**：Django、Django REST Framework（用于 API 开发）。
- **后台任务**：Celery。
- **缓存和任务队列**：Redis。
- **数据库**：MySQL（推荐用于生产环境）。

## Django 特定指南
- **模板和序列化器**：使用 Django 模板渲染 HTML，使用 DRF 序列化器处理 JSON 响应。
- **业务逻辑**：将业务逻辑放在模型和表单中；保持视图简洁，专注于请求处理。
- **URL 分发器**：使用 Django 的 URL 分发器（urls.py）定义清晰的 RESTful URL 模式。
- **安全最佳实践**：应用 Django 的安全最佳实践（如 CSRF 保护、SQL 注入防护、XSS 预防）。
- **测试**：使用 Django 的内置工具（unittest 和 pytest-django）进行测试，确保代码质量和可靠性。
- **缓存框架**：利用 Django 的缓存框架优化频繁访问数据的性能。
- **中间件**：使用 Django 的中间件处理常见任务，如认证、日志记录和安全相关操作。

## 性能优化
- **查询优化**：使用 Django ORM 的 select_related 和 prefetch_related 优化关联对象的查询性能。
- **缓存**：结合后端支持（如 Redis 或 Memcached）使用 Django 的缓存框架，减少数据库负载。
- **数据库索引**：实施数据库索引和查询优化技术以提升性能。
- **异步视图**：对于 I/O 密集型或长时间运行的操作，使用异步视图和后台任务（通过 Celery）。
- **静态文件处理**：通过 Django 的静态文件管理系统（如 WhiteNoise 或 CDN 集成）优化静态文件处理。

## 关键约定
1. **约定优于配置**：遵循 Django 的原则，减少样板代码。
2. **安全性和性能**：在开发的每个阶段都优先考虑安全性和性能优化。
3. **项目结构**：保持清晰合理的项目结构，提升可读性和可维护性。

## 参考资料
有关视图、模型、表单和安全性考虑的最佳实践，请参考 Django 官方文档。