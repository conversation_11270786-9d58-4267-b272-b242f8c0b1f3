# Jenkins流水线开发规范

**文档创建时间：** 2025-08-25 16:10:00  
**作者：** hongdong.xie

## 适用场景
- Jenkins Pipeline脚本开发
- Groovy脚本编写
- CI/CD流水线配置
- Jenkins Job自动化管理
- 构建和部署流程编排

## Pipeline基础规范

### 1. 声明式Pipeline结构
```groovy
/**
 * 应用构建流水线
 * 
 * 功能：自动化构建、测试、打包和部署Java应用
 * 
 * <AUTHOR>
 * @date 2025-08-25 16:10:00
 * @version 1.0.0
 */

@Library('pa-pipeline-library@master') _

pipeline {
    // 代理配置
    agent {
        label 'java-build-agent'
    }
    
    // 全局选项
    options {
        // 保持构建历史
        buildDiscarder(logRotator(
            numToKeepStr: '30',
            daysToKeepStr: '90'
        ))
        
        // 超时设置
        timeout(time: 60, unit: 'MINUTES')
        
        // 时间戳输出
        timestamps()
        
        // 跳过默认检出
        skipDefaultCheckout()
        
        // 禁止并发构建
        disableConcurrentBuilds()
        
        // 构建后清理工作空间
        skipStagesAfterUnstable()
    }
    
    // 参数定义
    parameters {
        choice(
            name: 'BUILD_ENV',
            choices: ['dev', 'test', 'staging', 'prod'],
            description: '构建环境'
        )
        
        string(
            name: 'APP_VERSION',
            defaultValue: '',
            description: '应用版本号（留空自动生成）'
        )
        
        booleanParam(
            name: 'SKIP_TESTS',
            defaultValue: false,
            description: '跳过测试'
        )
        
        booleanParam(
            name: 'DEPLOY_AFTER_BUILD',
            defaultValue: true,
            description: '构建后自动部署'
        )
    }
    
    // 环境变量
    environment {
        // Maven配置
        MAVEN_OPTS = '-Xmx1024m -Xms512m'
        MAVEN_CONFIG = '/opt/maven/conf/settings.xml'
        
        // Java配置
        JAVA_HOME = '/usr/lib/jvm/java-8-openjdk-amd64'
        
        // 应用信息
        APP_NAME = "${env.JOB_BASE_NAME}"
        BUILD_NUMBER = "${env.BUILD_NUMBER}"
        GIT_COMMIT_SHORT = "${env.GIT_COMMIT?.take(8)}"
        
        // 构建环境
        BUILD_ENV = "${params.BUILD_ENV}"
        
        // 版本号
        APP_VERSION = "${params.APP_VERSION ?: generateVersion()}"
        
        // 部署配置
        NEXUS_URL = 'http://nexus.internal.com'
        DEPLOY_TARGET = "${BUILD_ENV == 'prod' ? 'production' : 'staging'}"
    }
    
    // 构建阶段
    stages {
        stage('环境初始化') {
            steps {
                script {
                    // 记录构建信息
                    currentBuild.displayName = "#${BUILD_NUMBER}-${APP_VERSION}-${BUILD_ENV}"
                    currentBuild.description = "构建环境: ${BUILD_ENV}"
                    
                    // 打印构建参数
                    printBuildInfo()
                    
                    // 检查构建权限
                    if (BUILD_ENV == 'prod' && !hasProductionPermission()) {
                        error("当前用户没有生产环境构建权限")
                    }
                }
            }
        }
        
        stage('代码检出') {
            steps {
                script {
                    // 检出代码
                    def checkoutResult = checkoutFromGit(
                        branch: env.BRANCH_NAME ?: 'master',
                        credentialsId: 'git-credentials'
                    )
                    
                    // 设置Git信息
                    env.GIT_COMMIT = checkoutResult.GIT_COMMIT
                    env.GIT_BRANCH = checkoutResult.GIT_BRANCH
                    
                    echo "检出完成: ${env.GIT_BRANCH}@${env.GIT_COMMIT?.take(8)}"
                }
            }
        }
        
        stage('代码质量检查') {
            parallel {
                stage('编译检查') {
                    steps {
                        script {
                            // Maven编译
                            sh """
                                mvn clean compile \
                                    -DskipTests=true \
                                    -Dmaven.test.skip=true \
                                    --batch-mode \
                                    --update-snapshots
                            """
                        }
                    }
                }
                
                stage('静态代码分析') {
                    when {
                        not { params.SKIP_TESTS }
                    }
                    steps {
                        script {
                            // SonarQube分析
                            withSonarQubeEnv('SonarQube') {
                                sh """
                                    mvn sonar:sonar \
                                        -Dsonar.projectKey=${APP_NAME} \
                                        -Dsonar.projectName=${APP_NAME} \
                                        -Dsonar.projectVersion=${APP_VERSION}
                                """
                            }
                            
                            // 等待质量门检查
                            timeout(time: 10, unit: 'MINUTES') {
                                def qg = waitForQualityGate()
                                if (qg.status != 'OK') {
                                    error "质量门检查失败: ${qg.status}"
                                }
                            }
                        }
                    }
                }
            }
        }
        
        stage('单元测试') {
            when {
                not { params.SKIP_TESTS }
            }
            steps {
                script {
                    try {
                        // 执行单元测试
                        sh """
                            mvn test \
                                --batch-mode \
                                -Dmaven.test.failure.ignore=false
                        """
                    } finally {
                        // 发布测试报告
                        publishTestResults testResultsPattern: '**/target/surefire-reports/TEST-*.xml'
                        publishHTML([
                            allowMissing: false,
                            alwaysLinkToLastBuild: true,
                            keepAll: true,
                            reportDir: 'target/site/jacoco',
                            reportFiles: 'index.html',
                            reportName: 'JaCoCo Coverage Report'
                        ])
                    }
                }
            }
        }
        
        stage('构建打包') {
            steps {
                script {
                    // Maven打包
                    sh """
                        mvn package \
                            -DskipTests=true \
                            -Dmaven.test.skip=true \
                            -Dapp.version=${APP_VERSION} \
                            -Dbuild.env=${BUILD_ENV} \
                            --batch-mode
                    """
                    
                    // 验证构建产物
                    def jarFile = findFiles(glob: '**/target/*.jar')[0]
                    if (!jarFile) {
                        error "构建产物未找到"
                    }
                    
                    env.JAR_FILE = jarFile.path
                    echo "构建产物: ${env.JAR_FILE}"
                }
            }
        }
        
        stage('制品上传') {
            steps {
                script {
                    // 上传到Nexus
                    uploadToNexus(
                        artifactPath: env.JAR_FILE,
                        groupId: 'com.howbuy.pa',
                        artifactId: env.APP_NAME,
                        version: env.APP_VERSION,
                        repository: "${BUILD_ENV == 'prod' ? 'releases' : 'snapshots'}"
                    )
                    
                    // 记录制品信息
                    writeFile file: 'artifact.properties', text: """
                        artifact.groupId=com.howbuy.pa
                        artifact.artifactId=${env.APP_NAME}
                        artifact.version=${env.APP_VERSION}
                        artifact.buildNumber=${env.BUILD_NUMBER}
                        artifact.gitCommit=${env.GIT_COMMIT}
                        artifact.buildTime=${new Date().format('yyyy-MM-dd HH:mm:ss')}
                    """
                    
                    archiveArtifacts artifacts: 'artifact.properties', fingerprint: true
                }
            }
        }
        
        stage('部署应用') {
            when {
                expression { params.DEPLOY_AFTER_BUILD }
            }
            steps {
                script {
                    // 触发部署
                    def deployResult = deployApplication(
                        appName: env.APP_NAME,
                        version: env.APP_VERSION,
                        environment: env.BUILD_ENV,
                        timeout: 600
                    )
                    
                    if (!deployResult.success) {
                        error "部署失败: ${deployResult.message}"
                    }
                    
                    echo "部署成功: ${deployResult.deploymentId}"
                    
                    // 健康检查
                    healthCheck(
                        appName: env.APP_NAME,
                        environment: env.BUILD_ENV,
                        timeout: 300
                    )
                }
            }
        }
        
        stage('集成测试') {
            when {
                allOf {
                    expression { params.DEPLOY_AFTER_BUILD }
                    not { params.SKIP_TESTS }
                    anyOf {
                        environment name: 'BUILD_ENV', value: 'test'
                        environment name: 'BUILD_ENV', value: 'staging'
                    }
                }
            }
            steps {
                script {
                    // 运行集成测试
                    runIntegrationTests(
                        appName: env.APP_NAME,
                        environment: env.BUILD_ENV,
                        testSuite: 'smoke'
                    )
                }
            }
        }
    }
    
    // 后置处理
    post {
        always {
            script {
                // 清理工作空间
                cleanWs(
                    cleanWhenNotBuilt: false,
                    cleanWhenAborted: true,
                    cleanWhenFailure: true,
                    cleanWhenSuccess: true,
                    cleanWhenUnstable: true,
                    deleteDirs: true
                )
                
                // 记录构建时长
                def duration = currentBuild.durationString.replace(' and counting', '')
                echo "构建完成，总耗时: ${duration}"
            }
        }
        
        success {
            script {
                // 发送成功通知
                sendBuildNotification(
                    status: 'SUCCESS',
                    message: "构建成功: ${env.APP_NAME} v${env.APP_VERSION}"
                )
                
                // 更新构建状态到Git
                updateGitStatus('success', '构建成功')
            }
        }
        
        failure {
            script {
                // 发送失败通知
                sendBuildNotification(
                    status: 'FAILURE',
                    message: "构建失败: ${env.APP_NAME} v${env.APP_VERSION}"
                )
                
                // 更新构建状态到Git
                updateGitStatus('failure', '构建失败')
                
                // 生产环境构建失败时进行回滚
                if (env.BUILD_ENV == 'prod' && params.DEPLOY_AFTER_BUILD) {
                    rollbackApplication(
                        appName: env.APP_NAME,
                        environment: env.BUILD_ENV
                    )
                }
            }
        }
        
        unstable {
            script {
                sendBuildNotification(
                    status: 'UNSTABLE',
                    message: "构建不稳定: ${env.APP_NAME} v${env.APP_VERSION}"
                )
            }
        }
    }
}

/**
 * 生成版本号
 */
def generateVersion() {
    def timestamp = new Date().format('yyyyMMdd.HHmmss')
    def gitCommit = env.GIT_COMMIT?.take(8) ?: 'unknown'
    return "${timestamp}-${gitCommit}"
}

/**
 * 打印构建信息
 */
def printBuildInfo() {
    echo """
    ================================
    构建信息
    ================================
    应用名称: ${env.APP_NAME}
    构建版本: ${env.APP_VERSION}
    构建环境: ${env.BUILD_ENV}
    构建编号: ${env.BUILD_NUMBER}
    Git分支: ${env.GIT_BRANCH}
    Git提交: ${env.GIT_COMMIT?.take(8)}
    构建时间: ${new Date().format('yyyy-MM-dd HH:mm:ss')}
    构建用户: ${env.BUILD_USER ?: 'system'}
    ================================
    """
}

/**
 * 检查生产环境权限
 */
def hasProductionPermission() {
    def buildUser = env.BUILD_USER ?: 'system'
    def allowedUsers = ['admin', 'release-manager', 'senior-dev']
    return allowedUsers.contains(buildUser)
}
```

### 2. 共享库开发规范
```groovy
// vars/checkoutFromGit.groovy
/**
 * Git代码检出共享函数
 * 
 * <AUTHOR>
 * @date 2025-08-25 16:10:00
 */

def call(Map config) {
    def branch = config.branch ?: 'master'
    def credentialsId = config.credentialsId ?: 'git-credentials'
    def shallow = config.shallow ?: true
    def depth = config.depth ?: 1
    
    echo "检出代码: 分支=${branch}, 浅克隆=${shallow}"
    
    def checkoutResult = checkout([
        $class: 'GitSCM',
        branches: [[name: "*/${branch}"]],
        doGenerateSubmoduleConfigurations: false,
        extensions: [
            [$class: 'CloneOption', 
             depth: depth, 
             noTags: false, 
             reference: '', 
             shallow: shallow, 
             timeout: 20],
            [$class: 'CheckoutOption', timeout: 20],
            [$class: 'CleanCheckout']
        ],
        submoduleCfg: [],
        userRemoteConfigs: [[
            credentialsId: credentialsId,
            url: env.GIT_URL
        ]]
    ])
    
    // 获取Git信息
    def gitCommit = sh(
        script: 'git rev-parse HEAD',
        returnStdout: true
    ).trim()
    
    def gitBranch = sh(
        script: 'git rev-parse --abbrev-ref HEAD',
        returnStdout: true
    ).trim()
    
    echo "检出完成: ${gitBranch}@${gitCommit.take(8)}"
    
    return [
        GIT_COMMIT: gitCommit,
        GIT_BRANCH: gitBranch,
        GIT_URL: env.GIT_URL
    ]
}
```

```groovy
// vars/deployApplication.groovy
/**
 * 应用部署共享函数
 * 
 * <AUTHOR>
 * @date 2025-08-25 16:10:00
 */

def call(Map config) {
    def appName = config.appName
    def version = config.version
    def environment = config.environment
    def timeout = config.timeout ?: 600
    
    if (!appName || !version || !environment) {
        error "部署参数不完整: appName=${appName}, version=${version}, environment=${environment}"
    }
    
    echo "开始部署应用: ${appName} v${version} to ${environment}"
    
    try {
        // 调用部署脚本
        def deployCommand = """
            python3 /opt/scripts/app_deployment.py \
                --app-name "${appName}" \
                --env "${environment}" \
                --version "${version}" \
                --timeout ${timeout}
        """
        
        def deployOutput = sh(
            script: deployCommand,
            returnStdout: true
        ).trim()
        
        echo "部署输出: ${deployOutput}"
        
        // 解析部署结果
        def deploymentId = extractDeploymentId(deployOutput)
        
        echo "部署成功: 部署ID=${deploymentId}"
        
        return [
            success: true,
            deploymentId: deploymentId,
            message: "部署成功"
        ]
        
    } catch (Exception e) {
        echo "部署失败: ${e.getMessage()}"
        
        return [
            success: false,
            deploymentId: null,
            message: e.getMessage()
        ]
    }
}

/**
 * 从部署输出中提取部署ID
 */
def extractDeploymentId(String output) {
    def matcher = output =~ /deployment_id:\s*(\w+)/
    return matcher ? matcher[0][1] : "unknown-${UUID.randomUUID().toString().take(8)}"
}
```

```groovy
// vars/sendBuildNotification.groovy
/**
 * 构建通知共享函数
 * 
 * <AUTHOR>
 * @date 2025-08-25 16:10:00
 */

def call(Map config) {
    def status = config.status
    def message = config.message
    def channels = config.channels ?: ['slack', 'email']
    
    echo "发送构建通知: ${status} - ${message}"
    
    // 确定通知颜色
    def color = getStatusColor(status)
    def emoji = getStatusEmoji(status)
    
    // 构建详细信息
    def buildInfo = """
        项目: ${env.JOB_NAME}
        分支: ${env.GIT_BRANCH}
        版本: ${env.APP_VERSION}
        构建: #${env.BUILD_NUMBER}
        环境: ${env.BUILD_ENV}
        耗时: ${currentBuild.durationString.replace(' and counting', '')}
        链接: ${env.BUILD_URL}
    """
    
    // 发送Slack通知
    if (channels.contains('slack')) {
        try {
            slackSend(
                channel: '#devops-alerts',
                color: color,
                message: "${emoji} ${message}\n```${buildInfo}```",
                teamDomain: 'howbuy',
                token: env.SLACK_TOKEN
            )
            echo "Slack通知发送成功"
        } catch (Exception e) {
            echo "Slack通知发送失败: ${e.getMessage()}"
        }
    }
    
    // 发送邮件通知
    if (channels.contains('email')) {
        try {
            def recipients = getEmailRecipients()
            if (recipients) {
                emailext(
                    subject: "${emoji} [${status}] ${env.JOB_NAME} - Build #${env.BUILD_NUMBER}",
                    body: """
                        <h3>构建通知</h3>
                        <p><strong>状态:</strong> ${status}</p>
                        <p><strong>信息:</strong> ${message}</p>
                        <pre>${buildInfo}</pre>
                        <p><a href="${env.BUILD_URL}">查看构建详情</a></p>
                    """,
                    to: recipients,
                    mimeType: 'text/html'
                )
                echo "邮件通知发送成功"
            }
        } catch (Exception e) {
            echo "邮件通知发送失败: ${e.getMessage()}"
        }
    }
    
    // 企业微信通知
    if (channels.contains('wecom')) {
        try {
            sendWecomNotification(status, message, buildInfo)
            echo "企业微信通知发送成功"
        } catch (Exception e) {
            echo "企业微信通知发送失败: ${e.getMessage()}"
        }
    }
}

/**
 * 获取状态颜色
 */
def getStatusColor(String status) {
    switch (status.toUpperCase()) {
        case 'SUCCESS':
            return 'good'
        case 'FAILURE':
            return 'danger'
        case 'UNSTABLE':
            return 'warning'
        default:
            return '#439FE0'
    }
}

/**
 * 获取状态表情
 */
def getStatusEmoji(String status) {
    switch (status.toUpperCase()) {
        case 'SUCCESS':
            return '✅'
        case 'FAILURE':
            return '❌'
        case 'UNSTABLE':
            return '⚠️'
        default:
            return 'ℹ️'
    }
}

/**
 * 获取邮件接收人
 */
def getEmailRecipients() {
    // 从Git提交者获取邮箱
    def committerEmail = sh(
        script: 'git log -1 --pretty=format:"%ce"',
        returnStdout: true
    ).trim()
    
    // 默认接收人列表
    def defaultRecipients = ['<EMAIL>']
    
    if (committerEmail && committerEmail.contains('@')) {
        return "${committerEmail},${defaultRecipients.join(',')}"
    }
    
    return defaultRecipients.join(',')
}

/**
 * 发送企业微信通知
 */
def sendWecomNotification(String status, String message, String buildInfo) {
    def webhook = env.WECOM_WEBHOOK
    if (!webhook) {
        echo "企业微信Webhook未配置"
        return
    }
    
    def payload = [
        msgtype: "markdown",
        markdown: [
            content: """
## ${getStatusEmoji(status)} 构建通知

**状态:** ${status}
**信息:** ${message}

\`\`\`
${buildInfo}
\`\`\`
            """
        ]
    ]
    
    def response = httpRequest(
        httpMode: 'POST',
        url: webhook,
        contentType: 'APPLICATION_JSON',
        requestBody: groovy.json.JsonOutput.toJson(payload)
    )
    
    if (response.status != 200) {
        throw new Exception("企业微信通知发送失败: HTTP ${response.status}")
    }
}
```

### 3. 脚本式Pipeline规范
```groovy
/**
 * 应用部署脚本式Pipeline
 * 
 * <AUTHOR>
 * @date 2025-08-25 16:10:00
 */

node('deployment-agent') {
    try {
        // 设置构建信息
        currentBuild.displayName = "#${env.BUILD_NUMBER}"
        currentBuild.description = "部署应用到${params.ENVIRONMENT}"
        
        // 阶段1: 参数验证
        stage('参数验证') {
            echo "开始参数验证..."
            
            if (!params.APP_NAME) {
                error "应用名称不能为空"
            }
            
            if (!params.VERSION) {
                error "版本号不能为空"
            }
            
            if (!params.ENVIRONMENT) {
                error "环境不能为空"
            }
            
            // 验证环境权限
            if (params.ENVIRONMENT == 'prod') {
                def buildUser = env.BUILD_USER ?: 'unknown'
                if (!isProductionUser(buildUser)) {
                    error "用户 ${buildUser} 没有生产环境部署权限"
                }
            }
            
            echo "参数验证通过"
        }
        
        // 阶段2: 环境准备
        stage('环境准备') {
            echo "准备部署环境: ${params.ENVIRONMENT}"
            
            // 设置环境变量
            env.APP_NAME = params.APP_NAME
            env.VERSION = params.VERSION
            env.ENVIRONMENT = params.ENVIRONMENT
            env.DEPLOY_USER = env.BUILD_USER ?: 'jenkins'
            
            // 检查应用状态
            def appStatus = checkApplicationStatus(
                env.APP_NAME, 
                env.ENVIRONMENT
            )
            
            if (appStatus.error) {
                error "应用状态检查失败: ${appStatus.message}"
            }
            
            env.CURRENT_VERSION = appStatus.currentVersion
            echo "当前版本: ${env.CURRENT_VERSION}"
            echo "目标版本: ${env.VERSION}"
        }
        
        // 阶段3: 下载制品
        stage('下载制品') {
            echo "下载应用制品..."
            
            def artifactUrl = "${env.NEXUS_URL}/repository/releases/com/howbuy/pa/${env.APP_NAME}/${env.VERSION}/${env.APP_NAME}-${env.VERSION}.jar"
            
            sh """
                mkdir -p artifacts
                cd artifacts
                
                echo "下载制品: ${artifactUrl}"
                curl -f -L -o ${env.APP_NAME}-${env.VERSION}.jar "${artifactUrl}"
                
                if [ ! -f "${env.APP_NAME}-${env.VERSION}.jar" ]; then
                    echo "制品下载失败"
                    exit 1
                fi
                
                # 验证制品完整性
                if [ ! -s "${env.APP_NAME}-${env.VERSION}.jar" ]; then
                    echo "制品文件为空"
                    exit 1
                fi
                
                echo "制品下载成功: \$(ls -lh ${env.APP_NAME}-${env.VERSION}.jar)"
            """
            
            // 归档制品
            archiveArtifacts artifacts: "artifacts/${env.APP_NAME}-${env.VERSION}.jar", fingerprint: true
        }
        
        // 阶段4: 部署前检查
        stage('部署前检查') {
            echo "执行部署前检查..."
            
            parallel(
                "健康检查": {
                    if (env.CURRENT_VERSION != 'none') {
                        def healthStatus = performHealthCheck(
                            env.APP_NAME,
                            env.ENVIRONMENT
                        )
                        
                        if (!healthStatus.healthy) {
                            echo "警告: 当前应用健康检查失败，但继续部署"
                        } else {
                            echo "当前应用健康状态正常"
                        }
                    }
                },
                
                "资源检查": {
                    def resourceStatus = checkResourceAvailability(
                        env.ENVIRONMENT
                    )
                    
                    if (resourceStatus.warning) {
                        echo "资源警告: ${resourceStatus.message}"
                    }
                    
                    if (resourceStatus.error) {
                        error "资源检查失败: ${resourceStatus.message}"
                    }
                },
                
                "依赖服务检查": {
                    def dependencies = getDependentServices(env.APP_NAME)
                    
                    dependencies.each { service ->
                        def serviceStatus = checkServiceStatus(service, env.ENVIRONMENT)
                        if (!serviceStatus.available) {
                            error "依赖服务不可用: ${service}"
                        }
                    }
                    
                    echo "所有依赖服务检查通过"
                }
            )
        }
        
        // 阶段5: 执行部署
        stage('执行部署') {
            echo "开始部署应用..."
            
            timeout(time: 10, unit: 'MINUTES') {
                // 调用部署脚本
                def deployResult = sh(
                    script: """
                        python3 /opt/scripts/deploy_application.py \\
                            --app-name "${env.APP_NAME}" \\
                            --version "${env.VERSION}" \\
                            --environment "${env.ENVIRONMENT}" \\
                            --artifact-path "./artifacts/${env.APP_NAME}-${env.VERSION}.jar" \\
                            --deploy-user "${env.DEPLOY_USER}" \\
                            --timeout 600 \\
                            --output-format json
                    """,
                    returnStdout: true
                ).trim()
                
                // 解析部署结果
                def deployData = readJSON text: deployResult
                
                if (deployData.success) {
                    env.DEPLOYMENT_ID = deployData.deploymentId
                    env.DEPLOYED_VERSION = deployData.version
                    
                    echo "部署成功:"
                    echo "  部署ID: ${env.DEPLOYMENT_ID}"
                    echo "  部署版本: ${env.DEPLOYED_VERSION}"
                    echo "  部署时间: ${deployData.deployTime}"
                } else {
                    error "部署失败: ${deployData.message}"
                }
            }
        }
        
        // 阶段6: 部署后验证
        stage('部署后验证') {
            echo "执行部署后验证..."
            
            // 等待应用启动
            sleep(time: 30, unit: 'SECONDS')
            
            def maxRetries = 12
            def retryInterval = 10
            def healthCheckPassed = false
            
            for (int i = 1; i <= maxRetries; i++) {
                echo "健康检查第 ${i}/${maxRetries} 次..."
                
                def healthResult = performHealthCheck(
                    env.APP_NAME,
                    env.ENVIRONMENT
                )
                
                if (healthResult.healthy) {
                    echo "健康检查通过"
                    healthCheckPassed = true
                    break
                } else {
                    echo "健康检查失败: ${healthResult.message}"
                    
                    if (i < maxRetries) {
                        echo "等待 ${retryInterval} 秒后重试..."
                        sleep(time: retryInterval, unit: 'SECONDS')
                    }
                }
            }
            
            if (!healthCheckPassed) {
                error "部署后健康检查失败，可能需要手动检查或回滚"
            }
        }
        
        // 阶段7: 更新部署记录
        stage('更新部署记录') {
            echo "更新部署记录..."
            
            def deploymentRecord = [
                deploymentId: env.DEPLOYMENT_ID,
                appName: env.APP_NAME,
                version: env.VERSION,
                environment: env.ENVIRONMENT,
                previousVersion: env.CURRENT_VERSION,
                deployUser: env.DEPLOY_USER,
                buildNumber: env.BUILD_NUMBER,
                deployTime: new Date().format('yyyy-MM-dd HH:mm:ss'),
                status: 'success'
            ]
            
            // 写入部署记录文件
            writeJSON file: 'deployment-record.json', json: deploymentRecord
            archiveArtifacts artifacts: 'deployment-record.json', fingerprint: true
            
            // 更新数据库记录
            updateDeploymentDatabase(deploymentRecord)
            
            echo "部署记录更新完成"
        }
        
        // 成功处理
        currentBuild.result = 'SUCCESS'
        
        echo """
        =====================================
        部署成功
        =====================================
        应用: ${env.APP_NAME}
        版本: ${env.VERSION} (从 ${env.CURRENT_VERSION})
        环境: ${env.ENVIRONMENT}
        部署ID: ${env.DEPLOYMENT_ID}
        构建号: ${env.BUILD_NUMBER}
        操作人: ${env.DEPLOY_USER}
        =====================================
        """
        
        // 发送成功通知
        sendDeploymentNotification(
            'SUCCESS',
            "应用 ${env.APP_NAME} v${env.VERSION} 部署到 ${env.ENVIRONMENT} 成功"
        )
        
    } catch (Exception e) {
        // 失败处理
        currentBuild.result = 'FAILURE'
        
        echo "部署失败: ${e.getMessage()}"
        
        // 发送失败通知
        sendDeploymentNotification(
            'FAILURE',
            "应用 ${env.APP_NAME} v${env.VERSION} 部署到 ${env.ENVIRONMENT} 失败: ${e.getMessage()}"
        )
        
        // 询问是否需要回滚
        if (env.ENVIRONMENT == 'prod' && env.CURRENT_VERSION && env.CURRENT_VERSION != 'none') {
            try {
                timeout(time: 5, unit: 'MINUTES') {
                    def rollbackChoice = input(
                        message: '部署失败，是否执行回滚？',
                        parameters: [
                            choice(
                                name: 'ROLLBACK_CHOICE',
                                choices: ['yes', 'no'],
                                description: '选择是否回滚到上一个版本'
                            )
                        ]
                    )
                    
                    if (rollbackChoice == 'yes') {
                        echo "执行回滚到版本: ${env.CURRENT_VERSION}"
                        rollbackApplication(env.APP_NAME, env.ENVIRONMENT, env.CURRENT_VERSION)
                    }
                }
            } catch (Exception rollbackError) {
                echo "回滚过程中发生错误: ${rollbackError.getMessage()}"
            }
        }
        
        throw e
    } finally {
        // 清理工作空间
        cleanWs(
            cleanWhenNotBuilt: false,
            cleanWhenAborted: true,
            cleanWhenFailure: true,
            cleanWhenSuccess: true,
            deleteDirs: true
        )
    }
}

/**
 * 检查是否为生产环境用户
 */
def isProductionUser(String user) {
    def productionUsers = ['admin', 'release-manager', 'senior-developer']
    return productionUsers.contains(user)
}

/**
 * 检查应用状态
 */
def checkApplicationStatus(String appName, String environment) {
    try {
        def response = sh(
            script: "curl -s -f http://spider-api:9011/api/apps/status?name=${appName}&env=${environment}",
            returnStdout: true
        ).trim()
        
        def data = readJSON text: response
        
        return [
            error: false,
            currentVersion: data.currentVersion ?: 'none',
            status: data.status
        ]
    } catch (Exception e) {
        return [
            error: true,
            message: e.getMessage()
        ]
    }
}

/**
 * 执行健康检查
 */
def performHealthCheck(String appName, String environment) {
    try {
        def healthUrl = "http://${appName}.${environment}.internal.com/health"
        
        def response = httpRequest(
            httpMode: 'GET',
            url: healthUrl,
            timeout: 30,
            validResponseCodes: '200:299'
        )
        
        def healthData = readJSON text: response.content
        
        return [
            healthy: healthData.status == 'UP',
            message: healthData.message ?: 'OK'
        ]
    } catch (Exception e) {
        return [
            healthy: false,
            message: e.getMessage()
        ]
    }
}

/**
 * 发送部署通知
 */
def sendDeploymentNotification(String status, String message) {
    def color = status == 'SUCCESS' ? 'good' : 'danger'
    def emoji = status == 'SUCCESS' ? '✅' : '❌'
    
    // Slack通知
    slackSend(
        channel: '#deployment-alerts',
        color: color,
        message: "${emoji} ${message}",
        teamDomain: 'howbuy'
    )
    
    // 企业微信通知
    def webhook = env.WECOM_DEPLOYMENT_WEBHOOK
    if (webhook) {
        def payload = [
            msgtype: "text",
            text: [
                content: "${emoji} 部署通知\n\n${message}\n\n构建: ${env.BUILD_URL}"
            ]
        ]
        
        httpRequest(
            httpMode: 'POST',
            url: webhook,
            contentType: 'APPLICATION_JSON',
            requestBody: groovy.json.JsonOutput.toJson(payload)
        )
    }
}
```

### 4. Pipeline测试规范
```groovy
// test/PipelineTest.groovy
/**
 * Pipeline测试
 * 
 * <AUTHOR>
 * @date 2025-08-25 16:10:00
 */

@Library('jenkins-pipeline-unit@master') _

import com.lesfurets.jenkins.unit.BasePipelineTest
import org.junit.Test
import org.junit.Before

class PipelineTest extends BasePipelineTest {
    
    @Before
    void setUp() {
        super.setUp()
        
        // 设置Pipeline助手
        helper.registerAllowedMethod('echo', [String.class], null)
        helper.registerAllowedMethod('sh', [Map.class], { return 'test-output' })
        helper.registerAllowedMethod('checkout', [Map.class], { return [:] })
        helper.registerAllowedMethod('archiveArtifacts', [Map.class], null)
        
        // 模拟环境变量
        binding.setVariable('env', [
            JOB_NAME: 'test-app-build',
            BUILD_NUMBER: '123',
            GIT_COMMIT: 'abc123def456',
            WORKSPACE: '/var/jenkins_home/workspace/test'
        ])
        
        // 模拟参数
        binding.setVariable('params', [
            BUILD_ENV: 'test',
            APP_VERSION: '1.0.0',
            SKIP_TESTS: false,
            DEPLOY_AFTER_BUILD: true
        ])
    }
    
    @Test
    void testSuccessfulBuild() {
        // 加载Pipeline脚本
        def script = loadScript('Jenkinsfile')
        
        // 执行Pipeline
        script.run()
        
        // 验证步骤执行
        assert helper.callStack.findAll { call ->
            call.methodName == 'echo'
        }.any { call ->
            call.argsToString().contains('构建成功')
        }
    }
    
    @Test
    void testFailedBuildWithRollback() {
        // 模拟构建失败
        helper.registerAllowedMethod('sh', [Map.class], {
            throw new Exception('Build failed')
        })
        
        // 加载Pipeline脚本
        def script = loadScript('Jenkinsfile')
        
        try {
            script.run()
            assert false, "应该抛出异常"
        } catch (Exception e) {
            // 验证回滚逻辑被调用
            assert helper.callStack.findAll { call ->
                call.methodName == 'rollbackApplication'
            }.size() > 0
        }
    }
    
    @Test
    void testProductionDeploymentPermissions() {
        // 设置生产环境参数
        binding.setVariable('params', [
            BUILD_ENV: 'prod',
            APP_VERSION: '1.0.0'
        ])
        
        // 设置非授权用户
        binding.setVariable('env', [
            BUILD_USER: 'normal-user'
        ])
        
        def script = loadScript('Jenkinsfile')
        
        try {
            script.run()
            assert false, "应该检查权限失败"
        } catch (Exception e) {
            assert e.getMessage().contains('权限')
        }
    }
}
```

### 5. Groovy脚本规范
```groovy
/**
 * Jenkins任务创建脚本
 * 
 * 批量创建和更新Jenkins任务配置
 * 
 * <AUTHOR>
 * @date 2025-08-25 16:10:00
 */

@Grab('org.jenkins-ci.plugins:job-dsl-core:1.77')

import javaposse.jobdsl.dsl.DslScriptLoader
import javaposse.jobdsl.plugin.JenkinsJobManagement

// 任务配置数据
def jobConfigs = [
    [
        name: 'app-build-pipeline',
        displayName: '应用构建流水线',
        description: '自动化构建Java应用',
        gitUrl: 'http://git.internal.com/apps/sample-app.git',
        branch: 'master',
        schedule: 'H 2 * * *'
    ],
    [
        name: 'app-deploy-pipeline',
        displayName: '应用部署流水线',
        description: '自动化部署应用到各环境',
        gitUrl: 'http://git.internal.com/apps/sample-app.git',
        branch: 'master',
        schedule: null
    ]
]

// 创建任务
jobConfigs.each { config ->
    createPipelineJob(config)
}

/**
 * 创建Pipeline任务
 */
def createPipelineJob(Map config) {
    def jobName = config.name
    def jobXml = generateJobXml(config)
    
    try {
        // 检查任务是否已存在
        def existingJob = jenkins.model.Jenkins.instance.getItem(jobName)
        
        if (existingJob) {
            println "更新现有任务: ${jobName}"
            existingJob.updateByXml(new ByteArrayInputStream(jobXml.bytes))
        } else {
            println "创建新任务: ${jobName}"
            jenkins.model.Jenkins.instance.createProjectFromXML(jobName, new ByteArrayInputStream(jobXml.bytes))
        }
        
        println "任务 ${jobName} 创建/更新成功"
        
    } catch (Exception e) {
        println "任务 ${jobName} 创建失败: ${e.message}"
        throw e
    }
}

/**
 * 生成任务XML配置
 */
def generateJobXml(Map config) {
    def xmlTemplate = """<?xml version='1.1' encoding='UTF-8'?>
<flow-definition plugin="workflow-job@2.40">
    <actions/>
    <description>${config.description}</description>
    <keepDependencies>false</keepDependencies>
    <properties>
        <hudson.plugins.jira.JiraProjectProperty plugin="jira@3.1.1"/>
        <hudson.model.ParametersDefinitionProperty>
            <parameterDefinitions>
                <hudson.model.ChoiceParameterDefinition>
                    <name>BUILD_ENV</name>
                    <description>构建环境</description>
                    <choices class="java.util.Arrays\$ArrayList">
                        <a class="string-array">
                            <string>dev</string>
                            <string>test</string>
                            <string>staging</string>
                            <string>prod</string>
                        </a>
                    </choices>
                </hudson.model.ChoiceParameterDefinition>
                <hudson.model.StringParameterDefinition>
                    <name>APP_VERSION</name>
                    <description>应用版本号</description>
                    <defaultValue></defaultValue>
                    <trim>true</trim>
                </hudson.model.StringParameterDefinition>
                <hudson.model.BooleanParameterDefinition>
                    <name>SKIP_TESTS</name>
                    <description>跳过测试</description>
                    <defaultValue>false</defaultValue>
                </hudson.model.BooleanParameterDefinition>
            </parameterDefinitions>
        </hudson.model.ParametersDefinitionProperty>
        <org.jenkinsci.plugins.workflow.job.properties.PipelineTriggersJobProperty>
            <triggers>
                ${config.schedule ? "<hudson.triggers.TimerTrigger><spec>${config.schedule}</spec></hudson.triggers.TimerTrigger>" : ''}
            </triggers>
        </org.jenkinsci.plugins.workflow.job.properties.PipelineTriggersJobProperty>
    </properties>
    <definition class="org.jenkinsci.plugins.workflow.cps.CpsScmFlowDefinition" plugin="workflow-cps@2.87">
        <scm class="hudson.plugins.git.GitSCM" plugin="git@4.8.2">
            <configVersion>2</configVersion>
            <userRemoteConfigs>
                <hudson.plugins.git.UserRemoteConfig>
                    <url>${config.gitUrl}</url>
                    <credentialsId>git-credentials</credentialsId>
                </hudson.plugins.git.UserRemoteConfig>
            </userRemoteConfigs>
            <branches>
                <hudson.plugins.git.BranchSpec>
                    <name>*/${config.branch}</name>
                </hudson.plugins.git.BranchSpec>
            </branches>
            <doGenerateSubmoduleConfigurations>false</doGenerateSubmoduleConfigurations>
            <submoduleCfg class="list"/>
            <extensions/>
        </scm>
        <scriptPath>Jenkinsfile</scriptPath>
        <lightweight>true</lightweight>
    </definition>
    <triggers/>
    <disabled>false</disabled>
</flow-definition>"""

    return xmlTemplate
}

println "Jenkins任务批量创建完成"
```

---

**注意：Jenkins流水线开发必须严格遵循上述规范，确保流水线的稳定性、可维护性和安全性。**