# Vue前端开发规范

**文档创建时间：** 2025-08-25 16:00:00  
**作者：** hongdong.xie

## 适用场景
- Vue.js组件开发
- 前端页面和路由配置  
- 状态管理（Pinia/Vuex）
- 前端构建和部署
- TypeScript前端开发

## 项目架构和目录规范

### 1. 目录结构规范
```
src/
├── api/                    # API接口定义
│   ├── modules/           # 按模块分组的API
│   │   ├── app.ts
│   │   ├── user.ts
│   │   └── system.ts
│   ├── types/            # API类型定义
│   └── request.ts        # 请求封装
├── assets/               # 静态资源
│   ├── images/
│   ├── icons/
│   └── styles/
├── components/           # 公共组件
│   ├── common/          # 通用组件
│   ├── business/        # 业务组件
│   └── layout/          # 布局组件
├── composables/         # 组合式函数
│   ├── useAuth.ts
│   ├── useTable.ts
│   └── useForm.ts
├── directives/          # 自定义指令
├── hooks/               # Vue3 hooks（旧项目兼容）
├── layouts/             # 页面布局
├── router/              # 路由配置
├── store/               # 状态管理
│   ├── modules/
│   └── index.ts
├── styles/              # 样式文件
│   ├── variables.scss
│   ├── mixins.scss
│   └── global.scss
├── utils/               # 工具函数
├── views/               # 页面组件
│   ├── app-management/
│   ├── environment/
│   └── pipeline/
├── App.vue
└── main.ts
```

## Vue 3组件开发规范

### 1. 组件基本结构
```vue
<template>
  <!-- 模板内容 -->
  <div class="app-management">
    <n-card title="应用管理">
      <div class="app-list">
        <app-item 
          v-for="app in appList" 
          :key="app.id"
          :app="app"
          @edit="handleEdit"
          @delete="handleDelete"
        />
      </div>
      
      <!-- 分页组件 -->
      <n-pagination 
        v-model:page="pagination.current"
        :page-size="pagination.pageSize"
        :item-count="pagination.total"
        @update:page="fetchApps"
      />
    </n-card>
    
    <!-- 编辑对话框 -->
    <app-edit-modal 
      v-model:show="showEditModal"
      :app="currentApp"
      @save="handleSave"
    />
  </div>
</template>

<script setup lang="ts">
/**
 * 应用管理页面
 * <AUTHOR>
 * @date 2025-08-25 16:00:00
 */
import { ref, reactive, onMounted, computed } from 'vue'
import { useMessage } from 'naive-ui'
import { useAppStore } from '@/store/modules/app'
import { useAuth } from '@/composables/useAuth'
import AppItem from './components/AppItem.vue'
import AppEditModal from './components/AppEditModal.vue'
import type { App, PaginationInfo } from '@/types/app'

// 响应式数据
const message = useMessage()
const appStore = useAppStore()
const { hasPermission } = useAuth()

const appList = ref<App[]>([])
const loading = ref(false)
const showEditModal = ref(false)
const currentApp = ref<App | null>(null)

// 分页信息
const pagination = reactive<PaginationInfo>({
  current: 1,
  pageSize: 20,
  total: 0
})

// 计算属性
const canEdit = computed(() => hasPermission('app:edit'))
const canDelete = computed(() => hasPermission('app:delete'))

// 方法
const fetchApps = async (page = 1) => {
  try {
    loading.value = true
    const params = {
      page,
      pageSize: pagination.pageSize
    }
    
    const response = await appStore.fetchAppList(params)
    
    appList.value = response.items
    pagination.current = page
    pagination.total = response.total
  } catch (error) {
    message.error('获取应用列表失败')
    console.error('Fetch apps error:', error)
  } finally {
    loading.value = false
  }
}

const handleEdit = (app: App) => {
  if (!canEdit.value) {
    message.warning('您没有编辑权限')
    return
  }
  
  currentApp.value = app
  showEditModal.value = true
}

const handleDelete = async (app: App) => {
  if (!canDelete.value) {
    message.warning('您没有删除权限')
    return
  }
  
  try {
    await appStore.deleteApp(app.id)
    message.success('删除成功')
    fetchApps(pagination.current)
  } catch (error) {
    message.error('删除失败')
  }
}

const handleSave = async (appData: Partial<App>) => {
  try {
    if (currentApp.value?.id) {
      await appStore.updateApp(currentApp.value.id, appData)
      message.success('更新成功')
    } else {
      await appStore.createApp(appData)
      message.success('创建成功')
    }
    
    showEditModal.value = false
    fetchApps(pagination.current)
  } catch (error) {
    message.error('保存失败')
  }
}

// 生命周期
onMounted(() => {
  fetchApps()
})
</script>

<style lang="scss" scoped>
.app-management {
  padding: 16px;
  
  .app-list {
    margin-bottom: 16px;
    
    .app-item + .app-item {
      margin-top: 12px;
    }
  }
}

// 响应式样式
@media (max-width: 768px) {
  .app-management {
    padding: 8px;
  }
}
</style>
```

### 2. 组件规范要点
- **组件名称**：使用PascalCase，多个单词用连字符连接
- **Props定义**：使用TypeScript类型定义，必须有默认值和验证
- **事件命名**：使用kebab-case，语义化命名
- **插槽使用**：合理使用具名插槽和作用域插槽
- **样式作用域**：使用scoped样式，避免样式污染

### 3. Props和事件规范
```vue
<script setup lang="ts">
/**
 * 应用项组件
 * <AUTHOR>  
 * @date 2025-08-25 16:00:00
 */
import { computed } from 'vue'
import type { App } from '@/types/app'

// Props定义
interface Props {
  app: App
  showActions?: boolean
  size?: 'small' | 'medium' | 'large'
}

const props = withDefaults(defineProps<Props>(), {
  showActions: true,
  size: 'medium'
})

// 事件定义
interface Emits {
  edit: [app: App]
  delete: [app: App]
  view: [app: App]
}

const emit = defineEmits<Emits>()

// 计算属性
const statusColor = computed(() => {
  const colorMap = {
    active: 'success',
    inactive: 'warning', 
    archived: 'error'
  }
  return colorMap[props.app.status] || 'default'
})

const sizeClass = computed(() => `app-item--${props.size}`)

// 方法
const handleEdit = () => {
  emit('edit', props.app)
}

const handleDelete = () => {
  emit('delete', props.app)
}

const handleView = () => {
  emit('view', props.app)
}
</script>
```

## 状态管理规范（Pinia）

### 1. Store结构规范
```typescript
// store/modules/app.ts
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { appApi } from '@/api/modules/app'
import type { App, AppListParams, AppListResponse } from '@/types/app'

/**
 * 应用管理Store
 * <AUTHOR>
 * @date 2025-08-25 16:00:00
 */
export const useAppStore = defineStore('app', () => {
  // 状态定义
  const appList = ref<App[]>([])
  const currentApp = ref<App | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)
  
  // 计算属性
  const activeApps = computed(() => 
    appList.value.filter(app => app.status === 'active')
  )
  
  const appCount = computed(() => ({
    total: appList.value.length,
    active: activeApps.value.length,
    inactive: appList.value.filter(app => app.status === 'inactive').length
  }))
  
  // Actions
  const fetchAppList = async (params: AppListParams): Promise<AppListResponse> => {
    try {
      loading.value = true
      error.value = null
      
      const response = await appApi.getAppList(params)
      
      if (response.code === 0) {
        appList.value = response.data.items
        return response.data
      } else {
        throw new Error(response.message)
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取应用列表失败'
      throw err
    } finally {
      loading.value = false
    }
  }
  
  const fetchAppById = async (id: number): Promise<App> => {
    try {
      loading.value = true
      
      const response = await appApi.getAppById(id)
      
      if (response.code === 0) {
        currentApp.value = response.data
        return response.data
      } else {
        throw new Error(response.message)
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取应用详情失败'
      throw err
    } finally {
      loading.value = false
    }
  }
  
  const createApp = async (appData: Partial<App>): Promise<App> => {
    try {
      const response = await appApi.createApp(appData)
      
      if (response.code === 0) {
        appList.value.push(response.data)
        return response.data
      } else {
        throw new Error(response.message)
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '创建应用失败'
      throw err
    }
  }
  
  const updateApp = async (id: number, appData: Partial<App>): Promise<App> => {
    try {
      const response = await appApi.updateApp(id, appData)
      
      if (response.code === 0) {
        const index = appList.value.findIndex(app => app.id === id)
        if (index !== -1) {
          appList.value[index] = { ...appList.value[index], ...response.data }
        }
        return response.data
      } else {
        throw new Error(response.message)
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '更新应用失败'
      throw err
    }
  }
  
  const deleteApp = async (id: number): Promise<void> => {
    try {
      const response = await appApi.deleteApp(id)
      
      if (response.code === 0) {
        const index = appList.value.findIndex(app => app.id === id)
        if (index !== -1) {
          appList.value.splice(index, 1)
        }
      } else {
        throw new Error(response.message)
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '删除应用失败'
      throw err
    }
  }
  
  // 重置状态
  const resetState = () => {
    appList.value = []
    currentApp.value = null
    loading.value = false
    error.value = null
  }
  
  return {
    // 状态
    appList,
    currentApp,
    loading,
    error,
    
    // 计算属性
    activeApps,
    appCount,
    
    // Actions
    fetchAppList,
    fetchAppById,
    createApp,
    updateApp,
    deleteApp,
    resetState
  }
})
```

### 2. 组合式函数（Composables）
```typescript
// composables/useTable.ts
import { ref, reactive, computed } from 'vue'

/**
 * 表格通用组合函数
 * <AUTHOR>
 * @date 2025-08-25 16:00:00
 */
export interface TableOptions<T = any> {
  fetchData: (params: any) => Promise<{ items: T[], total: number }>
  immediate?: boolean
  defaultPageSize?: number
}

export interface PaginationState {
  current: number
  pageSize: number
  total: number
}

export function useTable<T = any>(options: TableOptions<T>) {
  const { fetchData, immediate = true, defaultPageSize = 20 } = options
  
  // 状态
  const loading = ref(false)
  const data = ref<T[]>([])
  const error = ref<string | null>(null)
  
  const pagination = reactive<PaginationState>({
    current: 1,
    pageSize: defaultPageSize,
    total: 0
  })
  
  const searchParams = ref<Record<string, any>>({})
  
  // 计算属性
  const isEmpty = computed(() => data.value.length === 0)
  const hasMore = computed(() => 
    pagination.current * pagination.pageSize < pagination.total
  )
  
  // 方法
  const load = async (params: Record<string, any> = {}) => {
    try {
      loading.value = true
      error.value = null
      
      const requestParams = {
        page: pagination.current,
        pageSize: pagination.pageSize,
        ...searchParams.value,
        ...params
      }
      
      const response = await fetchData(requestParams)
      
      data.value = response.items
      pagination.total = response.total
      
      return response
    } catch (err) {
      error.value = err instanceof Error ? err.message : '加载数据失败'
      throw err
    } finally {
      loading.value = false
    }
  }
  
  const reload = () => load()
  
  const search = (params: Record<string, any>) => {
    searchParams.value = { ...params }
    pagination.current = 1
    return load()
  }
  
  const changePage = (page: number) => {
    pagination.current = page
    return load()
  }
  
  const changePageSize = (pageSize: number) => {
    pagination.pageSize = pageSize
    pagination.current = 1
    return load()
  }
  
  const reset = () => {
    searchParams.value = {}
    pagination.current = 1
    pagination.total = 0
    data.value = []
    error.value = null
  }
  
  // 初始化加载
  if (immediate) {
    load()
  }
  
  return {
    // 状态
    loading: readonly(loading),
    data: readonly(data),
    error: readonly(error),
    pagination: readonly(pagination),
    searchParams: readonly(searchParams),
    
    // 计算属性
    isEmpty,
    hasMore,
    
    // 方法
    load,
    reload,
    search,
    changePage,
    changePageSize,
    reset
  }
}
```

## API接口封装规范

### 1. 请求封装
```typescript
// api/request.ts
import axios, { 
  AxiosInstance, 
  AxiosRequestConfig, 
  AxiosResponse, 
  AxiosError 
} from 'axios'
import { useAuthStore } from '@/store/modules/auth'
import { useMessage } from 'naive-ui'

/**
 * API响应接口
 * <AUTHOR>
 * @date 2025-08-25 16:00:00
 */
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  timestamp?: number
}

/**
 * 创建axios实例
 */
const createAxiosInstance = (): AxiosInstance => {
  const instance = axios.create({
    baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json'
    }
  })
  
  // 请求拦截器
  instance.interceptors.request.use(
    (config) => {
      const authStore = useAuthStore()
      
      // 添加认证token
      if (authStore.token) {
        config.headers['Authorization'] = `Bearer ${authStore.token}`
      }
      
      // 添加请求ID用于链路追踪
      config.headers['X-Request-ID'] = generateRequestId()
      
      return config
    },
    (error: AxiosError) => {
      console.error('Request error:', error)
      return Promise.reject(error)
    }
  )
  
  // 响应拦截器
  instance.interceptors.response.use(
    (response: AxiosResponse<ApiResponse>) => {
      const { data } = response
      
      // 统一处理业务错误
      if (data.code !== 0) {
        const message = useMessage()
        message.error(data.message || '请求失败')
        return Promise.reject(new Error(data.message))
      }
      
      return response
    },
    (error: AxiosError) => {
      const message = useMessage()
      
      if (error.response) {
        const { status, data } = error.response
        
        switch (status) {
          case 401:
            // 未授权，跳转登录
            const authStore = useAuthStore()
            authStore.logout()
            window.location.href = '/login'
            break
          case 403:
            message.error('权限不足')
            break
          case 404:
            message.error('请求的资源不存在')
            break
          case 500:
            message.error('服务器内部错误')
            break
          default:
            message.error(
              (data as any)?.message || `请求失败（${status}）`
            )
        }
      } else if (error.request) {
        message.error('网络连接失败，请检查网络')
      } else {
        message.error('请求配置错误')
      }
      
      return Promise.reject(error)
    }
  )
  
  return instance
}

// 生成请求ID
const generateRequestId = (): string => {
  return `req-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
}

// 导出实例
export const request = createAxiosInstance()

// 封装常用请求方法
export const api = {
  get: <T = any>(url: string, config?: AxiosRequestConfig) =>
    request.get<ApiResponse<T>>(url, config),
    
  post: <T = any>(url: string, data?: any, config?: AxiosRequestConfig) =>
    request.post<ApiResponse<T>>(url, data, config),
    
  put: <T = any>(url: string, data?: any, config?: AxiosRequestConfig) =>
    request.put<ApiResponse<T>>(url, data, config),
    
  patch: <T = any>(url: string, data?: any, config?: AxiosRequestConfig) =>
    request.patch<ApiResponse<T>>(url, data, config),
    
  delete: <T = any>(url: string, config?: AxiosRequestConfig) =>
    request.delete<ApiResponse<T>>(url, config)
}
```

### 2. API模块定义
```typescript
// api/modules/app.ts
import { api } from '../request'
import type { 
  App, 
  AppListParams, 
  AppListResponse, 
  CreateAppRequest,
  UpdateAppRequest 
} from '@/types/app'

/**
 * 应用管理API
 * <AUTHOR>
 * @date 2025-08-25 16:00:00
 */
export const appApi = {
  /**
   * 获取应用列表
   */
  getAppList: (params: AppListParams) => 
    api.get<AppListResponse>('/apps', { params }),
  
  /**
   * 获取应用详情
   */
  getAppById: (id: number) => 
    api.get<App>(`/apps/${id}`),
  
  /**
   * 创建应用
   */
  createApp: (data: CreateAppRequest) => 
    api.post<App>('/apps', data),
  
  /**
   * 更新应用
   */
  updateApp: (id: number, data: UpdateAppRequest) => 
    api.patch<App>(`/apps/${id}`, data),
  
  /**
   * 删除应用
   */
  deleteApp: (id: number) => 
    api.delete(`/apps/${id}`),
  
  /**
   * 获取应用统计信息
   */
  getAppStatistics: () => 
    api.get<{
      totalCount: number
      activeCount: number
      inactiveCount: number
    }>('/apps/statistics')
}
```

## 路由和导航规范

### 1. 路由配置
```typescript
// router/modules/app.ts
import type { RouteRecordRaw } from 'vue-router'

/**
 * 应用管理路由
 * <AUTHOR>
 * @date 2025-08-25 16:00:00
 */
export const appRoutes: RouteRecordRaw[] = [
  {
    path: '/app-management',
    name: 'AppManagement',
    component: () => import('@/layouts/BasicLayout.vue'),
    meta: {
      title: '应用管理',
      icon: 'apps',
      requireAuth: true,
      permissions: ['app:view']
    },
    children: [
      {
        path: '',
        name: 'AppList',
        component: () => import('@/views/app-management/AppList.vue'),
        meta: {
          title: '应用列表',
          keepAlive: true
        }
      },
      {
        path: 'create',
        name: 'AppCreate',
        component: () => import('@/views/app-management/AppForm.vue'),
        meta: {
          title: '创建应用',
          permissions: ['app:create']
        }
      },
      {
        path: ':id/edit',
        name: 'AppEdit',
        component: () => import('@/views/app-management/AppForm.vue'),
        meta: {
          title: '编辑应用',
          permissions: ['app:edit']
        },
        props: true
      },
      {
        path: ':id',
        name: 'AppDetail',
        component: () => import('@/views/app-management/AppDetail.vue'),
        meta: {
          title: '应用详情',
          keepAlive: false
        },
        props: true
      }
    ]
  }
]
```

### 2. 路由守卫
```typescript
// router/guards.ts
import type { Router } from 'vue-router'
import { useAuthStore } from '@/store/modules/auth'
import { useMessage } from 'naive-ui'

/**
 * 设置路由守卫
 * <AUTHOR>
 * @date 2025-08-25 16:00:00
 */
export function setupRouterGuards(router: Router) {
  // 全局前置守卫
  router.beforeEach(async (to, from, next) => {
    const authStore = useAuthStore()
    const message = useMessage()
    
    // 检查认证状态
    if (to.meta.requireAuth && !authStore.isAuthenticated) {
      message.warning('请先登录')
      next({ name: 'Login', query: { redirect: to.fullPath } })
      return
    }
    
    // 检查权限
    if (to.meta.permissions) {
      const permissions = Array.isArray(to.meta.permissions) 
        ? to.meta.permissions 
        : [to.meta.permissions]
      
      const hasPermission = permissions.every(permission =>
        authStore.hasPermission(permission)
      )
      
      if (!hasPermission) {
        message.error('权限不足')
        next({ name: 'Forbidden' })
        return
      }
    }
    
    next()
  })
  
  // 全局后置钩子
  router.afterEach((to) => {
    // 设置页面标题
    if (to.meta.title) {
      document.title = `${to.meta.title} - PA平台`
    }
    
    // 页面访问统计
    if (import.meta.env.PROD) {
      // 发送页面浏览统计
      console.log(`Page view: ${to.fullPath}`)
    }
  })
  
  // 路由错误处理
  router.onError((error) => {
    console.error('Router error:', error)
    const message = useMessage()
    message.error('页面加载失败')
  })
}
```

## 样式和主题规范

### 1. SCSS变量定义
```scss
// styles/variables.scss

/**
 * 设计令牌定义
 * <AUTHOR>
 * @date 2025-08-25 16:00:00
 */

// 颜色系统
$primary-color: #1890ff;
$success-color: #52c41a;
$warning-color: #faad14;
$error-color: #f5222d;
$info-color: #1890ff;

// 中性色
$text-color-primary: rgba(0, 0, 0, 0.85);
$text-color-secondary: rgba(0, 0, 0, 0.65);
$text-color-disabled: rgba(0, 0, 0, 0.25);

$border-color-base: #d9d9d9;
$border-color-light: #f0f0f0;

$background-color-base: #fafafa;
$background-color-light: #ffffff;

// 间距系统
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 16px;
$spacing-lg: 24px;
$spacing-xl: 32px;

// 字体系统
$font-size-base: 14px;
$font-size-sm: 12px;
$font-size-lg: 16px;
$font-size-xl: 20px;

$font-family-base: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 
  'Helvetica Neue', Arial, 'Noto Sans', sans-serif;

// 圆角
$border-radius-base: 6px;
$border-radius-sm: 4px;
$border-radius-lg: 8px;

// 阴影
$box-shadow-base: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 
  0 6px 16px 0 rgba(0, 0, 0, 0.08), 
  0 9px 28px 8px rgba(0, 0, 0, 0.05);

// 层级
$zindex-modal: 1000;
$zindex-notification: 1010;
$zindex-tooltip: 1020;

// 响应式断点
$screen-xs: 480px;
$screen-sm: 576px;
$screen-md: 768px;
$screen-lg: 992px;
$screen-xl: 1200px;
$screen-xxl: 1600px;
```

### 2. 组件样式规范
```scss
// components/AppItem.vue <style>
.app-item {
  // 使用BEM命名规范
  padding: $spacing-md;
  border: 1px solid $border-color-base;
  border-radius: $border-radius-base;
  background-color: $background-color-light;
  
  &:hover {
    border-color: $primary-color;
    box-shadow: $box-shadow-base;
  }
  
  // 修饰符
  &--small {
    padding: $spacing-sm;
    
    .app-item__title {
      font-size: $font-size-sm;
    }
  }
  
  &--large {
    padding: $spacing-lg;
    
    .app-item__title {
      font-size: $font-size-lg;
    }
  }
  
  // 元素
  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: $spacing-sm;
  }
  
  &__title {
    font-size: $font-size-base;
    font-weight: 500;
    color: $text-color-primary;
    margin: 0;
  }
  
  &__status {
    &--active {
      color: $success-color;
    }
    
    &--inactive {
      color: $warning-color;
    }
    
    &--archived {
      color: $text-color-disabled;
    }
  }
  
  &__content {
    color: $text-color-secondary;
    font-size: $font-size-sm;
    line-height: 1.5;
  }
  
  &__actions {
    margin-top: $spacing-md;
    text-align: right;
    
    .n-button + .n-button {
      margin-left: $spacing-sm;
    }
  }
}

// 响应式设计
@media (max-width: $screen-md) {
  .app-item {
    &__header {
      flex-direction: column;
      align-items: flex-start;
      gap: $spacing-xs;
    }
    
    &__actions {
      text-align: left;
    }
  }
}
```

## 性能优化规范

### 1. 组件懒加载
```typescript
// 路由懒加载
const AppList = () => import('@/views/app-management/AppList.vue')

// 组件动态导入
import { defineAsyncComponent } from 'vue'

const AsyncAppChart = defineAsyncComponent({
  loader: () => import('./AppChart.vue'),
  loadingComponent: LoadingSpinner,
  errorComponent: ErrorDisplay,
  delay: 200,
  timeout: 5000
})
```

### 2. 列表虚拟化
```vue
<template>
  <div class="virtual-list-container">
    <RecycleScroller
      v-slot="{ item }"
      class="scroller"
      :items="appList"
      :item-size="80"
      key-field="id"
    >
      <app-item :app="item" />
    </RecycleScroller>
  </div>
</template>

<script setup lang="ts">
import { RecycleScroller } from 'vue-virtual-scroller'
import AppItem from './AppItem.vue'
</script>
```

### 3. 图片懒加载和优化
```vue
<template>
  <div class="app-logo">
    <img 
      v-lazy="app.logoUrl"
      :alt="app.name"
      @error="handleImageError"
    />
  </div>
</template>

<script setup lang="ts">
const handleImageError = (event: Event) => {
  const target = event.target as HTMLImageElement
  target.src = '/images/default-app-logo.png'
}
</script>
```

## 测试规范

### 1. 组件单元测试
```typescript
// tests/components/AppItem.spec.ts
import { mount } from '@vue/test-utils'
import { describe, it, expect, vi } from 'vitest'
import AppItem from '@/components/AppItem.vue'
import type { App } from '@/types/app'

/**
 * AppItem组件测试
 * <AUTHOR>
 * @date 2025-08-25 16:00:00
 */
describe('AppItem', () => {
  const mockApp: App = {
    id: 1,
    name: '测试应用',
    code: 'test-app',
    status: 'active',
    description: '这是一个测试应用'
  }
  
  it('should render app information correctly', () => {
    const wrapper = mount(AppItem, {
      props: { app: mockApp }
    })
    
    expect(wrapper.text()).toContain('测试应用')
    expect(wrapper.text()).toContain('test-app')
    expect(wrapper.text()).toContain('这是一个测试应用')
  })
  
  it('should emit edit event when edit button clicked', async () => {
    const wrapper = mount(AppItem, {
      props: { app: mockApp }
    })
    
    await wrapper.find('[data-testid="edit-btn"]').trigger('click')
    
    expect(wrapper.emitted().edit).toBeTruthy()
    expect(wrapper.emitted().edit[0]).toEqual([mockApp])
  })
  
  it('should apply correct status class', () => {
    const wrapper = mount(AppItem, {
      props: { app: mockApp }
    })
    
    const statusEl = wrapper.find('[data-testid="app-status"]')
    expect(statusEl.classes()).toContain('app-item__status--active')
  })
})
```

### 2. E2E测试
```typescript
// e2e/app-management.spec.ts
import { test, expect } from '@playwright/test'

/**
 * 应用管理E2E测试
 * <AUTHOR>
 * @date 2025-08-25 16:00:00
 */
test.describe('应用管理', () => {
  test.beforeEach(async ({ page }) => {
    // 登录
    await page.goto('/login')
    await page.fill('[data-testid="username"]', 'testuser')
    await page.fill('[data-testid="password"]', 'testpass')
    await page.click('[data-testid="login-btn"]')
    
    // 等待跳转到应用管理页面
    await page.waitForURL('/app-management')
  })
  
  test('should display app list', async ({ page }) => {
    // 验证页面标题
    await expect(page.locator('h1')).toContainText('应用管理')
    
    // 验证应用列表存在
    await expect(page.locator('[data-testid="app-list"]')).toBeVisible()
    
    // 验证至少有一个应用项
    const appItems = page.locator('[data-testid="app-item"]')
    await expect(appItems).toHaveCountGreaterThan(0)
  })
  
  test('should create new app', async ({ page }) => {
    // 点击创建按钮
    await page.click('[data-testid="create-app-btn"]')
    
    // 填写表单
    await page.fill('[data-testid="app-name"]', '新测试应用')
    await page.fill('[data-testid="app-code"]', 'new-test-app')
    await page.fill('[data-testid="description"]', '这是新创建的测试应用')
    
    // 提交表单
    await page.click('[data-testid="submit-btn"]')
    
    // 验证成功消息
    await expect(page.locator('.n-message')).toContainText('创建成功')
    
    // 验证列表中出现新应用
    await expect(page.locator('[data-testid="app-list"]')).toContainText('新测试应用')
  })
})
```

---

**注意：Vue前端开发必须严格遵循上述规范，确保代码的一致性、可维护性和高性能。**