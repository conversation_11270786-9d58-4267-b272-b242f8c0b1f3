# 数据库层开发规范

**文档创建时间：** 2025-08-25 15:45:00  
**作者：** hongdong.xie

## 适用场景
- 数据库模型设计和ORM操作
- SQL脚本编写和数据迁移
- 数据库性能优化
- 数据库版本管理和部署

## 数据库设计原则

### 1. 表结构设计规范
```sql
-- 表命名规范：模块前缀_业务含义
-- 示例：app_mgt_app_info, env_mgt_node_info
CREATE TABLE app_mgt_app_info (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    app_name VARCHAR(100) NOT NULL COMMENT '应用名称',
    app_code VARCHAR(50) NOT NULL UNIQUE COMMENT '应用编码',
    description TEXT COMMENT '应用描述',
    team_id BIGINT NOT NULL COMMENT '团队ID',
    owner_id BIGINT COMMENT '负责人ID',
    status TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_deleted TINYINT DEFAULT 0 COMMENT '是否删除：1-已删除，0-未删除',
    
    INDEX idx_app_code (app_code),
    INDEX idx_team_id (team_id),
    INDEX idx_owner_id (owner_id),
    INDEX idx_status_deleted (status, is_deleted),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='应用信息表';
```

#### 表设计要点
- 主键必须使用`BIGINT AUTO_INCREMENT`
- 所有字段必须有`COMMENT`注释
- 必须包含`created_at`和`updated_at`时间字段
- 使用软删除字段`is_deleted`代替物理删除
- 状态字段使用`TINYINT`类型，并明确说明含义
- 字符串字段长度要合理设置
- 必要的字段创建索引

### 2. 索引设计规范
```sql
-- 单列索引
ALTER TABLE app_mgt_app_info ADD INDEX idx_app_code (app_code);

-- 复合索引（注意字段顺序）
ALTER TABLE app_mgt_app_info ADD INDEX idx_team_status (team_id, status, is_deleted);

-- 唯一索引
ALTER TABLE app_mgt_app_info ADD UNIQUE INDEX uk_app_code (app_code);

-- 全文索引（MySQL 5.7+）
ALTER TABLE app_mgt_app_info ADD FULLTEXT INDEX ft_description (description);
```

#### 索引设计要点
- 经常用于WHERE条件的字段创建索引
- 复合索引字段顺序：等值查询在前，范围查询在后
- 索引名称格式：`idx_字段名` 或 `uk_字段名`（唯一索引）
- 避免过多索引影响写性能
- 定期分析索引使用情况

### 3. 外键约束设计
```sql
-- 外键约束（推荐在应用层维护关系）
ALTER TABLE app_mgt_app_module ADD CONSTRAINT fk_module_app_id 
    FOREIGN KEY (app_id) REFERENCES app_mgt_app_info(id) 
    ON DELETE CASCADE ON UPDATE CASCADE;

-- 在Django模型中维护外键关系（推荐）
class AppModule(models.Model):
    app = models.ForeignKey(
        AppInfo, 
        on_delete=models.CASCADE,
        related_name='modules',
        db_constraint=False  # 不在数据库层创建外键约束
    )
```

## Django ORM最佳实践

### 1. 模型定义规范
```python
from django.db import models
from django.contrib.auth.models import User
from typing import Optional

class TimestampMixin(models.Model):
    """
    时间戳混入类
    <AUTHOR>
    @date 2025-08-25 15:45:00
    """
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")
    
    class Meta:
        abstract = True

class SoftDeleteMixin(models.Model):
    """
    软删除混入类
    <AUTHOR>
    @date 2025-08-25 15:45:00
    """
    is_deleted = models.BooleanField(default=False, verbose_name="是否删除")
    
    class Meta:
        abstract = True
    
    def delete(self, using=None, keep_parents=False):
        """重写删除方法实现软删除"""
        self.is_deleted = True
        self.save(using=using)
        
    def hard_delete(self, using=None, keep_parents=False):
        """物理删除"""
        super().delete(using=using, keep_parents=keep_parents)

class AppInfo(TimestampMixin, SoftDeleteMixin):
    """
    应用信息模型
    <AUTHOR>
    @date 2025-08-25 15:45:00
    """
    
    class StatusChoices(models.TextChoices):
        ACTIVE = 'active', '正常'
        INACTIVE = 'inactive', '禁用'
        ARCHIVED = 'archived', '已归档'
    
    app_name = models.CharField(max_length=100, verbose_name="应用名称")
    app_code = models.CharField(max_length=50, unique=True, verbose_name="应用编码")
    description = models.TextField(blank=True, verbose_name="应用描述")
    team = models.ForeignKey(
        'team_mgt.Team',
        on_delete=models.PROTECT,
        related_name='apps',
        verbose_name="所属团队"
    )
    owner = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        related_name='owned_apps',
        verbose_name="负责人"
    )
    status = models.CharField(
        max_length=20,
        choices=StatusChoices.choices,
        default=StatusChoices.ACTIVE,
        verbose_name="状态"
    )
    
    class Meta:
        db_table = 'app_mgt_app_info'
        verbose_name = '应用信息'
        verbose_name_plural = '应用信息'
        indexes = [
            models.Index(fields=['app_code'], name='idx_app_code'),
            models.Index(fields=['team'], name='idx_team_id'),
            models.Index(fields=['status', 'is_deleted'], name='idx_status_deleted'),
        ]
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.app_name}({self.app_code})"
```

### 2. 查询优化规范
```python
from django.db import models
from django.db.models import Prefetch, Q, F, Count, Sum
from typing import List, Optional

class AppInfoQuerySet(models.QuerySet):
    """
    应用信息查询集
    <AUTHOR>
    @date 2025-08-25 15:45:00
    """
    
    def active(self):
        """获取活跃应用"""
        return self.filter(status='active', is_deleted=False)
    
    def by_team(self, team_id: int):
        """按团队筛选"""
        return self.filter(team_id=team_id)
    
    def with_modules(self):
        """预加载模块信息"""
        return self.prefetch_related(
            Prefetch(
                'modules',
                queryset=AppModule.objects.filter(is_deleted=False)
            )
        )
    
    def with_statistics(self):
        """添加统计信息"""
        return self.annotate(
            modules_count=Count('modules', filter=Q(modules__is_deleted=False)),
            total_deployments=Sum('modules__deployments_count')
        )

class AppInfoManager(models.Manager):
    """
    应用信息管理器
    <AUTHOR>
    @date 2025-08-25 15:45:00
    """
    
    def get_queryset(self):
        return AppInfoQuerySet(self.model, using=self._db)
    
    def active(self):
        return self.get_queryset().active()
    
    def by_team(self, team_id: int):
        return self.get_queryset().by_team(team_id)

# 在模型中使用自定义管理器
class AppInfo(TimestampMixin, SoftDeleteMixin):
    # ... 字段定义 ...
    
    objects = AppInfoManager()
    
    @classmethod
    def get_app_statistics(cls, team_id: Optional[int] = None):
        """获取应用统计信息"""
        queryset = cls.objects.active()
        if team_id:
            queryset = queryset.by_team(team_id)
        
        return queryset.aggregate(
            total_count=Count('id'),
            active_count=Count('id', filter=Q(status='active')),
            inactive_count=Count('id', filter=Q(status='inactive'))
        )
```

### 3. 复杂查询示例
```python
from django.db.models import Q, F, Case, When, Value
from django.db.models.functions import Concat

class AppService:
    """
    应用服务类
    <AUTHOR>
    @date 2025-08-25 15:45:00
    """
    
    @staticmethod
    def search_apps(keyword: str, team_ids: List[int] = None, status: str = None):
        """搜索应用"""
        queryset = AppInfo.objects.select_related('team', 'owner').active()
        
        # 关键字搜索
        if keyword:
            queryset = queryset.filter(
                Q(app_name__icontains=keyword) |
                Q(app_code__icontains=keyword) |
                Q(description__icontains=keyword)
            )
        
        # 团队筛选
        if team_ids:
            queryset = queryset.filter(team_id__in=team_ids)
        
        # 状态筛选
        if status:
            queryset = queryset.filter(status=status)
        
        # 添加计算字段
        queryset = queryset.annotate(
            full_name=Concat('app_name', Value('('), 'app_code', Value(')')),
            priority=Case(
                When(status='active', then=Value(1)),
                When(status='inactive', then=Value(2)),
                default=Value(3),
                output_field=models.IntegerField()
            )
        ).order_by('priority', '-created_at')
        
        return queryset
    
    @staticmethod
    def get_app_with_deployment_info(app_id: int):
        """获取应用及其部署信息"""
        return AppInfo.objects.select_related(
            'team', 'owner'
        ).prefetch_related(
            'modules',
            'modules__deployments',
            'modules__deployments__environment'
        ).get(id=app_id, is_deleted=False)
    
    @staticmethod
    def bulk_update_app_status(app_ids: List[int], status: str):
        """批量更新应用状态"""
        return AppInfo.objects.filter(
            id__in=app_ids,
            is_deleted=False
        ).update(
            status=status,
            updated_at=timezone.now()
        )
```

## 数据迁移规范

### 1. Django迁移文件
```python
# 迁移文件命名：0001_initial.py, 0002_add_status_field.py
from django.db import migrations, models
import django.db.models.deletion

class Migration(migrations.Migration):
    """
    应用信息表初始迁移
    <AUTHOR>
    @date 2025-08-25 15:45:00
    """
    
    initial = True
    
    dependencies = [
        ('team_mgt', '0001_initial'),
        ('auth', '0012_alter_user_first_name_max_length'),
    ]
    
    operations = [
        migrations.CreateModel(
            name='AppInfo',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('app_name', models.CharField(max_length=100, verbose_name='应用名称')),
                ('app_code', models.CharField(max_length=50, unique=True, verbose_name='应用编码')),
                ('description', models.TextField(blank=True, verbose_name='应用描述')),
                ('status', models.CharField(choices=[('active', '正常'), ('inactive', '禁用'), ('archived', '已归档')], default='active', max_length=20, verbose_name='状态')),
                ('owner', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='owned_apps', to='auth.user', verbose_name='负责人')),
                ('team', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='apps', to='team_mgt.team', verbose_name='所属团队')),
            ],
            options={
                'verbose_name': '应用信息',
                'verbose_name_plural': '应用信息',
                'db_table': 'app_mgt_app_info',
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddIndex(
            model_name='appinfo',
            index=models.Index(fields=['app_code'], name='idx_app_code'),
        ),
        migrations.AddIndex(
            model_name='appinfo',
            index=models.Index(fields=['team'], name='idx_team_id'),
        ),
    ]
```

### 2. 数据迁移脚本
```python
# 数据迁移示例
from django.db import migrations
from django.contrib.auth.models import User

def forward_migration(apps, schema_editor):
    """
    正向数据迁移
    <AUTHOR>
    @date 2025-08-25 15:45:00
    """
    AppInfo = apps.get_model('app_mgt', 'AppInfo')
    Team = apps.get_model('team_mgt', 'Team')
    
    # 为现有应用分配默认团队
    default_team, created = Team.objects.get_or_create(
        name='默认团队',
        defaults={'description': '系统自动创建的默认团队'}
    )
    
    AppInfo.objects.filter(team__isnull=True).update(team=default_team)

def backward_migration(apps, schema_editor):
    """反向数据迁移"""
    # 反向迁移逻辑
    pass

class Migration(migrations.Migration):
    
    dependencies = [
        ('app_mgt', '0002_appinfo_team'),
    ]
    
    operations = [
        migrations.RunPython(forward_migration, backward_migration),
    ]
```

### 3. SQL迁移脚本规范
```sql
-- 文件命名格式：迭代版本/序号-功能描述.sql
-- 示例：迭代3_3_8/01-应用信息表优化.sql

-- 脚本头部必须包含描述信息
/*
 * 应用信息表结构优化
 * 作者：hongdong.xie
 * 日期：2025-08-25 15:45:00
 * 版本：3.3.8
 * 描述：优化应用信息表索引，添加状态字段
 */

-- 使用事务确保原子性
START TRANSACTION;

-- 1. 添加新字段
ALTER TABLE app_mgt_app_info 
ADD COLUMN priority TINYINT DEFAULT 1 COMMENT '优先级：1-高，2-中，3-低' AFTER status;

-- 2. 创建索引
ALTER TABLE app_mgt_app_info 
ADD INDEX idx_priority (priority);

-- 3. 更新现有数据
UPDATE app_mgt_app_info 
SET priority = CASE 
    WHEN status = 'active' THEN 1
    WHEN status = 'inactive' THEN 2
    ELSE 3
END
WHERE priority IS NULL;

-- 4. 验证数据
SELECT COUNT(*) as total_count, 
       SUM(CASE WHEN priority IS NOT NULL THEN 1 ELSE 0 END) as updated_count
FROM app_mgt_app_info;

-- 提交事务
COMMIT;
```

## 性能优化规范

### 1. 查询性能优化
```python
from django.db import connection
from django.core.cache import cache
import logging

logger = logging.getLogger(__name__)

class DatabasePerformanceMixin:
    """
    数据库性能监控混入类
    <AUTHOR>
    @date 2025-08-25 15:45:00
    """
    
    def log_queries(self, func_name: str):
        """记录查询统计"""
        initial_queries = len(connection.queries)
        
        def decorator(func):
            def wrapper(*args, **kwargs):
                result = func(*args, **kwargs)
                
                queries_count = len(connection.queries) - initial_queries
                if queries_count > 10:  # 超过10个查询记录警告
                    logger.warning(f"{func_name} 执行了 {queries_count} 个数据库查询")
                
                return result
            return wrapper
        return decorator

class OptimizedAppService:
    """
    优化后的应用服务
    <AUTHOR>
    @date 2025-08-25 15:45:00
    """
    
    @staticmethod
    @cache.cache_result(timeout=300)  # 缓存5分钟
    def get_app_statistics():
        """获取应用统计（带缓存）"""
        return AppInfo.objects.active().aggregate(
            total_count=Count('id'),
            team_count=Count('team', distinct=True),
            avg_modules=Avg('modules__count')
        )
    
    @staticmethod
    def get_apps_with_pagination(page_size: int = 20, page: int = 1):
        """分页获取应用列表"""
        offset = (page - 1) * page_size
        
        # 只查询必要字段
        queryset = AppInfo.objects.select_related('team').only(
            'id', 'app_name', 'app_code', 'status', 'created_at',
            'team__name'
        ).active()[offset:offset + page_size]
        
        return list(queryset)
    
    @staticmethod
    def batch_create_apps(apps_data: List[dict]) -> List[AppInfo]:
        """批量创建应用"""
        apps = [AppInfo(**data) for data in apps_data]
        return AppInfo.objects.bulk_create(apps, batch_size=100)
```

### 2. 索引优化
```python
from django.db import models
from django.core.management.base import BaseCommand

class Command(BaseCommand):
    """
    数据库索引分析命令
    <AUTHOR>
    @date 2025-08-25 15:45:00
    """
    help = '分析数据库索引使用情况'
    
    def handle(self, *args, **options):
        from django.db import connection
        
        # 分析慢查询
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT query_time, lock_time, rows_sent, rows_examined, sql_text
                FROM mysql.slow_log
                WHERE start_time > DATE_SUB(NOW(), INTERVAL 1 DAY)
                ORDER BY query_time DESC
                LIMIT 20
            """)
            
            slow_queries = cursor.fetchall()
            
            for query in slow_queries:
                self.stdout.write(
                    f"Query Time: {query[0]}, SQL: {query[4][:100]}..."
                )
        
        # 分析索引使用情况
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT DISTINCT table_name, index_name, cardinality
                FROM information_schema.statistics
                WHERE table_schema = DATABASE()
                AND table_name LIKE 'app_mgt_%'
                ORDER BY table_name, cardinality DESC
            """)
            
            indexes = cursor.fetchall()
            
            for index in indexes:
                self.stdout.write(
                    f"Table: {index[0]}, Index: {index[1]}, Cardinality: {index[2]}"
                )
```

## 数据库安全规范

### 1. 敏感数据处理
```python
from django.contrib.auth.hashers import make_password, check_password
from cryptography.fernet import Fernet
import base64

class EncryptedFieldMixin:
    """
    加密字段混入类
    <AUTHOR>
    @date 2025-08-25 15:45:00
    """
    
    @staticmethod
    def encrypt_data(data: str, key: str) -> str:
        """加密数据"""
        f = Fernet(key.encode())
        encrypted_data = f.encrypt(data.encode())
        return base64.b64encode(encrypted_data).decode()
    
    @staticmethod
    def decrypt_data(encrypted_data: str, key: str) -> str:
        """解密数据"""
        f = Fernet(key.encode())
        decoded_data = base64.b64decode(encrypted_data.encode())
        decrypted_data = f.decrypt(decoded_data)
        return decrypted_data.decode()

class SecureAppConfig(models.Model):
    """
    安全应用配置
    <AUTHOR>
    @date 2025-08-25 15:45:00
    """
    app = models.ForeignKey(AppInfo, on_delete=models.CASCADE)
    config_key = models.CharField(max_length=100)
    config_value = models.TextField()  # 存储加密后的值
    is_encrypted = models.BooleanField(default=True)
    
    def set_value(self, value: str, encrypt: bool = True):
        """设置配置值"""
        if encrypt:
            self.config_value = EncryptedFieldMixin.encrypt_data(
                value, settings.SECRET_KEY
            )
            self.is_encrypted = True
        else:
            self.config_value = value
            self.is_encrypted = False
    
    def get_value(self) -> str:
        """获取配置值"""
        if self.is_encrypted:
            return EncryptedFieldMixin.decrypt_data(
                self.config_value, settings.SECRET_KEY
            )
        return self.config_value
```

### 2. 数据库连接安全
```python
# settings.py
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': os.getenv('DB_NAME', 'spider'),
        'USER': os.getenv('DB_USER', 'spider_user'),
        'PASSWORD': os.getenv('DB_PASSWORD', ''),  # 从环境变量获取
        'HOST': os.getenv('DB_HOST', 'localhost'),
        'PORT': os.getenv('DB_PORT', '3306'),
        'OPTIONS': {
            'charset': 'utf8mb4',
            'use_unicode': True,
            'autocommit': True,
            'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",
            'ssl': {
                'ca': '/path/to/ca-cert.pem',  # SSL证书
                'cert': '/path/to/client-cert.pem',
                'key': '/path/to/client-key.pem',
            }
        },
        'CONN_MAX_AGE': 300,  # 连接池最大存活时间
        'CONN_HEALTH_CHECKS': True,  # 连接健康检查
    }
}

# 数据库操作审计日志
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'db_audit': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': '/var/log/spider/db_audit.log',
            'maxBytes': 1024*1024*10,  # 10MB
            'backupCount': 5,
            'formatter': 'detailed',
        },
    },
    'loggers': {
        'django.db.backends': {
            'level': 'DEBUG',
            'handlers': ['db_audit'],
            'propagate': False,
        },
    },
}
```

## 数据备份和恢复

### 1. 数据备份脚本
```bash
#!/bin/bash
# 数据库备份脚本
# 作者：hongdong.xie
# 日期：2025-08-25 15:45:00

DB_NAME="spider"
DB_USER="backup_user"
DB_PASSWORD="${DB_PASSWORD}"
BACKUP_DIR="/data/backup/mysql"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p "${BACKUP_DIR}/${DATE}"

# 全量备份
mysqldump -u"${DB_USER}" -p"${DB_PASSWORD}" \
  --single-transaction \
  --routines \
  --triggers \
  --hex-blob \
  --compress \
  "${DB_NAME}" > "${BACKUP_DIR}/${DATE}/spider_full_${DATE}.sql"

# 压缩备份文件
gzip "${BACKUP_DIR}/${DATE}/spider_full_${DATE}.sql"

# 删除7天前的备份
find "${BACKUP_DIR}" -type d -mtime +7 -exec rm -rf {} \;

echo "数据库备份完成：${BACKUP_DIR}/${DATE}/spider_full_${DATE}.sql.gz"
```

### 2. 数据恢复验证
```python
from django.core.management.base import BaseCommand
from django.db import connection

class Command(BaseCommand):
    """
    数据一致性检查命令
    <AUTHOR>
    @date 2025-08-25 15:45:00
    """
    help = '检查数据一致性'
    
    def handle(self, *args, **options):
        self.check_foreign_key_consistency()
        self.check_data_integrity()
        self.check_index_consistency()
    
    def check_foreign_key_consistency(self):
        """检查外键一致性"""
        with connection.cursor() as cursor:
            # 检查应用和团队的关联
            cursor.execute("""
                SELECT COUNT(*) as count
                FROM app_mgt_app_info a
                LEFT JOIN team_mgt_team t ON a.team_id = t.id
                WHERE a.is_deleted = 0 AND t.id IS NULL
            """)
            
            orphan_count = cursor.fetchone()[0]
            if orphan_count > 0:
                self.stdout.write(
                    self.style.ERROR(f'发现 {orphan_count} 个孤立的应用记录')
                )
            else:
                self.stdout.write(
                    self.style.SUCCESS('外键一致性检查通过')
                )
    
    def check_data_integrity(self):
        """检查数据完整性"""
        # 检查必填字段
        from app_mgt.models import AppInfo
        
        invalid_apps = AppInfo.objects.filter(
            models.Q(app_name='') | 
            models.Q(app_code='') |
            models.Q(team__isnull=True)
        ).count()
        
        if invalid_apps > 0:
            self.stdout.write(
                self.style.ERROR(f'发现 {invalid_apps} 个数据不完整的应用记录')
            )
        else:
            self.stdout.write(
                self.style.SUCCESS('数据完整性检查通过')
            )
```

---

**注意：数据库操作必须严格遵循上述规范，确保数据安全性、一致性和高性能。**