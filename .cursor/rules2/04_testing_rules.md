# 测试规范

**文档创建时间：** 2025-08-25 15:55:00  
**作者：** hongdong.xie

## 适用场景
- 单元测试编写
- 集成测试设计
- API接口测试
- 前端组件测试
- 端到端测试

## 测试原则和策略

### 1. 测试金字塔原则
```
         E2E Tests (5%)
      Integration Tests (15%)
    Unit Tests (80%)
```

#### 测试类型分工
- **单元测试**：测试独立的函数、类、组件
- **集成测试**：测试模块间交互、数据库操作、外部API调用
- **端到端测试**：测试完整的用户场景和业务流程

### 2. 测试覆盖率要求
- **单元测试覆盖率**：≥ 70%
- **关键业务逻辑覆盖率**：≥ 90%
- **API接口覆盖率**：≥ 80%
- **前端组件覆盖率**：≥ 60%

## Django单元测试规范

### 1. 测试文件组织
```python
# 测试文件目录结构
app_mgt/
├── tests/
│   ├── __init__.py
│   ├── test_models.py      # 模型测试
│   ├── test_views.py       # 视图测试
│   ├── test_serializers.py # 序列化器测试
│   ├── test_services.py    # 服务层测试
│   └── test_utils.py       # 工具函数测试
├── fixtures/               # 测试数据
│   ├── test_apps.json
│   └── test_users.json
└── factories.py           # 测试数据工厂
```

### 2. 基础测试类
```python
from django.test import TestCase, TransactionTestCase
from django.contrib.auth.models import User
from rest_framework.test import APITestCase
from unittest.mock import Mock, patch
import factory

class BaseTestCase(TestCase):
    """
    基础测试类
    <AUTHOR>
    @date 2025-08-25 15:55:00
    """
    
    @classmethod
    def setUpTestData(cls):
        """类级别的测试数据设置（只执行一次）"""
        cls.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        cls.admin_user = User.objects.create_superuser(
            username='admin',
            email='<EMAIL>', 
            password='adminpass123'
        )
    
    def setUp(self):
        """每个测试方法前执行"""
        pass
    
    def tearDown(self):
        """每个测试方法后执行"""
        pass

class BaseAPITestCase(APITestCase):
    """
    API测试基类
    <AUTHOR>
    @date 2025-08-25 15:55:00
    """
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='apiuser',
            password='apipass123'
        )
        self.client.force_authenticate(user=self.user)
    
    def assertAPISuccess(self, response, status_code=200):
        """断言API成功响应"""
        self.assertEqual(response.status_code, status_code)
        self.assertEqual(response.data.get('code'), 0)
        self.assertIsNotNone(response.data.get('data'))
    
    def assertAPIError(self, response, status_code=400):
        """断言API错误响应"""
        self.assertEqual(response.status_code, status_code)
        self.assertNotEqual(response.data.get('code'), 0)
```

### 3. 模型测试示例
```python
from django.test import TestCase
from django.core.exceptions import ValidationError
from django.db.utils import IntegrityError
from app_mgt.models import AppInfo, Team

class AppInfoModelTest(TestCase):
    """
    应用信息模型测试
    <AUTHOR>  
    @date 2025-08-25 15:55:00
    """
    
    def setUp(self):
        """测试前置条件"""
        self.team = Team.objects.create(
            name='测试团队',
            description='测试团队描述'
        )
        self.app_data = {
            'app_name': '测试应用',
            'app_code': 'test-app',
            'description': '这是一个测试应用',
            'team': self.team
        }
    
    def test_create_app_success(self):
        """测试成功创建应用"""
        app = AppInfo.objects.create(**self.app_data)
        
        self.assertEqual(app.app_name, '测试应用')
        self.assertEqual(app.app_code, 'test-app')
        self.assertEqual(app.team, self.team)
        self.assertEqual(app.status, 'active')
        self.assertFalse(app.is_deleted)
        self.assertIsNotNone(app.created_at)
        self.assertIsNotNone(app.updated_at)
    
    def test_app_code_unique_constraint(self):
        """测试应用编码唯一约束"""
        AppInfo.objects.create(**self.app_data)
        
        with self.assertRaises(IntegrityError):
            AppInfo.objects.create(**self.app_data)
    
    def test_app_str_method(self):
        """测试字符串表示方法"""
        app = AppInfo.objects.create(**self.app_data)
        self.assertEqual(str(app), '测试应用(test-app)')
    
    def test_soft_delete(self):
        """测试软删除功能"""
        app = AppInfo.objects.create(**self.app_data)
        app.delete()
        
        self.assertTrue(app.is_deleted)
        # 验证软删除后查询不到
        self.assertEqual(
            AppInfo.objects.filter(is_deleted=False, id=app.id).count(), 
            0
        )
    
    def test_app_validation(self):
        """测试模型验证"""
        # 测试必填字段
        with self.assertRaises(ValidationError):
            app = AppInfo(app_name='', app_code='invalid-code')
            app.full_clean()
        
        # 测试字段长度限制
        with self.assertRaises(ValidationError):
            app = AppInfo(
                app_name='x' * 101,  # 超过最大长度
                app_code='valid-code'
            )
            app.full_clean()
```

### 4. 视图测试示例
```python
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APITestCase
from unittest.mock import patch, Mock

class AppViewSetTest(APITestCase):
    """
    应用视图集测试
    <AUTHOR>
    @date 2025-08-25 15:55:00
    """
    
    def setUp(self):
        """测试前置条件"""
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
        self.client.force_authenticate(user=self.user)
        
        self.team = Team.objects.create(name='测试团队')
        self.app = AppInfo.objects.create(
            app_name='测试应用',
            app_code='test-app',
            team=self.team,
            owner=self.user
        )
    
    def test_list_apps(self):
        """测试获取应用列表"""
        url = reverse('app-list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['code'], 0)
        self.assertEqual(len(response.data['data']['items']), 1)
        
        # 验证返回数据格式
        app_data = response.data['data']['items'][0]
        self.assertEqual(app_data['app_name'], '测试应用')
        self.assertEqual(app_data['app_code'], 'test-app')
    
    def test_create_app_success(self):
        """测试成功创建应用"""
        url = reverse('app-list')
        data = {
            'app_name': '新应用',
            'app_code': 'new-app',
            'description': '新应用描述',
            'team_id': self.team.id
        }
        
        response = self.client.post(url, data)
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['code'], 0)
        
        # 验证数据库中的数据
        app = AppInfo.objects.get(app_code='new-app')
        self.assertEqual(app.app_name, '新应用')
        self.assertEqual(app.team, self.team)
    
    def test_create_app_validation_error(self):
        """测试创建应用验证错误"""
        url = reverse('app-list')
        data = {
            'app_name': '',  # 空名称
            'app_code': 'invalid code',  # 无效编码
            'team_id': 999  # 不存在的团队
        }
        
        response = self.client.post(url, data)
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertNotEqual(response.data['code'], 0)
        self.assertIn('errors', response.data)
    
    def test_update_app_permission(self):
        """测试更新应用权限控制"""
        other_user = User.objects.create_user(
            username='otheruser',
            password='otherpass123'
        )
        self.client.force_authenticate(user=other_user)
        
        url = reverse('app-detail', kwargs={'pk': self.app.id})
        data = {'app_name': '修改后的应用名'}
        
        response = self.client.patch(url, data)
        
        # 应该返回权限不足错误
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
    
    def test_delete_app(self):
        """测试删除应用（软删除）"""
        url = reverse('app-detail', kwargs={'pk': self.app.id})
        response = self.client.delete(url)
        
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        
        # 验证软删除
        self.app.refresh_from_db()
        self.assertTrue(self.app.is_deleted)
    
    @patch('app_mgt.services.external_service.create_jenkins_job')
    def test_create_app_with_external_service(self, mock_jenkins):
        """测试创建应用时调用外部服务（Mock）"""
        mock_jenkins.return_value = {'job_id': 'test-job-123'}
        
        url = reverse('app-list')
        data = {
            'app_name': '需要Jenkins的应用',
            'app_code': 'jenkins-app',
            'team_id': self.team.id,
            'auto_create_jenkins': True
        }
        
        response = self.client.post(url, data)
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        mock_jenkins.assert_called_once()
```

### 5. 数据库事务测试
```python
from django.test import TransactionTestCase
from django.db import transaction, IntegrityError

class AppTransactionTest(TransactionTestCase):
    """
    应用事务测试
    <AUTHOR>
    @date 2025-08-25 15:55:00
    """
    
    def test_transaction_rollback(self):
        """测试事务回滚"""
        team = Team.objects.create(name='事务测试团队')
        
        try:
            with transaction.atomic():
                # 创建第一个应用
                app1 = AppInfo.objects.create(
                    app_name='应用1',
                    app_code='app1',
                    team=team
                )
                
                # 创建重复编码的应用（会失败）
                app2 = AppInfo.objects.create(
                    app_name='应用2',
                    app_code='app1',  # 重复编码
                    team=team
                )
        except IntegrityError:
            pass
        
        # 验证事务回滚，app1也不应该存在
        self.assertEqual(AppInfo.objects.filter(app_code='app1').count(), 0)
```

## 测试数据管理

### 1. Factory Boy数据工厂
```python
import factory
from django.contrib.auth.models import User
from app_mgt.models import AppInfo, Team

class UserFactory(factory.django.DjangoModelFactory):
    """
    用户工厂
    <AUTHOR>
    @date 2025-08-25 15:55:00
    """
    class Meta:
        model = User
        django_get_or_create = ('username',)
    
    username = factory.Sequence(lambda n: f'user{n}')
    email = factory.LazyAttribute(lambda obj: f'{obj.username}@example.com')
    first_name = factory.Faker('first_name')
    last_name = factory.Faker('last_name')
    is_active = True

class TeamFactory(factory.django.DjangoModelFactory):
    """
    团队工厂
    <AUTHOR>
    @date 2025-08-25 15:55:00
    """
    class Meta:
        model = Team
    
    name = factory.Sequence(lambda n: f'团队{n}')
    description = factory.Faker('text', max_nb_chars=200)
    leader = factory.SubFactory(UserFactory)

class AppInfoFactory(factory.django.DjangoModelFactory):
    """
    应用信息工厂
    <AUTHOR>
    @date 2025-08-25 15:55:00
    """
    class Meta:
        model = AppInfo
    
    app_name = factory.Sequence(lambda n: f'应用{n}')
    app_code = factory.Sequence(lambda n: f'app-{n}')
    description = factory.Faker('text', max_nb_chars=500)
    team = factory.SubFactory(TeamFactory)
    owner = factory.SubFactory(UserFactory)
    status = 'active'

# 使用工厂创建测试数据
class AppFactoryTest(TestCase):
    """
    使用工厂创建测试数据
    <AUTHOR>
    @date 2025-08-25 15:55:00
    """
    
    def test_create_app_with_factory(self):
        """使用工厂创建应用"""
        app = AppInfoFactory()
        
        self.assertIsNotNone(app.id)
        self.assertIsInstance(app.team, Team)
        self.assertIsInstance(app.owner, User)
    
    def test_create_multiple_apps(self):
        """批量创建应用"""
        apps = AppInfoFactory.create_batch(5)
        
        self.assertEqual(len(apps), 5)
        self.assertEqual(AppInfo.objects.count(), 5)
    
    def test_create_app_with_custom_data(self):
        """使用工厂创建自定义数据"""
        team = TeamFactory(name='特定团队')
        app = AppInfoFactory(
            app_name='特定应用',
            team=team
        )
        
        self.assertEqual(app.app_name, '特定应用')
        self.assertEqual(app.team.name, '特定团队')
```

### 2. 测试固件（Fixtures）
```python
# fixtures/test_apps.json
[
  {
    "model": "team_mgt.team",
    "pk": 1,
    "fields": {
      "name": "开发团队",
      "description": "负责核心开发的团队"
    }
  },
  {
    "model": "app_mgt.appinfo",
    "pk": 1,
    "fields": {
      "app_name": "测试应用",
      "app_code": "test-app",
      "description": "用于测试的应用",
      "team": 1,
      "status": "active",
      "is_deleted": false
    }
  }
]

class AppFixtureTest(TestCase):
    """
    使用固件的测试
    <AUTHOR>
    @date 2025-08-25 15:55:00
    """
    fixtures = ['test_apps.json']
    
    def test_fixture_data(self):
        """测试固件数据加载"""
        app = AppInfo.objects.get(app_code='test-app')
        self.assertEqual(app.app_name, '测试应用')
        self.assertEqual(app.team.name, '开发团队')
```

## Mock和补丁技术

### 1. Mock外部服务
```python
from unittest.mock import Mock, patch, MagicMock
import requests

class ExternalServiceTest(TestCase):
    """
    外部服务测试
    <AUTHOR>
    @date 2025-08-25 15:55:00
    """
    
    @patch('app_mgt.services.jenkins_service.requests.post')
    def test_create_jenkins_job(self, mock_post):
        """测试创建Jenkins任务"""
        # 模拟成功响应
        mock_response = Mock()
        mock_response.status_code = 201
        mock_response.json.return_value = {'job_id': 'test-job-123'}
        mock_post.return_value = mock_response
        
        from app_mgt.services import jenkins_service
        result = jenkins_service.create_job('test-app')
        
        self.assertEqual(result['job_id'], 'test-job-123')
        mock_post.assert_called_once()
    
    @patch('app_mgt.services.notification_service.send_email')
    def test_app_creation_notification(self, mock_send_email):
        """测试应用创建通知"""
        mock_send_email.return_value = True
        
        # 创建应用时应该发送通知
        app = AppInfoFactory()
        
        # 验证邮件发送被调用
        mock_send_email.assert_called_once_with(
            to=app.owner.email,
            subject=f'应用 {app.app_name} 创建成功',
            template='app_created.html',
            context={'app': app}
        )
    
    def test_mock_database_error(self):
        """测试数据库错误处理"""
        with patch('django.db.models.Model.save') as mock_save:
            mock_save.side_effect = IntegrityError("数据库错误")
            
            with self.assertRaises(IntegrityError):
                AppInfoFactory()
```

### 2. 时间相关测试
```python
from unittest.mock import patch
from django.utils import timezone
import datetime

class TimeBasedTest(TestCase):
    """
    时间相关测试
    <AUTHOR>
    @date 2025-08-25 15:55:00
    """
    
    def test_app_creation_time(self):
        """测试应用创建时间"""
        with patch('django.utils.timezone.now') as mock_now:
            fixed_time = datetime.datetime(2025, 8, 25, 15, 0, 0, tzinfo=timezone.utc)
            mock_now.return_value = fixed_time
            
            app = AppInfoFactory()
            
            self.assertEqual(app.created_at, fixed_time)
            self.assertEqual(app.updated_at, fixed_time)
    
    @patch('django.utils.timezone.now')
    def test_app_expiry_check(self, mock_now):
        """测试应用过期检查"""
        # 设置当前时间为未来时间
        future_time = timezone.now() + datetime.timedelta(days=365)
        mock_now.return_value = future_time
        
        app = AppInfoFactory()
        
        # 测试过期检查逻辑
        from app_mgt.services import app_service
        expired_apps = app_service.get_expired_apps()
        
        self.assertIn(app, expired_apps)
```

## 性能测试

### 1. 数据库查询性能测试
```python
from django.test import TestCase
from django.test.utils import override_settings
from django.db import connection
from django.conf import settings

class PerformanceTest(TestCase):
    """
    性能测试
    <AUTHOR>
    @date 2025-08-25 15:55:00
    """
    
    def setUp(self):
        """创建大量测试数据"""
        self.teams = TeamFactory.create_batch(10)
        self.apps = []
        for team in self.teams:
            self.apps.extend(
                AppInfoFactory.create_batch(50, team=team)
            )
    
    def test_app_list_query_performance(self):
        """测试应用列表查询性能"""
        with self.assertNumQueries(3):  # 期望执行3个查询
            from app_mgt.views import AppViewSet
            viewset = AppViewSet()
            queryset = viewset.get_queryset()
            
            # 触发查询
            apps = list(queryset[:20])
            for app in apps:
                _ = app.team.name  # 访问关联对象
    
    @override_settings(DEBUG=True)
    def test_slow_query_detection(self):
        """测试慢查询检测"""
        initial_queries = len(connection.queries)
        
        # 执行可能较慢的操作
        AppInfo.objects.filter(
            app_name__icontains='测试',
            team__name__icontains='团队'
        ).select_related('team').count()
        
        query_count = len(connection.queries) - initial_queries
        
        # 验证查询数量合理
        self.assertLessEqual(query_count, 1)
        
        # 检查查询时间（如果有记录的话）
        if connection.queries:
            last_query = connection.queries[-1]
            if 'time' in last_query:
                query_time = float(last_query['time'])
                self.assertLess(query_time, 0.1)  # 小于100ms
```

## 测试运行和报告

### 1. 测试配置
```python
# settings/test.py
from .base import *

# 测试数据库配置
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': ':memory:',  # 内存数据库，测试更快
    }
}

# 关闭缓存
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.dummy.DummyCache',
    }
}

# 简化密码验证
PASSWORD_HASHERS = [
    'django.contrib.auth.hashers.MD5PasswordHasher',
]

# 禁用日志
LOGGING_CONFIG = None

# 测试邮件后端
EMAIL_BACKEND = 'django.core.mail.backends.locmem.EmailBackend'
```

### 2. 测试命令
```bash
# 运行所有测试
python manage.py test

# 运行特定应用的测试
python manage.py test app_mgt

# 运行特定测试类
python manage.py test app_mgt.tests.test_models.AppInfoModelTest

# 运行特定测试方法
python manage.py test app_mgt.tests.test_models.AppInfoModelTest.test_create_app_success

# 生成覆盖率报告
coverage run --source='.' manage.py test
coverage html

# 并行运行测试
python manage.py test --parallel

# 保留测试数据库
python manage.py test --keepdb

# 详细输出
python manage.py test --verbosity=2
```

### 3. 持续集成测试脚本
```bash
#!/bin/bash
# CI测试脚本
# 作者：hongdong.xie
# 日期：2025-08-25 15:55:00

set -e

echo "开始运行测试套件..."

# 安装依赖
pip install -r requirements/test.txt

# 运行代码质量检查
echo "运行代码质量检查..."
flake8 .
pylint app_mgt/ --load-plugins=pylint_django

# 运行类型检查
echo "运行类型检查..."
mypy app_mgt/

# 运行安全检查
echo "运行安全检查..."
bandit -r app_mgt/

# 运行单元测试
echo "运行单元测试..."
coverage run --source='.' manage.py test --verbosity=2

# 生成覆盖率报告
echo "生成覆盖率报告..."
coverage report --fail-under=70
coverage html

# 运行集成测试
echo "运行集成测试..."
python manage.py test tests.integration --tag=integration

echo "所有测试通过！"
```

---

**注意：测试是代码质量的重要保障，必须严格遵循测试规范，确保代码的可靠性和可维护性。**