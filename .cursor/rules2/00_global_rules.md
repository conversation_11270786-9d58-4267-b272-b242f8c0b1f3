# PA平台全局开发规范

**文档创建时间：** 2025-08-25 15:30:00  
**作者：** hongdong.xie

## 核心原则

### 1. 代码质量原则
- 单个文件不超过1000行
- 严格遵循项目分层架构
- 所有代码必须通过静态检查和类型检查
- 禁止使用已废弃的API和不安全的编程实践

### 2. 注释和文档规范
- 类和方法必须有完整的注释
- 注释作者统一为：hongdong.xie
- 现有注释中的@author和@date值不要更新或覆盖
- 获取当前时间命令：`date "+%Y-%m-%d %H:%M:%S"`

### 3. 依赖管理原则
- 禁止使用BeanUtils.copyProperties进行对象属性拷贝
- VO/PO/DTO/BO等实体类使用@Setter/@Getter（不用@Data）
- 严格控制依赖版本，避免版本冲突

### 4. 安全规范
- 所有敏感信息必须通过配置文件管理
- 禁止在代码中硬编码密码、密钥等敏感信息
- 所有外部接口调用必须进行参数验证和错误处理

## 规范文件调度机制

根据开发场景和文件类型自动选择相应的规范文件：

1. **Django应用开发** → `django_app_rules.md`
2. **API接口开发** → `api_interface_rules.md`
3. **数据库操作** → `database_rules.md`
4. **测试相关** → `testing_rules.md`
5. **前端Vue开发** → `frontend_vue_rules.md`
6. **Python脚本开发** → `python_script_rules.md`
7. **Jenkins流水线** → `jenkins_pipeline_rules.md`

## 文件命名和目录规范

### 目录结构规范
```
.cursor/rules/
├── 00_global_rules.md           # 全局规范（本文件）
├── 01_django_app_rules.md       # Django应用层规范
├── 02_api_interface_rules.md    # API接口层规范
├── 03_database_rules.md         # 数据库层规范
├── 04_testing_rules.md          # 测试层规范
├── 05_frontend_vue_rules.md     # 前端Vue规范
├── 06_python_script_rules.md    # Python脚本规范
├── 07_jenkins_pipeline_rules.md # Jenkins流水线规范
└── modules/                     # 模块专用规范
    ├── app_mgt_rules.md         # 应用管理模块
    ├── env_mgt_rules.md         # 环境管理模块
    ├── iter_mgt_rules.md        # 迭代管理模块
    ├── pipeline_rules.md        # 流水线模块
    ├── publish_rules.md         # 发布模块
    └── task_mgt_rules.md        # 任务管理模块
```

### 文件命名规范
- Python模块文件：使用下划线命名法（snake_case）
- Vue组件文件：使用大驼峰命名法（PascalCase）
- 配置文件：使用小写加下划线
- SQL文件：按迭代版本号命名，如`迭代3_3_8/01-应用信息.sql`

## 代码检查和质量控制

### 必须通过的检查
1. **语法检查**：Python代码必须通过pylint检查
2. **类型检查**：所有Python代码必须有类型注解
3. **安全检查**：不能包含敏感信息泄露
4. **性能检查**：避免明显的性能问题

### 提交前检查清单
- [ ] 代码符合项目架构层次
- [ ] 通过所有单元测试
- [ ] 符合代码风格规范
- [ ] 更新了相关文档
- [ ] 没有遗留TODO或FIXME

## 异常处理规范

### 统一异常处理
```python
# 业务异常
class BusinessException(Exception):
    def __init__(self, message: str, error_code: str = None):
        super().__init__(message)
        self.error_code = error_code

# 系统异常
class SystemException(Exception):
    def __init__(self, message: str, original_exception: Exception = None):
        super().__init__(message)
        self.original_exception = original_exception
```

### 错误日志规范
```python
import logging

logger = logging.getLogger(__name__)

# 错误日志必须包含：时间、模块、错误类型、详细信息
logger.error(f"[{module_name}] {error_type}: {error_detail}", 
             exc_info=True)
```

## 性能优化规范

### 数据库查询优化
- 必须使用索引进行查询
- 避免N+1查询问题
- 大数据量查询必须分页
- 复杂查询优先使用原生SQL

### 缓存使用规范
- 热点数据必须使用缓存
- 缓存键命名要有明确业务含义
- 设置合理的过期时间
- 缓存更新策略要考虑数据一致性

## 监控和日志规范

### 关键业务指标监控
- 接口响应时间
- 数据库查询性能
- 业务操作成功率
- 系统资源使用情况

### 日志级别使用
- `DEBUG`：调试信息，仅开发环境
- `INFO`：关键业务流程记录
- `WARNING`：可能的问题，但不影响正常流程
- `ERROR`：错误信息，需要人工干预
- `CRITICAL`：严重错误，系统无法继续运行

---

**注意：此规范文件会根据项目发展持续更新，所有开发人员必须严格遵守。**