import datetime
import json
import threading

from rest_framework import viewsets
from rest_framework.response import Response

from app_mgt.app_compare_ser import get_can_automatic_archive_mobile_info
from app_mgt.app_mgt_ser import get_app_publish_info
from app_mgt.models import JenkinsAutomaticArchiveInfo
from env_mgt import env_info_ser
from iter_mgt.models import Branches, BranchIncludeSys
from public.send_email import SendMail
from spider.settings import logger, ApiResult
from task_mgt import zeus_ser
from task_mgt.external_service import ExternalService
from task_mgt.http_task import SaltTask, HttpTask


class WarException(Exception):
    def __init__(self, msg):
        self.msg = msg


class PackageNoneException(Exception):
    def __init__(self, msg):
        self.msg = msg


class MinionIdNoneException(Exception):
    def __init__(self, msg):
        self.msg = msg


class CmdRetrunException(Exception):
    def __init__(self, msg):
        self.msg = msg


class AppCompareApi(viewsets.ViewSet):

    @staticmethod
    def run_compare_cmd(salt_task, user, minion_id, cmd):
        """
        执行salt命令，返回执行结果
        :param salt_task:
        :param user:
        :param minion_id:
        :param cmd:
        :return:
        """
        compare_info_list = []
        status, ress = salt_task.salt_run(user, minion_id, cmd)
        if ress:
            logger.info("返回信息 {}".format(ress))
            for res in ress.split("\n"):
                if "\t" in res:
                    compare_info = res.split("\t")[0]
                else:
                    compare_info = res.split(" ")[0]
                if "du:" in compare_info:
                    logger.error("目录没找到 {}".format(res))
                    return False, res
                else:
                    compare_info_list.append(compare_info)
            return True, compare_info_list
        else:
            logger.error(ress)
            return False, ress

    @staticmethod
    def get_special_path():
        """
         获取特殊的对比路径字典
        :return:
        """
        special_path_dict = {}
        # for row in SpecialContrastPath.objects.filter().all():
        #     if row.module_name in special_path_dict:
        #         special_path_dict[row.module_name].append(row.special_path)
        #     else:
        #         special_path_dict[row.module_name] = [row.special_path]
        return special_path_dict

    @staticmethod
    def generate_cmd(package_type, module_name, package_name, deploy_path, package_full, special_path_dict):
        """
        生成需要执行的命令
        :param package_type: 包类型 jar, war
        :param module_name: 引用包
        :param package_name: 打出来的包名称
        :param deploy_path: 发布路径
        :param package_full: 是否全量包 1 全 0 不全
        :param special_path_dict: 特殊的路径字典 {"gateway-web":["/WEB-INF/lib","/WEB-INF/lib2"]}
        :return: str 需要执行的命令
        """

        if package_type == "war":
            # 特殊的对比路径
            if module_name in special_path_dict:
                cmd_list = []
                for special_path in special_path_dict[module_name]:
                    cmd_list.append("du -s -b {}{}".format(deploy_path, special_path))
                cmd = " && ".join(cmd_list)
            else:
                cmd = "du -s -b {}/WEB-INF/lib && du -s -b {}/WEB-INF/classes/com".format(deploy_path, deploy_path)
        elif package_type == "jar":
            if package_full == 1:
                if "*" not in package_name:
                    package_name = package_name.replace(".jar", "*.jar")
                cmd = """
                                   package_path=$(find {} -name "{}")
                                   if [ -z $package_path ];then
                                   package_path=$(find {} -name "{}*.jar")
                                   if [ -z $package_path ];then
                                   exit 1
                                   fi
                                   fi
                                   echo $(md5sum $package_path)   
                                   """.format(deploy_path, package_name, deploy_path, module_name)
            else:
                # 特殊的对比路径
                if module_name in special_path_dict:
                    cmd_list = []
                    for special_path in special_path_dict[module_name]:
                        cmd_list.append("du -s -b {}".format(special_path))
                    cmd = " && ".join(cmd_list)
                else:
                    cmd = """du -s -b {}""".format(deploy_path)
        else:
            return None
        return cmd

    # def recode_md5(self, node_bind_id, md5):
        # AppMd5Info.objects.update_or_create(defaults={'md5': md5, 'update_time': datetime.datetime.now()},
        #                                     node_ip_bind_id=node_bind_id)

    def automatic_archive(self, can_archive_info, user):
        pass

    def get_compare_info(self, user, ip_list, app_name):
        res_dict = {}
        special_path_dict = self.get_special_path()
        for row in get_app_publish_info(ip_list, app_name):
            module_name = row[0]
            node_ip = row[1]
            minion_id = row[2]
            suite_code = row[3]
            package_name = row[4]
            package_type = row[5]
            deploy_path = row[6]
            node_bind_id = row[7]
            package_full = row[8]
            if package_type not in ("jar", "war"):
                raise WarException("只支持jar,war类型包MD5对比，{}应用的类型为{}".format(app_name, package_type))
            # if package_name == "" or package_name is None:
            #     raise PackageNoneException("{}应用的包名称没有维护，请联系冯伟敏! 电话：18916598509".format(app_name))
            if minion_id is None:
                raise MinionIdNoneException("{}应用的minion_id没有维护，请联系冯伟敏! 电话：18916598509".format(app_name))
            if package_name == "" or package_name is None:
                raise PackageNoneException("{}应用的包名称没有维护，请联系冯伟敏! 电话：18916598509".format(app_name))

            # 没找到发布路径，尝试用规则匹配
            if deploy_path is None:
                if package_type == "jar":
                    deploy_path = "/data/app/{}/lib".format(row[0])
                else:
                    raise PackageNoneException(
                        "{}服务器上没找到{}应用的包{}，请联系冯伟敏! 电话：18916598509".format(node_ip, app_name, package_name))
            cmd = self.generate_cmd(package_type, module_name, package_name, deploy_path, package_full,
                                    special_path_dict)
            salt_task = SaltTask.instance(suite_code)
            status, res = self.run_compare_cmd(salt_task, user, minion_id, cmd)
            if status:

                res_dict[node_ip] = res
                # self.recode_md5(node_bind_id, res)
            else:
                logger.error(res)
                raise CmdRetrunException("{}节点，执行{}命令错误，报错为{}".format(node_ip, module_name, res))
        return res_dict

    @staticmethod
    def compile_res(base_ip, res_dict):
        """
        比较结果
        :param base_ip: 基础ip
        :param res_dict: 对比信息字典
        :return:  不一养的个数
        """
        diff_num = 0
        for row in res_dict:
            if res_dict[row] != res_dict[base_ip]:
                diff_num = diff_num + 1
        return diff_num

    def create(self, request):
        user = str(request.user)
        logger.info(request.data)
        ip_list = request.data["ip_list"]
        app_name = request.data["app_name"]
        try:
            res_dict = self.get_compare_info(user, ip_list, app_name)
            if not ip_list:
                return Response(
                    data=ApiResult.success_dict(msg="调用成功", data={"res_dict": res_dict, "diff_num": 0}))
            logger.info("比较结果 {}".format(res_dict))
            diff_num = self.compile_res(ip_list[0], res_dict)
            logger.info("差异数 {} ".format(diff_num))
            return Response(data=ApiResult.success_dict(msg="调用成功", data={"res_dict": res_dict, "diff_num": diff_num}))
        except Exception as e:
            return Response(data=ApiResult.failed_dict(msg=str(e)))


class JenkinsAutomaticArchiveApi(AppCompareApi):
    authentication_classes = []

    def get_all_automatic_archive_app(self, user, suite_list):
        # 获取全部满足自动归档的应用
        res_dict = {}
        error_info_list = []
        # special_path_dict = self.get_special_path()
        # # 1、新流水线服务开出的迭代分支 迭代处于 上线中
        # for row in get_all_meet_automatic_archive_info(suite_list):
        #     pipeline_id = row[0]
        #     module_name = row[1]
        #     proposer = row[2]
        #     suite_code = row[3]
        #     node_ip = row[4]
        #     minion_id = row[5]
        #     package_name = row[6]
        #     package_type = row[7]
        #     deploy_path = row[8]
        #     node_bind_id = row[9]
        #     package_full = row[10]
        #     # 2、每个应用 对应的节点（产线和灾备）都有发布操作
        #     deploy_info = OpsOperateHistory.objects.filter(pipeline_id=pipeline_id,
        #                                                    appName=module_name,
        #                                                    IP=node_ip,
        #                                                    operateType='deploy')
        #     if deploy_info:
        #         if minion_id is None:
        #             logger.error(row)
        #             error_info_list.append(node_ip + "：minion_id 不存在")
        #             continue
        #         cmd = self.generate_cmd(package_type, module_name, package_name, deploy_path, package_full,
        #                                 special_path_dict)
        #         if cmd is None:
        #             logger.info("{}应用为{}类型，不用对比".format(module_name, package_type))
        #             continue
        #         salt_task = SaltTask.instance(suite_code)
        #         status, res = self.run_compare_cmd(salt_task, user, minion_id, cmd)
        #         if status:
        #             if pipeline_id in res_dict:
        #                 if module_name in res_dict[pipeline_id]:
        #                     res_dict[pipeline_id][module_name][node_ip] = res
        #                 else:
        #                     res_dict[pipeline_id] = {module_name: {node_ip: res}}
        #             else:
        #                 res_dict[pipeline_id] = {module_name: {node_ip: res}}
        #             # self.recode_md5(node_bind_id, res)
        #         else:
        #             logger.error(res)
        #             error_info_list.append("{}节点，执行{}命令错误，报错为{}".format(node_ip, module_name, res))
        #     else:
        #         error_info_list.append('迭代{}下应用{}对应节点{}未有上线记录'.format(pipeline_id, module_name, node_ip))
        return res_dict, error_info_list

    def diff_info(self, diff_dict):
        """
        对比差异项
        :param diff_dict:
        :return:
        """
        mail_info = []
        can_archive_info = {}
        for pipeline_id in diff_dict:
            for app_name in diff_dict[pipeline_id]:
                diff_num = self.compile_res(sorted(diff_dict[pipeline_id][app_name].keys())[0],
                                            diff_dict[pipeline_id][app_name])
                if diff_num > 0:
                    logger.warning("{}迭代下{}应用有{}个差异项".format(pipeline_id, app_name, diff_num))
                    mail_info.append("{}迭代下{}应用有{}个差异项".format(pipeline_id, app_name, diff_num))
                    logger.warning(diff_dict[pipeline_id][app_name])
                    mail_info.append(json.dumps(diff_dict[pipeline_id][app_name]))
                else:
                    if pipeline_id in can_archive_info:
                        can_archive_info[pipeline_id].append(app_name)
                    else:
                        can_archive_info[pipeline_id] = [app_name]
        return mail_info, can_archive_info


    def get_all_automatic_archive_mobile_app(self, can_archive_info):
        """
        获取自动归档的app迭代信息
        1、迭代应用信息状态处于上线中
        2、 48小时未有最新的确认信息
        """
        for row in get_can_automatic_archive_mobile_info():
            pipeline_id = row[0]
            app_name = row[1]
            last_comfirm_time = row[2]
            now = datetime.datetime.now()
            if last_comfirm_time and (now - last_comfirm_time).days > 2:
                if pipeline_id in can_archive_info:
                    can_archive_info[pipeline_id].append(app_name)
                else:
                    can_archive_info[pipeline_id] = [app_name]

        return can_archive_info


    def check_config_consistent(self, pipeline_id, app_name_list):
        interface_name = "check_config_consistent"
        logger.info("自动归档发布配置分支参数{},{}".format(pipeline_id, app_name_list))
        br_info = Branches.objects.filter(pipeline_id=pipeline_id).first()
        join_zeus_app = zeus_ser.get_join_zeus_app(app_name_list, pipeline_id)
        env = 'prod'
        # 获取接入宙斯的应用
        if len(join_zeus_app) == 0:
            logger.info("{}没有接入宙斯或者不需要产线配置".format(app_name_list))
            return True
        logger.info("接入宙斯的应用为{}".format(join_zeus_app))
        with HttpTask() as http_task:
            suite_info = env_info_ser.get_suite_name(join_zeus_app, [env])
            if suite_info:
                for row in suite_info:
                    logger.info(row)
                    r_status, result = http_task.call_interface(interface_name,
                                                                {"iteration_number": br_info.br_name,
                                                                 "app_name": row[1],
                                                                 "tenant_id": row[0],
                                                                 })
                    if r_status == 'failure':
                        return False
                    if result["code"] == "error" or result["block"] == "true":
                        logger.error("调用结果{}".format(result["message"]))
                        return False
                    logger.info("调用结果{}".format(result))

            else:
                return False
            return True

    def archive_config(self, pipeline_id):
        interface_name = "archive_config"
        logger.info("归档配置分支参数{}".format(pipeline_id))
        # 获取接入宙斯的应用
        with HttpTask() as http_task:
            for row in zeus_ser.get_app_info(pipeline_id):
                logger.info(row)
                if row[2] == 1:
                    r_status, result = http_task.call_interface(interface_name,
                                                                {"iteration_number": row[0],
                                                                 "app_name": row[1]
                                                                 })
                    if r_status == 'failure':
                        logger.info(result)
                        return False
        return True

    def automatic_archive(self, can_archive_info, user):
        # 回合符合条件的迭代
        archive_info = []
        archive_error_info = []
        for pipeline_id in can_archive_info:
            # 第一次检测到发邮件提示并落库数据
            values = BranchIncludeSys.objects.filter(pipeline_id=pipeline_id).first()
            if JenkinsAutomaticArchiveInfo.objects.get(pipeline_id=pipeline_id):
                get = JenkinsAutomaticArchiveInfo.objects.get(pipeline_id=pipeline_id)
                get.update_time = datetime.datetime.now() + datetime.timedelta(hours=8)
                get.save()
                # 1、回合配置
                if self.check_config_consistent(pipeline_id, can_archive_info[pipeline_id]):
                    if self.archive_config(pipeline_id):
                        archive_info.append('迭代{}配置自动归档成功'.format(pipeline_id))
                    else:
                        archive_error_info.append('迭代{}配置自动归档失败，请自行前往配置平台手动归档'.format(pipeline_id))
                else:
                    archive_error_info.append('迭代{}配置自动归档失败，请自行前往配置平台手动归档'.format(pipeline_id))
                # 2、回合代码
                business_name = 'archive'
                params = {
                    'business_name': business_name,
                    'iteration_id': pipeline_id,
                }
                rt_code, sid = ExternalService(business_name, user, params).call_service()
                if rt_code == 0:
                    archive_info.append('迭代{}代码自动归档成功'.format(pipeline_id))
                else:
                    archive_error_info.append('迭代{}代码自动归档失败，请自行前往配置平台手动归档'.format(pipeline_id))
                send_mail = SendMail()
                send_mail.set_subject("迭代{}自动归档提醒".format(pipeline_id))
                mail_info_str = """
                          <!DOCTYPE html>
                              <html>
                                <body>
                                   {}
                                    <br>
                                     {}
                                </body>
                              </html>
                          """.format("<br>".join(archive_info),
                                     "<br>".join(archive_error_info))
                send_mail.set_content(mail_info_str)
                send_mail.set_to([values.proposer + '@howbuy.com', '<EMAIL>'])
                send_mail.set_cc('<EMAIL>')
                send_mail.send()
            else:
                time = datetime.datetime.now() + datetime.timedelta(hours=8)
                JenkinsAutomaticArchiveInfo.objects.create(pipeline_id=pipeline_id,
                                                           create_time=time,
                                                           update_time=time)
                send_mail = SendMail()
                send_mail.set_subject("迭代{}即将自动归档提醒".format(pipeline_id))
                mail_info_str = """
                                  <!DOCTYPE html>
                                      <html>
                                        <body>
                                          迭代{}即将在明日凌晨自动归档，如有疑问请联系冯伟敏！
                                        </body>
                                      </html>
                              """.format(pipeline_id)
                send_mail.set_content(mail_info_str)
                send_mail.set_to(values.proposer + '@howbuy.com')
                send_mail.set_cc('<EMAIL>')
                send_mail.send()

        return archive_info, archive_error_info

    def main(self, user, suite_list, mail_to):
        """
        自动归档
        :param user:
        :param suite_list:
        :param mail_to:
        :return:
        """
        # 1、筛选可自动归档的应用
        # 2、判断是提醒还是直接归档
        # 2.1、调用归档接口并发送归档完成邮件
        # 2.2、发送提醒邮件
        logger.info('开始获取自动归档的应用信息')
        res_dict, error_info = self.get_all_automatic_archive_app(user, suite_list)
        logger.info('自动归档信息如下：')
        logger.info(res_dict)
        # res_dict = {
        #     'tenpay_1.2.18': {
        #         'tenpay': {'*************': ['74477708', '625576'], '*************': ['74477708', '625576'],
        #                    '10.12.102.132': ['74477708', '625576'], '10.12.102.138': ['74473612', '625576']},
        #         'weixinpay': {'*************': ['74477708', '625576'], '*************': ['74477708', '625576'],
        #                       '10.12.102.132': ['74477708', '625576']}
        #     },
        #     'mojie_5.6.0': {
        #         'mring': {'*************': ['74477708', '625576'], '*************': ['74477708', '625576'],
        #                   '10.12.102.132': ['74477708', '625576']},
        #         'mring-coop': {'*************': ['74477708', '625576'], '*************': ['74477708', '625576'],
        #                        '10.12.102.132': ['74477708', '625576']},
        #         'mring-itest': {'*************': ['74477708', '625576'], '*************': ['74477708', '625576'],
        #                         '10.12.102.132': ['74477708', '625576'], '10.12.102.138': ['74473612', '625576']}
        #     }
        # }
        mail_info, can_archive_info = self.diff_info(res_dict)
        # 获取移动端app自动归档的信息 20211014 by fwm
        can_archive_info = self.get_all_automatic_archive_mobile_app(can_archive_info)
        logger.info('mail_info:{}'.format(mail_info))
        logger.info('can_archive_info:{}'.format(can_archive_info))

        if can_archive_info:
            self.automatic_archive(can_archive_info, user)
        send_mail = SendMail()
        send_mail.set_subject("迭代自动归档时对比信息信息")
        mail_info_str = """
          <!DOCTYPE html>
              <html>
                <body>
                   {}
                    <br>
                    <p>错误节点信息</p>
                     {}
                </body>
              </html>
          """.format("<br>".join(mail_info),
                     "<br>".join(error_info))
        send_mail.set_content(mail_info_str)
        send_mail.set_to(mail_to)
        send_mail.send()

    def create(self, request):
        user = "jenkins"
        suite_list = ['"prod"', '"bs-zb"']
        mail_to = ["<EMAIL>", "<EMAIL>"]
        # self.main(user, suite_list, mail_to)
        threading.Thread(target=self.main, args=(user, suite_list, mail_to), daemon=True).start()
        return Response(data=ApiResult.success_dict(msg="接口调用成功"))
