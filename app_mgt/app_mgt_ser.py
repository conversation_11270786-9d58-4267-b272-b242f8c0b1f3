# 应用管理 zt@2020-05-21
from django.db import connection
from spider.settings import logger


def get_app_module(*args, **kwargs):
    module_name = kwargs['module_name']

    search_sql = '''
        SELECT DISTINCT
            m.id, 
            m.module_name, 
            m.module_code, 
            m.module_status, 
            m.module_desc,
            i.team_name, 
            i.team_short_name,
            i.cmdb_team_name
        FROM app_mgt_app_module m
        INNER JOIN app_mgt_app_build b ON b.module_name = m.module_name
        AND m.need_online = 1 AND m.need_check = 1
        LEFT JOIN team_mgt_app_bind t ON t.app_id = m.app_id
        LEFT JOIN tool_mgt_team_info i ON t.team_id = i.id'''
    if module_name:
        search_sql += " and m.module_name = '" + module_name + "'"
    search_sql += " order by m.id desc"

    cursor = connection.cursor()
    cursor.execute(search_sql)
    return cursor


def get_test_publish_app(*args, **kwargs):
    test_template = kwargs['test_template']

    page_num = kwargs['page_num']
    page_size = kwargs['page_size']
    page_total = kwargs['page_total']

    if not page_num:
        page_num = 1
    if not page_size:
        page_size = 10
    if not page_total:
        page_total = 0

    start_idx = page_size * (page_num - 1)

    column_sql = '''
        ti.module_name, 
        ti.support_docker,
        IFNULL(ai.platform_type,0) as platform_type,
        IFNULL(ti.is_middleware,0) as is_middleware,
        tt.default_deploy
    '''

    table_sql = '''
        from app_mgt_template_info m
        inner join app_mgt_test_template tt on tt.template_id = m.id and tt.relation_is_active = 1
        inner join app_mgt_test_info ti on ti.id = tt.module_id and ti.module_status = 1
        inner join app_mgt_app_module module on ti.module_name = module.module_name
        inner join app_mgt_app_info ai on module.app_id = ai.id
        where m.template_is_active = 1
    '''

    where_sql = ""
    if test_template:
        where_sql += " and m.template_name = '" + test_template + "'"

    order_by_sql = " order by " \
                   "platform_type desc, is_middleware desc, support_docker desc, module_name asc, default_deploy desc"

    limit_sql = " limit {},{}".format(start_idx, page_size)

    cursor = connection.cursor()
    if start_idx == 0:
        count_sql = 'select count(1) ' + table_sql + where_sql
        cursor.execute(count_sql)
        page_total = cursor.fetchone()[0]

    page = {
        'num': page_num,
        'size': page_size,
        'total': page_total,
    }

    search_sql = 'select ' + column_sql + table_sql + \
                 where_sql + order_by_sql + limit_sql
    print(">>>> search_sql = " + search_sql)
    cursor.execute(search_sql)
    data_list = [dict(zip([col[0] for col in cursor.description], row))
                 for row in cursor.fetchall()]

    return page, data_list


def get_all_app_publish_info(suite_list):
    """
    获取环境套
    suite_list: .str 环境套
    return : .iter  迭代对象
    """
    cursor = connection.cursor()
    cursor.execute('''SELECT t.module_name,h.node_ip,h.minion_id,m.suite_code,f.package_name,t.deploy_path,t.id,
f.package_type,f.package_full FROM 
env_mgt_node_bind t LEFT JOIN app_mgt_app_build f ON t.module_name = f.module_name LEFT JOIN env_mgt_suite m 
ON t.suite_id=m.id LEFT JOIN env_mgt_node h ON h.id=t.node_id WHERE  f.package_name <> "" and h.node_status = 0
AND m.suite_code IN ("{}")  and h.minion_id is not null and t.deploy_path is not null'''.format('","'.join(suite_list)))

    return cursor.fetchall()


def get_app_publish_info(ip_list, module_name):
    """
    获取环境套
    ip_list: .str 节点
    app_list: .str 应用名
    return : .iter  迭代对象
    """
    cursor = connection.cursor()
    sql = '''SELECT t.module_name,h.node_ip,h.minion_id,m.suite_code,f.package_name,f.package_type,
t.deploy_path,t.id, f.package_full FROM 
env_mgt_node_bind t LEFT JOIN app_mgt_app_build f ON t.module_name = f.module_name LEFT JOIN env_mgt_suite m 
ON t.suite_id=m.id LEFT JOIN env_mgt_node h ON h.id=t.node_id WHERE  f.module_name = "{}" AND 
h.node_ip IN ("{}") '''.format(module_name, '","'.join(ip_list))
    logger.info(sql)
    cursor.execute(sql)

    return cursor.fetchall()


def get_app_info(*args, **kwargs):
    cursor = connection.cursor()
    module_name = kwargs['module_name']
    page_num = kwargs['page_num']
    page_size = kwargs['page_size']
    page_total = kwargs['page_total']

    column_sql = '''
                 DISTINCT
                 v.module_name,
                 v.jdkVersion AS app_jdk_version,
                 IFNULL(v.git_absolute_path,v.trunkPath) AS url_path,
                 IFNULL(v.git_url,v.svn_url) AS url,
                 IFNULL(v.git_path,v.svn_path) AS path,
                 v.gitRepo AS lib_repo,
                 v.appPort AS app_port,
                 v.containerName AS container_name,
                 v.deployPath AS deploy_path,
                 v.packageName AS package_name,
                 v.package_type AS app_type,
                 v.need_online,
                 v.need_check,
                 v.zeus_type,
                 v.git_url,
                 v.git_path,
                 v.svn_url,
                 v.svn_path,
                 v.platform_type,
                 v.createPath AS create_path,
                 v.team_name'''

    table_sql = '''
    FROM
 (SELECT 
  m.app_id,
  m.id,
  m.module_code AS resourceCode,
  m.module_name,
  a.git_url,
  a.git_path,
  IF(m.module_jdk_version IS NULL OR TRIM(m.module_jdk_version) = '',a.app_jdk_version, m.module_jdk_version) AS jdkVersion,
  b.package_name AS packageName,
  b.package_type,
  m.need_online,
  m.need_check,
  m.zeus_type,
  IFNULL(a.platform_type,0) AS platform_type,
  i.team_name,
  a.svn_path,
  a.svn_url,
  IFNULL(m.module_desc, a.app_cname) AS appCnName,
  IF(a.svn_url IS NULL, NULL, CONCAT(ss.svn_addr, su.svn_url, IFNULL(a.svn_path, ''), IFNULL(m.module_svn_path, CONCAT('/', m.module_name)))) AS trunkPath,
  IF(a.git_url IS NULL, NULL, CONCAT(a.git_url, a.git_path)) AS gitCodePath,
  IF(a.git_url IS NULL, NULL, CONCAT(gs.git_addr, a.git_url, a.git_path)) AS git_absolute_path,
  m.create_path AS createPath,
  m.lib_repo AS gitRepo,
  a.lib_location AS featureTeam,
  m.container_name AS containerName,
  m.app_port AS appPort,
  m.deploy_path AS deployPath
 FROM spider.app_mgt_app_module m
  INNER JOIN spider.app_mgt_app_info a ON a.id = m.app_id
  LEFT JOIN spider.app_mgt_app_team t ON t.app_id = m.app_id
  LEFT JOIN spider.tool_mgt_team_info i ON i.id = t.team_id
  INNER JOIN spider.app_mgt_app_build b ON b.app_id = m.app_id AND b.module_name = m.module_name
  LEFT JOIN spider.tool_mgt_git_url gu ON a.git_url = gu.git_url
  LEFT JOIN spider.tool_mgt_git_server gs ON gu.git_server_id = gs.id
  LEFT JOIN spider.tool_mgt_svn_url su ON a.svn_url = su.svn_name
  LEFT JOIN spider.tool_mgt_svn_server ss ON su.svn_server_id = ss.id
  WHERE 1=1 
 ORDER BY m.app_id, m.module_name
 )v'''
    where_sql = ""
    if module_name:
        where_sql += " where module_name = '" + module_name + "'"

    start_idx = page_size * (page_num - 1)
    limit_sql = " limit {},{}".format(start_idx, page_size)
    search_sql = 'select ' + column_sql + table_sql + where_sql + limit_sql
    print(">>>> search_sql = " + search_sql)
    if not page_num:
        page_num = 1
    if not page_size:
        page_size = 10
    if not page_total:
        page_total = 0

    page = {
        'num': page_num,
        'size': page_size,
        'total': page_total,
    }
    search_sql = 'select ' + column_sql + table_sql + where_sql + limit_sql
    print(search_sql)
    cursor.execute(search_sql)

    data_list = [dict(zip([col[0] for col in cursor.description], row))
                 for row in cursor.fetchall()]

    return page, data_list


def get_code_repo(module_name):
    sql = '''
        SELECT CONCAT(a.git_url,a.git_path) FROM app_mgt_app_info a LEFT JOIN app_mgt_app_module m ON m.app_id = a.id 
    WHERE m.module_name="{}"'''.format(module_name)
    cursor = connection.cursor()
    cursor.execute(sql)
    for row in cursor.fetchall():
        return row[0]
    raise IOError("没找到应用的源码仓库{}".format(module_name))


def get_app_info_for_mock():
    sql = '''
            select t.module_name, b.package_type, i.platform_type from app_mgt_app_module t
			INNER JOIN app_mgt_app_build b on t.module_name = b.module_name
			INNER JOIN app_mgt_app_info i on t.app_id = i.id
			where t.need_online = '1' and i.third_party_middleware = '0' and t.need_check = '1';
        '''
    cursor = connection.cursor()
    cursor.execute(sql)
    return cursor


def get_java_app_info(batch_publish=None):
    sql = '''
          select DISTINCT m.module_name
          from app_mgt_app_module m
          left join app_mgt_app_build b on m.app_id = b.app_id
          left join app_mgt_app_info i on m.app_id = i.id
          where b.package_type in ('war', 'jar', 'tar')
            and m.need_online = '1'
            and m.need_check = '1'
            and i.third_party_middleware = '0'
            and i.platform_type = '1'
          '''
    if batch_publish:
        sql += " AND m.jenkins_batch_publish = 1"
    cursor = connection.cursor()
    cursor.execute(sql)
    java_app_list = []
    for row in cursor.fetchall():
        java_app_list.append(row[0])

    return java_app_list


def get_app_infos():
    """
    """
    cursor = connection.cursor()
    cursor.execute('''						
	SELECT DISTINCT b.module_name, t.team_id, a.third_party_middleware ,team.team_alias
                    FROM env_mgt_node_bind b
                    INNER JOIN app_mgt_app_module m ON b.module_name = m.module_name 
                    INNER JOIN app_mgt_app_info a ON m.app_id = a.id
                    INNER JOIN team_mgt_app_bind t ON t.app_id = a.id
                    INNER JOIN app_mgt_app_build ab ON ab.module_name = m.module_name
                    INNER JOIN tool_mgt_team_info team ON team.id = t.team_id
                    WHERE  ab.package_type NOT IN('android', 'ios', 'dist') and team_id not in (9,10,12) order by t.team_id,b.module_name
                    ''')
    return cursor.fetchall()


def get_node_app_infos():
    """
    """
    cursor = connection.cursor()
    cursor.execute('''						
	 select 
            m.node_ip, 
            m.node_status, 
            b.module_name,
            b.node_port,
            b.deploy_type,
            s.suite_code
        from env_mgt_node_bind b
        left join env_mgt_node m on m.id = b.node_id
        left join env_mgt_container c on c.container_code = b.node_docker
        left join env_mgt_deploy_group g on b.deploy_group = g.id
        left join env_mgt_suite s on b.suite_id = s.id
        left join env_mgt_region r on s.region_id = r.id
        where 1=1
     and  (s.suite_code like '%prod%' or s.suite_code like '%zb%') order by b.module_name, g.deploy_group_code, m.node_ip
	''')
    return cursor.fetchall()


def get_app_suite_info(module_name, region_group):
    """
    适用移动端的灰度和产线申请，绑了虚拟节点id的情况，所以用了left join
    """
    cursor = connection.cursor()
    cursor.execute('''						
                    SELECT DISTINCT
                        b.module_name,
                        s.suite_code ,
                        s.suite_name,
                        GROUP_CONCAT(m.node_ip) AS node_ip_list
                    FROM
                        env_mgt_node_bind b
                        LEFT JOIN env_mgt_node m ON m.id = b.node_id AND m.node_status = 0
                        LEFT JOIN env_mgt_suite s ON b.suite_id = s.id and s.suite_is_active = 1
                        LEFT JOIN env_mgt_region r ON s.region_id = r.id 
                    WHERE
                        b.module_name = "{}" 
                        AND r.region_group = '{}'
                    GROUP BY b.module_name,
                        s.suite_code ,
                        s.suite_name
                    '''.format(module_name, region_group))
    results = []
    for row in cursor.fetchall():
        results.append({"module_name": row[0], "suite_code": row[1],
                        "suite_name": row[2], "node_ip_list": row[3]})
    return results


def get_app_suite_info_node(module_name, region_group):
    """获取环境及节点（必须是真实节点）"""
    cursor = connection.cursor()
    cursor.execute('''						
                    SELECT DISTINCT
                        b.module_name,
                        s.suite_code ,
                        s.suite_name,
                        GROUP_CONCAT(m.node_ip) AS node_ip_list
                    FROM
                        env_mgt_node_bind b
                        INNER JOIN env_mgt_node m ON m.id = b.node_id AND m.node_status = 0
                        LEFT JOIN env_mgt_suite s ON b.suite_id = s.id and s.suite_is_active = 1
                        LEFT JOIN env_mgt_region r ON s.region_id = r.id 
                    WHERE
                        b.module_name = "{}" 
                        AND r.region_group = '{}'
                    GROUP BY b.module_name,
                        s.suite_code ,
                        s.suite_name
                    '''.format(module_name, region_group))
    results = []
    for row in cursor.fetchall():
        results.append({"module_name": row[0], "suite_code": row[1],
                        "suite_name": row[2], "node_ip_list": row[3]})
    return results


def get_app_prod_suite_info(module_name):
    """
    """

    return get_app_suite_info_node(module_name, "prod")


def get_app_hd_suite_info(module_name):
    """
    """

    return get_app_suite_info(module_name, "hd")


def get_pa_app_infos():
    """
    """
    cursor = connection.cursor()
    cursor.execute('''						
	SELECT DISTINCT b.module_name, t.team_id, a.third_party_middleware ,team.team_alias
                    FROM env_mgt_node_bind b
                    INNER JOIN app_mgt_app_module m ON b.module_name = m.module_name 
                    INNER JOIN app_mgt_app_info a ON m.app_id = a.id
                    INNER JOIN app_mgt_app_team t ON t.app_id = a.id
                    INNER JOIN app_mgt_app_build ab ON ab.module_name = m.module_name
                    INNER JOIN tool_mgt_team_info team ON team.id = t.team_id
                    WHERE  ab.package_type NOT IN('android', 'ios', 'dist') and team_id  in (9) order by t.team_id,b.module_name
                    ''')
    return cursor.fetchall()


def get_3_app_infos():
    """
    """
    cursor = connection.cursor()
    cursor.execute('''						
	SELECT DISTINCT b.module_name, t.team_id, a.third_party_middleware ,team.team_alias
                    FROM env_mgt_node_bind b
                    INNER JOIN app_mgt_app_module m ON b.module_name = m.module_name 
                    INNER JOIN app_mgt_app_info a ON m.app_id = a.id
                    INNER JOIN app_mgt_app_team t ON t.app_id = a.id
                    INNER JOIN app_mgt_app_build ab ON ab.module_name = m.module_name
                    INNER JOIN tool_mgt_team_info team ON team.id = t.team_id
                    WHERE  ab.package_type NOT IN('android', 'ios', 'dist') and team_id  in (10,12) order by t.team_id,b.module_name
                    ''')
    return cursor.fetchall()


def get_valid_app_info():
    sql = '''
            select distinct t.module_name, b.package_type, i.platform_type from app_mgt_app_module t
			INNER JOIN app_mgt_app_build b on t.module_name = b.module_name
			INNER JOIN app_mgt_app_info i on t.app_id = i.id
			where t.need_online = '1' and i.third_party_middleware = '0' and t.need_check = '1' 
			and i.app_status = '1' and i.platform_type = '1' 

            UNION

            select distinct bd.module_name, b.package_type, i.platform_type from app_mgt_app_module t
			INNER JOIN app_mgt_app_build b on t.module_name = b.module_name
			INNER JOIN app_mgt_app_info i on t.app_id = i.id
			INNER JOIN env_mgt_node_bind bd on t.module_name = bd.module_name
			INNER JOIN env_mgt_suite s on bd.suite_id = s.id
			INNER JOIN env_mgt_region r on s.region_id = r.id
			where t.need_online = '1' and i.third_party_middleware = '0' and t.need_check = '0' 
			and i.app_status = '1'  and i.platform_type = '1' and r.region_group = 'prod'
        '''
    cursor = connection.cursor()
    cursor.execute(sql)
    return cursor


def get_ready_to_delete_interface_id(module_name, branch_name):
    sql = '''
            select id from app_mgt_interface_info t 
            where t.module_name = '{module_name}' and t.branch_name = '{branch_name}' and t.update_time is not null 
            and t.update_time < (select MAX(update_time) from app_mgt_interface_info 
            where module_name = '{module_name}' and branch_name = '{branch_name}')
        '''.format(module_name=module_name, branch_name=branch_name)
    cursor = connection.cursor()
    cursor.execute(sql)
    id_list = []
    for row in cursor.fetchall():
        id_list.append(row[0])
    return id_list

def get_had_batch_publish_app_list(module_name, suite_code, team_name, department_name, page_num, page_size, restart_app_list):
    start_idx = page_size * (page_num - 1)
    limit_sql = " limit {},{}".format(start_idx, page_size)
    replace_sql = " e.region_group = 'prod' "
    if module_name:
        replace_sql += " and am.module_name = '{}' ".format(module_name)
    if suite_code:
        replace_sql += " and d.suite_code = '{}' ".format(suite_code)
    if team_name:
        replace_sql += " and ti.team_name = '{}' ".format(team_name)
    if department_name:
        replace_sql += " and ti2.team_name = '{}' ".format(department_name)
    column_sql = '''
            vv.module_name,
            vv.suite_code,
            vv.node_ips,
            vv.team_name,
            vv.department_name,
            ii.br_name 
    '''
    count_sql = '''
        count(DISTINCT node_ips) 
    '''

    sql = '''
        FROM task_mgt_deploy_result dr 
        INNER JOIN (
            SELECT app_name, suite_name, MAX(action_id) as max_action_id
            FROM task_mgt_deploy_result 
            WHERE op_type = 'jenkins_batch_publish'
            GROUP BY app_name, suite_name
        ) max_dr ON dr.app_name = max_dr.app_name 
            AND dr.suite_name = max_dr.suite_name 
            AND dr.action_id = max_dr.max_action_id
            AND dr.op_type = 'jenkins_batch_publish'
        INNER JOIN (
            SELECT DISTINCT
                am.module_name,
                d.suite_code,
                GROUP_CONCAT(f.node_ip) AS node_ips,
                ti.team_name,
                ti2.team_name as department_name
            FROM 
                app_mgt_app_info ai 
            LEFT JOIN app_mgt_app_module am ON ai.id = am.app_id AND am.zeus_type = 1
            LEFT JOIN team_mgt_app_bind ab ON ai.id = ab.app_id
            LEFT JOIN tool_mgt_team_info ti ON ab.team_id = ti.id
            LEFT JOIN tool_mgt_team_info ti2 ON ti.parent_id = ti2.id
            LEFT JOIN env_mgt_node_bind c ON am.module_name = c.module_name AND c.enable_bind = 1
            INNER JOIN env_mgt_node f ON f.id = c.node_id AND f.node_status = 0
            LEFT JOIN env_mgt_suite d ON c.suite_id = d.id  AND d.suite_is_active = 1
            LEFT JOIN env_mgt_region e ON d.region_id = e.id AND e.region_is_active = 1
            WHERE am.jenkins_batch_publish = 1
                 and {replace_sql}
                AND (c.is_cold_standby_node is null or c.is_cold_standby_node  = 0)
                GROUP BY am.module_name, d.suite_code
        ) vv ON dr.app_name = vv.module_name AND dr.suite_name = vv.suite_code
        LEFT JOIN iter_mgt_iter_info ii ON dr.iteration_id = ii.pipeline_id
        WHERE vv.node_ips is not null
    '''.format(replace_sql=replace_sql)


    cursor = connection.cursor()
    count_sql =  'SELECT '+ count_sql + sql
    search_sql = 'SELECT DISTINCT ' + column_sql + sql + limit_sql
    logger.info(count_sql)
    cursor.execute(count_sql)
    page_total = cursor.fetchone()[0]

    page = {
        "total": page_total if page_total is not None else 0,
        "page_num": page_num,
        "page_size": page_size,
    }

    logger.info(search_sql)
    cursor = connection.cursor()
    cursor.execute(search_sql)
    app_list = []
    for row in cursor.fetchall():
        node_ips = row[2]
        str_list = node_ips.split(",")
        ip_set = set(str_list)
        status = 0
        if restart_app_list:
            restart_set = set((app.module_name, app.suite_code) for app in restart_app_list)
            if (row[0], row[1]) in restart_set:
                status = 1
        app_list.append({"module_name": row[0], "suite_code": row[1], "node_ips": ip_set, "team_name": row[3],
                         "department_name": row[4], "branch_name": row[5], "status": status})
    return page, app_list


def get_base_branch_name(module_name):
    sql = '''
            select  DISTINCT t.module_name, t.branch_name, max(t.create_time) from app_mgt_interface_param_info t 
            where t.module_name = "{}"
            GROUP BY t.module_name;
        '''.format(module_name)
    cursor = connection.cursor()
    cursor.execute(sql)
    branch_name = ""
    for row in cursor.fetchall():
        branch_name = row[1]
    return branch_name


def get_archive_branch_name_by_app(module_name):
    sql = '''
        SELECT
        i.appName,         
            m.br_name      
        FROM iter_mgt_iter_info m
        LEFT JOIN iter_mgt_iter_app_info i ON i.pipeline_id = m.pipeline_id
        LEFT JOIN(
            SELECT
            i.appName, MAX(m.br_end_date) AS max_br_end_date
            FROM iter_mgt_iter_info m
            INNER JOIN iter_mgt_iter_app_info i ON i.pipeline_id = m.pipeline_id
            WHERE m.br_status = 'close' and i.appName = '{module_name}'
            GROUP BY i.appName
        )v ON v.appName = i.appName AND v.max_br_end_date = m.br_end_date
        WHERE m.br_end_date IS NOT NULL AND    
        m.br_status = 'close' AND v.max_br_end_date IS NOT NULL and i.appName = '{module_name}';
        '''.format(module_name=module_name)
    cursor = connection.cursor()
    cursor.execute(sql)
    branch_name = ""
    for row in cursor.fetchall():
        branch_name = row[1]
    return branch_name


def bak_api_from_api_ids(api_ids):
    api_ids = ','.join(api_ids)
    sql = '''
        insert into app_mgt_apidoc_del_his select * from app_mgt_apidoc_info where id in ({api_ids})
        '''.format(api_ids=api_ids)
    cursor = connection.cursor()
    cursor.execute(sql)


def bak_api_param_from_api_ids(api_ids):
    api_ids = ','.join(api_ids)
    sql = '''
        insert into app_mgt_apidoc_param_del_his select * from app_mgt_apidoc_param_info where api_id in ({api_ids})
        '''.format(api_ids=api_ids)
    cursor = connection.cursor()
    cursor.execute(sql)


def del_api_and_param_from_api_ids(api_ids):
    api_ids = ','.join(api_ids)
    sql = '''
        delete from app_mgt_apidoc_info where id in ({api_ids})
        '''.format(api_ids=api_ids)

    cursor = connection.cursor()
    cursor.execute(sql)
    sql = '''
        delete from app_mgt_apidoc_param_info where api_id in ({api_ids})
        '''.format(api_ids=api_ids)
    cursor.execute(sql)


def get_archive_branch_name_by_app_before_date(module_name, before_date):
    sql = '''
        SELECT
        i.appName,         
            m.br_name      
        FROM iter_mgt_iter_info m
        LEFT JOIN iter_mgt_iter_app_info i ON i.pipeline_id = m.pipeline_id
        LEFT JOIN(
            SELECT
            i.appName, MAX(m.br_end_date) AS max_br_end_date
            FROM iter_mgt_iter_info m
            INNER JOIN  app_mgt_interface_info api on m.br_name=api.branch_name and api.module_name='{module_name}'
            INNER JOIN iter_mgt_iter_app_info i ON i.pipeline_id = m.pipeline_id
            WHERE m.br_status = 'close' and i.appName = '{module_name}' and m.br_end_date < '{before_date}' and m.br_name not like '%pa-sql%' and  m.br_name not like '%basic-features%'
            GROUP BY i.appName
        )v ON v.appName = i.appName AND v.max_br_end_date = m.br_end_date
        WHERE m.br_end_date IS NOT NULL AND    
        m.br_status = 'close' AND v.max_br_end_date IS NOT NULL and i.appName = '{module_name}';
        '''.format(module_name=module_name, before_date=before_date)
    cursor = connection.cursor()
    cursor.execute(sql)
    branch_name = ""
    for row in cursor.fetchall():
        branch_name = row[1]
    return branch_name


def batch_insert_interface_info(module_name, branch_name, archive_branch_name, create_user):
    sql = '''
            insert into app_mgt_interface_info (module_name, branch_name, interface_path, interface_method, interface_type, status, create_version, create_user, create_time)
            select module_name, '{branch_name}', interface_path, interface_method, interface_type, status, branch_name, '{create_user}', now() from app_mgt_interface_info a
            where a.module_name = '{module_name}' and a.branch_name = '{archive_branch_name}';
        '''.format(module_name=module_name, branch_name=branch_name, archive_branch_name=archive_branch_name,
                   create_user=create_user)
    cursor = connection.cursor()
    cursor.execute(sql)


def batch_insert_interface_info_from_temp(module_name, branch_name, interface_type):
    sql = '''
            insert into app_mgt_interface_info 
            (module_name, branch_name, interface_name, interface_path, interface_method, interface_type, content_type, encryption, request_params, response_params, defines_params, status, create_version, create_user, create_time, update_user, update_time)
            select 
            module_name, branch_name, interface_name, interface_path, interface_method, interface_type, content_type, encryption, request_params, response_params, defines_params, status, create_version, create_user, create_time, update_user, update_time 
            from app_mgt_interface_info_temp a
            where a.module_name = '{module_name}' and a.branch_name = '{branch_name}' and a.interface_type = '{interface_type}' and a.status = 1;
        '''.format(module_name=module_name, branch_name=branch_name, interface_type=interface_type)
    cursor = connection.cursor()
    cursor.execute(sql)


def get_app_interface_info(module_name, branch_name, interface_path=None, interface_method=None, interface_type=None):
    sql = '''
            SELECT b.module_name, b.branch_name, b.interface_name, b.interface_name_dev, b.interface_path, b.interface_method, b.interface_type, b.content_type, b.encryption,
            b.request_params, b.response_params, b.defines_params, b.status, b.create_version, b.create_user, b.create_time, b.update_user, b.update_time
            FROM app_mgt_interface_info b
            LEFT JOIN app_mgt_interface_and_api_exclude ex on b.interface_path = ex.interface_path
            WHERE b.module_name = '{module_name}'
            AND b.branch_name = '{branch_name}'
            AND  b.status = 1
            AND ex.interface_path IS NULL
          '''.format(module_name=module_name,
                     branch_name=branch_name)
    if interface_path:
        sql += " AND b.interface_path = '{}'".format(interface_path)
    if interface_method:
        sql += " AND b.interface_method = '{}'".format(interface_method)
    if interface_type:
        sql += " AND b.interface_type='{}'".format(interface_type)

    logger.info(sql)
    cursor = connection.cursor()
    cursor.execute(sql)
    results = []
    for row in cursor.fetchall():
        results.append(
            {"module_name": row[0], "branch_name": row[1], "interface_name": row[2], "interface_name_dev": row[3],
             "interface_path": row[4], "interface_method": row[5], "interface_type": row[6],
             "content_type": row[7], "encryption": row[8], "request_params": row[9], "response_params": row[10],
             "defines_params": row[11], "status": row[12], "create_version": row[13], "create_user": row[14], "create_time": row[15], "update_user": row[16], "update_time": row[17]})

    return results


def get_app_encode(module_name=None, suite_code=None):
    sql = '''
            SELECT DISTINCT b.module_name AS appName, s.suite_code AS suiteCode, IFNULL(IFNULL(m.module_encode, i.app_encode), 'UTF-8') AS encodeFormat 
            FROM env_mgt_node_bind b
            LEFT JOIN app_mgt_app_module m ON b.module_name = m.module_name
            LEFT JOIN app_mgt_app_build ab ON b.module_name = ab.module_name
            LEFT JOIN app_mgt_app_info i ON m.app_id = i.id
            LEFT JOIN env_mgt_suite s ON b.suite_id = s.id
            LEFT JOIN env_mgt_region r ON s.region_id = r.id
            WHERE ab.package_type IN ('jar', 'war', 'tar') 
            AND m.need_online = '1' 
            AND i.third_party_middleware = 0 
            AND r.region_group = 'test' 
            AND s.suite_is_active = '1' 
          '''

    if suite_code:
        sql += " AND s.suite_code = '{}'".format(suite_code)

    if module_name:
        sql += " AND b.module_name='{}'".format(module_name)

    cursor = connection.cursor()
    cursor.execute(sql)

    return cursor


def get_pepole_team_info(team_name):
    team_pepole_list = []
    if team_name:
        search_condition = "WHERE mti.team_alias = '{}'".format(team_name)
    else:
        search_condition = 'WHERE mtm.bind_is_active = 1'
    sql = '''
    SELECT
        DISTINCT CONCAT (ugm.last_name,ugm.first_name) AS cn_name, ugm.username, mti.team_alias, mti.team_desc, mti.main_skill
    FROM
        spider.team_mgt_user_bind mtm
    LEFT OUTER JOIN
        spider.tool_mgt_team_info mti ON mti.id = mtm.team_id
    LEFT JOIN `auth_user` ugm ON ugm.username = mtm.user_name  {}
    '''.format(search_condition)
    cursor = connection.cursor()
    cursor.execute(sql)
    logger.info(sql)
    result = cursor.fetchall()
    for item in result:
        if item[1]:
            team_pepole_list.append(
                {'cn_name': item[0], 'username': item[1], 'team_alias': item[2]})
    return team_pepole_list


def get_small_team_info():
    small_team_list = []
    sql = '''
        SELECT cn.username, cn.cn_name, ub.team_name FROM (
        (
        SELECT team_owner, team_name FROM tool_mgt_team_info 
        WHERE team_level = 2 AND team_owner IS NOT NULL
        ) as ub
        LEFT outer JOIN (
        select distinct * from
                    (
                    select git.username,
                    case when spe.cn_name is not null then spe.cn_name else git.cn_name end as cn_name
                    from
                    (
                    SELECT
                        username, max(cn_name) as cn_name
                    FROM
                        spider.user_gitlab_members
                    group by username
                    ) git
                    left outer join
                    spider.base_cn_name_specific spe
                    on git.username = spe.username
                    union all
                    select * from spider.base_cn_name_specific spe
                    ) a
        ) cn on ub.team_owner = cn.username
        )where username is not null
    '''
    logger.info(sql)
    cursor = connection.cursor()
    cursor.execute(sql)
    result = cursor.fetchall()
    for item in result:
        small_team_list.append(
            {'username': item[0], 'cn_name': item[1], 'team_name': item[2]})
    return small_team_list


def get_pepole_team_lead_info(team_name):
    team_lead_list = []
    if team_name:
        search_condition = "WHERE mti.team_alias = '{}'".format(team_name)
    else:
        search_condition = ''
    sql = '''
        SELECT
            DISTINCT CONCAT (ugm.last_name,ugm.first_name) AS cn_name, ugm.username, mti.team_alias, mti.team_desc, mti.main_skill
        FROM spider.tool_mgt_team_info mti 
        LEFT JOIN `auth_user` ugm ON ugm.username = mti.team_owner {}
        '''.format(search_condition)
    cursor = connection.cursor()
    cursor.execute(sql)
    logger.info(sql)
    result = cursor.fetchall()
    for item in result:
        if item[1]:
            team_lead_list.append(
                {'cn_name': item[0], 'username': item[1], 'team_alias': item[2]})
    return team_lead_list


def get_module_team_info(team_name):
    module_team_list = []
    if team_name:
        search_condition = "AND ti.team_alias = '{}'".format(team_name)
    else:
        search_condition = ''
    sql = '''
    SELECT t.module_name,ti.team_alias FROM `app_mgt_app_module` t 
    LEFT JOIN `team_mgt_app_bind` ta ON ta.app_id = t.app_id
    LEFT JOIN tool_mgt_team_info ti ON ti.id = ta.team_id  WHERE t.need_online = 1 AND t.need_check = 1 {}
    '''.format(search_condition)
    cursor = connection.cursor()
    cursor.execute(sql)
    logger.info(sql)
    result = cursor.fetchall()
    for item in result:
        module_team_list.append({'module_name': item[0], 'team_alias': item[1]})
    return module_team_list


def get_module_repo_by_module_name(pipeline_id, module_name):
    sql = '''
    SELECT CONCAT(s.git_url,s.git_path) AS git_path, t.appName
                  FROM iter_mgt_iter_app_info t
                  LEFT JOIN app_mgt_app_module f ON f.module_name=t.appName 
                  LEFT JOIN app_mgt_app_info s ON f.app_id =s.id 
                  WHERE t.pipeline_id="{}" 
                  AND CONCAT(s.git_url,s.git_path) IS NOT NULL AND f.module_name = "{}"
    '''.format(pipeline_id, module_name)
    cursor = connection.cursor()
    cursor.execute(sql)
    logger.info(sql)
    result = cursor.fetchall()
    return result


def get_app_info_by_module_name(module_name):
    sql = '''
    SELECT m.module_name,
       p.platform_name,
       p.platform_code,
       b.package_type,
       i.app_name, i.git_url, i.git_path,
       ti.team_alias
FROM h5_dist_platform p
INNER JOIN h5_dist_bind bind ON bind.platform_code = p.platform_code
INNER JOIN app_mgt_app_module m ON m.module_name = bind.module_name
INNER JOIN app_mgt_app_build b ON b.module_name = m.module_name AND b.app_id = m.app_id
INNER JOIN app_mgt_app_info i ON i.id = m.app_id
LEFT JOIN team_mgt_app_bind t ON t.app_id = i.id
LEFT JOIN tool_mgt_team_info ti ON ti.id = t.team_id
WHERE 1=1 and m.module_name = "{}"
ORDER BY bind.platform_bind_order, bind.module_bind_order
    '''.format(module_name)
    cursor = connection.cursor()
    cursor.execute(sql)
    logger.info(sql)
    result = cursor.fetchall()
    return result


def get_alarm_team_info(team_name):
    alarm_team_list = []
    if team_name:
        search_condition = "AND ti.team_alias = '{}'".format(team_name)
    else:
        search_condition = ''
    sql = '''
    SELECT i.team_alias,n.wecom_group_id AS alarm_id FROM tool_mgt_team_info i 
    LEFT JOIN `team_mgt_notify` n ON n.team_id = i.id WHERE i.team_level = 2 {}
    '''.format(search_condition)
    cursor = connection.cursor()
    cursor.execute(sql)
    logger.info(sql)
    result = cursor.fetchall()
    for item in result:
        alarm_team_list.append({"alarm_id": item[1], "team_alias": item[0]})

    return alarm_team_list


def get_db_alias_by_module_name(module_name):
    db_alias_list = []

    sql = '''
        SELECT CASE
               WHEN d.alias_display = 'APP_NAME' THEN b.app_module_name
               WHEN d.alias_display = 'ALIAS' THEN d.db_alias
               ELSE
                   CONCAT('数据库别名显示配置类型错误：', d.alias_display)
               END AS db_alias,
               b.app_module_name,
               li.logic_db_name AS db_info_suffix_name
            FROM `db_mgt_domain` d
            INNER JOIN `db_mgt_app_bind` b ON b.db_domain_id = d.id
            INNER JOIN db_mgt_logic_info li ON li.db_domain_id = d.id
        where 1=1
    '''
    if module_name:
        sql += " and b.app_module_name = '{}'".format(module_name)
    cursor = connection.cursor()
    cursor.execute(sql)
    logger.info(sql)
    result = cursor.fetchall()
    for item in result:
        if item[0] not in db_alias_list:
            db_alias_list.append(item[0])
    return db_alias_list


def get_db_in_or_ex_by_module_name(module_name):
    sql = '''
     SELECT need_online,need_check,need_ops FROM `app_mgt_app_module` WHERE module_name = '{}'
    '''.format(module_name)
    cursor = connection.cursor()
    cursor.execute(sql)
    logger.info(sql)
    result = cursor.fetchall()
    # for item in result:

    # print(item)
    if result:
        app_type_info = {"need_online": result[0][0], "need_check": result[0][1], "need_ops": result[0][2]}
        return app_type_info
