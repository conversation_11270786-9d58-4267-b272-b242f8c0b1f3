from django.db import connection


def get_all_meet_automatic_archive_info(suite_list):
    """
    获取满足自动归档的应用以及节点信息
    suite_list: .str 环境套
    return : .iter  迭代对象
    """
    cursor = connection.cursor()
    cursor.execute('''SELECT
                            imiai.pipeline_id,
                            imiai.appName,
                            imiai.proposer,
                            ems.suite_code,
                            emn.node_ip,
                            emn.minion_id,
                            amab.package_name,
                            amab.package_type,
                            emnb.deploy_path,
                            emnb.id,
                            amab.package_full
                            
                        FROM
                            `app_mgt_app_info` amai
                            LEFT JOIN app_mgt_app_module amam ON amai.id = amam.app_id
                            LEFT JOIN `iter_mgt_iter_app_info` imiai ON amam.module_name = imiai.appName
                            LEFT JOIN `env_mgt_node_bind` emnb ON imiai.appName = emnb.module_name
                            LEFT JOIN app_mgt_app_build amab ON amab.module_name = emnb.module_name
                            LEFT JOIN `env_mgt_suite` ems ON emnb.suite_id = ems.id
                            LEFT JOIN `env_mgt_node` emn ON emnb.node_id = emn.id 
                        WHERE
                            imiai.sys_status = '上线中' 
                            AND amai.platform_type = 1
                            AND emn.node_status = 0
                            AND emnb.enable_bind = 1
                            AND ems.suite_code IN ( {} )'''.format(','.join(suite_list)))

    return cursor.fetchall()


def get_can_automatic_archive_mobile_info():
    cursor = connection.cursor()
    cursor.execute('''SELECT
                        imiai.pipeline_id,
                        imiai.appName,
                        max( imme.affirm_time ) AS affirm_time 
                    FROM
                        iter_mgt_iter_app_info imiai
                        LEFT JOIN app_mgt_app_build amab ON imiai.appName = amab.module_name
                        LEFT JOIN iter_mgt_publish_application_confirmation imme ON imiai.pipeline_id = imme.iteration_id 
                    WHERE
                        imiai.sys_status = '上线中' 
                        AND amab.package_type IN ( 'android', 'ios' ) 
                        AND imme.suite_name = 'prod' 
                        AND imme.`status` = 'success' 
                    GROUP BY
                        imme.iteration_id;''')

    return cursor.fetchall()
