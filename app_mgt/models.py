from django.db import models


class AppInfo(models.Model):
    """
    应用表 zt@2020-04-26
    更改表名为 app_mgt_app_info zt@2020-05-06
    """
    app_name = models.CharField(verbose_name='英文名', max_length=100)
    app_cname = models.CharField(verbose_name='中文名', max_length=100)
    app_status = models.BooleanField(verbose_name='应用状态：0-已废弃，1-使用中')
    git_url = models.CharField(verbose_name='git编码', max_length=999)
    git_path = models.CharField(verbose_name='git路径', max_length=999)
    svn_url = models.CharField(verbose_name='svn编码', max_length=999)
    svn_path = models.CharField(verbose_name='svn编码', max_length=999)
    app_jdk_version = models.CharField(verbose_name='JDK整体默认版本', max_length=100)
    app_desc = models.CharField(verbose_name='应用说明', max_length=255)
    create_user = models.CharField(verbose_name='创建人', max_length=20)
    create_time = models.DateTimeField(verbose_name='创建时间')
    update_user = models.CharField(verbose_name='修改人', max_length=20)
    lib_location = models.CharField(verbose_name='团队名', max_length=10)
    update_time = models.DateTimeField(verbose_name='修改时间')
    stamp = models.IntegerField(verbose_name='版本')
    platform_type = models.BooleanField(verbose_name='是否接入平台')
    platform_time = models.DateTimeField(verbose_name='接入平台时间')

    class Meta:
        db_table = 'app_mgt_app_info'
        verbose_name = '应用表'


class AppModule(models.Model):
    """
    应用模块表 zt@2020-04-26
    """
    app_id = models.IntegerField(verbose_name='应用ID')
    module_name = models.CharField(verbose_name='模块名', max_length=100)
    module_code = models.CharField(verbose_name='模块编码', max_length=100)
    module_status = models.BooleanField(verbose_name='模块状态：0-已废弃，1-使用中')
    module_desc = models.CharField(verbose_name='模块说明', max_length=255)
    module_svn_path = models.CharField(verbose_name='svn路径', max_length=999)
    module_jdk_version = models.CharField(verbose_name='模块JDK版本', max_length=100)
    need_online = models.BooleanField(verbose_name='jar是否需要上线')
    need_check = models.BooleanField(verbose_name='是否需要维护')
    app_port = models.IntegerField(verbose_name='应用端口')
    container_name = models.CharField(verbose_name='容器名', max_length=255)
    create_path = models.CharField(verbose_name='打包路径', max_length=100)
    lib_repo = models.CharField(verbose_name='制品库', max_length=100)
    deploy_path = models.CharField(verbose_name='发布路径', max_length=999)
    extend_attr = models.CharField(verbose_name='扩展属性：dubbo端口等', max_length=999)
    create_user = models.CharField(verbose_name='创建人', max_length=20)
    create_time = models.DateTimeField(verbose_name='创建时间')
    update_user = models.CharField(verbose_name='修改人', max_length=20)
    update_time = models.DateTimeField(verbose_name='修改时间')
    stamp = models.IntegerField(verbose_name='版本')
    zeus_type = models.BooleanField(verbose_name='是否接入宙斯')
    need_ops = models.BooleanField(verbose_name='是不是mock应用')
    is_component = models.IntegerField(verbose_name='是否是组件')

    class Meta:
        db_table = 'app_mgt_app_module'
        verbose_name = '应用模块表'


class AppBuild(models.Model):
    """
    应用构建表 zt@2020-04-26
    """
    app_id = models.IntegerField(verbose_name='应用ID')
    module_name = models.CharField(verbose_name='模块名', max_length=100)
    module_code = models.CharField(verbose_name='模块编码', max_length=100)
    module_version = models.CharField(verbose_name='模块版本', max_length=100)
    package_type = models.CharField(verbose_name='包类型：pom、war、jar、tar', max_length=100)
    package_name = models.CharField(verbose_name='包名', max_length=255)
    package_full = models.BooleanField(verbose_name='是否完整包')
    build_jdk_version = models.CharField(verbose_name='编译JDK版本', max_length=100)
    create_user = models.CharField(verbose_name='创建人', max_length=20)
    create_time = models.DateTimeField(verbose_name='创建时间')
    update_user = models.CharField(verbose_name='修改人', max_length=20)
    update_time = models.DateTimeField(verbose_name='修改时间')
    stamp = models.IntegerField(verbose_name='版本')
    need_mock = models.BooleanField(verbose_name='是否存在mock类型')
    mock_build_cmd = models.CharField(verbose_name='mock编译命令', max_length=100)
    build_cmd = models.CharField(verbose_name='编译命令', max_length=128)

    class Meta:
        db_table = 'app_mgt_app_build'
        verbose_name = '应用构建表'


class MobileBuild(models.Model):
    """
     移动端构建表
    """
    module_name = models.CharField(verbose_name='模块名', max_length=100)
    build_path = models.CharField(verbose_name='构建路径', max_length=100)
    build_product_path = models.CharField(verbose_name='构建产物', max_length=100)
    repo_product_path = models.CharField(verbose_name='制品库路径', max_length=255)
    node_module_src_path = models.BooleanField(verbose_name='模块源')
    node_module_tgt_path = models.CharField(verbose_name='模块目标', max_length=100)
    entrance_file = models.CharField(verbose_name='entrance_file路径', max_length=20)
    create_time = models.DateTimeField(verbose_name='创建时间')
    node_version = models.CharField(verbose_name='node版本', max_length=20)
    entrance_file_tgt = models.DateTimeField(verbose_name='组装后的entrance文件')

    class Meta:
        db_table = 'app_mgt_h5_build'
        verbose_name = '应用构建表'

class AppTeam(models.Model):
    """
    应用团队表 zt@2020-04-26
    """
    app_id = models.IntegerField(verbose_name='应用ID')
    team_id = models.IntegerField(verbose_name='团队ID')
    create_user = models.CharField(verbose_name='创建人', max_length=20)
    create_time = models.DateTimeField(verbose_name='创建时间')
    update_user = models.CharField(verbose_name='修改人', max_length=20)
    update_time = models.DateTimeField(verbose_name='修改时间')
    stamp = models.IntegerField(verbose_name='版本')

    class Meta:
        db_table = 'app_mgt_app_team'
        verbose_name = '应用团队表'

class AppTeamBind(models.Model):
    app_id = models.IntegerField(verbose_name='应用ID')
    team_id = models.IntegerField(verbose_name='团队ID')
    bind_desc = models.CharField(verbose_name='绑定说明', max_length=255)
    create_user = models.CharField(verbose_name='创建人', max_length=20)
    create_time = models.DateTimeField(verbose_name='创建时间')
    update_user = models.CharField(verbose_name='修改人', max_length=20)
    update_time = models.DateTimeField(verbose_name='修改时间')
    stamp = models.IntegerField(verbose_name='版本')
    class Meta:
        db_table = 'team_mgt_app_bind'
        verbose_name = '应用团队绑定表'


# class AppUser(models.Model):
#     """
#     应用人员表 zt@2020-04-26
#     """
#     app_id = models.IntegerField(verbose_name='应用ID')
#     user_type = models.IntegerField(verbose_name='人员类型')
#     user_name = models.CharField(verbose_name='用户名', max_length=100)
#     create_user = models.CharField(verbose_name='创建人', max_length=20)
#     create_time = models.DateTimeField(verbose_name='创建时间')
#     update_user = models.CharField(verbose_name='修改人', max_length=20)
#     update_time = models.DateTimeField(verbose_name='修改时间')
#     stamp = models.IntegerField(verbose_name='版本')
#
#     class Meta:
#         db_table = 'app_mgt_app_user'
#         verbose_name = '应用人员表'


class TemplateInfo(models.Model):
    """
    测试环境模板表：app_mgt_template_info zt@2020-08-13
    update at 2020-09-09 yanwei,shen
    add cloums: init_db、use_env_time、clean_cache、test_set_id
    """
    # id = models.IntegerField(verbose_name='ID', primary_key=True)
    template_name = models.CharField(verbose_name='模板名（唯一）', max_length=100)
    default_suite = models.CharField(verbose_name='默认环境套', max_length=100)
    template_is_active = models.BooleanField(verbose_name='模板是否可用')
    template_desc = models.CharField(verbose_name='模块说明', max_length=255)

    init_db = models.CharField(verbose_name='初始化数据库', max_length=255)
    ccms_type = models.IntegerField(verbose_name='是否需要初始化ccms')
    use_env_time = models.DateTimeField(verbose_name='设置环境时间')
    clean_cache = models.CharField(verbose_name='是否清除缓存', max_length=255)
    test_set_id = models.CharField(verbose_name='测试集ID', max_length=255)
    all_selected_mails = models.CharField(verbose_name='邮箱', max_length=1000)

    create_user = models.CharField(verbose_name='创建人', max_length=20)
    create_time = models.DateTimeField(verbose_name='创建时间')
    update_user = models.CharField(verbose_name='修改人', max_length=20)
    update_time = models.DateTimeField(verbose_name='修改时间')
    stamp = models.IntegerField(verbose_name='版本')

    class Meta:
        db_table = 'app_mgt_template_info'
        verbose_name = '测试环境模板表'


class TemplateApp(models.Model):
    """
    测试环境模板相关应用：app_mgt_test_template yanwei.shen@2020-09-14
    """
    # id = models.AutoField(verbose_name='ID', primary_key=True)
    template_id = models.IntegerField(verbose_name='模板ID')
    module_name = models.CharField(verbose_name='标准统一模块名', max_length=255)
    content_type = models.IntegerField(verbose_name='应用的用途类型')
    create_user = models.CharField(verbose_name='创建人', max_length=20)
    create_time = models.DateTimeField(verbose_name='创建时间')
    update_user = models.CharField(verbose_name='修改人', max_length=20)
    update_time = models.DateTimeField(verbose_name='修改时间')
    stamp = models.IntegerField(verbose_name='版本')

    class Meta:
        db_table = 'app_mgt_test_template'
        verbose_name = '测试环境模板相关应用表'


# class TestSet(models.Model):
#     """
#     单元测试集表：app_mgt_test_set zt@2020-08-13
#     """
#     set_id = models.IntegerField(verbose_name='测试集ID')
#     module_id = models.IntegerField(verbose_name='模块ID')
#     module_name = models.CharField(verbose_name='标准统一模块名', max_length=100)
#     set_is_active = models.BooleanField(verbose_name='测试集是否可用')
#     set_desc = models.CharField(verbose_name='测试集说明', max_length=255)
#
#     create_user = models.CharField(verbose_name='创建人', max_length=20)
#     create_time = models.DateTimeField(verbose_name='创建时间')
#     update_user = models.CharField(verbose_name='修改人', max_length=20)
#     update_time = models.DateTimeField(verbose_name='修改时间')
#     stamp = models.IntegerField(verbose_name='版本')
#
#     class Meta:
#         db_table = 'app_mgt_test_set'
#         verbose_name = '单元测试集表'


# class AppMd5Info(models.Model):
#     """
#
#     """
#
#     node_ip_bind_id = models.IntegerField(verbose_name='节点绑定ID')
#     md5 = models.CharField(verbose_name='md5值', max_length=100)
#     update_time = models.DateTimeField(verbose_name='修改时间')
#
#     class Meta:
#         db_table = 'app_mgt_app_md5_info'
#         verbose_name = 'md5信息'


# class SpecialContrastPath(models.Model):
#     """
#      产线节点对比时候特殊的对比路径
#     """
#     module_name = models.CharField(verbose_name='标准统一模块名', max_length=100)
#     special_path = models.CharField(verbose_name='部署路径', max_length=255)
#
#     class Meta:
#         db_table = 'app_mgt_special_contrast_path'
#         verbose_name = '特殊的对比路径'


class JenkinsAutomaticArchiveInfo(models.Model):
    """
     流水线自动回合提醒相关信息
    """
    pipeline_id = models.CharField(verbose_name='迭代', max_length=50, default=None)
    create_time = models.DateTimeField(verbose_name='创建时间')
    update_time = models.DateTimeField(verbose_name='修改时间')

    class Meta:
        db_table = 'jenkins_automatic_archive_info'
        verbose_name = '流水线自动回合提醒相关信息'


class NacosNamespaceInfo(models.Model):
    """
        多个应用对应一个nacos的命名空间，不能通过应用名做唯一标识，需要通过一层转换
    """

    module_name = models.CharField(verbose_name='标准统一模块名', max_length=255)
    namespace = models.CharField(verbose_name='nacos的命名空间', max_length=255)

    class Meta:
        db_table = 'app_mgt_nacos_namespace_info'
        verbose_name = '应用和nacos命名空间的对应关系'


class MiniVersionUpgradeModule(models.Model):
    module_name = models.CharField(verbose_name='标准统一模块名', max_length=255)

    class Meta:
        db_table = 'app_mgt_mini_version_upgrade_module'
        verbose_name = '需要小版本更新的模块'


class AppMgtInterfaceInfo(models.Model):
    module_name = models.CharField(verbose_name='应用名', max_length=50)
    branch_name = models.CharField(verbose_name='分支名', max_length=100)
    interface_name = models.CharField(verbose_name='接口名', max_length=100)
    interface_name_dev = models.CharField(verbose_name='开发编辑接口名', max_length=100)
    interface_path = models.CharField(verbose_name='接口路径', max_length=255)
    interface_method = models.CharField(verbose_name='接口方法', max_length=100)
    interface_type = models.CharField(verbose_name='接口类型', max_length=10)
    content_type = models.CharField(verbose_name='请求参数类型', max_length=50)
    encryption = models.CharField(verbose_name='加密类型', max_length=50)
    request_params = models.JSONField(verbose_name='请求参数')
    response_params = models.JSONField(verbose_name='返回参数')
    defines_params = models.JSONField(verbose_name='自定义对象参数')
    status = models.IntegerField(verbose_name='接口状态，0：不可用，1：可用')
    create_version = models.CharField(verbose_name='接口创建版本', max_length=100)
    create_user = models.CharField(verbose_name='创建人', max_length=50)
    create_time = models.DateTimeField(verbose_name='创建时间')
    update_user = models.CharField(verbose_name='更新人', max_length=50)
    update_time = models.DateTimeField(verbose_name='更新时间')

    class Meta:
        db_table = 'app_mgt_interface_info'
        verbose_name = '应用接口信息管理表'


class AppMgtInterfaceQueryExtraAppInfo(models.Model):
    module_name = models.CharField(verbose_name='应用名', max_length=255)
    branch_name = models.CharField(verbose_name='分支名', max_length=255)

    class Meta:
        db_table = 'app_mgt_interface_query_extra_app_info'
        verbose_name = '应用接口信息查询特殊版本配置表'


# class AgentMgtPublishWarnLog(models.Model):
#     module_name = models.CharField(max_length=100, verbose_name='应用名')
#     branch = models.CharField(max_length=100, verbose_name='分支')
#     agent_type = models.CharField(max_length=100, verbose_name='agent')
#     err_msg = models.TextField(max_length=2000, verbose_name='错误信息')
#     status = models.CharField(max_length=100, verbose_name='状态')
#     update_user = models.CharField(verbose_name='更新人', max_length=50)
#     update_time = models.DateTimeField(verbose_name='更新时间')
#     class Meta:
#         db_table = 'agent_mgt_publish_warn_log'
#         verbose_name = '应用接口信息管理临时表'


class AppMgtInterfaceInfoTemp(models.Model):
    module_name = models.CharField(verbose_name='应用名', max_length=50)
    branch_name = models.CharField(verbose_name='分支名', max_length=100)
    interface_name = models.CharField(verbose_name='接口名', max_length=100)
    interface_path = models.CharField(verbose_name='接口路径', max_length=255)
    interface_method = models.CharField(verbose_name='接口方法', max_length=100)
    interface_type = models.CharField(verbose_name='接口类型', max_length=10)
    content_type = models.CharField(verbose_name='请求参数类型', max_length=50)
    encryption = models.CharField(verbose_name='加密类型', max_length=50)
    request_params = models.JSONField(verbose_name='请求参数')
    response_params = models.JSONField(verbose_name='返回参数')
    defines_params = models.JSONField(verbose_name='自定义对象参数')
    status = models.IntegerField(verbose_name='接口状态，0：不可用，1：可用')
    create_version = models.CharField(verbose_name='接口创建版本', max_length=100)
    create_user = models.CharField(verbose_name='创建人', max_length=50)
    create_time = models.DateTimeField(verbose_name='创建时间')
    update_user = models.CharField(verbose_name='更新人', max_length=50)
    update_time = models.DateTimeField(verbose_name='更新时间')

    class Meta:
        db_table = 'app_mgt_interface_info_temp'
        verbose_name = '应用接口信息管理临时表'


class AppMgtInterfaceScanHistory(models.Model):
    module_name = models.CharField(verbose_name='应用名', max_length=50)
    branch_name = models.CharField(verbose_name='分支名', max_length=100)
    interface_type = models.CharField(verbose_name='接口类型', max_length=10)
    create_time = models.DateTimeField(verbose_name='创建时间')
    interface_info = models.JSONField(verbose_name='扫描接口信息')

    class Meta:
        db_table = 'app_mgt_interface_scan_history'
        verbose_name = '接口信息扫描历史表'


class AppMgtInterfaceParamInfo(models.Model):
    module_name = models.CharField(verbose_name='应用名', max_length=50)
    branch_name = models.CharField(verbose_name='分支名称', max_length=100)
    interface_path = models.CharField(verbose_name='接口路径', max_length=255)
    interface_method = models.CharField(verbose_name='接口方法', max_length=100)
    interface_type = models.CharField(verbose_name='接口类型', max_length=10)
    field_name = models.CharField(verbose_name='字段名称', max_length=100)
    field_type = models.CharField(verbose_name='字段类型', max_length=100)
    is_required = models.IntegerField(verbose_name='是否必填参数：1，必填，0，选填')
    enum_values = models.CharField(verbose_name='枚举值', max_length=50)
    create_user = models.CharField(verbose_name='创建人', max_length=50)
    create_time = models.DateTimeField(verbose_name='创建时间')
    update_user = models.CharField(verbose_name='更新人', max_length=50)
    update_time = models.DateTimeField(verbose_name='更新时间')

    class Meta:
        db_table = 'app_mgt_interface_param_info'
        verbose_name = '应用接口参数列表信息'


class AppMgtInterfaceMap(models.Model):
    module_name = models.CharField(verbose_name='应用名', max_length=100)
    interface_path = models.CharField(verbose_name='接口路径', max_length=255)
    interface_method = models.CharField(verbose_name='接口方法', max_length=100)
    content_type = models.CharField(verbose_name='请求参数类型', max_length=50)

    class Meta:
        db_table = 'app_mgt_interface_map'
        verbose_name = '应用接口信息匹配表'


class AppMgtInterfaceScanLock(models.Model):
    module_name = models.CharField(verbose_name='应用名', max_length=100)
    branch_name = models.CharField(verbose_name='分支名称', max_length=100)
    interface_type = models.CharField(verbose_name='接口类型', max_length=10)
    is_lock = models.IntegerField(verbose_name='锁状态')
    create_time = models.DateTimeField(verbose_name='创建时间')
    update_time = models.DateTimeField(verbose_name='更新时间')

    class Meta:
        db_table = 'app_mgt_interface_scan_lock'
        verbose_name = '应用接口扫描锁'


class AppMgtApidocInfo(models.Model):
    id = models.BigAutoField(primary_key=True)
    module_name = models.CharField(verbose_name='应用名', max_length=50)
    iter_branch = models.CharField(verbose_name='分支名', max_length=50)
    api_name = models.CharField(verbose_name='接口名', max_length=50)
    api_path = models.CharField(verbose_name='接口路径', max_length=255)
    api_method = models.CharField(verbose_name='接口方法', max_length=50)
    api_type = models.CharField(verbose_name='接口类型', max_length=20)
    api_method_signature = models.CharField(verbose_name='接口类型', max_length=255)
    api_request_sample = models.CharField(verbose_name='请求参数样例', max_length=1000)
    api_response_sample = models.CharField(verbose_name='响应参数样例', max_length=1000)
    retain =models.CharField(verbose_name='强制保留', max_length=20)
    create_user = models.CharField(verbose_name='创建人', max_length=50)
    create_time = models.DateTimeField(verbose_name='创建时间')
    update_user = models.CharField(verbose_name='更新人', max_length=50)
    update_time = models.DateTimeField(verbose_name='更新时间')
    status = models.IntegerField(verbose_name='接口状态，0：不可用，1：可用')

    class Meta:
        db_table = 'app_mgt_apidoc_info'
        verbose_name = '应用apidoc信息表'


class AppMgtApidocParamInfo(models.Model):
    api_id = models.BigIntegerField(verbose_name='api id')
    param_type = models.CharField(verbose_name='参数类型（header/requestParam/responseParam）', max_length=50)
    param_name = models.CharField(verbose_name='参数名称', max_length=255)
    param_value_type = models.CharField(verbose_name='参数值类型（基本类型+自定义类型）', max_length=255)
    param_desc = models.TextField(verbose_name='参数描述', max_length=1000)
    param_default_value = models.CharField(verbose_name='参数默认值', max_length=20)
    param_size = models.CharField(verbose_name='Information about the size of the variable.', max_length=255)
    param_allowed_values = models.CharField(verbose_name='Information about allowed values of the variable.',
                                            max_length=1000)
    optional = models.IntegerField(verbose_name='是否可选(是1/否0)')
    create_user = models.CharField(verbose_name='创建人', max_length=50)
    create_time = models.DateTimeField(verbose_name='创建时间')
    update_user = models.CharField(verbose_name='更新人', max_length=50)
    update_time = models.DateTimeField(verbose_name='更新时间')

    class Meta:
        db_table = 'app_mgt_apidoc_param_info'
        verbose_name = '应用api参数信息表'

class PublishMgtBackupInfo(models.Model):

    create_user = models.CharField(verbose_name='创建人', max_length=20)
    create_time = models.DateTimeField(verbose_name='创建时间')
    update_user = models.CharField(verbose_name='修改人', max_length=20)
    update_time = models.DateTimeField(verbose_name='修改时间')
    stamp = models.IntegerField(verbose_name='版本')
    module_name = models.CharField(verbose_name='应用名', max_length=100)
    node_ip = models.CharField(verbose_name='节点IP', max_length=100)
    backup_date = models.DateTimeField(verbose_name='备份时间')
    backup_path = models.CharField(verbose_name='备份路径', max_length=255)
    salt_upd_log = models.CharField(verbose_name='salt更新日志', max_length=255)
    lib_build_time = models.DateTimeField(verbose_name='lib构建时间')
    publish_user = models.CharField(verbose_name='发布人', max_length=100)
    publish_time = models.DateTimeField(verbose_name='发布时间')
    publish_iteration_id = models.CharField(verbose_name='发布迭代ID', max_length=100)
    product_info_id = models.IntegerField(verbose_name='产品线ID')
    node_bind_id = models.IntegerField(verbose_name='节点绑定ID')
    backup_desc = models.CharField(verbose_name='备份描述', max_length=255)
    class Meta:
        db_table = 'publish_mgt_backup_info'
        verbose_name = '发布备份信息表'
