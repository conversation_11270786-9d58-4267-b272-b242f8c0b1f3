from django.contrib import admin

from app_mgt import models


@admin.register(models.AppInfo)
class AppInfoAdmin(admin.ModelAdmin):
    list_per_page = 10
    list_filter = ('app_status',)
    list_display = ('app_name',
                    'app_cname',
                    'app_status',
                    'git_url',
                    'git_path',
                    'svn_url',
                    'svn_path',
                    'app_jdk_version',
                    'app_desc',
                    'create_user',
                    'create_time',
                    'update_user',
                    'update_time',
                    'stamp')


@admin.register(models.AppModule)
class AppModuleAdmin(admin.ModelAdmin):
    list_per_page = 10
    list_filter = ('module_status',)
    list_display = ('app_id',
                    'module_name',
                    'module_code',
                    'module_status',
                    'module_desc',
                    'module_svn_path',
                    'module_jdk_version',
                    'need_online',
                    'need_check',
                    'app_port',
                    'container_name',
                    'create_path',
                    'lib_repo',
                    'deploy_path',
                    'extend_attr',
                    'create_user',
                    'create_time',
                    'update_user',
                    'update_time',
                    'stamp')


@admin.register(models.AppBuild)
class AppBuildAdmin(admin.ModelAdmin):
    list_per_page = 10
    list_filter = ('package_type',)
    list_display = ('app_id',
                    'module_name',
                    'module_code',
                    'module_version',
                    'package_type',
                    'package_name',
                    'package_full',
                    'build_jdk_version',
                    'create_user',
                    'create_time',
                    'update_user',
                    'update_time',
                    'stamp')


@admin.register(models.AppTeamBind)
class AppTeamAdmin(admin.ModelAdmin):
    list_per_page = 10
    list_filter = ('team_id',)
    list_display = ('app_id',
                    'team_id',
                    'create_user',
                    'create_time',
                    'update_user',
                    'update_time',
                    'stamp')


# @admin.register(models.AppUser)
# class AppUserAdmin(admin.ModelAdmin):
#     list_per_page = 10
#     list_filter = ('user_type',)
#     list_display = ('app_id',
#                     'user_type',
#                     'user_name',
#                     'create_user',
#                     'create_time',
#                     'update_user',
#                     'update_time',
#                     'stamp')
