#
import re
import os
import gitlab
import datetime
import subprocess
from iter_mgt.models import Branches
from rest_framework import viewsets, response, serializers
from tool_mgt.models import GitUrl, TeamInfo, SvnUrl
from spider.settings import ApiResult, GITLAP_INFO, logger
from app_mgt.models import AppInfo, AppModule, AppTeam, AppBuild


class AppRegisterApi(viewsets.ModelViewSet):
    """
    添加应用
    """
    serializer_class = serializers.Serializer
    queryset = Branches.objects
    user = ''
    git_workspace = '/tmp/tmp_spider/'

    def list(self, request):
        return response.Response(data=ApiResult.success_dict(msg="成功"))

    @staticmethod
    def shell_cmd(cmd):
        return subprocess.Popen(cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)

    @staticmethod
    def git_repo_exist(git_code_path):
        gl = gitlab.Gitlab(GITLAP_INFO['HTTP_URL'], GITLAP_INFO['TOKEN'])
        for p in gl.projects.list(all=True, all_list=False):
            if p.path_with_namespace == git_code_path:
                return True
        return False

    def svn_repo_exist(self, trunk_path):
        p = self.shell_cmd('svn list %s ' % trunk_path)
        p.communicate()
        rt_code = p.returncode
        if rt_code == 0:
            return True
        else:
            return False

    def handle_single_app(self, data):
        app_name = data[0]['app_name'].strip() if 'app_name' in data[0] else ''
        app_type = data[0]['app_type'].strip() if 'app_type' in data[0] else ''
        jar_name = data[0]['jar_name'].strip() if 'jar_name' in data[0] else ''
        jdk_version = data[0]['jdk_version'].strip() if 'jdk_version' in data[0] else ''
        team = data[0]['team'].strip() if 'team' in data[0] else ''
        trunk_path = data[0]['trunk_path'].strip() if 'trunk_path' in data[0] else ''
        repo_path = data[0]['repo_path'].strip() if 'repo_path' in data[0] else ''
        app_name_cn = data[0]['app_name_cn'].strip() if 'app_name_cn' in data[0] else ''
        now = datetime.datetime.now()
        if jar_name:
            need_online = 1
        else:
            need_online = 0

        try:
            AppInfo.objects.get(app_name=app_name)
            return False, '应用已存在'
        except AppInfo.DoesNotExist:
            pass

        if trunk_path.startswith('svn://'):
            group_name = self.check_svn_group(trunk_path)
            if group_name:
                svn_url = SvnUrl.objects.get(svn_url=group_name).svn_name
            else:
                return False, '未匹配到svn组'
            try:
                # 添加 app_mgt_app_info
                app_obj = AppInfo.objects.create(
                    app_name=app_name, app_cname=app_name_cn, app_status=1,
                    svn_url=svn_url, svn_path=trunk_path.split(group_name)[-1], app_jdk_version=jdk_version,
                    create_time=now, update_time=now, create_user=self.user, update_user=self.user, stamp=0,
                    lib_location=team
                )
                app_id = app_obj.id
            except Exception as e:
                logger.error(str(e))
                return False, '插入app_info失败'

            try:
                # 添加 app_mgt_app_module
                AppModule.objects.create(
                    app_id=app_id, module_name=app_name, module_status=1, module_desc=app_name_cn,
                    need_online=need_online, need_check=1, create_user=self.user, update_user=self.user,
                    create_time=now, update_time=now, stamp=0, extend_attr=None, module_svn_path='/',
                    lib_repo=repo_path, create_path=app_name
                )
            except Exception as e:
                logger.error(str(e))
                return False, '插入app_module失败'

            try:
                # 添加 app_mgt_app_team
                team_obj = TeamInfo.objects.get(team_short_name=team)
                team_id = team_obj.id
                AppTeam.objects.create(
                    app_id=app_id, team_id=team_id, stamp=0,
                    create_user=self.user, update_user=self.user,
                    create_time=now, update_time=now
                )
            except Exception as e:
                logger.error(str(e))
                return False, '插入app_team失败'

            try:
                # 添加 app_mgt_app_build
                AppBuild.objects.create(
                    app_id=app_id, module_name=app_name, package_full=1,
                    create_user=self.user, update_user=self.user, package_type=app_type, package_name=jar_name,
                    create_time=now, update_time=now, stamp=0
                )
            except Exception as e:
                logger.error(str(e))

            return True, ''
        else:
            try:
                # 添加 app_mgt_app_info
                app_obj = AppInfo.objects.create(
                    app_name=app_name, app_cname=app_name_cn, app_status=1, app_jdk_version=jdk_version,
                    git_url=trunk_path.split('/')[0], git_path=trunk_path.strip(trunk_path.split('/')[0]),
                    create_time=now, update_time=now, create_user=self.user, update_user=self.user, stamp=0
                )
                app_id = app_obj.id
            except Exception as e:
                logger.error(str(e))
                return False, '插入app_info失败'

            try:
                # 添加 app_mgt_app_module
                AppModule.objects.create(
                    app_id=app_id, module_name=app_name, module_status=1, module_desc=app_name_cn,
                    need_online=need_online, need_check=1, create_user=self.user, update_user=self.user,
                    create_time=now, update_time=now, stamp=0, extend_attr=None
                )
            except Exception as e:
                logger.error(str(e))
                return False, '插入app_module失败'

            try:
                # 添加 app_mgt_app_team
                team_obj = TeamInfo.objects.get(team_short_name=team)
                team_id = team_obj.id
                AppTeam.objects.create(
                    app_id=app_id, team_id=team_id, stamp=0,
                    create_user=self.user, update_user=self.user,
                    create_time=now, update_time=now
                )
            except Exception as e:
                logger.error(str(e))
                return False, '插入app_team失败'

            try:
                # 添加 app_mgt_app_build
                AppBuild.objects.create(
                    app_id=app_id, module_name=app_name, package_full=1,
                    package_type=app_type, package_name=jar_name,
                    create_user=self.user, update_user=self.user, create_time=now, update_time=now, stamp=0
                )
            except Exception as e:
                logger.error(str(e))
                return False, '插入app_build失败'

            return True, ''

    def handle_sub_git_app(self, data, app_id, now):
        for app in data:
            if app['app_type'] == 'pom':
                continue
            app_name = app['app_name'].strip() if 'app_name' in app else ''
            app_type = app['app_type'].strip() if 'app_type' in app else ''
            jar_name = app['jar_name'].strip() if 'jar_name' in app else ''
            jdk_version = app['jdk_version'].strip() if 'jdk_version' in app else ''
            repo_path = app['repo_path'].strip() if 'repo_path' in app else ''
            app_name_cn = app['app_name_cn'].strip() if 'app_name_cn' in app else ''

            if jar_name:
                need_online = 1
            else:
                need_online = 0

            try:
                # 添加 app_mgt_app_module
                AppModule.objects.create(
                    app_id=app_id, module_name=app_name, module_status=1, module_desc=app_name_cn,
                    need_online=need_online, need_check=1, create_user=self.user, update_user=self.user,
                    lib_repo=repo_path, create_time=now, update_time=now, stamp=0, extend_attr=None
                )
            except Exception as e:
                logger.error(str(e))
                return False, '插入app_module失败'

            try:
                # 添加 app_mgt_app_build
                AppBuild.objects.create(
                    app_id=app_id, module_name=app_name, package_full=1,
                    package_type=app_type, package_name=jar_name, build_jdk_version=jdk_version,
                    create_user=self.user, update_user=self.user, create_time=now, update_time=now, stamp=0
                )
            except Exception as e:
                logger.error(str(e))
                return False, '插入app_build失败'

        return True, ''

    def handle_sub_svn_app(self, data, app_id, now):
        for app in data:
            if app['app_type'] == 'pom':
                continue
            app_name = app['app_name'].strip() if 'app_name' in app else ''
            app_type = app['app_type'].strip() if 'app_type' in app else ''
            jar_name = app['jar_name'].strip() if 'jar_name' in app else ''
            jdk_version = app['jdk_version'].strip() if 'jdk_version' in app else ''
            repo_path = app['repo_path'].strip() if 'repo_path' in app else ''
            app_name_cn = app['app_name_cn'].strip() if 'app_name_cn' in app else ''

            if jar_name:
                need_online = 1
            else:
                need_online = 0

            try:
                # 添加 app_mgt_app_module
                AppModule.objects.create(
                    app_id=app_id, module_name=app_name, module_status=1, module_desc=app_name_cn,
                    need_online=need_online, need_check=1, create_user=self.user, update_user=self.user,
                    create_time=now, update_time=now, stamp=0, extend_attr=None, lib_repo=repo_path,
                )
            except Exception as e:
                logger.error(str(e))
                return False, '插入app_module失败'

            try:
                # 添加 app_mgt_app_build
                AppBuild.objects.create(
                    app_id=app_id, module_name=app_name, package_full=1,
                    create_user=self.user, update_user=self.user, package_type=app_type, package_name=jar_name,
                    create_time=now, update_time=now, stamp=0, build_jdk_version=jdk_version
                )
            except Exception as e:
                logger.error(str(e))

        return True, ''

    def handle_multi_app(self, data):
        now = datetime.datetime.now()
        for app in data:
            if app['app_type'] == 'pom':
                app_name = app['app_name'].strip() if 'app_name' in app else ''
                app_type = app['app_type'].strip() if 'app_type' in app else ''
                jar_name = app['jar_name'].strip() if 'jar_name' in app else ''
                jdk_version = app['jdk_version'].strip() if 'jdk_version' in app else ''
                team = app['team'].strip() if 'team' in app else ''
                trunk_path = app['trunk_path'].strip() if 'trunk_path' in app else ''
                repo_path = app['repo_path'].strip() if 'repo_path' in app else ''
                app_name_cn = app['app_name_cn'].strip() if 'app_name_cn' in app else ''

                try:
                    AppInfo.objects.get(app_name=app_name)
                    return False, '应用已存在'
                except AppInfo.DoesNotExist:
                    pass

                if trunk_path.startswith('svn://'):
                    group_name = self.check_svn_group(trunk_path)
                    if group_name:
                        svn_url = SvnUrl.objects.get(svn_url=group_name).svn_name
                    else:
                        return False, '未匹配到svn组'

                    try:
                        # 添加 app_mgt_app_info
                        app_obj = AppInfo.objects.create(
                            app_name=app_name, app_cname=app_name_cn, app_status=1,
                            svn_url=svn_url, svn_path=trunk_path.split(group_name)[-1], app_jdk_version=jdk_version,
                            create_time=now, update_time=now, create_user=self.user, update_user=self.user, stamp=0,
                            lib_location=team
                        )
                        app_id = app_obj.id
                    except Exception as e:
                        logger.error(str(e))
                        return False, '插入app_info失败'

                    try:
                        # 添加 app_mgt_app_module
                        AppModule.objects.create(
                            app_id=app_id, module_name=app_name, module_status=1, module_desc=app_name_cn,
                            need_online=0, need_check=1, create_user=self.user, update_user=self.user,
                            create_time=now, update_time=now, stamp=0, extend_attr=None, module_svn_path='/',
                            lib_repo=repo_path, create_path=app_name
                        )
                    except Exception as e:
                        logger.error(str(e))
                        return False, '插入app_module失败'

                    try:
                        # 添加 app_mgt_app_team
                        team_obj = TeamInfo.objects.get(team_short_name=team)
                        team_id = team_obj.id
                        AppTeam.objects.create(
                            app_id=app_id, team_id=team_id, stamp=0,
                            create_user=self.user, update_user=self.user,
                            create_time=now, update_time=now
                        )
                    except Exception as e:
                        logger.error(str(e))
                        return False, '插入app_team失败'

                    try:
                        # 添加 app_mgt_app_build
                        AppBuild.objects.create(
                            app_id=app_id, module_name=app_name, package_full=1,
                            create_user=self.user, update_user=self.user, package_type=app_type, package_name=jar_name,
                            create_time=now, update_time=now, stamp=0
                        )
                    except Exception as e:
                        logger.error(str(e))

                    result, msg = self.handle_sub_svn_app(data, app_id, now)
                    return result, msg
                else:
                    self.check_git_group(trunk_path)
                    try:
                        # 添加 app_mgt_app_info
                        app_obj = AppInfo.objects.create(
                            app_name=app_name, app_cname=app_name_cn, app_status=1, app_jdk_version=jdk_version,
                            git_url=trunk_path.split('/')[0], git_path=trunk_path.strip(trunk_path.split('/')[0]),
                            create_time=now, update_time=now, create_user=self.user, update_user=self.user, stamp=0
                        )
                        app_id = app_obj.id
                    except Exception as e:
                        logger.error(str(e))
                        return False, '插入app_info失败'

                    try:
                        # 添加 app_mgt_app_module
                        AppModule.objects.create(
                            app_id=app_id, module_name=app_name, module_status=1, module_desc=app_name_cn,
                            need_online=0, need_check=1, create_user=self.user, update_user=self.user,
                            create_time=now, update_time=now, stamp=0, extend_attr=None, lib_repo=repo_path
                        )
                    except Exception as e:
                        logger.error(str(e))
                        return False, '插入app_module失败'

                    try:
                        # 添加 app_mgt_app_team
                        team_obj = TeamInfo.objects.get(team_short_name=team)
                        team_id = team_obj.id
                        AppTeam.objects.create(
                            app_id=app_id, team_id=team_id, stamp=0,
                            create_user=self.user, update_user=self.user,
                            create_time=now, update_time=now
                        )
                    except Exception as e:
                        logger.error(str(e))
                        return False, '插入app_team失败'

                    try:
                        # 添加 app_mgt_app_build
                        AppBuild.objects.create(
                            app_id=app_id, module_name=app_name, package_full=1,
                            package_type=app_type, package_name=jar_name,
                            create_user=self.user, update_user=self.user,
                            create_time=now, update_time=now, stamp=0
                        )
                    except Exception as e:
                        logger.error(str(e))
                        return False, '插入app_build失败'

                    result, msg = self.handle_sub_git_app(data, app_id, now)
                    return result, msg
        return False, '未发现pom应用'

    def add_java_app(self, data):
        if len(data) == 1:
            result, msg = self.handle_single_app(data)
        else:
            result, msg = self.handle_multi_app(data)
        return result, msg

    def check_git_group(self, trunk_path):
        git_group = trunk_path.split('/')[0]
        now = datetime.datetime.now()
        try:
            GitUrl.objects.get(git_url=git_group)
        except GitUrl.DoesNotExist:
            add_group = GitUrl.objects.create(
                git_url=git_group, git_name='git-{}'.format(git_group), git_server_id=1, git_url_desc=git_group,
                create_user=self.user, update_user=self.user, stamp=0, create_time=now, update_time=now
            )
            add_group.save()

    @staticmethod
    def check_svn_group(trunk_path):
        svn_groups = sorted(list(SvnUrl.objects.all().values_list('svn_url', flat=True)), reverse=True)
        for g in svn_groups:
            if g in trunk_path:
                return g
        return ''

    def non_java_git_insert_data(self, team, trunk_path, app_name, app_name_cn, now):
        try:
            # 添加 app_mgt_app_info
            app_obj = AppInfo.objects.create(
                app_name=app_name, app_cname=app_name_cn, app_status=1,
                git_url=trunk_path.split('/')[0], git_path=trunk_path.strip(trunk_path.split('/')[0]),
                create_time=now, update_time=now, create_user=self.user, update_user=self.user, stamp=0,
                lib_location=team
            )
            app_id = app_obj.id
        except Exception as e:
            logger.error(str(e))
            return False, '插入app_info失败'

        try:
            # 添加 app_mgt_app_module
            AppModule.objects.create(
                app_id=app_id, module_name=app_name, module_status=1, module_desc=app_name_cn,
                need_online=1, need_check=1, create_user=self.user, update_user=self.user,
                create_time=now, update_time=now, stamp=0, extend_attr=None
            )
        except Exception as e:
            logger.error(str(e))
            return False, '插入app_module失败'

        try:
            # 添加 app_mgt_app_team
            team_obj = TeamInfo.objects.get(team_short_name=team)
            team_id = team_obj.id
            AppTeam.objects.create(
                app_id=app_id, team_id=team_id, stamp=0,
                create_user=self.user, update_user=self.user,
                create_time=now, update_time=now
            )
        except Exception as e:
            logger.error(str(e))
            return False, '插入app_team失败'

        try:
            # 添加 app_mgt_app_build
            AppBuild.objects.create(
                app_id=app_id, module_name=app_name, package_full=1,
                create_user=self.user, update_user=self.user,
                create_time=now, update_time=now, stamp=0
            )
        except Exception as e:
            logger.error(str(e))
            return False, '插入app_build失败'

        return True, ''

    def non_java_svn_insert_data(self, team, trunk_path, group_name, svn_url, app_name, app_name_cn, now):
        try:
            # 添加 app_mgt_app_info
            app_obj = AppInfo.objects.create(
                app_name=app_name, app_cname=app_name_cn, app_status=1,
                svn_url=svn_url, svn_path=trunk_path.split(group_name)[-1],
                create_time=now, update_time=now, create_user=self.user, update_user=self.user, stamp=0,
                lib_location=team
            )
            app_id = app_obj.id
        except Exception as e:
            logger.error(str(e))
            return False, '插入app_info失败'

        try:
            # 添加 app_mgt_app_module
            AppModule.objects.create(
                app_id=app_id, module_name=app_name, module_status=1, module_desc=app_name_cn,
                need_online=1, need_check=1, create_user=self.user, update_user=self.user,
                create_time=now, update_time=now, stamp=0, extend_attr=None, module_svn_path="/"
            )
        except Exception as e:
            logger.error(str(e))
            return False, '插入app_module失败'

        try:
            # 添加 app_mgt_app_team
            team_obj = TeamInfo.objects.get(team_short_name=team)
            team_id = team_obj.id
            AppTeam.objects.create(
                app_id=app_id, team_id=team_id, stamp=0,
                create_user=self.user, update_user=self.user,
                create_time=now, update_time=now
            )
        except Exception as e:
            logger.error(str(e))
            return False, '插入app_team失败'

        try:
            # 添加 app_mgt_app_build
            AppBuild.objects.create(
                app_id=app_id, module_name=app_name, package_full=1,
                create_user=self.user, update_user=self.user,
                create_time=now, update_time=now, stamp=0
            )
        except Exception as e:
            logger.error(str(e))

        return True, ''

    def add_non_java_app(self, data):
        team = data[0]['team']
        repo_type = data[0]['repo_type']
        trunk_path = data[0]['trunk_path']
        app_name = data[0]['app_name']
        app_name_cn = data[0]['app_name_cn']
        now = datetime.datetime.now()

        try:
            AppInfo.objects.get(app_name=app_name)
            return False, '应用已存在'
        except AppInfo.DoesNotExist:
            pass

        if repo_type == 'GIT' and self.git_repo_exist(trunk_path):
            self.check_git_group(trunk_path)
            result, msg = self.non_java_git_insert_data(team, trunk_path, app_name, app_name_cn, now)
            return result, msg
        elif repo_type == 'SVN' and self.svn_repo_exist(trunk_path):
            group_name = self.check_svn_group(trunk_path)
            if group_name:
                svn_url = SvnUrl.objects.get(svn_url=group_name).svn_name
                result, msg = self.non_java_svn_insert_data(team, trunk_path, group_name, svn_url, app_name,
                                                            app_name_cn, now)
                return result, msg
            else:
                return False, '未匹配到svn组'
        else:
            return False, '未发现该仓库'

    def create(self, request):
        # 新增应用接口
        if isinstance(request.user, str):
            self.user = request.user
        else:
            self.user = request.user.username
        if request.data:
            if 'app_category' in request.data[0] and request.data[0]['app_category'] == '非JAVA':
                result, msg = self.add_non_java_app(request.data)
            else:
                result, msg = self.add_java_app(request.data)

            if result:
                return response.Response(data=ApiResult.success_dict(msg="添加成功"))
            else:
                return response.Response(data=ApiResult.failed_dict(msg=msg))
        else:
            return response.Response(data=ApiResult.failed_dict(msg="没有应用信息"))

    def parse_svn_repo(self, team, trunk_path):
        app_data = []
        if trunk_path and not trunk_path.endswith('/'):
            trunk_path += '/'

        try:
            ret = subprocess.check_output('svn list -R %s |grep pom.xml' % trunk_path, shell=True)
            result = ret.decode().strip().split('\n')
        except subprocess.CalledProcessError:
            return False, app_data, "源码地址解析错误"

        for pom_file in result:
            ret = subprocess.check_output('svn cat %s%s ' % (trunk_path, pom_file), shell=True)
            pom_content = ret.decode()
            try:
                del_content = re.search(r'<parent>(.*)</parent>', pom_content, re.DOTALL).group(0)
            except AttributeError:
                del_content = ''
            if del_content:
                pom_content = re.sub(del_content, '', pom_content)

            try:
                app_name = re.search(r'<artifactId>(.*)</artifactId>', pom_content).group(1)
            except AttributeError:
                app_name = ''

            try:
                app_type = re.search(r'<packaging>(.*)</packaging>', pom_content).group(1)
            except AttributeError:
                app_type = ''
            if not app_type:
                app_type = 'jar'

            app_data.append({
                'app_name': app_name,
                'app_type': app_type,
                'jdk_version': '1.8',
                'team': team,
                'repo_path': '',
                'trunk_path': trunk_path + pom_file.replace('pom.xml', '')
            })
        return True, app_data, '解析完成'

    def parse_git_repo(self, team, trunk_path):
        self.shell_cmd('rm -rf {}/*'.format(self.git_workspace)).communicate()
        self.shell_cmd('mkdir -p {}'.format(self.git_workspace)).communicate()
        os.chdir(self.git_workspace)
        self.shell_cmd('git clone --depth 1 {}:{}'.format(GITLAP_INFO['SSH_URL'], trunk_path)).communicate()
        repo_dir = trunk_path.split('/')[-1]
        p = self.shell_cmd("find {} -name 'pom.xml' ".format(repo_dir))
        out, err = p.communicate()
        rt_code = p.returncode
        if rt_code == 0:
            app_data = []
            pom_files = out.decode().split()
            for pom_file in pom_files:
                p = self.shell_cmd('cat {}'.format(pom_file))
                out, err = p.communicate()
                pom_content = out.decode()
                try:
                    del_content = re.search(r'<parent>(.*)</parent>', pom_content, re.DOTALL).group(0)
                except AttributeError:
                    del_content = ''
                if del_content:
                    pom_content = re.sub(del_content, '', pom_content)

                try:
                    app_name = re.search(r'<artifactId>(.*)</artifactId>', pom_content).group(1)
                except AttributeError:
                    app_name = ''

                try:
                    app_type = re.search(r'<packaging>(.*)</packaging>', pom_content).group(1)
                except AttributeError:
                    app_type = ''
                if not app_type:
                    app_type = 'jar'

                app_data.append({
                    'app_name': app_name,
                    'app_type': app_type,
                    'jdk_version': '1.8',
                    'team': team,
                    'repo_path': '',
                    'trunk_path': trunk_path
                })
            return True, app_data, '解析完成'
        else:
            return False, err.decode()

    def put(self, request):
        # 解析仓库接口
        if isinstance(request.user, str):
            self.user = request.user
        else:
            self.user = request.user.username

        if request.data:
            team = request.data.get('team', '')
            repo_type = request.data.get('repo_type', '')
            trunk_path = request.data.get('trunk_path', '')
            if repo_type == 'SVN':
                if not self.svn_repo_exist(trunk_path):
                    return response.Response(data=ApiResult.failed_dict(msg='仓库不存在'))
                result, app_data, msg = self.parse_svn_repo(team, trunk_path)
            elif repo_type == 'GIT':
                if not self.git_repo_exist(trunk_path):
                    return response.Response(data=ApiResult.failed_dict(msg='仓库不存在'))
                result, app_data, msg = self.parse_git_repo(team, trunk_path)
            else:
                return response.Response(data=ApiResult.failed_dict(msg="仓库类型不正确"))
        else:
            return response.Response(data=ApiResult.failed_dict(msg="没有应用信息"))

        if result:
            return response.Response(data=ApiResult.success_dict(data=app_data, msg=msg))
        else:
            return response.Response(data=ApiResult.failed_dict(data=[], msg=msg))
