import datetime
import itertools
import json
import os
import traceback
from threading import Lock

from django.core import serializers
from django.db import transaction
from django.db.models import Count
from rest_framework import viewsets, response

from app_mgt.app_mgt_ser import get_base_branch_name, get_archive_branch_name_by_app, \
    batch_insert_interface_info, batch_insert_interface_info_from_temp, get_app_interface_info
from app_mgt.models import AppMgtInterfaceInfo, AppMgtInterfaceParamInfo, \
    AppMgtInterfaceInfoTemp, AppMgtInterfaceMap, AppMgtInterfaceScanLock, AppMgtApidocInfo
from sharding_mgt.models import AgentMgtExecLog
from spider.settings import ApiResult, logger, INTERFACE_SCAN


class AppMgtInterfaceApi(viewsets.ViewSet):
    """
    接口服务，非APIDOC
    """
    authentication_classes = []

    def __get_create_version(self, module_name, interface_path, interface_method, interface_type, branch_name):
        obj = AppMgtInterfaceInfo.objects.filter(module_name=module_name,
                                                 interface_path=interface_path,
                                                 interface_method=interface_method,
                                                 interface_type=interface_type).values("create_version")
        if obj:
            return obj[0].get("create_version")
        else:
            return branch_name

    def __send_warn_email(self, module_name, branch_name, cur_time):
        msg = "应用{}的版本{}的在{}接口扫描信息入库后超过{}条待删除的数据，需要人工排查！扫描结果查看app_mgt_interface_info_temp表和app_mgt_interface_scan_history表".format(
            module_name, branch_name, cur_time, INTERFACE_SCAN.get("interface_check_threshold"))
        logger.error(msg)
        # mail_builder = SendMail()
        # mail_builder.set_subject("接口扫描入spider库异常提醒")
        # mail_builder.set_content(
        #     "应用{}的版本{}的在{}接口扫描信息入库后超过{}条待删除的数据，需要人工排查！扫描结果查看app_mgt_interface_info_temp表和app_mgt_interface_scan_history表".format(
        #         module_name, branch_name, cur_time, INTERFACE_SCAN.get("interface_check_threshold")))
        # mail_builder.set_to("<EMAIL>")
        # mail_builder.send()

    def __init_app_interface_param(self, module_name, branch_name, interface_path, interface_method, interface_type):
        cur_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        base_branch_name = get_base_branch_name(module_name)
        if base_branch_name:
            obj = AppMgtInterfaceParamInfo.objects.filter(
                module_name=module_name, branch_name=base_branch_name,
                interface_path=interface_path, interface_method=interface_method,
                interface_type=interface_type).values('field_name', 'field_type',
                                                      'is_required', 'enum_values')

            for item in obj:
                new_data = {"module_name": module_name, "branch_name": branch_name, "interface_path": interface_path,
                            "interface_method": interface_method, "interface_type": interface_type,
                            "field_name": item.get('field_name'), "field_type": item.get("field_type"),
                            "is_required": item.get('is_required'), "enum_values": item.get("enum_values"),
                            "create_user": 'spider', "create_time": cur_time}
                AppMgtInterfaceParamInfo.objects.create(**new_data)

    def __create_interface_first(self, module_name, branch_name, interface_type, create_user):
        archive_branch_name = get_archive_branch_name_by_app(module_name)
        obj = AppMgtInterfaceInfo.objects.filter(module_name=module_name, branch_name=archive_branch_name,
                                                 interface_type=interface_type)
        if obj:
            batch_insert_interface_info(
                module_name, branch_name, archive_branch_name, create_user)

    def __insert_interface_temp(self, interface_info):
        interface_type = interface_info.get("type")
        branch_name = interface_info.get("version")
        module_name = interface_info.get("appName")
        interface_data_list = interface_info.get("data")
        content_type = interface_info.get(
            "content_type") if interface_info.get("content_type") else ""
        encryption = interface_info.get(
            "encryption") if interface_info.get("encryption") else ""
        operator = "interface_scan_agent"
        cur_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        for interface_data in interface_data_list:
            methods = interface_data.get("methods")
            if isinstance(methods, dict):
                methods = methods.get("methods")
            if not methods and interface_type.upper() == 'HTTP':
                methods = ["GET", "PUT", "POST", "DELETE"]
            logger.info(interface_data)
            dubbo_interface_path = interface_data.get(
                "interface").strip() if interface_data.get("interface") else ""
            http_interface_path_list = []
            if interface_data.get("paths"):
                for path in interface_data.get("paths"):
                    if not path.strip().endswith('.protobuf') and path.strip() != '/':
                        # 如果接口路径不以"/"开头，则补充
                        if not path.startswith("/"):
                            path = "/" + path
                        http_interface_path_list.append(path)
            dubbo_interface_name = dubbo_interface_path.split(
                ".")[-1] if dubbo_interface_path else ""

            if methods:
                if interface_type.upper() == 'DUBBO':
                    methods_list = methods.split(',')
                    for interface_method in methods_list:
                        create_version = self.__get_create_version(module_name, dubbo_interface_path,
                                                                   interface_method, interface_type, branch_name)
                        interface_info_temp_obj = AppMgtInterfaceInfoTemp.objects.filter(
                            module_name=module_name,
                            branch_name=branch_name,
                            interface_type=interface_type,
                            interface_method=interface_method,
                            interface_path=dubbo_interface_path)
                        if not interface_info_temp_obj:
                            AppMgtInterfaceInfoTemp.objects.update_or_create(module_name=module_name,
                                                                             branch_name=branch_name,
                                                                             interface_path=dubbo_interface_path,
                                                                             interface_method=interface_method,
                                                                             interface_type=interface_type,
                                                                             defaults={'create_version': create_version,
                                                                                       'interface_name': dubbo_interface_name,
                                                                                       'create_user': operator,
                                                                                       'status': 1,
                                                                                       'create_time': cur_time})
                # 排掉http扫描的结果中接口路径为"/"的数据
                elif interface_type.upper() == 'HTTP' and http_interface_path_list:
                    methodParameters = interface_data.get("methodParameters")
                    defines = interface_data.get("defines")
                    for http_interface_path in http_interface_path_list:
                        for interface_method in methods:
                            if not content_type:
                                obj = AppMgtInterfaceMap.objects.filter(module_name=module_name,
                                                                        interface_path=http_interface_path).values(
                                    'content_type')
                                if obj:
                                    content_type = obj[0].get('content_type') if obj[0].get(
                                        'content_type') else ""
                            create_version = self.__get_create_version(module_name, http_interface_path,
                                                                       interface_method, interface_type, branch_name)

                            http_interface_name = http_interface_path.split(
                                "/")[-1]
                            interface_info_temp_obj = AppMgtInterfaceInfoTemp.objects.filter(
                                module_name=module_name,
                                branch_name=branch_name,
                                interface_type=interface_type,
                                interface_method=interface_method,
                                interface_path=http_interface_path)
                            if not interface_info_temp_obj:
                                AppMgtInterfaceInfoTemp.objects.update_or_create(module_name=module_name,
                                                                                 branch_name=branch_name,
                                                                                 interface_path=http_interface_path,
                                                                                 interface_method=interface_method,
                                                                                 interface_type=interface_type,
                                                                                 defaults={
                                                                                     'create_version': create_version,
                                                                                     'interface_name': http_interface_name,
                                                                                     'content_type': content_type,
                                                                                     'encryption': encryption,
                                                                                     'request_params': methodParameters,
                                                                                     'defines_params': defines,
                                                                                     'create_user': operator,
                                                                                     'status': 1,
                                                                                     'create_time': cur_time})

    def __merge_interface_info(self, interface_info):
        interface_type = interface_info.get("type")
        branch_name = interface_info.get("version")
        module_name = interface_info.get("appName")

        interface_info_obj = AppMgtInterfaceInfo.objects.filter(module_name=module_name, branch_name=branch_name,
                                                                interface_type=interface_type)
        for interface_info in interface_info_obj:
            interface_info_temp_obj = None
            if interface_info.interface_type.upper() == 'DUBBO':
                interface_info_temp_obj = AppMgtInterfaceInfoTemp.objects.filter(module_name=interface_info.module_name,
                                                                                 branch_name=interface_info.branch_name,
                                                                                 interface_type=interface_info.interface_type,
                                                                                 interface_method=interface_info.interface_method,
                                                                                 interface_path=interface_info.interface_path)
            elif interface_info.interface_type.upper() == 'HTTP':
                interface_info_temp_obj = AppMgtInterfaceInfoTemp.objects.filter(module_name=interface_info.module_name,
                                                                                 branch_name=interface_info.branch_name,
                                                                                 interface_type=interface_info.interface_type,
                                                                                 interface_path=interface_info.interface_path)

            # 如果存在于主表，不存在临时表的数据，补充到临时表，状态为0
            if not interface_info_temp_obj:
                AppMgtInterfaceInfoTemp.objects.create(module_name=module_name, branch_name=branch_name,
                                                       interface_path=interface_info.interface_path,
                                                       interface_method=interface_info.interface_method,
                                                       interface_type=interface_info.interface_type,
                                                       create_version=interface_info.create_version,
                                                       interface_name=interface_info.interface_name,
                                                       content_type=interface_info.content_type,
                                                       encryption=interface_info.encryption,
                                                       request_params=interface_info.request_params,
                                                       defines_params=interface_info.defines_params,
                                                       create_user=interface_info.create_user, status=0,
                                                       create_time=interface_info.create_time,
                                                       update_user=interface_info.update_user,
                                                       update_time=interface_info.update_time)

    def __batch_insert_interface_info(self, module_name, branch_name, interface_type):
        try:
            with transaction.atomic():
                # 删除主表数
                AppMgtInterfaceInfo.objects.filter(module_name=module_name,
                                                   branch_name=branch_name, interface_type=interface_type).delete()
                # 批量将临时表数据插入主表
                batch_insert_interface_info_from_temp(
                    module_name, branch_name, interface_type)
                # 删除临时表数据
                AppMgtInterfaceInfoTemp.objects.filter(module_name=module_name,
                                                       branch_name=branch_name,
                                                       interface_type=interface_type).delete()
        except Exception as e:
            logger.error("批量操作接口信息失败")
            logger.error(str(e))

    current_locker = Lock()

    def create(self, request):
        try:
            self.current_locker.acquire()
            interface_info = request.data
            interface_type = interface_info.get("type")
            branch_name = interface_info.get("version")
            module_name = interface_info.get("appName")
            exec_batch = interface_info.get("exec_batch")
            logger.info(
                "interface_type: {},branch_name:{},module_name:{}".format(interface_type, branch_name, module_name))
            # dump请求报文
            self.dump_agent_api_request_message(exec_batch, interface_info, module_name, branch_name)
            operator = "interface_scan_agent"
            cur_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            if len(interface_info.get("data", [])) == 0:
                AgentMgtExecLog.objects.update_or_create(module_name=module_name,
                                                         branch=branch_name,
                                                         exec_batch=exec_batch,
                                                         agent_type="howbuy-interface-scan-agent",
                                                         status="success",
                                                         err_msg="agent上传的是空数据,仅做记录,不处理应用的存量接口数据！",
                                                         create_user="scm", create_time=datetime.datetime.now())
                return response.Response(
                    data=ApiResult.success_dict(msg="接口信息上报成功", data=exec_batch))

            # 防并发锁 20230303 by fwm
            obj = AppMgtInterfaceScanLock.objects.filter(module_name=module_name, branch_name=branch_name,
                                                         interface_type=interface_type).first()
            if obj and obj.is_lock == 1:
                return response.Response(data=ApiResult.failed_dict(msg="并发了，请稍后再试！", data=""))
            else:
                AppMgtInterfaceScanLock.objects.update_or_create(defaults={'is_lock': 1, 'create_time': cur_time},
                                                                 module_name=module_name, branch_name=branch_name,
                                                                 interface_type=interface_type)
            # 删除临时表数据
            AppMgtInterfaceInfoTemp.objects.filter(module_name=module_name,
                                                   branch_name=branch_name,
                                                   interface_type=interface_type).delete()

            obj = AppMgtInterfaceInfo.objects.filter(module_name=module_name, branch_name=branch_name,
                                                     interface_type=interface_type)
            # 首次创建用库存基准（归档版本）接口信息来批量创建
            if not obj:
                self.__create_interface_first(module_name=module_name, branch_name=branch_name,
                                              interface_type=interface_type, create_user=operator)

                obj = AppMgtInterfaceInfo.objects.filter(module_name=module_name, branch_name=branch_name,
                                                         interface_type=interface_type).values('module_name',
                                                                                               'branch_name',
                                                                                               'interface_path',
                                                                                               'interface_method',
                                                                                               'interface_type')
                for item in obj:
                    self.__init_app_interface_param(item.get('module_name'), item.get('branch_name'),
                                                    item.get('interface_path'),
                                                    item.get('interface_method'), item.get('interface_type'))
            # 扫描数据插入临时表
            self.__insert_interface_temp(interface_info)

            # 合并主表数据到临时表
            self.__merge_interface_info(interface_info)
            ready_to_delete_interface_count = 0
            if interface_type.upper() == 'DUBBO':
                ready_to_delete_interface_count = AppMgtInterfaceInfoTemp.objects.filter(module_name=module_name,
                                                                                         branch_name=branch_name,
                                                                                         interface_type=interface_type,
                                                                                         status=0).count()
            elif interface_type.upper() == 'HTTP':
                ready_to_delete_interface_count = AppMgtInterfaceInfoTemp.objects.filter(module_name=module_name,
                                                                                         branch_name=branch_name,
                                                                                         interface_type=interface_type,
                                                                                         status=0).values(
                    'interface_path').annotate(c=Count('interface_path')).count()
                logger.info("ready_to_delete_interface_count:{}".format(ready_to_delete_interface_count))
            if ready_to_delete_interface_count > int(INTERFACE_SCAN.get("interface_check_threshold")):
                self.__send_warn_email(module_name, branch_name, cur_time)
            else:
                self.__batch_insert_interface_info(
                    module_name, branch_name, interface_type)
            logger.info("开始数据稽核")
            self.audit_app_branch_api(module_name, branch_name)
            logger.info("数据稽核结束")
            cur_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            AppMgtInterfaceScanLock.objects.update_or_create(defaults={'is_lock': 0, 'update_time': cur_time},
                                                             module_name=module_name, branch_name=branch_name,
                                                             interface_type=interface_type)

            AgentMgtExecLog.objects.update_or_create(module_name=module_name,
                                                     branch=branch_name,
                                                     exec_batch=exec_batch,
                                                     agent_type="howbuy-interface-scan-agent",
                                                     status="success",
                                                     create_user="scm",
                                                     create_time=datetime.datetime.now())

            # AgentMgtPublishWarnLog.objects.filter(module_name=module_name,
            #                                       branch=branch_name,
            #                                       agent_type="howbuy-interface-scan-agent").update(status="已处理",
            #                                                                                        update_time=datetime.datetime.now())
            # 记录扫描结果记录 success
            return response.Response(data=ApiResult.success_dict(msg="接口信息导入成功！", data=exec_batch))
        except Exception as e:
            logger.error("接口信息导入异常，原因为：{}".format(traceback.format_exc()))
            cur_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            AppMgtInterfaceScanLock.objects.update_or_create(defaults={'is_lock': 0, 'update_time': cur_time},
                                                             module_name=module_name, branch_name=branch_name,
                                                             interface_type=interface_type)
            AgentMgtExecLog.objects.update_or_create(module_name=module_name,
                                                     branch=branch_name,
                                                     exec_batch=exec_batch,
                                                     agent_type="howbuy-interface-scan-agent",
                                                     status="error",
                                                     err_msg=str(e),
                                                     create_user="scm",
                                                     create_time=datetime.datetime.now())
            # AgentMgtPublishWarnLog.objects.filter(module_name=module_name,
            #                                       branch=branch_name,
            #                                       agent_type="howbuy-interface-scan-agent").update(
            #     status="接口解析处理异常",
            #     update_time=datetime.datetime.now())
            # 记录扫描结果记录 error
            return response.Response(
                data=ApiResult.failed_dict(msg="接口信息导入异常，原因为：{}".format(str(e)), data=exec_batch))
        finally:
            self.current_locker.release()

    def dump_agent_api_request_message(self, exec_batch, interface_info, module_name, branch_name):
        agent_request_message_log_dir = INTERFACE_SCAN[
                                            'agent_request_message_log_dir'] + os.sep + module_name + os.sep + branch_name
        if not os.path.exists(agent_request_message_log_dir):
            os.makedirs(agent_request_message_log_dir)
        request_message_path = agent_request_message_log_dir + os.sep + exec_batch + '.json'
        with open(request_message_path, 'w') as file_obj:
            file_obj.write(json.dumps(interface_info.get("data"), indent=4, ensure_ascii=False))

    # 处理api信息
    def audit_app_branch_api(self, module_name: str, iter_branch: str):
        api_doc_apis = []
        agent_apis = []
        api_doc_api_filter_param = {
            "module_name": module_name, "iter_branch": iter_branch, "retain": 'N'}
        api_doc_apis = AppMgtApidocInfo.objects.filter(**api_doc_api_filter_param)
        logger.info("api_doc_apis num :{}".format(len(api_doc_apis)))
        if not api_doc_apis:
            logger.info("no api_doc_apis return")
            return
        # 查询基准
        agent_api_filter_param = {
            "module_name": module_name, "branch_name": iter_branch, "status": 1}
        agent_apis = AppMgtInterfaceInfo.objects.filter(
            **agent_api_filter_param)
        logger.info("agent_apis num :{}".format(len(agent_apis)))
        if not agent_apis:
            logger.error("no agent_apis return")
            return
        agent_apis_basics = [d.interface_path +
                             d.interface_method for d in agent_apis]
        logger.info("api_doc_apis:{}".format(
            serializers.serialize('json', api_doc_apis)))
        logger.info("agent_apis_basics:{}".format(agent_apis_basics))
        invalid_apidoc_api_ids = []
        # 使用 itertools.groupby() 函数按照 path 字段分组
        grouped_apidocs_list = itertools.groupby(api_doc_apis, key=lambda x: x.api_type + ":" + x.api_path)
        for api, api_group in grouped_apidocs_list:
            exist_in_agent_api = False
            if 'DUBBO' in api.upper():
                for api_doc_api in api_group:
                    api_path_basic: str = api_doc_api.api_path + api_doc_api.api_method
                    logger.info("api_path_basic:{}".format(api_path_basic))
                    if api_path_basic in agent_apis_basics:
                        exist_in_agent_api = True
                    if not exist_in_agent_api:
                        invalid_apidoc_api_ids.append(str(api_doc_api.id))
            elif 'HTTP' in api.upper():
                self.calc_main_api(agent_api_filter_param, agent_apis_basics, api_group, invalid_apidoc_api_ids)
            elif not 'MQ' in api.upper():
                for api_doc_api in api_group:
                    invalid_apidoc_api_ids.append(str(api_doc_api.id))
        self.handle_mult_method_http_api(agent_api_filter_param)
        # 不再通过 interface 表中的数据 校正api doc 扫描的数据 by 帅 2024-04-16
        # if invalid_apidoc_api_ids:
        #     logger.info("记录无效的api_doc_api {} 并删除 开始".format(len(invalid_apidoc_api_ids)))
        #     bak_api_from_api_ids(invalid_apidoc_api_ids)
        #     bak_api_param_from_api_ids(invalid_apidoc_api_ids)
        #     del_api_and_param_from_api_ids(invalid_apidoc_api_ids)
        #     logger.info("记录无效的api_doc_api {} 并删除 结束".format(len(invalid_apidoc_api_ids)))
        #     try:
        #         mq = RocketMq()
        #         message = {'branch_name': iter_branch, 'module_name': module_name,
        #                    "idempotent_no": time.strftime("%Y%m%d%H%M%S")}
        #         mq.rocket_mq(
        #             message, ROCKETMQ['scan_interface_topic'], ROCKETMQ['scan_interface_namesrv'])
        #     except Exception as ex:
        #         logger.error(ex)
        # else:
        #     logger.info("无效的api_doc_api数量为0")

    def handle_mult_method_http_api(self, agent_api_filter_param):
        new_filter_param = {
            "module_name": agent_api_filter_param['module_name'], "branch_name": agent_api_filter_param['branch_name'],
            'interface_type': 'http', "status": 1}
        agent_apis = AppMgtInterfaceInfo.objects.filter(
            **new_filter_param).all()
        grouped_api_list = itertools.groupby(agent_apis, key=lambda x: x.interface_path)
        for api, api_group in grouped_api_list:
            unique_agent_api = []
            GET_API = None
            POST_API = None
            PUT_API = None
            DELETE_API = None
            for agent_api in api_group:
                unique_agent_api.append(agent_api)
                if agent_api.interface_method == 'GET':
                    GET_API = agent_api
                if agent_api.interface_method == 'POST':
                    POST_API = agent_api
                if agent_api.interface_method == 'PUT':
                    PUT_API = agent_api
                if agent_api.interface_method == 'DELETE':
                    DELETE_API = agent_api
            if len(unique_agent_api) > 1:
                logger.info(unique_agent_api)
                if GET_API:
                    new_filter_param['interface_path'] = GET_API.interface_path
                    AppMgtInterfaceInfo.objects.filter(
                        **new_filter_param).exclude(interface_method=GET_API.interface_method).update(
                        status='2')
                    logger.info("GET 生效:{}".format(GET_API.interface_path))
                elif POST_API:
                    new_filter_param['interface_path'] = POST_API.interface_path
                    AppMgtInterfaceInfo.objects.filter(
                        **new_filter_param).exclude(interface_method=POST_API.interface_method).update(
                        status='2')
                    logger.info("POST 生效:{}".format(POST_API.interface_path))
                elif PUT_API:
                    new_filter_param['interface_path'] = PUT_API.interface_path
                    AppMgtInterfaceInfo.objects.filter(
                        **new_filter_param).exclude(interface_method=PUT_API.interface_method).update(
                        status='2')
                    logger.info("PUT 生效:{}".format(PUT_API.interface_path))
                elif DELETE_API:
                    new_filter_param['interface_path'] = DELETE_API.interface_path
                    AppMgtInterfaceInfo.objects.filter(
                        **new_filter_param).exclude(interface_method=DELETE_API.interface_method).update(
                        status='2')
                    logger.info("DELETE 生效:{}".format(DELETE_API.interface_path))

    def calc_main_api(self, agent_api_filter_param, agent_apis_basics, api_group, invalid_apidoc_api_ids):
        new_filter_param = {
            "module_name": agent_api_filter_param['module_name'], "branch_name": agent_api_filter_param['branch_name'],
            "status": 1}
        GET_API = None
        POST_API = None
        PUT_API = None
        DELETE_API = None
        api_apth = None
        for api_doc_api in api_group:
            api_apth = api_doc_api.api_path
            if api_doc_api.api_method == 'GET':
                api_path_basic: str = api_doc_api.api_path + api_doc_api.api_method
                if api_path_basic in agent_apis_basics or ("/" + api_path_basic) in agent_apis_basics:
                    GET_API = api_doc_api
            if api_doc_api.api_method == 'POST':
                api_path_basic: str = api_doc_api.api_path + api_doc_api.api_method
                if api_path_basic in agent_apis_basics or ("/" + api_path_basic) in agent_apis_basics:
                    POST_API = api_doc_api
            if api_doc_api.api_method == 'PUT':
                api_path_basic: str = api_doc_api.api_path + api_doc_api.api_method
                if api_path_basic in agent_apis_basics or ("/" + api_path_basic) in agent_apis_basics:
                    PUT_API = api_doc_api
            if api_doc_api.api_method == 'DELETE':
                api_path_basic: str = api_doc_api.api_path + api_doc_api.api_method
                if api_path_basic in agent_apis_basics or ("/" + api_path_basic) in agent_apis_basics:
                    DELETE_API = api_doc_api
        logger.info(
            "api_apth:{},GET:{},POST:{},PUT:{},DELETE:{}".format(api_apth, GET_API, POST_API,
                                                                 PUT_API, DELETE_API))
        if GET_API:
            new_filter_param['interface_path'] = GET_API.api_path
            AppMgtInterfaceInfo.objects.filter(
                **new_filter_param).exclude(interface_method=GET_API.api_method).update(
                status='2')
            logger.info("GET 生效:{}".format(GET_API.api_path))
        elif POST_API:
            new_filter_param['interface_path'] = POST_API.api_path
            AppMgtInterfaceInfo.objects.filter(
                **new_filter_param).exclude(interface_method=POST_API.api_method).update(
                status='2')
            logger.info("POST 生效:{}".format(POST_API.api_path))
        elif PUT_API:
            new_filter_param['interface_path'] = PUT_API.api_path
            AppMgtInterfaceInfo.objects.filter(
                **new_filter_param).exclude(interface_method=PUT_API.api_method).update(
                status='2')
            logger.info("PUT 生效:{}".format(PUT_API.api_path))
        elif DELETE_API:
            new_filter_param['interface_path'] = DELETE_API.api_path
            AppMgtInterfaceInfo.objects.filter(
                **new_filter_param).exclude(interface_method=DELETE_API.api_method).update(
                status='2')
            logger.info("DELETE 生效:{}".format(DELETE_API.api_path))
        else:
            invalid_apidoc_api_ids.append(str(api_doc_api.id))

    def list(self, request):
        """
        获取接口列表信息，非APIDOC，不含参数
        """

        module_name = request.query_params.get("app_name")
        branch_name = request.query_params.get("branch_name")
        interface_path = request.query_params.get("interface_path")
        interface_method = request.query_params.get("interface_method")
        interface_type = request.query_params.get("interface_type")
        filter_param = {"status": 1}
        if module_name:
            filter_param['module_name'] = module_name
        if branch_name:
            filter_param['branch_name'] = branch_name
        if interface_path:
            filter_param['interface_path'] = interface_path
        if interface_method:
            filter_param['interface_method'] = interface_method
        if interface_type:
            filter_param['interface_type'] = interface_type

        obj = AppMgtInterfaceInfo.objects.filter(**filter_param)

        api_info = []
        for item in obj:
            result = {"app_name": item.module_name, "branch_name": item.branch_name,
                      "interface_name": item.interface_name, "interface_path": item.interface_path,
                      "interface_name_dev": item.interface_name_dev,
                      "interface_method": item.interface_method, "interface_type": item.interface_type,
                      "content_type": item.content_type, "encryption": item.encryption,
                      "status": item.status, "create_version": item.create_version,
                      "create_user": item.create_user, "create_time": item.create_time,
                      "update_user": item.update_user, "update_time": item.update_time}
            api_info.append(result)
        return response.Response(data=ApiResult.success_dict(msg="查询应用{}版本{}的接口信息成功！".
                                                             format(module_name, branch_name), data=api_info))

class AppInterfaceParamsApi(viewsets.ViewSet):
    authentication_classes = []

    def list(self, request):
        interface_info = request.query_params
        module_name = interface_info.get("app_name")
        branch_name = interface_info.get("branch_name")
        interface_path = interface_info.get("interface_path")
        interface_method = interface_info.get("interface_method")
        interface_type = interface_info.get("interface_type")

        filter_data = {"module_name": module_name, "interface_path": interface_path,
                       "interface_method": interface_method, "interface_type": interface_type,
                       "branch_name": branch_name}

        obj = AppMgtInterfaceParamInfo.objects.filter(**filter_data). \
            values('field_name', 'field_type', 'is_required', 'enum_values')

        params_list = []
        for item in obj:
            params_data = {"fieldName": item.get('field_name'), "fieldType": item.get('field_type'),
                           "required": item.get('is_required'), "enumValues": item.get('enum_values')}
            params_list.append(params_data)
        return_data = {"module_name": module_name, "interface_path": interface_path,
                       "interface_method": interface_method, "interface_type": interface_type,
                       "params_list": params_list}

        return response.Response(data=ApiResult.success_dict(msg="查询接口参数信息成功！", data=return_data))

    def create(self, request):
        interface_info = request.data

        module_name = interface_info.get("app_name")
        branch_name = interface_info.get("branch_name")
        interface_path = interface_info.get("interface_path")
        interface_method = interface_info.get("interface_method")
        interface_type = interface_info.get("interface_type")
        param_list = interface_info.get("param_list")
        user = interface_info.get("user")
        cur_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        for item in param_list:
            field_name = item.get("filedName")
            field_type = item.get("fieldType")
            is_required = item.get("required")
            enum_values = item.get("enumVaules")

            filter_data = {"module_name": module_name, "branch_name": branch_name,
                           "interface_path": interface_path, "interface_method": interface_method,
                           "interface_type": interface_type, "field_name": field_name}

            new_data = {'module_name': module_name, 'branch_name': branch_name, 'interface_path': interface_path,
                        'interface_method': interface_method, 'interface_type': interface_type,
                        'field_name': field_name, 'field_type': field_type, 'is_required': is_required,
                        'enum_values': enum_values, 'create_user': user, 'create_time': cur_time}

            update_data = {'field_name': field_name, 'field_type': field_type,
                           'is_required': is_required, 'enum_values': enum_values,
                           'update_user': user, 'update_time': cur_time}
            obj = AppMgtInterfaceParamInfo.objects.filter(**filter_data)
            if obj:
                obj.update(**update_data)
            else:
                AppMgtInterfaceParamInfo.objects.create(**new_data)

        return response.Response(data=ApiResult.success_dict(msg="更新接口参数信息成功！", data={}))


class AppInterfaceApi(viewsets.ViewSet):
    authentication_classes = []

    def create(self, request):
        interface_info = request.data

        module_name = interface_info.get("app_name")
        branch_name = interface_info.get("branch_name")
        interface_path = interface_info.get("interface_path")
        interface_method = interface_info.get("interface_method")
        interface_type = interface_info.get("interface_type")
        user = interface_info.get("user")
        cur_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        edit_type = interface_info.get("edit_type")
        main_filter_data = {"module_name": module_name, "branch_name": branch_name, "interface_path": interface_path,
                            "interface_method": interface_method, "interface_type": interface_type}
        if edit_type == 'test':
            interface_name = interface_info.get("edit_interface_name_params")
            update_data = {'interface_name': interface_name,
                           'update_user': user, 'update_time': cur_time}
        elif edit_type == 'dev':
            interface_name = interface_info.get("edit_interface_name_params")
            update_data = {'interface_name_dev': interface_name,
                           'update_user': user, 'update_time': cur_time}
        else:
            return response.Response(data=ApiResult.failed_dict(msg="请编辑接口类型"))
        try:
            main_obj = AppMgtInterfaceInfo.objects.filter(**main_filter_data)
            if main_obj:
                main_obj.update(**update_data)
        except Exception as e:
            logger.error(str(e))
        return response.Response(data=ApiResult.success_dict(msg="更新接口名成功！", data={}))


class AppBranchInfoExistApi(viewsets.ViewSet):
    authentication_classes = []

    def list(self, request):
        obj_list = AppMgtInterfaceInfo.objects.filter().values(
            'module_name', 'branch_name').distinct()
        new_dat_list = []
        for obj in obj_list:
            new_dat_list.append(obj)

        return response.Response(data=ApiResult.success_dict(msg="查询成功！", data=new_dat_list))


class AppBranchInfoExistApiForMring(viewsets.ViewSet):
    """
    给外部java服务的接口，返回参数用驼峰
    """
    authentication_classes = []

    def list(self, request):
        obj_list = AppMgtInterfaceInfo.objects.filter().values(
            'module_name', 'branch_name').distinct()
        new_dat_list = []
        for obj in obj_list:
            request = {"appName": obj.get(
                "module_name"), "branchName": obj.get("branch_name")}
            new_dat_list.append(request)

        return response.Response(data=ApiResult.success_dict(msg="查询成功！", data=new_dat_list))


class AppBranchInfoApi(viewsets.ViewSet):
    """
    根据应用名查询分支列表
    """
    authentication_classes = []

    def list(self, request):
        module_name = request.GET['module_name']
        obj_list = AppMgtInterfaceInfo.objects.filter(
            module_name=module_name).values('branch_name').distinct()
        new_dat_list = []
        for obj in obj_list:
            new_dat_list.append(obj)

        return response.Response(data=ApiResult.success_dict(msg="查询成功！", data=new_dat_list))

if __name__ == "__main__":
    module_name = 'otc-center-remote'
    branch_name = '6.7.5'
    api_info = []
    obj = get_app_interface_info(module_name, branch_name)
    # obj = AppMgtInterfaceInfo.objects.filter(**filter_param)
    if not obj:
        obj = get_app_interface_info(module_name, branch_name)
        # obj = AppMgtInterfaceInfo.objects.filter(**filter_param)
    for item in obj:
        result = {"appName": item.module_name, "branchName": branch_name,
                  "interfaceName": item.interface_name, "interfacePath": item.interface_path,
                  "interfaceMethod": item.interface_method, "interfaceType": item.interface_type,
                  "contentType": item.content_type, "encryption": item.encryption,
                  "status": item.status, "createVersion": item.create_version,
                  "originVersion": branch_name,
                  "createUser": item.create_user, "createTime": item.create_time,
                  "updateUser": item.update_user, "updateTime": item.update_time, "origin": "agent"}
        api_info.append(result)
    logger.info("start get_api_doc_api")
