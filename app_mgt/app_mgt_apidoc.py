from rest_framework import viewsets, response

from app_mgt.app_mgt_ser import get_archive_branch_name_by_app_before_date, \
    get_app_interface_info
from app_mgt.models import AppMgtApidocInfo, AppMgtApidocParamInfo, \
    AppMgtInterfaceQueryExtraAppInfo
from spider.settings import <PERSON><PERSON><PERSON><PERSON><PERSON>, logger, INTERFACE_SCAN


class AppMgtInterfaceApiForMring(viewsets.ViewSet):
    """
    给外部java服务的接口，返回参数用驼峰
    """

    authentication_classes = []


    def list(self, request):
        """
        获取接口列表信息，不含参数
        """
        logger.info("start agent apis")
        module_name = request.query_params.get("app_name")
        branch_name = request.query_params.get("branch_name")
        interface_path = request.query_params.get("interface_path")
        interface_method = request.query_params.get("interface_method")
        interface_type = request.query_params.get("interface_type")
        filter_param = {}
        if module_name:
            filter_param['module_name'] = module_name
        if branch_name:
            filter_param['branch_name'] = branch_name
        if interface_path:
            filter_param['interface_path'] = interface_path
        if interface_method:
            filter_param['interface_method'] = interface_method
        if interface_type:
            filter_param['interface_type'] = interface_type
        archive_branch_name = branch_name
        logger.info("start AppMgtInterfaceQueryExtraAppInfo")
        need_extra_app_branch = AppMgtInterfaceQueryExtraAppInfo.objects.filter(
            **filter_param)
        if not need_extra_app_branch:
            logger.info("start get_archive_branch_name_by_app_before_date")
            archive_branch_name = get_archive_branch_name_by_app_before_date(module_name, INTERFACE_SCAN[
                "agent_api_archive_bf_date"])
        api_info = []
        filter_param['status'] = 1
        if archive_branch_name:
            # filter_param['branch_name'] = archive_branch_name
            # obj = AppMgtInterfaceInfo.objects.filter(**filter_param)
            obj = get_app_interface_info(module_name, archive_branch_name, interface_path, interface_method,
                                         interface_type)
            if not obj:
                # filter_param['branch_name'] = branch_name
                # obj = AppMgtInterfaceInfo.objects.filter(**filter_param)
                obj = get_app_interface_info(module_name, branch_name, interface_path, interface_method, interface_type)
            for item in obj:
                result = {"appName": item.get('module_name'), "branchName": branch_name,
                          "interfaceName": item.get('interface_name'), "interfacePath": item.get('interface_path'),
                          "interfaceMethod": item.get('interface_method'), "interfaceType": item.get('interface_type'),
                          "contentType": item.get('content_type'), "encryption": item.get('encryption'),
                          "status": item.get('status'), "createVersion": item.get('create_version'),
                          "originVersion": archive_branch_name,
                          "createUser": item.get('create_user'), "createTime": item.get('create_time'),
                          "updateUser": item.get('update_user'), "updateTime": item.get('update_time'),
                          "origin": "agent"}
                api_info.append(result)
        logger.info("start get_api_doc_api")
        api_doc_apis = self.__get_api_doc_api(module_name, branch_name)
        logger.info("start merge_api")
        api_info = self.__merge_api(api_info, api_doc_apis)
        logger.info("end query")
        return response.Response(data=ApiResult.success_dict(msg="查询应用{}版本{}的接口信息成功！".
                                                             format(module_name, branch_name), data=api_info))

    def __get_api_doc_api(self, module_name, branch_name):
        api_info_list = []
        # api_filter_param = {"status": 1}
        api_filter_param = {}
        if module_name:
            api_filter_param['module_name'] = module_name
        if branch_name:
            api_filter_param['iter_branch'] = branch_name
        logger.info("start api_info_from_doc")
        api_info_from_doc = AppMgtApidocInfo.objects.filter(**api_filter_param)
        logger.info("start api_param_info_from_doc")
        api_param_info_from_doc = AppMgtApidocParamInfo.objects.raw(
            "select param.* from app_mgt_apidoc_info api  join app_mgt_apidoc_param_info param on api.id=param.api_id  where"
            "  api.module_name=%s and api.iter_branch=%s and api.status=1 and param.param_type='header_param'",
            [module_name, branch_name])
        # api_param_info = AppMgtApiParamInfo.objects.raw(
        #     "select param.* from app_mgt_api_info api  join app_mgt_apidoc_param_info param on api.id=param.api_id  "
        #     "where  api.module_name=%s and api.iter_branch=%s and api.status=1",
        #     [module_name, branch_name])
        logger.info("end api_param_info_from_doc")
        for api_info in api_info_from_doc:
            if api_info.api_type.upper() == 'HTTPS':
                api_info.api_type = "http"
                api_path = api_info.api_path
            else:
                api_path = api_info.api_path
            contentType, encryption, header = self.__get_extra_param(
                api_param_info_from_doc, api_info.id)
            # param_list = self.get_interface_param(api_param_info, api_info.id)
            result = {"appName": api_info.module_name, "branchName": api_info.iter_branch,
                      "interfaceName": api_info.api_name, "interfacePath": api_path,
                      "extendedData": {"httpHeader": header},
                      "interfaceMethod": api_info.api_method, "interfaceType": api_info.api_type,
                      "apiMethodSignature": api_info.api_method_signature,
                      "contentType": contentType, "encryption": encryption,
                      "status": api_info.status,
                      "createUser": api_info.create_user, "createTime": api_info.create_time,
                      "updateUser": api_info.update_user, "updateTime": api_info.update_time,
                      "origin": "api_doc"}
            api_info_list.append(result)
        logger.info("end get_api_doc_api")
        return api_info_list

    def __get_extra_param(self, api_param_filter_param, api_id):
        contentType = None
        encryption = None
        header = {}
        for param in api_param_filter_param:
            if param.api_id == api_id:
                header[param.param_name] = param.param_default_value
            if param.api_id == api_id and param.param_name == "content-type":
                contentType = param.param_default_value
            if param.api_id == api_id and param.param_name == "encryption":
                encryption = param.param_default_value
        # 尝试优化apidoc中的content-type大小写问题。zt@2025-06-11
        # if not header.get("content-type"):
        if not (header.get("content-type")
                or header.get("Content-Type")
                or header.get("CONTENT-TYPE")):
            header["content-type"] = INTERFACE_SCAN['default_content_type']
        return contentType, encryption, header

    def get_interface_param(self, api_param_filter_param, api_id):
        param_list = []
        for param in api_param_filter_param:
            if param.api_id == api_id:
                param_list.append({'param_type': param.param_type, 'param_name': param.param_name,
                                   'param_default_value': param.param_default_value})
        return param_list

    # 计算最终api
    def __merge_api(self, api_info, api_doc_apis):
        # 如果没有api_doc接口
        if not api_doc_apis:
            api_doc_apis = []
        # 如果有api_doc_apis 看全不全
        only_in_agent_apis = []
        for agent_api in api_info:
            exist = False
            for api_doc_api in api_doc_apis:
                if api_doc_api["interfaceType"].upper() == 'HTTP' and api_doc_api["interfacePath"] == agent_api[
                    "interfacePath"]:
                    exist = True
                    if not api_doc_api["contentType"]:
                        api_doc_api["contentType"] = agent_api["contentType"]
                    if not api_doc_api["encryption"]:
                        api_doc_api["encryption"] = agent_api["encryption"]
                if api_doc_api["interfaceType"].upper() == 'DUBBO' and api_doc_api["interfacePath"] == agent_api[
                    "interfacePath"] and api_doc_api["interfaceMethod"] == agent_api["interfaceMethod"]:
                    exist = True
                    if not api_doc_api["contentType"]:
                        api_doc_api["contentType"] = agent_api["contentType"]
                    if not api_doc_api["encryption"]:
                        api_doc_api["encryption"] = agent_api["encryption"]
            if not exist:
                if agent_api["contentType"]:
                    agent_api["extendedData"] = {"httpHeader": {"content-type": agent_api["contentType"]}}
                else:
                    agent_api["extendedData"] = {"httpHeader": {"content-type": INTERFACE_SCAN['default_content_type']}}
                only_in_agent_apis.append(agent_api)
        # 补全agent api独有的api,也就是api_doc没写的api
        api_doc_apis.extend(only_in_agent_apis)
        return api_doc_apis

class AppMgtInterfaceApiParamForMring(viewsets.ViewSet):
    authentication_classes = []
    """
    给外部java服务的接口，返回参数用驼峰
    """

    def list(self, request):
        """
        获取应用接口信息，含参数
        """
        logger.info("start agent apis")
        module_name = request.query_params.get("app_name")
        branch_name = request.query_params.get("branch_name")
        interface_path = request.query_params.get("interface_path")
        interface_method = request.query_params.get("interface_method")
        interface_type = request.query_params.get("interface_type")
        need_response_param = request.query_params.get("need_response_param")
        filter_param = {}
        if module_name:
            filter_param['module_name'] = module_name
        if branch_name:
            filter_param['iter_branch'] = branch_name
        if interface_path:
            filter_param['api_path__contains'] = interface_path
        if interface_method:
            filter_param['api_method'] = interface_method
        if interface_type:
            filter_param['api_type'] = interface_type
        logger.info("start AppMgtInterfaceQueryExtraAppInfo")
        filter_param['status'] = 1
        api_doc_apis = self.__get_api_doc_api(filter_param, need_response_param)
        logger.info("start merge_api")
        logger.info("end query")
        return response.Response(data=ApiResult.success_dict(msg="查询应用{}版本{}的接口信息成功！".
                                                             format(module_name, branch_name), data=api_doc_apis))

    def __get_api_doc_api(self, filter_param, need_response_param=None):
        api_info_list = []
        logger.info("start api_info_from_doc")
        api_info_from_doc = AppMgtApidocInfo.objects.filter(**filter_param)
        logger.info("start api_param_info_from_doc")
        sql = "select param.* from app_mgt_apidoc_info api  join app_mgt_apidoc_param_info param on api.id=param.api_id where  api.module_name=%s and api.iter_branch=%s and api.status=1"
        params = [filter_param.get('module_name'), filter_param.get('iter_branch')]
        if filter_param.get('api_path__contains'):
            if need_response_param:
                sql += " AND api.api_path like %s"
            else:
                sql += " AND param_type ='request_param' and api.api_path like %s"
            params.append('%'+filter_param.get('api_path__contains')+'%')

        logger.info(sql)

        api_param_info = AppMgtApidocParamInfo.objects.raw(sql, params)
        logger.info("end api_param_info_from_doc")
        for api_info in api_info_from_doc:
            if api_info.api_type.upper() == 'HTTPS':
                api_info.api_type = "http"
                api_path = api_info.api_path
            else:
                api_path = api_info.api_path
            param_list = self.__get_interface_param(api_param_info, api_info.id)
            result = {"appName": api_info.module_name, "branchName": api_info.iter_branch,
                      "interfaceName": api_info.api_name, "interfacePath": api_path,
                      "interfaceMethod": api_info.api_method, "interfaceType": api_info.api_type,
                      "status": api_info.status,
                      "createUser": api_info.create_user, "createTime": api_info.create_time,
                      "updateUser": api_info.update_user, "updateTime": api_info.update_time,
                      "param_list": param_list}
            api_info_list.append(result)
        logger.info("end get_api_doc_api")
        return api_info_list

    def __get_interface_param(self, api_param_filter_param, api_id):
        param_list = []
        for param in api_param_filter_param:
            if param.api_id == api_id:
                param_list.append({'param_type': param.param_type, 'param_name': param.param_name,
                                   'param_default_value': param.param_default_value,
                                   'param_desc': param.param_desc,
                                   'param_allowed_values': param.param_allowed_values,
                                   'param_size': param.param_size,
                                   'optional': param.optional,
                                   'param_value_type': param.param_value_type})
        return param_list


if __name__ == "__main__":
    module_name = 'otc-center-remote'
    branch_name = '6.7.5'
    api_info = []
    obj = get_app_interface_info(module_name, branch_name)
    # obj = AppMgtInterfaceInfo.objects.filter(**filter_param)
    if not obj:
        obj = get_app_interface_info(module_name, branch_name)
        # obj = AppMgtInterfaceInfo.objects.filter(**filter_param)
    for item in obj:
        result = {"appName": item.module_name, "branchName": branch_name,
                  "interfaceName": item.interface_name, "interfacePath": item.interface_path,
                  "interfaceMethod": item.interface_method, "interfaceType": item.interface_type,
                  "contentType": item.content_type, "encryption": item.encryption,
                  "status": item.status, "createVersion": item.create_version,
                  "originVersion": branch_name,
                  "createUser": item.create_user, "createTime": item.create_time,
                  "updateUser": item.update_user, "updateTime": item.update_time, "origin": "agent"}
        api_info.append(result)
    logger.info("start get_api_doc_api")
