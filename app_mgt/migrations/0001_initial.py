# Generated by Django 3.1.2 on 2021-03-04 15:51

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [

        migrations.CreateModel(
            name='NacosNamespaceInfo',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('module_name', models.Char<PERSON>ield(max_length=255, verbose_name='标准统一模块名')),
                ('namespace', models.CharField(max_length=255, verbose_name='nacos的命名空间')),
            ],
            options={
                'verbose_name': '应用和nacos命名空间的对应关系',
                'db_table': 'app_mgt_nacos_namespace_info',
            },
        ),
    ]
