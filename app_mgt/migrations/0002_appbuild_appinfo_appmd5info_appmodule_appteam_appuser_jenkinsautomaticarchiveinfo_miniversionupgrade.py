# Generated by Django 3.2 on 2022-03-11 17:53

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app_mgt', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='MiniVersionUpgradeModule',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('module_name', models.CharField(max_length=255, verbose_name='标准统一模块名')),
            ],
            options={
                'verbose_name': '需要小版本更新的模块',
                'db_table': 'app_mgt_mini_version_upgrade_module',
            },
        )
    ]
