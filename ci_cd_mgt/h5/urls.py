from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

from ci_cd_mgt.h5 import view, suite_info_view, h5_stats, flow_view, nf_view, app_view

router = DefaultRouter()

router.register(r'h5_compile_api', view.H5CompileApi, basename="h5_compile_api")
router.register(r'h5_mini_compile_api', view.H5MiniCompileApi, basename="h5_mini_compile_api")
router.register(r'h5_remote_test_beta_prod_compile_api',
                view.H5RemoteTestBetaProdCompileApi,
                basename="h5_remote_test_beta_prod_compile_api")
router.register(r'h5_auto_compile_api', view.H5AutoCompileApi, basename="h5_auto_compile_api")
router.register(r'h5_test_publish_api', view.H5TestPublishApi, basename="h5_test_publish_api")
router.register(r'h5_publish_apply_api', view.H5PublishApplyApi, basename="h5_publish_apply_api")
router.register(r'h5_ci_pipeline_api', view.H5CIPipelineApi, basename="h5_ci_pipeline_api")
router.register(r'app_suite_code_api', suite_info_view.AppSuiteCodeApi, basename="app_suite_code_api")
router.register(r'platform_suite_code_api', suite_info_view.PlatformSuiteCodeApi, basename="platform_suite_code_api")
router.register(r'pipeline_env_bind', view.PipelineEnvBind, basename="pipeline_env_bind")
router.register(r'h5_branch_file', view.H5BranchFile, basename="h5_branch_file")
router.register(r'h5_pipeline_stop', view.H5PipelineStop, basename="h5_pipeline_stop")
router.register(r'hd_status_check_api', flow_view.HdStatusCheckApi, basename="hd_status_check_api")
router.register(r'h5_elapse_stats', h5_stats.H5ElapseStatsApi, basename="h5_elapse_stats")
router.register(r'h5_app_bind_nf_app_api', nf_view.H5AppBindNfAppApi, basename="h5_app_bind_nf_app_api")
router.register(r'h5_nf_app_status_api', nf_view.H5NfAppStatuApi, basename="h5_app_bind_nf_app_api")
router.register(r'findH5ZipVersionByEnv', view.FindH5ZipVersionByEnv, basename="findH5ZipVersionByEnv")
router.register('findH5ZipVersionByEnvForPlatform', view.FindH5ZipVersionByEnvForPlatform, basename="findH5ZipVersionByEnvForPlatform")
router.register(r'findH5HDDistVersionByEnv', view.FindH5HDDistVersionByEnv, basename="findH5HDDistVersionByEnv")
router.register(r'appTestPublishApi', app_view.AppTestPublishApi, basename="appTestPublishApi")
router.register(r'appPublishApplyApi', app_view.AppPublishApplyApi, basename="appPublishApplyApi")
router.register(r'app_tag_test_publish_api', app_view.AppTagTestPublishApi, basename="app_tag_test_publish_api")
router.register(r'app_tag_publish_apply_api', app_view.AppTagPublishApplyApi, basename="app_tag_publish_apply_api")
router.register(r'get_last_apply_info_api', view.GetLastApplyInfoByBranchAndEnv, basename="get_last_apply_info_api")
router.register(r'h5_jenkins_info_api', view.H5JenkinsInfoApi, basename="h5_jenkins_info_api")
router.register(r'findTestSuiteCodeOfH5Zip', view.findTestSuiteCodeOfH5Zip, basename="findTestSuiteCodeOfH5Zip")
router.register(r'mobile_branch_status', view.mobile_branch_status, basename="mobile_branch_status")
router.register(r'notice_apply_by_wechat', view.notice_apply_by_wechat, basename="notice_apply_by_wechat")
router.register(r'get_last_prod_android_info', view.GetLastProdAndroidInfo, basename="get_last_prod_android_info")
router.register(r'h5_publish_apply_status', nf_view.H5PublishApplyStatus, basename="h5_publish_apply_status")
router.register(r'get_last_prod_dist_info', view.GetLastProdDistInfo, basename="get_last_prod_dist_info")
router.register(r'get_last_prod_dist_app_info', view.GetLastProdDistAppInfo, basename="get_last_prod_dist_app_info")
router.register(r'get_test_publish_dist_info', view.GetTestPublishDistInfo, basename="get_test_publish_dist_info")

urlpatterns = [
    path("", include(router.urls))
]
