#
import re
import datetime
from rest_framework import viewsets
from rest_framework.response import Response
from spider.settings import ApiResult
from task_mgt.models import HmOptLogMain


class H5ElapseStatsApi(viewsets.ViewSet):
    """
       h5耗时统计
    """

    @staticmethod
    def second_to_readable_time(sec):
        return "{:0>8}".format(str(datetime.timedelta(seconds=sec)))

    def list(self, request):
        app_name = request.GET.get("app_name")
        action_type = request.GET.get("action_type", "")
        br_name = request.GET.get("br_name", "")
        last_obj = HmOptLogMain.objects.filter(app_name__contains=app_name, exec_type=action_type,
                                               branch_name=br_name, status='success').last()
        last_elapsed = '-'
        current_elapsed = '准备中'
        last_id = 0
        if last_obj:
            last_elapsed = self.second_to_readable_time(last_obj.elapsed)
            last_id = last_obj.id
        current_obj = HmOptLogMain.objects.filter(pk__gt=last_id, app_name__contains=app_name, branch_name=br_name,
                                                  exec_type=action_type, status='running').last()
        if current_obj:
            date = datetime.datetime.now()
            elapsed = date - current_obj.start_time
            current_elapsed = re.sub(r'\.\d*', '', str(elapsed))
        data = {
            'last_elapsed': last_elapsed,
            'current_elapsed': current_elapsed
        }
        return Response(data=ApiResult.success_dict(msg="", data=data))
