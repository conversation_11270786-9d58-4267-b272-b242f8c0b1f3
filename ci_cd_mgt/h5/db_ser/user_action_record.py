from django.db import connection

from spider.settings import logger


def get_apply_user_and_time(app_name, iteration_id):
    sql = """
    SELECT
        vv.username,
        vv.operate_time 
        FROM(
            SELECT
                * 
            FROM
                user_action_record r 
            WHERE
                r.action_value LIKE '%{iteration_id}%' 
                AND r.action_item = 'publish_apply' 
                AND r.action_value LIKE "%'{app_name}'%"
                AND (
                    INSTR(r.action_value,'pre') > 0 
                    or INSTR(r.action_value,'beta') > 0 
                    or INSTR(r.action_value,'zb') > 0 
                    or INSTR(r.action_value,'wgq-hd') > 0
                )  
                    ) vv 
            WHERE vv.operate_time = ( 
                SELECT
                     max(operate_time)
                FROM
                    user_action_record r 
                WHERE
                    r.action_value LIKE '%{iteration_id}%' 
                    AND r.action_item = 'publish_apply' 
                    AND r.action_value LIKE "%'{app_name}'%"
                    AND (
                        INSTR(r.action_value,'pre') > 0 
                        or INSTR(r.action_value,'beta') > 0 
                        or INSTR(r.action_value,'beta') > 0 
                        or INSTR(r.action_value,'wgq-hd') > 0
                    )  
                    )""".format(iteration_id=iteration_id, app_name=app_name)

    logger.info(sql)
    cursor = connection.cursor()
    cursor.execute(sql)
    result = {}
    for row in cursor.fetchall():
        result = {"user_name": row[0], "operate_time": row[1]}

    return result
