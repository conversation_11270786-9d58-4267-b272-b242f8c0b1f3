import datetime

from iter_mgt.models import BranchIncludeSys
from spider.settings import logger


class IterMgtAppInfo:
    sys_status_dict = {
        "test": "测试中",
        "vph": "灰度中",
        "vps": "灰度中",
        "pre": "灰度中",
        "wgq-hd": "灰度中",
        "prod": "上线中"
    }

    @staticmethod
    def update_sys_status(request_data):

        sql_where = 'pipeline_id = "{}" and appName in ("{}")'.format(request_data['iteration_id'],
                                                                      ",".join(request_data['app_name_list']))
        logger.info(sql_where)
        res = BranchIncludeSys.objects.extra(where=[sql_where]).update(
            sys_status=IterMgtAppInfo.sys_status_dict[request_data['env']])
        return res
