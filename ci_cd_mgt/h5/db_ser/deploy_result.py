import datetime

from ci_cd_mgt.h5.enums import DeployBaseStatusEnum
from spider.settings import logger
from task_mgt.models import H5DeployResult


class DeployResult:

    op_type_to_status = {
        "test_publish": {"running": H5DeployResult.RUNNING, "failure": H5DeployResult.FAILURE,
                         "success": H5DeployResult.SUCCESS},
        "publish_apply": {"running": H5DeployResult.P_RUNNING, "failure": H5DeployResult.P_FAILURE,
                          "success": H5DeployResult.P_SUCCESS},
        "hd_publish": {"running": H5DeployResult.RUNNING, "failure": H5DeployResult.FAILURE,
                       "success": H5DeployResult.SUCCESS},
        "pro_publish": {"running": H5DeployResult.RUNNING, "failure": H5DeployResult.FAILURE,
                        "success": H5DeployResult.SUCCESS},
    }

    @staticmethod
    def insert_status_data(user, request_data):
        """新增running状态，返回True or False"""
        res = H5DeployResult.objects.update_or_create(
            iteration_id=request_data['iterationID'],
            app_name=request_data['appName'],
            action_id=request_data['actionId'],
            op_user=user,
            op_time=datetime.datetime.now(),
            op_type=request_data['actionItem'],
            suite_name=request_data['appEnv'],
            status=DeployResult.op_type_to_status["test_publish"][DeployBaseStatusEnum.BASE_STATUS.running])
        return res[1], res[0].id

    @staticmethod
    def insert_result_data(request_data):
        """新增running状态，返回True or False"""
        res = H5DeployResult.objects.update_or_create(
            iteration_id=request_data['iterationID'],
            app_name=request_data['appName'],
            action_id=request_data['actionId'],
            op_user=request_data['user'],
            op_time=datetime.datetime.now(),
            op_type=request_data['actionItem'],
            suite_name=request_data['appEnv'],
            status=request_data['status'])
        return res[1], res[0].id

    @staticmethod
    def update_deploy_result_data_by_id(deploy_id, status, msg, job_name=None):
        """根据id更新status,msg,job_name"""
        sql_where = "id = {}".format(deploy_id)
        res = H5DeployResult.objects.extra(where=[sql_where]).update(status=status, job_name=job_name, message=msg)
        return res

    @staticmethod
    def update_deploy_result_data_by_ids(deploy_ids, status, msg, job_name=None):
        """deploy_ids 为元组(1,2),若只有一位需新增无效位，避免元组逗号影响"""
        sql_where = "id IN {}".format(deploy_ids)
        res = H5DeployResult.objects.extra(where=[sql_where]).update(status=status, job_name=job_name, message=msg)
        return res

    @staticmethod
    def check_deploy_status(app_name, iteration_id, suite_name, op_type):
        """根据行为，检查状态是否在运行中"""
        res = H5DeployResult.objects.filter(app_name=app_name, iteration_id=iteration_id,
                                            suite_name=suite_name, op_type=op_type).last()
        if res and res.status == DeployResult.op_type_to_status[op_type][DeployBaseStatusEnum.BASE_STATUS.running]:
            return False
        return True

    @staticmethod
    def check_app_component_publish(param):
        """检查组件是否在发布中"""
        for row in param:
            if "app_component_publish" in row['actionItem']:
                res = H5DeployResult.objects.filter(app_name=row['appName'], iteration_id=row['iterationID'],
                                                    suite_name=row['appEnv'], op_type=row['actionItem']).last()
                if res and res.status == "running":
                    return False
        return True

    @staticmethod
    def update_result_by_filter(filter_dict, param):
        """根据条件更新入参（dict对应表中字段），目前条件支持id，action_id,iteration_id,app_name"""
        sql_where = "1=1"
        if not filter_dict['id']:
            sql_where = sql_where + " AND id = "+filter_dict['id']
        if not filter_dict['action_id']:
            sql_where = sql_where + " AND action_id = "+filter_dict['action_id']
        if not filter_dict['iteration_id']:
            sql_where = sql_where + " AND iteration_id = "+filter_dict['iteration_id']
        if not filter_dict['app_name']:
            sql_where = sql_where + " AND app_name = "+filter_dict['app_name']
        logger.info("update_result_by_filter语句：%s", sql_where)
        res = H5DeployResult.objects.extra(where=[sql_where]).update(param)
        return res

    @staticmethod
    def update_result_by_sql_where(sql_where, status, msg):
        """根据条件更新入参"""
        res = H5DeployResult.objects.extra(where=[sql_where]).update(status=status, message=msg)
        return res

    @staticmethod
    def update_result_by_sql_where(sql_where, status, msg):
        """根据条件更新入参"""
        res = H5DeployResult.objects.extra(where=[sql_where]).update(status=status, message=msg)
        return res