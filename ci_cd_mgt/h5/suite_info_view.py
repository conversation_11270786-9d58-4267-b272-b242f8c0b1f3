import datetime
from rest_framework import viewsets, serializers
from rest_framework.response import Response


from spider.settings import logger, ApiResult

from ci_cd_mgt.publish_info_ser import get_iter_suite, get_iter_platform_suite


class AppSuiteCodeApi(viewsets.ViewSet):

    def list(self, request, *args, **kwargs):
        logger.info(request.GET.get)
        iteration_id = request.GET.get("iteration_id")
        app_suite_info = get_iter_suite(["pd-test"], iteration_id)

        return Response(data=ApiResult.success_dict(msg="发布申请信息查询成功", data={"app_suite_info": app_suite_info}))

class PlatformSuiteCodeApi(viewsets.ViewSet):

    def list(self, request, *args, **kwargs):
        iteration_id = request.GET.get("iteration_id")
        fund_platform_suite, piggy_platform_suite = get_iter_platform_suite(["pd-test"], iteration_id)

        return Response(data=ApiResult.success_dict(msg="查询dist平台环境信息",
                                                    data={"fund_platform_suite": fund_platform_suite,
                                                          "piggy_platform_suite": piggy_platform_suite}))
