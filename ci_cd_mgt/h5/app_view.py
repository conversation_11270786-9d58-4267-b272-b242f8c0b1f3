from rest_framework.response import Response

from ci_cd_mgt.h5.app_ci_pipeline import AppCIPipeline, AppTagCIPipeline
from ci_cd_mgt.h5.db_ser.deploy_result import DeployResult
from ci_cd_mgt.h5.enums import DeployBaseStatusEnum
from spider.settings import logger, ApiResult
from app_mgt.app_mgt_ser import get_code_repo
from user.models import ActionRecord
import datetime


class AppTestPublishApi(AppCIPipeline):
    """app测试发布"""

    def create(self, request, *args, **kwargs):
        business_name = "test_publish"
        """app测试发布接口"""
        try:
            if isinstance(request.user, str):
                user = request.user
            else:
                user = request.user.username
            logger.info(request.data)
            param = request.data['data']
            # fixme 临时适配方案，如果是list的情况处理 20210914 shuai
            if isinstance(param, list):
                if not DeployResult.check_app_component_publish(param):
                    return Response(data=ApiResult.failed_dict(msg="组件正在发布中，请稍后重试", data=[]))
                action_record = ActionRecord.objects.create(username=user,
                                                            operate_time=datetime.datetime.now(),
                                                            action_item=param[0]['actionItem'],
                                                            action_value=param)
                deploy_status_ids = [0]

                for row in param:
                    row['operator'] = user
                    row['actionId'] = action_record.id
                    row['jobName'] = "{}_{}".format(row['iterationID'], row['appName'])
                    row['repoPath'] = get_code_repo(row['appName'])
                    is_add, deploy_status_id = DeployResult.insert_status_data(user, row)
                    if is_add:
                        deploy_status_ids.append(deploy_status_id)
                        row['deploy_status_id'] = deploy_status_id
                    else:
                        raise RuntimeError("应用{}状态初始化失败".format(row['appName']))
                # 调用jenkins  开始执行任务队列
                self.run_stage(user, action_record.id, param)
            else:
                param['operator'] = user
                param['jobName'] = "{}_{}".format(param['iterationID'], param['appName'])
                logger.info("发布所需参数%s", param)
                # 根据[应用][迭代][环境][行为]确定当前应用是否已经在发布中了，避免用户重复发布,只能降低部分
                if not DeployResult.check_deploy_status(param['appName'], param['iterationID'], param['appEnv'],
                                                        op_type=param['actionItem']):
                    return Response(data=ApiResult.failed_dict(msg="当前应用已经在发布中，无法再次发布", data=[]))
                is_add, deploy_status_id = DeployResult.insert_status_data(user, param)
                if is_add:
                    param['deploy_status_id'] = deploy_status_id
                else:
                    return Response(data=ApiResult.failed_dict(msg="发布状态初始化失败", data=[]))
                # 调用jenkins  开始执行任务队列
                self.run_stage(user, param['actionId'], param)
        except Exception as err:
            # 更新状态
            if is_add:
                DeployResult.update_deploy_result_data_by_id(
                    deploy_status_id,
                    DeployResult.op_type_to_status[business_name][DeployBaseStatusEnum.BASE_STATUS.failure],
                    str(err))
            return Response(data=ApiResult.failed_dict(msg="异常：" + str(err), data=[]))
        return Response(data=ApiResult.success_dict(msg="发布调用成功", data=param))


class AppPublishApplyApi(AppCIPipeline):
    """app发布申请"""
    check_business_name_list = ["mobile_publish_apply_check"]

    def create(self, request, *args, **kwargs):
        try:
            if isinstance(request.user, str):
                user = request.user
            else:
                user = request.user.username
            logger.info(request.data)
            param = request.data['data']
            params = param['requestParams']
            deploy_status_ids = [0]
            for row in params:
                # 根据[应用][迭代][环境][行为]确定当前应用是否已经在发布中了，避免用户重复发布,只能降低部分
                if not DeployResult.check_deploy_status(row['appName'], row['iterationID'], row['appEnv'],
                                                        row['actionItem']):
                    return Response(data=ApiResult.failed_dict(msg="应用{}已经在申请中，无法再次申请".format(row['appName']), data=[]))
            for row in params:
                row['operator'] = user
                row['actionId'] = param['actionId']
                row['jobName'] = "{}_{}".format(row['iterationID'], row['appName'])
                is_add, deploy_status_id = DeployResult.insert_status_data(user, row)
                if is_add:
                    deploy_status_ids.append(deploy_status_id)
                    row['deploy_status_id'] = deploy_status_id
                else:
                    raise RuntimeError("应用{}状态初始化失败".format(row['appName']))
            logger.info("发布所需参数%s", params)
            # 调用jenkins  开始执行任务队列
            self.run_publish_apply_stage(user, param, params)

        except Exception as err:
            # 更新状态
            if len(deploy_status_ids) > 1:
                DeployResult.update_deploy_result_data_by_ids(
                    tuple(deploy_status_ids),
                    DeployResult.op_type_to_status[param['actionItem']][DeployBaseStatusEnum.BASE_STATUS.failure],
                    str(err))
            return Response(data=ApiResult.failed_dict(msg="异常：" + str(err), data=[]))
        return Response(data=ApiResult.success_dict(msg="发布申请调用成功", data=param))


class AppTagTestPublishApi(AppTagCIPipeline, AppTestPublishApi):
    """ app tag 发布接口"""
    # tag 没有检查业务
    check_business_name_list = []


class AppTagPublishApplyApi(AppTagCIPipeline, AppPublishApplyApi):
    """app tag 的发布申请"""
    # tag 没有检查业务
    check_business_name_list = []
