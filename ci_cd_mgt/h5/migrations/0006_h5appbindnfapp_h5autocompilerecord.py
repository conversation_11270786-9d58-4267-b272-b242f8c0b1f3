# Generated by Django 3.2 on 2021-04-12 10:55

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('h5', '0005_auto_20210121_2010'),
    ]

    operations = [
        # migrations.CreateModel(
        #     name='H5AppBindNfApp',
        #     fields=[
        #         ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
        #         ('iteration_id', models.Char<PERSON><PERSON>(max_length=100, verbose_name='迭代版本')),
        #         ('app_name', models.CharField(max_length=100, verbose_name='模块名')),
        #         ('nf_br_name', models.Char<PERSON>ield(max_length=100, verbose_name='nf的分支号')),
        #         ('nf_app_name', models.Char<PERSON>ield(max_length=100, verbose_name='模块编码')),
        #         ('created_at', models.DateTime<PERSON>ield(verbose_name='创建时间')),
        #         ('updated_at', models.DateTime<PERSON>ield(verbose_name='修改时间')),
        #         ('operator', models.CharField(max_length=100, verbose_name='操作者')),
        #     ],
        #     options={
        #         'verbose_name': 'h5app应用绑定nf应用',
        #         'db_table': 'h5_app_bind_nf_app',
        #     },
        # ),
    ]
