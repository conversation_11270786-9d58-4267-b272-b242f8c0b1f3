# Generated by Django 3.1.2 on 2021-01-19 20:11

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='CIRecord',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('action_id', models.IntegerField(blank=True, null=True, verbose_name='行为id')),
                ('iteration_id', models.CharField(max_length=100, verbose_name='操作应用名称')),
                ('app_name', models.CharField(max_length=100, verbose_name='操作应用名称')),
                ('ip', models.CharField(max_length=100, verbose_name='ip')),
                ('suite_name', models.CharField(max_length=100, verbose_name='环境套')),
                ('status', models.Char<PERSON>ield(choices=[('compile_running', '编译中'), ('compile_success', '编译成功'), ('compile_failure', '编译失败'), ('publish_running', '发布中'), ('publish_success', '发布成功'), ('publish_failure', '发布失败')], max_length=30, verbose_name='执行状态')),
                ('message', models.TextField(verbose_name='信息描述')),
            ],
            options={
                'verbose_name': '自动编译的记录数据',
                'db_table': 'ci_cd_mgt_ci_record',
            },
        ),

    ]
