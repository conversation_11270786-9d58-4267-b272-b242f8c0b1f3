from django.db import models


# H5 自动编译的记录数据
# class H5AutoCompileRecord(models.Model):
#     log_id = models.CharField(verbose_name='git commit logid', max_length=100, default=None)
#     iteration_id = models.CharField(verbose_name='iteration id', max_length=100)
#     app_list = models.CharField(verbose_name='需要编译的应用列表', max_length=100, default=None)
#
#     class Meta:
#         db_table = 'h5_auto_compile_record'
#         verbose_name = '自动编译的记录数据'


class CIRecord(models.Model):
    """CI状态"""
    (C_RUNNING, C_SUCCESS, C_FAILURE,
     P_RUNNING, P_SUCCESS, P_FAILURE) = ('compile_running', 'compile_success', 'compile_failure',
                                   'publish_running', 'publish_success', 'publish_failure')
    STATUS_CHOICE = ((C_RUNNING, '编译中'), (C_SUCCESS, '编译成功'), (C_FAILURE, '编译失败'),
                     (P_RUNNING, '发布中'), (P_SUCCESS, '发布成功'), (P_FAILURE, '发布失败'))

    action_id = models.IntegerField(verbose_name='行为id', blank=True, null=True)
    job_name = models.CharField(max_length=200, blank=True, null=True, verbose_name='job名')
    iteration_id = models.CharField(max_length=100, verbose_name='操作应用名称')
    app_name = models.CharField(max_length=100, verbose_name='操作应用名称')
    ip = models.CharField(max_length=100, verbose_name='ip')
    suite_name = models.CharField(max_length=100, verbose_name='环境套')
    status = models.CharField(choices=STATUS_CHOICE, max_length=30, verbose_name='执行状态')
    begin_ver =models.CharField(max_length=200, blank=True, null=True, verbose_name='打包开始版本')
    end_ver = models.CharField(max_length=200, blank=True, null=True, verbose_name='打包结束版本')
    message = models.TextField(verbose_name='信息描述')
    class Meta:
        db_table = 'ci_cd_mgt_ci_record'
        verbose_name = '自动编译的记录数据'


# class H5AppBindNfApp(models.Model):
#     """
#     h5 bind nf应用表
#     """
#     iteration_id = models.CharField(verbose_name='迭代版本', max_length=100)
#     app_name = models.CharField(verbose_name='模块名', max_length=100)
#     nf_br_name = models.CharField(verbose_name='nf的分支号', max_length=100)
#     nf_app_name = models.CharField(verbose_name='模块编码', max_length=100)
#     created_at = models.DateTimeField(verbose_name='创建时间')
#     updated_at = models.DateTimeField(verbose_name='修改时间')
#     operator = models.CharField(verbose_name='操作者', max_length=100)
#     stage = models.CharField(verbose_name='阶段', max_length=100, default="")
#     class Meta:
#         db_table = 'h5_app_bind_nf_app'
#         verbose_name = 'h5app应用绑定nf应用'

class PublishApplicationExtraInfo(models.Model):
    """
    h5 应用发布申请表
    """
    id = models.BigAutoField(primary_key=True)
    application_id = models.BigIntegerField(verbose_name='产线申请ID')
    package_type = models.CharField(verbose_name='包类型', max_length=100)
    app_version = models.CharField(verbose_name='App版本名称', max_length=100)
    check_result = models.IntegerField(verbose_name='审核结果', default=0)
    create_user = models.CharField(verbose_name='创建人', max_length=50)
    create_time = models.DateTimeField(verbose_name='创建时间')
    update_user = models.CharField(verbose_name='更新人', max_length=50)
    update_time = models.DateTimeField(verbose_name='修改时间')
    stamp = models.IntegerField(verbose_name='版本')
    class Meta:
        db_table = 'iter_mgt_publish_application_extra_info'
        verbose_name = '应用发布申请扩展表'

class H5DistPlatformPublishInfo(models.Model):
    iteration_id = models.CharField(verbose_name='迭代版本', max_length=100)
    platform_code = models.CharField(verbose_name='平台名', max_length=100)
    suite_code = models.CharField(verbose_name='环境名', max_length=100)
    app_name = models.CharField(verbose_name='应用名', max_length=100)
    publish_br_name = models.CharField(verbose_name='发布分支', max_length=100)
    create_user = models.CharField(verbose_name='创建人', max_length=64)
    create_time = models.DateTimeField(verbose_name='创建时间')
    update_user = models.CharField(verbose_name='更新人', max_length=64)
    update_time = models.DateTimeField(verbose_name='修改时间')
    stamp = models.IntegerField(verbose_name='版本')
    class Meta:
        db_table = 'h5_dist_platform_publish_info'
        verbose_name = 'h5dist应用发布信息'


class H5AndroidPushResult(models.Model):
    """安卓渠道推包推送记录表"""
    create_user = models.CharField(verbose_name='创建人', max_length=50)
    create_time = models.DateTimeField(verbose_name='创建时间')
    update_user = models.CharField(verbose_name='更新人', max_length=50)
    update_time = models.DateTimeField(verbose_name='修改时间')
    stamp = models.IntegerField(verbose_name='版本')

    id = models.BigAutoField(primary_key=True)
    application_id = models.IntegerField(verbose_name='产线申请记录ID')
    h5_extend_id = models.IntegerField(verbose_name='产线申请H5扩展记录ID')
    push_br_name = models.CharField(verbose_name='推包版本号', max_length=50)
    push_result = models.BooleanField(verbose_name='推包结果')
    push_msg = models.CharField(verbose_name='推包信息', max_length=999)

    class Meta:
        db_table = 'h5_android_push_result'
        verbose_name = '安卓渠道推包推送记录表'


class H5AndroidCheckResult(models.Model):
    """安卓渠道推包检查记录表"""
    create_user = models.CharField(verbose_name='创建人', max_length=50)
    create_time = models.DateTimeField(verbose_name='创建时间')
    update_user = models.CharField(verbose_name='更新人', max_length=50)
    update_time = models.DateTimeField(verbose_name='修改时间')
    stamp = models.IntegerField(verbose_name='版本')

    id = models.BigAutoField(primary_key=True)
    application_id = models.IntegerField(verbose_name='产线申请记录ID')
    h5_extend_id = models.IntegerField(verbose_name='产线申请H5扩展记录ID')
    check_br_name = models.CharField(verbose_name='检查版本号', max_length=50)
    check_result = models.BooleanField(verbose_name='检查结果')
    check_msg = models.CharField(verbose_name='检查信息', max_length=999)

    class Meta:
        db_table = 'h5_android_check_result'
        verbose_name = '安卓渠道推包检查记录表'
