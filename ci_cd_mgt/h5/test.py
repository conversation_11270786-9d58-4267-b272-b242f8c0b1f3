from ci_cd_mgt.h5.enums import DeployBaseStatusEnum


def test_join():
    a = [1, 2]
    a1 = (1, 2)
    # b = 'id in ("{}")'.format(",".join(a))
    b1 = "id in {}".format(a1)
    c = tuple(a)
    # print(b)
    print(b1)
    print(c)


def test_enum():
    res = DeployBaseStatusEnum.BASE_STATUS.running
    print(res)


def test_if_else():
    a = "1" if 1 == 2 else "2"
    print(a)


if __name__ == "__main__":
    test_if_else()
