from django.db import connection
from spider.settings import logger


def get_jenkins_build_info(job_name_list, build_id = "" ):
    """
    获取环境套
    module_name: .str 应用名
    suite_code : .环境信息
    return : dict
    """
    cursor = connection.cursor()
    sql = '''
        SELECT m.job_name,m.build_url,m.app_name,m.status,m.end_time,c.username,m.env FROM hm_optlog_main m INNER JOIN 
        task_mgt_jenkins_results r ON m.job_name = r.job_name 
AND m.job_build_id=r.build_id
INNER JOIN user_action_record c ON c.id=r.action_id INNER JOIN 
 (SELECT job_name, MAX(job_build_id) AS hm_id FROM hm_optlog_main  GROUP BY job_name) mm ON mm.hm_id = m.job_build_id
WHERE m.job_name IN ("{}");'''.format('","'.join(job_name_list))
    logger.info(sql)
    cursor.execute(sql)
    job_info = []
    for row in cursor.fetchall():
        job_info.append({"job_name": row[0],
                         "build_url": row[1],
                         "app_name": row[2],
                         "status": row[3],
                         "end_time": row[4],
                         "username": row[5],
                         "env": row[6]})
    return job_info

