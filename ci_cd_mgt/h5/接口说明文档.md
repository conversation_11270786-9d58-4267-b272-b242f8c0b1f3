# H5 CI/CD 管理系统 API 接口说明文档

## 概述

本文档描述了H5 CI/CD管理系统中所有API接口的详细信息，包括接口功能、参数、返回值等。系统主要包含H5应用的编译、发布、测试等CI/CD流程管理功能。

## API接口列表

### 1. H5CIPipelineApi (基础CI流水线API)

**基类说明**: 提供H5 CI/CD流水线的基础功能

#### 1.1 list - 获取流水线信息列表
- **HTTP方法**: GET
- **功能**: 查询指定迭代的流水线信息
- **参数**:
  - `iteration_id`: 迭代ID
  - `suite_code`: 套件代码(可选)
- **返回值**: 流水线信息列表

#### 1.2 temp_app_params - 获取应用临时参数
- **HTTP方法**: GET
- **功能**: 获取应用的临时配置参数
- **参数**:
  - `app_name`: 应用名称
  - `iteration_id`: 迭代ID
- **返回值**: 应用参数配置信息

#### 1.3 ci_publish_info - 获取CI发布信息
- **HTTP方法**: 内部方法
- **功能**: 构建CI发布所需的参数信息
- **参数**: `request_data` - 请求数据列表
- **返回值**: Jenkins任务参数字典

#### 1.4 check_stage - 校验阶段
- **HTTP方法**: 内部方法
- **功能**: 执行发布前的校验检查
- **参数**:
  - `action_id`: 操作ID
  - `request_data`: 请求数据
  - `task_queue`: 任务队列
  - `check_business_name_dict`: 校验业务名称字典

#### 1.5 zeus_stage - 宙斯配置阶段
- **HTTP方法**: 内部方法
- **功能**: 处理宙斯配置相关逻辑
- **参数**:
  - `action_id`: 操作ID
  - `request_data`: 请求数据
  - `task_queue`: 任务队列

### 2. H5JenkinsInfoApi - Jenkins信息查询API

#### 2.1 list - 查询Jenkins任务信息
- **HTTP方法**: GET
- **功能**: 根据迭代和业务项查询Jenkins任务信息
- **参数**:
  - `iteration_id`: 迭代ID
  - `business_item`: 业务项
- **返回值**: Jenkins任务信息列表

### 3. H5CompileApi - H5编译API

**业务名称**: `h5_compile`
**操作项**: `compile`

#### 3.1 create - 创建编译任务
- **HTTP方法**: POST
- **功能**: 创建H5应用编译任务
- **参数**:
  - 请求体包含应用列表，每个应用包含:
    - `app_name`: 应用名称
    - `iteration_id`: 迭代ID
    - `suite_code`: 套件代码
    - `package_type`: 包类型
- **返回值**: 
  - 成功: `{"action_id": "操作ID"}`
  - 失败: 错误信息和冲突应用列表

#### 3.2 compile - 编译处理逻辑
- **HTTP方法**: 内部方法
- **功能**: 执行编译的核心逻辑
- **特性**:
  - 检查应用是否正在编译中
  - 记录用户操作行为
  - 异步执行Jenkins任务
  - 支持宙斯配置检查

### 4. H5MiniCompileApi - H5小程序编译API

**继承**: H5CompileApi
**业务名称**: `h5_mini_compile`
**操作项**: `mini_compile`

#### 4.1 create - 创建小程序编译任务
- **HTTP方法**: POST
- **功能**: 创建H5小程序编译任务
- **支持包类型**:
  - `mini-program`: 小程序
  - `param-remote`: 小程序远端
  - `ssr-remote`: SSR远端

### 5. H5AutoCompileApi - H5自动编译API

**继承**: H5CompileApi
**功能**: 基于Git提交自动触发编译

#### 5.1 create - Git提交触发编译
- **HTTP方法**: POST
- **功能**: 接收Git webhook，解析提交信息并触发自动编译
- **触发条件**:
  - 提交信息包含 `即时编译-all`: 编译所有应用
  - 提交信息包含 `即时编译-{app_name}`: 编译指定应用
- **特性**:
  - 自动停止冲突的编译任务
  - 发送邮件通知相关人员
  - 支持多应用并发编译

#### 5.2 get_iteration_app - 获取迭代应用列表
- **HTTP方法**: 静态方法
- **功能**: 获取指定迭代包含的应用列表
- **参数**: `iteration_id` - 迭代ID
- **返回值**: 应用名称列表

#### 5.3 convert_log - 解析提交日志
- **HTTP方法**: 静态方法
- **功能**: 解析Git提交信息，判断是否需要触发即时编译
- **参数**:
  - `commit_info`: 提交信息
  - `iteration_app_list`: 迭代应用列表
  - `iteration_id`: 迭代ID
- **返回值**: `(是否触发编译, 需要编译的应用列表)`

### 6. H5TestPublishApi - H5测试发布API

**继承**: H5CIPipelineApi
**操作项**: `test_publish`

#### 6.1 run_stage - 执行发布阶段
- **HTTP方法**: 内部方法
- **功能**: 执行测试发布的完整流程
- **流程**:
  1. 校验阶段
  2. 宙斯配置阶段
  3. 发布阶段
- **特性**: 异步执行任务队列

### 7. H5PublishApplyApi - H5产线发布申请API

**继承**: H5TestPublishApi
**业务名称**: `publish_apply`
**操作项**: `publish_apply`

#### 7.1 list - 获取发布申请列表
- **HTTP方法**: GET
- **功能**: 查询发布申请信息列表
- **参数**:
  - `iteration_id`: 迭代ID
  - `suite_code`: 套件代码(可选)
- **返回值**: 发布申请信息列表，包含邮件信息和仓库信息

#### 7.2 run_stage - 执行发布申请阶段
- **HTTP方法**: 内部方法
- **功能**: 执行产线发布申请的完整流程
- **特殊逻辑**:
  - Tag类型发布跳过校验和宙斯配置
  - 产线环境增加额外校验
  - 自动发送邮件通知
  - 记录产线申请信息

#### 7.3 is_concurrent_apply - 检查并发申请
- **HTTP方法**: 内部方法
- **功能**: 检查是否存在并发的发布申请
- **参数**: `request` - 请求对象
- **返回值**: 并发检查结果

#### 7.4 get_mail_info - 获取邮件信息
- **HTTP方法**: 内部方法
- **功能**: 构建发布申请的邮件通知内容
- **返回值**: 包含邮件主题、内容和收件人的字典

### 8. H5RemoteTestBetaProdCompileApi - H5远程测试/预发/产线编译API

**继承**: H5CIPipelineApi
**业务名称**: `h5_remote_test_beta_prod_compile`
**操作项**: `h5_remote_test_beta_prod_compile`

#### 8.1 create - 创建远程编译任务
- **HTTP方法**: POST
- **功能**: 创建H5远程环境编译任务
- **支持包类型**:
  - `param-remote`: 参数远程
  - `mini-program`: 小程序
  - `ssr-remote`: SSR远程
  - `remote`: 远程
- **特性**:
  - 支持Tag类型发布
  - 宙斯配置检查
  - 编译冲突检测

### 9. PipelineEnvBind - 流水线环境绑定API

#### 9.1 list - 获取环境绑定信息
- **HTTP方法**: GET
- **功能**: 查询用户的环境绑定配置
- **参数**: `iter_id` - 迭代ID
- **返回值**: 环境绑定信息字典

#### 9.2 create - 创建/更新环境绑定
- **HTTP方法**: POST
- **功能**: 创建或更新环境绑定配置
- **参数**:
  - `data.iter_id`: 迭代ID
  - `data.bind_env_list`: 绑定环境列表
- **返回值**: 操作结果

### 10. H5BranchFile - H5分支文件API

#### 10.1 list - 获取分支归档信息
- **HTTP方法**: GET
- **功能**: 查询分支归档页面信息
- **参数**: `iteration_id` - 迭代ID
- **返回值**: 分支归档信息

### 11. H5PipelineStop - H5流水线停止API

#### 11.1 create - 停止流水线任务
- **HTTP方法**: POST
- **功能**: 停止正在运行的Jenkins任务
- **参数**:
  - `app_name`: 应用名称
  - `iteration_id`: 迭代ID
  - `status`: 状态
- **返回值**: 操作结果

#### 11.2 list - 查询任务运行状态
- **HTTP方法**: GET
- **功能**: 查询指定任务是否正在运行
- **参数**:
  - `iteration_id`: 迭代ID
  - `app_name`: 应用名称
  - `status`: 状态
- **返回值**: `{"running": true/false}`

#### 11.3 stop_jenkins - 停止Jenkins任务
- **HTTP方法**: 静态方法
- **功能**: 直接停止指定的Jenkins构建任务
- **参数**:
  - `job_name`: 任务名称
  - `build_id`: 构建ID
  - `is_biz_job`: 是否业务任务(可选)

### 12. FindH5ZipVersionByEnv - 根据环境查找H5版本API

#### 12.1 list - 查询H5包版本
- **HTTP方法**: GET
- **功能**: 根据应用和环境查询可用的H5包版本
- **参数**:
  - `app_name`: 应用名称
  - `suite_code`: 套件代码
- **返回值**: 版本列表，格式: `[{"value": "版本号", "label": "版本号"}]`

### 13. FindH5ZipVersionByEnvForPlatform - 根据平台查找H5版本API

#### 13.1 list - 查询平台H5版本
- **HTTP方法**: GET
- **功能**: 根据平台和环境查询H5版本，支持机房切换场景
- **参数**:
  - `platform_code`: 平台代码
  - `suite_code`: 套件代码
- **返回值**: 版本列表
- **特性**: 使用region作为查询条件，优化Android发布申请

### 14. GetLastApplyInfoByBranchAndEnv - 获取最后申请信息API

#### 14.1 list - 查询最后申请信息
- **HTTP方法**: GET
- **功能**: 根据分支和环境查询最后的申请信息
- **参数**:
  - `branch_name`: 分支名称
  - `suite_code`: 套件代码
  - `group_name`: 组名称
  - `tag_name`: 标签名称
- **返回值**: 
  - 成功: `{"status": "true", "return_msg": "任务URL"}`
  - 失败: `{"status": "false", "return_msg": "错误信息"}`

### 15. findTestSuiteCodeOfH5Zip - 查找H5测试套件代码API

#### 15.1 list - 获取测试套件代码
- **HTTP方法**: GET
- **功能**: 查询H5的测试套件代码列表
- **返回值**: 测试套件代码列表

### 16. mobile_branch_status - 移动端分支状态API

#### 16.1 list - 查询分支状态
- **HTTP方法**: GET
- **功能**: 查询移动端分支的发布状态
- **参数**: `iteration_id[]` - 迭代信息JSON字符串
- **返回值**: 
  - `msg`: "上线中" 或 "正常"
  - `data`: 状态列表

### 17. notice_apply_by_wechat - 微信通知API

#### 17.1 list - 发送微信通知
- **HTTP方法**: GET
- **功能**: 通过企业微信发送申请失败通知
- **参数**:
  - `iteration_id`: 迭代ID
  - `env`: 环境
- **返回值**: 发送结果
- **特性**: 使用企业微信API发送消息

### 18. FindH5HDDistVersionByEnv - 查找H5高清分发版本API

#### 18.1 list - 查询高清分发版本
- **HTTP方法**: GET
- **功能**: 根据平台和版本查询对应的分支名称
- **参数**:
  - `h5_platform_code`: H5平台代码
  - `suite_code`: 套件代码
  - `end_ver`: 结束版本
- **返回值**: `{"branch_name": "分支名称"}`

### 19. GetLastProdAndroidInfo - 获取最后产线Android信息API

#### 19.1 list - 查询产线Android APK
- **HTTP方法**: GET
- **功能**: 查询最后的产线Android APK下载信息
- **参数**: `app_name` - 应用名称
- **返回值**: Android APK下载链接列表

### 20. GetLastProdDistInfo - 获取最后产线分发信息API

#### 20.1 list - 查询产线分发信息
- **HTTP方法**: GET
- **功能**: 查询产线环境的分发应用信息
- **参数**: `select_list[]` - 选择的应用列表
- **返回值**: 产线分发信息列表
- **状态**: 当前实现为空(pass)

### 21. GetTestPublishDistInfo - 获取测试发布分发信息API

#### 21.1 list - 查询测试发布分发信息
- **HTTP方法**: GET
- **功能**: 查询测试发布时的分发应用信息，处理fund和piggy平台的差异化应用
- **参数**: `select_list[]` - 选择的应用列表
- **返回值**: 
  ```json
  {
    "fund_platform": [],
    "piggy_platform": [],
    "fund_dist": boolean,
    "piggy_dist": boolean
  }
  ```

### 22. GetLastProdDistAppInfo - 获取最后产线分发应用信息API

#### 22.1 list - 查询产线分发应用信息
- **HTTP方法**: GET
- **功能**: 查询产线环境中缺失的分发应用信息
- **参数**:
  - `fund_h5_res[]`: fund平台H5资源列表
  - `piggy_h5_res[]`: piggy平台H5资源列表
- **返回值**: 缺失应用的产线分发信息

## 通用响应格式

所有API接口都使用统一的响应格式:

```json
{
  "code": 200,
  "msg": "操作结果描述",
  "data": {
    // 具体数据内容
  }
}
```

## 错误处理

- **成功响应**: 使用 `ApiResult.success_dict()`
- **失败响应**: 使用 `ApiResult.failed_dict()`
- **HTTP状态码**: 通常返回200，具体错误信息在响应体中

## 认证方式

- 大部分API需要用户认证
- 支持用户名和用户对象两种认证方式
- `H5AutoCompileApi` 无需认证(用于Git webhook)

## 异步任务处理

系统使用 `TaskQueue` 进行异步任务处理，支持:
- Jenkins任务执行
- 邮件发送
- 宙斯配置
- 校验检查
- 发布申请记录

## 业务流程说明

### 编译流程
1. 用户提交编译请求
2. 系统检查应用是否正在编译
3. 记录用户操作
4. 执行宙斯配置检查(如需要)
5. 提交Jenkins编译任务
6. 异步执行并更新状态

### 发布流程
1. 校验阶段 - 检查应用状态和权限
2. 宙斯配置阶段 - 处理配置相关逻辑
3. 发布阶段 - 执行实际发布操作
4. 邮件通知 - 发送相关通知
5. 记录申请信息(产线环境)

### 自动编译流程
1. 接收Git webhook
2. 解析提交信息
3. 判断是否触发即时编译
4. 停止冲突的编译任务
5. 发送邮件通知
6. 触发新的编译任务

## 注意事项

1. **并发控制**: 系统会检查应用是否正在编译，避免重复编译
2. **邮件通知**: 重要操作会自动发送邮件通知相关人员
3. **异步处理**: 大部分耗时操作都采用异步处理方式
4. **环境区分**: 支持测试、预发、产线等多环境部署
5. **包类型支持**: 支持多种包类型(remote、dist、mini-program、ssr-remote等)
6. **权限控制**: 不同操作有相应的权限检查机制

---

**文档版本**: 1.0  
**更新时间**: 2024年12月  
**维护人员**: CI/CD团队