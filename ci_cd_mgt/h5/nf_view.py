from rest_framework import viewsets
from rest_framework.response import Response

from iter_mgt import iter_mgt_ser
from iter_mgt.models import PublishApplicationConfirmation
# from ci_cd_mgt.h5.models import H5AppBindNfApp
from spider.settings import logger, ApiResult


class H5AppBindNfAppApi(viewsets.ViewSet):
    def list(self, request, *args, **kwargs):
        """
        获取绑定的数据
        :param request:
        :param args:
        :param kwargs:
        :return:
        """
        # if request.GET.get('suite_code') == 'test':
        #     stage = '测试'
        # elif request.GET.get('suite_code') == 'prod':
        #     stage = '上线'
        # else:
        #     stage = '灰度'
        # bind_info = H5AppBindNfApp.objects.filter(iteration_id=request.GET.get("iteration_id"),
        #                                           app_name=request.GET.get("app_name"), stage=stage).order_by("-created_at")
        # if bind_info:
        #     for row in bind_info:
        #         return Response(data=ApiResult.success_dict(msg="获取到绑定数据",
        #                                                     data={"nf_br_name": row.nf_br_name,
        #                                                       "nf_app_name": row.nf_app_name}))
        # else:
        return Response(data=ApiResult.success_dict(msg="没有绑定数据", data={}))

    def create(self, request, *args, **kwargs):
        """
        创建app和nf新框架的绑定关系
        :param request:
        :param args:
        :param kwargs:
        :return:
        """
        # if isinstance(request.user, str):
        #     user = request.user
        # else:
        #     user = request.user.username
        logger.info("发布请求{}".format(request.data))
        # H5AppBindNfApp.objects.create(iteration_id=request.data["iteration_id"],
        #                               app_name=request.data["app_name"],
        #                               nf_br_name=request.data["nf_br_name"],
        #                               nf_app_name=request.data["nf_app_name"],
        #                               stage=request.data["stage"],
        #                               created_at=datetime.datetime.now(),
        #                               updated_at=datetime.datetime.now(),
        #                               operator=user)
        return Response(data=ApiResult.success_dict(msg="绑定成功"))

class H5NfAppStatuApi(viewsets.ViewSet):
    def list(self,request):
        # iteration_id = request.GET.get("iteration_id")
        # app_name = request.GET.get("app_name")
        # nf_br_name,sys_status = iter_mgt_ser.get_nf_app_sys_statu(app_name, iteration_id)
        # if nf_br_name == iteration_id:
        #     if_same_iter = 1
        # else:
        #     if_same_iter = 0
        # return Response(data=ApiResult.success_dict(msg="获取到绑定数据",
        #                                                     data={"nf_br_name": nf_br_name,
        #                                                       "sys_status": sys_status,
        #                                                           "if_same_iter":if_same_iter,
        #                                                           }))
        return Response(data=ApiResult.failed_dict(msg="获取到绑定数据失败"))

class H5PublishApplyStatus(viewsets.ViewSet):
    def list(self, request):
        iteration_id = request.GET.get("iteration_id")
        app_name = request.GET.get("app_name")
        objs = PublishApplicationConfirmation.objects.filter(iteration_id=iteration_id, status="success")
        package_type = iter_mgt_ser.get_app_build_package_type(app_name)
        if objs or package_type != 'static':
            return Response(data=ApiResult.success_dict(msg="获取产线申请信息",
                                                    data=''))
        else:
            return Response(data=ApiResult.failed_dict(msg="获取产线申请信息"))