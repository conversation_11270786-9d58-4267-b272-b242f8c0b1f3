import datetime
import copy
import datetime
import json
import time
import traceback
from urllib import request

from rest_framework import viewsets
from rest_framework.response import Response

from app_mgt.app_mgt_ser import get_app_suite_info_node
from app_mgt.app_mgt_ser import get_module_repo_by_module_name, get_app_info_by_module_name, get_app_suite_info
from app_mgt.models import AppModule
from ci_cd_mgt.h5.models import H5DistPlatformPublishInfo
from ci_cd_mgt.publish_info_ser import PublishApplyInfoCollector
from ci_cd_mgt.publish_info_ser import get_app_publish_info, CIInfoCollector, H5BranchFileCollector, \
    PipelineStopController, get_last_job_url, get_last_task_h5_deploy_info, get_jenkins_id, \
    get_test_suite_code, get_latest_h5_hd_branch, get_prod_download_android_url, \
    get_h5_dist_last_arc_info, get_h5_dist_app, get_br_name, get_h5_dist_app_platform, get_h5_dist_iter_app_info
from task_mgt.zeus_ser import get_zeus_namespace
from ci_cd_mgt.publish_info_ser import get_branch_status
from iter_mgt.models import BranchIncludeSys
from iter_mgt.models import Branches
from jenkins_mgt.jenkins_job_mgt import JenkinsJobMgt
from pipeline.models import EnvBind
from public.generate_content import h5_send_reject_email
from public.send_email import SendMail
from public.template.h5_apply_template import apply_email_template, dist_table_template
from publish.models import PackageMain
from publish.publish_ser import get_h5_zip_suite_all_ver_info_by_region
from spider.settings import logger, ApiResult
from task_mgt.jenkins_task.call_job import JenkinsCaller
from task_mgt.models import H5DeployResult
from task_mgt.models import JenkinsResults
from task_mgt.ser.task_queue_ser import TaskQueueInfoCollector
from task_mgt.task_queue import TaskQueue, TaskTypeObject
from user.models import ActionRecord


class H5CIPipelineApi(viewsets.ViewSet):
    """
       h5 测试发布接口
       """

    action_item = "ci_pipeline"
    business_name_dict = {"remote": "h5_test_remote_publish",
                          "remote_vm": "h5_test_remote_vm_publish",
                          "dist_vm": "h5_test_app_publish",
                          "dist": "h5_test_app_publish",
                          "static": "h5_test_remote_publish",
                          "static_vm": "h5_test_remote_vm_publish", }
    # check_business_name_list = ["compile_check"]
    check_business_name_list = []
    check_business_name_dict = {}

    def list(self, request, *args, **kwargs):
        iteration_id = request.GET.get("iteration_id")
        # action_id = request.GET.get("action_id")
        ci_c = CIInfoCollector()
        ci_info = ci_c.get_info(iteration_id, [self.action_item, "h5_compile", "test_publish"])
        email_info = PublishApplyInfoCollector.find_all_email(iteration_id)
        logger.info(email_info)
        ci_info_temp = []
        # 拼装 email_info 和 publish_info
        for row in ci_info:
            row.update(email_info)
            ci_info_temp.append(row)

        logger.info(ci_info_temp)
        ci_info = ci_info_temp

        return Response(data=ApiResult.success_dict(msg="正在处理中", data={"ci_info": ci_info}))

    def temp_app_params(self, call_dict):
        call_dict["iterationID"] = call_dict["iteration_id"]
        call_dict["appName"] = call_dict["app_name"]
        call_dict["appEnv"] = call_dict["suite_code"]
        call_dict["appBranch"] = call_dict["br_name"]
        call_dict["appTagName"] = call_dict["tag_name"]
        call_dict["appCodeType"] = call_dict["code_type"]
        call_dict["packageType"] = call_dict["package_type"]
        call_dict["repoPath"] = call_dict["repo_path"]
        call_dict["appVersion"] = call_dict["app_version"]
        # call_dict["h5AppName"] = call_dict["h5_app_name"]
        call_dict["h5ZipVersion"] = call_dict["h5_version"]
        call_dict["h5Env"] = call_dict["h5_env"]
        call_dict["operator"] = ""
        call_dict["actionId"] = ""
        return call_dict

    def ci_publish_info(self, params, action_id=None):
        call_job_param_dict = {}
        for row in params:
            if row["suite_code"]:
                try:
                    deploy_info = get_app_publish_info(row["suite_code"], row["app_name"], multi_node=True)

                    # 增加nf 应用的处理逻辑，获取nf相关信息
                    # if "nf_app_name" in row and row["nf_app_name"]:
                    #     nf_deploy_info = get_nf_app_info(row["nf_app_name"])
                    # else:
                    #     nf_deploy_info = {"nf_lib_repo": "", "nf_zeus_type": "", "nf_namespace": ""}
                    # row.update(**nf_deploy_info)

                except Exception as e:
                    logger.error(str(e))
                    if action_id:
                        H5DeployResult.objects.filter(iteration_id=row["iteration_id"],
                                                      app_name=row["app_name"],
                                                      action_id=action_id).update(status=H5DeployResult.P_FAILURE,
                                                                                  message=str(e))
                    continue
                m_key = deploy_info.keys() & row.keys()
                logger.info("参数中重复的key{}".format(m_key))
                for key in m_key:
                    logger.info("参数中重复的key{}".format(key))
                    row.pop(key)

                call_dict = dict(**deploy_info, **row)
                # 1、 默认用 package_type 获取 2、有虚拟机业务 用虚拟机业务 3、 有tag业务 用tag业务 by帅 20221118
                business_key = deploy_info["package_type"]
                if deploy_info['deploy_type'] in ['1', 1]:
                    if business_key + "_vm" in self.business_name_dict:
                        business_key = business_key + '_vm'
                if "code_type" in row and row["code_type"] == "tag":
                    if business_key + "_tag" in self.business_name_dict:
                        business_key = business_key + "_tag"
                logger.info(business_key)
                # 为了兼容app 设计时的驼峰参数
                if deploy_info["package_type"] in ("ios", "android", "ios-global", "android-global"):
                    call_dict = self.temp_app_params(call_dict)

                if self.business_name_dict[business_key] in call_job_param_dict:
                    call_job_param_dict[self.business_name_dict[business_key]].append(call_dict)
                else:
                    call_job_param_dict[self.business_name_dict[business_key]] = [call_dict]
        return call_job_param_dict

    def check_stage(self, action_id, request_data, task_queue, check_business_name_dict):
        if len(check_business_name_dict) > 0:
            check_dict = {}
            for row in request_data:
                deploy_info = get_app_publish_info(row["suite_code"], row["app_name"], multi_node=True)
                if deploy_info['package_type'] not in check_business_name_dict:
                    raise ValueError("{}类型没有定义check_business_name_dict".format(deploy_info['package_type']))
                if check_business_name_dict[deploy_info['package_type']] in check_dict:
                    check_dict[check_business_name_dict[deploy_info['package_type']]]["app_name_list"] \
                        .append(row["app_name"])
                else:
                    if "logic_env" in row:
                        env = row["logic_env"]
                    else:
                        env = row["suite_code"]
                    check_dict[check_business_name_dict[deploy_info['package_type']]] = {
                        'business_name': check_business_name_dict[deploy_info['package_type']],
                        'iteration_id': row["iteration_id"],
                        'app_name_list': [row["app_name"]],
                        'env': env,
                        'sql_check_list': request_data[0].get("sql_check_list") if request_data[0].get(
                            "sql_check_list") else [],
                        'opt_user': row.get("opt_user")}

            logger.info("需要检查的check字典")
            logger.info(check_dict)
            for check_business_name, check_params in check_dict.items():
                # 添加检查任务
                task_queue.enter_queue(TaskTypeObject.script, check_business_name, action_id, check_params)

    def add_zeus_queue(self, action_id, br_name, task_queue, namespace, suite_code):
        # 回合前暂时先同步，后面再去掉，减少交互  20250402 by fwm
        task_queue.enter_queue(TaskTypeObject.interface,
                               "sync_config",
                               action_id,
                               {"iteration_number": br_name,
                                "app_name": namespace,
                                "environments": suite_code})

        # 添加自动回合(此为父方法实现，会同时覆盖所有的h5_test。)。zt@2025-01-10
        task_queue.enter_queue(TaskTypeObject.interface,
                               "auto_merge_zeus",
                               action_id,
                               {"iteration_number": br_name,
                                "app_name": namespace
                                })

        task_queue.enter_queue(TaskTypeObject.interface,
                               "sync_config",
                               action_id,
                               {"iteration_number": br_name,
                                "app_name": namespace,
                                "environments": suite_code})

        task_queue.enter_queue(TaskTypeObject.interface,
                               "check_config_consistent",
                               action_id,
                               {"iteration_number": br_name,
                                "app_name": namespace,
                                "tenant_id": suite_code
                                })
        # 添加 配置为空 检测 20240328 by fwm
        task_queue.enter_queue(TaskTypeObject.interface,
                               "check_config_sync",
                               action_id,
                               {"iteration_number": br_name,
                                "app_name": namespace,
                                "tenant_id": suite_code
                                })

        # 添加回合检查 20210419 by 帅
        task_queue.enter_queue(TaskTypeObject.interface,
                               "check_config_merge",
                               action_id,
                               {"iteration_number": br_name,
                                "app_name": namespace
                                })
        task_queue.enter_queue(TaskTypeObject.publish_zeus_config,
                               "publish_config",
                               action_id,
                               {"iteration_number": br_name,
                                "app_name": namespace,
                                "environment": suite_code
                                })

    def zeus_stage(self, action_id, request_data, task_queue):
        # br_name = request_data[0]["br_name"]
        # 环境套取各行中的。zt@2024-05-21l
        # suite_code = request_data[0]["suite_code"]
        app_name_list = []
        namespace_info = {}
        namespace_list = []
        exist_list = []
        for row in request_data:
            app_name_list.append(row["app_name"])
            zeus_type = AppModule.objects.filter(module_name=row["app_name"]).values("zeus_type")[0]
            if zeus_type["zeus_type"] != 1:
                continue
            na_sp = get_zeus_namespace([row["app_name"]])

            if len(na_sp) > 0:
                namespace = na_sp[0]
            else:
                namespace = row["app_name"]
            if namespace + "_" + row["br_name"] + "_" + row["suite_code"] not in exist_list:
                exist_list.append(namespace + "_" + row["br_name"] + "_" + row["suite_code"])
                self.add_zeus_queue(action_id, row["br_name"], task_queue, namespace, row["suite_code"])

    def insert_status_data(self, user, request_data):
        action_item = ''
        if request_data is not None and len(request_data) > 0:
            action_item = request_data[0].get('action_item')
        if request_data[0].get('action_item') is None:
            action_item = self.action_item
        action_record = ActionRecord.objects.create(username=user,
                                                    operate_time=datetime.datetime.now(),
                                                    action_item=action_item,
                                                    action_value=request_data)
        request_dict = {}
        for row in request_data:
            if row["app_name"] in request_dict:
                if isinstance(row["suite_code"], list):
                    request_dict[row["app_name"]]["suite_code_list"].extend(row["suite_code"])
                else:
                    request_dict[row["app_name"]]["suite_code_list"].append(row["suite_code"])
            else:
                # todo 以下判断逻辑是一样的，delete
                if "begin_ver" in row:
                    begin_ver = row["begin_ver"]
                else:
                    begin_ver = ""
                if "end_ver" in row:
                    end_ver = row["end_ver"]
                else:
                    end_ver = ""
                if isinstance(row["suite_code"], list):
                    request_dict[row["app_name"]] = {
                        "suite_code_list": row["suite_code"],
                        "begin_ver": begin_ver,
                        "end_ver": end_ver,
                        "iteration_id": row["iteration_id"]
                    }
                else:
                    request_dict[row["app_name"]] = {
                        "suite_code_list": [row["suite_code"]],
                        "begin_ver": begin_ver,
                        "end_ver": end_ver,
                        "iteration_id": row["iteration_id"]
                    }
        if self.action_item in ["ci_pipeline", "h5_compile"]:
            initial_status = H5DeployResult.C_RUNNING
        else:
            initial_status = H5DeployResult.P_RUNNING
        logger.info(request_dict)
        op_time = datetime.datetime.now()
        for app_name in request_dict:
            H5DeployResult.objects.update_or_create(iteration_id=row["iteration_id"], app_name=app_name,
                                                    action_id=action_record.id, status=initial_status,
                                                    op_user=user,
                                                    op_time=op_time,
                                                    op_type=self.action_item,
                                                    begin_ver=request_dict[app_name]["begin_ver"],
                                                    end_ver=request_dict[app_name]["end_ver"],
                                                    suite_name=",".join(request_dict[app_name]["suite_code_list"]))
        return action_record.id

    @staticmethod
    def update_deploy_result_data(deploy_id, status, msg, job_name=None):
        sql_where = 'id="{}"'.format(deploy_id),
        H5DeployResult.objects.extra(where=[sql_where]).update(status=status, job_name=job_name, message=msg)

    @staticmethod
    def update_deploy_result_data_by_ids(deploy_ids, status, msg, job_name=None):
        sql_where = 'id IN {}'.format(deploy_ids),
        H5DeployResult.objects.extra(where=[sql_where]).update(status=status, job_name=job_name, message=msg)

    @staticmethod
    def check_dist_iter_platform_app(app_info):
        """
        检查当前迭代app 是否有遗漏
        :param app_info_list:
        :return:
        """
        app_platform_info = []
        diff_app = []
        platform_list = get_h5_dist_app_platform()
        h5_dist_iter_app_info = get_h5_dist_iter_app_info(app_info[0]["iteration_id"])

        for item in app_info:
            if item.get("platform_code") in platform_list:
                if item.get("br_name") != h5_dist_iter_app_info.get(item.get("app_name")).get("br_name"):
                    diff_app.append({"app_name": item.get("app_name"), "br_name": item.get("br_name")})

        if diff_app:
            return Response(data=ApiResult.failed_dict(msg="申请异常，本次申请应用不全请check，"
                                                           "缺失应用{}，刷新页面重来".format(diff_app)))

        return Response(data=ApiResult.success_dict(msg="正在处理中", data=''))

    @staticmethod
    def check_dist_platform(data):
        dist_app_info = {}
        platform_list = get_h5_dist_app_platform()
        for item in data:
            if item.get("platform_code") in platform_list:
                if not dist_app_info.get(item["platform_code"]) and not \
                        isinstance(dist_app_info.get(item["platform_code"]), list):
                    dist_app_info[item["platform_code"]] = [item["app_name"]]
                else:
                    if item["app_name"] not in dist_app_info.get(item["platform_code"]):
                        dist_app_info.get(item["platform_code"]).append(item["app_name"])
                try:
                    public_info = get_app_publish_info(item["suite_code"], item["app_name"], multi_node=True)
                    if not public_info:
                        return Response(data=ApiResult.failed_dict(msg="申请异常，刷新页面重来,应用：{},"
                                                                       " 环境:{}缺失".format(item["app_name"],
                                                                                             item["suite_code"])))
                except Exception as e:
                    logger.error(str(e))
                    return Response(data=ApiResult.failed_dict(msg=str(e)))
                if "is_silent" not in item:
                    return Response(data=ApiResult.failed_dict(msg="申请异常，is_silent丢失，刷新页面重来"))
                if "begin_ver" not in item:
                    return Response(data=ApiResult.failed_dict(msg="申请异常，起始版本丢失，刷新页面重来"))
                if "end_ver" not in item:
                    return Response(data=ApiResult.failed_dict(msg="申请异常，结束版本丢失，刷新页面重来"))
                if "suite_code" not in item:
                    return Response(data=ApiResult.failed_dict(msg="申请异常，发布环境丢失，刷新页面重来"))
        return Response(data=ApiResult.success_dict(msg="正在处理中", data=''))

    @staticmethod
    def handle_dist_params(data):
        op_time = datetime.datetime.now()
        platform_list = get_h5_dist_app_platform()
        for item in data:
            if item.get("platform_code") in platform_list:
                if item.get("br_name"):
                    if item.get("br_name") != get_br_name(item["iteration_id"]):
                        last_arc_version = get_h5_dist_last_arc_info(item["app_name"])
                        br_name = get_br_name(last_arc_version)
                        item["br_name"] = br_name
                else:
                    last_arc_version = get_h5_dist_last_arc_info(item["app_name"])
                    if last_arc_version:
                        br_name = get_br_name(last_arc_version)
                        item['br_name'] = br_name
        new_data = list(filter(lambda it: it['br_name'] != '' and it['br_name'] is not None, data))
        for item in new_data:
            if item.get("platform_code") in platform_list:
                H5DistPlatformPublishInfo.objects.update_or_create(iteration_id=item["iteration_id"],
                                                                   app_name=item["app_name"],
                                                                   suite_code=item["suite_code"],
                                                                   platform_code=item["platform_code"],
                                                                   defaults={"publish_br_name": item["br_name"],
                                                                             "update_user": item["opt_user"],
                                                                             "update_time": op_time})
        return new_data

    def run_stage(self, user, action_id, request_data):
        task_queue = TaskQueue(task_queue=[])
        # 校验阶段
        # self.check_stage(action_id, request_data, task_queue)
        # 编译阶段
        call_job_param_dict = self.ci_publish_info(request_data, action_id=action_id)
        logger.info(call_job_param_dict)
        if len(call_job_param_dict) == 0:
            return False

        # 编译阶段
        task_queue.enter_queue(TaskTypeObject.jenkins, ["h5_compile"], action_id, request_data)
        #
        # 发布配置阶段 by 帅 20210318
        self.zeus_stage(action_id, request_data, task_queue)
        # 发布阶段
        task_queue.enter_queue(TaskTypeObject.jenkins, call_job_param_dict.keys(), action_id,
                               call_job_param_dict)
        # task_queue.run()
        task_queue.async_run()

    @staticmethod
    def _set_suite_code_by_logic_env(request_data):
        for row in request_data:
            if "logic_env" in row:
                logic_env = row["logic_env"]
                # 只有「remote、dist」类型，才需要单环境，包括beta、hd、prod都是单环境（有节点）。zt@2024-05-16
                # 小程序「mini-program」只会在「pd-prod」，绑定了中转库作为节点（暂时加上）
                # 而「ios、ios-com、android、android-com」都不需要有节点。
                package_type = row["package_type"]
                if package_type and package_type in ["remote", "dist", "param-remote", "mini-program", "ssr-remote"]:
                    suite_info = get_app_suite_info_node(row["app_name"], logic_env)
                else:
                    suite_info = get_app_suite_info(row["app_name"], logic_env)

                if len(suite_info) > 1:
                    raise Exception("应用{}在{}环境下有多环境套，请检查！".format(row["app_name"], logic_env))
                elif len(suite_info) == 0:
                    raise Exception("应用{}在{}环境下没有绑定环境套，请检查！".format(row["app_name"], logic_env))
                else:
                    row["suite_code"] = suite_info[0]["suite_code"]
        return request_data

    def create(self, request, *args, **kwargs):

        """发布 重启 停止 回滚 配置更新 代码更新"""
        dist_app_info = {}
        if isinstance(request.user, str):
            user = request.user
        else:
            user = request.user.username
        logger.info("发布请求{}".format(request.data))
        # 伪代码，未写具体实现
        if self.is_concurrent_apply(request):
            logger.info("正在申请中，请勿重复操作")
            return Response(data=ApiResult.failed_dict(msg="正在申请中，请勿重复操作"))

        new_data = []
        for row in request.data:
            if isinstance(row["suite_code"], list):
                for suite_code in row["suite_code"]:
                    new_row = copy.deepcopy(row)
                    new_row["suite_code"] = suite_code
                    new_row["opt_user"] = user
                    new_data.append(new_row)
            else:
                logger.info(row)
                row["opt_user"] = user
                new_data.append(row)
            if "br_name" not in row and "iteration_id" in row and row.get('package_type') != 'dist':
                row["br_name"] = Branches.objects.filter(pipeline_id=row["iteration_id"]).last().br_name

        new_data = self.handle_dist_params(new_data)
        check_result = self.check_dist_platform(new_data)
        # check_result_miss = self.check_dist_iter_platform_app(new_data)
        if check_result.data['status'] == 'failed':
            return Response(data=ApiResult.failed_dict(msg=check_result.data['msg']))
        # if check_result_miss.data['status'] == 'failed':
        #     return Response(data=ApiResult.failed_dict(msg=check_result_miss.data['msg']))
        # todo
        try:
            new_data = self._set_suite_code_by_logic_env(new_data)
        except Exception as e:
            return Response(data=ApiResult.failed_dict(msg=str(e)))
        action_id = self.insert_status_data(user, new_data)
        # 执行ci cd 的阶段步骤
        try:
            self.run_stage(user, action_id, new_data)
        except Exception as e:
            H5DeployResult.objects.filter(action_id=action_id).update(status=H5DeployResult.S_FAILURE, message=str(e))
            err_msg = traceback.format_exc()
            logger.error(err_msg)
            return Response(data=ApiResult.failed_dict(msg=str(e), data={"action_id": action_id}))
        return Response(data=ApiResult.success_dict(msg="正在处理中", data={"action_id": action_id}))

    def is_concurrent_apply(self, request):
        """
        判断是否正在并发申请
        :param request:参数
        :return:  True:正在申请，False：没有并发申请
        """
        return False


class H5JenkinsInfoApi(viewsets.ViewSet):
    """
    根据迭代和业务项查询jenkinsjob信息接口
    """

    def list(self, request, *args, **kwargs):
        iteration_id = request.GET.get("iteration_id")
        action_item_list = request.GET.getlist("action_item[]")
        package_type = request.GET.getlist("package_type[]")
        app_name_list = request.GET.getlist("app_name_list[]", [])
        logger.debug(request.GET)

        jenkins_info = []
        logger.debug(action_item_list)
        for item in action_item_list:
            for type in package_type:
                task_queue = TaskQueueInfoCollector(iteration_id, item, type)
                jenkins_info.extend(task_queue.get_jenkins_info_by_business())

        logger.debug("jenkins_info===={}".format(jenkins_info))
        publish_info = []
        for i in jenkins_info:
            # todo 临时过滤条件，如果前端传入了 app_name 则只要传入应用的数据 20220426 by 帅
            if len(app_name_list) > 0 and i["app_name"] not in app_name_list:
                continue
            flag = True
            main_id = i["main_id"]
            app_name = i["app_name"]
            status = i["status"]
            message = i["message"]
            op_time = i["op_time"]
            business_name = i["business_name"]
            job_url = i["job_url"]
            op_type = str(i["op_type"])
            begin_ver = i["begin_ver"]
            end_ver = i["end_ver"]
            op_user = i["op_user"]
            suite_code = i["suite_name"]
            compile_status = ''
            test_publish_status = ''
            compile_url = ''
            publish_url = ''
            compile_message = ''
            publish_message = ''
            if "compile" in op_type:
                compile_status = status
                compile_url = job_url
                compile_message = message
            elif "publish" in op_type:
                test_publish_status = status
                publish_url = job_url
                publish_message = message

            new_publish_info = {"main_id": main_id,
                                "app_name": app_name,
                                "compile_status": compile_status,
                                "test_publish_status": test_publish_status,
                                "publish_apply_status": test_publish_status,
                                "publish_apply_message": publish_message,
                                "compile_message": compile_message,
                                "publish_message": publish_message,
                                "op_time": op_time,
                                "begin_ver": begin_ver,
                                "end_ver": end_ver,
                                "business_name": business_name,
                                "op_user": op_user,
                                "suite_code": suite_code,
                                "end_time": i["end_time"],
                                "compile_url": compile_url,
                                "publish_url": publish_url,
                                # "_checked": False,
                                }
            for row in publish_info:
                """去重复数据"""
                if app_name == row["app_name"]:
                    if main_id is None:
                        flag = False
                        break
                    elif main_id == row["main_id"]:
                        flag = False
                        break

                    new_publish_info = self.dict_replace(row, new_publish_info)
                    publish_info[:] = [d for d in publish_info if d.get('app_name') != app_name]
                    break

            if not flag:
                continue

            publish_info.append(new_publish_info)

        publish_info.sort(key=lambda item: item['app_name'], reverse=True)
        publish_info = self.add_other_info(publish_info, iteration_id)
        publish_info = self.add_repo_info(publish_info, iteration_id)
        publish_info = self.add_platform_info(publish_info)
        logger.debug("publish_info===={}".format(publish_info))
        return Response(data=ApiResult.success_dict(msg="查询成功", data={"publish_info": publish_info}))

    def dict_replace(self, publish_info_dict, new_publish_info):
        if publish_info_dict["compile_status"]:
            publish_info_dict["publish_url"] = new_publish_info["publish_url"]
            publish_info_dict["test_publish_status"] = new_publish_info["test_publish_status"]
            publish_info_dict["publish_message"] = new_publish_info["publish_message"]
            """取发布记录中的绑定环境返回给前端"""
            publish_info_dict["suite_code"] = new_publish_info["suite_code"]
        elif publish_info_dict["test_publish_status"]:
            publish_info_dict["compile_url"] = new_publish_info["compile_url"]
            publish_info_dict["compile_status"] = new_publish_info["compile_status"]
            publish_info_dict["compile_message"] = new_publish_info["compile_message"]
        if new_publish_info["op_time"] is not None and publish_info_dict["op_time"] is not None and \
                new_publish_info["op_time"] > publish_info_dict["op_time"]:
            """挑选最近一次操作时间的记录中的信息返回给前端"""
            publish_info_dict["op_time"] = new_publish_info["op_time"]
            # 用最近一次操作的结束时间
            publish_info_dict["end_time"] = new_publish_info["end_time"]
            publish_info_dict["begin_ver"] = new_publish_info["begin_ver"]
            publish_info_dict["end_ver"] = new_publish_info["end_ver"]
            publish_info_dict["op_user"] = new_publish_info["op_user"]

        return publish_info_dict

    def add_other_info(self, publish_info, iteration_id):
        new_publish_info = []
        for item in publish_info:
            for appinfo in BranchIncludeSys.objects.filter(pipeline_id=iteration_id, appName=item["app_name"]):
                package_type = appinfo.package_type
            item.update({'package_type': package_type})
            new_publish_info.append(item)
        return new_publish_info

    def add_repo_info(self, publish_info, iteration_id):
        new_publish_info = []
        for item in publish_info:
            for appinfo in get_module_repo_by_module_name(iteration_id, item["app_name"]):
                repo_path = appinfo[0]
            item.update({'repo_path': repo_path})
            new_publish_info.append(item)

        return new_publish_info

    def add_platform_info(self, publish_info):
        new_publish_info = []
        platform_name = ''
        platform_code = ''
        for item in publish_info:
            for appinfo in get_app_info_by_module_name(item["app_name"]):
                platform_name = appinfo[1]
                platform_code = appinfo[2]
            item.update({'platform_name': platform_name, 'platform_code': platform_code})
            new_publish_info.append(item)
        return new_publish_info


class H5CompileApi(H5CIPipelineApi):
    """
    h5 编译接口
    """
    business_name = "h5_compile"
    action_item = "h5_compile"

    def compile(self, user, app_name_list, iteration_id, request_data, email=None):
        # 记录用户行为
        action_item = ''
        if request_data is not None and len(request_data) > 0 and type(request_data) is list:
            action_item = request_data[0].get('action_item')
        else:
            action_item = self.business_name
        action_record = ActionRecord.objects.create(username=user,
                                                    operate_time=datetime.datetime.now(),
                                                    action_item=action_item,
                                                    action_value=request_data)

        # update_iter_app_action_id(iteration_id, app_name_list, "ci_action_id", action_record.id)
        task_queue = TaskQueue(task_queue=[])

        res_list = []
        email_app_list = []
        for app_name in app_name_list:
            try:
                jenkins_caller = JenkinsCaller(self.business_name, iteration_id, app_name)
            except Exception as e:
                return Response(data=ApiResult.failed_dict(msg=str(e)))
            job_name = "{}_{}".format(iteration_id, app_name)
            if jenkins_caller.jenkins_is_running(job_name):
                email_app_list.append(app_name)
                res_list.append({"iteration_id": iteration_id,
                                 "app_name": app_name,
                                 "job_name": job_name,
                                 "request_status": JenkinsResults.FAILURE,
                                 "request_result": '应用正在编译'})

        if len(res_list) > 0:
            return Response(
                data=ApiResult.failed_dict(msg="有其他应用正在编译 {}，本次编译取消".format(",".join(email_app_list)),
                                           data={"res_list": res_list}))
        # 编译阶段
        op_time = datetime.datetime.now()
        for app_name in app_name_list:
            H5DeployResult.objects.update_or_create(iteration_id=iteration_id, app_name=app_name,
                                                    action_id=action_record.id, status=H5DeployResult.C_RUNNING,
                                                    op_user=user,
                                                    op_time=op_time,
                                                    op_type=self.action_item
                                                    )
        task_queue.enter_queue(TaskTypeObject.jenkins, [self.business_name], action_record.id, request_data)
        task_queue.async_run()
        return Response(data=ApiResult.success_dict(msg="订单创建成功", data={"action_id": action_record.id}))

    @staticmethod
    def send_reject_email(mail_to, reject_list):

        logger.info("准备给用户{} 发送停止的即时编译应用列表:{}".format(mail_to, reject_list))
        send_mail = SendMail()
        send_mail.set_subject("即时编译停止通知")
        mail_info_str = h5_send_reject_email(reject_list)
        send_mail.set_content(mail_info_str)
        send_mail.set_to(mail_to)
        send_mail.send()

    def create(self, request, *args, **kwargs):
        if isinstance(request.user, str):
            user = request.user
        else:
            user = request.user.username
        logger.info("编译参数{}".format(request.data))
        iteration_id = request.data[0]["iteration_id"]
        app_name_list = []
        for row in request.data:
            app_name_list.append(row["app_name"])

        return self.compile(user, app_name_list, iteration_id, request.data)


class H5MiniCompileApi(H5CIPipelineApi):
    """
    h5_mini 编译接口
    """
    business_name = "h5_mini_compile"
    action_item = "h5_mini_compile"

    def compile(self, user, app_name_list, iteration_id, suite_code, request_data, email=None):
        # 记录用户行为
        action_item = ''
        if request_data is not None and len(request_data) > 0 and type(request_data) is list:
            action_item = request_data[0].get('action_item')
        else:
            action_item = self.business_name
        action_record = ActionRecord.objects.create(username=user,
                                                    operate_time=datetime.datetime.now(),
                                                    action_item=action_item,
                                                    action_value=request_data)
        action_id = action_record.id
        task_queue = TaskQueue(task_queue=[])

        for item in request_data:
            if item.get('package_type') in ['ssr-remote', 'param-remote', 'mini-program']:
                self.zeus_stage(action_id, request_data, task_queue)
                break

        res_list = []
        email_app_list = []
        for row in request_data:
            app_name = row["app_name"]
            # package_type = row["package_type"]
            jenkins_caller = JenkinsCaller(self.business_name, iteration_id, app_name)
            # 包类型为：小程序（mini-program）、小程序远端（param-remote）
            job_name = "{}_{}".format(iteration_id, app_name)
            # if package_type in ['param-remote', 'mini-program']:
            #     job_name = "h5_pipeline_template"
            if jenkins_caller.jenkins_is_running(job_name):
                email_app_list.append(app_name)
                res_list.append({"iteration_id": iteration_id,
                                 "app_name": app_name,
                                 "job_name": job_name,
                                 "request_status": JenkinsResults.FAILURE,
                                 "request_result": '应用正在编译'})

        if len(res_list) > 0:
            return Response(
                data=ApiResult.failed_dict(msg="有其他应用正在编译 {}，本次编译取消".format(",".join(email_app_list)),
                                           data={"res_list": res_list}))
        # 编译阶段
        op_time = datetime.datetime.now()
        for app_name in app_name_list:
            H5DeployResult.objects.update_or_create(iteration_id=iteration_id, app_name=app_name,
                                                    action_id=action_record.id, status=H5DeployResult.C_RUNNING,
                                                    suite_name=suite_code,
                                                    op_user=user,
                                                    op_time=op_time,
                                                    op_type=self.action_item
                                                    )
        task_queue.enter_queue(TaskTypeObject.jenkins, [self.business_name], action_record.id, request_data)
        task_queue.async_run()
        return Response(data=ApiResult.success_dict(msg="订单创建成功", data={"action_id": action_record.id}))

    def create(self, request, *args, **kwargs):
        if isinstance(request.user, str):
            user = request.user
        else:
            user = request.user.username
        logger.info("编译参数{}".format(request.data))
        iteration_id = request.data[0].get("iteration_id")
        suite_code = request.data[0].get("suite_code")
        app_name_list = []
        for row in request.data:
            app_name_list.append(row["app_name"])

        return self.compile(user, app_name_list, iteration_id, suite_code, request.data)


class H5AutoCompileApi(H5CompileApi):
    authentication_classes = []
    """
    h5 即时编译接口
    """

    @staticmethod
    def get_iteration_app(iteration_id):
        iteration_app_list = []
        for app in BranchIncludeSys.objects.filter(pipeline_id=iteration_id):
            iteration_app_list.append(app.appName)
        return iteration_app_list

    @staticmethod
    def convert_log(commit_info, iteration_app_list, iteration_id):
        log_id = commit_info['id']
        log_message = commit_info['message']

        logger.info("[auto_cp] commit info : {}".format(commit_info))

        flag = False
        tmp_compile_app_list = []
        # 判断是否为即时编译
        if '即时编译-all' in log_message:
            flag = True
            tmp_compile_app_list = iteration_app_list

        for app_name in iteration_app_list:
            if '即时编译-{}'.format(app_name) in log_message:
                flag = True
                tmp_compile_app_list.append(app_name)

        # if flag:
        #     logger.info("[auto_cp] commit log id {} contains app  : {}".format(log_id, tmp_compile_app_list))
        #     if H5AutoCompileRecord.objects.filter(log_id=log_id):
        #         logger.info("已经触发过{}".format(log_id))
        #         flag = False
        #     else:
        #         H5AutoCompileRecord.objects.create(log_id=log_id, iteration_id=iteration_id,
        #                                            app_list=tmp_compile_app_list)
        #         logger.info("创建完{}的执行记录信息".format(log_id))

        return flag, tmp_compile_app_list

    def create(self, request, *args, **kwargs):
        logger.info("[auto_cp]代码提交触发git信息:{}".format(request.data))

        commit_json = json.loads(json.dumps(request.data))

        # 应用分支信息
        project_name = commit_json['repository']['name']
        branch_name = commit_json['ref']

        # 查询迭代信息
        iteration_id = "h5_" + branch_name.replace('refs/heads/', '')

        # 查询迭代包含的应用
        iteration_app_list = self.get_iteration_app(iteration_id)
        logger.info("[auto_cp] project {} iteration {} contains app list : {}".format(project_name, iteration_id,
                                                                                      iteration_app_list))

        # 解析 commit 信息
        commits = commit_json['commits']
        logger.info("[auto_cp] commit contains commit log size  : {}".format(len(commits)))

        compile_app_list = []
        flag = False
        log_author = commit_json['user_username']
        auth_email = "{}@howbuy.com".format(log_author)
        for commit_info in commits:
            logger.info(commit_info)

            tmp_flag, tmp_compile_app_list = self.convert_log(commit_info, iteration_app_list, iteration_id)

            if tmp_flag:
                compile_app_list.extend(tmp_compile_app_list)
                flag = True

        if flag:
            # 遍历需要即时编译的应用,若即时编译正在编译中则将即时编译的应用停止后重新触发编译
            for app_name in compile_app_list:
                jenkins_info = PipelineStopController.get_info(iteration_id, 'compile_running', app_name)
                logger.info(jenkins_info)
                # 停止上一次编译行为
                if jenkins_info is not None and jenkins_info.__len__() > 0:
                    H5PipelineStop.stop_jenkins(jenkins_info["job_name"], jenkins_info["build_id"])
                    # 在重启之前，给被影响的上一次编译者发送邮件
                    email = jenkins_info.get('email')
                    message = {}
                    message.update({'app_name': app_name})
                    message.update({'br_name': branch_name.replace('refs/heads/', '')})
                    message.update({"end_time": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())})
                    message.update({"reject_man": log_author})
                    # 给上次编译的人发邮件
                    self.send_reject_email(email, message)
                    # 给本次编译的人发邮件
                    self.send_reject_email(auth_email, message)

            app_name_list = set(compile_app_list)
            # 此处的complie_data的数据初始化一个是为了避免和正常的action_item数据冲突，避免数据影响
            compile_data = [{
                "begin_ver": '',
                "end_ver": '',
                "iteration_id": iteration_id,
                "br_name": branch_name,
                "app_name": app_name,
                "is_silent": '',
                "action_item": iteration_id + '_auto_compile',
                "status": 'compile_running'
            }]
            return self.compile(log_author, app_name_list, iteration_id, compile_data, auth_email)
        else:
            return Response(data=ApiResult.success_dict(msg="代码提交处理成功"))


class H5TestPublishApi(H5CIPipelineApi):
    action_item = "test_publish"

    def run_stage(self, user, action_id, request_data):
        task_queue = TaskQueue(task_queue=[])
        # 校验阶段
        self.check_stage(action_id, request_data, task_queue, self.check_business_name_dict)
        # 发布配置阶段 by 帅 20210318
        # 应远相的强烈要求，简化移动端「测试发布」的宙斯配置检查。zt@2024-12-11
        # 再次优化，宙斯提供自动回合的功能，先把功能还原。zt@2024-12-20
        self.zeus_stage(action_id, request_data, task_queue)

        # 发布阶段
        call_job_param_dict = self.ci_publish_info(request_data, action_id=action_id)
        logger.info(call_job_param_dict)
        task_queue.enter_queue(TaskTypeObject.jenkins, call_job_param_dict.keys(), action_id,
                               call_job_param_dict)
        # task_queue.run()
        task_queue.async_run()


class H5PublishApplyApi(H5TestPublishApi):
    """
        h5 产线发布申请接口
        """
    business_name = "publish_apply"
    action_item = "publish_apply"
    business_name_dict = {"remote": "h5_remote_publish_apply",
                          "dist": "h5_app_publish_apply",
                          "dist_vm": "h5_app_publish_apply",
                          "remote_vm": "h5_remote_publish_apply",
                          "static": "h5_remote_publish_apply",
                          "static_vm": "h5_remote_publish_apply",
                          "param-remote": "h5_mini_compile",
                          "mini-program": "h5_mini_compile",
                          "param-remote_vm": "h5_mini_compile",
                          "mini-program_vm": "h5_mini_compile",
                          # 增加「SSR」支持 zt@2023-08-02
                          "ssr-remote": "h5_mini_compile",

                          "android": "app_android_publish",
                          "ios": "app_ios_publish",
                          "android_tag": "app_tag_android_publish",
                          "ios_tag": "app_tag_ios_publish",

                          "android-global": "app_android_publish",
                          "ios-global": "app_ios_publish",
                          }
    check_business_name_list = ["publish_apply_check"]
    check_business_name_dict = {"param-remote": "mobile_publish_apply_check",
                                "mini-program": "mobile_publish_apply_check",
                                # 增加「SSR」支持 zt@2023-08-02
                                "ssr-remote": "mobile_publish_apply_check",

                                "ios": "mobile_publish_apply_check",
                                "android": "mobile_publish_apply_check",

                                "ios-global": "mobile_publish_apply_check",
                                "android-global": "mobile_publish_apply_check",

                                # h5-remote编译发布一体化后--产线申请不再需要代码编译检查。zt@2025-06-26
                                # "remote": "publish_apply_check",
                                "remote": "mobile_publish_apply_check",
                                "dist": "publish_apply_check",
                                "static": "publish_apply_check",
                                "jar": "publish_apply_check",
                                "war": "publish_apply_check",
                                "py": "publish_apply_check"}
    hd_check_business_name_dict = {"param-remote": "package_check",
                                   "mini-program": "package_check",
                                   # 增加「SSR」支持 zt@2023-08-02
                                   "ssr-remote": "package_check",

                                   "ios": "mobile_publish_apply_check",
                                   "android": "mobile_publish_apply_check",

                                   "ios-global": "mobile_publish_apply_check",
                                   "android-global": "mobile_publish_apply_check",

                                   "remote": "package_check",
                                   "dist": "package_check",
                                   "static": "package_check",
                                   "jar": "publish_apply_check",
                                   "war": "publish_apply_check",
                                   "py": "publish_apply_check"}

    def is_concurrent_apply(self, request):
        request_data = request.data

        suite_code = request_data[0]["suite_code"]
        iteration_id = request_data[0]["iteration_id"]
        app_list = []
        for row in request_data:
            app_list.append(row['app_name'])

        return PublishApplyInfoCollector.find_app_is_apply(iteration_id, suite_code, app_list)

    def get_mail_info(self, user, request_data):
        """发送邮件通知"""

        br_name = request_data[0]["br_name"]
        cc = request_data[0]["cc"]
        suite_code = request_data[0]["suite_code"]
        app_name_list = []
        dist_package_info = ""
        for row in request_data:
            deploy_info = get_app_publish_info(row["suite_code"], row["app_name"], multi_node=True)
            app_name_list.append(row["app_name"])
            # dist 应用列出来打包的起始和结束版本 2021-02-25 by shuai.liu
            if deploy_info["package_type"] == "dist":
                dist_package_info = dist_package_info + dist_table_template.format(app_name=row["app_name"],
                                                                                   begin_ver=row["begin_ver"],
                                                                                   end_ver=row["end_ver"])
        logger.info(app_name_list)
        content = apply_email_template.format(team="h5",
                                              branch_name=br_name,
                                              applicant=user,
                                              description=Branches.objects.filter(
                                                  pipeline_id=row["iteration_id"]).first().description,
                                              app_name_list=app_name_list,
                                              dist_package_info=dist_package_info)

        subject = '{}分支{}环境上线申请通知'.format(br_name, suite_code)

        return {"content": content, "subject": subject, "mail_to": cc}

    # @staticmethod
    # def __set_suite_code_by_logic_env(request_data):
    #     for row in request_data:
    #         logic_env = row["logic_env"]
    #         suite_info = get_app_suite_info(row["app_name"], logic_env)
    #         if len(suite_info) > 1:
    #             raise Exception("应用{}在{}环境下有多环境套，请检查！".format(row["app_name"], logic_env))
    #         elif len(suite_info) == 0:
    #             raise Exception("应用{}在{}环境下没有套件，请检查！".format(row["app_name"], logic_env))
    #         else:
    #             row["suite_code"] = suite_info[0]["suite_code"]
    #     return request_data

    def run_stage(self, user, action_id, request_data):
        task_queue = TaskQueue(task_queue=[])
        # 校验阶段
        iteration_id = request_data[0]["iteration_id"]
        # tag 不校验 不发宙斯
        if "code_type" in request_data[0] and request_data[0]["code_type"] == "tag":
            pass
        else:
            # todo 暂时注释掉 等数据调整好之后放开
            # request_data = self.__set_suite_code_by_logic_env(request_data)
            # 如果是产线环境增加校验阶段
            if request_data[0]["logic_env"] == "prod":
                self.check_stage(action_id, request_data, task_queue, self.check_business_name_dict)
            else:
                self.check_stage(action_id, request_data, task_queue, self.hd_check_business_name_dict)
                # zb_data = []
                # import copy
                # for row in request_data:
                #     new_row = copy.deepcopy(row)
                #     new_row["suite_code"] = "bs-zb"
                #     zb_data.append(new_row)
                # request_data.extend(zb_data)
            # 发布阶段
            # 发布配置阶段 by 帅 20210318
            self.zeus_stage(action_id, request_data, task_queue)
        logger.info(request_data)
        call_job_param_dict = self.ci_publish_info(request_data, action_id=action_id)
        logger.info(call_job_param_dict)

        # 移动端灾备 和 prod 互斥 不可以同时存在 20240429  by 帅
        # params_list = []
        # if request_data[0]["suite_code"] == 'prod' and \
        #         call_job_param_dict[list(call_job_param_dict.keys())[0]][0]["package_type"] \
        #         == 'ssr-remote':  # 只有产线阶段要分主备,未来放开给所有应用
        #     suite_info = get_suite_info(request_data[0]["app_name"], request_data[0]["suite_code"], iteration_id)
        #     call_jenkins = []
        #     for item in suite_info:
        #         if item['suite_group'] not in call_jenkins:
        #             item.update(call_job_param_dict[list(call_job_param_dict.keys())[0]][0])
        #             params_list.append(item)
        #
        #     call_job_param_dict[list(call_job_param_dict.keys())[0]] = params_list
        logger.info('----------------------')
        task_queue.enter_queue(TaskTypeObject.jenkins, call_job_param_dict.keys(), action_id,
                               call_job_param_dict)

        email_info = self.get_mail_info(user, request_data)
        task_queue.enter_queue(TaskTypeObject.email, "send_email", action_id,
                               email_info)

        # 将记录产线申请表加入队列
        if request_data[0]["logic_env"] == "prod":
            task_queue.enter_queue(TaskTypeObject.recode_publish_application, "recode_publish_application",
                                   action_id, request_data)

        # task_queue.run()
        task_queue.async_run()

    def add_repo_info(self, publish_info):
        new_publish_info = []
        for item in publish_info:
            platform_name = ''
            platform_code = ''
            for appinfo in get_app_info_by_module_name(item["app_name"]):
                platform_name = appinfo[1]
                platform_code = appinfo[2]
            item.update({'platform_name': platform_name, 'platform_code': platform_code})
            new_publish_info.append(item)
        return new_publish_info

    def list(self, request, *args, **kwargs):
        iteration_id = request.GET.get("iteration_id")
        suite_code = request.GET.get("suite_code", "")
        publish_info = PublishApplyInfoCollector().get_info(iteration_id, [self.action_item], suite_code)
        email_info = PublishApplyInfoCollector.find_all_email(iteration_id)
        logger.debug(email_info)
        publish_info_temp = []
        # 拼装 email_info 和 publish_info
        for row in publish_info:
            row.update(email_info)
            publish_info_temp.append(row)

        logger.debug(publish_info_temp)
        publish_info = publish_info_temp
        publish_info = self.add_repo_info(publish_info)
        # if suite_code:
        #     # 查询已经过的发布
        #     # 分支，环境，迭代，应用
        #     branch_version = request.GET.get("branch_code")
        #     for row in publish_info:
        #         # if row['package_type'] == 'ios' or row['package_type'] == 'android':
        #         #     break
        #         app_name = row['app_name']
        #         if PublishApplyInfoCollector.is_last_compiled_apply(app_name, iteration_id, branch_version,
        #                                                                 suite_code):
        #             row['_checked'] = False
        #             row['code_message'] = '代码没变动'
        #         else:
        #             row['_checked'] = True
        #             row['code_message'] = '代码有变动'
        # logger.info(row)
        return Response(data=ApiResult.success_dict(msg="正在处理中", data={"ci_info": publish_info}))


class H5RemoteTestBetaProdCompileApi(H5CIPipelineApi):
    """
    h5_mini 编译接口
    """
    business_name = "h5_remote_test_beta_prod_compile"
    action_item = "h5_remote_test_beta_prod_compile"

    def compile(self, user, app_name_list, iteration_id, suite_code, request_data, email=None):
        # 记录用户行为
        if (request_data
                and len(request_data) > 0
                and type(request_data) is list):
            action_item = request_data[0].get('action_item')
        else:
            action_item = self.business_name
        # 创建记录
        action_record = ActionRecord.objects.create(username=user,
                                                    operate_time=datetime.datetime.now(),
                                                    action_item=action_item,
                                                    action_value=request_data)
        action_id = action_record.id
        task_queue = TaskQueue(task_queue=[])

        for item in request_data:
            if item.get('package_type') in ['param-remote',
                                            'mini-program',
                                            'ssr-remote',
                                            'remote']:
                self.zeus_stage(action_id, request_data, task_queue)

        res_list = []
        email_app_list = []
        business_name = ''
        for row in request_data:
            if row.get("code_type") and row.get("code_type") == "tag":
                business_name = "h5_remote_tag_publish"
            else:
                business_name = self.business_name
                app_name = row["app_name"]
                jenkins_caller = JenkinsCaller(business_name, iteration_id, app_name)
                job_name = "{}_{}".format(iteration_id, app_name)
                if jenkins_caller.jenkins_is_running(job_name):
                    email_app_list.append(app_name)
                    res_list.append({"iteration_id": iteration_id,
                                     "app_name": app_name,
                                     "job_name": job_name,
                                     "request_status": JenkinsResults.FAILURE,
                                     "request_result": '应用正在编译'})

        if len(res_list) > 0:
            return Response(
                data=ApiResult.failed_dict(msg="有其他应用正在编译 {}，本次编译取消".format(",".join(email_app_list)),
                                           data={"res_list": res_list}))
        # 编译阶段：compile_running
        op_time = datetime.datetime.now()
        for app_name in app_name_list:
            H5DeployResult.objects.update_or_create(iteration_id=iteration_id,
                                                    app_name=app_name,
                                                    action_id=action_record.id,
                                                    status=H5DeployResult.C_RUNNING,
                                                    suite_name=suite_code,
                                                    op_user=user,
                                                    op_time=op_time,
                                                    op_type=self.action_item)
        # 异步队列
        task_queue.enter_queue(TaskTypeObject.jenkins, [business_name], action_record.id, request_data)
        task_queue.async_run()

        return Response(data=ApiResult.success_dict(msg="订单创建成功", data={"action_id": action_record.id}))

    def create(self, req, *args, **kwargs):
        if isinstance(req.user, str):
            user = req.user
        else:
            user = req.user.username
        logger.info("编译参数{}".format(req.data))
        iteration_id = req.data[0].get("iteration_id")
        suite_code = req.data[0].get("suite_code")
        app_name_list = []
        for row in req.data:
            app_name_list.append(row["app_name"])

        return self.compile(user, app_name_list, iteration_id, suite_code, req.data)


class PipelineEnvBind(viewsets.ViewSet):
    """
        环境绑定
    """

    def list(self, request, *args, **kwargs):
        if isinstance(request.user, str):
            user = request.user
        else:
            user = request.user.username

        iter_id = request.GET.get("iter_id")
        if EnvBind.objects.filter(pipeline_id=iter_id, operator=user).__len__() == 0:
            env_bind_info = EnvBind.objects.filter(pipeline_id=iter_id)
        else:
            env_bind_info = EnvBind.objects.filter(pipeline_id=iter_id, operator=user)
        env_bind_info_dict = {}
        for row in env_bind_info:
            env_bind_info_dict[row.app_name] = row.env.split(",")

        logger.info(env_bind_info_dict)
        return Response(
            data=ApiResult.success_dict(msg="环境绑定信息查询成功", data={"bind_env_list": env_bind_info_dict}))

    def create(self, request):
        if isinstance(request.user, str):
            user = request.user
        else:
            user = request.user.username
        data = request.data.get('data')
        logger.info(data)
        env_list = []
        for app_name in data["bind_env_list"]:
            logger.info(app_name)
            # env = EnvBind(pipeline_id=data["iter_id"], app_name=app_name,
            #               env=",".join(data["bind_env_list"][app_name]), operator=user)
            EnvBind.objects.update_or_create(defaults={'env': ",".join(data["bind_env_list"][app_name])},
                                             pipeline_id=data["iter_id"],
                                             app_name=app_name,
                                             operator=user)
        #     env_list.append(env)
        # logger.info(env_list)
        # EnvBind.objects.bulk_create(env_list)
        return Response(data=ApiResult.success_dict(msg="环境绑定插入成功", data=[]))


class H5BranchFile(viewsets.ViewSet):
    """
        分支归档页面查询
    """

    def list(self, request, *args, **kwargs):
        iteration_id = request.GET.get("iteration_id")
        h5_branch_file_info = H5BranchFileCollector.h5_branch_file(iteration_id)
        logger.info(h5_branch_file_info)
        return Response(data=ApiResult.success_dict(msg="分支归档页面查询成功",
                                                    data={"h5_branch_file_info": h5_branch_file_info}))


class H5PipelineStop(viewsets.ViewSet):

    def create(self, request):
        app_name = request.data["app_name"]
        iteration_id = request.data["iteration_id"]
        status = request.data["status"]

        # 根据信息查询jenkins job id
        jenkins_info = PipelineStopController.get_info(iteration_id, status, app_name)

        H5PipelineStop.stop_jenkins(jenkins_info["job_name"], jenkins_info["build_id"])

        return Response(data=ApiResult.success_dict(msg="", data=[]))

    @staticmethod
    def stop_jenkins(job_name, build_id, is_biz_job=None):
        jenkins_job_mgt = JenkinsJobMgt()
        jenkins_server = jenkins_job_mgt.get_jenkins_server_by_job_name(job_name, is_biz_job).server
        jenkins_server.stop_build(job_name, int(build_id))

    def list(self, request, *args, **kwargs):
        iteration_id = request.GET.get("iteration_id")
        app_name = request.GET.get("app_name")
        status = request.GET.get("status")

        # 根据信息查询jenkins job id
        jenkins_info = PipelineStopController.get_info(iteration_id, status, app_name)

        result = {'running': True if len(jenkins_info) > 0 else False}

        return Response(data=ApiResult.success_dict(msg="", data=result))


class FindH5ZipVersionByEnv(viewsets.ViewSet):

    @staticmethod
    def list(request):
        app_name = request.GET['app_name']
        suite_code = request.GET['suite_code']
        try:
            list1 = PackageMain.objects.filter(app_name=app_name, suite_code=suite_code).values('end_ver') \
                .order_by('-end_ver').distinct()

            res_data = list()
            # {value: '21.0.1', label: '21.0.1'},
            for item in list1:
                item['label'] = item['end_ver']
                item['value'] = item['end_ver']
                res_data.append(item)
            logger.debug("res_data===" + res_data)
        except Exception as err:
            return Response(data=ApiResult.failed_dict(msg="异常：" + str(err), data=res_data))
        return Response(data=ApiResult.success_dict(msg="查询成功", data=res_data))


class FindH5ZipVersionByEnvForPlatform(viewsets.ViewSet):

    @staticmethod
    def list(request):
        platform_code = request.GET['platform_code']
        suite_code = request.GET['suite_code']
        res_data = None
        try:
            # 优化android发布申请，选择h5版本号时使用「region」作为条件。（典型场景是机房切换）zt@2024-08-27
            h5_res_cursor = get_h5_zip_suite_all_ver_info_by_region(platform_code, suite_code)
            h5_res_dict_list = FindH5ZipVersionByEnvForPlatform.dict_fetchall(h5_res_cursor)

            h5_res_list = [obj.get('end_ver') for obj in h5_res_dict_list if obj.get('end_ver')]

            h5_res_ver_list = [{"value": obj, "label": obj} for obj in h5_res_list]

            # list1 = get_h5_zip_suite_ver_info(platform_code, suite_code)
            # res_data = list()
            # # {value: '21.0.1', label: '21.0.1'},
            # for item in list1:
            #     if item not in res_data:
            #         item['label'] = item['end_ver']
            #         item['value'] = item['end_ver']
            #         res_data.append(item)
            # logger.debug("res_data==={}".format(res_data))
            res_data = h5_res_ver_list
            logger.debug("res_data==={}".format(res_data))
        except Exception as err:
            logger.error(err)
            return Response(data=ApiResult.failed_dict(msg="异常：" + str(err), data=res_data))
        return Response(data=ApiResult.success_dict(msg="查询成功", data=res_data))

    @staticmethod
    def dict_fetchall(cursor):
        return [dict(zip([col[0] for col in cursor.description], row)) for row in cursor.fetchall()]


class GetLastApplyInfoByBranchAndEnv(viewsets.ViewSet):

    @staticmethod
    def list(request):
        branch_name = request.GET['branch_name']
        suite_code = request.GET['suite_code']
        group_name = request.GET['group_name']
        tag_name = request.GET.get('tag_name', '')
        op_type = request.GET.get('op_type', 'publish_apply')
        iteration_id = group_name + '_' + branch_name
        publish_info = get_last_task_h5_deploy_info(op_type, iteration_id, suite_code, tag_name)
        if publish_info:
            for item in publish_info:
                if item['status'] == 'script_failure':
                    re_msg = item['message']
                    res_data = {'status': 'false', 'return_msg': re_msg}
                    return Response(data=ApiResult.success_dict(msg="查询成功", data=res_data))
                else:
                    action_id = item['action_id']
                """ 
                jenkins 异步回写状态可能会失败，导致发布任务一直处于发布中 0924 by fwm
                elif item['status'] == 'publish_running':
                    res_data = {'status': 'false', 'return_msg': "发布中，请稍后"}
                    return Response(data=ApiResult.success_dict(msg="查询成功", data=res_data))
                """

            jenkins_info = get_jenkins_id(action_id)
            if jenkins_info:
                jenkins_id_list = []
                for i in jenkins_info:
                    if i['jenkins_status'] == 'failure':
                        res_data = {'status': 'false',
                                    'return_msg': "jenkins调用失败，请联系SCM排查原因：（task_mgt_jenkins_results.id={}）".format(
                                        i['jenkins_id'])}
                        return Response(data=ApiResult.success_dict(msg="查询成功", data=res_data))
                    elif i['jenkins_status'] == '':
                        res_data = {'status': 'false', 'return_msg': "jenkins任务排队中，请稍后查看"}
                        return Response(data=ApiResult.success_dict(msg="查询成功", data=res_data))
                    jenkins_id_list.append(i['jenkins_id'])

                job_url = get_last_job_url(jenkins_id_list)

                if job_url:
                    res_data = {'status': 'true', 'return_msg': job_url}
                else:
                    res_data = {'status': 'false', 'return_msg': "未查到申请记录"}
        else:
            res_data = {'status': 'false', 'return_msg': "未找到申请记录"}

        return Response(data=ApiResult.success_dict(msg="查询成功", data=res_data))


class findTestSuiteCodeOfH5Zip(viewsets.ViewSet):

    @staticmethod
    def list(request):
        try:
            res_data = get_test_suite_code()
        except Exception as err:
            return Response(data=ApiResult.failed_dict(msg="异常：" + str(err), data=res_data))
        return Response(data=ApiResult.success_dict(msg="查询成功", data=res_data))


class mobile_branch_status(viewsets.ViewSet):
    def list(self, request):
        status = []
        publish_info = request.GET['iteration_id[]']
        publish_info_dic = json.loads(publish_info)
        iteration_id = publish_info_dic['iterationID']
        logger.info(iteration_id)
        app_name = publish_info_dic['appName']
        results = get_branch_status(iteration_id)
        for i in results:
            status.append(i[3])
        if '上线中' in status:
            msg = '上线中'
        else:
            msg = '正常'
        return Response(data=ApiResult.success_dict(msg=msg, data=status))


class notice_apply_by_wechat(viewsets.ViewSet):
    def request_http(self, url, data=None):
        headers = {"Content-Type": "application/json;charset=utf-8"}
        if data:
            data = bytes(json.dumps(data), 'utf8')

        req = request.Request(url, data=data, headers=headers)
        page = json.loads(request.urlopen(req).read().decode('utf-8'))
        return page

    def send_wechat(self, to_users, send_msg):
        app_id = 1000063
        corp_id = "wx261bb2bb281b1cce"
        secret_id = "BAMQwu5VlUd1qUqRIlNs8_7_3VrjFhZEQL-sV6q0u0E"
        token_urls = "https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid=%s&corpsecret=%s" % (corp_id, secret_id)
        debug_access_token = self.request_http(token_urls)
        access_token = debug_access_token['access_token']
        purl = "https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token=%s" % access_token
        user_list = to_users
        for uid in user_list:
            send_body = {"touser": uid, "msgtype": "text", "agentid": app_id,
                         "text": {"content": send_msg}}
            req = self.request_http(purl, data=send_body)
        return req

    def list(self, request):
        # user_list = ["shuai.liu","huaitian.zhang","weimin.feng"]
        user_list = []
        apply_msg = {}
        if isinstance(request.user, str):
            to_user = request.user
        else:
            to_user = request.user.username
        user_list.append(to_user)
        iteration_id = request.GET.get("iteration_id")
        env = request.GET.get("env")
        apply_msg["env"] = env
        apply_msg["iteration_id"] = iteration_id
        send_msg = "申请失败\n[迭代]: {}\n[环境]: {}".format(iteration_id, env)
        self.send_wechat(user_list, send_msg)
        return Response(data=ApiResult.success_dict(msg="成功"))


class FindH5HDDistVersionByEnv(viewsets.ViewSet):

    @staticmethod
    def list(request):
        h5_platform_code = request.GET['h5_platform_code']
        suite_code = request.GET['suite_code']
        end_ver = request.GET['end_ver']
        try:
            branch_name = get_latest_h5_hd_branch(h5_platform_code, suite_code, end_ver)
            res_data = {"branch_name": branch_name}
            logger.debug("res_data==={}".format(str(res_data)))
        except Exception as err:
            return Response(data=ApiResult.failed_dict(msg="异常：" + str(err), data=res_data))
        return Response(data=ApiResult.success_dict(msg="查询成功", data=res_data))


class GetLastProdAndroidInfo(viewsets.ViewSet):

    def list(self, request):
        app_name = request.GET['app_name'] + "-android"
        android_last_prod_apk_list = get_prod_download_android_url(app_name)
        return Response(data=ApiResult.success_dict(msg="查询成功", data=android_last_prod_apk_list))


class GetLastProdDistInfo(viewsets.ViewSet):
    action_item = "publish_apply"

    def add_repo_info(self, publish_info):
        new_publish_info = []
        for item in publish_info:
            for appinfo in get_app_info_by_module_name(item["app_name"]):
                platform_name = appinfo[1]
                platform_code = appinfo[2]
            item.update({'platform_name': platform_name, 'platform_code': platform_code})
            new_publish_info.append(item)
        return new_publish_info

    def list(self, request):
        # iteration_app_list = []
        # fund_platform_list = []
        # piggy_platform_list = []
        # publish_info_list = []
        # diff_app_list = []
        # select_list = request.GET.getlist('select_list[]')
        # for item in select_list:
        #     iteration_app_list.append(
        #         {'app_name': json.loads(item).get('app_name'), 'platform_code': json.loads(item).get('platform_code')})
        #     if json.loads(item).get('platform_code') == 'fund-h5-res':
        #         fund_platform_list.append(json.loads(item).get('platform_code'))
        #     elif json.loads(item).get('platform_code') == 'piggy-h5-res':
        #         piggy_platform_list.append(json.loads(item).get('platform_code'))
        #
        # dist_app_info, dist_platform_app_list = get_h5_dist_app()
        # for it in dist_app_info:
        #     if it not in iteration_app_list and fund_platform_list and not piggy_platform_list:
        #         if it.get('platform_code') == 'fund-h5-res':
        #             diff_app_list.append(it)
        #     elif it not in iteration_app_list and piggy_platform_list and not fund_platform_list:
        #         if it.get('platform_code') == 'piggy-h5-res':
        #             diff_app_list.append(it)
        #     elif it not in iteration_app_list and piggy_platform_list and fund_platform_list:
        #         diff_app_list.append(it)
        #
        # for i in diff_app_list:
        #     last_arc_version = get_h5_dist_last_arc_info(i.get('app_name'))
        #     publish_info = PublishApplyInfoCollector().get_info(last_arc_version, [self.action_item], 'prod')
        #     email_info = PublishApplyInfoCollector.find_all_email(last_arc_version)
        #     for temp in publish_info:
        #         if temp.get('app_name') == i.get('app_name'):
        #             temp.update(email_info)
        #             publish_info_list.append(temp)
        #             break
        #
        # publish_info = self.add_repo_info(publish_info_list)
        # return Response(data=ApiResult.success_dict(msg="正在处理中", data={"ci_info": publish_info}))
        pass


class GetTestPublishDistInfo(viewsets.ViewSet):
    action_item = "publish_apply"

    def list(self, request):
        publish_info_list = []
        fund_platform = []
        piggy_platform = []
        iteration_app_list = []
        diff_app_list = []
        fund_dist = False
        piggy_dist = False
        select_list = request.GET.getlist('select_list[]')
        for item in select_list:

            item = json.loads(item)
            iteration_id = item.get('iteration_id')
            action_item = item.get('action_item')
            br_name = item.get('br_name')
            package_type = item.get('package_type')
            h5_env = item.get('h5_env')
            _checked = item.get('_checked')
            suite_code = item.get('suite_code')
            begin_ver = item.get('begin_ver')
            end_ver = item.get('end_ver')
            is_silent = item.get('is_silent')

            iteration_app_list.append(
                {'app_name': item.get('app_name'), 'platform_code': item.get('platform_code')})
            if item.get('platform_code') == 'fund-h5-res':
                fund_dist = True
                # fund_platform_list.append(item.get('platform_code'))
                fund_platform.append(item)
            elif item.get('platform_code') == 'piggy-h5-res':
                piggy_dist = True
                # piggy_platform_list.append(item.get('platform_code'))
                piggy_platform.append(item)
        dist_app_info, dist_platform_app_list = get_h5_dist_app()
        for it in dist_app_info:
            if it not in iteration_app_list and fund_dist and not piggy_dist:
                if it.get('platform_code') == 'fund-h5-res':
                    diff_app_list.append(it)
            elif it not in iteration_app_list and piggy_dist and not fund_dist:
                if it.get('platform_code') == 'piggy-h5-res':
                    diff_app_list.append(it)
            elif it not in iteration_app_list and piggy_dist and fund_dist:
                diff_app_list.append(it)
        for app in diff_app_list:
            app['br_name'] = ''
            last_arc_version = get_h5_dist_last_arc_info(app.get('app_name'))
            publish_info = PublishApplyInfoCollector().get_info(last_arc_version, [self.action_item], 'prod')
            for temp in publish_info:
                if temp.get('app_name') == app.get('app_name'):
                    app['br_name'] = temp['br_name']
                    app['iteration_id'] = iteration_id
                    break
            app['action_item'] = action_item
            app['package_type'] = package_type
            app['h5_env'] = h5_env
            app['_checked'] = 'false'
            app['suite_code'] = suite_code
            app['begin_ver'] = begin_ver
            app['end_ver'] = end_ver
            app['is_silent'] = is_silent
            for temp in publish_info:
                if temp.get('app_name') == app.get('app_name'):
                    publish_info_list.append(temp)
                    break
            if app.get('platform_code') == 'fund-h5-res':
                app['platform_code'] = 'fund-h5-res'
                # app['nf_app_name'] = 'nf-fund'
                fund_platform.append(app)
            elif app.get('platform_code') == 'piggy-h5-res':
                app['platform_code'] = 'piggy-h5-res'
                # app['nf_app_name'] = 'nf-piggy'
                piggy_platform.append(app)
        return Response(data=ApiResult.success_dict(msg="正在处理中", data={"fund_platform": fund_platform,
                                                                            "piggy_platform": piggy_platform,
                                                                            'fund_dist': fund_dist,
                                                                            'piggy_dist': piggy_dist}))


class GetLastProdDistAppInfo(viewsets.ViewSet):
    action_item = "publish_apply"

    def add_repo_info(self, publish_info):
        new_publish_info = []
        for item in publish_info:
            for appinfo in get_app_info_by_module_name(item["app_name"]):
                platform_name = appinfo[1]
                platform_code = appinfo[2]
            item.update({'platform_name': platform_name, 'platform_code': platform_code})
            new_publish_info.append(item)
        return new_publish_info

    def list(self, request):
        publish_info_list = []
        diff_app_list = []
        publish_info = {}
        fund_platform_list = request.GET.getlist('fund_h5_res[]')
        piggy_platform_list = request.GET.getlist('piggy_h5_res[]')
        dist_app_info, dist_platform_app_list = get_h5_dist_app()
        print(dist_app_info)
        for it in dist_app_info:
            if it.get('platform_code') == 'fund-h5-res':
                if it.get('app_name') not in fund_platform_list and fund_platform_list:
                    diff_app_list.append(it.get('app_name'))
            elif it.get('platform_code') == 'piggy-h5-res':
                if it.get('app_name') not in piggy_platform_list and piggy_platform_list:
                    diff_app_list.append(it.get('app_name'))
        for i in diff_app_list:
            last_arc_version = get_h5_dist_last_arc_info(i)
            publish_info = PublishApplyInfoCollector().get_info(last_arc_version, [self.action_item], 'prod')
            if not publish_info:
                publish_info.append({'app_name': i, 'br_name': '', 'iteration_id': '', 'action_item': 'publish_apply',
                                     'package_type': 'dist',
                                     'h5_env': '', '_checked': 'false', 'suite_code': '', 'begin_ver': '',
                                     'end_ver': '',
                                     'is_silent': '', 'platform_code': ''})
            email_info = PublishApplyInfoCollector.find_all_email(last_arc_version)
            for temp in publish_info:
                if temp.get('app_name') == i:
                    temp.update(email_info)
                    publish_info_list.append(temp)
                    break

        publish_info = self.add_repo_info(publish_info_list)
        return Response(data=ApiResult.success_dict(msg="正在处理中", data={"ci_info": publish_info}))
