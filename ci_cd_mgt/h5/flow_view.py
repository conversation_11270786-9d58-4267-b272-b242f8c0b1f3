from ci_cd_mgt.h5.db_ser import user_action_record
from ci_cd_mgt.publish_info_ser import get_suite_code_list
from iter_mgt.models import BranchIncludeSys, Branches
from spider.settings import logger, ApiResult
from rest_framework import viewsets
from rest_framework.response import Response
from django.db import connection


class HdStatusCheckApi(viewsets.ViewSet):

    def list(self, request, *args, **kwargs):
        app_name_list = request.GET.getlist('app_name_list[]')
        iteration_id = request.GET.get("iteration_id")
        logger.info(app_name_list)
        # sql_where = 'sys_status="灰度中" and appName in ("{}")'.format('","'.join(app_name_list))
        app_iter_list = self._get_app_hd_deploy_iter(app_name_list)
        conflict_app = []
        for row in app_iter_list:
            branch_apply_info = user_action_record.get_apply_user_and_time(row.get("app_name"), row.get("iteration_id"))
            branch_info = {"br_name": row.get("iteration_id"), "app_name": row.get("app_name")}
            branch_apply_info.update(branch_info)
            conflict_app.append(branch_apply_info)
        logger.info(conflict_app)
        conflict_app = self._dict_filter(conflict_app)
        if len(conflict_app) > 0:
            return Response(data=ApiResult.dicey_dict(msg="有其他分支正在灰度的应用,继续申请可能会覆盖其他分支应用在灰度上的部署，详情如下：",
                                                      data={"conflict_app": conflict_app}))
        else:
            return Response(data=ApiResult.success_dict(msg="没有其他正在灰度的应用"))

    def _dict_filter(self, conflict_list):
        new_conflict_list = []

        for row in conflict_list:
            flag = True
            for row1 in new_conflict_list:
                if row1.get("app_name") == row.get("app_name"):
                    flag = False
                    if row1.get("operate_time") is not None and row.get("operate_time") is not None and (
                            row1.get("operate_time") < row.get("operate_time")):
                        new_conflict_list[:] = [d for d in new_conflict_list if
                                                d.get('app_name') != row1.get("app_name")]
                        new_conflict_list.append(row)
                        break
            if flag:
                new_conflict_list.append(row)

        return new_conflict_list

    def _get_app_hd_deploy_iter(self, app_list):
        hd_suite_list = get_suite_code_list('hd')

        sql = 'select DISTINCT t.module_name,i.iteration_id from env_mgt_node_bind t left join env_mgt_suite s on ' \
              't.suite_id = s.id left join product_mgt_product_info i on i.id = t.lib_repo_info_id where ' \
              't.module_name in ("{}" )and s.suite_code in ( "{}") '.format('","'.join(app_list),
                                                                            '","'.join(hd_suite_list))
        logger.info(sql)
        cursor = connection.cursor()
        cursor.execute(sql)
        result = {}
        app_iter_list = []
        for row in cursor.fetchall():
            if row[1]:
                result = {"app_name": row[0], "iteration_id": row[1]}
                app_iter_list.append(result)
        return app_iter_list
