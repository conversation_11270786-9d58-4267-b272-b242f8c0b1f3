from ci_cd_mgt.h5.app_task_queue import AppTaskQueue
from ci_cd_mgt.h5.view import H5CIPipelineApi
from iter_mgt.models import Branches
from public.template.h5_apply_template import apply_email_template
from spider.settings import logger
from task_mgt.task_queue import TaskTypeObject


class AppCIPipeline(H5CIPipelineApi):
    """app测试发布，后期出现多处调用时可以将TasKQueue抽离外部继承"""
    business_name_dict = {
        "ios": "app_ios_publish",
        "android": "app_android_publish",
        "ios-com": "app_component_publish",
        "android-com": "app_component_publish",
        "android-global": "app_android_publish",
        "ios-global": "app_ios_publish"
    }

    def check_stage(self, action_id, request_data, task_queue):
        iteration_id = request_data["iterationID"]
        suite_code = request_data["appEnv"]
        app_name_list = request_data['appNameList']
        logger.info(iteration_id)
        logger.info(app_name_list)
        for check_business_name in self.check_business_name_list:
            check_params = {
                'business_name': check_business_name,
                'iteration_id': iteration_id,
                'app_name_list': app_name_list,
                'env': suite_code
            }
            # 添加检查任务
            task_queue.enter_queue(TaskTypeObject.script, check_business_name, action_id, check_params)

    def ci_publish_info(self, params, action_id=None):
        """重写，params为列表和字典的处理"""
        call_job_param_dict = {}
        if isinstance(params, list):
            for row in params:
                if row["packageType"] in self.business_name_dict:
                    if self.business_name_dict[row["packageType"]] in call_job_param_dict:
                        call_job_param_dict[self.business_name_dict[row["packageType"]]].append(row)
                    else:
                        call_job_param_dict[self.business_name_dict[row["packageType"]]] = [row]
        else:
            if params["packageType"] in self.business_name_dict:
                call_job_param_dict[self.business_name_dict[params["packageType"]]] = [params]

        return call_job_param_dict

    @staticmethod
    def get_email_info(user, request_data):
        """获取app发送电子邮件的信息"""
        res = Branches.objects.filter(pipeline_id=request_data['iterationID']).first().description
        content = apply_email_template.format(team="app",
                                              branch_name=request_data['appBranch'],
                                              applicant=user,
                                              description=res,
                                              app_name_list=request_data['appNameList'],
                                              dist_package_info="")
        subject = '{}分支{}环境上线申请通知'.format(request_data['appBranch'], request_data['appEnv'])
        return {"content": content, "subject": subject, "mail_to": request_data["cc"]}

    def run_stage(self, user, action_id, request_data):
        """测试发布阶段执行"""
        logger.info("run_stage调用开始%s,%s,%s", user, action_id, request_data)
        task_queue = AppTaskQueue(task_queue=[])
        # 调用发布jenkins job阶段
        call_job_param_dict = self.ci_publish_info(request_data, action_id=action_id)
        logger.info(call_job_param_dict)
        task_queue.enter_queue(TaskTypeObject.jenkins, call_job_param_dict.keys(), action_id, call_job_param_dict)
        task_queue.async_run()

    def run_publish_apply_stage(self, user, param, request_data):
        """发布申请阶段执行"""
        logger.info("run_stage调用开始%s,%s,%s", user, param["actionId"], request_data)
        task_queue = AppTaskQueue(task_queue=[])
        # 责任链检查任务 加入队列
        self.check_stage(param["actionId"], param, task_queue)

        # 发布jenkins job加入队列
        call_job_param_dict = self.ci_publish_info(request_data, action_id=param["actionId"])
        logger.info(call_job_param_dict)
        task_queue.enter_queue(TaskTypeObject.jenkins, call_job_param_dict.keys(), param["actionId"],
                               call_job_param_dict)
        # 获取邮件任务 加入队列
        email_info = self.get_email_info(user, param)
        task_queue.enter_queue(TaskTypeObject.email, "send_email", param["actionId"], email_info)

        # 记录上线申请单 加入队列
        # task_queue.enter_queue(TaskTypeObject.recode_publish_application, request_data, action_id=param["actionId"])
        task_queue.async_run()


class AppTagCIPipeline(AppCIPipeline):
    business_name_dict = {
        "ios": "app_tag_ios_publish",
        "android": "app_tag_android_publish",
    }
