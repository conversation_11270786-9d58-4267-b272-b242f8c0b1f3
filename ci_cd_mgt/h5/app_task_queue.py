import time

from ci_cd_mgt.h5.db_ser.deploy_result import DeployResult
from ci_cd_mgt.h5.db_ser.iter_mgt_iter_app_info import IterMgtAppInfo
from ci_cd_mgt.h5.enums import DeployBaseStatusEnum
from ci_cd_mgt.h5.view import H5CIPipelineApi
from spider.settings import logger
from task_mgt.jenkins_task.call_job import JenkinsCaller
from task_mgt.models import JenkinsResults, HmOptLogMain
from task_mgt.task_queue import TaskQueue
from task_mgt.jenkins_task.call_job import JenkinsCaller


class AppTaskQueue(TaskQueue):
    """app任务队列"""
     # todo app搞了个单独 jenkins 队列调用调用
    def run_jenkins(self, q_obj):
        """调用jenkins 直到成功or失败 注：只会有一个business_name
           :param q_obj:
           :return:
         """
        final_status = JenkinsResults.SUCCESS
        res_list = []
        for business_name in q_obj.business_name:
            for row in q_obj.params[business_name]:
                logger.info(row)
                jenkins_caller = <PERSON><PERSON>aller(business_name, row["iterationID"], row["appName"])
                res_dict = self.exec_jenkins(jenkins_caller, q_obj.action_id, row)
                if res_dict['request_status'] == JenkinsResults.FAILURE:
                    DeployResult.update_deploy_result_data_by_id(
                        row['deploy_status_id'],
                        DeployResult.op_type_to_status["test_publish"][DeployBaseStatusEnum.BASE_STATUS.failure],
                        res_dict['request_result'], res_dict['job_name'])
                    return JenkinsResults.FAILURE, res_list
                else:
                    H5CIPipelineApi.update_deploy_result_data(
                        row['deploy_status_id'],
                        DeployResult.op_type_to_status["test_publish"][DeployBaseStatusEnum.BASE_STATUS.running],
                        res_dict['request_result'], res_dict['job_name'])
                res_dict['app_name_list'] = [row['appName']]
                res_dict['iteration_id'] = row['iterationID']
                res_dict['business_name'] = business_name
                res_dict['action_item'] = row['actionItem']
                res_dict['env'] = row['appEnv']
                res_list.append(res_dict)
        # 建立jenkins连接，确定jenkins——job执行情况，如果job有变更，更新状态（停止）,一次只会有一个business_name所以可以放外面
        final_status = self.check_jenkins_run_status(business_name, final_status, q_obj, res_list)
        return final_status, res_list

    def check_jenkins_run_status(self, business_name, final_status, q_obj, res_list):
        """检查jenkins的job执行状态，并循环更新"""
        jenkins_caller = JenkinsCaller(business_name)
        count = 0
        logger.info(res_list)
        final_status_list = []
        while count < len(res_list):
            for res in res_list:
                if 'done' not in res:
                    sql_where = 'iteration_id="{}" and action_id ={} and app_name in ("{}")'. \
                        format(res["iteration_id"], q_obj.action_id, '","'.join(res["app_name_list"]))
                    if res["request_status"] == JenkinsResults.FAILURE:
                        final_status = JenkinsResults.FAILURE
                        DeployResult.update_result_by_sql_where(
                            sql_where,
                            DeployResult.op_type_to_status["test_publish"][DeployBaseStatusEnum.BASE_STATUS.running],
                            res["request_result"]
                        )
                        # 执行后+1
                        final_status_list.append(final_status)
                        res['done'] = True
                        count += 1
                    else:
                        # jenkins_state = jenkins_caller.jenkins_build_id_state(res["job_name"], res["request_result"])
                        # if jenkins_state:
                        #     if jenkins_state == 'ABORTED':
                        #         status, detail = HmOptLogMain.ABORTED, '已终止'
                        #     elif jenkins_state == 'FAILURE':
                        #         status, detail = self.get_exec_jenkins_result(res["job_name"], res["request_result"])
                        #         detail = 'jenkins job运行失败,失败原因未记录' if status == HmOptLogMain.RUNNING else detail
                        #         status = HmOptLogMain.FAILURE
                        #     else:

                        #todo  app特殊的继承关系 20210927 后期统一
                        status, detail = self.get_jenkins_result(res["job_name"], res["request_result"])
                        logger.info(status)
                        logger.info(detail)
                        if status in (HmOptLogMain.SUCCESS, HmOptLogMain.FAILURE, HmOptLogMain.ABORTED):
                            if status == HmOptLogMain.FAILURE:
                                final_status = JenkinsResults.FAILURE
                                status = DeployResult.op_type_to_status["test_publish"][
                                    DeployBaseStatusEnum.BASE_STATUS.failure]

                            DeployResult.update_result_by_sql_where(sql_where, status, detail)
                            logger.info(status)
                            logger.info(detail)
                            # 执行后+1
                            final_status_list.append(final_status)
                            res['done'] = True
                            count += 1
            time.sleep(3)
        # fixme 把更新应用迭代状态临时加在这个地方，后期放到任务队列中触发,未做进一步的状态判断处理 by 黄敏
        # if final_status == JenkinsResults.SUCCESS:
        flag = True
        for status in final_status_list:
            if status != JenkinsResults.SUCCESS:
                flag = False
                break
        if flag:
            for res in res_list:
                IterMgtAppInfo.update_sys_status(res)

        return JenkinsResults.SUCCESS if flag else JenkinsResults.FAILURE
