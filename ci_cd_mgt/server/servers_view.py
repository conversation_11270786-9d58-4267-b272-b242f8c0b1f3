from ci_cd_mgt.h5.view import H5PublishApply<PERSON>pi
from env_mgt import env_info_ser
from spider.settings import logger
from task_mgt.task_queue import TaskQueue, TaskTypeObject


class ServersPublishApplyApi(H5PublishApplyApi):
    action_item = "servers_publish_apply"

    def add_zeus_queue(self, action_id, br_name, task_queue, namespace, suite_code):
        # 回合前暂时先同步，后面再去掉，减少交互  20250402 by fwm
        task_queue.enter_queue(TaskTypeObject.interface,
                               "sync_config",
                               action_id,
                               {"iteration_number": br_name,
                                "app_name": namespace,
                                "environments": suite_code})

        # 宙斯配置回合提前 @20250320 by zhangwenlong
        task_queue.enter_queue(TaskTypeObject.interface,
                               "auto_merge_zeus",
                               action_id,
                               {"iteration_number": br_name,
                                "app_name": namespace
                                })
        task_queue.enter_queue(TaskTypeObject.interface,
                               "sync_config",
                               action_id,
                               {"iteration_number": br_name,
                                "app_name": namespace,
                                "environments": suite_code})

        task_queue.enter_queue(TaskTypeObject.interface,
                               "check_config_consistent",
                               action_id,
                               {"iteration_number": br_name,
                                "app_name": namespace,
                                "tenant_id": suite_code
                                })
        # 添加 配置为空 检测 20240328 by fwm
        # task_queue.enter_queue(TaskTypeObject.interface,
        #                        "check_config_sync",
        #                        action_id,
        #                        {"iteration_number": br_name,
        #                         "app_name": namespace,
        #                         "tenant_id": suite_code
        #                         })

        # 添加自动回合(此为服务端申请专用，继承自h5_test。)。zt@2025-01-21

        # 添加回合检查 20210419 by 帅
        task_queue.enter_queue(TaskTypeObject.interface,
                               "check_config_merge",
                               action_id,
                               {"iteration_number": br_name,
                                "app_name": namespace
                                })

        # task_queue.enter_queue(TaskTypeObject.interface,
        #                        "publish_config",
        #                        action_id,
        #                        {"iteration_number": br_name,
        #                         "app_name": namespace,
        #                         "environment": suite_code
        #                         })

    def zeus_stage(self, action_id, request_data, task_queue):
        """
        临时给服务端加一个灾备的步骤 by帅 20220328
        :param action_id:
        :param request_data:
        :param task_queue:
        :return:
        """
        br_name = request_data[0]["br_name"]
        suite_code = request_data[0]["suite_code"]
        # app_name_list = []
        # 根据可用域 获取应用绑定的环境套 by 帅 20220524
        for row in request_data:
            # app_name_list.append(row["app_name"])
            if suite_code == "prod":
                for res in env_info_ser.get_suite_name([row["app_name"]], ['prod', 'zb']):
                    logger.info("同步宙斯的环境套为{}".format(res[0]))
                    self.add_zeus_queue(action_id, br_name, task_queue, row["app_name"], res[0])
            else:
                self.add_zeus_queue(action_id, br_name, task_queue, row["app_name"], suite_code)

    def run_stage(self, user, action_id, request_data):
        task_queue = TaskQueue(task_queue=[])
        # 校验阶段
        iteration_id = request_data[0]["iteration_id"]
        # 如果是产线环境增加校验阶段
        if request_data[0]["suite_code"] == "prod":
            self.check_stage(action_id, request_data, task_queue, self.check_business_name_dict)
        # 发布阶段
        # 发布配置阶段
        self.zeus_stage(action_id, request_data, task_queue)
        logger.info(request_data)

        # todo 目前服务端发布申请功能没有做成jenkins，需要未来改造后逻辑才能全部移到后端 by帅 20220316
        task_queue.enter_queue(TaskTypeObject.script_publish_apply, self.action_item, action_id, {})

        task_queue.async_run()
