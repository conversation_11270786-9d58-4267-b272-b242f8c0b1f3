from django.db import connection
from spider.settings import logger


class AppExpressBuildServiceSer:
    h5_platform_combinations = {"fund-h5-res": ("fund-ios", "fund-android"),
                                "piggy-h5-res": ("piggy-ios", "piggy-android")}

    def get_app_by_h5(self, h5_list):
        app_list = []
        for h5_name in h5_list:
            app_list.extend(self.h5_platform_combinations[h5_name])
        return app_list

    def get_h5_platform_by_app(self, app_name):
        for key, value in self.h5_platform_combinations.items():
            if app_name in value:
                return key
        return None

    def get_app_online_version(self, h5_list):
        app_list = self.get_app_by_h5(h5_list)
        cursor = connection.cursor()
        sql = '''
        SELECT lt.appName,tt.br_name,pp.end_ver,lt.last_end_date,tt.pipeline_id,bb.package_type,
        CONCAT(ff.git_url,ff.git_path) AS git_repo,ff.git_url
         FROM (SELECT i.appName,MAX(t.br_end_date) 
        AS last_end_date FROM iter_mgt_iter_app_info i 
LEFT JOIN iter_mgt_iter_info t ON i.pipeline_id = t.pipeline_id 
WHERE i.appName IN ("{}") AND t.br_status = "close" GROUP BY i.appName) lt LEFT JOIN 
iter_mgt_iter_info tt ON tt.br_end_date = lt.last_end_date LEFT JOIN hm_package_main_info pp ON tt.br_name = 
pp.br_name AND lt.appName = pp.app_name
LEFT JOIN app_mgt_app_build bb ON bb.module_name = lt.appName 
LEFT JOIN app_mgt_app_info ff ON bb.app_id = ff.id
WHERE pp.suite_code = "prod"
        '''.format('","'.join(app_list))
        app_online_version_list = []

        cursor.execute(sql)
        logger.info(sql)
        for row in cursor.fetchall():
            app_online_version_list.append({"app_name": row[0],
                                            "br_name": row[1],
                                            "online_version": row[2],
                                            "last_end_date": row[3],
                                            "env": "beta",
                                            "iter_id": row[4],
                                            "package_type": row[5],
                                            "git_repo": row[6],
                                            "h5_platform_code": self.get_h5_platform_by_app(row[0]),
                                            "group_name": row[7],
                                            "loading_btn": False,
                                            "app_version": row[2] + ".0"})

        return app_online_version_list
