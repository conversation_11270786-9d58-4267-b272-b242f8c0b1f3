from django.db import connection
from spider.settings import logger

#编译




"""
SELECT*FROM task_mgt_deploy_result re RIGHT JOIN
task_mgt_jenkins_results je ON re.action_id = je.action_id
AND CONCAT("h5_h5-pkg-fund_0622_zhongou_",re.app_name) = je.job_name
RIGHT JOIN
(SELECT  t.app_name, MAX(t.id) AS id FROM task_mgt_deploy_result t
LEFT JOIN user_action_record u ON  u.id = t.action_id
 WHERE t.app_name IN (
SELECT i.appName FROM iter_mgt_iter_app_info i LEFT JOIN app_mgt_app_build b
ON i.appName = b.module_name
 WHERE pipeline_id = "h5_h5-pkg-fund_0622_zhongou" AND b.package_type = "dist"
 )
  AND u.action_item = "h5_h5-pkg-fund_0622_zhongou_test_dist_compile"
 GROUP BY app_name
  ) act ON act.id =re.id
"""


