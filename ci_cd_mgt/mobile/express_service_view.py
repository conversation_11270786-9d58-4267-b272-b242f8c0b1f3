from rest_framework import viewsets
from rest_framework.response import Response

from spider.settings import logger, ApiResult
from ci_cd_mgt.mobile.express_service_ser import AppExpressBuildServiceSer


class AppExpressBuildService(viewsets.ViewSet):
    __app_express_ser = AppExpressBuildServiceSer

    def list(self, request, *args, **kwargs):
        logger.info(request.GET)
        h5_platform_code_list = request.GET.getlist("h5_platform_code[]")
        logger.info(h5_platform_code_list)
        app_express_ser = self.__app_express_ser()
        app_online_version_info = app_express_ser.get_app_online_version(h5_platform_code_list)

        return Response(data=ApiResult.success_dict(msg="查询salt命令成功",
                                                    data={"app_online_version_info": app_online_version_info}))
