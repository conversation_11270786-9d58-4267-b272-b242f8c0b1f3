from ci_cd_mgt.h5.app_task_queue import AppTaskQueue
from ci_cd_mgt.h5.view import H5CIPipelineApi
from spider.settings import logger, ApiResult
from task_mgt.task_queue import TaskQueue, TaskTypeObject


class H5TestPublishApi(H5CIPipelineApi):
    action_item = "app_test_publish"
    business_name_dict = {
        "ios": "app_ios_publish",
        "android": "app_android_publish",
    }
    def run_stage(self, user, action_id, request_data):
        task_queue = TaskQueue(task_queue=[])
        # 校验阶段
        self.check_stage(action_id, request_data, task_queue)
        # 发布阶段
        call_job_param_dict = self.ci_publish_info(request_data, action_id=action_id)
        logger.info(call_job_param_dict)
        task_queue.enter_queue(TaskTypeObject.jen<PERSON>, call_job_param_dict.keys(), action_id,
                               call_job_param_dict)
        # task_queue.run()
        task_queue.async_run()
