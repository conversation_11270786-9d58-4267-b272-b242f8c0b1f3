from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

from ci_cd_mgt.h5 import view, suite_info_view, h5_stats, flow_view, nf_view, app_view
from ci_cd_mgt.mobile.express_service_view import AppExpressBuildService

router = DefaultRouter()

router.register(r'h5_compile_api', view.H5CompileApi, basename="h5_compile_api")
router.register(r'h5_auto_compile_api', view.H5AutoCompileApi, basename="h5_auto_compile_api")
router.register(r'app_express_build_service_api', AppExpressBuildService, basename="app_express_build_service_api")


urlpatterns = [
    path("", include(router.urls))
]
