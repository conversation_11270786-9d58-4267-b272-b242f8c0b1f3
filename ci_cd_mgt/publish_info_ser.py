import datetime
import json

from django.db import connection

# from ci_cd_mgt.publish_info_ser import get_publish_type_by_action_id, get_publish_salt_result
from iter_mgt.models import PublishApplicationConfirmation
from spider.settings import logger, JENKINS_INFO
from task_mgt.config.analysis_ini import LoadConfig
from task_mgt.models import H5DeployResult, HmOptLogMain

try:
    from pymysql.converters import escape_string
except:
    import MySQLdb.escape_string


class DataDuplicationException(Exception):
    """ 数据重复异常"""

    def __init__(self, data):
        self.msg = 'ERROR: {} 数据重复.'.format(data)


class DataMissingException(Exception):
    """ 数据重复异常"""

    def __init__(self, data):
        self.msg = 'ERROR: {} 的数据缺失.'.format(data)


def get_app_publish_info(suite_code, module_name, multi_node=False):
    """
    获取环境套
    module_name: .str 应用名
    suite_code : .环境信息
    return : dict
    """
    cursor = connection.cursor()
    """前端传prod环境，SQL适配为产线组进行查询 20211117 by fwm"""
    if suite_code == 'prod':
        cursor.execute('''
                SELECT m.module_name,b.deploy_type,IF (n.node_ip is NULL,"",n.node_ip) as node_ip,m.lib_repo,
                d.package_type,m.zeus_type,IF (na.namespace IS NULL,m.module_name,na.namespace) AS namespace
                 FROM env_mgt_node_bind b 
                LEFT JOIN env_mgt_node n ON n.id = b.node_id 
                LEFT JOIN  env_mgt_suite s ON s.id = b.suite_id
                LEFT JOIN app_mgt_app_module m ON m.module_name = b.module_name
                LEFT JOIN app_mgt_app_build d ON d.module_name = m.module_name
                LEFT JOIN app_mgt_nacos_namespace_info na ON na.module_name=m.module_name
                LEFT JOIN env_mgt_region emr ON emr.id = s.region_id
                WHERE emr.region_group = "{}" AND m.module_name = "{}"'''
                       .format(suite_code, module_name))
    else:
        cursor.execute('''
            SELECT m.module_name,b.deploy_type,IF (n.node_ip is NULL,"",n.node_ip) as node_ip,m.lib_repo,
            d.package_type,m.zeus_type,IF (na.namespace IS NULL,m.module_name,na.namespace) AS namespace
             FROM env_mgt_node_bind b 
            LEFT JOIN env_mgt_node n ON n.id = b.node_id 
            LEFT JOIN  env_mgt_suite s ON s.id = b.suite_id
            LEFT JOIN app_mgt_app_module m ON m.module_name = b.module_name
            LEFT JOIN app_mgt_app_build d ON d.module_name = m.module_name
            LEFT JOIN app_mgt_nacos_namespace_info na ON na.module_name=m.module_name
            WHERE s.suite_code = "{}" AND m.module_name = "{}"'''
                       .format(suite_code, module_name))
    publish_info = {}
    for row in cursor.fetchall():
        if publish_info == {}:
            publish_info["module_name"] = row[0]
            publish_info["deploy_type"] = row[1]
            publish_info["node_ip"] = row[2]
            publish_info["lib_repo"] = row[3]
            publish_info["package_type"] = row[4]
            publish_info["zeus_type"] = row[5]
            publish_info["namespace"] = row[6]
        else:
            if multi_node:
                # 查出多行数据，说明数据绑定重复
                return publish_info
            else:
                raise DataDuplicationException("env_mgt_node_bind table {}绑定的{}".format(row[0], suite_code))
    if publish_info == {}:
        raise DataMissingException("未查到应用{} 与 环境{}的绑定关系，请先去绑定！".format(module_name, suite_code))
    return publish_info


def get_nf_app_info(module_name):
    """
    获取nf 应用相关发布打包相关信息 by 帅 20210316
    module_name: .str 应用名

    return : dict
    """
    cursor = connection.cursor()
    cursor.execute('''
        SELECT m.module_name,m.lib_repo,
      m.zeus_type,IF (na.namespace IS NULL,m.module_name,na.namespace) AS namespace
         FROM app_mgt_app_module m 
        LEFT JOIN app_mgt_nacos_namespace_info na ON na.module_name=m.module_name
        WHERE m.module_name = "{}"'''
                   .format(module_name))
    publish_info = {}
    for row in cursor.fetchall():
        if publish_info == {}:
            publish_info["nf_lib_repo"] = row[1]
            publish_info["nf_zeus_type"] = row[2]
            publish_info["nf_namespace"] = row[3]

    if publish_info == {}:
        raise DataMissingException("module_name table {}应用没有数据".format(module_name))
    return publish_info


def get_suite_code_by_region_group(region_group_list):
    """
    获取可用域下可用的环境
    :param region_group_list:
    :return:
    """
    sql = '''
        SELECT distinct su.suite_code 
        FROM env_mgt_suite su
        INNER JOIN env_mgt_region  re ON re.id =su.region_id 
        WHERE re.region_group in ("{}") 
        AND su.suite_is_active = 1
        AND re.region_is_active = 1;
        '''.format('","'.join(region_group_list))
    cursor = connection.cursor()
    logger.debug(sql)
    cursor.execute(sql)
    suite_code_list = []
    for row in cursor.fetchall():
        suite_code_list.append(row[0])
    return suite_code_list


def get_iter_suite(region_name_list, iteration_id):
    """
    获取可用域下 迭代存在的环境套数量
    :param region_name_list:
    :param iteration_id:
    :return:
    """
    sql = '''
        SELECT ap.appName,su.suite_code FROM iter_mgt_iter_app_info ap
    LEFT JOIN env_mgt_node_bind b ON ap.appName = b.module_name
    LEFT JOIN env_mgt_suite su ON su.id = b.suite_id 
    LEFT JOIN env_mgt_region  re ON re.id =su.region_id 
    WHERE  ap.pipeline_id = "{}" and re.region_name in ("{}");
        '''.format(iteration_id, '","'.join(region_name_list))
    cursor = connection.cursor()
    logger.debug(sql)
    cursor.execute(sql)
    app_suite_info = {}
    for row in cursor.fetchall():
        if row[0] in app_suite_info:
            app_suite_info[row[0]].append(row[1])
        else:
            app_suite_info[row[0]] = [row[1]]
    return app_suite_info


def get_last_task_h5_deploy_info(op_type, iteration_id, suite_name, tag_name):
    """
    获取分支和环境对应的最近一次h5部署信息
    :param op_type:
    :param iteration_id:
    :param suite_name:
    :param tag_name:
    :return:
    """
    if tag_name:
        sql = '''
                select t.action_id,t.app_name, t.status, t.message from task_mgt_deploy_result t where t.op_time = 					 
                (select MAX(op_time) from task_mgt_deploy_result t where op_type = '{}' 
                and iteration_id = '{}' and suite_name = '{}' and job_name like "%_template"
                GROUP BY op_type) ;
             '''.format(op_type, iteration_id, suite_name)
    else:
        sql = '''
                select t.action_id,t.app_name, t.status, t.message from task_mgt_deploy_result t where t.op_time = 					 
                    (select MAX(op_time) from task_mgt_deploy_result t where op_type = '{}' 
                    and iteration_id = '{}' and suite_name = '{}'
                    GROUP BY op_type) ;
              '''.format(op_type, iteration_id, suite_name)
    cursor = connection.cursor()
    logger.debug(sql)
    cursor.execute(sql)
    publish_info_list = []
    for row in cursor.fetchall():
        publish_info = {}
        publish_info['action_id'] = row[0]
        publish_info['app_name'] = row[1]
        publish_info['status'] = row[2]
        publish_info_list.append(publish_info)
    return publish_info_list


def get_jenkins_id(action_id):
    """
        获取分支和环境对应的最近一次
        :param action_id:
        :return:
    """
    sql = """
        select id, request_status  from task_mgt_jenkins_results where action_id = '{}'
        """.format(action_id)
    cursor = connection.cursor()
    logger.debug(sql)
    cursor.execute(sql)
    jenkins_info_list = []
    for row in cursor.fetchall():
        jenkins_info = {}
        jenkins_info['jenkins_id'] = row[0]
        jenkins_info['jenkins_status'] = row[1]
        jenkins_info_list.append(jenkins_info)
    return jenkins_info_list


def get_last_job_url(jenkins_id_list):
    """
    获取分支和环境对应的最近一次
    :param jenkins_id_list:
    :return:
    """
    # /数字list转换成字符串
    id_list = ','.join('%s' % id for id in jenkins_id_list)

    sql = '''
            select job_url from hm_optlog_main where jenkins_id in ({}) 
          '''.format(id_list)
    cursor = connection.cursor()
    logger.debug(sql)
    cursor.execute(sql)
    job_url = []
    for row in cursor.fetchall():
        job_url.append(row[0])

    return job_url


class PublishInfoCollector:
    sql = '''
     SELECT ap.appName,m.job_url,m.job_name,m.status,ac.username,ac.action_item,bd.package_type,
    CONCAT(s.git_url,s.git_path),ac.operate_time,ac.action_value AS git_path FROM iter_mgt_iter_app_info ap
    LEFT JOIN app_mgt_app_build bd ON bd.module_name=ap.appName
    LEFT JOIN app_mgt_app_info s ON  bd.app_id =s.id 
    LEFT JOIN user_action_record ac ON ap.{data_type_id}=ac.id
    LEFT JOIN task_mgt_jenkins_results je ON je.action_id=ac.id
    LEFT JOIN hm_optlog_main m  ON m.job_name = je.job_name AND m.job_build_id=je.build_id 
        WHERE pipeline_id = "{iteration_id}";
    '''

    def __init__(self, data_type_id="ci_action_id"):
        """

        :param data_type_id: 需要获取ci数据还是发布申请数据 ci_action_id or publish_apply_action_id
        """
        self.data_type_id = data_type_id

    def get_app_apply_info(self, iteration_id):
        sql = """SELECT ap.appName,acr.action_value FROM iter_mgt_iter_app_info ap
        LEFT JOIN user_action_record acr ON ap.{}=acr.id
        WHERE pipeline_id = "{}" """.format(self.data_type_id, iteration_id)
        logger.debug(sql)
        cursor = connection.cursor()
        all_suite_code_list = []
        for row in cursor.fetchall():
            app_name = row[0]
            action_value = row[1]
            all_suite_code_list = []
            if action_value:
                suite_code_list = self.get_env_info(action_value, app_name)
                if len(suite_code_list) > 0:
                    for suite_code in suite_code_list:
                        if suite_code not in all_suite_code_list:
                            all_suite_code_list.append(suite_code)

    def get_env_info(self, action_value, app_name):
        """
        获取应用发布的环境信息
        :param action_value:
        :param app_name:
        :return:
        """
        suite_code_list = []
        action_value = action_value.replace(" ", "").replace("'", '"')
        action_value_dict = json.loads(action_value)
        for value in action_value_dict:
            if value["app_name"] == app_name:
                suite_code_list.append(value["suite_code"])
        return suite_code_list

    def get_pkg_info(self, action_value, app_name):
        action_value = action_value.replace(" ", "").replace("'", '"')
        action_value_dict = json.loads(action_value)
        for value in action_value_dict:
            if value["app_name"] == app_name:
                return value["begin_ver"], value["end_ver"]

    def produce_data(self, iteration_id, cursor):
        publish_info = []
        load_config = LoadConfig()
        h5_app_job_list = [load_config.loading("jenkins")["h5_test_app_publish"]["job_name"],
                           load_config.loading("jenkins")["h5_app_publish_apply"]["job_name"]]
        h5_remote_job_list = [load_config.loading("jenkins")["h5_test_remote_publish"]["job_name"],
                              load_config.loading("jenkins")["h5_remote_publish_apply"]["job_name"]]
        logger.debug(h5_app_job_list)
        logger.debug(h5_remote_job_list)

        for row in cursor.fetchall():
            action_item = row[5]
            job_name = row[2]
            app_name = row[0]
            package_type = row[6]
            suite_code_list = []
            begin_ver = ""
            end_ver = ""
            logger.debug(row)
            if job_name:
                if action_item == "h5_compile":
                    if job_name != iteration_id + "_" + app_name:
                        continue
                else:
                    if package_type == "dist":
                        logger.debug(job_name)
                        logger.debug(h5_app_job_list)
                        if job_name not in h5_app_job_list:
                            continue
                    elif package_type in ("remote", "static"):
                        if job_name not in h5_remote_job_list:
                            continue
                    if row[9]:
                        suite_code_list = self.get_env_info(row[9], app_name)
                        if package_type == "dist":
                            begin_ver, end_ver = self.get_pkg_info(row[9], app_name)
            # else:
            #     if job_name != load_config.loading("jenkins")[action_item]:
            #         continue
            publish_info.append({"app_name": app_name,
                                 "job_url": row[1],
                                 "job_name": job_name,
                                 "status": row[3],
                                 "username": row[4],
                                 "action_item": action_item,
                                 "package_type": package_type,
                                 "git_path": row[7],
                                 "operate_time": row[8],
                                 "suite_code": suite_code_list,
                                 "begin_ver": begin_ver,
                                 "end_ver": end_ver})
        logger.debug(publish_info)
        return publish_info

    def get_info(self, iteration_id):
        """
        获取环境套
        module_name: .str 应用名
        suite_code : .环境信息
        return : dict
        """
        cursor = connection.cursor()
        logger.debug(self.sql)
        exec_sql = self.sql.format(data_type_id=self.data_type_id, iteration_id=iteration_id)
        logger.debug(exec_sql)
        cursor.execute(exec_sql)
        publish_info = self.produce_data(iteration_id, cursor)
        return publish_info


class SlatPublishInfoCollector(PublishInfoCollector):
    sql = '''
  SELECT ap.appName,ac.username,ac.action_item,bd.package_type,sa.request_status,sa.request_result,
CONCAT(s.git_url,s.git_path) AS git_path,ac.operate_time,ac.action_value,be.status as check_status,
                 be.detail as check_detail FROM iter_mgt_iter_app_info ap
LEFT JOIN app_mgt_app_build bd ON bd.module_name=ap.appName
LEFT JOIN app_mgt_app_info s ON  bd.app_id =s.id 
LEFT JOIN user_action_record ac ON ap.{data_type_id}=ac.id
 LEFT JOIN task_mgt_service_results be ON be.operator=ac.id
LEFT JOIN task_mgt_salt_operation_results sa ON sa.action_id=ac.id 
WHERE pipeline_id = "{iteration_id}" 
    '''

    def get_ip_info(self, action_value, app_name):
        """
        获取发布的节点信息
        :param action_value:
        :param app_name:
        :return:
        """
        ip_list = []
        action_value = action_value.replace(" ", "").replace("'", '"')
        action_value_dict = json.loads(action_value)
        for value in action_value_dict:
            if value["app_name"] == app_name:
                ip_list.append(value["ip_list"])
        return ip_list

    def produce_data(self, iteration_id, cursor):
        publish_info = []
        for row in cursor.fetchall():
            app_name = row[0]
            username = row[1]
            action_item = row[2]
            package_type = row[3]
            status = row[4]
            result = row[5]
            git_path = row[6]
            operate_time = row[7]
            action_value = row[8]
            check_status = row[9]
            check_detail = row[10]
            suite_code = ""
            ip_list = []
            if action_value:
                suite_code = self.get_env_info(action_value, app_name)
                ip_list = self.get_ip_info(action_value, app_name)

            publish_info.append({"app_name": app_name,
                                 "status": status,
                                 "username": username,
                                 "action_item": action_item,
                                 "package_type": package_type,
                                 "git_path": git_path,
                                 "result": result,
                                 "operate_time": operate_time,
                                 "suite_code": suite_code,
                                 "ip_list": ip_list,
                                 "check_status": check_status,
                                 "check_detail": check_detail})
        logger.debug(publish_info)
        return publish_info


class H5DeployStatusCollector:

    @staticmethod
    def update_h5_deploy_status(action_id, status, message, app_name=None, ip=None):
        """

        :param app_name:
        :param message:
        :param status:
        :param action_id:
        :param ip:
        :return:
        """
        sql = ''' update task_mgt_deploy_result set status="{}",message='{}' where action_id ="{}" ''' \
            .format(status, escape_string("{}".format(message)), action_id)
        if status == 'failure':
            sql = ''' update task_mgt_deploy_result set status="{}",message='{}' where action_id ="{}"
             and status != "success"''' \
                .format(status, escape_string("{}".format(message)), action_id)
        if app_name:
            sql += ' and app_name = "{}"'.format(app_name)
        if ip:
            sql += ' and ip = "{}"'.format(ip)
        logger.info(sql)
        cursor = connection.cursor()
        logger.debug(sql)
        cursor.execute(sql)

    @staticmethod
    def get_info(iteration_id, op_type, suite_code_list, query_detail=False):
        sql = ''' 
            SELECT
                hr.action_id,
                hr.app_name,
                hr.ip,
                hr.suite_name,
                hr.status,
                hr.message,
                hr.op_time,
                concat(au.last_name,au.first_name),
                app.package_type,
                app.git_path,
                hr.begin_ver,
                hr.end_ver,
                app.sys_status
            FROM
                iter_mgt_iter_app_info app,
                task_mgt_deploy_result hr,
                auth_user au,
                (   
                    select max(a.action_id) action_id 
                    '''
        if query_detail:
            sql += ', a.app_name'

        sql += '''   from task_mgt_deploy_result a 
                    where a.op_type = '{op_type}' 
                    and a.suite_name in ({suite_code_list})
                    and a.iteration_id = '{iteration_id}'
                    '''
        if query_detail:
            sql += ' group by app_name'
        sql += ''') mhr
            WHERE
                hr.iteration_id = '{iteration_id}'
                and hr.action_id = mhr.action_id
                and app.appName = hr.app_name
                and au.username = hr.op_user'''
        if query_detail:
            sql += ' and mhr.app_name = hr.app_name'
        sql += '''
                and hr.iteration_id = app.pipeline_id
         '''
        sql = sql.format(iteration_id=iteration_id, op_type=op_type,
                         suite_code_list=','.join(["'%s'" % item for item in suite_code_list]))

        logger.debug(sql)
        cursor = connection.cursor()
        cursor.execute(sql)

        h5_deploy_for_display = H5DeployResult()
        publish_info = []
        for row in cursor.fetchall():
            app_name = row[1]
            ip = row[2]
            suite_name = row[3]
            status = row[4]
            message = row[5]
            operate_time = row[6]
            user_name = row[7]
            package_type = row[8]
            git_path = row[9]
            begin_ver = row[10]
            end_ver = row[11]
            sys_status = row[12]

            h5_deploy_for_display.status = status

            publish_info.append({"app_name": app_name,
                                 "iteration_id": iteration_id,
                                 "ip": ip,
                                 "suite_name": suite_name,
                                 "status": status,
                                 "status_display": h5_deploy_for_display.get_status_display(),
                                 "operate_time": operate_time,
                                 "username": user_name,
                                 "package_type": package_type,
                                 "git_path": git_path,
                                 "begin_ver": begin_ver,
                                 "end_ver": end_ver,
                                 "sys_status": sys_status,
                                 "message": message, })

        return publish_info

    @staticmethod
    def check_is_running(iteration_id, op_type, app_name):
        sql = ''' 
                    SELECT
                        count(1) AS counts
                    FROM
                        task_mgt_deploy_result a
                    WHERE
                        a.iteration_id = '{iteration_id}'
                    AND a.op_type = '{op_type}'
                    AND a.`status` = '{status}'
                    and a.`app_name` = '{app_name}'
                 '''.format(iteration_id=iteration_id, op_type=op_type, status=H5DeployResult.RUNNING,
                            app_name=app_name)
        logger.debug(sql)
        cursor = connection.cursor()
        cursor.execute(sql)

        is_running = False
        for row in cursor.fetchall():
            counts = row[0]
            is_running = True if counts > 0 else False
            break

        return is_running

    @staticmethod
    def get_node_ip_list(app_name, suite_name):
        # 如果前端只传了一个统一的prod，相当于region_group的概念，sql改一下 20250731 by fwm
        if suite_name == 'prod':
            sql = '''
                SELECT 
                    node.node_ip
                from env_mgt_node_bind bind
                inner join app_mgt_app_module app_m on app_m.module_name = bind.module_name
                inner join app_mgt_app_build app_b on app_b.module_name = app_m.module_name and app_b.app_id = app_m.app_id
                inner join app_mgt_app_info app_i on app_i.id = app_m.app_id
                inner join env_mgt_suite suite on suite.id = bind.suite_id
                inner join env_mgt_region r on suite.region_id = r.id
                inner join env_mgt_node node on node.id = bind.node_id
                where 1=1
                    and bind.module_name = '{module_name}'
                    and r.region_group = '{region_group}'
                    and app_m.need_ops = 1
                    and app_m.need_check = 1
                    and app_m.need_online = 1
                    and bind.enable_bind = 1
                    and node.node_status = 0
                order by 
                    node.node_ip
            '''.format(region_group=suite_name, module_name=app_name)
        else:
            sql = '''
                    SELECT 
                    node.node_ip
                    from env_mgt_node_bind bind
                    inner join app_mgt_app_module app_m on app_m.module_name = bind.module_name
                    inner join app_mgt_app_build app_b on app_b.module_name = app_m.module_name and app_b.app_id = app_m.app_id
                    inner join app_mgt_app_info app_i on app_i.id = app_m.app_id
                    inner join env_mgt_suite suite on suite.id = bind.suite_id
                    inner join env_mgt_node node on node.id = bind.node_id
                    where 1=1
                        and bind.module_name = '{module_name}'
                        and suite.suite_code = '{suite_code}'
                        and app_m.need_ops = 1
                        and app_m.need_check = 1
                        and app_m.need_online = 1
                        and bind.enable_bind = 1
                        and node.node_status = 0
                    order by 
                        node.node_ip
                  '''.format(suite_code=suite_name, module_name=app_name)

        logger.debug(sql)
        cursor = connection.cursor()
        cursor.execute(sql)

        ip_list = []
        for row in cursor.fetchall():
            ip_list.append(row[0])

        return ip_list

    def update_deploy_status(self, action_id, status, message, app_name=None, ip=None, op_type=None):
        action_item = get_publish_type_by_action_id(action_id)
        status_list, stage_list = get_publish_salt_result(action_id, ip)

        if status == 'failure':
            self.update_h5_deploy_status(action_id, 'failure', message, None)
        if action_item == "update_and_deploy_and_verify":  # 此处的action_item是update_and_deploy_and_verify以verify为结束标志更新节点状态
            if op_type == 'verify':
                if status_list and 'failure' in status_list:
                    self.update_h5_deploy_status(action_id, 'failure', message, None)
                else:
                    if status_list:
                        self.update_h5_deploy_status(action_id, 'success', message, None, ip)
        elif action_item == "update_and_deploy":  # 此处的action_item是update_and_deploy以deploy为结束标志更新节点状态
            if op_type == 'deploy':
                if status_list and 'failure' in status_list:
                    self.update_h5_deploy_status(action_id, 'failure', message, None)
                else:
                    if status_list:
                        self.update_h5_deploy_status(action_id, 'success', message, None, ip)
        else:
            self.update_h5_deploy_status(action_id, status, message, None, ip)


class EnvBindSql:
    @staticmethod
    def evn_select_latest(action_item):
        sql = ''' 
                SELECT
                    *
                FROM
                    pipeline_env_bind 
                WHERE
                    pipeline_id = '{pipeline_id}'
             '''.format(pipeline_id=action_item)

        logger.debug(sql)
        cursor = connection.cursor()
        cursor.execute(sql)
        env_info = []
        for row in cursor.fetchall():
            pipeline_id = row[1]
            app_name = row[2]
            env = row[3]
            operator = row[4]
            datetime = row[5]

            env_info.append({
                "pipeline_id": pipeline_id,
                "app_name": app_name,
                "env": env,
                "operator": operator,
                "datetime": datetime,
            })

        return env_info


class CIInfoCollector:
    # 优化（只修改格式，不修改逻辑） zt@2021-04-06
    sql = '''SELECT DISTINCT
m.br_name, am.need_online, am.need_ops,
i.package_type, i.appName, i.git_path,
r.`status`, r.message, r.suite_name, r.begin_ver, r.end_ver, r.job_name, r.action_id,
CONCAT(a.last_name, a.first_name) AS username, u.operate_time, j.build_id, l.job_url
FROM iter_mgt_iter_info m
INNER JOIN iter_mgt_iter_app_info i ON i.pipeline_id = m.pipeline_id
INNER JOIN app_mgt_app_module am ON i.appName = am.module_name
LEFT JOIN(
    SELECT iteration_id, app_name, MAX(action_id) AS mid
    FROM task_mgt_deploy_result
    WHERE op_type IN ('{action_item}','python_apply')
    GROUP BY iteration_id, app_name
)v ON v.iteration_id = m.pipeline_id AND v.app_name = i.appName
LEFT JOIN task_mgt_deploy_result r ON r.app_name = v.app_name AND r.action_id = v.mid
LEFT JOIN user_action_record u ON u.id = r.action_id
LEFT JOIN auth_user a ON a.username = u.username
LEFT JOIN task_mgt_jenkins_results j ON j.action_id = r.action_id AND j.job_name = r.job_name
LEFT JOIN hm_optlog_main l ON l.job_name = r.job_name AND l.job_build_id = j.build_id
WHERE m.pipeline_id = '{iteration_id}'
ORDER BY l.id DESC'''

    def produce_data(self, cursor):
        publish_info = []
        h5_deploy_for_display = H5DeployResult()

        time_out = JENKINS_INFO["TIME_OUT"]

        # 需要更新状态的列表
        need_update_app_infos = []
        suite_job_url = {}
        for row in cursor.fetchall():
            br_name = row[0]
            need_online = row[1]
            need_ops = row[2]

            package_type = row[3]
            app_name = row[4]
            git_path = row[5]

            status = row[6]
            message = row[7]
            suite_name = row[8]
            begin_ver = row[9]
            end_ver = row[10]
            job_name = row[11]
            action_id = row[12]

            username = row[13]
            operate_time = row[14]
            build_id = row[15]
            hm_job_url = row[16]

            if len(row) > 18:
                if row[18] and isinstance(eval(row[18]), dict):
                    if eval(row[18]).get('suite_group') and eval(row[18]).get('suite_group') not in suite_job_url \
                            and len(row) > 18:
                        suite_job_url[eval(row[18]).get('suite_group')] = hm_job_url
            if len(row) < 18:
                module_desc = ''
            else:
                module_desc = row[17]

            # job_url = jenkins_url + "/job/" + job_name + "/" + str(build_id) + "/display/redirect" if build_id else None

            logger.debug(hm_job_url)
            # logger.debug(job_url)

            h5_deploy_for_display.status = status

            # 判断是否需要对超时的运行状态的请求进行更新状态
            if operate_time and status and status.find('running') != -1 and (
                    datetime.datetime.now() - operate_time).total_seconds() > time_out:
                need_update_app_infos.append(
                    {"action_id": action_id, "build_id": build_id, "job_name": job_name, "app_name": app_name,
                     "status": status})
            # 如果应用存在就跳过 by 帅 20210803
            app_exist = 0
            for row in publish_info:
                if app_name == row["app_name"]:
                    # 重复的应用
                    app_exist = 1
                    break
            if app_exist:
                continue

            publish_info.append({"app_name": app_name,
                                 "status": status,
                                 "status_display": h5_deploy_for_display.get_status_display(),
                                 "username": username,
                                 "package_type": package_type,
                                 "operate_time": operate_time,
                                 "suite_name": suite_name,
                                 "message": message,
                                 "begin_ver": begin_ver,
                                 "end_ver": end_ver,
                                 "git_path": git_path,
                                 "job_url": hm_job_url,
                                 "br_name": br_name,
                                 "need_online": need_online,
                                 "need_ops": need_ops,
                                 "module_desc": module_desc,
                                 "suite_job_url": suite_job_url})

        if len(need_update_app_infos) > 0:
            self.update_h5_timeout_result(need_update_app_infos)
        logger.debug(publish_info)
        return publish_info

    @staticmethod
    def update_h5_timeout_result(need_update_app_infos):
        if len(need_update_app_infos) > 0:
            for row in need_update_app_infos:
                action_id = row['action_id']
                app_name = row['app_name']
                build_id = row['build_id']
                status = row['status']
                job_name = row['job_name']

                success = H5DeployResult.SUCCESS if status == H5DeployResult.RUNNING else H5DeployResult.C_SUCCESS \
                    if status == H5DeployResult.C_RUNNING else H5DeployResult.P_SUCCESS
                failure = H5DeployResult.FAILURE if status == H5DeployResult.RUNNING else H5DeployResult.C_RUNNING \
                    if status == H5DeployResult.C_FAILURE else H5DeployResult.P_FAILURE

                sql_where = 'action_id ={action_id} and app_name in ("{app_name}")'.format(action_id=action_id,
                                                                                           app_name=app_name)
                # 未找到 jenkins job
                if not build_id:
                    H5DeployResult.objects.extra(where=[sql_where]). \
                        update(status=failure, message="未触发jenkins，可能因为检测失败，超时自动更新为失败")
                else:
                    hm_obj = HmOptLogMain.objects.filter(job_name=job_name, job_build_id=build_id).first()
                    if hm_obj:
                        if hm_obj.status == HmOptLogMain.SUCCESS:
                            H5DeployResult.objects.extra(where=[sql_where]). \
                                update(status=success, message=hm_obj.result_msg)
                        if hm_obj.status == "failed":
                            H5DeployResult.objects.extra(where=[sql_where]). \
                                update(status=failure, message=hm_obj.result_msg)
                        if hm_obj.status == HmOptLogMain.ABORTED:
                            H5DeployResult.objects.extra(where=[sql_where]). \
                                update(status=H5DeployResult.ABORTED, message=hm_obj.result_msg)
                    else:
                        H5DeployResult.objects.extra(where=[sql_where]). \
                            update(status=failure, message="jenkins失败，未触发hm_scripts，超时自动更新为失败")

    def get_info(self, iteration_id, action_item):
        """
        获取环境套
        module_name: .str 应用名
        suite_code : .环境信息
        return : dict
        """
        cursor = connection.cursor()
        logger.debug(self.sql)
        if isinstance(action_item, list):
            exec_sql = self.sql.format(iteration_id=iteration_id, action_item="','".join(action_item))
        else:
            exec_sql = self.sql.format(iteration_id=iteration_id, action_item=action_item)
        logger.debug(exec_sql)
        cursor.execute(exec_sql)
        publish_info = self.produce_data(cursor)
        return publish_info


class PublishApplyInfoCollector(CIInfoCollector):
    # 优化（只修改格式，不修改逻辑） zt@2021-04-06
    # 优化多环境显示重复问题 zt@2021-04-08
    sql = '''SELECT DISTINCT
    m.br_name, am.need_online, am.need_ops,
    i.package_type, i.appName, i.git_path,
    r.`status`, r.message, r.suite_name, r.begin_ver, r.end_ver, r.job_name, r.action_id,
    CONCAT(a.last_name, a.first_name) AS username, u.operate_time, j.build_id, l.job_url, am.module_desc
    FROM iter_mgt_iter_info m
    INNER JOIN iter_mgt_iter_app_info i ON i.pipeline_id = m.pipeline_id
    INNER JOIN app_mgt_app_module am ON i.appName = am.module_name
    INNER JOIN env_mgt_suite s ON s.suite_code = '{suite_code}'
    INNER JOIN env_mgt_node_bind b ON b.module_name = am.module_name AND b.suite_id = s.id
    LEFT JOIN(
        SELECT iteration_id, app_name, MAX(action_id) AS mid
        FROM task_mgt_deploy_result
        WHERE op_type IN ('{action_item}','python_apply')
        GROUP BY iteration_id, app_name
    )v ON v.iteration_id = m.pipeline_id AND v.app_name = i.appName
    LEFT JOIN task_mgt_deploy_result r ON r.app_name = v.app_name AND r.action_id = v.mid
    LEFT JOIN user_action_record u ON u.id = r.action_id
    LEFT JOIN auth_user a ON a.username = u.username
    LEFT JOIN task_mgt_jenkins_results j ON j.action_id = r.action_id AND j.job_name = r.job_name
    LEFT JOIN hm_optlog_main l ON l.jenkins_id =j.id
    WHERE m.pipeline_id = '{iteration_id}'
    ORDER BY i.package_type, i.appName'''

    """前端传prod环境，SQL适配为产线组进行查询 20211117 by fwm"""
    sql_prod = '''SELECT DISTINCT
    m.br_name, am.need_online, am.need_ops,
    i.package_type, i.appName, i.git_path,
    r.`status`, r.message, r.suite_name, r.begin_ver, r.end_ver, r.job_name, r.action_id,
    CONCAT(a.last_name, a.first_name) AS username, u.operate_time, j.build_id, l.job_url, am.module_desc, j.request_params
    FROM iter_mgt_iter_info m
    INNER JOIN iter_mgt_iter_app_info i ON i.pipeline_id = m.pipeline_id
    INNER JOIN app_mgt_app_module am ON i.appName = am.module_name
    INNER JOIN env_mgt_region emr on emr.region_group = '{suite_code}'
    INNER JOIN env_mgt_suite s ON s.region_id = emr.id
    INNER JOIN env_mgt_node_bind b ON b.module_name = am.module_name AND b.suite_id = s.id
    LEFT JOIN(
        SELECT iteration_id, app_name, MAX(action_id) AS mid
        FROM task_mgt_deploy_result
        WHERE op_type IN ('{action_item}','python_apply')
        GROUP BY iteration_id, app_name
    )v ON v.iteration_id = m.pipeline_id AND v.app_name = i.appName
    LEFT JOIN task_mgt_deploy_result r ON r.app_name = v.app_name AND r.action_id = v.mid
    LEFT JOIN user_action_record u ON u.id = r.action_id
    LEFT JOIN auth_user a ON a.username = u.username
    LEFT JOIN task_mgt_jenkins_results j ON j.action_id = r.action_id AND j.job_name = r.job_name
    LEFT JOIN hm_optlog_main l ON l.jenkins_id =j.id
    WHERE m.pipeline_id = '{iteration_id}'
    ORDER BY i.package_type, i.appName'''

    def get_info(self, iteration_id, action_item, suite_code=''):
        """
        获取环境套
        module_name: .str 应用名
        suite_code : .环境信息
        return : dict
        """
        cursor = connection.cursor()
        logger.debug(self.sql)
        if isinstance(action_item, list):
            action_item = '","'.join(action_item)
        # 这里的逻辑关联移动端发布申请页的列表，前端处理比较复杂。暂时改不动，先简化改造。zt@2024-09-11
        if suite_code in ('prod', 'hd', 'zb'):
            exec_sql = self.sql_prod.format(iteration_id=iteration_id, action_item=action_item, suite_code=suite_code)
        else:
            exec_sql = self.sql.format(iteration_id=iteration_id, action_item=action_item, suite_code=suite_code)
        logger.debug(exec_sql)
        cursor.execute(exec_sql)
        publish_info = self.produce_data(cursor)
        return publish_info

    @staticmethod
    def is_last_compiled_apply(app_name, iteration_id, branch_version, suite_code):
        sql = '''
            SELECT
                a.lib_repo_branch,
                max(a.create_time) create_time
            FROM
                product_mgt_product_info a
            WHERE
                a.iteration_id = '{iteration_id}'
            AND a.module_name = '{app_name}'
            AND a.lib_repo_branch IN (
                '{branch_version}',
                '{branch_version}_{suite_code}'
            )
            GROUP BY
                lib_repo_branch 
        '''.format(app_name=app_name, iteration_id=iteration_id, branch_version=branch_version, suite_code=suite_code)

        logger.debug(sql)
        cursor = connection.cursor()
        cursor.execute(sql)

        last_compile_date = None
        last_apply_date = None
        for row in cursor.fetchall():
            data_branch_version = row[0]
            last_date = row[1]
            if branch_version == data_branch_version:
                last_compile_date = last_date
            else:
                last_apply_date = last_date

        return True if last_compile_date and last_apply_date and last_apply_date > last_compile_date else False

    @staticmethod
    def find_all_email(iteration_id):
        sql = '''
                SELECT
                    email.status email_status,
                    email.affirm_time,
                    concat(user.last_name,user.first_name) assertor,
                    email.iteration_id
                FROM
                    iter_mgt_publish_application_confirmation email ,
                    auth_user user
                WHERE
                    email.assertor = user.username
                    and email.iteration_id = '{iteration_id}'
                order by
                    email.create_time desc
            '''.format(iteration_id=iteration_id)

        logger.debug(sql)
        cursor = connection.cursor()
        cursor.execute(sql)
        # 查出的所有数据
        email_info = []
        # 返回的数据 --> 如果有success，返回success的数据，如果没有，返回最新的一条数据
        return_email_info = {}

        md5Email = PublishApplicationConfirmation()
        for row in cursor.fetchall():
            email_status = row[0]
            affirm_time = row[1]
            assertor = row[2]

            email_info.append({
                'email_status': email_status,
                'affirm_time': affirm_time,
                'assertor': assertor
            })

            if email_status == 'success':
                return_email_info = {
                    'email_status': email_status,
                    'affirm_time': affirm_time,
                    'assertor': assertor
                }
            break
        if return_email_info.__len__() == 0 and email_info.__len__() > 0:
            return_email_info = email_info[0]

        if return_email_info.__len__() > 0:
            md5Email.status = return_email_info['email_status']
            return_email_info['email_status_display'] = md5Email.get_status_display()

        return return_email_info

    @staticmethod
    def find_app_is_apply(iteration_id, suite_code, app_list):
        sql = '''
        SELECT
            count(1)
        FROM
            task_mgt_deploy_result a
        WHERE
            a.suite_name = '{suite_code}'
        AND a.op_type = 'publish_apply'
        AND `status` = '{status}'
        AND app_name IN ("{app_name}")
        And iteration_id = '{iteration_id}'
        '''.format(suite_code=suite_code, status=H5DeployResult.P_RUNNING, app_name='","'.join(app_list),
                   iteration_id=iteration_id)

        logger.debug(sql)
        cursor = connection.cursor()
        cursor.execute(sql)

        return cursor.fetchone()[0] > 0


class H5BranchFileCollector:

    @staticmethod
    def h5_branch_file(iteration_id):
        """
            分支归档页面查询
        """
        sql = """
                SELECT 
                    r.app_name,
                    CONCAT(s.git_url,s.git_path) AS git_path,
                    r.op_time,
                    r.op_user,
                    r.status
                FROM task_mgt_deploy_result r
                LEFT JOIN app_mgt_app_module f ON f.module_name=r.app_name 
                LEFT JOIN app_mgt_app_info s ON f.app_id = s.id 
                INNER JOIN (select r.app_name,max(r.op_time) max_time
                FROM task_mgt_deploy_result r
                GROUP BY r.app_name) temp on temp.max_time = r.op_time and temp.app_name = r.app_name
                WHERE  r.iteration_id ='{}' and op_type = 'test_publish'
            """.format(iteration_id)
        logger.debug(sql)
        cursor = connection.cursor()
        cursor.execute(sql)
        h5_branch_file_info = []
        for row in cursor.fetchall():
            app_name = row[0]
            git_path = row[1]
            op_time = row[2]
            op_user = row[3]
            status = row[4]

            h5_branch_file_info.append({
                "app_name": app_name,
                "git_path": git_path,
                "op_time": op_time,
                "op_user": op_user,
                "status": status,
            })

        return h5_branch_file_info


class Publish2EnvController:
    @staticmethod
    def get_info(iteration_id, app_name_list, env=None):
        if env:
            sql = '''SELECT DISTINCT s.suite_code,s.suite_desc FROM iter_mgt_iter_app_info i 
                                LEFT JOIN env_mgt_node_bind b ON b.module_name = i.appName 
                                LEFT JOIN env_mgt_suite s ON s.id = b.suite_id
                                LEFT JOIN env_mgt_region r ON s.region_id = r.id
                                WHERE i.pipeline_id= "{iteration_id}" AND s.suite_code = "{env}" 
                                ORDER BY s.id DESC
                            '''.format(iteration_id=iteration_id, env=env)
        else:
            sql = '''SELECT DISTINCT s.suite_code,s.suite_desc FROM iter_mgt_iter_app_info i 
                    LEFT JOIN env_mgt_node_bind b ON b.module_name = i.appName 
                    LEFT JOIN env_mgt_suite s ON s.id = b.suite_id
                    LEFT JOIN env_mgt_region r ON s.region_id = r.id
                    WHERE i.pipeline_id= "{iteration_id}" AND r.region_group IN ("prod") 
                    ORDER BY s.id DESC
                '''.format(iteration_id=iteration_id)


        logger.debug(sql)
        cursor = connection.cursor()
        cursor.execute(sql)
        result = []
        for row in cursor.fetchall():
            result.append({"suite_code": row[0], "suite_desc": row[1]})
        return result


class PipelineStopController:

    @staticmethod
    def get_info(iteration_id, status, app_name):
        sql = '''
                SELECT
                    a.job_name,
                    b.build_id,
                    u.email
                FROM
                    task_mgt_deploy_result a,
                    task_mgt_jenkins_results b,
                    auth_user u 
                WHERE
                    a.app_name = '{app_name}' 
                    AND a.iteration_id = '{iteration_id}' 
                    AND a.STATUS = '{status}' 
                    AND b.action_id = a.action_id 
                    AND a.job_name = b.job_name
                    AND u.username = a.op_user
                '''.format(app_name=app_name, iteration_id=iteration_id, status=status)

        logger.debug(sql)
        cursor = connection.cursor()
        cursor.execute(sql)
        result = {}
        for row in cursor.fetchall():
            result['job_name'] = row[0]
            result['build_id'] = row[1]
            result['email'] = row[2]
        return result


def get_test_suite_code():
    """
    获取环境套
    module_name: .str 应用名
    suite_code : .环境信息
    return : dict
    """
    cursor = connection.cursor()
    cursor.execute('''
                    select DISTINCT a.suite_code from hm_package_main_info a
                    left join env_mgt_suite e on a.suite_code = e.suite_code
                    where e.region_id = 32 and e.suite_is_active = 1
                    order by suite_code
            ''')
    result = []
    for row in cursor.fetchall():
        result.append(row[0])
    return result


def get_branch_status(pipeline_id):
    """
    获取当前分支状态
    """
    cursor = connection.cursor()
    cursor.execute('''select * from iter_mgt_iter_app_info where pipeline_id = "{}"'''.format(pipeline_id))
    return cursor.fetchall()


class SpiderPublishController:

    @staticmethod
    def get_bind_info(region_group, app_name, node_ip, open_group_publish=None):
        if node_ip:
            sql = '''
                    select nb.module_name as app_name, nb.id as bind_id, n.node_ip, s.suite_code, g.deploy_group_name, IFNULL(module.jenkins_batch_publish, 0) as batch_publish, nb.is_cold_standby_node
                    from env_mgt_node_bind nb
                    left join app_mgt_app_module module on nb.module_name = module.module_name
                    left join env_mgt_suite s on nb.suite_id = s.id
                    left join env_mgt_region r on s.region_id = r.id
                    left join env_mgt_deploy_group g on nb.deploy_group = g.id
                    left join env_mgt_node n on n.id = nb.node_id
                    where r.region_group = '{region_group}' and nb.module_name = '{app_name}' and n.node_status = '0' and n.node_ip = '{node_ip}'
                    '''.format(region_group=region_group, app_name=app_name, node_ip=node_ip)
        else:
            sql = '''
                    select nb.module_name as app_name, nb.id as bind_id, n.node_ip, s.suite_code, g.deploy_group_name,IFNULL(module.jenkins_batch_publish, 0) as batch_publish, nb.is_cold_standby_node
                    from env_mgt_node_bind nb
                    left join app_mgt_app_module module on nb.module_name = module.module_name
                    left join env_mgt_suite s on nb.suite_id = s.id
                    left join env_mgt_region r on s.region_id = r.id
                    left join env_mgt_deploy_group g on nb.deploy_group = g.id
                    left join env_mgt_node n on n.id = nb.node_id
                    where r.region_group = '{region_group}' and nb.module_name = '{app_name}'
                    AND n.node_status = '0' AND nb.enable_bind = 1 
                    '''.format(region_group=region_group, app_name=app_name)

        if open_group_publish:
            sql += " and (module.jenkins_batch_publish = 0 or module.jenkins_batch_publish is null or nb.is_cold_standby_node = 1) "

        sql += " ORDER BY s.suite_code desc, n.node_ip"

        logger.info(sql)
        cursor = connection.cursor()
        cursor.execute(sql)
        bind_info = []
        for row in cursor.fetchall():
            if row[6]:
                batch_publish = 0
            else:
                batch_publish = row[5]
            result = {'app_name': row[0], 'bind_id': row[1], 'node_ip': row[2], 'suite_code': row[3],
                      'deploy_group_name': row[4], 'batch_publish': batch_publish}
            bind_info.append(result)
        return bind_info

    @staticmethod
    def get_bind_info_region_group_list(region_group_list, app_name, node_ip):
        if node_ip:
            sql = '''
                    select nb.module_name as app_name, nb.id as bind_id, n.node_ip, s.suite_code, g.deploy_group_name from env_mgt_node_bind nb
                    left join env_mgt_suite s on nb.suite_id = s.id
                    left join env_mgt_region r on s.region_id = r.id
                    left join env_mgt_deploy_group g on nb.deploy_group = g.id
                    left join env_mgt_node n on n.id = nb.node_id
                    where r.region_group in ('{region_group_list}') and nb.module_name = '{app_name}' and n.node_status = '0' and n.node_ip = '{node_ip}'
                    ORDER BY s.suite_code desc
                    '''.format(region_group_list="','".join(region_group_list), app_name=app_name, node_ip=node_ip)
        else:
            sql = '''
                    select nb.module_name as app_name, nb.id as bind_id, n.node_ip, s.suite_code, g.deploy_group_name from env_mgt_node_bind nb
                    left join env_mgt_suite s on nb.suite_id = s.id
                    left join env_mgt_region r on s.region_id = r.id
                    left join env_mgt_deploy_group g on nb.deploy_group = g.id
                    left join env_mgt_node n on n.id = nb.node_id
                    where r.region_group in ('{region_group_list}') and nb.module_name = '{app_name}' and n.node_status = '0'
                    ORDER BY s.suite_code desc
                    '''.format(region_group_list="','".join(region_group_list), app_name=app_name)

        logger.info(sql)
        cursor = connection.cursor()
        cursor.execute(sql)
        bind_info = []
        for row in cursor.fetchall():
            result = {'app_name': row[0], 'bind_id': row[1], 'node_ip': row[2], 'suite_code': row[3],
                      'deploy_group_name': row[4]}
            bind_info.append(result)
        return bind_info

    @staticmethod
    def get_latest_publish_status_info(iteration_id, app_name, region_group, node_list=None):
        if node_list:

            sql = """
                select t.app_name, t.ip, t.suite_name, t.`status`, t.message, t.op_time, t.op_user, t.op_type, 
                t.iteration_id,t.action_id,u.action_item from task_mgt_deploy_result t 
                LEFT JOIN user_action_record u ON u.id = t.action_id
                where (t.action_id, t.ip) in ( 								
                select distinct MAX(DR.action_id), dr.ip from task_mgt_deploy_result dr
                LEFT JOIN env_mgt_suite s on dr.suite_name = s.suite_code
		        LEFT JOIN env_mgt_region r on r.id = s.region_id
                where dr.app_name = '{app_name}' and r.region_group = '{region_group}' and dr.ip in ('{ip_list}')
                 """.format(app_name=app_name, region_group=region_group, ip_list="','".join(node_list))
            if iteration_id:
                sql += """ and dr.iteration_id = '{iteration_id}'""".format(iteration_id=iteration_id)
            sql += """ GROUP BY dr.app_name, dr.ip, dr.suite_name) and t.app_name = '{app_name}';""".format(
                app_name=app_name)
        else:
            sql = """
                 select t.app_name, t.ip, t.suite_name, t.`status`, t.message, t.op_time, t.op_user, t.op_type, 
                 t.iteration_id,t.action_id,u.action_item from task_mgt_deploy_result t 
                 LEFT JOIN user_action_record u ON u.id = t.action_id
                 where (t.action_id, t.ip) in ( 								
                 select distinct MAX(DR.action_id), dr.ip from task_mgt_deploy_result dr
                 LEFT JOIN env_mgt_suite s on dr.suite_name = s.suite_code
		         LEFT JOIN env_mgt_region r on r.id = s.region_id 
                 where dr.app_name = '{app_name}' and r.region_group = '{region_group}'
                 """.format(app_name=app_name, region_group=region_group)
            if iteration_id:
                sql += """ and dr.iteration_id = '{iteration_id}'""".format(iteration_id=iteration_id)
            sql += """ GROUP BY dr.app_name, dr.ip, dr.suite_name) and t.app_name = '{app_name}';""".format(
                app_name=app_name)

        logger.info(sql)
        cursor = connection.cursor()
        cursor.execute(sql)
        status_info = []
        for row in cursor.fetchall():
            res = SpiderPublishController.get_last_publish_stage_message(iteration_id, app_name, region_group, row[9],
                                                                         row[1])
            result = {"app_name": row[0], "node_ip": row[1], "suite_code": row[2], "publish_status": row[3],
                      "publish_message": row[4],
                      "op_time": row[5], "op_user": row[6], "op_type": row[7], "iteration_id": row[8],
                      "action_item": row[10]}
            result.update(res)
            status_info.append(result)

        return status_info

    @staticmethod
    def get_last_publish_stage_message(iteration_id, app_name, region_group, action_id, ip):
        sql = '''
         SELECT t2.action_id,t2.request_result, GROUP_CONCAT(t2.deploy_stage SEPARATOR ', ') AS deploy_stages, t2.ip,t2.request_status  
            FROM (  
                SELECT request_status, request_result, deploy_stage, MAX(end_at) AS max_end_at  
                FROM task_mgt_salt_operation_results  
                WHERE (action_id, ip) IN ( 								
                SELECT MAX(DR.action_id), dr.ip FROM task_mgt_deploy_result dr
                LEFT JOIN env_mgt_suite s ON dr.suite_name = s.suite_code
                LEFT JOIN env_mgt_region r ON r.id = s.region_id 
                WHERE dr.app_name = '{}' AND r.region_group = '{}' AND dr.ip = '{}'
                AND dr.iteration_id = '{}' )  
                GROUP BY  ip,deploy_stage  
            ) AS t1  
            JOIN task_mgt_salt_operation_results AS t2 ON t1.deploy_stage = t2.deploy_stage AND t1.max_end_at = t2.end_at
            WHERE t2.request_status = 'failure'
        '''.format(app_name, region_group, ip, iteration_id)
        cursor = connection.cursor()
        cursor.execute(sql)
        result = cursor.fetchone()
        if result:
            res = {"deploy_stages": result[2], "request_status": result[4]}
        else:
            res = {"deploy_stages": '', "request_status": ''}
        return res

    @staticmethod
    def get_publish_history(app_name, iteration_id=None):

        sql = '''
                SELECT DISTINCT dr.suite_name,dr.ip,dr.status,
                CASE
                     WHEN dr.status = 'success' THEN
                             dr.message
                     WHEN dr.status = 'failure' THEN
                             IFNULL(sor.request_result, dr.message)
                     ELSE
                             ''
                     END as message,dr.op_time,dr.op_user,dr.op_type
                FROM task_mgt_deploy_result dr 
                LEFT JOIN task_mgt_salt_operation_results sor on dr.action_id = sor.action_id AND dr.`ip` = sor.`ip`
                WHERE dr.app_name = '{app_name}' AND dr.op_type IN ('jenkins_batch_publish','pro_publish')
            '''.format(app_name=app_name)
        if iteration_id:
            sql += ''' AND dr.iteration_id = "{iteration_id}" '''.format(iteration_id=iteration_id)

        sql += ''' ORDER BY dr.id DESC limit 100'''

        logger.info(sql)
        cursor = connection.cursor()
        cursor.execute(sql)
        history_info = []
        for row in cursor.fetchall():
            result = {"suite_code": row[0], "ip": row[1], "status": row[2], "message": row[3],
                      "op_time": row[4], "op_user": row[5], "op_type": row[6]
                      }
            history_info.append(result)
        return history_info

    @staticmethod
    def get_is_same_day_publish(app_name, node_ip, deploy_stage):
        now = datetime.datetime.now()
        operate_time = now.strftime("%Y-%m-%d")
        sql = '''
                SELECT count(sor.id)
                FROM task_mgt_deploy_result dr 
                INNER JOIN task_mgt_salt_operation_results sor on dr.action_id = sor.action_id AND dr.`ip` = sor.`ip`
                WHERE dr.app_name = '{app_name}' AND dr.op_type IN ('jenkins_batch_publish','pro_publish')
                and dr.ip = '{node_ip}' and sor.deploy_stage = '{deploy_stage}' and dr.op_time >= '{operate_time}' 
            '''.format(app_name=app_name, node_ip=node_ip, deploy_stage=deploy_stage, operate_time=operate_time)

        logger.info(sql)
        cursor = connection.cursor()
        cursor.execute(sql)
        return cursor.fetchone()[0] > 0


def get_suite_code_list(regine_group):
    sql = '''select suite_code from env_mgt_suite s
            LEFT JOIN env_mgt_region r on r.id = s.region_id
            where r.region_group = '{regine_group}' and s.suite_is_active = '1';
        '''.format(regine_group=regine_group)
    logger.debug(sql)
    cursor = connection.cursor()
    cursor.execute(sql)
    suite_code_list = []
    for row in cursor.fetchall():
        suite_code_list.append(row[0])
    return suite_code_list


def get_latest_h5_hd_branch(platform_code, suite_code, end_ver):
    sql = '''
            SELECT distinct m.br_name FROM hm_package_main_info m LEFT JOIN hm_package_download_info d 
            ON m.id=d.mid WHERE m.platform_code="{platform_code}" and m.suite_code = "{suite_code}" 
            AND m.end_ver= "{end_ver}" AND d.file_name = "{end_ver}.zip" 
            ORDER BY m.datetime DESC;
        '''.format(platform_code=platform_code, suite_code=suite_code, end_ver=end_ver)

    logger.debug(sql)
    cursor = connection.cursor()
    cursor.execute(sql)
    for row in cursor.fetchall():
        return row[0]


def get_prod_download_android_url(app_name):
    android_last_prod_apk_list = []
    sql = '''
    SELECT d.download_url,m.br_name,d.file_name FROM hm_package_main_info m 
    LEFT join hm_package_download_info d on m.id = d.mid
    WHERE m.suite_code = 'test' and m.app_name = '{}' ORDER BY m.datetime Desc
    '''.format(app_name)

    cursor = connection.cursor()
    cursor.execute(sql)
    # print(cursor.fetchall())
    for row in cursor.fetchall():
        if row[2].endswith('.apk'):
            android_last_prod_apk_list.append({'apk': row[2], 'apk_url': row[0]})
    return android_last_prod_apk_list


# def get_suite_info(app_name, env, iteration_id):
#     suite_info = []
#     sql = '''SELECT DISTINCT t.suite_code,t.suite_group,f.module_name,h.lib_repo,j.br_name,i.transit_ip
#                   FROM env_mgt_node_bind f
#                   LEFT JOIN env_mgt_suite t ON f.suite_id = t.id
#                   LEFT JOIN env_mgt_region m ON t.region_id=m.id
#                   LEFT JOIN app_mgt_app_module h ON h.module_name = f.module_name
#                   LEFT JOIN iter_mgt_iter_app_info z ON z.appName =  h.module_name
#                   left join iter_mgt_iter_info j on j.pipeline_id = z.pipeline_id
#                   LEFT JOIN `env_mgt_transit_info` i ON i.suite_code = t.suite_code
#                   WHERE f.enable_bind = '1' and f.module_name = "{}"
#                            AND m.region_group="{}" AND z.pipeline_id = "{}" ''' \
#         .format(app_name, env, iteration_id)
#     cursor = connection.cursor()
#     cursor.execute(sql)
#     # print(cursor.fetchall())
#     result = cursor.fetchall()
#
#     if result:
#         for row in result:
#             suite_info.append({"suite_code_conf": row[0], "suite_group": row[1]})
#     return suite_info


def get_h5_dist_last_arc_info(app_name):
    sql = '''SELECT
            m.pipeline_id AS last_arc_iteration_id,
            i.appName ,
            v.max_br_end_date
        FROM
            iter_mgt_iter_info m
            LEFT JOIN iter_mgt_iter_app_info i ON i.pipeline_id = m.pipeline_id
            LEFT JOIN (
            SELECT
                i.appName,
                MAX( m.br_end_date ) AS max_br_end_date 
            FROM
                iter_mgt_iter_info m
                INNER JOIN iter_mgt_iter_app_info i ON i.pipeline_id = m.pipeline_id 
            WHERE
                m.br_status = 'close' 
            GROUP BY
                i.appName 
            ) v ON v.appName = i.appName 
            AND v.max_br_end_date = m.br_end_date 
        WHERE
            m.br_end_date IS NOT NULL 
            AND m.br_status = 'close' 
            AND v.max_br_end_date IS NOT NULL 
            AND i.appName = '{}';'''.format(app_name)
    cursor = connection.cursor()
    cursor.execute(sql)
    result = cursor.fetchall()
    if result:
        return result[0][0]
    return result


def get_h5_dist_app():
    dist_app_list = []
    dist_platform_app_list = {}
    sql = '''select m.module_name,
       p.platform_code,
       b.package_type,
       i.app_name, i.git_url, i.git_path,
       ti.team_alias,
       bind.module_bind_order
from h5_dist_platform p
inner join h5_dist_bind bind on bind.platform_code = p.platform_code
inner join app_mgt_app_module m on m.module_name = bind.module_name
inner join app_mgt_app_build b on b.module_name = m.module_name and b.app_id = m.app_id
inner join app_mgt_app_info i on i.id = m.app_id
left join team_mgt_app_bind t on t.app_id = i.id
left join tool_mgt_team_info ti on ti.id = t.team_id
where 1=1 and bind.bind_is_active = 1
order by bind.platform_bind_order, bind.module_bind_order'''
    cursor = connection.cursor()
    cursor.execute(sql)
    for item in cursor.fetchall():
        if not dist_platform_app_list.get(item[1]) and not isinstance(dist_platform_app_list.get(item[1]), list):
            dist_platform_app_list[item[1]] = [item[0]]
        else:
            if item[0] not in dist_platform_app_list[item[1]]:
                dist_platform_app_list[item[1]].append(item[0])
        dist_app_list.append({'app_name': item[0], 'platform_code': item[1]})
    return dist_app_list, dist_platform_app_list


def get_h5_dist_iter_app_info(pipeline_id):
    dist_platform_app_list = {}
    sql = '''
    SELECT ai.pipeline_id,ai.appName,db.platform_code,i.br_name FROM `iter_mgt_iter_app_info` ai 
     LEFT JOIN `iter_mgt_iter_info` i ON ai.pipeline_id = i.pipeline_id
     LEFT JOIN `h5_dist_bind` db ON db.module_name = ai.appName
     LEFT JOIN `h5_dist_platform` dp ON dp.platform_code = db.platform_code
     WHERE ai.pipeline_id = '{}'
    '''.format(pipeline_id)
    cursor = connection.cursor()
    cursor.execute(sql)
    for item in cursor.fetchall():
        dist_platform_app_list[item[1]] = {'pipeline_id': item[0], 'platform_code': item[2], 'br_name': item[3]}
        # dist_platform_app_list.append({'pipeline_id': item[0],'app_name': item[1], 'platform_code': item[2], 'br_name': item[3]})
    return dist_platform_app_list


def get_iter_platform_app_info(iteration_id):
    iter_platform_app_list = {}
    sql = '''SELECT ai.appName,bind.platform_code,bind.module_bind_order, FROM `iter_mgt_iter_app_info` ai 
        LEFT JOIN `h5_dist_bind` bind ON ai.appName = bind.module_name
        LEFT JOIN `h5_dist_platform` p ON p.platform_code = bind.platform_code
        WHERE pipeline_id = '{}' and bind.bind_is_active = 1;'''.format(iteration_id)
    cursor = connection.cursor()
    logger.debug(sql)
    cursor.execute(sql)
    fund_platform = []
    piggy_platform = []
    for item in cursor.fetchall():
        if item[1] == 'fund-platform':
            fund_platform.append(item[0])
        elif item[1] == 'piggy-platform':
            piggy_platform.append(item[0])
    iter_platform_app_list['fund-platform'] = fund_platform
    iter_platform_app_list['piggy-platform'] = piggy_platform
    return iter_platform_app_list


def get_h5_dist_app_platform():
    platform_list = []
    sql = '''
    SELECT platform_code FROM `h5_dist_platform` WHERE platform_is_active = 1;
    '''
    cursor = connection.cursor()
    logger.debug(sql)
    cursor.execute(sql)
    for item in cursor.fetchall():
        platform_list.append(item[0])
    return platform_list


def get_iter_platform_suite(region_name_list, iteration_id):
    """
    获取可用域下 迭代下平台存在的环境套数量
    :param region_name_list:
    :param iteration_id:
    :return:
    """
    fund_platform_suite = []
    piggy_platform_suite = []
    sql = '''
        SELECT ap.appName,su.suite_code,db.platform_code,db.module_bind_order FROM iter_mgt_iter_app_info ap
    LEFT JOIN env_mgt_node_bind b ON ap.appName = b.module_name
    LEFT JOIN h5_dist_bind db ON db.module_name = ap.appName
    LEFT JOIN `h5_dist_platform` p ON p.platform_code = db.platform_code
    LEFT JOIN env_mgt_suite su ON su.id = b.suite_id 
    LEFT JOIN env_mgt_region  re ON re.id =su.region_id  
    WHERE  ap.pipeline_id = "{}" and re.region_name in ("{}") and db.bind_is_active = 1;
        '''.format(iteration_id, '","'.join(region_name_list))
    cursor = connection.cursor()
    logger.debug(sql)
    cursor.execute(sql)
    for item in cursor.fetchall():
        if item[2] == 'fund-h5-res' and item[1] not in fund_platform_suite:
            fund_platform_suite.append(item[1])
        elif item[2] == 'piggy-h5-res' and item[1] not in piggy_platform_suite:
            piggy_platform_suite.append(item[1])
    return fund_platform_suite, piggy_platform_suite


def get_batch_deploy_last_status(iteration_id, app_name, op_type):
    sql = '''
            SELECT res.action_id, res.app_name, res.ip, res.suite_name, res.status, res.iteration_id 
            FROM task_mgt_deploy_result res
             WHERE res.action_id = 
             (SELECT MAX(id) AS action_id FROM user_action_record r 
             WHERE r.action_value LIKE "%'{}'%"
             AND r.action_value LIKE "%'{}'%" AND r.action_value LIKE "%'{}'%")'''.format(iteration_id, app_name,
                                                                                          op_type)
    cursor = connection.cursor()
    cursor.execute(sql)
    result = cursor.fetchall()

    status_list = []
    if result:
        for row in result:
            status_list.append(row[4])
    return status_list


def get_last_verify_result(app_name, node_ip, suite_code):
    sql = '''
            SELECT b.request_result,b.request_status,b.request_status_code, b.end_at
            FROM task_mgt_deploy_result a 
            LEFT JOIN task_mgt_salt_operation_results b on a.action_id = b.action_id AND b.ip = a.ip
            WHERE a.app_name = '{}' 
            AND b.ip = '{}' 
            AND a.suite_name = '{}'
            AND b.deploy_stage = 'verify'
            ORDER BY b.action_id DESC LIMIT 1
        '''.format(app_name, node_ip, suite_code)
    cursor = connection.cursor()
    cursor.execute(sql)
    result = cursor.fetchone()

    app_verify_result = None
    if result:
        app_verify_result = {'request_result': result[0], 'request_status': result[1], 'request_status_code': result[2], 'end_at': result[3]}
    return app_verify_result


def get_br_name(pipeline_id):
    sql = """select br_name
             from iter_mgt_iter_info
             where pipeline_id = '%s' """ % pipeline_id
    cursor = connection.cursor()
    cursor.execute(sql)
    result = cursor.fetchone()
    return result[0]


def get_publish_type_by_action_id(action_id):
    sql = '''SELECT DISTINCT r.action_item, r.action_value FROM
     user_action_record r WHERE r.id = "{}";'''.format(action_id)
    cursor = connection.cursor()
    cursor.execute(sql)
    result = cursor.fetchone()
    action_item = result[0]
    return action_item


def get_publish_salt_result(action_id, ip):
    status_list = []
    stage_list = []
    sql = '''SELECT t2.request_status, t2.request_result, t2.deploy_stage  
        FROM (  
            SELECT request_status, request_result, deploy_stage, MAX(end_at) AS max_end_at  
            FROM task_mgt_salt_operation_results  
            WHERE action_id = "{}" AND ip = "{}"  
            GROUP BY  action_id,ip,deploy_stage  
        ) AS t1  
        JOIN task_mgt_salt_operation_results AS t2 ON
         t1.deploy_stage = t2.deploy_stage AND
          t1.max_end_at = t2.end_at;'''.format(action_id, ip)
    cursor = connection.cursor()
    cursor.execute(sql)
    result = cursor.fetchall()
    for item in result:
        if item[0] not in status_list:
            status_list.append(item[0])
        if item[2] not in stage_list:
            stage_list.append(item[2])
    return status_list, stage_list


def get_jenkins_batch_publish_status(action_info):
    publish_status_info = []
    sql = '''
        SELECT d.status,j.build_id,d.op_user,d.ip,d.message,j.job_name FROM `task_mgt_deploy_result` d 
        LEFT JOIN `task_mgt_jenkins_publish_pipeline_info` j ON j.action_id = d.action_id 
        WHERE d.action_id IN ('{}') 
    '''.format('","'.join(action_info))
    cursor = connection.cursor()
    logger.debug(sql)
    cursor.execute(sql)
    for it in cursor.fetchall():
        publish_status_info.append(
            {"status": it[0], "build_id": it[1], "op_user": it[2], "ip": it[3], "message": it[4], "job_name":it[5]})
    return publish_status_info
