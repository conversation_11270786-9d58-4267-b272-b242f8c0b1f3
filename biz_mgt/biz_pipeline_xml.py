class BizPipelineXml:
    biz_pipeline_config = '''<?xml version='1.1' encoding='UTF-8'?>
    <flow-definition plugin="workflow-job@1207.ve6191ff089f8">
      <actions>
        <org.jenkinsci.plugins.pipeline.modeldefinition.actions.DeclarativeJobAction plugin="pipeline-model-definition@2.2118.v31fd5b_9944b_5"/>
        <org.jenkinsci.plugins.pipeline.modeldefinition.actions.DeclarativeJobPropertyTrackerAction plugin="pipeline-model-definition@2.2118.v31fd5b_9944b_5">
          <jobProperties/>
          <triggers/>
          <parameters/>
          <options/>
        </org.jenkinsci.plugins.pipeline.modeldefinition.actions.DeclarativeJobPropertyTrackerAction>
      </actions>
      <description></description>
      <keepDependencies>false</keepDependencies>
      <properties>
        <com.sonyericsson.rebuild.RebuildSettings plugin="rebuild@1.34">
          <autoRebuild>false</autoRebuild>
          <rebuildDisabled>false</rebuildDisabled>
        </com.sonyericsson.rebuild.RebuildSettings>
        <hudson.model.ParametersDefinitionProperty>
          <parameterDefinitions>
            <hudson.model.StringParameterDefinition>
              <name>suite_code</name>
              <trim>false</trim>
            </hudson.model.StringParameterDefinition>
            <hudson.model.StringParameterDefinition>
              <name>biz_test_iter_id</name>
              <trim>false</trim>
            </hudson.model.StringParameterDefinition>
            <hudson.model.StringParameterDefinition>
              <name>label</name>
              <defaultValue>vm</defaultValue>
              <trim>false</trim>
            </hudson.model.StringParameterDefinition>
          </parameterDefinitions>
        </hudson.model.ParametersDefinitionProperty>
      </properties>
      <definition class="org.jenkinsci.plugins.workflow.cps.CpsFlowDefinition" plugin="workflow-cps@2803.v1a_f77ffcc773">
        <script>pipeline {{
       agent {{label label}}
       environment{{
    	    BEP=&quot;/home/<USER>/be-scripts/be-scripts&quot;
    	    job_url=&quot;${{currentBuild.absoluteUrl}}&quot;
    	    build_id=&quot;${{currentBuild.id}}&quot;
        }}
        stages {{
            stage(&apos;0: 解析数据&apos;) {{
    				steps {{
    				    wrap([$class: &apos;BuildUser&apos;]) {{
        					sh label: &apos;&apos;, script: &apos;python3.x ${{BEP}}/job/jenkins/biz_auto_test/self_define_parse.py ${{JOB_NAME}} ${{WORKSPACE}} &apos;+job_url+&apos; &apos;+build_id+&apos; &apos;+suite_code+&apos; &apos;+biz_test_iter_id+&apos; &apos;+env.BUILD_USER_ID
    				    }}
    				}}
    			}}
    		{stage_template}
        }}
        post{{
            failure{{
                echo &apos;failure&apos;
                sh label: &apos;&apos;, script: &apos;python3.x ${{BEP}}/job/jenkins/biz_auto_test/record_pipeline_status.py ${{JOB_NAME}} &quot;failure&quot; &apos;+build_id+&apos; &apos;+suite_code
            }}
            aborted{{
    			echo &apos;aborted&apos;
                sh label: &apos;&apos;, script: &apos;python3.x ${{BEP}}/job/jenkins/biz_auto_test/record_pipeline_status.py ${{JOB_NAME}} &quot;aborted&quot; &apos;+build_id+&apos; &apos;+suite_code
            }}
            success{{
    			echo &apos;success&apos;
                sh label: &apos;&apos;, script: &apos;python3.x ${{BEP}}/job/jenkins/biz_auto_test/record_pipeline_status.py ${{JOB_NAME}} &quot;success&quot; &apos;+build_id+&apos; &apos;+suite_code
            }}
        }}
    }}</script>
        <sandbox>true</sandbox>
      </definition>
      <triggers/>
      <disabled>false</disabled>
    </flow-definition>
        '''

    stage_template = '''
        stage(&apos;{step}: {action_name}&apos;) {{
    				steps {{
    					sh label: &apos;&apos;, script: &apos;python3.x ${{BEP}}/job/jenkins/biz_auto_test/self_define_pipeline.py &quot;{action}&quot; ${{JOB_NAME}} ${{WORKSPACE}} &quot;{step}&quot;&apos;+&apos; &apos;+suite_code+&apos; &apos;+biz_test_iter_id+&apos; &apos;+build_id
    				}}
    			}}
        '''