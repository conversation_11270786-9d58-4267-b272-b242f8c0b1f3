import datetime
import json
import os.path
from enum import Enum
from time import sleep

from django.db import transaction

from biz_mgt.biz_mgt_dao import get_dump_jenkins_url
from biz_mgt.biz_pipeline_ser import get_pipeline_step_info, get_flow_info, get_flow_detail_info, \
    get_testset_order_in_biz_pipeline
from biz_mgt.biz_pipeline_xml import BizPipelineXml
from biz_mgt.models import BizTestFlowInfo, BizTestFlowConfig, BizTestFlowAction, BizTestFlowActionParam, BizBaseInfo, \
    JenkinsMgtBizJobInfo, JenkinsMgtBizJobInfoLog, BizTestFlowExecHistory, BizAppBind, JenkinsMgtBizJobRunTestset, \
    BizTestFlowTestsetDetail
from db_mgt.models import JenkinsMgtTestDataDevJob
from jenkins_mgt.jenkins_job_mgt import JenkinsJobMgt
from jenkins_mgt.jenkins_view import generate_unique_batch_no
from jenkins_mgt.models import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, JenkinsMgtJobCompositionDetail
from spider.settings import logger, JEN<PERSON>INS_INFO
from rest_framework import viewsets
from rest_framework.response import Response

from spider.settings import ApiResult
from task_mgt.http_task import HttpTask
from task_mgt.models import ServiceResults
from test_env_mgt.models import TestSuiteInitLog


class BizPipelineDefineApi(viewsets.ViewSet):

    def list(self, request):
        page = request.GET.get("page")
        size = request.GET.get("size")
        biz_pipeline_name = request.GET.get("biz_pipeline_name")
        creator = request.GET.get("creator")
        biz_code = request.GET.get("biz_code")

        flow_info = get_flow_info(page, size, biz_pipeline_name, biz_code, creator)
        flow_count_all = BizTestFlowInfo.objects.all()
        if biz_pipeline_name:
            flow_count_all = flow_count_all.filter(biz_pipeline_name__icontains=biz_pipeline_name)
        if biz_code:
            flow_count_all = flow_count_all.filter(biz_code=biz_code)
        if creator:
            flow_count_all = flow_count_all.filter(create_user=creator)
        flow_count = flow_count_all.count()
        return Response(data=ApiResult.success_dict(
            msg="查询编排信息成功",
            data={"results": flow_info, "count": flow_count}))

    def get_biz_app_list_dict(self, biz_code):
        biz_app = BizAppBind.objects.filter(biz_code=biz_code, biz_app_bind_is_active=1).values("app_module_name")
        app_list = []
        for app_info in biz_app:
            app_list.append(app_info.get("app_module_name"))
        app_dict = {"app_list": app_list}
        return app_dict

    def create(self, request):

        data = request.data
        logger.info(data)
        biz_code = data.get("biz_code")
        flow_name = data.get("flow_name").strip()
        flow_content = data.get("flow_content")
        # 限制20个
        if len(flow_content.items()) > 20:
            return Response(data=ApiResult.failed_dict(data='', msg="编排线不可超过20个"))
        biz_infos = BizBaseInfo.objects.filter(biz_is_active=1, biz_code=biz_code).first()
        # 兼容从希娜同步业务的新逻辑 for怀天  20241223 by fwm
        biz_info_name = None
        if biz_infos:
            biz_name = biz_infos.biz_name
            scenario_name = biz_infos.biz_scenario_name
            type_department = biz_infos.biz_type.biz_type_department
            type_transaction = biz_infos.biz_type.biz_type_transaction
            if scenario_name:
                biz_info_name = "{}{}-{}-{}".format(type_department,
                                                    type_transaction,
                                                    biz_name,
                                                    scenario_name)
            else:
                biz_info_name = biz_name
        biz_pipeline_name = biz_info_name + '_' + flow_name
        opt_user = str(request.user)
        curr_time = datetime.datetime.now()
        try:
            with transaction.atomic():
                old_flow_id = None
                obj = BizTestFlowInfo.objects.filter(biz_code=biz_code, biz_flow_name=flow_name).first()
                if obj:
                    # 如果存在先删除
                    old_flow_id = obj.id
                    self._delete_flow_info(old_flow_id)

                obj = BizTestFlowInfo.objects.create(biz_code=biz_code, biz_flow_name=flow_name,
                                                     biz_pipeline_name=biz_pipeline_name,
                                                     create_time=curr_time, create_user=opt_user)
                new_flow_id = obj.id

                for flow_config_order, flow_config_content in flow_content.items():
                    obj = BizTestFlowConfig.objects.create(flow_id=new_flow_id,
                                                           config_name=str(new_flow_id) + '-' + str(flow_config_order),
                                                           order_num=flow_config_order, create_time=curr_time,
                                                           create_user=opt_user)
                    flow_config_id = obj.id

                    for flow_action_order, flow_action_content in flow_config_content.items():
                        obj = BizTestFlowAction.objects.create(flow_config_id=flow_config_id,
                                                               action_name=flow_action_content.get("action_name"),
                                                               action=flow_action_content.get("action"),
                                                               order_num=flow_action_order,
                                                               create_time=curr_time, create_user=opt_user)
                        action_id = obj.id

                        if flow_action_content.get("param"):
                            param_type = 'datetime' if flow_action_content.get("action") == 'set_time' else 'string'
                            BizTestFlowActionParam.objects.create(action_id=action_id,
                                                                  param=flow_action_content.get("param"),
                                                                  param_type=param_type, create_time=curr_time,
                                                                  create_user=opt_user)
                if old_flow_id:
                    BizTestFlowTestsetDetail.objects.filter(flow_id=old_flow_id).update(flow_id=new_flow_id)

                result, jenkins_url = self._create_biz_pipeline(biz_code, flow_name, opt_user)

                if not result:
                    return Response(data=ApiResult.failed_dict(data='', msg="jenkins详情： {}".format(jenkins_url)))
                else:
                    return Response(
                        data=ApiResult.success_dict(data='',
                                                    msg="业务编排保存成功!jenkins详情： {}".format(jenkins_url)))
        except Exception as e:
            logger.error(str(e))
            return Response(data=ApiResult.failed_dict(msg=str(e)))

    def _create_biz_pipeline(self, biz_code, flow_name, opt_user):
        curr_time = datetime.datetime.now()
        pipeline_name = BizTestFlowInfo.objects.filter(biz_code=biz_code, biz_flow_name=flow_name). \
            values("biz_pipeline_name").first().get("biz_pipeline_name")

        step_list = get_pipeline_step_info(biz_code, flow_name)
        bpx = BizPipelineXml()
        stage_template = bpx.stage_template
        biz_pipeline_config = bpx.biz_pipeline_config
        stage_info = ''
        for step_info in step_list:
            stage_info = stage_info + stage_template.format(step=step_info.get("step"),
                                                            action_name=step_info.get("action_name"),
                                                            action=step_info.get("action"))
        pipeline_config = biz_pipeline_config.format(stage_template=stage_info)

        logger.info("new_pipeline_xml={}".format(pipeline_config))
        jenkins_job_mgt = JenkinsJobMgt()
        if JenkinsMgtBizJobInfo.objects.filter(biz_pipeline_name=pipeline_name):
            result, jenkins_url = self._update_jenkins_pipeline(jenkins_job_mgt, pipeline_name, pipeline_config,
                                                                curr_time, opt_user)
        else:
            result, jenkins_url = self._create_jenkins_pipeline(jenkins_job_mgt, pipeline_name, pipeline_config,
                                                                curr_time, opt_user)
        return result, jenkins_url

    def _update_jenkins_pipeline(self, jenkins_job_mgt, pipeline_name, pipeline_config, curr_time, opt_user):
        jenkins_server = jenkins_job_mgt.get_jenkins_server_by_biz_job_name(pipeline_name)
        count = 3
        while count > 0:
            try:
                jenkins_server.server.reconfig_job(pipeline_name, pipeline_config)
                break
            except Exception as e:
                msg = str(e)
                logger.warn(msg)
                count -= 1
        if count == 0:
            raise Exception("3次重试都失败了！！！")
        else:
            JenkinsMgtBizJobInfo.objects.update_or_create(biz_pipeline_name=pipeline_name,
                                                          defaults={"update_time": curr_time, "update_user": opt_user})
            jenkins_url = os.path.join(jenkins_server.jenkins_url, 'job', pipeline_name)
            return True, jenkins_url

    def _create_jenkins_pipeline(self, jenkins_job_mgt, pipeline_name, pipeline_config, curr_time, opt_user):
        count = 3
        while count > 0:
            try:
                jenkins_server = jenkins_job_mgt.build_job_auto_retry_other_jenkins(pipeline_name, pipeline_config)
                break
            except Exception as e:
                msg = str(e)
                logger.warn(msg)
                count -= 1

        if count == 0:
            raise Exception("3次重试都失败了！！！")
        else:
            logger.info("jenkins_info_id={}".format(jenkins_server.jenkins_info_id))
            logger.info("jenkins_url={}".format(jenkins_server.jenkins_url))
            JenkinsMgtBizJobInfo.objects.create(biz_pipeline_name=pipeline_name,
                                                jenkins_info_id=jenkins_server.jenkins_info_id,
                                                create_user=opt_user, create_time=curr_time)
            jenkins_url = os.path.join(jenkins_server.jenkins_url, 'job', pipeline_name)
            return True, jenkins_url

    def _delete_flow_info(self, flow_id):
        config_id_list = [item["id"] for item in BizTestFlowConfig.objects.filter(flow_id=flow_id).values("id")]
        action_id_list = [item["id"] for item in
                          BizTestFlowAction.objects.filter(flow_config_id__in=config_id_list).values("id")]
        action_param_id_list = [item["id"] for item in
                                BizTestFlowActionParam.objects.filter(action_id__in=action_id_list).values("id")]
        logger.info("flow_id={}".format(flow_id))
        logger.info("config_id_list={}".format(config_id_list))
        logger.info("action_id_list={}".format(action_id_list))
        logger.info("action_param_id_list={}".format(action_param_id_list))
        try:
            with transaction.atomic():
                BizTestFlowActionParam.objects.filter(id__in=action_param_id_list).delete()
                BizTestFlowAction.objects.filter(id__in=action_id_list).delete()
                BizTestFlowConfig.objects.filter(id__in=config_id_list).delete()
                BizTestFlowInfo.objects.filter(id=flow_id).delete()
        except Exception as e:
            msg = str(e)
            logger.warn(msg)
            raise Exception("删除数据错误，原因：{}".format(msg))


class BizPipelineInfoApi(viewsets.ViewSet):
    def list(self, request):
        biz_pipeline_name = request.GET.get("biz_pipeline_name")
        flow_detail_info = get_flow_detail_info(biz_pipeline_name)
        modalData = []
        modalDataDict = {}
        order_num = 0
        for flow_detail in flow_detail_info:
            if order_num != flow_detail.get("order_num"):
                order_num = flow_detail.get("order_num")
                if modalDataDict:
                    modalData.append(modalDataDict)
                    modalDataDict = {}
            if flow_detail.get("action") == "clear_cache":
                modalDataDict.update({"clearCache": True})
            if flow_detail.get("action") == "is_checked":
                modalDataDict.update({"isChecked": True})
            if flow_detail.get("action") == "restart_app":
                app_list = json.loads(flow_detail.get("param")).get(
                    "app_list") if flow_detail.get("param") else []
                modalDataDict.update({"restartApp": True,
                                      "appLists": app_list if app_list else []})
            if flow_detail.get("action") == "set_time":
                modalDataDict.update({"changeTime": True,
                                      "time": json.loads(flow_detail.get("param")).get("sys_datetime")})
            if flow_detail.get("action") == "run_testset":
                modalDataDict.update({"runTestSet": True,
                                      "testSetId": json.loads(flow_detail.get("param")).get("test_set")})
            if flow_detail.get("action") == "delete_file":
                modalDataDict.update({"deleteFile": True,
                                      "filePath": json.loads(flow_detail.get("param")).get("file_path")})
            if flow_detail.get("action") == "wait_check":
                modalDataDict.update({"wait_check": True,
                                      "wait_minutes": json.loads(flow_detail.get("param")).get("wait_minutes")})
            if flow_detail.get("action") == "make_dump":
                modalDataDict.update({"makeDump": True,
                                      "dumpPrefix": json.loads(flow_detail.get("param")).get("dump_file_prefix_name")})

        if modalDataDict:
            modalData.append(modalDataDict)

        return Response(data=ApiResult.success_dict(data=modalData))


class TestSetRunStatus(Enum):
    SUCCESS = ('success', '成功')
    FAILURE = ('failure', '失败')
    RUNNING = ('running', '运行中')

    def __init__(self, status_name, status_name_desc):
        self.status_name = status_name
        self.status_name_desc = status_name_desc


class BizPipelineBuild(viewsets.ViewSet):

    def retrieve(self, request, pk=None):
        param = request.query_params
        obj = JenkinsMgtBizJobInfoLog.objects.filter(id=param["id"]).get()
        obj = obj.__dict__
        obj.pop('_state')
        return Response(data=ApiResult.success_dict(data=obj))

    def create(self, request):
        """
        创建业务流水线执行任务
        
        参数:
            request: HTTP请求对象，包含任务执行所需的参数
            
        返回:
            Response: 包含执行结果的响应对象
        """
        # 1. 提取请求参数
        params = self._extract_request_params(request)
        suite_code = params['suite_code']
        biz_pipeline_name = params['biz_pipeline_name']
        batch_no = params['batch_no']
        version_type = params['version_type']
        testset_detail_list = params['testset_detail_list']
        opt_user = params['opt_user']
        
        # 2. 环境检查 - 确保当前环境没有正在执行的自动化任务
        env_available, job_build_id = self._check_environment_available(suite_code, batch_no)
        if not env_available:
            return Response(
                data=ApiResult.failed_dict(
                    msg="当前环境有正在执行自动化任务: jenkins_composition的job_build_id: {}，请稍后再试！".format(job_build_id)))
        
        # 3. 处理定时任务类型 - 定时任务类型的自动化必须使用REMOTE版本
        version_type = self._handle_schedule_task(batch_no, version_type)
        
        # 4. 更新测试集详情
        BizTestFlowTestsetDetail.update_testset_detail(biz_pipeline_name, testset_detail_list, opt_user, version_type)
        testset_detail_list_map = self._get_testset_detail_list_map(testset_detail_list)
        
        # 5. 准备Jenkins任务执行
        jenkins_info = self._prepare_jenkins_job(params)
        jenkins_server = jenkins_info['jenkins_server']
        latest_build_info = jenkins_info['latest_build_info']
        builds = jenkins_info['builds']
        
        try:
            # 6. 执行数据库事务 - 触发Jenkins任务并记录相关信息
            with transaction.atomic():
                # 触发Jenkins任务
                queue_item_int = jenkins_server.server.build_job(biz_pipeline_name, jenkins_info['job_http_dict'])
                
                # 计算构建ID
                cur_job_build_id = latest_build_info.get("number") + 1 if builds else 1
                pre_build_id = latest_build_info.get("number") if builds else 0
                
                # 创建测试集运行记录
                self._create_testset_run_records(biz_pipeline_name, batch_no, testset_detail_list_map, 
                                                version_type, opt_user)
                
                # 创建任务日志记录
                bizJobInfoLog = self._create_job_info_log(biz_pipeline_name, cur_job_build_id, 
                                                        jenkins_info['job_http_dict'], queue_item_int,
                                                        pre_build_id, batch_no, opt_user)
            
            # 7. 返回成功响应
            return Response(
                data=ApiResult.success_dict(
                    data={"id": bizJobInfoLog.id, "batch_no": batch_no},
                    msg="【{}】业务执行流水线执行成功！".format(biz_pipeline_name)))
                    
        except Exception as e:
            # 记录错误并返回失败响应
            logger.error("业务流水线执行异常: {}".format(str(e)))
            return Response(
                data=ApiResult.failed_dict(
                    data='',
                    msg="【{}】业务执行流水线执行异常！详情： {}".format(biz_pipeline_name, str(e))))

    def _extract_request_params(self, request):
        """
        从请求中提取所需参数
        
        参数:
            request: HTTP请求对象
            
        返回:
            dict: 包含所有提取的参数
        """
        params = {
            'suite_code': request.data.get("suite_code"),
            'biz_test_iter_id': request.data.get("biz_test_iter_id"),
            'biz_pipeline_name': request.data.get("biz_pipeline_name"),
            'batch_no': request.data.get("batch_no"),
            'version_type': request.data.get("version_type"),
            'testset_detail_list': request.data.get("testset_detail_list"),
            'opt_user': str(request.user)
        }
        
        # 如果没有提供批次号，则生成一个唯一批次号
        if not params['batch_no']:
            params['batch_no'] = generate_unique_batch_no()
            
        return params
    
    def _check_environment_available(self, suite_code, batch_no):
        """
        检查当前环境是否有正在执行的自动化任务
        
        参数:
            suite_code: 测试套件代码
            batch_no: 批次号
            
        返回:
            bool: 环境是否可用
        """
        obj = JenkinsMgtJobComposition.objects.filter(suite_code=suite_code).exclude(batch_no=batch_no).order_by(
            "-id").first()
        if obj and obj.status not in ("success", "failure"):
            logger.warning("当前环境有正在执行自动化任务: jenkins_composition的job_build_id: {}".format(obj.job_build_id))
            return False, obj.job_build_id
        return True, None
    
    def _handle_schedule_task(self, batch_no, version_type):
        """
        处理定时任务类型的自动化任务
        
        参数:
            batch_no: 批次号
            version_type: 版本类型
            
        返回:
            str: 处理后的版本类型
        """
        # 定时任务类型的自动化，必须跑remote 20250520 by fwm
        obj = JenkinsMgtJobComposition.objects.filter(batch_no=batch_no).first()
        if obj and obj.job_type == "flow_schedule":
            return 'REMOTE'
        return version_type
    
    def _prepare_jenkins_job(self, params):
        """
        准备Jenkins任务执行所需的信息
        
        参数:
            params: 请求参数字典
            
        返回:
            dict: 包含Jenkins任务执行所需的信息
        """
        biz_pipeline_name = params['biz_pipeline_name']
        suite_code = params['suite_code']
        biz_test_iter_id = params['biz_test_iter_id']
        
        # 初始化Jenkins管理器并获取服务器
        jenkins_job_mgt = JenkinsJobMgt()
        jenkins_server = jenkins_job_mgt.get_jenkins_server_by_biz_job_name(biz_pipeline_name)
        
        # 准备HTTP参数
        job_http_dict = {"suite_code": suite_code, "biz_test_iter_id": biz_test_iter_id}
        
        # 获取任务信息和构建历史
        job_info = jenkins_server.server.get_job_info(biz_pipeline_name)
        builds = job_info['builds']
        latest_build_info = {}
        
        # 获取最新构建信息
        if builds:
            latest_build_info = jenkins_server.server.get_build_info(biz_pipeline_name, builds[0]['number'])
            logger.info("latest_build_number={}".format(latest_build_info['number']))
            logger.info("latest_build_status={}".format(latest_build_info['result']))
        
        return {
            'jenkins_server': jenkins_server,
            'job_http_dict': job_http_dict,
            'builds': builds,
            'latest_build_info': latest_build_info
        }
    
    def _create_testset_run_records(self, biz_pipeline_name, batch_no, testset_detail_list_map, version_type, opt_user):
        """
        创建测试集运行记录
        
        参数:
            biz_pipeline_name: 业务流水线名称
            batch_no: 批次号
            testset_detail_list_map: 测试集详情列表映射
            version_type: 版本类型
            opt_user: 操作用户
        """
        testset_order_list = get_testset_order_in_biz_pipeline(biz_pipeline_name)
        ins_list = []
        curr_time = datetime.datetime.now()
        
        for testset_order in testset_order_list:
            for k, v in testset_order.items():
                obj = JenkinsMgtBizJobRunTestset()
                obj.batch_no = batch_no
                obj.order_no = int(k)
                obj.testset_id = int(v)
                obj.execute_id = 'be-script_' + generate_unique_batch_no()
                obj.testset_detail = testset_detail_list_map.get(v)
                obj.testset_run_status = TestSetRunStatus.RUNNING.status_name
                obj.testset_version_type = version_type
                obj.create_user = opt_user
                obj.create_time = curr_time
                
                ins_list.append(obj)
        
        JenkinsMgtBizJobRunTestset.objects.bulk_create(ins_list)
    
    def _create_job_info_log(self, biz_pipeline_name, cur_job_build_id, job_http_dict, queue_item_int, 
                            pre_build_id, batch_no, opt_user):
        """
        创建任务信息日志
        
        参数:
            biz_pipeline_name: 业务流水线名称
            cur_job_build_id: 当前任务构建ID
            job_http_dict: 任务HTTP参数
            queue_item_int: 队列项ID
            pre_build_id: 前一个构建ID
            batch_no: 批次号
            opt_user: 操作用户
            
        返回:
            JenkinsMgtBizJobInfoLog: 创建的任务信息日志对象
        """
        return JenkinsMgtBizJobInfoLog.objects.create(
            job_name=biz_pipeline_name,
            job_build_id=cur_job_build_id,
            job_param=job_http_dict,
            job_queue_item=queue_item_int,
            pre_build_id=pre_build_id,
            batch_no=batch_no,
            create_user=opt_user,
            create_time=datetime.datetime.now()
        )
        
    def _get_testset_detail_list_map(self, testset_detail_list):
        """
        获取测试集详情列表映射
        
        参数:
            testset_detail_list: 测试集详情列表
            
        返回:
            dict: 测试集详情列表映射
        """
        testset_detail_list_map = {}
        for testset_detail in testset_detail_list:
            testset_id = testset_detail.get('testSetId')
            app_name = testset_detail.get('appName')
            script_branch = testset_detail.get('script_branch')
            if testset_id not in testset_detail_list_map:
                testset_detail_list_map[testset_id] = {app_name: script_branch}
            else:
                testset_detail_list_map[testset_id].update({app_name: script_branch})
        return testset_detail_list_map


class BizTestFlowExecHistoryView(viewsets.ViewSet):

    def retrieve(self, request, pk=None):
        try:
            param = request.query_params
            url = ""
            if param['exec_action_type'] == "archive_sql":
                objs = ServiceResults.objects.filter(business_name="test_sql_archive",
                                                     script_params__icontains=param['biz_test_iter_id']).order_by(
                    '-id')
                if objs:
                    return Response(data=ApiResult.success_dict(data=objs[0].detail))
                else:
                    return Response(data=ApiResult.failed_dict("没有查询到执行记录"))
            if param['exec_action_type'] == "diff_sql":
                job_log = JenkinsMgtTestDataDevJob.objects.filter(biz_iter_id=param['biz_test_iter_id'],
                                                                  job_type="diff_sql").order_by(
                    '-id')[0]
                return Response(data=ApiResult.success_dict(data=job_log.job_url))
            if param['exec_action_type'] == "test_data_init":
                job_url = get_dump_jenkins_url(param['biz_test_iter_id'], "test_data_init")
                return Response(data=ApiResult.success_dict(data=job_url))
            if param['exec_action_type'] == "biz_job":
                biz_job_obj = JenkinsMgtBizJobInfoLog.objects.filter(
                    id=json.loads(param['exec_detail_param'])["id"]).get()
                if biz_job_obj and not biz_job_obj.job_url:
                    return Response(data=ApiResult.failed_dict("job正在初始化，请稍后！"))
                return Response(data=ApiResult.success_dict(
                    data=[{"job_name": biz_job_obj.job_name, "job_url": biz_job_obj.job_url,
                           "job_status": biz_job_obj.status}]))
            if param['exec_action_type'] in ["init", "seg_init"]:
                biz_job_obj = TestSuiteInitLog.objects.filter(
                    id=json.loads(param['exec_detail_param'])["jenkins_url"]).get()
                if biz_job_obj and not biz_job_obj.job_build_id:
                    return Response(data=ApiResult.failed_dict("job正在初始化，请稍后！"))
                return Response(data=ApiResult.success_dict(
                    data=[{"job_name": biz_job_obj.job_http_job_name,
                           "job_url": "{}/blue/organizations/jenkins/test_suite_init/detail/test_suite_init/{}/pipeline/".format(
                               JENKINS_INFO.get('URL'),
                               biz_job_obj.job_build_id),
                           "job_status": "success"}]))
            if param['exec_action_type'] in ["flow_schedule", "init&biz_job_jenkins_composition",
                                             "seg_init&biz_job_jenkins_composition"]:
                job_composition = JenkinsMgtJobComposition.objects.filter(
                    batch_no=json.loads(param['exec_detail_param'])['batch_no'],
                    job_type=param['exec_action_type']).get()
                if not job_composition.job_build_id:
                    return Response(data=ApiResult.failed_dict("job正在初始化，请稍后！"))
                job_composition_details = JenkinsMgtJobCompositionDetail.objects.filter(
                    batch_no=json.loads(param['exec_detail_param'])['batch_no']).all()
                job_composition_details_results = []
                for job_composition_detail in job_composition_details:
                    job_composition_details_results.append(
                        {"job_name": job_composition_detail.job_name.split("_job_type_")[1],
                         "job_url": job_composition_detail.job_url, "job_status": job_composition_detail.status})
            return Response(data=ApiResult.success_dict(data=job_composition_details_results))
        except Exception as ex:
            logger.error(ex)
            return Response(data=ApiResult.failed_dict("没有查询到你的执行记录"))

    def create(self, request):
        exec_action_type = request.data.get("exec_action_type")
        exec_detail_param = request.data.get("exec_detail_param")
        biz_type = request.data.get("biz_type")
        exec_suite_code = request.data.get("exec_suite_code")
        opt_user = str(request.user)
        BizTestFlowExecHistory.objects.create(exec_suite_code=exec_suite_code, exec_action_type=exec_action_type,
                                              exec_detail_param=exec_detail_param,
                                              biz_type=biz_type,
                                              create_user=opt_user,
                                              create_time=datetime.datetime.now())
        return Response(data=ApiResult.success_dict(data="", msg="记录成功"))

    def list(self, request):
        param = request.query_params
        try:
            objs = BizTestFlowExecHistory.objects.filter(biz_type=param['biz_type']).order_by('-id')[0:20]
            results = []
            if objs:
                for his in objs:
                    his = his.__dict__
                    his.pop('_state')
                    results.append(his)

            return Response(data=ApiResult.success_dict(data=results))
        except Exception as ex:
            logger.error(ex)
            return Response(data=ApiResult.failed_dict("当前编排的记录"))


class BizPipelineDeleteApi(viewsets.ViewSet):
    """
    业务流水线删除API
    """
    
    def create(self, request):
        """
        删除业务流水线
        """
        data = request.data
        biz_code = data.get("biz_code")
        biz_flow_name = data.get("biz_flow_name")

        if not biz_code or not biz_flow_name:
            return Response(data=ApiResult.failed_dict(msg="biz_code和biz_flow_name参数不能为空"))

        try:
            # 查找业务流水线信息
            flow_info = BizTestFlowInfo.objects.filter(biz_code=biz_code, biz_flow_name=biz_flow_name).first()
            if not flow_info:
                return Response(data=ApiResult.failed_dict(msg="未找到对应的业务流水线"))

            flow_id = flow_info.id
            biz_pipeline_name = flow_info.biz_pipeline_name

            # 在事务中执行删除操作
            with transaction.atomic():
                # 1. 首先删除jenkins_mgt_biz_job_info表的记录
                JenkinsMgtBizJobInfo.objects.filter(biz_pipeline_name=biz_pipeline_name).delete()
                logger.info("删除jenkins_mgt_biz_job_info记录: biz_pipeline_name={}".format(biz_pipeline_name))

                # 2. 按照倒序删除业务流水线相关表
                self._delete_flow_info(flow_id)

            return Response(data=ApiResult.success_dict(msg="业务流水线删除成功"))

        except Exception as e:
            logger.error("删除业务流水线失败: {}".format(str(e)))
            return Response(data=ApiResult.failed_dict(msg="删除业务流水线失败: {}".format(str(e))))
    
    def _delete_flow_info(self, flow_id):
        """
        删除流水线相关信息
        """
        config_id_list = [item["id"] for item in BizTestFlowConfig.objects.filter(flow_id=flow_id).values("id")]
        action_id_list = [item["id"] for item in
                          BizTestFlowAction.objects.filter(flow_config_id__in=config_id_list).values("id")]
        action_param_id_list = [item["id"] for item in
                                BizTestFlowActionParam.objects.filter(action_id__in=action_id_list).values("id")]
        logger.info("flow_id={}".format(flow_id))
        logger.info("config_id_list={}".format(config_id_list))
        logger.info("action_id_list={}".format(action_id_list))
        logger.info("action_param_id_list={}".format(action_param_id_list))
        
        # 按照倒序删除：先删除子表，再删除父表
        BizTestFlowActionParam.objects.filter(id__in=action_param_id_list).delete()
        BizTestFlowAction.objects.filter(id__in=action_id_list).delete()
        BizTestFlowConfig.objects.filter(id__in=config_id_list).delete()
        BizTestFlowInfo.objects.filter(id=flow_id).delete()
        
        logger.info("业务流水线相关表删除完成")


class GetTestSetListApi(viewsets.ViewSet):
    interface_name = "test_set"

    def list(self, request):

        with HttpTask() as http_task:
            r_status, result = http_task.call_interface(self.interface_name, params={})
            if r_status == "success":
                if result.get("code") == 200:
                    return Response(data=ApiResult.success_dict(data=result.get("data")))
            return Response(data=ApiResult.failed_dict(data="测试集列表获取失败！"))
