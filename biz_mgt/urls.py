#
from django.urls import path, include

from biz_mgt import biz_mgt_view, biz_pipeline_view, biz_test_data_view
from rest_framework.routers import DefaultRouter

router = DefaultRouter()

router.register(r'get_biz_name_list', biz_mgt_view.BizBaseInfoApi, basename="get_biz_name_list")
router.register(r'upd_biz_name_list', biz_mgt_view.BizBaseInfoView, basename="upd_biz_name_list")
router.register(r'get_test_iter_list', biz_mgt_view.BizTestIterApi, basename="get_test_iter_list")
router.register(r'get_test_flow_list', biz_mgt_view.BizTestFlowInfoApi, basename="get_test_flow_list")
router.register(r'get_biz_flow_list', biz_mgt_view.BizFlowInfoApi, basename="get_biz_flow_list")
router.register(r'get_test_flow_name_list', biz_mgt_view.BizTestFlowInfoByBizIterApi,
                basename="get_test_flow_name_list")
router.register(r'get_test_suite_code', biz_mgt_view.BizTestSuiteCodeByBizIterApi,
                basename="get_test_suite_code")
router.register(r'get_biz_app_list', biz_mgt_view.BizAppBindApi, basename="get_biz_app_list")
router.register(r'flow_schedule_config', biz_mgt_view.BizTestFlowScheduleConfigApi, basename="flow_schedule_config")
router.register(r'get_auto_exec_record', biz_mgt_view.BizAutoExecRecordApi, basename="get_auto_exec_record")

router.register(r'save_biz_pipeline', biz_pipeline_view.BizPipelineDefineApi, basename="save_biz_pipeline")
router.register(r'get_biz_pipeline', biz_pipeline_view.BizPipelineDefineApi, basename="get_biz_pipeline")
router.register(r'execute_biz_pipeline', biz_pipeline_view.BizPipelineBuild, basename="execute_biz_pipeline")
router.register(r'execute_history', biz_pipeline_view.BizTestFlowExecHistoryView, basename="execute_history")

router.register(r'get_biz_pipeline_detail', biz_pipeline_view.BizPipelineInfoApi, basename="get_biz_pipeline_detail")
router.register(r'get_test_set_list', biz_pipeline_view.GetTestSetListApi, basename="get_test_set_list")
router.register(r'delete_biz_pipeline_flow', biz_pipeline_view.BizPipelineDeleteApi, basename="delete_biz_pipeline_flow")

router.register(r'get_test_data_dev_status', biz_test_data_view.BizTestDataPauseView, basename="get_test_data_dev_status")
router.register(r'update_test_data_dev_status', biz_test_data_view.BizTestDataPauseView, basename="update_test_data_dev_status")

router.register(r'get_biz_relative_schedule', biz_mgt_view.BizRelativeScheduleAPI, basename="get_biz_relative_schedule")

# router.register(r'get_base_db_bind_list', biz_mgt_view.BizBaseDbBindExtendApi, basename="get_base_db_bind_list")
# router.register(r'get_biz_test_iter_list', biz_mgt_view.BizTestIterByBizCodeApi, basename="get_biz_test_iter_list")

urlpatterns = [
    path("", include(router.urls))
]
