
from rest_framework.response import Response
from rest_framework import viewsets, serializers

from biz_mgt.models import BizTestIter
from spider.settings import ApiResult


class BizTestDataPauseView(viewsets.ModelViewSet):
    serializer_class = serializers.Serializer
    def create(self, request):
        """
        暂停测试数据
        :param request:
        :return:
        """
        biz_test_iter_id = request.data.get("biz_test_iter_id")
        is_pause = request.data.get("is_pause")
        if biz_test_iter_id is None:
            return Response(ApiResult.failed_dict(msg="biz_code is None"))
        BizTestIter.objects.filter(biz_test_iter_id=biz_test_iter_id).update(pause_test_data_recording=is_pause)
        return Response(ApiResult.success_dict(msg="更新成功"))

    def list(self, request):
        """
        获取测试数据暂停状态
        :param request:
        :return:
        """
        biz_test_iter_id = request.query_params.get("biz_test_iter_id")
        if biz_test_iter_id is None:
            return Response(ApiResult.failed_dict(msg="biz_code is None"))
        biz_test_iter = BizTestIter.objects.filter(biz_test_iter_id=biz_test_iter_id).first()
        if biz_test_iter is None:
            return Response(ApiResult.failed_dict(msg="biz_code is not exist"))
        return Response(ApiResult.success_dict(msg=biz_test_iter.pause_test_data_recording))