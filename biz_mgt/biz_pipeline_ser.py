import json
import logging

from django.db import connection


def get_pipeline_step_info(biz_code, biz_flow_name):
    sql = '''
            SELECT CONCAT(c.order_num, '-', a.order_num) AS step, a.action_name, a.action, p.param 
            FROM biz_test_flow_action a
            LEFT JOIN biz_test_flow_action_param p ON p.action_id = a.id
            INNER JOIN biz_test_flow_config c ON a.flow_config_id = c.id
            INNER JOIN biz_test_flow_info f ON c.flow_id = f.id
            WHERE f.biz_code = '{}' AND f.biz_flow_name = '{}'
            ORDER BY c.order_num, a.order_num
          '''.format(biz_code, biz_flow_name)

    cursor = connection.cursor()
    cursor.execute(sql)
    step_list = []
    for row in cursor.fetchall():
        step_dict = {"step": row[0], "action_name": row[1], "action": row[2]}
        step_list.append(step_dict)
    return step_list


def get_flow_info(page, size, biz_pipeline_name=None, biz_code=None, creator=None):
    sql = '''
            SELECT t.biz_pipeline_name, t.create_time, t.create_user, t.biz_code, t.biz_flow_name, CONCAT(i.jenkins_url, 'job/', t.biz_pipeline_name, '/') AS jenkins_url
            FROM biz_test_flow_info t
            INNER JOIN jenkins_mgt_biz_job_info ji ON t.biz_pipeline_name = ji.biz_pipeline_name
            INNER JOIN jenkins_mgt_jenkins_info i ON ji.jenkins_info_id = i.id
            where 1=1
          '''
    if biz_pipeline_name:
        sql += " and t.biz_pipeline_name like '%{}%'".format(biz_pipeline_name)
    if biz_code:
        sql +=" and t.biz_code = '{}' ".format(biz_code)
    if creator:
        sql += " and t.create_user = '{}' ".format(creator)

    sql_limit = ''' ORDER BY t.create_time DESC
                    LIMIT {}, {}  
                  '''.format((int(page) - 1) * int(size), int(size))
    sql += sql_limit
    logging.info(sql)
    cursor = connection.cursor()
    cursor.execute(sql)

    data_list = [dict(zip([col[0] for col in cursor.description], row)) for row in cursor.fetchall()]
    return data_list


def get_flow_detail_info(biz_pipeline_name):
    sql = '''
            SELECT fc.order_num, fa.action, fap.param FROM biz_test_flow_info fi
            INNER JOIN biz_test_flow_config fc ON fi.id = fc.flow_id
            INNER JOIN biz_test_flow_action fa ON fc.id = fa.flow_config_id
            LEFT JOIN biz_test_flow_action_param fap ON fa.id = fap.action_id
            WHERE fi.biz_pipeline_name = '{}'
            ORDER BY fc.order_num;
          '''.format(biz_pipeline_name)

    cursor = connection.cursor()
    cursor.execute(sql)

    data_list = [dict(zip([col[0] for col in cursor.description], row)) for row in cursor.fetchall()]
    return data_list


# def get_biz_base_db_info_by_biz_iter(biz_test_iter_br):
#     sql = '''
#             SELECT db.biz_base_db_code, bi.biz_category, bi.biz_parent_id
#             FROM biz_base_info bi
#             INNER JOIN biz_test_iter ti ON bi.biz_code = ti.biz_code
#             INNER JOIN biz_base_db_bind db ON bi.biz_code = db.biz_code
#             WHERE ti.biz_test_iter_br = '{}';
#           '''.format((biz_test_iter_br))
#
#     cursor = connection.cursor()
#     cursor.execute(sql)
#
#     data_list = [dict(zip([col[0] for col in cursor.description], row)) for row in cursor.fetchall()]
#     return data_list


# def get_biz_base_db_info_by_id(biz_id, base_db_code_list):
#     sql = '''
#             SELECT db.biz_base_db_code, bi.biz_category, bi.biz_parent_id  FROM biz_base_info bi
#             INNER JOIN biz_base_db_bind db ON bi.biz_code = db.biz_code
#             WHERE bi.id = '{}';
#           '''.format(biz_id)
#
#     cursor = connection.cursor()
#     cursor.execute(sql)
#
#     base_db_info = cursor.fetchone()
#     base_db_code_list.append(base_db_info[0])
#
#     if base_db_info[1] == 2:
#         get_biz_base_db_info_by_id(base_db_info[2], base_db_code_list)
#     return base_db_code_list


def get_testset_order_in_biz_pipeline(biz_pipeline_name):
    sql = '''
            SELECT ap.param, c.order_num AS cn FROM biz_test_flow_action_param ap 
            INNER JOIN biz_test_flow_action a ON ap.action_id = a.id
            INNER JOIN biz_test_flow_config c ON a.flow_config_id = c.id
            INNER JOIN biz_test_flow_info f ON c.flow_id = f.id
            WHERE f.biz_pipeline_name = '{}' AND a.action = 'run_testset'
            ORDER BY c.order_num ASC;
          '''.format(biz_pipeline_name)

    cursor = connection.cursor()
    cursor.execute(sql)
    order = 1
    testset_order_dict_list = []
    for row in cursor.fetchall():
        if row[0]:
            testset = json.loads(row[0]).get("test_set")
        if isinstance(testset, list):
            for testset_id in testset:
                testset_order_dict = {order: testset_id}
                testset_order_dict_list.append(testset_order_dict)
                order += 1
        else:
            testset_order_dict = {order: testset}
            testset_order_dict_list.append(testset_order_dict)
            order += 1
    return testset_order_dict_list


def get_testset_detail_info(biz_pipeline_name):
    sql = '''
            SELECT DISTINCT CONCAT(d.testset_id, '_', d.app_name) AS testset_app, 
            CONCAT(d.script_branch,'|', IFNULL(d.version_type,'REMOTE')) AS testset_version
            FROM biz_test_flow_testset_detail d
            INNER JOIN biz_test_flow_info t ON d.flow_id = t.id
            WHERE t.biz_pipeline_name = '{}';
          '''.format(biz_pipeline_name)
    cursor = connection.cursor()
    cursor.execute(sql)
    data_dict = {}
    for row in cursor.fetchall():
        data_dict[row[0]] = row[1]
    return data_dict
