import datetime
import json
import requests
import time

from django.db import transaction
from rest_framework import status
from rest_framework import viewsets, serializers
from rest_framework.decorators import action
from rest_framework.response import Response

from external_interaction.mantis_mgt.mantis_adapter_view import MantisRequestAdapterView
from jenkins_mgt.jenkins_job_auto_mgt.jenkins_job_manager import JenkinsJobManager
from jenkins_mgt.jenkins_job_auto_mgt.pipeline_project_builder.properties.parameter_definition import \
    ParameterDefinition
from jenkins_mgt.jenkins_job_mgt import JenkinsJobMgt
from spider.settings import logger, INTERFACE_URL, NewApiResult
from biz_mgt.biz_mgt_dao import get_flow_and_biz_base_info, get_apps_by_biz_code, get_flow_name_by_biz_test_iter_id, \
    get_suite_code_by_biz_test_iter_id, get_auto_exec_record
from biz_mgt.biz_mgt_dao import get_all_biz_for_upd
from biz_mgt.models import BizBaseInfo, BizTestIter, BizAppBind, BizTestFlowScheduleConfig, \
    BizTestFlowGlobalParam, BizTestIterApp, BizTestFlowTestsetDetail, \
    BizTestFlowInfo
# from biz_mgt.models import CheckMgtCheckSwitch
from biz_mgt.models import BizBaseType
from spider.settings import ApiResult
from spider.settings import MOCK_SYSTEM_INTERFACE
from task_mgt.http_task import HttpTask


class BizAutoExecRecordApi(viewsets.ViewSet):
    """
    自动执行记录查询API
    """
    def list(self, request):
        # 获取分页参数
        page_num = request.query_params.get('page_num', 1)
        page_size = request.query_params.get('page_size', 10)
        
        # 获取查询参数
        biz_name = request.query_params.get('biz_name')
        biz_test_iter_br = request.query_params.get('biz_test_iter_br')
        suite_code = request.query_params.get('suite_code')
        start_date = request.query_params.get('start_date')
        end_date = request.query_params.get('end_date')
        opt_user = request.query_params.get('opt_user')
        batch_no = request.query_params.get('batch_no')
        # status_0 = request.query_params.get('status_0')
        # status_1 = request.query_params.get('status_1')
        
        # 构建查询参数字典
        query_params = {}
        if biz_name:
            query_params['biz_name'] = biz_name
        if biz_test_iter_br:
            query_params['biz_test_iter_br'] = biz_test_iter_br
        if suite_code:
            query_params['suite_code'] = suite_code
        if opt_user:
            query_params['opt_user'] = opt_user
        if batch_no:
            query_params['batch_no'] = batch_no
        if start_date:
            query_params['start_date'] = start_date
        if end_date:
            query_params['end_date'] = end_date
        # if status_0:
        #     query_params['status_0'] = status_0
        # if status_1:
        #     query_params['status_1'] = status_1
        
        # 调用DAO层方法获取数据
        result_list, total_count, batch_no_list = get_auto_exec_record(page_num=page_num, page_size=page_size, **query_params)

        params = {"run_batch_number_list": batch_no_list}

        with HttpTask() as http_task:
            host_url = INTERFACE_URL["mantis"]
            business_name = "get_test_flow_report_lib"
            request_url = host_url + "/mantis/test_report/get_test_flow_report_lib/"
            status, result = http_task.send_request("get", request_url, params, business_name, None)
            result = json.loads(result)
            final_result = result.get("data")

        for record in result_list:
            for record_item in final_result:
                if record["batch_no"] == record_item["run_batch_number"]:
                    record["test_flow_lib_url"] = record_item["report_url"]
                    record["avg_case_pass_rate"] = record_item["avg_case_pass_rate"]
        # 构建返回结果
        result = {
            "total": total_count,
            "records": result_list
        }
        
        return Response(data=NewApiResult.success_dict(data=result, message="查询自动执行记录成功"))


class BizBaseInfoApi(viewsets.ViewSet):

    def list(self, request):
        biz_infos = BizBaseInfo.objects.filter(biz_is_active=1).all()
        biz_list = []
        for biz_info in biz_infos:
            biz_info_name = None
            if biz_info:
                biz_name = biz_info.biz_name
                scenario_name = biz_info.biz_scenario_name
                type_department = biz_info.biz_type.biz_type_department
                type_transaction = biz_info.biz_type.biz_type_transaction
                if scenario_name:
                    biz_info_name = "{}{}-{}-{}".format(type_department,
                                                        type_transaction,
                                                        biz_name,
                                                        scenario_name)
                else:
                    biz_info_name = biz_name
            # biz_info_name = biz_info.biz_type.biz_type_department + biz_info.biz_type.biz_type_transaction + '-' + biz_info.biz_name + '-' + biz_info.biz_scenario_name
            biz_list.append({"biz_name": biz_info_name, "biz_code": biz_info.biz_code})

        return Response(data=ApiResult.success_dict(data=biz_list, msg="业务名称查询成功"))


class BizBaseInfoView(viewsets.ViewSet):
    authentication_classes = []

    opt_user = "howbuyscm"

    def create(self, request):
        logger.info(request.data)
        curr_time = datetime.datetime.now()
        ret_vo = {}

        spider_url = '{}qa-info/business/business_combination/getAllCombination/'.format(
            MOCK_SYSTEM_INTERFACE["qa_info_url"])
        spider_params = {}
        spider_headers = {'Content-Type': 'application/json'}
        ret_data = BizBaseInfoView.send_request(spider_url, 'get', data=spider_params, headers=spider_headers)
        if ret_data:
            logger.info(">>>> ret_data = {}".format(ret_data))
            db_cursor = get_all_biz_for_upd()
            db_dict_list = BizBaseInfoView.dict_fetchall(db_cursor)
            logger.info(">>>> db_dict_list = {}".format(db_dict_list))
            ins_list, upd_list, del_list = BizBaseInfoView.compare_biz_list(ret_data, db_dict_list)
            logger.info(">>>> ins_list = {}".format(ins_list))
            logger.info(">>>> upd_list = {}".format(upd_list))
            logger.info(">>>> del_list = {}".format(del_list))
            ins_count = self.ins_biz_list(ins_list, curr_time=curr_time)
            logger.info(">>>> ins_count = {}".format(ins_count))
            ret_vo["ins_count"] = ins_count
            upd_count = self.upd_biz_list(upd_list, curr_time=curr_time)
            logger.info(">>>> upd_count = {}".format(upd_count))
            ret_vo["upd_count"] = upd_count
            del_count = self.del_biz_list(del_list, curr_time=curr_time)
            logger.info(">>>> del_count = {}".format(del_count))
            ret_vo["del_count"] = del_count

        return Response(status=status.HTTP_200_OK,
                        data=ApiResult.success_dict(data=ret_vo, msg="从希娜同步业务数据成功"))

    @staticmethod
    def send_request(url, method="GET", data=None, headers=None, params=None, retry=3):
        try:
            logger.info("send_request-请求地址：{}".format(url))
            if method.upper() == "GET":
                response = requests.get(url, params=params, headers=headers)
            elif method.upper() == "POST":
                if not headers:
                    headers = {'Content-Type': 'application/json'}
                response = requests.post(url, data=json.dumps(data), headers=headers)
            else:
                raise ValueError("Invalid request method: {}".format(method))
            # 检查返回状态码
            if response.status_code == 200:
                # 返回响应内容
                text = response.text
                json_text = json.loads(text)
                if json_text.get('status') == 'success' or json_text.get('code') == 200:
                    return json_text.get("data")
            else:
                logger.error('send_request error response: {}'.format(response.text))
                if retry > 0:
                    time.sleep(10)
                    retry = retry - 1
                    BizBaseInfoView.send_request(url, method, data, headers, params, retry)
        except Exception as e:
            logger.error("{}:请求发生异常：", url, e)
            raise Exception('请求发生异常：{}'.format(e) + "请求地址：" + url)

    @staticmethod
    def dict_fetchall(cursor):
        return [dict(zip([col[0] for col in cursor.description], row)) for row in cursor.fetchall()]

    @staticmethod
    def compare_biz_list(scene_biz_list, db_biz_list):
        ins_list = []
        upd_list = []
        del_list = []
        if scene_biz_list:
            scene_biz_dict = {}
            for scene_biz_obj in scene_biz_list:
                scene_biz_obj_code = scene_biz_obj.get("combinationCode")
                if scene_biz_obj_code:
                    scene_biz_dict[scene_biz_obj_code] = scene_biz_obj

            if scene_biz_dict:
                if db_biz_list:
                    for db_biz_obj in db_biz_list:
                        biz_id = db_biz_obj.get("biz_id")
                        biz_code = db_biz_obj.get("biz_code")
                        biz_name = db_biz_obj.get("biz_name")
                        biz_is_active = db_biz_obj.get("biz_is_active")

                        # 希娜返回的数据字典，挨个处理。
                        scene_comp_obj = scene_biz_dict.pop(biz_code, None)
                        if scene_comp_obj:
                            is_upd = False
                            if biz_name != scene_comp_obj.get("combinationName"):
                                is_upd = True
                            if not is_upd and not biz_is_active:
                                is_upd = True

                            if is_upd:
                                upd_biz_code = biz_code
                                upd_biz_name = scene_comp_obj.get("combinationName")
                                upd_biz_is_active = biz_is_active
                                upd_biz_id = biz_id
                                upd_biz_obj = {
                                    "biz_code": upd_biz_code,
                                    "biz_name": upd_biz_name,
                                    "biz_is_active": upd_biz_is_active,
                                    "biz_id": upd_biz_id,
                                }
                                upd_list.append(upd_biz_obj)
                        else:
                            del_biz_code = biz_code
                            del_biz_name = biz_name
                            del_biz_is_active = biz_is_active
                            del_biz_id = biz_id
                            del_biz_obj = {
                                "biz_code": del_biz_code,
                                "biz_name": del_biz_name,
                                "biz_is_active": del_biz_is_active,
                                "biz_id": del_biz_id,
                            }

                            del_list.append(del_biz_obj)

                # 循环完dict还有，需要新增：
                if scene_biz_dict:
                    for scene_add_obj in scene_biz_dict.values():
                        scene_add_biz_code = scene_add_obj.get("combinationCode")
                        scene_add_biz_name = scene_add_obj.get("combinationName")
                        if not scene_add_biz_code or not scene_add_biz_name:
                            warn_msg = ">>>> 希娜系统返回的数据不完整：combinationCode = {}, combinationName = {}。此条跳过处理"
                            logger.warning(warn_msg.format(scene_add_biz_code, scene_add_biz_name))
                        else:
                            add_biz_code = scene_add_biz_code
                            add_biz_name = scene_add_biz_name

                            add_biz_obj = {
                                "biz_code": add_biz_code,
                                "biz_name": add_biz_name,
                            }

                            ins_list.append(add_biz_obj)
            else:
                raise Exception(">>>> 希娜接口异常，没有返回业务数据。同步中止！")

        return ins_list, upd_list, del_list

    def ins_biz_list(self, ins_dict_list, curr_time=None, opt_user=None):
        ins_count = 0
        if ins_dict_list:
            if not curr_time:
                curr_time = datetime.datetime.now()
            if not opt_user:
                opt_user = self.opt_user

            ins_obj_list = []
            for ins_dict in ins_dict_list:
                if ins_dict:
                    biz_code = ins_dict.get("biz_code")
                    biz_name = ins_dict.get("biz_name")
                    biz_is_active = True
                    biz_desc = "通过希娜系统同步新增。"
                    create_user = opt_user
                    create_time = curr_time
                    update_user = opt_user
                    update_time = curr_time
                    stamp = 0
                    biz_base_type = 123

                    biz_base_type = BizBaseType.objects.filter(id=biz_base_type).first()
                    biz_type = biz_base_type

                    ins_obj = BizBaseInfo(
                        biz_code=biz_code,
                        biz_name=biz_name,
                        biz_scenario_name=None,
                        biz_category=None,
                        biz_parent_id=None,
                        biz_is_active=biz_is_active,
                        biz_desc=biz_desc,
                        create_user=create_user,
                        create_time=create_time,
                        update_user=update_user,
                        update_time=update_time,
                        stamp=stamp,
                        biz_type=biz_type,
                    )
                    ins_obj_list.append(ins_obj)
            if ins_obj_list:
                try:
                    with transaction.atomic():
                        BizBaseInfo.objects.bulk_create(ins_obj_list)
                        ins_count = len(ins_obj_list)
                except Exception as e:
                    logger.error(e)
        return ins_count

    def upd_biz_list(self, upd_dict_list, curr_time=None, opt_user=None):
        upd_count = 0
        if upd_dict_list:
            if not curr_time:
                curr_time = datetime.datetime.now()
            if not opt_user:
                opt_user = self.opt_user

            upd_obj_list = []
            for upd_dict in upd_dict_list:
                if upd_dict:
                    biz_code = upd_dict.get("biz_code")
                    biz_name = upd_dict.get("biz_name")
                    biz_is_active = upd_dict.get("biz_is_active")
                    update_user = opt_user
                    update_time = curr_time

                    biz_id = upd_dict.get("biz_id")

                    upd_obj = BizBaseInfo(
                        biz_code=biz_code,
                        biz_name=biz_name,
                        biz_scenario_name=None,
                        biz_is_active=biz_is_active,
                        update_user=update_user,
                        update_time=update_time,
                        id=biz_id,
                    )
                    upd_obj_list.append(upd_obj)

            if upd_obj_list:
                upd_fields = [
                    'biz_code',
                    'biz_name',
                    'biz_scenario_name',
                    'biz_is_active',
                    'update_user',
                    'update_time',
                ]
                try:
                    with transaction.atomic():
                        BizBaseInfo.objects.bulk_update(upd_obj_list, upd_fields)
                        upd_count = len(upd_obj_list)
                except Exception as e:
                    logger.error(e)
        return upd_count

    def del_biz_list(self, del_dict_list, curr_time=None, opt_user=None):
        del_count = 0
        if del_dict_list:
            if not curr_time:
                curr_time = datetime.datetime.now()
            if not opt_user:
                opt_user = self.opt_user

            del_obj_list = []
            for del_dict in del_dict_list:
                if del_dict:
                    biz_code = del_dict.get("biz_code")
                    biz_name = del_dict.get("biz_name")
                    biz_is_active = False
                    update_user = opt_user
                    update_time = curr_time

                    biz_id = del_dict.get("biz_id")

                    del_obj = BizBaseInfo(
                        biz_code=biz_code,
                        biz_name=biz_name,
                        biz_is_active=biz_is_active,
                        update_user=update_user,
                        update_time=update_time,
                        id=biz_id,
                    )
                    del_obj_list.append(del_obj)

            if del_obj_list:
                del_fields = [
                    'biz_code',
                    'biz_name',
                    'biz_is_active',
                    'update_user',
                    'update_time',
                ]
                try:
                    with transaction.atomic():
                        BizBaseInfo.objects.bulk_update(del_obj_list, del_fields)
                        del_count = len(del_obj_list)
                except Exception as e:
                    logger.error(e)
        return del_count


class BizTestFlowInfoApi(viewsets.ViewSet):
    __biz_code_param = "biz_code"
    __suite_code_param = "suite_code"

    def list(self, request):
        biz_code = request.GET.get(self.__biz_code_param)
        suite_code = request.GET.get(self.__suite_code_param)
        biz_flow_list = get_flow_and_biz_base_info(biz_code, suite_code)
        return Response(data=ApiResult.success_dict(data=biz_flow_list, msg="业务流查询成功"))


class BizFlowInfoApi(viewsets.ViewSet):
    __biz_iter_id_param = "biz_test_iter_id"
    __suite_code_param = "suite_code"

    def list(self, request):
        biz_iter_id = request.GET.get(self.__biz_iter_id_param)
        suite_code = request.GET.get(self.__suite_code_param)
        biz_test_iter_split = biz_iter_id.split('_')
        biz_code = biz_test_iter_split[0]
        biz_branch = biz_test_iter_split[1]
        biz_flow_list = get_flow_and_biz_base_info(biz_code, suite_code, biz_branch)
        return Response(data=ApiResult.success_dict(data=biz_flow_list, msg="业务流查询成功"))


class BizTestFlowInfoByBizIterApi(viewsets.ViewSet):
    __biz_code_param = "biz_test_iter_id"

    def list(self, request):
        serializer = BizTestFlowSerializer(data=request.query_params)
        if not serializer.is_valid():
            return Response(serializer.errors, status=400)
        biz_flow_list = get_flow_name_by_biz_test_iter_id(serializer.data.get(self.__biz_code_param))
        return Response(data=ApiResult.success_dict(data=biz_flow_list, msg="业务流查询成功"))


class BizTestSuiteCodeByBizIterApi(viewsets.ViewSet):
    __biz_code_param = "biz_test_iter_id"

    def list(self, request):
        serializer = BizTestFlowSerializer(data=request.query_params)
        if not serializer.is_valid():
            return Response(serializer.errors, status=400)
        suite_list = get_suite_code_by_biz_test_iter_id(serializer.data.get(self.__biz_code_param))
        return Response(data=ApiResult.success_dict(data=suite_list, msg="业务流查询成功"))


class BizTestSerializer(serializers.Serializer):
    biz_code = serializers.CharField()
    suite_code = serializers.CharField()


class BizTestCommonSerializer(serializers.Serializer):
    biz_code = serializers.CharField()


class BizTestFlowSerializer(serializers.Serializer):
    biz_test_iter_id = serializers.CharField()


class BizTestIterApi(viewsets.ViewSet):
    # authentication_classes = []
    def list(self, request):
        biz_code = request.GET.get("biz_code")
        if biz_code:
            biz_iter_list = BizTestIter.objects.filter(br_status="open", biz_code=biz_code).all()
        else:
            biz_iter_list = BizTestIter.objects.filter(br_status="open").all()
        new_biz_iter_list = []
        for biz_iter in biz_iter_list:
            biz_iter = biz_iter.__dict__
            biz_iter.pop('_state')
            new_biz_iter_list.append(biz_iter)
        return Response(data=ApiResult.success_dict(data=new_biz_iter_list, msg="业务迭代查询成功"))

    """
    根据迭代ID查询业务在途分支
    :param request:
    :return:
    """

    def create(self, request):
        biz_test_iter_id = request.data['biz_test_iter_id']
        biz_code = biz_test_iter_id.split('_')[0]
        biz_iter_list = BizTestIter.objects.filter(br_status="open",
                                                   biz_code=biz_code).all()
        new_biz_iter_list = []
        for biz_iter in biz_iter_list:
            biz_iter = biz_iter.__dict__
            new_biz_iter_list.append(biz_iter.get('biz_test_iter_id').split('_')[1])
        return Response(data=ApiResult.success_dict(data=new_biz_iter_list, msg="业务迭代查询成功"))


# class BizTestIterByBizCodeApi(viewsets.ViewSet):
#     def list(self, request):
#         biz_base_db = request.GET.get("biz_base_db")
#         biz_test_iters = BizTestIter.objects.filter(
#             br_status='open',
#             biz_code__in=BizBaseDbBind.objects.filter(biz_base_db_code=biz_base_db,
#                                                       biz_code__in=BizBaseInfo.objects.filter(
#                                                           biz_category=2).values_list('biz_code',
#                                                                                       flat=True)).values_list(
#                 'biz_code', flat=True)
#         ).values_list('biz_test_iter_br', flat=True)
#         biz_iter_list = ['master']
#         for biz_iter_info in biz_test_iters:
#             biz_iter_list.append(biz_iter_info)
#         return Response(data=ApiResult.success_dict(data=biz_iter_list, msg="业务迭代查询成功"))


class BizAppBindApi(viewsets.ViewSet):
    __biz_code_param = "biz_code"
    __biz_test_iter_id = "biz_test_iter_id"
    __biz_test_apps_param = "test_apps"
    __biz_dep_apps_param = "dep_apps"
    __biz_pa_apps_param = "pa_apps"
    __biz_d3_apps_param = "d3_apps"

    def list(self, request):
        serializer = BizTestCommonSerializer(data=request.query_params)
        if not serializer.is_valid():
            return Response(serializer.errors, status=400)
        biz_app_list = get_apps_by_biz_code(serializer.data.get(self.__biz_code_param))
        if request.query_params.get(self.__biz_test_iter_id):
            biz_iter_app_list = BizTestIterApp.objects.filter(
                biz_test_iter_id=request.query_params.get(self.__biz_test_iter_id)).all()
            for app in biz_app_list:
                for iter_app in biz_iter_app_list:
                    if iter_app.app_module_name == app.get("app_module_name"):
                        app["biz_iter_app_archive_br_name"] = iter_app.archive_br_name
                        br_list = app["br_name_fifteen"].split(",") if app.get("br_name_fifteen") else []
                        self.append_branch(br_list, app.get("br_name_fifteen"), iter_app.archive_br_name)
                        self.append_branch(br_list, app.get("archive_br"), app.get("archive_br"))
                        self.append_branch(br_list, app.get("online_br"), app.get("online_br"))
                        app["br_name_fifteen"] = ",".join(list(set(br_list))) if br_list else ''
        return Response(data=ApiResult.success_dict(data=biz_app_list, msg="业务应用查询成功"))

    def append_branch(self, br_list, br_name_fifteen, archive_br_name):
        if br_name_fifteen and archive_br_name:
            br_list.append(archive_br_name)

    def create(self, request):
        try:
            logger.info("biz_code:{}".format(request.data[self.__biz_code_param]))
            BizAppBind.objects.filter(biz_code=request.data[self.__biz_code_param]).delete()
            for test_app in request.data[self.__biz_test_apps_param]:
                BizAppBind.objects.create(biz_code=request.data[self.__biz_code_param], app_module_name=test_app,
                                          biz_app_bind_type=1,
                                          biz_app_bind_is_active=1, create_user=str(request.user),
                                          create_time=datetime.datetime.now())

            for test_app in request.data[self.__biz_dep_apps_param]:
                BizAppBind.objects.create(biz_code=request.data[self.__biz_code_param], app_module_name=test_app,
                                          biz_app_bind_type=2,
                                          biz_app_bind_is_active=1, create_user=str(request.user),
                                          create_time=datetime.datetime.now())

            for test_app in request.data[self.__biz_pa_apps_param]:
                BizAppBind.objects.create(biz_code=request.data[self.__biz_code_param], app_module_name=test_app,
                                          biz_app_bind_type=3,
                                          biz_app_bind_is_active=1, create_user=str(request.user),
                                          create_time=datetime.datetime.now())

            for test_app in request.data[self.__biz_d3_apps_param]:
                BizAppBind.objects.create(biz_code=request.data[self.__biz_code_param], app_module_name=test_app,
                                          biz_app_bind_type=4,
                                          biz_app_bind_is_active=1, create_user=str(request.user),
                                          create_time=datetime.datetime.now())
            return Response(data=ApiResult.success_dict(msg="业务应用绑定成功"))
        except Exception as ex:
            logger.error(ex)
            return Response(data=ApiResult.success_dict(msg=str(ex)))


class BizTestFlowScheduleConfigApi(viewsets.ViewSet):
    __biz_iter_branch = "biz_iter_branch"
    __biz_code = "biz_code"
    __test_suite_code = "suite_code"
    __biz_flow_name = "biz_flow_name"
    __cron = "cron"
    __enable_schedule = "enable_schedule"
    __testset_detail_list = "testset_detail_list"
    __version_type = "version_type"

    def list(self, request):
        data_list = BizTestFlowScheduleConfig.objects.all()
        new_List = []
        for data in data_list:
            global_params_dbs = BizTestFlowGlobalParam.objects.filter(schedule_config_id=data.id).all()
            data = data.__dict__
            data.pop('_state')
            for global_param_db in global_params_dbs:
                global_param_db = global_param_db.__dict__
                global_param_db.pop('_state')
                if global_param_db['param_type'] == 'test_apps':
                    data['global_param'] = global_param_db
                if global_param_db['param_type'] == 'ext_config':
                    data['ext_config'] = global_param_db
            new_List.append(data)
        return Response(data=ApiResult.success_dict(data=new_List, msg="自动化配置查询成功"))

    def create(self, request):
        try:
            serializer = BizTestFlowScheduleConfigSerializer(data=request.data)
            if not serializer.is_valid():
                return Response(serializer.errors, status=400)
            
            with transaction.atomic():
                # 1. 创建或更新调度配置
                schedule_config = self._create_or_update_schedule_config(request, serializer.data)
                
                # 2. 更新全局参数
                self._update_global_params(request, schedule_config.id)
                
                # 3. 创建或更新 Jenkins 任务
                self._setup_jenkins_job(schedule_config, serializer.data)
                
                # 4. 更新测试集详情
                self._update_test_set_details(request, serializer.data)

            return Response(data=ApiResult.success_dict(
                data=schedule_config.id, 
                msg="自动化执行配置保存成功！"
            ))
            
        except Exception as ex:
            logger.error(f"创建调度配置失败: {str(ex)}", exc_info=True)
            return Response(data=ApiResult.failed_dict(msg=str(ex)))

    def _create_or_update_schedule_config(self, request, data):
        """创建或更新调度配置"""
        filter_kwargs = {
            'biz_iter_branch': data.get(self.__biz_iter_branch),
            'biz_code': data.get(self.__biz_code),
            'suite_code': data.get(self.__test_suite_code),
            'biz_flow_name': data.get(self.__biz_flow_name)
        }
        
        obj = BizTestFlowScheduleConfig.objects.filter(**filter_kwargs).first()
        if obj:
            BizTestFlowScheduleConfig.objects.filter(id=obj.id).update(
                update_user=str(request.user),
                update_time=datetime.datetime.now(),
                enable_schedule=data.get(self.__enable_schedule),
                cron=data.get(self.__cron)
            )
            return obj
        
        return BizTestFlowScheduleConfig.objects.create(
            **filter_kwargs,
            cron=data.get(self.__cron),
            enable_schedule=data.get(self.__enable_schedule),
            create_user=str(request.user),
            create_time=datetime.datetime.now()
        )

    def _update_global_params(self, request, config_id):
        """更新全局参数"""
        BizTestFlowGlobalParam.objects.filter(schedule_config_id=config_id).delete()
        if request.data.get("params"):
            params_to_create = [
                BizTestFlowGlobalParam(
                    schedule_config_id=str(config_id),
                    param_type=param['param_type'],
                    param=param['params'],
                    create_user=str(request.user),
                    create_time=datetime.datetime.now()
                ) for param in request.data.get("params")
            ]
            BizTestFlowGlobalParam.objects.bulk_create(params_to_create)

    def _setup_jenkins_job(self, schedule_config, data):
        """设置 Jenkins 任务"""
        parameter_definitions = json.dumps([
            ParameterDefinition(
                name="schedule_config_id",
                description="定时配置ID",
                trim=False,
                defaultValue=str(schedule_config.id),
                type="string"
            ).__dict__
        ])

        pipeline_config = self._get_pipeline_config(
            parameter_definitions, 
            data.get(self.__enable_schedule),
            data.get(self.__cron)
        )

        pipeline_name = f"{data.get(self.__biz_iter_branch)}__{data.get(self.__biz_flow_name)}__{data.get(self.__test_suite_code)}"
        jenkins_job_mgt = JenkinsJobMgt()

        if schedule_config.jenkins_info_id:
            jenkins_server = jenkins_job_mgt.get_jenkins_server_id(schedule_config.jenkins_info_id)
            jenkins_server.server.reconfig_job(pipeline_name, pipeline_config)
        else:
            jenkins_server = jenkins_job_mgt.build_job_auto_retry_other_jenkins(pipeline_name, pipeline_config)
            jenkins_url = f"{jenkins_server.jenkins_url}job/{pipeline_name}/"
            BizTestFlowScheduleConfig.objects.filter(id=schedule_config.id).update(
                jenkins_url=jenkins_url,
                jenkins_info_id=jenkins_server.jenkins_info_id
            )

    def _get_pipeline_config(self, parameter_definitions, enable_schedule, cron):
        """获取流水线配置"""
        base_args = [
            '--locator', '*************************:scm/be-scripts.git',
            '--branch_spec', 'master',
            '--script_path', 'Jenkinsfile/schedule_execute_biz_config.groovy',
            '--parameter_definitions', parameter_definitions,
        ]
        
        if enable_schedule:
            base_args.extend(['--build-command', 'cron', '--trigger_cron', cron])
            
        return JenkinsJobManager.main(base_args)

    def _update_test_set_details(self, request, data):
        """更新测试集详情"""
        try:
            logger.info(f"更新测试集详情: {data}")
            biz_test_flow_obj = BizTestFlowInfo.objects.filter(
                biz_code=data.get(self.__biz_code),
                biz_flow_name=data.get(self.__biz_flow_name)
            ).first()
            
            if not biz_test_flow_obj:
                logger.warning(f"找不到对应的业务流程信息: biz_code={data.get(self.__biz_code)}, biz_flow_name={data.get(self.__biz_flow_name)}")
                return
            
            if not biz_test_flow_obj.biz_pipeline_name:
                logger.warning(f"业务流程的pipeline_name为空: biz_flow_id={biz_test_flow_obj.id}")
                return

            testset_detail_list = data.get(self.__testset_detail_list)
            if not testset_detail_list:
                logger.warning("测试集详情列表为空")
                return

            BizTestFlowTestsetDetail.update_testset_detail(
                biz_test_flow_obj.biz_pipeline_name,
                testset_detail_list,
                str(request.user),
                data.get(self.__version_type)
            )
        except Exception as e:
            logger.error(f"更新测试集详情失败: {str(e)}", exc_info=True)
            # 这里不抛出异常，让上层继续处理其他逻辑

    @action(methods=['post'], detail=False, url_path='del', authentication_classes=[])
    def del_flow_schedule_config(self, request):
        try:
            flow_schedule_config_id = request.data.get("id", None)
            if not flow_schedule_config_id:
                return Response(data=ApiResult.failed_dict(msg="参数错误：配置ID为空"))
            with transaction.atomic():
                obj = BizTestFlowScheduleConfig.objects.filter(id=flow_schedule_config_id)
                if obj:
                    schedule_config_flow = obj.get()
                    pipeline_name = schedule_config_flow.biz_iter_branch + "__" + schedule_config_flow.biz_flow_name + "__" + schedule_config_flow.suite_code
                    obj.delete()
                    BizTestFlowGlobalParam.objects.filter(schedule_config_id=flow_schedule_config_id).delete()
                    jenkins_job_mgt = JenkinsJobMgt()
                    jenkins_server = jenkins_job_mgt.get_jenkins_server_id(schedule_config_flow.jenkins_info_id)
                    logger.info("jenkins_url={}".format(jenkins_server.jenkins_url))
                    jenkins_server.server.delete_job(pipeline_name)
            return Response(data=ApiResult.success_dict(data={}, msg="删除自动化配置成功"))
        except Exception as ex:
            logger.error(ex)
            return Response(data=ApiResult.failed_dict(msg=str(ex)))


class BizTestFlowScheduleConfigSerializer(serializers.Serializer):
    biz_iter_branch = serializers.CharField()
    biz_flow_name = serializers.CharField()
    biz_code = serializers.CharField()
    suite_code = serializers.CharField()
    cron = serializers.CharField()
    enable_schedule = serializers.IntegerField()
    testset_detail_list = serializers.JSONField()
    version_type = serializers.CharField()


# class BizBaseDbBindApi(viewsets.ViewSet):
#
#     def list(self, request):
#         biz_code = request.GET.get("biz_code")
#
#         return_data = BizBaseDbBind.objects.filter(biz_code=biz_code, biz_base_db_bind_is_active=1). \
#             values("biz_base_db_code").first()
#
#         return Response(data=ApiResult.success_dict(data=return_data, msg="业务基础库查询成功！"))


# class BizBaseDbBindExtendApi(viewsets.ViewSet):
#
#     def list(self, request):
#         biz_test_iter = request.GET.get("biz_test_iter")
#
#         base_db_info_list = get_biz_base_db_info_by_biz_iter(biz_test_iter)
#         base_db_list = []
#         for base_db_info in base_db_info_list:
#             base_db_list.append(base_db_info.get("biz_base_db_code"))
#             if base_db_info.get("biz_category") == 2:
#                 base_db_list = get_biz_base_db_info_by_id(base_db_info.get("biz_parent_id"), base_db_list)
#         return Response(data=ApiResult.success_dict(data=base_db_list, msg="业务基础库查询成功！"))


class BizRelativeScheduleAPI(viewsets.ViewSet):

    def list(self, request):
        biz_code = request.GET.get("biz_code")

        query = BizTestFlowScheduleConfig.objects.filter(biz_code=biz_code, enable_schedule=1)
        msg = ''
        for item in query:
            msg += item.biz_iter_branch + "__" + item.biz_flow_name + "__" + item.suite_code + ";"
        if msg:
            return Response(data=ApiResult.success_dict(msg=msg))
        else:
            return Response(data=ApiResult.failed_dict(msg="未查询到相关配置"))
