from django.db import connection
from spider.settings import logger, JENKINS_INFO
import datetime
from typing import Optional, Tuple, List, Dict, Any


def get_flow_and_biz_base_info(biz_code, suite_code=None, biz_branch=None):
    # 兼容从希娜同步业务的新逻辑 for怀天  20241223 by fwm
    sql = '''
          select DISTINCT flow_info.biz_code, \
                          flow_info.biz_flow_name,
                          IF(base_info.biz_scenario_name IS NULL, base_info.biz_name, \
                             CONCAT(base_type.biz_type_department, base_type.biz_type_transaction, '-', \
                                    base_info.biz_name, '-', base_info.biz_scenario_name)),
                          flow_info.biz_pipeline_name
          from biz_test_flow_info flow_info
                   join biz_base_info base_info on flow_info.biz_code = base_info.biz_code
                   join biz_base_type base_type on base_info.biz_type = base_type.id \
          '''
    if suite_code:
        sql += " join biz_test_flow_schedule_config sc ON sc.biz_code = flow_info.biz_code AND flow_info.biz_flow_name = sc.biz_flow_name  "

    sql += """
         where flow_info.biz_code='{biz_code}'
    """.format(biz_code=biz_code)
    if suite_code:
        sql += """and sc.suite_code = '{suite_code}'
        """.format(suite_code=suite_code)
    if biz_branch:
        sql += """ and sc.biz_iter_branch = '{biz_branch}'
                """.format(biz_branch=biz_branch)

    logger.info(sql)
    cursor = connection.cursor()
    cursor.execute(sql)
    biz_flow_info_list = []
    for row in cursor.fetchall():
        biz_flow_info_list.append(
            {"biz_code": row[0], "biz_flow_name": row[1], "biz_name": row[2],
             "biz_pipeline_name": row[3]})
    return biz_flow_info_list


def get_app_relative_test_iter_list(module_name):
    sql = '''
           SELECT DISTINCT a.biz_test_iter_id
           FROM biz_test_iter a 
           INNER JOIN biz_app_bind b ON a.biz_code = b.biz_code
           WHERE b.app_module_name = '{module_name}'
        '''.format(module_name=module_name)

    logger.info(sql)
    cursor = connection.cursor()
    cursor.execute(sql)
    test_iter_list = []
    for row in cursor.fetchall():
        test_iter_list.append(row[0])
    return test_iter_list


def get_all_flow_info():
    sql = '''
          SELECT DISTINCT b.biz_code, \
                          b.biz_iter_branch, \
                          b.biz_flow_name, \
                          b.suite_code, \
                          b.enable_schedule, \
                          b.cron, \
                          b.create_user, \
                          b.update_user
          FROM biz_test_flow_info a
                   INNER JOIN biz_test_flow_schedule_config b on a.biz_code = b.biz_code \
          '''
    logger.info(sql)
    cursor = connection.cursor()
    cursor.execute(sql)
    biz_flow_info_list = []
    for row in cursor.fetchall():
        biz_flow_info_list.append(
            {"biz_code": row[0], "biz_iter_branch": row[1], "biz_flow_name": row[2], "suite_code": row[3],
             "is_active": row[4], "cron": row[5], "create_user": row[6], "update_user": row[7]})
    return biz_flow_info_list


def get_flow_name_by_biz_test_iter_br(biz_test_iter_br):
    sql = '''
            SELECT DISTINCT fi.biz_flow_name FROM biz_test_iter ti
            LEFT JOIN biz_test_flow_info fi ON ti.biz_code = fi.biz_code
            WHERE ti.biz_test_iter_br = "{biz_test_iter_br}";
        '''.format(biz_test_iter_br=biz_test_iter_br)

    logger.info(sql)
    cursor = connection.cursor()
    cursor.execute(sql)
    biz_flow_info_list = []
    for row in cursor.fetchall():
        biz_flow_info_list.append({"biz_flow_name": row[0]})
    return biz_flow_info_list


def get_flow_name_by_biz_test_iter_id(biz_test_iter_id):
    sql = '''
                SELECT DISTINCT fi.biz_flow_name FROM biz_test_iter ti
                LEFT JOIN biz_test_flow_info fi ON ti.biz_code = fi.biz_code
                WHERE ti.biz_test_iter_id = "{biz_test_iter_id}";
            '''.format(biz_test_iter_id=biz_test_iter_id)

    logger.info(sql)
    cursor = connection.cursor()
    cursor.execute(sql)
    biz_flow_info_list = []
    for row in cursor.fetchall():
        biz_flow_info_list.append({"biz_flow_name": row[0]})
    return biz_flow_info_list


def get_suite_code_by_biz_test_iter_id(biz_test_iter_id):
    biz_test_split = biz_test_iter_id.split("_")
    biz_code = biz_test_split[0]
    biz_branch = biz_test_split[1]
    sql = '''
            select DISTINCT sc.suite_code
            from biz_test_flow_schedule_config sc
            where sc.biz_code= '{biz_code}' AND sc.biz_iter_branch = '{biz_branch}';
            '''.format(biz_code=biz_code, biz_branch=biz_branch)

    logger.info(sql)
    cursor = connection.cursor()
    cursor.execute(sql)
    biz_flow_info_list = []
    for row in cursor.fetchall():
        biz_flow_info_list.append(row[0])
    return biz_flow_info_list


def get_iter_app_branch(bis_pipeline_id):
    sql = '''
    SELECT DISTINCT
            app.app_module_name,
            app_info.archive_br,
            iapp.archive_br_name
    FROM
            biz_app_bind app
            INNER JOIN app_br_cache app_info ON app.app_module_name = app_info.app_module_name 
            AND app_info.time_batch = ( SELECT MAX( time_batch ) FROM app_br_cache )
            INNER JOIN biz_test_iter_app iapp ON app.app_module_name = iapp.app_module_name 
            INNER join iter_mgt_iter_app_info ainfo ON ainfo.appName = app.app_module_name
            INNER JOIN iter_mgt_iter_info miter ON iapp.archive_br_name = miter.br_name and miter.pipeline_id = ainfo.pipeline_id
            WHERE app.biz_app_bind_type in (1,2)
            and miter.br_status = 'open'
            and iapp.biz_test_iter_id = '{}'
          '''.format(bis_pipeline_id)
    cursor = connection.cursor()
    cursor.execute(sql)
    iter_app_br_list = []
    for row in cursor.fetchall():
        iter_app_br_list.append({"app_name": row[0], "archive_br": row[1], "archive_br_name": row[2]})
    return iter_app_br_list


def get_last_archive_test_iter(biz_code, bis_pipeline_id):
    sql = '''
             select biz_test_iter_id, biz_test_iter_br, br_from from biz_test_iter 
             where biz_code = '{}' and br_status = 'close' and biz_test_iter_id != '{}'
             order by br_end_time DESC LIMIT 1
          '''.format(biz_code, bis_pipeline_id)
    cursor = connection.cursor()
    cursor.execute(sql)
    biz_app_list = []
    for row in cursor.fetchall():
        biz_app_list.append({"biz_test_iter_id": row[0], "biz_test_iter_br": row[1], "br_from": row[2]})
    return biz_app_list


def get_execute_dump_restore(db_logic_id, suite_code):
    sql = """
        SELECT li.logic_db_name FROM db_mgt_dump_restore dr
        INNER JOIN db_mgt_logic_info li on dr.db_logic_id = li.id
        WHERE dr.db_logic_id = {db_logic_id}
        AND dr.suite_code = '{suite_code}'
        AND dr.`status` = 'running'
        UNION ALL
        SELECT li.logic_db_name FROM db_mgt_dump_restore dr
        INNER JOIN db_mgt_logic_info li on dr.db_logic_id = li.id
        WHERE dr.lock_status = 'lock'
    """.format(db_logic_id=db_logic_id, suite_code=suite_code)
    logger.info(sql)
    cursor = connection.cursor()
    cursor.execute(sql)
    result = []
    for row in cursor.fetchall():
        result.append(row[0])
    return result


def get_app_and_db_name_list(biz_code):
    sql = '''
        SELECT DISTINCT abind.app_module_name,logic_info.logic_db_name
        FROM biz_app_bind abind 
        INNER JOIN db_mgt_app_bind app_bind ON abind.app_module_name = app_bind.app_module_name
        LEFT JOIN db_mgt_domain domain ON domain.id = app_bind.db_domain_id
        LEFT JOIN db_mgt_logic_info logic_info ON logic_info.db_domain_id = app_bind.db_domain_id
        WHERE abind.biz_code = '{}' AND app_bind.db_app_bind_is_active = 1
        AND logic_info.logic_db_name IS NOT NULL
          '''.format(biz_code)
    cursor = connection.cursor()
    logger.info(sql)
    cursor.execute(sql)
    app_dict_list = []
    app_list = list()
    db_name_list = []
    for row in cursor.fetchall():
        app_name = row[0]
        db_name = row[1]
        if app_name not in app_list:
            app_list.append(app_name)
            app_dict_list.append({'module_name': row[0]})
        if db_name not in db_name_list:
            db_name_list.append(db_name)
    return app_dict_list, db_name_list


def get_dump_jenkins_url(biz_iter_id, job_type):
    sql = """
        SELECT job_url,job_build_id FROM jenkins_mgt_test_data_dev_job 
        WHERE biz_iter_id = '{}' and job_type = '{}' order by id desc LIMIT 1
    """.format(biz_iter_id, job_type)
    cursor = connection.cursor()
    cursor.execute(sql)
    result = cursor.fetchone()
    if result:
        job_url = result[0] + "/" + result[1]
    return job_url


def get_open_iter_from_br_from(biz_code, br_from):
    test_iter_list = list()
    if br_from and br_from != '':
        sql = '''
                   select distinct biz_test_iter_id from biz_test_iter 
                   where biz_code = '{}' and br_from = '{}' and br_status = 'open'
                  '''.format(biz_code, br_from)
        cursor = connection.cursor()
        cursor.execute(sql)
        for row in cursor.fetchall():
            test_iter_list.append(row[0])
    return test_iter_list


def get_apps_by_biz_code(biz_code):
    sql = '''
            SELECT DISTINCT
                app.app_module_name,
                app_info.online_br,
                IFNULL(app_info.archive_br, '归档版本') AS archive_br,
                app_info.br_name_fifteen,
                app.biz_app_bind_type ,
                GROUP_CONCAT( DISTINCT li.logic_db_name ) AS db_names 
            FROM
                biz_app_bind app
                LEFT JOIN app_br_cache app_info ON app.app_module_name = app_info.app_module_name 
                AND app_info.time_batch = ( SELECT MAX( time_batch ) FROM app_br_cache )
                LEFT JOIN db_mgt_app_bind t ON 	app.app_module_name=t.app_module_name AND t.db_app_bind_is_active=1
                LEFT JOIN db_mgt_logic_info li ON t.db_domain_id = li.db_domain_id 
                WHERE app.biz_code='{biz_code}' 
                GROUP BY
                app.app_module_name  
                ORDER BY app.biz_app_bind_type ASC          
        '''.format(biz_code=biz_code)

    cursor = connection.cursor()
    cursor.execute(sql)
    biz_app_list = []
    for row in cursor.fetchall():
        biz_app_list.append(
            {"app_module_name": row[0], "online_br": row[1], "archive_br": row[2], "br_name_fifteen": row[3],
             "biz_app_bind_type": row[4],
             "db_names": row[5]})
    return biz_app_list


def check_suite_code_is_using(suite_code):
    sql = '''
            SELECT
                job_type,status
            FROM
                jenkins_mgt_test_data_dev_job
            WHERE suite_code='{suite_code}'
             order by id desc LIMIT 1
        '''.format(suite_code=suite_code)
    cursor = connection.cursor()
    cursor.execute(sql)
    result = cursor.fetchone()
    if result and result[1] == 'running':
        return result[0]
    return None


def update_execute_status(status, execute_id):
    sql = '''
        UPDATE jenkins_mgt_test_data_dev_job SET status = '{}' WHERE id = {}
    '''.format(status, execute_id)
    cursor = connection.cursor()
    cursor.execute(sql)
    connection.commit()


def get_open_dev_pipeline_id(biz_test_iter_id):
    sql = '''
        SELECT DISTINCT
            a.project_group,
            a.pipeline_id 
        FROM
            iter_mgt_iter_info a
            LEFT JOIN app_mgt_app_info b ON a.project_group = b.git_url
            LEFT JOIN app_mgt_app_module c ON b.id = c.app_id
            LEFT JOIN biz_test_iter_app app ON c.module_name = app.app_module_name 
            AND app.archive_br_name = a.br_name 
        WHERE
            app.biz_test_iter_id = '{}'
            and a.br_status = 'open'
            and a.project_group IS NOT NULL;
        '''.format(biz_test_iter_id)
    logger.info(sql)
    results = list()
    cursor = connection.cursor()
    cursor.execute(sql)
    pipeline_result = cursor.fetchall()
    for item in pipeline_result:
        results.append({"project_group": item[0], "pipeline_id": item[1]})
    return results


# 当前环境最后一次初始化的业务和分支与当前业务与分支是否一致
def get_biz_last_init_biz_iter_id(biz_code, suite_code):
    sql = '''
        SELECT status,biz_code,id
        FROM jenkins_mgt_test_data_dev_job 
        WHERE suite_code = '{suite_code}' 
        AND job_type = 'test_data_init' ORDER BY id DESC LIMIT 1
        '''.format(suite_code=suite_code)
    logger.info(sql)
    cursor = connection.cursor()
    cursor.execute(sql)
    pipeline_result = cursor.fetchone()
    if pipeline_result:
        status = pipeline_result[0]
        if status and status == 'success':
            db_biz_code = pipeline_result[1]
            check_result = db_biz_code == biz_code
            if check_result:
                return pipeline_result[2]
    return None


# 当前环境最后一次初始化后，是否归档，若有归档，判断是否归档成功
def check_last_archive_result(suite_code, execute_id):
    sql = '''
            SELECT status
            FROM jenkins_mgt_test_data_dev_job 
            WHERE suite_code = '{suite_code}' 
            AND id > '{id}'
            AND job_type = 'archive_make_dump' ORDER BY id DESC LIMIT 1
            '''.format(suite_code=suite_code, id=execute_id)
    logger.info(sql)
    cursor = connection.cursor()
    cursor.execute(sql)
    data = cursor.fetchone()
    if data and data[0] != 'success':
        return False
    return True


def get_all_biz_for_upd():
    """获取所有的业务数据，用来对比更新。zt@2024-11-20"""
    sql = '''
          select biz.biz_id,
                 biz.biz_code,
                 IF(biz.biz_scenario_name is NULL,
                    biz.biz_name,
                    biz.biz_info_name) as biz_name,
                 biz.biz_is_active
          from (select biz_i.id                        as biz_id, \
                       biz_i.biz_code, \
                       biz_i.biz_name, \
                       biz_i.biz_scenario_name, \
                       CONCAT(biz_t.biz_type_department, \
                              biz_t.biz_type_transaction, '-', \
                              biz_i.biz_name, '-', \
                              biz_i.biz_scenario_name) as biz_info_name, \
                       biz_i.biz_is_active, \
                       biz_t.biz_type_company, \
                       biz_t.biz_type_department, \
                       biz_t.biz_type_transaction, \
                       biz_t.biz_type_code, \
                       biz_t.biz_type_name \
                from biz_base_info biz_i \
                         inner join biz_base_type biz_t on biz_t.id = biz_i.biz_type) biz
          where 1 = 1
          order by biz.biz_code; \
          '''.format()
    cursor = connection.cursor()
    cursor.execute(sql)
    return cursor


def get_total_duration_seconds(exec_action_type: str, batch_no: str) -> int:
    """
    根据执行类型和批次号获取总耗时（秒）
    注意：此函数返回秒数，调用方会将其转换为分钟
    :param exec_action_type: 执行动作类型
    :param batch_no: 批次号
    :return: 总耗时秒数
    """
    cursor = connection.cursor()
    total_duration = 0
    
    try:
        if exec_action_type in ["init&biz_job_jenkins_composition", "flow_schedule", "seg_init&biz_job_jenkins_composition"]:
            # 环境初始化耗时
            init_sql = """
            SELECT SUM(IF(temtd.cost_time IS NULL, 0, temtd.cost_time)) as init_duration
            FROM jenkins_mgt_job_composition jmjc 
            JOIN test_env_mgt_test_suite_init_log temt ON jmjc.job_build_id = temt.job_build_id 
            JOIN test_env_mgt_test_suite_init_log_detail temtd ON temt.id = temtd.parent_id 
            WHERE jmjc.batch_no = %s
            """
            cursor.execute(init_sql, [batch_no])
            init_result = cursor.fetchone()
            logger.info(f"init_result: {init_result}")
            if init_result and init_result[0] is not None:
                total_duration += float(init_result[0])  # 统一转换为float类型
                logger.info(f"init_duration_added: {init_result[0]}, total_duration: {total_duration}")
            else:
                logger.info(f"No init_duration found, init_result: {init_result}")
            
            # 业务流水线耗时
            biz_sql = """
            SELECT SUM(IF(pl.cost_time IS NOT NULL, pl.cost_time, 0)) as biz_duration
            FROM jenkins_mgt_biz_job_info_log jl 
            JOIN pipeline_log_biz_auto_test pl ON jl.job_name = pl.job_name AND jl.job_build_id = pl.job_build_id 
            WHERE jl.batch_no = %s
            """
            cursor.execute(biz_sql, [batch_no])
            biz_result = cursor.fetchone()
            logger.info(f"biz_result: {biz_result}")
            logger.info(f"biz_result type: {type(biz_result)}")
            if biz_result:
                logger.info(f"biz_result[0]: {biz_result[0]}")
                logger.info(f"biz_result[0] type: {type(biz_result[0])}")
                logger.info(f"biz_result[0] is not None: {biz_result[0] is not None}")
                logger.info(f"bool(biz_result[0]): {bool(biz_result[0])}")
             
            logger.info(f"Before_condition_check - biz_result: {bool(biz_result)}, biz_result exists: {biz_result is not None}")
            condition_result = biz_result and biz_result[0] is not None
            logger.info(f"Condition_result: {condition_result}")
            if condition_result:
                total_duration += float(biz_result[0])  # 统一转换为float类型
                logger.info(f"biz_duration_added: {biz_result[0]}, total_duration: {total_duration}")
            else:
                logger.info(f"No biz_duration found, keeping total_duration: {total_duration}")
                logger.info(f"Condition check - biz_result: {bool(biz_result)}, biz_result[0] is not None: {biz_result[0] is not None if biz_result else 'N/A'}")
                
        elif exec_action_type == "biz_job":
            # 业务流水线耗时
            sql = """
            SELECT SUM(IF(pl.cost_time IS NOT NULL, pl.cost_time, 0)) as total_duration
            FROM jenkins_mgt_biz_job_info_log jl 
            JOIN pipeline_log_biz_auto_test pl ON jl.job_name = pl.job_name AND jl.job_build_id = pl.job_build_id 
            WHERE jl.batch_no = %s
            """
            cursor.execute(sql, [batch_no])
            result = cursor.fetchone()
            if result and result[0] is not None:
                total_duration = float(result[0])  # 统一转换为float类型
            logger.info(f"biz_job_result: {result}, total_duration: {total_duration}")
                
        elif exec_action_type in ["init", "seg_init"]:
            # 环境初始化耗时
            sql = """
            SELECT SUM(IF(temtd.cost_time IS NULL, 0, temtd.cost_time)) as total_duration
            FROM jenkins_mgt_job_composition jmjc 
            JOIN test_env_mgt_test_suite_init_log temt ON jmjc.job_build_id = temt.job_build_id 
            JOIN test_env_mgt_test_suite_init_log_detail temtd ON temt.id = temtd.parent_id 
            WHERE jmjc.batch_no = %s
            """
            cursor.execute(sql, [batch_no])
            result = cursor.fetchone()
            if result and result[0] is not None:
                total_duration = float(result[0])  # 统一转换为float类型
            logger.info(f"init/seg_init result: {result}, total_duration: {total_duration}")
        
        logger.info(f"Final_total_duration: {total_duration}")
        return int(total_duration) if total_duration is not None else 0
    except Exception as e:
        logger.error(f"获取总耗时失败: {e}")
        return 0

def get_auto_exec_record(page_num=1, page_size=10, **kwargs) -> Tuple[List[Dict[str, Any]], int, List[str]]:
    """
    获取自动执行记录
    :param page_num: 页码
    :param page_size: 每页大小
    :param kwargs: 其他查询条件，支持以下参数：
        - biz_name: 业务名称（精准查询）
        - biz_test_iter_br: 业务测试迭代分支（精准查询）
        - suite_code: 套件代码（精准查询）
        - opt_user: 操作用户（精准查询）
        - batch_no: 批次号（模糊查询）
        - status_0: 状态0（精准查询）
        - status_1: 状态1（精准查询）
    :return: 执行记录列表和总数
    """
    # 第一步：从biz_test_flow_exec_history表获取基础数据
    sql_1 = """
            SELECT id, \
                   SUBSTRING_INDEX(SUBSTRING_INDEX(biz_type, '_', 3), '_', -1)              AS biz_name, \
                   SUBSTRING_INDEX(SUBSTRING_INDEX(biz_type, '_', 2), '_', -1)              AS biz_test_iter_br, \
                   exec_suite_code                                                          AS suite_code, \
                   create_time                                                              AS execute_time, \
                   create_user                                                              AS opt_user, \
                   exec_action_type, \
                   CASE \
                       WHEN exec_action_type = "init" OR exec_action_type = "seg_init" \
                           THEN JSON_UNQUOTE(JSON_EXTRACT(exec_detail_param, '$.jenkins_url')) \
                       ELSE JSON_UNQUOTE(JSON_EXTRACT(exec_detail_param, '$.batch_no')) END AS batch_no
            FROM biz_test_flow_exec_history
            WHERE 1 = 1 \
            """

    # 添加查询条件
    where_conditions = []
    where_params = {}

    # 精准查询条件
    if kwargs.get('biz_name'):
        where_conditions.append("SUBSTRING_INDEX(SUBSTRING_INDEX(biz_type, '_', 3), '_', -1) = %(biz_name)s")
        where_params['biz_name'] = kwargs.get('biz_name')

    if kwargs.get('biz_test_iter_br'):
        where_conditions.append("SUBSTRING_INDEX(SUBSTRING_INDEX(biz_type, '_', 2), '_', -1) = %(biz_test_iter_br)s")
        where_params['biz_test_iter_br'] = kwargs.get('biz_test_iter_br')

    if kwargs.get('suite_code'):
        where_conditions.append("exec_suite_code = %(suite_code)s")
        where_params['suite_code'] = kwargs.get('suite_code')

    if kwargs.get('opt_user'):
        where_conditions.append("create_user = %(opt_user)s")
        where_params['opt_user'] = kwargs.get('opt_user')

    if kwargs.get('start_date'):
        where_conditions.append("create_time >= %(start_date)s")
        where_params['start_date'] = kwargs.get('start_date')

    if kwargs.get('end_date'):
        where_conditions.append("create_time <= %(end_date)s")
        where_params['end_date'] = kwargs.get('end_date')

    # 模糊查询条件
    if kwargs.get('batch_no'):
        batch_no = kwargs.get('batch_no')
        case_expression = '''CASE WHEN exec_action_type = 'init' OR exec_action_type = 'seg_init' 
                             THEN JSON_UNQUOTE(JSON_EXTRACT(exec_detail_param, '$.jenkins_url')) 
                             ELSE JSON_UNQUOTE(JSON_EXTRACT(exec_detail_param, '$.batch_no')) END'''

        where_conditions.append(f'''(({case_expression} LIKE %(batch_no)s 
                                  OR {case_expression} LIKE  
                                  (SELECT batch_no FROM jenkins_mgt_biz_job_run_testset 
                                   WHERE execute_id = %(execute_id)s)))''')
        where_params['batch_no'] = f"%{batch_no}%"
        where_params['execute_id'] = f"{batch_no}"

    # 将查询条件添加到SQL中
    if where_conditions:
        sql_1 += " AND " + " AND ".join(where_conditions)

    # 添加排序
    sql_1 += " ORDER BY id DESC"

    # 计算总数的SQL
    count_sql = "SELECT COUNT(*) FROM biz_test_flow_exec_history WHERE 1=1"
    if where_conditions:
        count_sql += " AND " + " AND ".join(where_conditions)

    # 添加分页
    limit_sql = " LIMIT {}, {}".format((int(page_num) - 1) * int(page_size), int(page_size))
    sql_1 += limit_sql

    logger.info(sql_1)
    cursor = connection.cursor()
    cursor.execute(sql_1, where_params)
    base_records = cursor.fetchall()

    # 获取总数
    cursor.execute(count_sql, where_params)
    total_count = cursor.fetchone()[0]

    result_list = []
    filtered_result_list = []
    batch_no_list = []

    # 第二步：根据exec_action_type类型执行不同的SQL查询获取详细信息
    for record in base_records:
        record_id = record[0]
        biz_name = record[1]
        biz_test_iter_br = record[2]
        suite_code = record[3]
        execute_time = record[4]
        opt_user = record[5]
        exec_action_type = record[6]
        batch_no = record[7]
        batch_no_list.append(batch_no)

        # 初始化结果字典
        result_dict = {
            "biz_name": biz_name,
            "biz_test_iter_br": biz_test_iter_br,
            "suite_code": suite_code,
            "execute_time": execute_time,
            "opt_user": opt_user,
            "batch_no": batch_no,
            "pipeline_type_0": "",
            "job_url_0": "",
            "status_0": "",
            "pipeline_type_1": "",
            "job_url_1": "",
            "status_1": "",
            "test_flow_lib_url": "",
            "total_duration": 0  # 新增字段，单位：分钟
        }

        # 根据exec_action_type执行不同的查询
        if exec_action_type in ["init&biz_job_jenkins_composition", "flow_schedule",
                                "seg_init&biz_job_jenkins_composition"]:
            # 执行sql_2
            sql_2 = """
            SELECT  
              distinct  
              d.batch_no, 
              d.suite_code, 
              MAX(CASE WHEN d.step_num = 0 THEN '环境初始化' END) AS pipeline_type_0, 
              MAX(CASE WHEN d.step_num = 0 THEN d.job_url END) AS job_url_0, 
              MAX(CASE WHEN d.step_num = 0 THEN d.status END) AS status_0, 
              MAX(CASE WHEN d.step_num = 1 THEN '编排线' END) AS pipeline_type_1, 
              MAX(CASE WHEN d.step_num = 1 THEN d.job_url END) AS job_url_1, 
              MAX(CASE WHEN d.step_num = 1 THEN d.status END) AS status_1 
            FROM jenkins_mgt_job_composition_detail d 
            WHERE d.batch_no = '{}' 
            GROUP BY d.batch_no, d.suite_code
            """.format(batch_no)

            logger.info(sql_2)
            cursor.execute(sql_2)
            detail_record = cursor.fetchone()

            if detail_record:
                result_dict["pipeline_type_0"] = detail_record[2] if detail_record[2] else ""
                result_dict["job_url_0"] = detail_record[3] if detail_record[3] else ""
                result_dict["status_0"] = detail_record[4] if detail_record[4] else ""
                result_dict["pipeline_type_1"] = detail_record[5] if detail_record[5] else ""
                result_dict["job_url_1"] = detail_record[6] if detail_record[6] else ""
                result_dict["status_1"] = detail_record[7] if detail_record[7] else ""

        elif exec_action_type == "biz_job":
            # 执行sql_3
            sql_3 = """
            SELECT batch_no, JSON_UNQUOTE(JSON_EXTRACT(job_param, '$.suite_code')) as suite_code, 
                   '' as pipeline_type_0, '' as job_url_0, '' as status_0, 
                   '编排线' as pipeline_type_1, job_url as job_url_1, status as status_1 
            FROM jenkins_mgt_biz_job_info_log 
            WHERE batch_no = '{}' 
            ORDER BY id DESC
            """.format(batch_no)

            logger.info(sql_3)
            cursor.execute(sql_3)
            detail_record = cursor.fetchone()

            if detail_record:
                result_dict["pipeline_type_0"] = detail_record[2] if detail_record[2] else ""
                result_dict["job_url_0"] = detail_record[3] if detail_record[3] else ""
                result_dict["status_0"] = detail_record[4] if detail_record[4] else ""
                result_dict["pipeline_type_1"] = detail_record[5] if detail_record[5] else ""
                result_dict["job_url_1"] = detail_record[6] if detail_record[6] else ""
                result_dict["status_1"] = detail_record[7] if detail_record[7] else ""

        elif exec_action_type in ["init", "seg_init"]:
            # 执行sql_4
            sql_4 = """
            SELECT job_build_id AS batch_no, job_http_suite_code AS suite_code, 
                   '环境初始化' AS pipeline_type_0, 
                   job_build_id AS job_url_0,
                   LOWER(job_result) AS status_0,  
                   '' AS pipeline_type_1, '' AS job_url_1, '' AS status_1 
            FROM test_env_mgt_test_suite_init_log 
            WHERE id = '{}'
            """.format(batch_no)

            logger.info(sql_4)
            cursor.execute(sql_4)
            detail_record = cursor.fetchone()

            if detail_record:
                result_dict["pipeline_type_0"] = detail_record[2] if detail_record[2] else ""
                result_dict[
                    "job_url_0"] = "{}/blue/organizations/jenkins/test_suite_init/detail/test_suite_init/{}/pipeline/".format(
                    JENKINS_INFO.get('URL'),
                    detail_record[3]) if detail_record[3] else ""
                result_dict["status_0"] = detail_record[4] if detail_record[4] else ""
                result_dict["pipeline_type_1"] = detail_record[5] if detail_record[5] else ""
                result_dict["job_url_1"] = detail_record[6] if detail_record[6] else ""
                result_dict["status_1"] = detail_record[7] if detail_record[7] else ""

        # 新增：计算总耗时（秒），并转换为分钟
        total_duration_seconds = get_total_duration_seconds(exec_action_type, batch_no)
        logger.info(f"batch_no: {batch_no}, exec_action_type: {exec_action_type}, total_duration_seconds: {total_duration_seconds}")
        # 将秒转换为分钟，保留2位小数
        total_duration_minutes = round(total_duration_seconds / 60, 2) if total_duration_seconds > 0 else 0
        result_dict["total_duration"] = total_duration_minutes

        result_list.append(result_dict)

    # 第三步：根据status_0和status_1进行过滤
    if kwargs.get('status_0') or kwargs.get('status_1'):
        for result in result_list:
            # 检查status_0
            if kwargs.get('status_0') and result["status_0"] != kwargs.get('status_0'):
                continue

            # 检查status_1
            if kwargs.get('status_1') and result["status_1"] != kwargs.get('status_1'):
                continue

            # 如果通过了所有过滤条件，则添加到过滤后的结果列表中
            filtered_result_list.append(result)

        # 更新总数
        total_count = len(filtered_result_list)
        return filtered_result_list, total_count, batch_no_list
    else:
        return result_list, total_count, batch_no_list

