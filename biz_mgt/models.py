import datetime

from django.db import models


class BizBaseType(models.Model):
    create_user = models.CharField(max_length=20, blank=True, null=True)
    create_time = models.DateTimeField(blank=True, null=True)
    update_user = models.CharField(max_length=20, blank=True, null=True)
    update_time = models.DateTimeField(blank=True, null=True)
    stamp = models.BigIntegerField(blank=True, null=True)
    id = models.BigAutoField(primary_key=True)
    biz_type_company = models.CharField(max_length=100, blank=True, null=True)
    biz_type_department = models.CharField(max_length=100, blank=True, null=True)
    biz_type_transaction = models.CharField(max_length=100, blank=True, null=True)
    biz_type_code = models.CharField(unique=True, max_length=100, blank=True, null=True)
    biz_type_name = models.CharField(max_length=100, blank=True, null=True)
    biz_type_is_active = models.IntegerField(blank=True, null=True)
    biz_type_desc = models.CharField(max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'biz_base_type'


class BizBaseInfo(models.Model):
    id = models.BigAutoField(primary_key=True)
    biz_code = models.CharField(verbose_name='业务编码', max_length=100)
    biz_name = models.CharField(verbose_name='业务名称', max_length=100)
    biz_scenario_name = models.CharField(verbose_name='业务「场景」', max_length=100)
    biz_category = models.IntegerField(verbose_name='1:标准业务;2:衍生业务')
    biz_parent_id = models.IntegerField(verbose_name='父业务ID')
    biz_is_active = models.IntegerField(verbose_name='业务是否可用')
    biz_desc = models.CharField(verbose_name='业务说明', max_length=255)
    biz_type = models.OneToOneField(BizBaseType, on_delete=models.CASCADE, to_field="id",
                                    db_column="biz_type",
                                    related_name="biz_type_info")
    create_user = models.CharField(verbose_name='创建人', max_length=20)
    create_time = models.DateTimeField(verbose_name='创建人')
    update_user = models.CharField(verbose_name='更新人', max_length=20)
    update_time = models.DateTimeField(verbose_name='更新时间')
    stamp = models.IntegerField(verbose_name='版本')

    class Meta:
        db_table = 'biz_base_info'
        verbose_name = '业务信息表'


class BizTestIter(models.Model):
    id = models.BigAutoField(primary_key=True)
    biz_test_iter_id = models.CharField(max_length=100, blank=True, null=True)
    biz_code = models.CharField(max_length=100, blank=True, null=True)
    biz_test_iter_br = models.CharField(unique=True, max_length=100, blank=True, null=True)
    br_status = models.CharField(max_length=100, blank=True, null=True)
    br_start_time = models.DateTimeField(blank=True, null=True)
    br_end_time = models.DateTimeField(blank=True, null=True)
    br_from = models.CharField(max_length=100, blank=True, null=True)
    stamp = models.BigIntegerField(blank=True, null=True)
    create_user = models.CharField(max_length=20, blank=True, null=True)
    create_time = models.DateTimeField(blank=True, null=True)
    update_user = models.CharField(max_length=20, blank=True, null=True)
    update_time = models.DateTimeField(blank=True, null=True)
    pause_test_data_recording = models.BooleanField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'biz_test_iter'


class BizTestIterApp(models.Model):
    id = models.BigAutoField(primary_key=True)
    biz_test_iter_id = models.CharField(max_length=100, blank=True, null=True)
    app_module_name = models.CharField(max_length=100, blank=True, null=True)
    archive_br_name = models.CharField(max_length=100, blank=True, null=True)
    stamp = models.BigIntegerField(blank=True, null=True)
    create_user = models.CharField(max_length=20, blank=True, null=True)
    create_time = models.DateTimeField(blank=True, null=True)
    update_user = models.CharField(max_length=20, blank=True, null=True)
    update_time = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'biz_test_iter_app'
        unique_together = (('biz_test_iter_id', 'app_module_name'),)


class BizTestFlowGlobalParam(models.Model):
    id = models.BigAutoField(primary_key=True)
    schedule_config_id = models.BigIntegerField(blank=None, null=None)
    param = models.JSONField()
    param_type = models.CharField(max_length=50)
    create_time = models.DateTimeField()
    create_user = models.CharField(max_length=20)
    update_time = models.DateTimeField(blank=True, null=True)
    update_user = models.CharField(max_length=20, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'biz_test_flow_global_param'
        unique_together = (('schedule_config_id', 'param_type'),)


class BizTestFlowScheduleConfig(models.Model):
    id = models.BigAutoField(primary_key=True)
    jenkins_info_id = models.BigIntegerField(blank=True, null=True)
    biz_iter_branch = models.CharField(max_length=50)
    biz_flow_name = models.CharField(max_length=255)
    biz_code = models.CharField(max_length=255)
    jenkins_url = models.CharField(max_length=1000)
    suite_code = models.CharField(max_length=50)
    enable_schedule = models.IntegerField(blank=True, null=True)
    cron = models.CharField(max_length=50)
    create_time = models.DateTimeField()
    create_user = models.CharField(max_length=20)
    update_time = models.DateTimeField(blank=True, null=True)
    update_user = models.CharField(max_length=20, blank=True, null=True)

    class Meta:
        db_table = 'biz_test_flow_schedule_config'
        unique_together = (('biz_iter_branch', 'biz_flow_name', 'suite_code'),)


class BizTestFlowInfo(models.Model):
    id = models.BigAutoField(primary_key=True)
    biz_code = models.CharField(max_length=255)
    biz_flow_name = models.CharField(max_length=50)
    biz_pipeline_name = models.CharField(max_length=255)
    create_time = models.DateTimeField()
    create_user = models.CharField(max_length=20)
    update_time = models.DateTimeField(blank=True, null=True)
    update_user = models.CharField(max_length=20, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'biz_test_flow_info'
        unique_together = (('biz_code', 'biz_flow_name'),)


class BizTestFlowConfig(models.Model):
    id = models.BigAutoField(primary_key=True)
    flow_id = models.BigIntegerField()
    config_name = models.CharField(max_length=50)
    order_num = models.IntegerField()
    create_time = models.DateTimeField()
    create_user = models.CharField(max_length=20)
    update_time = models.DateTimeField(blank=True, null=True)
    update_user = models.CharField(max_length=20, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'biz_test_flow_config'
        unique_together = (('flow_id', 'config_name'),)


class BizTestFlowActionParam(models.Model):
    id = models.BigAutoField(primary_key=True)
    action_id = models.BigIntegerField()
    param = models.JSONField()
    param_type = models.CharField(max_length=50)
    create_time = models.DateTimeField()
    create_user = models.CharField(max_length=20)
    update_time = models.DateTimeField(blank=True, null=True)
    update_user = models.CharField(max_length=20, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'biz_test_flow_action_param'
        unique_together = (('action_id', 'param_type'),)


class BizTestFlowAction(models.Model):
    id = models.BigAutoField(primary_key=True)
    flow_config_id = models.BigIntegerField()
    action_name = models.CharField(max_length=50)
    action = models.CharField(max_length=50)
    order_num = models.IntegerField()
    create_time = models.DateTimeField()
    create_user = models.CharField(max_length=20)
    update_time = models.DateTimeField(blank=True, null=True)
    update_user = models.CharField(max_length=20, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'biz_test_flow_action'
        unique_together = (('flow_config_id', 'action'),)


# class BizBaseDbBind(models.Model):
#     create_user = models.CharField(max_length=20, blank=True, null=True)
#     create_time = models.DateTimeField(blank=True, null=True)
#     update_user = models.CharField(max_length=20, blank=True, null=True)
#     update_time = models.DateTimeField(blank=True, null=True)
#     stamp = models.BigIntegerField(blank=True, null=True)
#     id = models.BigAutoField(primary_key=True)
#     biz_code = models.CharField(max_length=100, blank=True, null=True)
#     biz_base_db_code = models.CharField(max_length=100, blank=True, null=True)
#     biz_base_db_bind_is_active = models.IntegerField(blank=True, null=True)
#     biz_base_db_bind_desc = models.CharField(max_length=255, blank=True, null=True)
#
#     class Meta:
#         managed = False
#         db_table = 'biz_base_db_bind'
#         unique_together = (('biz_code', 'biz_base_db_code'),)


class BizAppBind(models.Model):
    create_user = models.CharField(max_length=20, blank=True, null=True)
    create_time = models.DateTimeField(blank=True, null=True)
    update_user = models.CharField(max_length=20, blank=True, null=True)
    update_time = models.DateTimeField(blank=True, null=True)
    stamp = models.BigIntegerField(blank=True, null=True)
    id = models.BigAutoField(primary_key=True)
    biz_code = models.CharField(max_length=100, blank=True, null=True)
    app_module_name = models.CharField(max_length=100, blank=True, null=True)
    biz_app_bind_type = models.IntegerField(blank=True, null=True)
    biz_app_bind_is_active = models.IntegerField(blank=True, null=True)
    biz_app_bind_desc = models.CharField(max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'biz_app_bind'
        unique_together = (('biz_code', 'app_module_name'),)


class JenkinsMgtBizJobInfo(models.Model):
    biz_pipeline_name = models.CharField(max_length=255, blank=True, null=True)
    jenkins_info_id = models.IntegerField(blank=True, null=True)
    create_user = models.CharField(max_length=20, blank=True, null=True)
    create_time = models.DateTimeField(blank=True, null=True)
    update_user = models.CharField(max_length=20, blank=True, null=True)
    update_time = models.DateTimeField(blank=True, null=True)
    stamp = models.BigIntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'jenkins_mgt_biz_job_info'
        verbose_name = '业务执行流水线信息表'


class JenkinsMgtBizJobInfoLog(models.Model):
    job_name = models.CharField(max_length=255, blank=True, null=True)
    job_build_id = models.IntegerField(blank=True, null=True)
    job_param = models.JSONField(blank=True, null=True)
    job_url = models.CharField(max_length=512, blank=True, null=True)
    pre_build_id = models.IntegerField(blank=True, null=True)
    job_queue_item = models.IntegerField(blank=True, null=True)
    batch_no = models.CharField(max_length=255, blank=True, null=True)
    status = models.CharField(max_length=20, blank=True, null=True)
    create_user = models.CharField(max_length=50, blank=True, null=True)
    create_time = models.DateTimeField(blank=True, null=True)
    update_user = models.CharField(max_length=50, blank=True, null=True)
    update_time = models.DateTimeField(blank=True, null=True)
    stamp = models.BigIntegerField(blank=True, null=True)

    class Meta:
        db_table = 'jenkins_mgt_biz_job_info_log'
        verbose_name = '业务执行流水线执行日志表'


class JenkinsMgtBizJobRunTestset(models.Model):
    batch_no = models.CharField(verbose_name='批次号', max_length=255)
    testset_id = models.IntegerField(verbose_name='应用名')
    order_no = models.IntegerField(verbose_name='执行顺序')
    execute_id = models.CharField(verbose_name='测试集执行id', max_length=255)
    testset_detail = models.JSONField(verbose_name='测试集详情')
    testset_version_type = models.CharField(verbose_name='测试集版本类型', max_length=10)
    testset_run_status = models.CharField(verbose_name='测试集运行状态', max_length=20)
    create_user = models.CharField(verbose_name='创建人', max_length=50, blank=True, null=True)
    create_time = models.DateTimeField(verbose_name='创建时间', blank=True, null=True)
    update_user = models.CharField(verbose_name='更新人', max_length=50, blank=True, null=True)
    update_time = models.DateTimeField(verbose_name='更新时间', blank=True, null=True)
    stamp = models.BigIntegerField(verbose_name='版本', blank=True, null=True)

    class Meta:
        db_table = 'jenkins_mgt_biz_job_run_testset'
        verbose_name = '业务执行流水线执行测试集信息表'


class BizTestFlowExecHistory(models.Model):
    id = models.BigAutoField(primary_key=True)
    biz_type = models.CharField(max_length=100)
    exec_suite_code = models.CharField(max_length=50)
    exec_action_type = models.CharField(max_length=50)
    exec_detail_param = models.JSONField(blank=True, null=True)
    create_time = models.DateTimeField()
    create_user = models.CharField(max_length=20)
    update_time = models.DateTimeField(blank=True, null=True)
    update_user = models.CharField(max_length=20, blank=True, null=True)

    class Meta:
        db_table = 'biz_test_flow_exec_history'


# class CheckMgtCheckSwitch(models.Model):
#     id = models.BigAutoField(primary_key=True)
#     check_name = models.CharField(max_length=300)
#     switch = models.IntegerField(blank=True, null=True)
#
#     class Meta:
#         db_table = 'check_mgt_check_switch'


class BizTestFlowTestsetDetail(models.Model):
    flow_id = models.BigIntegerField(verbose_name='biz_test_flow_info.id')
    testset_id = models.IntegerField(verbose_name='测试集id')
    app_name = models.CharField(verbose_name='测试集中的应用名', max_length=100)
    script_branch = models.CharField(verbose_name='脚本版本', max_length=100)
    version_type = models.CharField(verbose_name='版本类型', max_length=10)
    create_user = models.CharField(verbose_name='创建人', max_length=50, blank=True, null=True)
    create_time = models.DateTimeField(verbose_name='创建时间', blank=True, null=True)
    update_user = models.CharField(verbose_name='更新人', max_length=50, blank=True, null=True)
    update_time = models.DateTimeField(verbose_name='更新时间', blank=True, null=True)
    stamp = models.BigIntegerField(verbose_name='版本', blank=True, null=True)

    class Meta:
        db_table = 'biz_test_flow_testset_detail'
        verbose_name = '测试集指定执行信息表'

    @staticmethod
    def update_testset_detail(biz_pipeline_name, testset_detail_list, opt_user, version_type):
        if biz_pipeline_name:
            current_time = datetime.datetime.now()
            obj = BizTestFlowInfo.objects.filter(biz_pipeline_name=biz_pipeline_name).first()
            if obj:
                flow_id = obj.id
                for testset_detail in testset_detail_list:
                    testset_id = testset_detail.get('testSetId')
                    app_name = testset_detail.get('appName')
                    script_branch = testset_detail.get('script_branch')
                    obj, created = BizTestFlowTestsetDetail.objects.update_or_create(
                        flow_id=flow_id,
                        testset_id=int(testset_id),
                        app_name=app_name,
                        defaults={
                            'script_branch': script_branch,
                            'version_type': version_type,
                            'update_user': opt_user,
                            'update_time': current_time,
                            # 创建信息将在后续处理
                        }
                    )

                    # 如果是新创建记录，补充设置创建信息
                    if created:
                        obj.create_user = opt_user
                        obj.create_time = current_time
                        obj.save()
