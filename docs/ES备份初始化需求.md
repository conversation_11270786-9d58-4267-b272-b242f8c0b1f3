* 现状与背景

  1. 测试人员能创建es备份，没有地方查看创建的备份及状态等信息。
  2. 测试人员需要将已存在的备份初始化到某个环境，以便于基于已有备份进行二次开发。
* 需求：

  1. 平台提供一个es备份查询列表，支持按条件查询，支持分页显示，页面原型如图1.png。
  2. 提供 详情，验证，初始化 操作按钮
  3. 点击【详情】，跳出备份初始化详情，弹框的【详情】调整jenkins流水线，弹框原型如图2.png所示。
  4. 点击【验证】，对于备份文件，对于【运行中】的备份文件，可以去验证实际的创建结果，如果ok，回显备份文件的状态。验证逻辑1，验证文件是否存在；验证逻辑2，索引数是否能对上，大于初始值，置【失败】。等于初始化值，置【成功】。小于初始值，置【运行中】
  5. 点击【初始化】，弹出初始化的模态框（如图3.png所示），选择业务，分支，环境（复用数据开发里的环境列表接口），点击【确定】后，触发test_es_init流水线（如图4.png所示）。
* 优先级：

  1. 高
* 验收条件：

  1. 可以根据查询条件，能查询到备份信息。
  2. 可以查看详情，查看jenkins执行详情。
  3. 可以验证【运行中】的备份是否完成。
  4. 可以选择某个备份对某个业务分支环境进行初始化。
