export default {
    methods: {
        PrefixInteger(num, n) {
            /*
                补位函数 num传入的数字 n需要的字符长度
             */
            return (Array(n).join(0) + num).slice(-n)
        },
        localDateToUtcDate(obj, date=true)
        {
            /**
             * 本地时间转换为UTC时间的函数
             * obj为js 日期对象 可以通过new Date
             */
            let str = ''
            str += obj.getUTCFullYear() + '-'

            if ((obj.getUTCMonth() + 1) < 10) {
                str += '0' + (obj.getUTCMonth() + 1) + '-'
            }else{
                str += (obj.getUTCMonth() + 1) + '-'
            }

            if (obj.getUTCDate() < 10) {
                str += '0' + obj.getUTCDate()
            } else {
                str += obj.getUTCDate()
            }
            if (date){
                return str +' '+ obj.getUTCHours() +':'+ obj.getUTCMinutes() +':'+ obj.getUTCSeconds()
            }else{
                return str
            }
        },
        UtcDateToLocalDate(obj){
            /**
             * UTC时间转换为本地时间的函数
             * obj为js 日期对象 可以通过new Date
             */
            let str = ''
            str += obj.getFullYear() + '-'
            if ((obj.getMonth() + 1) < 10) {
              str += '0' + (obj.getMonth() + 1) + '-'
            }else{
              str += (obj.getMonth() + 1) + '-'
            }
        
            if (obj.getDate() < 10) {
              str += '0' + obj.getDate()
            } else {
              str += obj.getDate()
            }
            
            return str + ' ' + this.PrefixInteger(obj.getHours(),2) + ':' + this.PrefixInteger(obj.getMinutes(),2) + ':' + this.PrefixInteger(obj.getSeconds(),2)
        }
    }
}