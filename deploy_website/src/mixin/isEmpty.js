export default {
    methods: {
        isEmpty (data) {
            /*
                判断各种类型的变量是否为空
            */
            switch (typeof data) {
                case 'undefined':
                    return true;
                case 'string':
                    if (data.replace(/(^[ \t\n\r]*)|([ \t\n\r]*$)/g, '').length == 0) return true;
                    break;
                case 'boolean':
                    if (!data) return true;
                    break;
                case 'number':
                    if (0 === data || isNaN(data)) return true;
                    break;
                case 'object':
                    if (null === data || data.length === 0) return true;
                    for (let i in data) {
                        return false;
                    }
                    return true;
            }
            return false;
        },
    }
}