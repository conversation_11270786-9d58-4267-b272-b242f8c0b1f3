export default {
    home: '首页',
    components: '组件',
    publish_stat_page: '上线状态',
    prod_node_mgmt_page: '产线节点管理',
    prod_group_mgmt_page: '分组管理',
    ops_publish_page: '(待下线功能)',
    count_to_page: '数字渐变',
    tables_page: '多功能表格',
    split_pane_page: '分割窗口',
    markdown_page: 'Markdown编辑器',
    editor_page: '富文本编辑器',
    icons_page: '自定义图标',
    img_cropper_page: '图片编辑器',
    update: '上传数据',
    join_page: 'QQ群',
    doc: '文档',
    update_table_page: '上传CSV文件',
    update_paste_page: '粘贴表格数据',
    multilevel: '多级菜单',
    directive_page: '指令',
    release_sql_executed: '产线SQL',
    server_batch_reset: '服务端批量重启',
    server_batch_edit: '服务端批量重启编辑',
    simulate_sql_executed: '仿真SQL',
    level_1: 'Level-1',
    level_2: 'Level-2',
    level_2_1: 'Level-2-1',
    level_2_3: 'Level-2-3',
    level_2_2: 'Level-2-2',
    level_2_2_1: 'Level-2-2-1',
    level_2_2_2: 'Level-2-2-2',
    excel: 'Excel',
    'upload-excel': '上传excel',
    'export-excel': '导出excel',
    tools_methods_page: '工具函数',
    drag_list_page: '拖拽列表',
    i18n_page: '多语言',
    modalTitle: '模态框题目',
    content: '这是模态框内容',
    buttonText: '显示模态框',
    'i18n-tip': '注：仅此页做了多语言，其他页面没有在多语言包中添加语言内容',
    error_store_page: '错误收集',
    error_logger_page: '错误日志',
    query: '带参路由',
    params: '动态路由',
    cropper_page: '图片裁剪',
    message_page: '消息中心',
    tree_table_page: '树状表格',
    org_tree_page: '组织结构树',
    drag_drawer_page: '可拖动抽屉',
    self_service: '自助发布',
    interative_publish: '日常迭代发布',
    interative_publish_guide: '日常迭代发布指南',
    data_statistics: '数据统计',
    base_management: '基础信息管理',
    ops_service: '运维专属服务',
    publish_history: '发布历史',
    h5_publish_history: 'h5上线历史',
    h5_eslint_info: 'h5编译信息',
    create_branch: 'git迭代申请',
    git_iteration_page: 'git迭代发布',
    git_iteration_list: 'git迭代列表',
    script_management: '脚本管理',
    docker_service: '容器化服务',
    k8s_manage: '容器管理',
    docker_env_init: '环境初始化',
    speedy_sql: '快速SQL通道',
    docker_env_const: '迭代支持',
    docker_env_info: '环境信息',
    docker_env_auth: '权限管理',
    test_env: '测试环境管理',
    env_application: '测试环境申请',
    env_process: '处理进度查询',
    rsync_gitlab: '同步gitlab信息',
    env_current_info: '测试环境使用信息',
    test_env_guide: '测试环境管理使用指南',
    git_self_service: 'GIT自助服务',
    authorization: '权限管理',
    git_help: 'git接入指南',
    git_help_detail: '问题详情',
    git_guide: 'git上线流程指南',
    git_jinji_guide: 'git紧急上线流程指南',
    team_initial: '团队接入',
    ops_publish_guide: '应用发布操作指南',
    app_publish_history: '今日发布',
    java_compile_info: 'java编译统计',
    h5_compile_info: 'h5编译统计',
    h5_package_info: 'h5打包统计',
    env_pipeline_detail: '环境操作记录详情',
    env_pipeline: '环境操作记录（即将下线）',
    env_information_new: '环境信息',
    env_application_new: '环境申请（Jenkins）',
    env_application_test: '环境申请（即将下线）',
    env_docker: 'docker信息',
    h5_code_statistics: 'h5代码扫描统计',
    config_help: '配置指南',
    pipeline_service: '研发迭代管理',
    pipeline_page: 'Java服务端',
    iter_publish_plan_detail: '发布计划详情',
    iter_publish_plan_config: '应用发布计划配置',
    iter_quality_report: '迭代质量报告',
    mobile_pipeline: 'H5/APP',
    h5_mobile_pipeline: 'H5静态',
    app_mobile_pipeline: 'App客户端',
    python_pipeline: 'Python工程',
    h5_pipeline: '老移动端流水线（待废弃）',
    iter_apply: '分支申请',
    rsync_gitlab_info: '同步gitlab信息',
    iter_list: '迭代列表',
    app_mgt: '应用管理',
    env_mgt: '环境管理',
    node_mgt: '节点管理',
    app_regist: '新增应用',
    app_apply: '制品库列表',
    node_apply: '节点申请管理',
    node_recycle: '节点回收管理',
    env_init: '环境初始化',
    my_pipeline: '我的流水线',
    srv_pipeline_guide: '服务端流水线指南', // 「菜单」名称 zt@2021-11-09
    app_pipeline_guide: '移动端流水线指南', // 「菜单」名称 zt@2021-11-09
    publish_cmd: '运维salt维护',
    publish_service: '产线发布',
    node_group_mgt: '分组管理',
    goto_zeus: '跳转宙斯', // 「菜单」名称 zt@2021-12-20
    prod_rmq_apply: '产线组RMQ申请',
    app_mgt_interface: '应用接口管理',
    biz_iter_mgt: '测试迭代管理',
    biz_iter_apply: '业务分支申请',
    biz_mgt_app_bind: '业务关联应用管理',
    release_group: '组发布策略管理',
    agent_bing: 'agent绑定管理',
    biz_auto_test_define: '业务自动化编排',
    // test_data_dev: '业务测试数据开发',
    test_data_dev_new: '业务测试数据开发',
    dupReset: 'dump还原',
    // test_data_exec: '业务迭代自动化',
    test_data_exec_new: '业务迭代自动化',
    test_data_dump: 'dump还原',
    auth_apply_service: '权限申请管理',
    svn_account_apply: 'svn账号开通申请',
    personal: '个人面板',
    quality: '我的质量报告',
    effect: '我的研效',
    clipboard: '我的待办',
    auto_exec_record: '自动化执行记录'
}
