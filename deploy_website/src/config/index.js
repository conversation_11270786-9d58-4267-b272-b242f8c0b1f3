export default {
  /**
   * @description 配置显示在浏览器标签的title
   */
  title: 'DevOps平台',
  /**
   * @description token在Cookie中存储的天数，默认1天
   */
  cookieExpires: 1,
  /**
   * @description 是否使用国际化，默认为false
   *              如果不使用，则需要在路由中给需要在菜单中展示的路由设置meta: {title: 'xxx'}
   *              用来在菜单中显示文字
   */
  useI18n: true,
  /**
   * @description api请求基础路径
   */
  baseUrl: {
    // 开发时，老后端默认使用「测试环境」 zt@2021-11-11
    // dev: website_url,
    dev: spider_url,
    test: spider_url,
    prod: 'http://appdeploy.intelnal.howbuy.com/',

    // 开发时，spider配置使用「 appdeploy、shuai、wei、wm 」 zt@2021-11-11
    /*
    * 办公区NG协助解决跨域问题（必须）
    * 默认：http://appdeploy.ztst.info/
    * 刘帅：http://shuai.ztst.info/
    * 刘微：http://wei.ztst.info/
    * 伟敏：http://wm.ztst.info/
    * */
    spider_dev: spider_url,
    spider_test: '/',
    spider_prod: '/'
  },
  /**
   * @description 默认打开的首页的路由name值，默认为home
   */
  homeName: 'home',
  /**
   * @description 需要加载的插件
   */
  plugin: {
    'error-store': {
      showInHeader: true, // 设为false后不会在顶部显示错误日志徽标
      developmentOff: true // 设为true后在开发环境不会收集错误信息，方便开发中排查错误
    }
  },
  socket_host: '***************:9011'

}
