import Mock from 'mockjs'
import { doCustomTimes } from '@/libs/util'
const Random = Mock.Random


export const getUatPublishData = req => {
  let businessID = JSON.parse(req.body)['businessID']
  let uatPublishData = {}

  if (businessID === 'TP_JinJi_2019-01-11-1') {
    uatPublishData['tms-one'] = '*******'
    uatPublishData['tms-two'] = '*******'
  } else {
    let appList = ['pay-online', 'bankServer', 'fin-console-web', 'fds-batch-server', 'cgi', 'acc-center'];
    doCustomTimes(3, () => {
      let appName = Random.pick(appList)
      uatPublishData[appName] = Random.ip()
    });
  }

  return uatPublishData
}

export const getProdPublishData = req => {
  let businessID = JSON.parse(req.body)['businessID']
  let prodPublishData = []
  let appList = ['pay-online', 'bankServer', 'fin-console-web', 'fds-batch-server', 'cgi', 'acc-center'];
  let group_lst = ['A', 'B', 'C'];

  if (businessID === 'TP_JinJi_2019-01-11-1') {
    let node = []
    let group = new Set()
    doCustomTimes(2, () => {
      node.push(Random.ip())
      group.add(Random.pick(group_lst))
    });
    group = Array.from(group)
    prodPublishData.push({'app_name': 'tms-one', 'node': node, 'group': group})
  } else {
    let appNames = []
    doCustomTimes(2, () => {
      appNames.push(Random.pick(appList))
    });
    for (let i = 0; i < appNames.length; i++ ) {
      let node = []
      let group = new Set()
      doCustomTimes(2, () => {
        node.push(Random.ip())
        group.add(Random.pick(group_lst))
      });
      group = Array.from(group)
      prodPublishData.push({'app_name': appNames[i], 'node': node, 'group': group})
    }
  }

  return prodPublishData
}

export const getProdHistoryData = req => {
  let businessID = JSON.parse(req.body)['businessID']
  let appName = JSON.parse(req.body)['appName']
  let prodHistoryData = []

  let users = ['weijie.zhou', 'xuqing.dai', 'weiwei.chen', 'shuai.liu', 'jiemin.wang']
  let op_type = ['部署', '重启']
  let op_detail = ['Stopping tomcat......Done Starting tomcat......Done', 'error', 
      'Stopping tomcat......Done [ INFO ]: Backup The cgi ...... OK! [ INFO ]: Update The cgi ......  OK! drwxr-xr-x 6 <USER> <GROUP> 4096 7xe6x9cx88  30 16:11 /data/app/tomcat-ehowbuy-cgi/cgi Starting tomcat......Done']

  doCustomTimes(5, () => {
    prodHistoryData.push({
      detail: Random.pick(op_detail),
      type: Random.pick(op_type),
      ip: Random.ip(),
      operator: Random.pick(users),
      operateTime: Random.datetime()
    })
  });

  return prodHistoryData
}