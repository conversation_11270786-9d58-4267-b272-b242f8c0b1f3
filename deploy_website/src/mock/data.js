import Mock from 'mockjs'
import { doCustomTimes } from '@/libs/util'
import orgData from './data/org-data'
const Random = Mock.Random

export const getTableData = req => {
  let tableData = []
  doCustomTimes(5, () => {
    tableData.push(Mock.mock({
      appname: '@name',
      stage: '@email',
      createTime: '@date'
    }))
  })
  return tableData
}

export const getIterativeSyslist = req => {
  let appList = ['fin-console-web', 'fds-console', 'fds-batch-server', 'cgi'];
  let statList = ['测试中', '已推送制品库','计划上线','仿真通过','已上线'];
  let git_version =['111','222','','333']
  let tableData = []
  doCustomTimes(5, () => {
    tableData.push(Mock.mock({
      sys_name: Random.pick(appList),
      sys_status: Random.pick(statList),
      git_version:Random.pick(git_version)
    }))
  })
  return tableData
}

export const getH5eslintInfo = req => {
  let brList = ['br1', 'br2', 'br3', 'br4'];
  let keyList = ['HB_URL', 'no-var','undefined','quotes'];
  let errorInfo =['类型错误类型错误类型错误类型错误类型错误类型错误','未定义报错未定义报错未定义报错未定义报错未定义报错',
    '错误引用错误引用错误引用错误引用','url地址错误url地址错误url地址错误url地址错误url地址错误'];
  let filePath=['D:\\scm\\script\\front\\baseline.js','D:\\scm\\script\\mobile\\h5.py','D:\\scm\\script\\salt.vue','D:\\scm\\script\\front\\common.sql']
  let tableData = []
  doCustomTimes(5, () => {
    tableData.push(Mock.mock({
      branch_name: Random.pick(brList),
      rule: Random.pick(keyList),
      file_path:Random.pick(filePath),
      error_info:Random.pick(errorInfo),
      error_time:Mock.Random.date(),
    }))
  })
  return tableData
}


export const getDragList = req => {
  let dragList = []
  doCustomTimes(5, () => {
    dragList.push(Mock.mock({
      name: Random.csentence(10, 13),
      id: Random.increment(10)
    }))
  })
  return dragList
}

export const getInstalledList = req => {
  let dragList = []
  doCustomTimes(5, () => {
    dragList.push(Mock.mock({
      name: Random.city(),
      id: Random.increment(10)
    }))
  })
  return dragList
}

export const uploadImage = req => {
  return Promise.resolve()
}

export const getOrgData = req => {
  return orgData
}

export const getPublishStatData= req => {
  let bType = ['TP_JinJi_', 'TMS_JinJi_'];
  let appList = ['fin-console-web', 'fds-console', 'fds-batch-server', 'cgi'];
  let statList = ['已申请未处理', '未申请'];
  let publishStatList = [];
  doCustomTimes(35, () => {
    publishStatList.push(Mock.mock({
      pk: Random.increment(),
      business_name: Random.pick(bType)+Random.date(),
      app_name: Random.pick(appList),
      cur_stat: Random.pick(statList),
    }))
  });
  return publishStatList;
}

export const getProdNodeData = req => {
  let appList = ['pay-online', 'bankServer', 'fin-console-web', 'fds-batch-server', 'cgi', 'acc-center'];
  let groupList = ['A', 'B', 'C', 'D', ''];
  let prodNodeList = [];
  doCustomTimes(35, () => {
    prodNodeList.push(Mock.mock({
      pk: Random.increment(),
      ip: Random.ip(),
      app_name: Random.pick(appList),
      cur_group: Random.pick(groupList),
      // app_groups: ['A', 'B', 'B', ''],
      // exist_groups: ['A', 'B'],
      // new_group: 'C',
    }))
  });

  let tmp_app_groups_dict = {};
  for (let i = 0; i < prodNodeList.length; i++ ) {
    if (tmp_app_groups_dict.hasOwnProperty(prodNodeList[i].app_name)) {
      tmp_app_groups_dict[prodNodeList[i].app_name].push(prodNodeList[i].cur_group)
    } else {
      tmp_app_groups_dict[prodNodeList[i].app_name] = [prodNodeList[i].cur_group];
    }
  }

  let new_groups = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H'];
  let tmp_exist_groups_dict = {};
  let tmp_new_group_dict = {};
  for ( let key in tmp_app_groups_dict) {
    for (let j = 0; j < new_groups.length; j++) {
      if ( tmp_app_groups_dict[key].indexOf(new_groups[j]) === -1 ) {
        tmp_new_group_dict[key] = new_groups[j];
        break
      }
    }

    if ( ! tmp_exist_groups_dict.hasOwnProperty(key) ) {
      let tmp_set = new Set(tmp_app_groups_dict[key]);
      tmp_set.delete('');
      tmp_exist_groups_dict[key] = Array.from(tmp_set)
    }
  }

  for (let i = 0; i < prodNodeList.length; i++ ) {
    prodNodeList[i]['app_groups'] = tmp_app_groups_dict[prodNodeList[i].app_name];
    prodNodeList[i]['exist_groups'] = tmp_exist_groups_dict[prodNodeList[i].app_name];
    prodNodeList[i]['new_group'] = tmp_new_group_dict[prodNodeList[i].app_name];
  }

  return prodNodeList;
}

// 获取发布历史数据
export const getPublishHistoryData = req => {
  let PublishHistoryList = []
  let num = 1
  let ftList = ['TMS', 'TP'];
  let appList = ['fin-console-web', 'fds-console', 'fds-batch-server', 'cgi']
  let bollList = [0, 1]
  let sqlList = ['svn://xxxx.sql', '无']
  let confModifyList = ['无', 'dubbo-cluster.xml add']
  doCustomTimes(10, () => {
    let bussinessID = Random.pick(ftList) + '_' + Random.increment(1) + '.0.' + Random.increment(1)
    PublishHistoryList.push(Mock.mock({
        panelId: num.toString(),
        bussinessID: bussinessID,
        isDone: Random.pick(bollList).toString(),
        appDetails: {
          appConf: [
            {
              appName: Random.pick(appList),
              confModify: Random.pick(confModifyList)
            },
          ],
          sql: Random.pick(sqlList),
          publishDate: Random.date(),
          description: '迭代' + bussinessID + Mock.mock('@cword(10)'),
        }
      }))
    num = num + 1
  })
  return PublishHistoryList
}

export const h5publishHistory = req => {
  let bType = ['TP_JinJi_', 'TMS_JinJi_'];
  let appList = ['fund', 'piggy'];
  let isFullList = ['0', '1'];
  let publishStatList = [];
  doCustomTimes(35, () => {
    let app = Random.pick(appList)
    let isFull = Random.pick(isFullList)
    publishStatList.push(Mock.mock({
      fullPackVer: app + '_' + Mock.mock({"number|1-10": 100}).number,
      addtionVer: Random.pick(bType)+Random.date(),
      downloadURl: 'http://' + app,
      packSize: Random.zip() + 'MB',
      packPercent: Random.zip() + '%_' + isFull,
      publishDate: Mock.mock('@datetime("yyyy-M-d H:m:s")'),
      appType: app,
      isFull: isFull
    }))
  });
  return publishStatList;
}

export const execHistory = req => {
  let businessID = JSON.parse(req.body)['businessID']
  let compileHistoryMap = {}
  let compileHistoryList = []
  let statusList = ['success', 'warning', 'error']
  let contentList = []
  let compileResultList = ['成功', '错误日志']
  let userList = ['xuqing', 'jiemin', 'hang', 'weiwei', 'weijie']
  doCustomTimes(10, () => {
    let id = Random.increment(1)
    let info = Mock.mock({
      "title": '# ' + id + ' ' + Random.date() + '_' + Mock.mock('@time'),
      "status": Random.pick(statusList),
      "user": Random.pick(userList),
      "content": [],
    })
    let contentList = []
    let num = Mock.mock({
      "number|5-20": 10
    }).number
    doCustomTimes(num, () => {
      contentList.push(Mock.mock({
        "app": Random.word(4, 10),
        "execStatus": Random.pick(compileResultList),
        "log": 'test'
      }))
    })
    info['content'] = contentList
    compileHistoryMap[id] = info
  })
  let compileHistoryResult = {"TP_JinJi_2019-01-11-3": compileHistoryMap, "kubernetes": compileHistoryMap, "容器管理": compileHistoryMap}
  return compileHistoryResult[businessID]
}

export const  getDockerImg = (team) => {
  let data = [
    {
      title: 'all',
      expand: true,
      children: [
        {
          title: 'cgi'
        },
        {
          title: 'simu-cgi'
        },
        {
          title: 'howbuy-trade-wap'
        },
        {
          title: 'trade'
        },
        {
          title: 'tstatic'
        },
        {
          title: 'howbuy-interlayer-console'
        },
        {
          title: 'tms-counter-console'
        }
        ,
        {
          title: 'high-order-search-remote'
        }
        ,
        {
          title: 'high-order-trade-remote'
        }
        ,
        {
          title: 'high-batch-center-remote'
        }
        ,
        {
          title: 'batch-center-server-remote'
        },
        {
          title: 'howbuy-interlayer-product-remote'
        },
        {
          title: 'fin-center-remote'
        },
        {
          title: 'order-center-remote'
        },
        {
          title: 'order-center-search-remote'
        },
        {
          title: 'robot-order-center-remote'
        },
        {
          title: 'regular-order-center-remote'
        },
        {
          title: 'regular-batch-center-remote'
        },
        {
          title: 'batch-center-remote'
        },
        {
          title: 'message-center-remote'
        },
        {
          title: 'howbuy-regularfund-remote'
        },
        {
          title: 'howbuy-member-remote'
        },
        {
          title: 'redis'
        },
        {
          title: 'activemq'
        },
        {
          title: 'tomcat-quartz'
        },
        {
          title: 'howbuy-quartz'
        },
        {
          title: 'dubbo-admin'
        },
        {
          title: 'zookeeper'
        }
      ]
    }
  ]
  return JSON.stringify(data)
}
