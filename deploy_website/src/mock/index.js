import Mock from 'mockjs'
import { login, logout, getUserInfo } from './login'
import {
  getTableData,
  getDragList,
  uploadImage,
  getOrgData,
  getPublishStatData,
  // getPublishHistoryData,
  getProdNodeData,
  h5publishHistory,
  // execHistory,
  getInstalledList,
  getDockerImg,
  getIterativeSyslist,
  getH5eslintInfo
} from './data'
import { getMessageInit, getContentByMsgId, hasRead, removeReaded, restoreTrash, messageCount } from './user'
import { getBranchData } from './createBranch'
import { addSys } from './addSys'
import { getUatPublishData, getProdPublishData, getProdHistoryData } from './publish'

// 配置Ajax请求延时，可用来测试网络延迟大时项目中一些效果
Mock.setup({
  timeout: 1000
})

// 登录相关和获取用户信息
//Mock.mock(/\/login/, login)
// Mock.mock(/\/get_info/, getUserInfo)
//Mock.mock(/\/logout/, logout)
Mock.mock(/\/get_table_data/, getTableData)
Mock.mock(/\/get_drag_list/, getDragList)
Mock.mock(/\/get_Installed_list/, getInstalledList)
Mock.mock(/\/save_error_logger/, 'success')
Mock.mock(/\/image\/upload/, uploadImage)
Mock.mock(/\/message\/init/, getMessageInit)
Mock.mock(/\/message\/content/, getContentByMsgId)
Mock.mock(/\/message\/has_read/, hasRead)
Mock.mock(/\/message\/remove_readed/, removeReaded)
Mock.mock(/\/message\/restore/, restoreTrash)
Mock.mock(/\/message\/count/, messageCount)
Mock.mock(/\/get_org_data/, getOrgData)
// Mock.mock(/\/get_publish_stat_data/, getPublishStatData)
// Mock.mock(/\/get_publish_history_data/, getPublishHistoryData)
// Mock.mock(/\/get_prod_node_data/, getProdNodeData)
Mock.mock(/\/h5publishHistory/, h5publishHistory)
Mock.mock(/\/create_branch/, getBranchData)
// Mock.mock(/\/execHistory/, execHistory)
// Mock.mock(/\/get_uat_publish_data/, getUatPublishData)
// Mock.mock(/\/get_prod_publish_data/, getProdPublishData)
// Mock.mock(/\/get_prod_history_data/, getProdHistoryData)
Mock.mock(/\/get_docker_img/, getDockerImg)
Mock.mock(/\/add_sys/, addSys)
Mock.mock(/\/get_iterative_syslist/, getIterativeSyslist)
Mock.mock(/\/get_h5_eslint_info/, getH5eslintInfo)

//export default Mock
