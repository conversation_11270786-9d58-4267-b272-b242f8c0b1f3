/*
 * @Author: chao.bao <EMAIL>
 * @Date: 2024-09-27 17:59:27
 * @LastEditors: chao.bao <EMAIL>
 * @LastEditTime: 2025-08-11 14:23:17
 * @FilePath: /website_web/deploy_website/src/spider-store/index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import Vue from 'vue'
import Vuex from 'vuex'

Vue.use(Vuex)

export default new Vuex.Store({
    state: {
        iterationID: '',
        uname: '',
        tag_name: '',
        code_type: 'branch',
        show_tag: false,
        h5_app_names: [],
        h5_group: '',
        h5_branch_version: '',
        //发布类型
        publish_type: '',
        //H5流水线中，用于分支页和测试发布之间 应用 的通信
        app_array: [],
        static_array: [],
        remote_array: [],
        dist_array: [],
        check_count: 0,
        deadline: '',
        suite_code_list: [],
        // python工程构件页面
        python_iterationID: '',
        python_code_type: 'branch',
        python_branch_version: '',
        python_group: ''
    },
    mutations: {
        setTagName(state, tag_name) {
            state.tag_name = tag_name
        },
        setCodeType(state, code_type) {
            state.code_type = code_type
        },
        setShowTag(state, show_tag) {
            state.show_tag = show_tag
        },
        setIterationID(state, iterationID) {
            state.iterationID = iterationID
        },
        setLoginUser(state, uname) {
            state.uname = uname
        },
        setH5AppNames(state, h5_app_names) {
            state.h5_app_names = h5_app_names
        },
        setH5Group(state, h5_group) {
            state.h5_group = h5_group
        },
        setH5BranchVersion(state, h5_branch_version) {
            state.h5_branch_version = h5_branch_version
        },
        setPublishType(state, publish_type) {
            state.publish_type = publish_type
        },
        setAppArray(state, app_array) {
            state.app_array = app_array
        },
        setStaticArray(state, static_array) {
            state.static_array = static_array
        },
        setRemoteArray(state, remote_array) {
            state.remote_array = remote_array
        },
        setDistArray(state, dist_array) {
            state.dist_array = dist_array
        },
        setCheckCount(state, check_count) {
            state.check_count = check_count
        },
        setDeadLine(state, deadline) {
            state.deadline = deadline
        },
        setSuiteCodeList(state, suite_code_list) {
            state.suite_code_list = suite_code_list
        },
        // -----------------python工程构件页面
        setPyIterationID(state, python_iterationID) {
            state.python_iterationID = python_iterationID
        },
        setPyCodeType(state, python_code_type) {
            state.python_code_type = python_code_type
        },
        setPyGroup(state, python_group) {
            state.python_group = python_group
        },
        setPyBranchVersion(state, python_branch_version) {
            state.python_branch_version = python_branch_version
        }
    },
    actions: {
        //
    },
    modules: {
        // user,
        //app
    }
})
