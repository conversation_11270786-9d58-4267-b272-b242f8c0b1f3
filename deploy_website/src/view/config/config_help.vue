<template>
  <Card>
    <Row>
        <Col span="11">
            <Card dis-hover>
                <p slot="title">
                  配置文件流程
                </p>
                <ul>
                    <li v-for="item in config_prepare">
                        <a target="_blank" @click="iterationPage(item)">{{ item.name }}</a>
                    </li>
                </ul>
              </Card>
        </Col>
    </Row>
  </Card>
</template>

<script>
    export default {
        name: "config_help",
        data () {
            return {
                config_prepare: [
                    {
                        name: 'ccms配置',
                        content: "",
                        imgurl:"/config/ccms.png"
                    },

                    {
                        name: '配置魔方配置',
                        content:"",
                      imgurl:"/config/mofang.png"
                    },
                    {
                        name: '文件配置',
                        content:"",
                      imgurl:"/config/file.png"
                    }

                ],
            }
        },
        methods: {
           iterationPage (par){

                this.$router.push({name:"git_help_detail",
                  params:{"name":par.name,"content":par.content,"imgurl":par.imgurl}})

          },
        }


    }
</script>

<style scoped>

</style>
