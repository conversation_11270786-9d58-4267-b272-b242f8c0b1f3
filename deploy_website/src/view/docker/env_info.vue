<template>
  <Card shadow>

    <ProcessHeader :businessID="businessID" ref="indexImportOrder" v-on:buildEdit="changeBuildEdit" :webSocket="webSocket"></ProcessHeader>

    <Row :gutter="24" style="margin-top: 15px">
      <Col span="24">
      <Collapse simple v-model="collapse">
        <Panel name="1">
          <Tag type="border" color="default">类型：{{ actionType }}</Tag>
          <Tag type="border" color="default">环境：{{ envChoice }}</Tag>
          <Tag type="border" color="default">时间：{{ dateValue }}</Tag>
          <p slot="content" style="margin-top: 10px">
          <Table :columns="execution" :data="execution_data"></Table>
          </p>
        </Panel>
      </Collapse>
      </Col>
    </Row>

    <Drawer
      title=""
      v-model="buildEdit"
      width="430px"
      :mask-closable="false"
      :styles="styles">
      <Form :model="formData">
        <Row :gutter="32">
          <Col span="24">
          <FormItem label="容器管理任务配置栏" label-position="top">
            <Row style="margin-top: 50px">
              <Col span="12">
              类型：
              <Select class="marginRight" v-model="showActionTypeSelect" style="width:140px" placeholder="选择操作类型">
                <Option v-for="item in showActionType" :value="item.value" :key="item.value">{{ item.label }}</Option>
              </Select>
              </Col>
              <Col span="12">
              环境：
              <Select type="info" v-model="showEnvSelect" style="width:100px" placeholder="选择环境">
                <Option v-for="item in showEnv" :value="item.value" :key="item.value">{{ item.label }}</Option>
              </Select>
              </Col>
            </Row>
            <Row style="margin-top: 10px">
              日期：
              <DatePicker v-model="date_value" format="yyyy-MM-dd" type="date" placeholder="选择日期..." style="width: 300px"></DatePicker>
            </Row>
            <Row style="margin-top: 10px">
              时间：
              <TimePicker type="time" v-model="time_value" placeholder="选择时间..." style="width: 300px"></TimePicker>
            </Row>
            <Row style="margin-top: 10px">
              选择镜像名:
            </Row>
            <Col span="24">
            <Tree :data="appTree" show-checkbox ref="buildList"></Tree>
            </Col>
          </FormItem>
          </Col>
        </Row>
      </Form>
      <div class="demo-drawer-footer">
        <Button style="margin-right: 8px" @click="buildEdit = false">取消</Button>
        <Button type="primary" @click="savebuildList">保存</Button>
      </div>
    </Drawer>
  </Card>
</template>

<script>
  import { compileHistory, getDockerImg } from '@/api/data'
  import { initWebSocket, closeWebSocket } from '@/libs/web-socket'
  import ProcessHeader from '_c/process-header'
  export default {
    name: 'k8s_manage',
    components: {
      ProcessHeader
    },
    data () {
      return {
        businessID: '环境信息查询',
        process: 0,
        buildEdit: false,
        collapse: '1',
        webSocket: '/env_info',
        date_value: '',
        time_value:'',
        actionType: '待定',
        envChoice: '待定',
        dateValue: '待定',
        wb_data: [],
        formData: {
          name: '',
        },
        appTree: [],
        styles: {
          height: 'calc(100% - 55px)',
          overflow: 'auto',
          paddingBottom: '53px',
          position: 'static'
        },
        execution: [
          {
            title: '应用名',
            key: 'app'
          },
          {
            title: '版本获取',
            key: 'verStatus'
          },
          {
            title: '镜像制作',
            key: 'imgStatus'
          },
          {
            title: '集群部署',
            key: 'clusterDeploy'
          }
        ],
        execution_data: [],
        showEnvSelect: '',
        showActionType: [
          {
            value: 'RESTART',
            label: '重启'
          },
          {
            value: 'UPDATE-CONFIG',
            label: '更新配置'
          },
          {
            value: 'CHANG-DATE',
            label: '修改环境时间'
          },
          {
            value: 'UPDATE-URL',
            label: '更新集群访问路径'
          },
          {
            value: 'CRE-DEPLOYMENT',
            label: '创建发布对象'
          },
          {
            value: 'DEL-DEPLOYMENT',
            label: '删除发布对象'
          }
        ],
        showEnv: [
          {
            value: 'tms',
            label: 'tms'
          },
          {
            value: 'tms02',
            label: 'tms02'
          },
          {
            value: 'tms03',
            label: 'tms03'
          }
        ],
        showActionTypeSelect: '',
      }
    },
    methods: {
      // 从子组件ProcessHeader获取配置栏打开状态
      changeBuildEdit(val) {
        this.buildEdit = val
      },
      //
      formatDate (date, fmt) {
        let o = {
          'M+': date.getMonth() + 1, // 月份
          'd+': date.getDate(), // 日
          'h+': date.getHours(), // 小时
          'm+': date.getMinutes(), // 分
          's+': date.getSeconds(), // 秒
          'S': date.getMilliseconds() // 毫秒
        }
        if (/(y+)/.test(fmt)) {
          fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length))
        }
        for (var k in o) {
          if (new RegExp('(' + k + ')').test(fmt)) {
            fmt = fmt.replace(RegExp.$1, (RegExp.$1.length === 1) ? (o[k]) : (('00' + o[k]).substr(('' + o[k]).length)))
          }
        }
        return fmt
      },
      // 保存编译列表
      savebuildList () {
        let choicesAll = this.$refs.buildList.getCheckedNodes()
        let choiceData = []
        choicesAll.forEach((item) => {
          if ('children' in item) {
            //
          } else {
            let data = {
              app: item.title,
              verStatus: '已获取最新主干',
              imgStatus: '正在构建镜像',
              clusterDeploy: '等待部署',
            }
            choiceData.push(data)
          }
        })
        localStorage.setItem('buildList', JSON.stringify(choiceData));
        this.execution_data = JSON.parse(localStorage.getItem('buildList'));
        this.$Message.info('保存成功')
        this.buildEdit = false
        this.actionType = this.showActionTypeSelect
        this.envChoice = this.showEnvSelect
        let date = this.formatDate(this.date_value,'yyyy-MM-dd')
        let time = this.time_value
        this.dateValue = date + ' ' + time
      },
    },
    mounted() {
      getDockerImg('TMS').then(res => {
        this.appTree = JSON.parse(res.data)
      })
      // 建立websocekt链接
      // initWebSocket(this, '/java_compile?businessID=' + this.businessID)
      // initWebSocket(this, '/java_compile?businessID=TP_JinJi_2019-01-11-3')
    }
  }
</script>

<style lang="less">

</style>
