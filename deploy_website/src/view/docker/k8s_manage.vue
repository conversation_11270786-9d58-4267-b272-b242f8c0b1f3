<template>

    <Card shadow>

      <ProcessHeader :businessID="businessID" :businessID_CN="businessID_CN" ref="indexImportOrder"
                     v-on:buildEdit="changeBuildEdit" v-on:toRunScript="toRunScript" :process="process"
                     :current_status="current_status">
      </ProcessHeader>

      <Row :gutter="24" style="margin-top: 15px; width: 1000px">
        <Col span="24">
          <Collapse simple v-model="collapse">
            <Panel name="1">
                <Tag type="border" color="default">类型：{{ actionType }}</Tag>
                <Tag type="border" color="default">环境：{{ envChoice }}</Tag>
                <Tag type="border" color="default">时间：{{ dateValue }}</Tag>
              <p slot="content" style="margin-top: 10px">
                <Table :columns="execution" :data="execution_data"></Table>
              </p>
            </Panel>
          </Collapse>
        </Col>
      </Row>

      <Drawer
        title=""
        v-model="buildEdit"
        width="430px"
        :mask-closable="false"
        :styles="styles">
        <Form :model="formData">
          <Row :gutter="32">
            <Col span="24">
              <FormItem label="容器管理任务配置栏" label-position="top">
                <Row style="margin-top: 50px">
                  <Col span="12">
                    类型：
                    <Select class="marginRight" v-model="showActionTypeSelect" @on-change="showActionTypeSelectChange" style="width:140px" placeholder="选择操作类型">
                      <Option v-for="item in showActionType" :value="item.value" :key="item.value">{{ item.label }}</Option>
                    </Select>
                  </Col>
                  <Col span="12">
                    环境：
                    <Select type="info" v-model="showEnvSelect" style="width:100px" @on-change="showEnvSelectChange" placeholder="选择环境">
                      <Option v-for="item in showEnv" :value="item.value" :key="item.value">{{ item.label }}</Option>
                    </Select>
                  </Col>
                </Row>
                <Row style="margin-top: 10px">
                  日期：
                  <DatePicker v-model="date_value" format="yyyy-MM-dd" type="date" placeholder="选择日期..." style="width: 300px"></DatePicker>
                </Row>
                <Row style="margin-top: 10px">
                  时间：
                  <TimePicker type="time" v-model="time_value" placeholder="选择时间..." style="width: 300px"></TimePicker>
                </Row>
                <Row style="margin-top: 10px">
                  选择镜像名:
                </Row>
                <Col span="24">
                  <Tree :data="appTree" show-checkbox ref="buildList"></Tree>
                </Col>
              </FormItem>
            </Col>
          </Row>
        </Form>
        <div class="demo-drawer-footer">
          <Button style="margin-right: 8px" @click="buildEdit = false">取消</Button>
          <Button type="primary" @click="savebuildList">保存</Button>
        </div>
      </Drawer>

    </Card>

</template>

<script>
  import { getDockerImg } from '@/api/data'
  import { containerHandle } from '@/api/docker'
  import { initWebSocket, closeWebSocket } from '@/libs/web-socket'
  import ProcessHeader from '_c/process-header'
  export default {
    name: 'k8s_manage',
    components: {
      ProcessHeader
    },
    data () {
      return {
        businessID: 'k8s_manage',
        businessID_CN: '容器管理',
        process: 0,
        current_status: '空闲中',
        buildEdit: false,
        collapse: '1',
        date_value: '',
        time_value:'',
        actionType: '待定',
        envChoice: '待定',
        dateValue: '待定',
        choiceData: [],
        choiceApp: [],
        NOT_SUPPORT_EXT_CONF_APP: ['tstatic', 'tomcat-quartz', 'howbuy-quartz', 'redis', 'activemq', 'zookeeper', 'dubbo-admin'],
        wb_data: [],
        formData: {
          name: '',
        },
        appTree: [],
        styles: {
          height: 'calc(100% - 55px)',
          overflow: 'auto',
          paddingBottom: '53px',
          position: 'static'
        },
        execution: [
          {
            title: '镜像名',
            key: 'app'
          },
          {
            title: '状态',
            key: 'execStatus'
          },
        ],
        execution_data: [],
        showEnvSelect: '',
        showActionType: [
          {
            value: 'RESTART',
            label: '重启'
          },
          {
            value: 'UPDATE-CONFIG',
            label: '更新配置'
          },
          {
            value: 'CHANGE-DATE',
            label: '修改环境时间'
          },
          {
            value: 'UPDATE-URL',
            label: '更新集群访问路径'
          },
          {
            value: 'CRE-DEPLOYMENT',
            label: '创建发布对象'
          },
          {
            value: 'DEL-DEPLOYMENT',
            label: '删除发布对象'
          }
        ],
        showEnv: [
          {
            value: 'tms',
            label: 'tms'
          },
          {
            value: 'tms02',
            label: 'tms02'
          },
          {
            value: 'tms03',
            label: 'tms03'
          }
        ],
        showActionTypeSelect: '',
      }
    },
    methods: {
      // 配置校验
      checkConf() {
        let is_ok = true
        if (this.actionType != '待定') {
          if (this.actionType == 'UPDATE-URL' || this.actionType == 'CHANGE-DATE' || this.actionType == 'RESTART') {
            if (this.actionType == 'UPDATE-URL') {
              if (this.showEnvSelect == '') {
                this.$Message.error('选择更新集群访问路径，必须选择环境')
                is_ok = false
              }
              this.choiceData = []
              this.choiceApp = []
              let data = {
                app: 'UPDATE-URL',
                execStatus: '未执行',
              }
              this.choiceData.push(data)
              localStorage.setItem('buildList', JSON.stringify(this.choiceData));
            } else if (this.actionType == 'CHANGE-DATE') {
              if (this.date_value == '' || this.showEnvSelect == '' || this.date_value == '' || this.time_value == '') {
                this.$Message.error('选择修改时间必须选择环境，日期和时间')
                is_ok = false
              } else {
                this.choiceData = []
                this.choiceApp = []
                let data = {
                  app: 'CHANGE-DATE',
                  execStatus: '未执行',
                }
                this.choiceData.push(data)
                localStorage.setItem('buildList', JSON.stringify(this.choiceData));
              }
            } else if (this.actionType == 'RESTART') {
              let choicesAll = this.$refs.buildList.getCheckedNodes()
              if (this.showEnvSelect == '' || choicesAll.length === 0) {
                this.$Message.error('重启需要选择环境, 且至少选择一个镜像')
                is_ok = false
              } else {
                  // 保存配置
                  this.choiceData = []
                  this.choiceApp = []
                  choicesAll.forEach((item) => {
                    if ('children' in item) {
                      // // 传给脚本的applist是需要包含all的
                      // this.choiceApp.push('all')
                    } else {
                      let data = {
                        app: item.title,
                        execStatus: '未执行',
                      }
                      this.choiceData.push(data)
                      this.choiceApp.push(item.title)
                    }
                  })
                  localStorage.setItem('buildList', JSON.stringify(this.choiceData));
              }
            }
            // 处理镜像相关
          } else {
            let choicesAll = this.$refs.buildList.getCheckedNodes()
            choicesAll.forEach((item) => {
              if ('children' in item) {
                this.$Message.error('该类型不能选择all')
                is_ok = false
              } else {
                if (this.actionType == 'UPDATE-CONFIG' && this.NOT_SUPPORT_EXT_CONF_APP.indexOf(item.title) != -1) {
                  this.$Message.error(item.title + '不支持更新配置')
                  is_ok = false
                }
              }
            })
            if (choicesAll.length === 0) {
              this.$Message.error('必须选择至少一个镜像')
              is_ok = false
            }
            if (is_ok === true) {
              // 保存配置
              this.choiceData = []
              this.choiceApp = []
              choicesAll.forEach((item) => {
                if ('children' in item) {
                  //
                } else {
                  let data = {
                    app: item.title,
                    execStatus: '未执行',
                  }
                  this.choiceData.push(data)
                  this.choiceApp.push(item.title)
                }
              })
              localStorage.setItem('buildList', JSON.stringify(this.choiceData));
            }
          }
        } else {
          this.$Message.error('必须选择类型')
          is_ok = false
        }
        return is_ok
      },
      // 构建成功后推送框
      success (nodesc) {
        this.$Notice.success({
          title: '容器管理操作成功',
          desc: nodesc ? '' : '你的操作已成功，请查看',
          duration: 0
        });
      },
      // 操作类型选择赋值
      showActionTypeSelectChange() {
        this.actionType = this.showActionTypeSelect
      },
      // 环境选择赋值
      showEnvSelectChange() {
        this.envChoice = this.showEnvSelect
      },
      // 从子组件ProcessHeader获取配置栏打开状态
      changeBuildEdit(val) {
        this.buildEdit = val
      },
      // 执行完成的后续操作
      execDoneHandle(){
        if (this.current_status != '空闲中') {
          this.current_status = '空闲中'
        }
        this.process = 0
        this.execution_data = []
      },
      toRunScript(val) {
        let is_ok = this.checkConf()
        // 构建
        if (val == true) {
          if (is_ok == true) {
            this.current_status = '忙碌中'
            containerHandle(this.compileList).then(res => {
              // 如果调用脚本失败直接报错
              if (res.data['result'] != 'success') {
                alert('脚本启动错误：\n' +res.data['result'])
                this.execDoneHandle()
              }
            })
          } else {
            this.$Message.error('请填写配置信息')
          }
          // 重置
        } else {
          this.execDoneHandle()
        }
      },
      formatDate (date, fmt) {
        let o = {
          'M+': date.getMonth() + 1, // 月份
          'd+': date.getDate(), // 日
          'h+': date.getHours(), // 小时
          'm+': date.getMinutes(), // 分
          's+': date.getSeconds(), // 秒
          'S': date.getMilliseconds() // 毫秒
        }
        if (/(y+)/.test(fmt)) {
          fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length))
        }
        for (var k in o) {
          if (new RegExp('(' + k + ')').test(fmt)) {
            fmt = fmt.replace(RegExp.$1, (RegExp.$1.length === 1) ? (o[k]) : (('00' + o[k]).substr(('' + o[k]).length)))
          }
        }
        return fmt
      },
      // 保存编译列表
      savebuildList () {
        let is_ok = this.checkConf()
        if (is_ok == true) {
          // 如果类型是修改时间，则处理时间格式
          if (this.actionType == 'CHANGE-DATE') {
            let date = this.formatDate(this.date_value,'yyyy-MM-dd')
            let time = this.time_value
            this.dateValue = date + ' ' + time
          }
          this.execution_data = JSON.parse(localStorage.getItem('buildList'));
          this.$Message.info('保存成功')
          this.buildEdit = false
        }
      },
    },
    watch: {
      wb_data(val) {
        try {
          if (JSON.parse(val) != '') {
            let _data = JSON.parse(val)
            this.current_status = _data['status']
            this.process = _data['percent']
            this.execution_data = _data['app_status']
            if (this.current_status == '空闲中') {
              this.success()
              this.execDoneHandle()
            }
          }
        }
        catch(err){
          // 不能json解析的不做处理
        }
      }
    },
    mounted() {
      getDockerImg('TMS').then(res => {
        this.appTree = JSON.parse(res.data)
      })
      // 建立websocekt链接
      initWebSocket(this, '/k8s_manage?businessID=' + this.businessID)
    },
    destroyed () {
      closeWebSocket(this)
    }
  }
</script>

<style lang="less">

</style>
