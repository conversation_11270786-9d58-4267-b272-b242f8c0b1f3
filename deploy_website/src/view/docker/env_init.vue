<template>

  <Card shadow>

    <ProcessHeader :businessID="businessID" :businessID_CN="businessID_CN" ref="indexImportOrder"
                   v-on:buildEdit="changeBuildEdit" v-on:toRunScript="toRunScript" :process="process"
                   :current_status="current_status">
    </ProcessHeader>

    <Row :gutter="24" style="margin-top: 15px; width: 1000px">
      <Col span="24">
      <Collapse simple v-model="collapse">
        <Panel name="1">
          <Tag type="border" color="default">类型：{{ actionType }}</Tag>
          <Tag type="border" color="default">中台环境：{{ envChoice }}</Tag>
          <Tag type="border" color="default">后台环境：{{ tpEnv }}</Tag>
          <Tag type="border" color="default">是否更新配置：{{ isUpdateConf }}</Tag>
          <p slot="content" style="margin-top: 10px">
          <Table :columns="execution" :data="execution_data"></Table>
          </p>
        </Panel>
      </Collapse>
      </Col>
    </Row>

    <Drawer
      title=""
      v-model="buildEdit"
      width="530px"
      :mask-closable="false"
      :closable="false"
      :styles="styles">
      <Collapse>
        <Panel name="application_content">
          申请信息
          <div slot="content">
            <Row class="marginBottom">
              <span>
                测试项目：
              </span>
              <Input v-model="env_use" placeholder="填写环境用途" style="width: 300px" />
            </Row>
            <Row class="marginBottom">
              <span>
                归属团队：
              </span>
              <Select v-model="FtSelect" disabled style="width:150px">
                <Option v-for="item in FtList" :value="item.value" :key="item.value">{{ item.label }}</Option>
              </Select>
              <Button @click="showUtilization" style="margin-left: 10px" shape="circle" icon="ios-search">查询环境使用情况</Button>
              <Modal title="环境使用情况" v-model="rate_of_utilization" width="950px">
                <Table border :columns="rate_of_utilization_col" :data="rate_of_utilization_col_data"></Table>
              </Modal>
            </Row>
            <Row class="marginBottom">
              <span>
                后台环境：
              </span>
              <Input v-model="tp_env" disabled="disabled" style="width: 150px; margin-right: 5px" />
              搭建后台环境：
              <i-switch v-model="has_tp" @on-change="hasTPchange">
                <span slot="open">是</span>
                <span slot="close">否</span>
              </i-switch>
            </Row>
            <Row class="marginBottom">
              <span>
                中台环境：
              </span>
              <Input v-model="tms_env" disabled="disabled" style="width: 300px" />
            </Row>
            <Row class="marginBottom">
              <span style="margin-right: 10px">
                TA时间：
              </span>
              <DatePicker v-model="ta_time" type="date" placeholder="Select date" style="width: 150px"></DatePicker>
              <span style="margin-left: 10px">
                  更新基础库：
              </span>
              <i-switch v-model="is_update_basedb" @on-change="change">
                <span slot="open">是</span>
                <span slot="close">否</span>
              </i-switch>
            </Row>
            <Row class="marginBottom">
              <span>
                期望环境交付时间：
              </span>
              <DatePicker v-model="start_time" type="date" placeholder="Select date" style="width: 240px"></DatePicker>
            </Row>
            <Row class="marginBottom">
              <span>
                环境使用截止日期：
              </span>
              <DatePicker v-model="end_time" type="date" placeholder="Select date" style="width: 240px"></DatePicker>
            </Row>
          </div>
        </Panel>
      </Collapse>
      <Collapse value="env_init_content">
        <Panel name="env_init_content">
          环境初始化配置栏
          <Form slot="content" :model="formData">
            <Row :gutter="32">
              <Col span="24">
              <FormItem label-position="top">
                <Row>
                  <Col span="12">
                  类型：
                  <Select class="marginRight" v-model="showActionTypeSelect" @on-change="showActionTypeSelectChange" style="width:140px" placeholder="选择操作类型">
                    <Option v-for="item in showActionType" :value="item.value" :key="item.value">{{ item.label }}</Option>
                  </Select>
                  </Col>
                  <Col span="12">
                  中台环境：
                  <Select type="info" v-model="showEnvSelect" style="width:100px" @on-change="showEnvSelectChange" placeholder="选择环境">
                    <Option v-for="item in showEnv" :value="item.value" :key="item.value">{{ item.label }}</Option>
                  </Select>
                  </Col>
                </Row>
                <Row style="margin-top: 10px">
                  后台：
                  <Input placeholder="填写对应的后台交易的IP地址" style="width: 320px" v-model="tpEnv"/>
                </Row>
                <Row style="margin-top: 10px">
                  是否更新配置：
                  <Checkbox v-model="isUpdateConf"></Checkbox>
                </Row>
                <Row style="margin-top: 10px">
                  选择镜像名:
                </Row>
                <Col span="24">
                  <Tree :data="appTree" show-checkbox ref="buildList"></Tree>
                </Col>
              </FormItem>
              </Col>
            </Row>
          </Form>
        </Panel>
      </Collapse>
      <div class="demo-drawer-footer">
        <Button style="margin-right: 8px" @click="buildEdit = false">取消</Button>
        <Button type="primary" @click="savebuildList">保存</Button>
      </div>
    </Drawer>

  </Card>

</template>

<script>
  import { getDockerImg } from '@/api/data'
  import { envInit } from '@/api/docker'
  import { initWebSocket, closeWebSocket } from '@/libs/web-socket'
  import {testEnvApplication, getEnvUtilization} from "@/api/test-env";
  import {formatDate} from "@/libs/util";
  import ProcessHeader from '_c/process-header'
  export default {
    name: 'env_init',
    components: {
      ProcessHeader
    },
    data () {
      return {
        businessID: 'Env_Init',
        businessID_CN: '环境初始化',
        process: 0,
        current_status: '空闲中',
        buildEdit: false,
        collapse: '1',
        actionType: '待定',
        envChoice: '待定',
        tpEnv: '',
        isUpdateConf: false,
        choiceData: [],
        choiceApp: [],
        NOT_SUPPORT_EXT_CONF_APP: ['tstatic', 'tomcat-quartz', 'howbuy-quartz', 'redis', 'activemq', 'zookeeper', 'dubbo-admin'],
        wb_data: [],
        formData: {
          name: '',
        },
        appTree: [],
        styles: {
          height: 'calc(100% - 55px)',
          overflow: 'auto',
          paddingBottom: '53px',
          position: 'static'
        },
        execution: [
          {
            title: '镜像名',
            key: 'app'
          },
          {
            title: '状态',
            key: 'execStatus'
          },
        ],
        execution_data: [],
        showEnvSelect: '',
        showActionType: [
          {
            value: 'BUILD-TRUNK-IMG',
            label: '制作产线版本的基线镜像'
          },
          {
            value: 'APPLY-IMG',
            label: '使用基线镜像恢复环境'
          },
          {
            value: 'BUILD-APPLY',
            label: '制作并恢复环境'
          }
        ],
        showEnv: [],
        showActionTypeSelect: '',
        selectEnvID: '',
        has_tp: true,
        up: false,
        FtSelect: 'tms',
        FtList: [
          {
            value: "tms",
            label: "TMS",
          },
          {
            value: "tp",
            label: "TP",
          },
          {
            value: "otc",
            label: "OTC",
          }
        ],
        env_use: '',
        tms_env: '',
        tp_env: '',
        tp_env_tmp: '',
        is_update_basedb: true,
        applicant: this.$store.state.user.userName,
        ta_time: '',
        start_time: '',
        end_time: '',
        rate_of_utilization: false,
        rate_of_utilization_col: [
          {
            title: '环境',
            width: 150,
            render: (h, params) => {
              let _disable = false
              if (params.row.applicant !== null) {
                _disable = true
              } else {
                _disable = false
              }
              return h('div',{
                style: {
                  textAlign: "right"
                }
              },[
                h('span', {}, params.row.envID),
                h('Button', {
                  props: {
                    disabled: _disable,
                  },
                  style: {
                    marginLeft: "15px",
                  },
                  on: {
                    click: () => {
                      let env_group = params.row.envGroup
                      this.tms_env = env_group["tms"];
                      this.tp_env = env_group["tp"];
                      this.rate_of_utilization = false
                      this.selectEnvID = params.row.envID
                      this.showEnv.push({
                        value: params.row.envGroup.tms,
                        label: params.row.envGroup.tms,
                        tp: params.row.envGroup.tp
                      })
                      this.showEnvSelect = params.row.envGroup.tms
                      this.tpEnv = params.row.envGroup.tp
                    }
                  }
                }, "添加")
              ])
            }
          },
          {
            title: '环境组合',
            key: 'envGroup',
            width: 300
          },
          {
            title: '申请人',
            key: 'applicant',
            width: 110
          },
          {
            title: '用途',
            key: 'envUse'
          },
          {
            title: '截止日期',
            key: 'endTime',
            width: 100
          }
        ],
        rate_of_utilization_col_data: [],
      }
    },
    methods: {
      // 配置校验
      checkConf() {
        let is_ok = true
        // 校验后台ip格式是否正确
        let exp=/^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/
        let reg = this.tpEnv.match(exp);

        if (this.actionType == '待定') {
          this.$Message.error('必须选择类型')
          is_ok = false
        } else if (this.envChoice == '待定'){
          this.$Message.error('必须选择中台环境')
          is_ok = false
        } else if (this.tpEnv == ''){
          this.$Message.error('必须填写后台环境')
          is_ok = false
        } else if (this.$refs.buildList.getCheckedNodes().length === 0) {
          this.$Message.error('必须选择一个镜像')
          is_ok = false
        } else if (reg == null) {
          this.$Message.error('后台环境请输入正确的ip地址')
          is_ok = false
        }

        return is_ok
      },

      // 构建成功后推送框
      success (nodesc) {
        this.$Notice.success({
          title: '容器管理操作成功',
          desc: nodesc ? '' : '你的操作已成功，请查看',
          duration: 0
        });
      },
      // 操作类型选择赋值
      showActionTypeSelectChange() {
        this.actionType = this.showActionTypeSelect
      },
      // 环境选择赋值
      showEnvSelectChange() {
        this.envChoice = this.showEnvSelect
        for (var j=0, len=this.showEnv.length; j<len; j++){
          var ins = this.showEnv[j]
          if (ins['value'] === this.envChoice){
            this.tpEnv = ins['tp']
          }
        }
      },
      // 从子组件ProcessHeader获取配置栏打开状态
      changeBuildEdit(val) {
        this.buildEdit = val
      },
      // 执行完成的后续操作
      execDoneHandle(){
        if (this.current_status != '空闲中') {
          this.current_status = '空闲中'
        }
        this.process = 0
        this.execution_data = []
      },
      toRunScript(val) {
        let is_ok = this.checkConf()
        // 构建
        if (val == true) {
          if (is_ok == true) {
            this.current_status = '忙碌中'
            envInit(this.actionType, this.envChoice, JSON.stringify(this.choiceApp).replace('[', '').replace(']', ''), this.tpEnv, this.isUpdateConf, '').then(res => {
              // 如果调用脚本失败直接报错
              if (res.data['result'] != 'success') {
                alert('脚本启动错误：\n' +res.data['result'])
                this.execDoneHandle()
              }
            })
          } else {
            this.$Message.error('请正确填写配置信息')
          }
          // 重置
        } else {
          this.execDoneHandle()
        }
      },
      formatDate (date, fmt) {
        let o = {
          'M+': date.getMonth() + 1, // 月份
          'd+': date.getDate(), // 日
          'h+': date.getHours(), // 小时
          'm+': date.getMinutes(), // 分
          's+': date.getSeconds(), // 秒
          'S': date.getMilliseconds() // 毫秒
        }
        if (/(y+)/.test(fmt)) {
          fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length))
        }
        for (var k in o) {
          if (new RegExp('(' + k + ')').test(fmt)) {
            fmt = fmt.replace(RegExp.$1, (RegExp.$1.length === 1) ? (o[k]) : (('00' + o[k]).substr(('' + o[k]).length)))
          }
        }
        return fmt
      },
      // 保存编译列表
      savebuildList () {
        if (this.submit_check() === 0) {
          let data = {};
          data['env_id'] = this.selectEnvID;
          data['applicant'] = this.applicant;
          data['env_use'] = this.env_use;
          data['start_time'] = formatDate(this.start_time);
          data['end_time'] = formatDate(this.end_time);
          data['ta_time'] = formatDate(this.ta_time);
          data['is_update_db'] = this.is_update_basedb;
          data['env_owner'] = this.FtSelect;
          data['has_tp'] = this.has_tp;
          this.isShowLoading = true
          testEnvApplication(data).then(res => {
            this.$Message.info(res.data)
            this.isShowLoading = false
          })
        } else {
          this.$Message.error("请完整填写信息")
        }

        let is_ok = this.checkConf()
        if (is_ok == true) {
          // 保存镜像列表配置
          this.choiceData = []
          this.choiceApp = []
          let choicesAll = this.$refs.buildList.getCheckedNodes()
          choicesAll.forEach((item) => {
            if ('children' in item) {
              // // 传给脚本的applist是需要包含all的
              // this.choiceApp.push('all')
            } else {
              let data = {
                app: item.title,
                execStatus: '未执行',
              }
              this.choiceData.push(data)
              this.choiceApp.push(item.title)
            }
          })
          localStorage.setItem('buildList', JSON.stringify(this.choiceData));
          this.execution_data = JSON.parse(localStorage.getItem('buildList'));
          this.$Message.info('保存成功')
          this.buildEdit = false
        }
      },
      change (status) {
        this.is_update_basedb = status
      },
      submit_check() {
        if (this.applicant !== '' && this.env_use !== '' && this.FtSelect !== ''
           && this.tms_env !== '' && this.ta_time !== ''
          && this.start_time !== '' && this.end_time !== '') {
          if ((this.has_tp === true && this.tp_env !== '') || (this.has_tp === false && this.tp_env === '')) {
            return 0
          } else {
            return 1
          }
        } else {
          return 1
        }
      },
      hasTPchange () {
        if (this.has_tp){
          if (this.tp_env === '') {
            this.tp_env = this.tp_env_tmp
          }
        } else {
          this.tp_env_tmp = this.tp_env;
          this.tp_env = '';
        }
      },
      showUtilization() {
        if (this.FtSelect !== ''){
          this.rate_of_utilization = true;
          getEnvUtilization().then(res=> {
            this.rate_of_utilization_col_data = JSON.parse(res.data).filter(item => item['envID'].indexOf(this.FtSelect) > -1)
          })
        } else {
          this.$Message.error("请先选择归属团队")
        }
      }
    },
    watch: {
      wb_data(val) {
        try {
          if (JSON.parse(val) != '') {
            let _data = JSON.parse(val)
            this.current_status = _data['status']
            this.process = _data['percent']
            this.execution_data = _data['app_status']
            if (this.current_status == '空闲中') {
              this.success()
              this.execDoneHandle()
            }
          }
        }
        catch(err){
          // 不能json解析的不做处理
        }
      }
    },
    mounted() {
      getDockerImg('TMS').then(res => {
        this.appTree = JSON.parse(res.data)
      })
      // 建立websocekt链接
      initWebSocket(this, '/env_init?businessID=' + this.businessID)

      //初始化 ‘环境初始化配置栏’中‘中台环境’列表
      getEnvUtilization().then(res=> {
        var allTmsEnv = JSON.parse(res.data).filter(item => item['envID'].indexOf('tms') > -1)

        this.showEnv = []
        for(var j=0, len=allTmsEnv.length; j<len; j++){
          var ins = allTmsEnv[j]
          if (ins['applicant'] !== null){
            this.showEnv.push({
              value: ins['envGroup']['tms'],
              label: ins['envGroup']['tms'],
              tp: ins['envGroup']['tp']
            })
          }
        }
      })
    },
    destroyed () {
      closeWebSocket(this)
    },
  }
</script>

<style lang="less">
  .drawer_application_form_header{
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
  }

</style>
