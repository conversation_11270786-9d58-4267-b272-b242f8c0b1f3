<template>
  <Card>
    <Row>
        <Col span="11">
            <Card dis-hover>
                <p slot="title">

                   Git接入准备
                </p>
                <ul>
                    <li v-for="item in git_prepare">
                        <a target="_blank" @click="iterationPage(item)">{{ item.name }}</a>
                    </li>
                </ul>
              </Card>
        </Col>
       <Col span="11" offset="2">
                     <Card dis-hover>
                          <p slot="title">

                              Git分支策略
                          </p>
                          <ul>
                              <li v-for="item in branch_tactics">
                                  <a target="_blank"  @click="iterationPage(item)">{{ item.name }}</a>
                              </li>
                          </ul>
                  </Card>
        </Col>
    </Row>

    <Row>
        <Col span="11">
            <Card dis-hover>
                <p slot="title">

                   Git分支申请
                </p>
                <ul>
                    <li v-for="item in apply_branch">
                        <a target="_blank" @click="iterationPage(item)">{{ item.name }}</a>
                    </li>
                </ul>
              </Card>
        </Col>
      <Col span="11" offset="2">
                     <Card dis-hover>
                          <p slot="title">

                              Git上线流程
                          </p>
                          <ul>
                              <li v-for="item in git_flow">
                                 <a  target="_blank" @click="iterationPage(item)">{{ item.name }}</a>
                              </li>
                          </ul>
                  </Card>
        </Col>

        <Col span="11">
            <Card dis-hover>
                <p slot="title">

                   Git代码合并
                </p>
                <ul>
                    <li v-for="item in branch_meger">
                        <a target="_blank" @click="iterationPage(item)">{{ item.name }}</a>
                    </li>
                </ul>
              </Card>
        </Col>

    </Row>
  </Card>
</template>

<script>
    export default {
        name: "git_help",
        data () {
            return {
                git_prepare: [
                    {
                        name: 'Gitlab分组的影响',
                        content: '1、相同分组下的工程可以拉取到同一个分支；<br> ' +
                        '   2、同一迭代分支下，拉取分支时，将自动修改依赖版本；<br> ' +
                        '   3、上线时的release版本升级，以每个gitlab组为维度升级；<br> ' +
                        '   4、同一个组下有版本在仿真阶段，则下一个版本无法进入；<br> ' +
                        '   5、原则上，将依赖关系比较密切的项目划分到一个小组内；',
                     imgurl:""
                    },

                    {
                        name: '新项目如何接入git',
                        content: '1、提供gitlab仓库的组名<br> ' +
                        '   2、提供仓库名<br> ' +
                        '   3、提供需要权限的人员名单',
                      imgurl:"/git/git_help/git_flow/git上线流程.png"
                    },
                    {
                        name: 'svn如何转git',
                        content: ' 1、提供svn主干地址。<br> ' +
                        '   2、提供gitlab仓库的组名。<br> ' +
                        '   3、提供仓库名。<br> ' +
                        '    4、如果有分支需要迁移，则需要在master上拉取新的分支，再将svn上代码覆盖到新的分支上，再提交。',
                      imgurl:""
                    }

                ],
                git_flow: [
                    {
                        name: 'Git正常上线流程',
                        content:'',
                        imgurl:'/git/git_help/git_flow/git上线流程.png'
                    },
                    {
                        name: 'Git紧急上线流程',
                        content:'',
                        imgurl:'/git/git_help/git_flow/git紧急上线流程.png'
                    },
                    {
                        name: 'Git项目如何打测试包',
                        content:' 1、进入  GIT自助服务》》git迭代发布》》分支管理<br> ' +
                        '2、选择需要编译的迭代名称；<br> ' +
                        '3、进入 测试包编译页签；<br> ' +
                        '    4、勾选需要编译的项目；<br> ' +
                        '    5、点击执行按钮；<br> ' +
                        '    6、测试环境编译完成后到取包地址拿包部署。',
                        imgurl:'/git/git_help/git_flow/git测试包.png'
                    },
                    {
                        name: 'Git项目如何编译产线包',
                       content:'1、进入  GIT自助服务》》git迭代发布》》分支管理<br> ' +
                       '2、选择需要编译的迭代名称；<br> ' +
                       '3、进入 生产包编译 页签；<br> ' +
                       '    4、勾选需要编译的项目；<br> ' +
                       '    5、点击执行按钮；<br> ' +
                       '6、编译完成后会推送至制品库。',
                        imgurl:'/git/git_help/git_flow/git产线包.png'
                    },
                    {
                        name: 'Git项目如何发布仿真环境',
                       content:'1、进入  GIT自助服务》》git迭代发布》》分支管理<br> ' +
                       '2、选择需要发布的迭代名称；<br> ' +
                       '3、进入 仿真部署 页签；<br> ' +
                       '4、选择需要发布的应用；<br> ' +
                       '    5、点击 发布 按钮；<br> ' +
                       '    6、通过 查看健康状态 按钮来查看容器是否启动成功。',
                        imgurl:'/git/git_help/git_flow/仿真部署.png'
                    },
                    {
                        name: 'Git项目如何发布生产环境',
                        content:'1、进入  GIT自助服务》》git迭代发布》》分支管理<br> ' +
                        '2、选择需要发布的迭代名称；<br> ' +
                        '3、进入 产线发布 页签；<br> ' +
                        '4、选择需要发布的应用；<br> ' +
                        '5、选择需要发布的ip；<br> ' +
                        '    6、点击 发布+启动 按钮；<br> ' +
                        '    7、通过 查看健康状态 按钮来查看容器是否启动成功。',
                        imgurl:'/git/git_help/git_flow/产线部署.png'
                    },
                    {
                        name: 'Sql如何通知dba',
                       content:' 1、进入  GIT自助服务》》git迭代发布》》分支管理<br> ' +
                       '2、选择需要发布的迭代名称；<br> ' +
                       '3、进入 迭代计划 页签；<br> ' +
                       '4、再SQL文本框内，填写sql文件的地址；<br> ' +
                       '5、保存。',
                        imgurl:'/git/git_help/git_flow/sql.png'
                    },
                    {
                        name: '怎样提交配置文件',
                       content:' 1、进入  GIT自助服务》》git迭代发布》》分支管理<br> ' +
                       '2、选择需要发布的迭代名称；<br> ' +
                       '3、进入 迭代计划 页签；<br> ' +
                       '4、选择应用；（只有编译过产线包的应用才会显示）<br> ' +
                       '5、点击配置变更按钮；<br> ' +
                       '6、填写配置并保存。',
                        imgurl:'/git/git_help/git_flow/配置更改.png'
                    },
                    {
                        name: '如何发送上线申请邮件',
                       content:'1、进入  GIT自助服务》》git迭代发布》》分支管理<br> ' +
                       '2、选择需要发布的迭代名称；<br> ' +
                       '3、进入 迭代计划 页签；<br> ' +
                       '4、填写项目设计人员；<br> ' +
                       '5、填写配置，如果有的话；<br> ' +
                       '6、填写sql，如果有的话。<br> ' +
                       '7、填写注意事项，如果有的话；<br> ' +
                       '8、点击 产线申请 按钮。',
                        imgurl:'/git/git_help/git_flow/上线申请邮件.png'
                    },
                    {
                        name: '怎样归档',
                        content:'1、进入  GIT自助服务》》git迭代发布》》分支管理<br> ' +
                        '2、选择需要发布的迭代名称；<br> ' +
                        '3、进入 迭代计划 页签；<br> ' +
                        '4、点击上线完成；',
                        imgurl:'/git/git_help/git_flow/归档.png'
                    }
                ],
                apply_branch: [
                    {
                        name: '如何申请分支',
                        content: '1、进入  GIT自助服务》》git迭代发布》》分支管理；<br> ' +
                        '2、填写分支名称；<br> ' +
                        '3、选择分支类型；（选择研发类型时，分支名将自动添加br_的前缀；选择紧急类型时，分支名将自动添加jinji_的前缀；）<br> ' +
                        '4、选择git仓库名称；（一次只能申请一个git组下的迭代；只显示当前用户有代码权限的仓库；）<br> ' +
                        '5、填写分支截止日期；<br> ' +
                        '6、填写迭代开发内容。',
                        imgurl: '/git/git_help/apply_branch/分支申请.png'
                    },
                    {
                        name: '如何追加仓库',
                        content: '1、进入  GIT自助服务》》git迭代发布》》分支管理<br> ' +
                        '2、选择需要编译的迭代名称；<br> ' +
                        '4、点击追加仓库按钮；<br> ' +
                        '5、选择需要追加的仓库；<br> ' +
                        '6、提交。',
                        imgurl: '/git/git_help/apply_branch/追加仓库.png'
                    },
                    {
                        name: '如何删除仓库',
                        content: '1、进入  GIT自助服务》》git迭代发布》》分支管理<br> ' +
                        '2、选择需要编译的迭代名称；<br> ' +
                        '3、选择对应的仓库；<br> ' +
                        '4、点击 删除 按钮；<br> ' +
                        '5、点击 确定。',
                        imgurl: '/git/git_help/apply_branch/删除仓库.png'
                    }
                ],
                branch_tactics: [
                    {
                        name: 'git分支策略图',
                        content: '',
                        imgurl: '/git/git_help/branch_tactics/分支策略.png'
                    },
                    {
                        name: '何时修改pom文件版本',
                       content: '1、从master拉取分支时，会将pom文件版本修改为对应分支名称；<br> ' +
                       '2、编译产线代码时，pom文件版本会修改为release版本，符合产线版本升级规则。',
                        imgurl: ''
                    },
                    {
                        name: '产线版本升级规则',
                       content: ' 1、在编译产线包时会自动将引用的版本修改为RELEASE（会自动引用私服上版本号最大的release版本）；<br> ' +
                       '   2、引用中台的非git分支策略的版本会自动修改为1.0.0-release；<br> ' +
                       '   3、可以手动修改依赖版本为RELEASE，来引用切换为git分支策略的最新版本。',
                        imgurl: '/git/git_help/branch_tactics/版本升级规则.png'
                    },
                    {
                        name: '如何获取最新的产线版本',
                        content: ' 1、在编译产线包时会自动将引用的版本修改为RELEASE（会自动引用私服上版本号最大的release版本）；<br> ' +
                        '   2、引用中台的非git分支策略的版本会自动修改为1.0.0-release；<br> ' +
                        '   3、可以手动修改依赖版本为RELEASE，来引用切换为git分支策略的最新版本。',
                        imgurl: ''
                    },
                    {
                        name: '相同分组下的jar包版本事发后会修改',
                        content: ' 1、拉取分支时，相同分组下的jar包会自动修改依赖版本为当前分支的版本；<br> ' +
                        '   2、编译上线包时候，会将引用的版本自动修改为RELEASE，不用自己手动改。',
                        imgurl: ''
                    }
                ],
                branch_meger: [
                    {
                        name: '何时将master代码回合到分支',
                        content: '   1、其他分支上线归档后，可以将master代码回合到正在开发的分支；<br> ' +
                        '   2、编译产线代码时，如果提示未合并最新的代码，需要将master代码和合并到对应的分支，才能继续编译。',
                        imgurl: ''
                    },
                    {
                        name: '怎么将分支代码合并到master',
                        content: '    1、分支上线后，需要将分支代码合并至master；<br> ' +
                        '    2、进入  GIT自助服务》》git迭代发布》》分支管理<br> ' +
                        '3、选择需要发布的迭代名称；<br> ' +
                        '4、进入 迭代计划 页签；<br> ' +
                        '5、点击上线完成，将自动把代码合并至master。',
                        imgurl: ''
                    },
                    {
                        name: '何时将dev分支合并到test分支',
                        content: '1、dev分支开发完毕后，可以将dev分支合并到对应的test分支进行测试',
                       imgurl: ''
                    }
                ]

            }
        },
        methods: {
           iterationPage (par){

                this.$router.push({name:"git_help_detail",
                  params:{"name":par.name,"content":par.content,"imgurl":par.imgurl}})

          },
        }


    }
</script>

<style scoped>

</style>
