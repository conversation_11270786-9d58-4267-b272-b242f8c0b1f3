<template>
 <Card >
        <Row style="padding-left: 25px; margin-bottom: -10px">
            <h3>{{name}}</h3>
            </Row>
           <Divider />
          <Row>
           <p v-if="content" v-html="content">{{content}}</p>
          </Row>
          <Row>
            <div>
              <img  v-if="imgUrl" style="width: 100%" v-bind:src="imgUrl">
            </div>
          </Row>
    </Card>
</template>

<script>
  // import VueDirectiveImagePreviewer from 'vue-directive-image-previewer'
  // import 'vue-directive-image-previewer/dist/assets/style.css'
  // Vue.user(VueDirectiveImagePreviewer)
    export default {
        name: "git_help_detail",

       data () {
            return {
              name:this.$route.params.name,
              content: this.$route.params.content,
              imgUrl:null
            }
        },
    beforeRouteUpdate (to, from, next) {
        console.log(to)

       next();
      },
    mounted(){
        if   (this.$route.params.imgurl == ""){
          this.imgUrl = false
        }
        else {
          this.imgUrl = require("../../../public/imgs" + this.$route.params.imgurl)
        }
    }

    }
</script>

<style scoped>

</style>
、
