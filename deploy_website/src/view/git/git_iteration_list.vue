<template>
  <div>
    <div style="margin-bottom: 15px">
      <h2>与我相关的项目1</h2>
    </div>
    <Card>
 <tables ref="tables" v-model="tableData" :columns="columns" filteredValue @on-row-click="iterationPage"/>
    </Card>
  </div>
</template>
<script>
  import Tables from '_c/tables'
  import { getTableData } from '@/api/data'
  export default {
    name: 'git_tables_page',
    components: {
      Tables
    },
    data () {
      return {
        columns: [
          {title: '迭代名称', key: 'pipeline_id' },
          {title: '迭代类型',
            key: 'br_style',
             filters: [ { label: '研发',
                                value: "dev"
                            },
                            {
                                label: '测试',
                                value: "test"
                            },
                          {
                                label: '紧急',
                                value: "jinji"
                            }
                        ],
                      filterMultiple: false,
                        filterMethod (value, row) {
                          return row.br_style.indexOf(value) > -1;
                        },
                    filteredValue:["test"],
          },
          {title: '分组', key: 'project_group',
           filters: [],
            filterMethod (value, row) {
              return row.project_group.indexOf(value) > -1;
            },
          },
          {title: '创建时间', key: 'br_start_date', sortable: true},
          {title: '截止时间', key: 'duedate', sortable: true},
          {title: '功能描述', key: 'description', sortable: true},
          // {
          //   title: '操作',
          //   key: 'handle',
          //   options: ['delete'],
          //   button: [
          //     (h, params, vm) => {
          //       return h('Poptip', {
          //         props: {
          //           confirm: true,
          //           title: '你确定要删除吗?'
          //         },
          //         on: {
          //           'on-ok': () => {
          //             vm.$emit('on-delete', params)
          //             vm.$emit('input', params.tableData.filter((item, index) => index !== params.row.initRowIndex))
          //           }
          //         }
          //       })
          //     }
          //   ]
          // }
        ],
        tableData: []
      }
    },

    methods: {
      handleDelete (params) {
        console.log(params)
      },
      iterationPage (par){
        console.log(par.pipeline_id)
        this.$router.push({name:"git_iteration_page",params:{"iterative_name":par.pipeline_id,"br_style":par.br_style}})
  },
    },
    mounted () {
      getTableData().then(res => {
        this.tableData = res.data.iterative_list;
        this.columns[2].filters=res.data.group_list
        //this.groupList=res.data.group_list;
        //alert(this.groupList)

      })
    }
  }
</script>

<style>

</style>
