<template>
  <Card>
    <Row style="padding-left: 25px; margin-bottom: -10px">
      <h1>紧急分支申请</h1>
    </Row>
    <Divider />
    <Row>
      <img :src="imgUrl1">
    </Row>
     <Divider />
    <Row style="padding-left: 25px; margin-bottom: -10px">
    <h1>紧急编译注意事项</h1>
       </Row>
     <Divider />
    <p> 1、如果存在 已<span style="font-size: 15px;color: red">上线未归档</span>的迭代，请先<span style="font-size: 15px;color: red">去归档</span>。</p>
    <p>   2、如果判断此迭代<span style="font-size: 15px;color: red">没有上线</span>，可点击<span style="font-size: 15px;color: red">继续编译</span>，覆盖此迭代。</p>
    <Divider />
    <Row>
      <img :src="imgUrl2">
    </Row>
    <Divider />
    <Row>
      <img :src="imgUrl3">
    </Row>
    <Divider />
    <Row style="padding-left: 25px; margin-bottom: -10px">
      <h1>归档操作</h1>
    </Row>
    <Divider />
    <Row>
      <img :src="imgUrl4">
    </Row>
    <Divider />
  </Card>

</template>

<script>
    export default {
      name: "git_jinji_guide",
      data() {
        return {
          imgUrl1: require("../../../public/imgs/git/apply_for.png"),
          imgUrl2: require("../../../public/imgs/git/jinji_compile.jpg"),
          imgUrl3: require("../../../public/imgs/git/judge.jpg"),
          imgUrl4: require("../../../public/imgs/git/archive.png"),
        }
      }
    }
</script>

<style scoped>

</style>
