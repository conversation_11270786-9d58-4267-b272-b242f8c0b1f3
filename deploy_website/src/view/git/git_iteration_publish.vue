<template>
  <div>
    <Tabs v-model="tab_name" @on-click="getCompileTree">
      <TabPane name="branch_manager" label="分支管理" icon="ios-folder">
        <IterativeOverview ref="iterativeOverview"></IterativeOverview>
      </TabPane>
      <TabPane label="测试/灰度包编译" name="test_compile" :disabled="tab_disable" icon="ios-construct">
        <GitJavaCompile
          :compile_env="compile_env"
          :gitCompileTree="compileTree"
          :current_status="current_status"
          :buildHistory="buildHistory"
          :process="process"
          v-on:exec_execution_data="exec_execution_data"
          v-on:execDoneHandle="execDoneHandle"
          v-on:updateCompileEnv="updateCompileEnv"
          :execution_data="execution_data"
        ></GitJavaCompile>
      </TabPane>
      <TabPane label="生产包编译" name="prod_compile" :disabled="tab_disable" icon="ios-construct">
        <GitJavaCompile
          :compile_env="compile_env"
          :gitCompileTree="compileTree"
          :current_status="current_status"
          :buildHistory="buildHistory"
          :process="process"
          v-on:changeTab="changeTab"
          v-on:exec_execution_data="exec_execution_data"
          v-on:execDoneHandle="execDoneHandle"
          :execution_data="execution_data"
        ></GitJavaCompile>
      </TabPane>
      <TabPane label="迭代计划" name="iterative_plan" :disabled="tab_disable" icon="md-alarm">
        <GitIterativePlan ref="iterativePlan"></GitIterativePlan>
      </TabPane>
      <TabPane label="仿真部署" name="uat_publish" :disabled="tab_disable" icon="ios-cloud-upload">
        <GitUatPublish ref="gituatPublish"></GitUatPublish>
      </TabPane>
      <TabPane
        label="产线发布"
        name="prod_publish"
        :disabled="tab_disable"
        icon="ios-cloud-upload-outline"
      >
        <GitProdPublish ref="gitprodPublish"></GitProdPublish>
      </TabPane>
    </Tabs>
  </div>
</template>

<script>
import { getGitCompileTree } from "@/api/java-compile";
import IterativeOverview from "_c/iterative-overview";
//import BranchMgmt from '_c/branch-mgmt'
import GitJavaCompile from "_c/git-java-compile";
import GitUatPublish from "_c/git-uat-publish";
import GitProdPublish from "_c/git-prod-publish";
import EmailNotify from "_c/email-notify";
import GitIterativePlan from "_c/git-iterative-plan";
import store from "../../store";
import { initWebSocket, closeWebSocket } from "@/libs/web-socket";
import { execHistory } from "@/api/data";
export default {
  name: "git_iteration_publish",

  created() {
    store.commit("setBusinessID", this.$route.params.iterative_name);
    //alert(store.state.businessID)
  },
  components: {
    IterativeOverview,
    GitUatPublish,
    GitProdPublish,
    GitJavaCompile,
    EmailNotify,
    GitIterativePlan
  },
  methods: {
    // 选择测试或灰度编译
    updateCompileEnv(env) {
      this.compile_env = env
    },
    changeTab() {
      this.$refs.iterativePlan.initThisVue();
      this.tab_name = "iterative_plan";
      //this.$refs.iterativePlan.initThisVue()
      this.$refs.iterativeOverview.initThisVue();
    },
    // 执行完成的后续操作
    execDoneHandle() {
      if (this.current_status !== "空闲中") {
        this.current_status = "空闲中";
      }
      this.process = 0;
      this.execution_data = [];
    },
    exec_execution_data(params) {
      this.execution_data = params;
    },

    getCompileTree(params) {
      console.log(params);
      if (
        params == "prod_compile" ||
        params == "test_compile"
      ) {
        closeWebSocket(this);
        if (params == "prod_compile") {
          this.compile_env = "prod";
        } else {
          this.compile_env = "test";
        }
        getGitCompileTree().then(res => {
          //alert(JSON.stringify(res.data["build_trees"]))
          this.compileTree = res.data["build_trees"];
          execHistory(store.state.businessID, this.compile_env).then(res => {
            this.buildHistory = res.data;
            // alert(this.buildHistory)
          });
        });
        if (store.state.businessID !== "") {
          if (this.compile_env == "prod") {
            initWebSocket(
              this,
              "/java_compile?businessID=" + store.state.businessID
            );
          } else {
            initWebSocket(
              this,
              "/java_compile?businessID=" +
                this.compile_env +
                "__" +
                store.state.businessID
            );
          }
        }
      } else {
        if (params == "iterative_plan") {
          this.$refs.iterativePlan.initThisVue();
        } else if (params == "uat_publish") {
          this.$refs.gituatPublish.initThisVue();
        } else if (params == "prod_publish") {
          this.$refs.gitprodPublish.initThisVue();
        }
        closeWebSocket(this);
      }
    }
  },
  watch: {
    // 实时获取状态
    wb_data(val) {
      try {
        if (JSON.parse(val) !== "") {
          let _data = JSON.parse(val);
          this.current_status = _data["status"];
          this.process = _data["percent"];
          this.execution_data = _data["app_status"];
        }
      } catch (err) {
        // 不能json解析的不做处理
      }
    }
  },
  computed: {
    tab_disable() {
      if (store.state.businessID == "" || store.state.businessID == undefined) {
        return true;
      } else {
        return false;
      }
    }
  },
  data() {
    return {
      tab_name: "branch_manager",
      compile_env: "",
      buildHistory: [],
      wb_data: [],
      current_status: "空闲中",
      process: 0,
      execution_data: [],
      compileTree: [{ title: "数据加载中..." }]
    };
  }
};
</script>

<style lang="less">
</style>
