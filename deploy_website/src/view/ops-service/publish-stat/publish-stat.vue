<template>
  <div>
    <Dropdown style="margin: 10px" @on-click="changeStat">
      <Button size="large">
        选择状态
        <Icon type="ios-arrow-down"></Icon>
      </Button>
      <DropdownMenu slot="list">
        <DropdownItem name="all">全部</DropdownItem>
        <DropdownItem name="apply">已申请未处理</DropdownItem>
        <DropdownItem name="unapply">未申请</DropdownItem>
      </DropdownMenu>
    </Dropdown>
    <Page style="margin:10px; float: right" :total="total" :current="page" @on-change="changePage" show-total></Page>
    <Table :loading="loading" :columns="columns" :data="data"></Table>
    <Page style="margin: 10px; float: right" :total="total" :current="page" @on-change="changePage" show-total></Page>
    <Modal
      v-model="modal1"
      title="处理申请"
      @on-ok="ok"
      @on-cancel="cancel">
      <p>ID: {{ m_pk }}</p>
      <p>业务名称: {{ m_business_name }}</p>
      <p>应用名称: {{ m_app_name }}</p>
      <p>确定处理?</p>
    </Modal>
  </div>
</template>

<script>
import PublishStat from '_c/publish-stat'
import {
  getPublishStatData,
  setPublishStatData
} from '@/api/ops-service'

export default {
  name: 'publish_stat_page',
  components: {
    PublishStat
  },
  data () {
    return {
      loading: true,
      modal1: false,
      page: 1,
      pageSize: 10,
      input_name: '',
      input_pk: '',
      input_put_name: '',
      input_put_pk: '',
      m_business_name: '',
      m_app_name: '',
      m_pk: '',
      columns: [
        {
          title: 'ID',
          key: 'pk',
          width: 80,
          sortable: true
        },
        {
          title: '业务名称',
          width: 220,
          key: 'business_name'
        },
        {
          title: '应用名称',
          width: 220,
          key: 'app_name'
        },
        {
          title: '当前状态',
          key: 'cur_stat',
          width: 160,
        },
        {
          title: '操作',
          key: 'action',
          align: 'left',
          render: (h, params) => {
            if (params.row.cur_stat === '已申请未处理') {
              return h('div', [
                h('Button', {
                  props: {
                    type: 'primary',
                    size: 'small'
                  },
                  style: {
                    // marginRight: '5px'
                  },
                  on: {
                    click: () => {
                      this.show(params.index)
                    }
                  }
                }, '处理'),
              ]);
            }
          }
        }
      ],
      data: [
      ],
      total: 0,
      pageDataTotal: 0,
      pageData: [],
      list: [],
    }
  },
  methods: {
    // get page date
    getPageList(p, size) {
      return this.pageData.slice((p - 1) * size, p * size)
    },
    changePage(p) {
      this.page = p;
      this.data = this.getPageList(p, 10)
    },
    show (index) {
      this.m_business_name = this.data[index].business_name;
      this.m_app_name = this.data[index].app_name;
      this.m_pk = this.data[index].pk;
      this.modal1 = true
      // this.$Modal.info({
      //   title: '业务信息',
      //   content: `业务名称：${this.data[index].business_name}<br>应用名称：${this.data[index].app_name}<br>当前状态：${this.data[index].cur_stat}`
      // })
    },
    ok () {
      setPublishStatData({'pk': this.m_pk}).then(res => {
        if (res.data.code === 0) {
          this.remove(this.m_pk)
          this.$Message.success('Success');
        } else {
          this.$Message.error('Failed');
        }
      })
    },
    cancel () {
      this.$Message.info('Cancel');
    },
    remove (index) {
      this.data.splice(this.data.findIndex(item => item.pk === index), 1);
      this.list.splice(this.list.findIndex(item => item.pk === index), 1);
      this.pageDataTotal = this.pageDataTotal - 1;
      this.total = this.pageDataTotal;
    },
    changeStat (name) {
      if (name === 'apply') {
        this.pageData = this.list.filter(item => item.cur_stat === '已申请未处理');
        this.total = this.pageData.length;
        this.pageDataTotal = this.total;
        this.changePage(1)
      } else if (name === 'unapply') {
        this.pageData = this.list.filter(item => item.cur_stat === '未申请');
        this.total = this.pageData.length;
        this.pageDataTotal = this.total;
        this.changePage(1)
      } else if (name === 'all') {
        this.pageData = this.list;
        this.total = this.pageData.length;
        this.pageDataTotal = this.total;
        this.changePage(1)
      }
    },
  },
  mounted () {
    getPublishStatData().then(res => {
      this.list = res.data;
      this.pageData = this.list;
      this.total = this.list.length;
      this.pageDataTotal = this.total;
      this.changePage(1);
      this.loading = false
    })
  },
  destroyed () {
    console.log('exit publish-stat')
  }
}
</script>

<style lang="less">
@baseColor: ~"#dc9387";
.countto-page-row{
  height: 200px;
}
.count-to-con{
  display: block;
  width: 100%;
  text-align: center;
}
.count-text{
  font-size: 50px;
  color: @baseColor;
}
.slot-text{
  font-size: 22px;
}
.unit-class{
  font-size: 30px;
  color: @baseColor;
}
</style>
