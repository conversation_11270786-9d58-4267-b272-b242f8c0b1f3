<template>
  <card>
    <h2 style="margin: 1em">{{ date_title }}</h2>
    <Table style="margin-left: 2em" :loading="loading" :columns="columns" :data="data" width=700></Table>
  </card>
</template>

<script>
import {
  getAppPublishHistory,
} from '@/api/ops-service'

export default {
  name: 'app_publish_history',
  data() {
    return {
      date_title: "",
      data: [],
      loading: true,
      columns: [
        {
          title: '应用名称',
          key: 'app_name',
          width: 200,
        },
        {
          title: 'IP',
          key: 'ip',
          width: 130
        },
        {
          title: '操作',
          key: 'type',
          width: 80
        },
        {
          title: '执行人',
          key: 'operator',
          width: 130
        },
        {
          title: '时间',
          key: 'time',
        },
      ],
    }
  },
  methods: {
    initThisVue () {
      var date = new Date();
      this.date_title = date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate();  
      getAppPublishHistory().then(res => {
        this.data = res.data.data
        this.loading = false
      })
    }
  },
  mounted () {
    this.initThisVue()
  },
  destroyed () {

  }
}
</script>

<style scoped>

</style>
