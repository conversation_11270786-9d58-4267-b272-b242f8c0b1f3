<template>
  <Card>
    <Row style="padding-left: 25px; margin-bottom: -10px">
      <h1>应用发布操作指南</h1>
    </Row>
    <Divider />
    <Row style="padding-left: 25px; margin-bottom: -10px">
      <h2>应用列表</h2>
    </Row>
    <Divider />
    <p>用户进入运维发布页面，将看到可操作的应用列表。</p>
    <p>应用列表由权限管理控制，不同用户会有不同的角色，角色与应用分组进行绑定。</p>
    <br>
    <!-- <p> 1、如果存在 已<span style="font-size: 15px;color: red">上线未归档</span>的迭代，请先<span style="font-size: 15px;color: red">去归档</span>。</p> -->
    <Row>
      <img :src="imgUrl0" />
    </Row>
    <Divider />
    <Row style="padding-left: 25px; margin-bottom: -10px">
      <h2>页面介绍</h2>
    </Row>
    <Divider />
    <Row>
      <img :src="imgUrl1" />
    </Row>
    <Divider />
    <Row style="padding-left: 25px; margin-bottom: -10px">
      <h2>发布版本</h2>
    </Row>
    <Divider />
    <Row>
      <img :src="imgUrl2" />
    </Row>
    <Divider />
    <Row style="padding-left: 25px; margin-bottom: -10px">
      <h2>操作命令预览</h2>
    </Row>
    <Divider />
    <Row>
      <img :src="imgUrl3" />
    </Row>
    <Divider />
    <Row style="padding-left: 25px; margin-bottom: -10px">
      <h2>未部署的节点</h2>
    </Row>
    <Divider />
    <Row>
      <img :src="imgUrl4" />
    </Row>
    <Divider />
    <Row style="padding-left: 25px; margin-bottom: -10px">
      <h2>产线健康状态</h2>
    </Row>
    <Divider />
    <Row>
      <img :src="imgUrl5" />
    </Row>
    <Divider />
    <Row style="padding-left: 25px; margin-bottom: -10px">
      <h2>执行历史</h2>
    </Row>
    <Divider />
    <Row>
      <img :src="imgUrl6" />
    </Row>
  </Card>
</template>

<script>
export default {
  name: "ops-publish-guide",
  data() {
    return {
      imgUrl0: require("../../../../public/imgs/publish/0_role.png"),
      imgUrl1: require("../../../../public/imgs/publish/1_intro.png"),
      imgUrl2: require("../../../../public/imgs/publish/2_version.png"),
      imgUrl3: require("../../../../public/imgs/publish/3_command.png"),
      imgUrl4: require("../../../../public/imgs/publish/4_initial.png"),
      imgUrl5: require("../../../../public/imgs/publish/5_status.png"),
      imgUrl6: require("../../../../public/imgs/publish/6_history.png")
    };
  }
};
</script>

<style scoped>
</style>
