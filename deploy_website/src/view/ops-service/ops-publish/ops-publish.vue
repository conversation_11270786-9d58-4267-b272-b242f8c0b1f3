<template>
  <Card shadow>
    <div>
      <div class="message-page-con message-category-con">
        <Input @keyup.enter.native="search_apps" prefix="ios-search" placeholder="应用名称或IP" v-model="appSearch"
               style="width:12em; margin-right: 1em"/>
        <Button size="small" type="primary" @click="search_apps">搜索</Button>
        <Scroll style="margin-top: 1em" :height="window_height">
          <ul class="ivu-menu ivu-menu-light ivu-menu-vertical" v-for="item in app_list" :app_list="app_list"
              :key="item">
            <li class="ivu-menu-item" @click="clickPanel(item)">{{item}}</li>
          </ul>
        </Scroll>
      </div>
      <div class="message-page-con message-view-con">
        <div>
          <i-col>
            <Tag>用户组: <span style="color: #2d8cf0">{{this.$store.state.user.role_role}}</span></Tag>
            <Tag>应用权限: <span style="color: #2d8cf0">{{this.$store.state.user.role_project}}</span></Tag>
          </i-col>
          <i-col>
            <Tag><span style="color: #2d8cf0">仿真节点：蓝色</span></Tag>
            <Tag><span style="color: #19be6b">灾备节点：绿色</span></Tag>
            <Tag><span style="color: #17233d">产线节点：黑色</span></Tag>
            <Tag><span style="color: #ff9900">灰度节点：黄色</span></Tag>
          </i-col>
          <Divider>{{app_name}}</Divider>
          <i-col v-if="app_name">
            <Button style="margin-right: 1em;" size="small" ghost type="success" @click="show_prod_health">产线健康状态
            </Button>
            <Button style="margin-right: 1em;" size="small" ghost type="success" @click="history">执行历史</Button>
            <!-- <Button :disabled="zeibei_update_btn" style="margin-right: 1em;" size="small" ghost type="success" @click="zeibei_update">灾备代码更新</Button> -->
          </i-col>
          <i-col v-if="app_name" style="margin-top: 1em;">
            <span style="color:#515a6e; margin-right:3em;">选择环境</span>
            <!-- <span style="color:#515a6e; margin-right:3em;">选择机房</span> -->
            <span style="color:#515a6e; margin-right:3em;">选择组</span>
            <span style="color:#515a6e; margin-left:12em;">批量操作</span>
          </i-col>
          <i-col v-if="app_name" style="margin-top: 2px">
            <Select v-if="app_name" size="small" v-model="envSelect" style="width:6em; margin-right:1em;">
              <Option v-for="item in envList" :value="item.value" :key="item.value">{{ item.label }}</Option>
            </Select>
            <!-- <Select v-if="app_name" size="small" v-model="regionSelect" style="width:6em; margin-right:1em;"> -->
            <!-- <Option v-for="item in regionList" :value="item.value" :key="item.value">{{ item.label }}</Option> -->
            <!-- </Select> -->
            <Select v-if="app_name" size="small" v-model="groupSelect" style="width:6em; margin-right:1em;">
              <Option v-for="item in groupList" :value="item.value" :key="item.value">{{ item.label }}</Option>
            </Select>
            <Button :disabled='bat_deploy_btn' v-if="app_name" style="margin-left: 1em;margin-right: 1em;" ghost
                    type="primary" size="small" @click="multi_ack_show('deploy')">发布+启动
            </Button>
            <Button :disabled='bat_restart_btn' v-if="app_name" style="margin-right: 1em;" ghost type="primary"
                    size="small" @click="multi_ack_show('restart')">重启
            </Button>
            <Button :disabled='bat_rollback_btn' v-if="app_name" style="margin-right: 1em;" ghost type="primary"
                    size="small" @click="multi_ack_show('rollback')">回滚
            </Button>
            <Button :disabled='bat_update_btn' v-if="app_name" style="margin-right: 1em;" ghost type="primary"
                    size="small" @click="multi_ack_show('update')">配置更新
            </Button>
            <Button :disabled='bat_codeupdate_btn' v-if="app_name" style="margin-right: 1em;" ghost type="primary"
                    size="small" @click="multi_ack_show('code_update')">代码更新
            </Button>
            <span>节点是否一致：</span>
            <span style="color: green" v-if="consistentNode">一致 </span>
            <span style="color: red" v-else v-bind:title="nodeInfo">不一致</span>
            <Button type="primary" shape="circle" icon="ios-refresh" size="small"
                    @click="refreshNodeConsistent"></Button>
            <span style="color:#a59f9f">{{consistentTime}}</span>
          </i-col>
          <i-col v-if="app_name" style="margin-top: 1em;">
            <Table :columns="columns" :data="data"></Table>
          </i-col>
        </div>
      </div>
    </div>

    <Modal
      v-model="ack_modal"
      @on-cancel="cancel"
      @on-ok="doOperate">
      <p slot="header">
        <span> {{ app_name }} 操作 </span>
      </p>
      <div v-if="opt_type ==='deploy'">
        <div v-for="ip in ips" :key="ip">
          <p style="font-size: 1rem; margin-left: 6rem; margin-right: 1rem; display:inline;"><span style="color: blue">{{ip}}</span>
          </p>
          <p style="font-size: 1rem; display:inline;">执行<span style="color: red"> 发布 </span>?</p>
        </div>
      </div>
      <div v-if="opt_type ==='restart'">
        <div v-for="ip in ips" :key="ip">
          <p style="font-size: 1rem; margin-left: 6rem; margin-right: 1rem; display:inline;"><span style="color: blue">{{ip}}</span>
          </p>
          <p style="font-size: 1rem; display:inline;">执行<span style="color: red"> 重启 </span>?</p>
        </div>
      </div>
      <div v-if="opt_type ==='stop'">
        <div v-for="ip in ips" :key="ip">
          <p style="font-size: 1rem; margin-left: 6rem; margin-right: 1rem; display:inline;"><span style="color: blue">{{ip}}</span>
          </p>
          <p style="font-size: 1rem; display:inline;">执行<span style="color: red"> 停止 </span>?</p>
        </div>
      </div>
      <div v-if="opt_type ==='rollback'">
        <div v-for="ip in ips" :key="ip">
          <p style="font-size: 1rem; margin-left: 6rem; margin-right: 1rem; display:inline;"><span style="color: blue">{{ip}}</span>
          </p>
          <p style="font-size: 1rem; display:inline;">执行<span style="color: red"> 回滚 </span>?</p>
        </div>
      </div>
      <div v-if="opt_type ==='update'">
        <div v-for="ip in ips" :key="ip">
          <p style="font-size: 1rem; margin-left: 6rem; margin-right: 1rem; display:inline;"><span style="color: blue">{{ip}}</span>
          </p>
          <p style="font-size: 1rem; display:inline;">执行<span style="color: red"> 配置更新 </span>?</p>
        </div>
      </div>
      <div v-if="opt_type ==='code_update'">
        <div v-for="ip in ips" :key="ip">
          <p style="font-size: 1rem; margin-left: 6rem; margin-right: 1rem; display:inline;"><span style="color: blue">{{ip}}</span>
          </p>
          <p style="font-size: 1rem; display:inline;">执行<span style="color: red"> 代码更新 </span>?</p>
        </div>
      </div>
    </Modal>
    <Modal
      v-model="health_modal"
      width="680"
      :mask-closable="false"
      @on-cancel="health_cancel">
      <p slot="header" style="color:gray;">
        <Icon type="md-clipboard"></Icon>
        <span> {{ app_name }} 产线健康状态</span>
      </p>
      <Table border stripe :columns="health_columns" :data="health_data"></Table>
      <div slot="footer">
        <Button @click="health_cancel">关闭</Button>
      </div>
    </Modal>
    <Modal
      v-model="ops_operate_modal"
      width="680"
      :mask-closable="false"
      @on-cancel="cancel">
      <p slot="header" style="color:gray;">
        <Icon type="md-clipboard"></Icon>
        <span> {{ app_name }} 执行历史 </span>
      </p>
      <div style="height:300px; overflow-y: auto;">
        <table style="margin: 10px;" v-for="cont in historyCont" :key="cont.index">
          <tr>
            <td width="15px">
              <Icon type="md-arrow-round-forward"></Icon>
            </td>
            <td width="100px" style="color: darkblue;">{{ cont.operator }}</td>
            <td width="50px" style="color: black">{{ cont.type }}</td>
            <td width="400px">{{ cont.operateTime }}</td>
          </tr>
          <tr>
            <td width="15px"></td>
            <td width="100px" style="border-bottom: #DDDDDD solid 2px; color: black;">{{ cont.ip }}</td>
            <td width="450px" colspan="2" style="border-bottom: #DDDDDD solid 2px;" v-if="cont.detail !== 'error'"><span
              v-html="cont.detail"></span></td>
            <td width="450px" colspan="2" style="border-bottom: #DDDDDD solid 2px; color: red;" v-else><span
              v-html="cont.detail"></span></td>
          </tr>
        </table>
      </div>
      <div slot="footer">
        <Button @click="cancel">关闭</Button>
      </div>
    </Modal>
    <Modal
      v-model="info_modal"
      width="420"
      @on-cancel="cancel">
      <p slot="header" style="color:gray;">
        <Icon type="md-clipboard"></Icon>
        <span>{{ select_ip }} 信息</span>
      </p>
      <div style="height:100px; overflow-y: auto;">
        <p style="font-size: 1em; margin-left: 2em; margin-top: 1em;">当前发布版本:<span
          style="color: blue"> {{git_version}} </span></p>
        <p style="font-size: 1em; margin-left: 2em; margin-top: 1em;">最新发布时间:<span
          style="color: blue"> {{last_deploy}} </span></p>
      </div>
      <div slot="footer">
        <Button @click="cancel">关闭</Button>
      </div>
    </Modal>
    <Modal
      v-model="command_modal"
      width="46em"
      @on-cancel="cancel">
      <p slot="header">
        <span> {{ app_name }} {{ select_ip}} 命令预览 </span>
      </p>
      <div>
        <p align="right" style="font-size: 1rem; width: 4em; display:inline-block;">minion_id</p>
        <span
          style="width: 40em; font-size: 1rem; padding-left: 2em; color: #2d8cf0;">{{ operateCommand['minion_id'] }}</span>
      </div>
      <div style="margin-top: 1em">
        <p align="right" style="font-size: 1em; width: 4em; display:inline-block;">发布</p>
        <Input style="width: 8em; padding-left: 1em" disabled v-model="operateCommand['deploy_func']"/>
        <Input style="width: 32em; padding-left: 1em" disabled type='textarea' v-model="operateCommand['deploy']"/>
        <!--<Button style="margin-left: 1em;" ghost type="primary" size="small" @click="save_cmd('deploy')">保存</Button>-->
      </div>
      <div style="margin-top: 1em">
        <p align="right" style="font-size: 1em; width: 4em; display:inline-block;">重启</p>
        <Input style="width: 8em; padding-left: 1em" disabled v-model="operateCommand['restart_func']"/>
        <Input style="width: 32em; padding-left: 1em" type='textarea' disabled v-model="operateCommand['restart']"/>
        <!--<Button style="margin-left: 1em;" ghost type="primary" size="small" @click="save_cmd('restart')">保存</Button>-->
      </div>
      <div style="margin-top: 1em">
        <p align="right" style="font-size: 1em; width: 4em; display:inline-block;">停止</p>
        <Input style="width: 8em; padding-left: 1em" disabled v-model="operateCommand['stop_func']"/>
        <Input style="width: 32em; padding-left: 1em" type='textarea' disabled v-model="operateCommand['stop']"/>
        <!--<Button style="margin-left: 1em;" ghost type="primary" size="small" @click="save_cmd('stop')">保存</Button>-->
      </div>
      <div style="margin-top: 1em">
        <p align="right" style="font-size: 1em; width: 4em; display:inline-block;">回滚</p>
        <Input style="width: 8em; padding-left: 1em" disabled v-model="operateCommand['rollback_func']"/>
        <Input style="width: 32em; padding-left: 1em" disabled type='textarea' v-model="operateCommand['rollback']"/>
        <!--<Button style="margin-left: 1em;" ghost type="primary" size="small" @click="save_cmd('rollback')">保存</Button>-->
      </div>
      <div style="margin-top: 1em">
        <p align="right" style="font-size: 1em; width: 4em; display:inline-block;">配置更新</p>
        <Input style="width: 8em; padding-left: 1em" disabled v-model="operateCommand['update_func']"/>
        <Input style="width: 32em; padding-left: 1em" disabled type='textarea' v-model="operateCommand['update']"/>
        <!--<Button style="margin-left: 1em;" ghost type="primary" size="small" @click="save_cmd('update')">保存</Button>-->
      </div>
      <div style="margin-top: 1em">
        <p align="right" style="font-size: 1em; width: 4em; display:inline-block;">代码更新</p>
        <Input style="width: 8em; padding-left: 1em" disabled v-model="operateCommand['code_update_func']"/>
        <Input style="width: 32em; padding-left: 1em" disabled type='textarea' v-model="operateCommand['code_update']"/>
        <!--<Button style="margin-left: 1em;" ghost type="primary" size="small" @click="save_cmd('code_update')">保存</Button>-->
      </div>
      <div slot="footer">
        <Button @click="cancel">关闭</Button>
      </div>
    </Modal>
  </Card>
</template>

<script>
  import {
    getProdAppData,
    getProdGroup,
    getOpsOperateData,
    getOpsOperateCommand,
    saveOpsOperateCommand,
    doOpsOperate,
    doZaibeiUpdate,
  } from '@/api/ops-service'
  import {getNodeConsistentInfo} from '@/log/log'
  import {formatDateHour} from '@/libs/util'

  export default {
    name: 'ops_publish_page',
    components: {
      // PublishStat
    },
    data() {
      return {
        consistentTime:'',
        consistentNode: true,
        nodeInfo:'',
        zeibei_update_btn: false,
        bat_deploy_btn: true,
        bat_restart_btn: true,
        bat_rollback_btn: true,
        bat_update_btn: true,
        bat_codeupdate_btn: true,
        ops_operate_modal: false,
        info_modal: false,
        window_height: 500,
        historyCont: [],
        operateCommand: {},
        switch_history: '',
        new_group_name: '',
        select_group: '',
        select_ip: '',
        last_deploy: '',
        git_version: '',
        modal_title: '',
        ack_modal: false,
        command_modal: false,
        opt_type: '',
        appSearch: '',
        app_name: '',
        all_app_list: [],
        app_list: [],
        data: [],
        app_data: [],
        envSelect: 0,
        // regionSelect: 0,
        groupSelect: 0,
        health_modal: false,
        health_columns: [
          {
            title: 'IP',
            key: 'ip',
            width: 150
          },
          {
            title: '状态',
            render: (h, params) => {
              return h('div', {
                domProps: {
                  innerHTML: params.row.status
                },
              }, '')
            }
          }
        ],
        health_data: [],
        envList: [
          {
            value: 0,
            label: "全部"
          },
        ],
        // regionList: [
        // {
        // value: 0,
        // label: "全部"
        // },
        // ],
        groupList: [
          {
            value: 0,
            label: "全部"
          },
        ],
        columns: [
          {
            title: '节点地址',
            width: 140,
            render: (h, params) => {
              var git_version_color = '#19be6b'
              if (params.row.is_git_latest === 1) {
                git_version_color = '#ff9900'
              } else if (params.row.is_git_latest === 2) {
                git_version_color = 'black'
              }

              var node_env_color = '#17233d'
              if (params.row.environ == '仿真环境') {
                node_env_color = '#2d8cf0'
              } else if (params.row.environ == '生产环境' && params.row.ip.indexOf('10.11.') > -1) {
                node_env_color = '#19be6b'
              }

              if (params.row.group == 'hd') {
                node_env_color = '#ff9900'
              }

              return h('div', [
                h('p', {
                  style: {
                    display: 'inline',
                    color: node_env_color
                  }
                }, params.row.ip),
                h('a', {
                  attrs: {
                    class: "ivu-icon ivu-icon-ios-information-circle-outline"
                  },
                  style: {
                    color: git_version_color,
                    "font-size": "18px",
                    'margin-left': '3px'
                  },
                  on: {
                    click: () => {
                      this.select_ip = params.row.ip
                      this.git_version = params.row.git_version
                      this.last_deploy = params.row.last_deploy
                      this.show()
                    }
                  },
                }, ''),
              ])
            }
          },
          {
            title: '机房',
            width: 90,
            key: 'region',
            tooltip: true
          },
          {
            title: '组名',
            width: 70,
            key: 'group',
            tooltip: true
          },
          {
            title: '操作',
            render: (h, params) => {
              var default_btn = false;
              var disable_btn = false;
              var log_btn=false;
              if (params.row.group == 'zaibei_not_start') {
                 disable_btn = true;
                log_btn=true;
              } else if (params.row.group == '默认分组') {
                 disable_btn = true;
                 default_btn = true;
                 log_btn=true;
              }
              if(params.row.environ!=='生产环境'&&params.row.environ!=='灾备环境'){
                log_btn=true;
              }
              return h('div', [
                h('Button', {
                  attrs: {
                    class: 'ivu-btn ivu-btn-primary ivu-btn-small ivu-btn-ghost'
                  },
                  props: {
                    disabled: disable_btn
                  },
                  style: {
                    marginRight: '6px'
                  },
                }, [
                  h('Poptip', {
                    props: {
                      confirm: true,
                      transfer: true,
                      title: '发布 (' + params.row.ip + ')',
                      size: 'small'
                    },
                    on: {
                      'on-ok': () => {
                        this.select_group = params.row.name
                        this.opt_type = 'deploy'
                        this.ips = [params.row.ip]
                        this.doOperate()
                      },
                      'on-cancel': () => {
                        this.$Message.info('取消')
                      }
                    }
                  }, '发布+启动')
                ]),
                h('Button', {
                  attrs: {
                    class: 'ivu-btn ivu-btn-primary ivu-btn-small ivu-btn-ghost'
                  },
                  props: {
                    disabled: disable_btn
                  },
                  style: {
                    marginRight: '6px'
                  },
                }, [
                  h('Poptip', {
                    props: {
                      confirm: true,
                      transfer: true,
                      title: '重启 (' + params.row.ip + ')',
                      size: 'small'
                    },
                    on: {
                      'on-ok': () => {
                        this.select_group = params.row.name
                        this.opt_type = 'restart'
                        this.ips = [params.row.ip]
                        this.doOperate()
                      },
                      'on-cancel': () => {
                        this.$Message.info('取消')
                      }
                    }
                  }, '重启')
                ]),
                h('Button', {
                  attrs: {
                    class: 'ivu-btn ivu-btn-primary ivu-btn-small ivu-btn-ghost'
                  },
                  props: {
                    disabled: default_btn
                  },
                  style: {
                    marginRight: '6px'
                  },
                }, [
                  h('Poptip', {
                    props: {
                      confirm: true,
                      transfer: true,
                      title: '停止 (' + params.row.ip + ')',
                      size: 'small'
                    },
                    on: {
                      'on-ok': () => {
                        this.select_group = params.row.name
                        this.opt_type = 'stop'
                        this.ips = [params.row.ip]
                        this.doOperate()
                      },
                      'on-cancel': () => {
                        this.$Message.info('取消')
                      }
                    }
                  }, '停止')
                ]),
                h('Button', {
                  attrs: {
                    class: 'ivu-btn ivu-btn-primary ivu-btn-small ivu-btn-ghost'
                  },
                  props: {
                    disabled: disable_btn
                  },
                  style: {
                    marginRight: '6px'
                  },
                }, [
                  h('Poptip', {
                    props: {
                      confirm: true,
                      transfer: true,
                      title: '回滚 (' + params.row.ip + ')',
                      size: 'small'
                    },
                    on: {
                      'on-ok': () => {
                        this.select_group = params.row.name
                        this.opt_type = 'rollback'
                        this.ips = [params.row.ip]
                        this.doOperate()
                      },
                      'on-cancel': () => {
                        this.$Message.info('取消')
                      }
                    }
                  }, '回滚')
                ]),
                h('Button', {
                  attrs: {
                    class: 'ivu-btn ivu-btn-primary ivu-btn-small ivu-btn-ghost'
                  },
                  props: {
                    disabled: default_btn
                  },
                  style: {
                    marginRight: '6px'
                  },
                }, [
                  h('Poptip', {
                    props: {
                      confirm: true,
                      transfer: true,
                      title: '配置更新 (' + params.row.ip + ')',
                      size: 'small'
                    },
                    on: {
                      'on-ok': () => {
                        this.select_group = params.row.name
                        this.opt_type = 'update'
                        this.ips = [params.row.ip]
                        this.doOperate()
                      },
                      'on-cancel': () => {
                        this.$Message.info('取消')
                      }
                    }
                  }, '配置更新')
                ]),
                h('Button', {
                  attrs: {
                    class: 'ivu-btn ivu-btn-primary ivu-btn-small ivu-btn-ghost'
                  },
                  props: {
                    disabled: default_btn
                  },
                  style: {
                    marginRight: '6px'
                  },
                }, [
                  h('Poptip', {
                    props: {
                      confirm: true,
                      transfer: true,
                      title: '代码更新 (' + params.row.ip + ')',
                      size: 'small'
                    },
                    on: {
                      'on-ok': () => {
                        this.select_group = params.row.name
                        this.opt_type = 'code_update'
                        this.ips = [params.row.ip]
                        this.doOperate()
                      },
                      'on-cancel': () => {
                        this.$Message.info('取消')
                      }
                    }
                  }, '代码更新')
                ]),
                h('a', {
                  attrs: {
                    class: "ivu-icon ivu-icon-ios-eye"
                  },
                  style: {
                    color: 'green',
                    "font-size": "18px",
                  },
                  on: {
                    click: () => {
                      this.operateCommand = {}
                      this.select_ip = params.row.ip
                      this.get_operate_command(this.app_name, this.select_ip)
                      this.command_modal = true
                    }
                  },
                }, ''),
                h('Button', {
                  attrs: {
                    class: 'ivu-btn ivu-btn-primary ivu-btn-small ivu-btn-ghost'
                  },
                  props: {
                    disabled: log_btn
                  },
                  style: {
                    marginRight: '6px'
                  },
                  on: {
                    click: () => {
                      this.renderFunc(params.row.ip, this.app_name);
                    }
                  },
                }, '服务日志')
              ]);
            }
          },
        ],
      }
    },
    watch: {
      // 'appSearch': function(){
      // if ( this.appSearch ) {
      // this.app_list = this.all_app_list.filter(item => item.indexOf(this.appSearch) > -1);
      // } else {
      // this.app_list = this.all_app_list
      // }
      // },
      'envSelect': function () {
        this.switch_bat_opt_btn()
        this.filter_app_data()
      },
      // 'regionSelect': function(){
      // this.switch_bat_opt_btn()
      // this.filter_app_data()
      // },
      'groupSelect': function () {
        this.switch_bat_opt_btn()
        this.filter_app_data()
      },
    },
    methods: {
      //打开日志查看tab
      renderFunc(ip, app_name) {
        let routeData = this.$router.resolve({
          path: "/log",
          query: {
            ip: ip,
            app_name: app_name
          }
        });
        window.open(routeData.href, '_blank');
      },
      //刷新节点是否一致状态
      refreshNodeConsistent() {
        if (this.data.length > 0) {
          let ipList = [];
          for (let nodeInfo of this.data) {
            if(nodeInfo.environ==='生产环境'||nodeInfo.environ==='灾备环境'){
               ipList.push(nodeInfo.ip)
              }
          }
          getNodeConsistentInfo({'ip_list': ipList, 'app_name': this.app_name}).then(res => {
            if (res.data.status === 'success') {
              if (res.data.data.diff_num > 0) {
                this.consistentNode = false;
                this.nodeInfo=res.data.data.res_dict;
              } else {
                this.consistentNode = true;
                this.nodeInfo='';
              }
            } else {
              this.$Message.error(res.data.msg);
              this.consistentNode = false;
            }
            this.consistentTime=formatDateHour(new Date());
          })
        }

      },
      search_apps() {
        let data = {'search': this.appSearch}
        getProdAppData(data).then(res => {
          if (res.data.code === 0) {
            this.all_app_list = res.data.data
            this.app_list = this.all_app_list
          } else {
            this.$Message.error('获取应用列表失败');
          }
        })
      },
      save_cmd(opt_type) {
        if (this.$store.state.user.team !== 'app' || this.$store.state.user.role !== 'ops') {
          this.$Message.error("请运维人员进行操作");
          return
        }
        let data = {
          'opt_type': opt_type,
          'minion_id': this.operateCommand['minion_id'],
          'exec_cmd': this.operateCommand[opt_type],
          'app_name': this.app_name,
          'salt_func': this.operateCommand[opt_type + '_func'],
        }
        saveOpsOperateCommand(data).then(res => {
          if (res.data.code === 0) {
            this.$Message.success(res.data.msg)
          } else {
            this.$Message.error(res.data.msg)
          }
        })
      },
      switch_bat_opt_btn() {
        // if (this.envSelect != 0 && this.groupSelect !=0 && this.regionSelect !=0){
        if (this.envSelect != 0 && this.groupSelect != 0) {
          if (this.groupList[this.groupSelect].label === '默认分组') {
            this.bat_deploy_btn = true
            this.bat_restart_btn = true
            this.bat_rollback_btn = true
            this.bat_update_btn = true
            this.bat_codeupdate_btn = true
          } else if (this.groupList[this.groupSelect].label === 'not_start') {
            this.bat_deploy_btn = true
            this.bat_restart_btn = true
            this.bat_rollback_btn = true
            this.bat_update_btn = false
            this.bat_codeupdate_btn = false
          } else {
            this.bat_deploy_btn = false
            this.bat_restart_btn = false
            this.bat_rollback_btn = false
            this.bat_update_btn = false
            this.bat_codeupdate_btn = false
          }
        } else {
          this.bat_deploy_btn = true
          this.bat_restart_btn = true
          this.bat_rollback_btn = true
          this.bat_update_btn = true
          this.bat_codeupdate_btn = true
        }
      },
      show_prod_health() {
        this.health_modal = true
        this.initWebSocket()
      },
      health_cancel() {
        this.health_modal = false
        this.closeWebSocket()
      },
      // doMultiOperate (opt_type) {
      // ips = []
      // for (let n=0; n<this.data.length; n++) {
      // ips.push(this.data[n]['ip'])
      // }
      // this.doOpsOperate(ips, opt_type)
      // },
      doOperate() {
        let data = {
          'app_name': this.app_name,
          'ips': this.ips,
          'optType': this.opt_type
        }
        doOpsOperate(data).then(res => {
          if (res.data.code === 0) {
            this.$Message.info(res.data.msg)
            this.history()
          } else {
            this.$Message.error(res.data.msg)
          }
        })
      },
      show() {
        this.info_modal = true
      },
      multi_ack_show(opt_type) {
        var ips = []
        for (let n = 0; n < this.data.length; n++) {
          ips.push(this.data[n]['ip'])
        }
        this.ips = ips
        this.opt_type = opt_type
        this.ack_modal = true
      },
      ack_show() {
        this.ack_modal = true
      },
      init_modal() {
        this.new_group_name = ''
        this.opt_type = ''
        this.ack_modal = false
        this.command_modal = false
        this.ops_operate_modal = false
        this.info_modal = false
        if (this.switch_history) {
          clearInterval(this.switch_history)
        }
      },
      cancel() {
        this.init_modal()
      },
      get_history_data() {
        getOpsOperateData({'app_name': this.app_name}).then(res => {
          if (res.data.code === 0) {
            this.historyCont = res.data.data
          } else {
            this.historyCont = []
          }
        })
      },
      get_operate_command(app_name, ip) {
        getOpsOperateCommand({'app_name': app_name, 'ip': ip}).then(res => {
          if (res.data.code === 0) {
            this.operateCommand = res.data.data
          } else {
            this.operateCommand = {}
          }
        })
      },
      history() {
        this.ops_operate_modal = true
        this.get_history_data()
        this.switch_history = setInterval(this.get_history_data, 5000)
      },
      zeibei_update() {
        this.zeibei_update_btn = true
        doZaibeiUpdate({'app_name': this.app_name}).then(res => {
          if (res.data.code === 0) {
            this.$Message.success(res.data.msg);
          } else {
            this.$Message.error(res.data.msg);
          }
          this.zeibei_update_btn = false
        })
      },
      init_filter() {
        this.envList = [{value: 0, label: "全部"}]
        // this.regionList = [{value: 0, label: "全部"}]
        this.groupList = [{value: 0, label: "全部"}]
        this.envSelect = 0
        // this.regionSelect = 0
        this.groupSelect = 0
      },
      filter_app_data() {
        let tmp_data = []
        let match_list = []

        if (this.envSelect) {
          match_list.push(['environ', this.envList[this.envSelect]['label']])
        }
        // if (this.regionSelect) {
        // match_list.push(['region', this.regionList[this.regionSelect]['label']])
        // }
        if (this.groupSelect) {
          match_list.push(['group', this.groupList[this.groupSelect]['label']])
        }

        for (let n = 0; n < this.app_data.length; n++) {
          let flag = true
          for (let m = 0; m < match_list.length; m++) {
            if (this.app_data[n][match_list[m][0]] !== match_list[m][1]) {
              flag = false
            }
          }
          if (flag) {
            tmp_data.push(this.app_data[n])
          }
        }

        this.data = tmp_data
      },
      append_filter_item(the_list, the_item) {
        let flag = true
        for (let n = 0; n < the_list.length; n++) {
          if (the_list[n]['label'] === the_item) {
            flag = false
          }
        }
        if (flag) {
          the_list.push({'value': the_list.length, 'label': the_item})
        }
      },
      appGetInfo() {
        let vm = this
        vm.init_filter()
        getProdGroup({'appName': vm.app_name}).then(res => {
          if (res.data.code === 0) {
            vm.app_data = res.data.data.nodes
            vm.data = vm.app_data
            for (let n = 0; n < vm.data.length; n++) {
              vm.append_filter_item(vm.envList, vm.data[n]['environ'])
              // vm.append_filter_item(vm.regionList, vm.data[n]['region'])
              vm.append_filter_item(vm.groupList, vm.data[n]['group'])
            }
            this.refreshNodeConsistent();
          } else {
            vm.data = []
            vm.$Message.error(res.data.msg);
          }
        }).catch(function (err) {
          console.log(err)
          vm.data = []
          vm.$Message.error('发生异常')
        })
      },
      clickPanel(val) {
        if (val.length) {
          this.app_name = val
          this.appGetInfo()
        }
      },
      initWebSocket() {
        let socket_host = this.$store.state.socket_host + '/java_health'
        let socket = new WebSocket('ws://' + socket_host + '?app_name=' + this.app_name + '&app_type=prod')
        this.socket = socket
        let vm = this
        vm.health_data = []
        socket.onopen = function open() {
          console.log('WebSockets connection created.')
        }
        socket.onmessage = function message(event) {
          var result = JSON.parse(event.data)
          if (result.code === 0) {
            if (result.data) {
              vm.health_data = JSON.parse(result.data)
            }
          } else {
            vm.$Message.error(result.msg)
          }
        }
        socket.onclose = function () {
          console.log("Disconnected to socks socket")
        }
        if (socket.readyState === WebSocket.OPEN) {
          socket.onopen()
        }
      },
      closeWebSocket() {
        try {
          this.socket.close()
          this.socket = null
        } catch (error) {
        }
      },
    },
    beforeMount() {
      this.window_height = window.innerHeight - 220
    },
    mounted() {
      getProdAppData({}).then(res => {
        if (res.data.code === 0) {
          this.all_app_list = res.data.data
          this.app_list = this.all_app_list
        } else {
          this.$Message.error('获取应用列表失败');
        }
      })
    },
    destroyed() {
    }
  }
</script>

<style scoped lang="less">
  .message-page {
    &-con {
      // height: ~"calc(100vh - 176px)";
      min-height: 500;
      display: inline-block;
      vertical-align: top;
      position: relative;

      &.message-category-con {
        border-right: 1px solid #e6e6e6;
        width: 18em;
        height: auto;
      }

      &.message-view-con {
        position: absolute;
        left: 21em;
        top: 1em;
        right: 1em;
        bottom: 2em;
        overflow: auto;
        padding: 1em 1em 0;

        .message-view-header {
          margin-bottom: 20px;

          .message-view-title {
            display: inline-block;
          }

          .message-view-time {
            margin-left: 20px;
          }
        }
      }
    }
  }
</style>
