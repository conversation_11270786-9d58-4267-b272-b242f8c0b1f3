<template>
  <card>
    <h3>仿真SQL执行结果</h3>
    <br>
    <p>&nbsp;&nbsp;{{ message }}</p>
  </card>
</template>

<script>
import { SimulateSQLExecuted } from '@/api/ops-service'

export default {
  name: 'simulate_sql_executed',
  data () {
    return {
      message: '请稍等...'
    }
  },
  methods: {},
  mounted () {
    let data = {}
    data.business_id = this.$route.query.business_id
    SimulateSQLExecuted(data).then(res => {
      this.message = res.data
      this.$Message.info(this.message)
    })
  }
}
</script>

<style scoped>
</style>
