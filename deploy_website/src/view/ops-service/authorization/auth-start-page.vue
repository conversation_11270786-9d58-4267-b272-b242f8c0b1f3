<template>

  <div>
    <Tabs>
      <TabPane label="角色分配" icon="ios-folder">
        <RoleAssignment></RoleAssignment>
      </TabPane>
      <TabPane label="权限分组" icon="ios-construct">
        <PermissionGroup></PermissionGroup>
      </TabPane>
      <TabPane label="角色授权"  icon="md-alarm">
        <RoleAuthorization></RoleAuthorization>
      </TabPane>
    </Tabs>
  </div>

</template>

<script>
  import { compileHistory } from '@/api/data'
  import RoleAssignment from '_c/role_assignment'
  import PermissionGroup from '_c/permission_group'
  import RoleAuthorization from '_c/role_authorization'
  export default {
    name: 'authorization',
    components: {
      RoleAssignment,
      RoleAuthorization,
      PermissionGroup
    },
    data () {
      return {

      }
    },
    methods: {

    },
    watch: {

    },
    created () {

    },
  }
</script>

<style lang="less">

</style>
