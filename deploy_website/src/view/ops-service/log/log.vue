<template>
  <Row>
    <i-col span="24" style="background-color: black;">
      <p> {{ip}}</p>
      <p>日志路经：{{logPath}} </p>
      <p>java进程已存在，进程ID：{{processId}} </p>
      <p>应用包：{{appPage}} 更新时间：{{updateTime}}</p>
    </i-col>
    <i-col span="24">
      <div style="color: green;background-color: black;overflow:auto;height:90vh;" id="dialogue_box">
        <p v-for="(item,i) in logInfoList">
          {{item}}
        </p>
      </div>
    </i-col>
  </Row>
</template>

<script>
  import {getSaltLogInfo} from '@/log/log'

  export default {
    name: 'logInfo',
    components: {},
    data() {
      return {
        ip: this.$route.query.ip,
        app_name: this.$route.query.app_name,
        logPath: '',
        processId: '',
        appPage: '',
        updateTime: '',
        line_num: -1,
        logInfoList: [],
        logInterval: null,
        intervalNum: 0,
        intervalErrerNum: 0
      }
    },
    methods: {
      //日志加载及刷新
      refreshLogInfo() {
        getSaltLogInfo({'ip': this.ip, 'app_name': this.app_name, 'line_num': this.line_num}).then(res => {
          if (res.data.status === 'success') {
            this.line_num = res.data.data.line_num;
            this.logInfoList = this.logInfoList.concat(res.data.data.log_list);
            this.processId = res.data.data.process_id
            this.logPath = res.data.data.log_path
            this.appPage =  res.data.data.package_path
            this.updateTime = res.data.data.package_time
            this.intervalNum++;
          } else if (res.data.status === 'dicey') {
            this.$Notice.warning({
              title: '信息告知',
              desc: res.data.msg,
              duration: 0
            });
          } else {
            clearInterval(this.logInterval);
            this.$Notice.error({
              title: '信息告知',
              desc: res.data.msg,
              duration: 0
            });
          }
        }).catch(res => {
          this.intervalErrerNum++;
        })
      },
      //日志下拉条保持最底部
      scrollBottom() {
        this.$nextTick(function () {
          let div = document.getElementById('dialogue_box');
          div.scrollTop = div.scrollHeight;
        })
      },
    },
    watch: {
      'logInfoList': function () {
        this.scrollBottom();
      },
      'intervalNum': function () {
        let totNum = 15 * 60 / 2;
        if (this.intervalNum >= totNum) {
          clearInterval(this.logInterval);
          this.$Notice.error({
            title: '信息告知',
            desc: '服务器连接时间超过15分钟，已自动断开连接！！',
            duration: 0
          });
        }
      },
      'intervalErrerNum': function () {
        if (this.intervalErrerNum >= 10) {
          clearInterval(this.logInterval);
          this.$Notice.error({
            title: '信息告知',
            desc: '服务器连接错误，请联系冯伟敏！！',
            duration: 0
          });
        }
      },
    },
    created() {
      let vm = this;
      this.logInterval = setInterval(function () {
        vm.refreshLogInfo()
      }, 2000)

    },
  }
</script>

<style lang="less">

</style>
