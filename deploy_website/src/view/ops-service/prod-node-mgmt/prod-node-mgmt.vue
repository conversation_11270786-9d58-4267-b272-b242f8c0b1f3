<template>
  <div>
    <Input prefix="ios-search" size="large" placeholder="节点地址" v-model="nodeSearch" style="margin-left:10px; width:160px"/>
    <Input prefix="ios-search" size="large" placeholder="应用名称" v-model="appSearch" style="margin-left:10px; width:260px"/>
    <Page style="float:right; margin-right:10px;" :total="total" :current="page" @on-change="changePage" show-total></Page>
    <Table :loading="loading" style="margin: 6px" :columns="columns" :data="data"></Table>
    <Page style="float:right; margin-right:10px;" :total="total" :current="page" @on-change="changePage" show-total></Page>
    <Modal
      v-model="modal1"
      title="删除节点"
      @on-ok="ok"
      @on-cancel="cancel">
      <p>ID: {{ m_pk }}</p>
      <p>节点地址: {{ m_ip }}</p>
      <p>应用名称: {{ m_app_name }}</p>
      <p>确定删除?</p>
    </Modal>
  </div>
</template>

<script>
// import PublishStat from '_c/publish-stat'
import {
  getProdNodeData,
  setProdNodeData,
  delProdNodeData
} from '@/api/ops-service'

export default {
  name: 'prod_node_mgmt_page',
  components: {
    // PublishStat
  },
  data () {
    return {
      loading: true,
      modal1: false,
      page: 1,
      pageSize: 10,
      input_name: '',
      input_pk: '',
      input_put_name: '',
      input_put_pk: '',
      m_ip: '',
      m_app_name: '',
      m_pk: '',
      nodeSearch: '',
      appSearch: '',
      app_groups: {},
      data: [],
      total: 0,
      pageData: [],
      list: [],
      columns: [
        {
          title: 'ID',
          key: 'pk',
          width: 80,
          sortable: true
        },
        {
          title: '节点地址',
          width: 160,
          key: 'ip'
        },
        {
          title: '应用名称',
          width: 200,
          key: 'app_name'
        },
        {
          title: '组名',
          width: 180,
          render: (h, params) => {
            let tags = ['不分组', '现有', '新增'];
            let op_grp = [];
            let op_list = [];

            if ( ! params.row.cur_group ) {
              params.row.cur_group = '无'
            }

            params.row.exist_groups.forEach( item => {
              let vnode = h('Option', {
                props: {
                  value: item
                }
              });
              op_list.push(vnode)
            });

            tags.forEach( item => {
              if ( item === '不分组' ) {
                let vnode = h('OptionGroup', {
                  props: {
                    label: '不分组'
                  },
                }, [
                  h('Option', {
                    props: {
                      value: '无',
                    },
                  })
                ]);
                op_grp.push(vnode)
              } else if ( item === '现有' ) {
                let vnode = h('OptionGroup', {
                  props: {
                    label: '现有'
                  },
                }, op_list);
                op_grp.push(vnode)
              } else if ( item === '新增' ) {
                let vnode = h('OptionGroup', {
                  props: {
                    label: '新增'
                  },
                },[
                  h('Option', {
                    props: {
                      value: params.row.new_group,
                    },
                  })
                ]);
                op_grp.push(vnode)
              }
            });

            return h('Select', {
              props: {
                placeholder: params.row.cur_group,
                value: params.row.cur_group,
                transfer: true,
              },
              style: {
                width: '100px'
              },
              on: {
                'on-change': (val) => {
                  this.groupChange(params, val)
                }
              }
            }, op_grp);
          }
        },
        {
          title: '操作',
          key: 'action',
          align: 'left',
          render: (h, params) => {
            return h('div', [
              h('Button', {
                props: {
                  type: 'error',
                  size: 'small'
                },
                style: {
                  // marginRight: '5px'
                },
                on: {
                  click: () => {
                    this.show(params.index)
                  }
                }
              }, '删除'),
            ]);
          }
        }
      ],
    }
  },
  watch: {
    'nodeSearch': function(){
      this.listFilter()
    },
    'appSearch': function(){
      this.listFilter()
    }
  },
  methods: {
    getPageList(p, size) {
      return this.pageData.slice((p - 1) * size, p * size)
    },
    changePage(p) {
      this.page = p;
      this.data = this.getPageList(p, 10)
    },
    show (index) {
      this.m_ip = this.data[index].ip;
      this.m_app_name = this.data[index].app_name;
      this.m_pk = this.data[index].pk;
      this.modal1 = true
    },
    ok () {
      delProdNodeData(this.m_pk).then(res => {
        if (res.data.code === 0) {
          this.remove(this.m_pk)
          this.$Message.success('Success');
        } else {
          this.$Message.error('Failed');
        }
      })
    },
    cancel () {
      this.$Message.info('Cancel');
    },
    getNewGroup (tmp_app_groups) {
      let tmp_new_group = '';
      let new_groups = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I'];
      for (let j = 0; j < new_groups.length; j++) {
        if ( tmp_app_groups.indexOf(new_groups[j]) === -1 ) {
          tmp_new_group = new_groups[j];
          break
        }
      }
      return tmp_new_group
    },
    delGroupFromGroups (tmp_groups, del_group) {
      for(let i = 0; i < tmp_groups.length; i++){
        if (tmp_groups[i] === del_group) {
          tmp_groups.splice(i, 1);
          break
        }
      }
      return tmp_groups
    },
    remove (index) {
      let tmp_app_name = '';
      let tmp_app_groups = [];
      for (let i = 0; i < this.data.length; i++) {
        if (this.data[i].ip === this.m_ip) {
          tmp_app_name = this.data[i].app_name;
          tmp_app_groups = this.delGroupFromGroups(this.data[i].app_groups, this.data[i].cur_group);
          break
        }
      }
      let tmp_new_group = this.getNewGroup(tmp_app_groups);
      this.updateGroupChange(tmp_app_name, tmp_app_groups, tmp_new_group, this.m_ip, '');

      this.data.splice(this.data.findIndex(item => item.pk === index), 1);
      this.list.splice(this.list.findIndex(item => item.pk === index), 1);
      this.total = this.total - 1;
    },
    listFilter() {
      if ( this.nodeSearch && this.appSearch ) {
        this.pageData = this.list.filter(item => item.ip.indexOf(this.nodeSearch) > -1 && item.app_name.indexOf(this.appSearch) > -1);
      } else if ( this.nodeSearch ) {
        this.pageData = this.list.filter(item => item.ip.indexOf(this.nodeSearch) > -1);
      } else if ( this.appSearch ) {
        this.pageData = this.list.filter(item => item.app_name.indexOf(this.appSearch) > -1);
      } else {
        this.pageData = this.list
      }
      this.total = this.pageData.length;
      this.changePage(1)
    },
    updateGroupChange(app_name, new_groups, new_group, node_ip, node_cur_group) {
      let tmp_exist_groups = new Set(new_groups);
      tmp_exist_groups.delete('');

      [this.list, this.pageData, this.data].forEach( function (tmp_list, index) {
        for (let i = 0; i < tmp_list.length; i++) {
          if (tmp_list[i].app_name === app_name) {
            tmp_list[i].new_group = new_group;
            tmp_list[i].app_groups = new_groups;
            tmp_list[i].exist_groups = tmp_exist_groups;
          }
          if (tmp_list[i].ip === node_ip) {
            tmp_list[i].cur_group = node_cur_group;
          }
        }
      })
    },
    groupChange(params, val) {
      if ( val === '无' ) {
        val = ''
      }
      setProdNodeData({'pk': params.row.pk, 'group_name': val}).then(res => {
        if (res.data.code === 0) {
          let tmp_app_name = params.row.app_name;
          let tmp_new_groups = params.row.app_groups;
          tmp_new_groups = this.delGroupFromGroups(tmp_new_groups, params.row.cur_group);
          tmp_new_groups.push(val);
          let tmp_new_group = this.getNewGroup(tmp_new_groups);
          this.updateGroupChange(tmp_app_name, tmp_new_groups, tmp_new_group, params.row.ip, val);
          this.$Message.success('Success');
        } else {
          this.changePage(1);
          this.$Message.error('Failed');
        }
      })
    }
  },
  mounted () {
    getProdNodeData().then(res => {
      this.list = res.data;
      this.pageData = this.list;
      this.total = this.list.length;
      this.changePage(1);
      this.loading = false
    })
  },
  destroyed () {
    console.log('exit prod-node-mgmt')
  }
}
</script>

<style lang="less">
@baseColor: ~"#dc9387";
.countto-page-row{
  height: 200px;
}
.count-to-con{
  display: block;
  width: 100%;
  text-align: center;
}
.count-text{
  font-size: 50px;
  color: @baseColor;
}
.slot-text{
  font-size: 22px;
}
.unit-class{
  font-size: 30px;
  color: @baseColor;
}
</style>
