<template>
  <Card shadow>
    <div>
      <div class="message-page-con message-category-con">
        <Input prefix="ios-search" placeholder="应用名称" v-model="appSearch" style="width:16em; margin-right: 1em"/>
        <Scroll style="margin-top: 1em" :height="window_height">
          <ul class="ivu-menu ivu-menu-light ivu-menu-vertical" v-for="item in app_list" :app_list="app_list" :key="item">
            <li class="ivu-menu-item" @click="clickPanel(item)">{{item}}</li>
          </ul>
        </Scroll>
      </div>
      <div class="message-page-con message-view-con">
        <i-col>
          <Tag>用户组: <span style="color: #2d8cf0">{{this.$store.state.user.role_role}}</span></Tag>
          <Tag>应用权限: <span style="color: #2d8cf0">{{this.$store.state.user.role_project}}</span></Tag>
        </i-col>
        <i-col>
          <Tag><span style="color: #2d8cf0">仿真节点：蓝色</span></Tag>
          <Tag><span style="color: #19be6b">灾备节点：绿色</span></Tag>
          <Tag><span style="color: #17233d">产线节点：黑色</span></Tag>
          <Tag><span style="color: #ff9900">灰度节点：黄色</span></Tag>
        </i-col>
        <div style="margin-bottom: 10px;">
          <Divider>{{app_name}} 组管理</Divider>
          <Button v-if="app_name" style="margin: 5px;" type="success" size="small" @click="show('add')">新建</Button>
          <Table style="margin-top: 10px; width: 320px;" :columns="group_columns" :data="app_groups_show"></Table>
        </div>
        <div>
          <Divider>{{app_name}} 节点分组</Divider>
          <Table style="margin-top: 10px; width: 580px;" :columns="columns" :data="data"></Table>
        </div>
      </div>
    </div>
    <Modal
      v-model="group_modal"
      @on-cancel="cancel"
      @on-ok="ok">
      <p slot="header">
        <span> {{ app_name }} {{ modal_title }} </span>
      </p>
      <div v-if="g_stat==='add'">
        <p style="font-size: 1rem; margin-left: 6rem; margin-right: 1rem; display:inline;"><span style="color: red"> * </span>组名</p>
        <Input style="width: auto" v-model="new_group_name" />
      </div>
      <div v-else-if="g_stat==='del'">
        <p style="font-size: 1rem; margin-left: 6rem; margin-right: 1rem;">是否删除<span style="color: red"> {{select_group}} </span>?</p>
      </div>
      <div v-if="g_stat==='set'">
        <p style="font-size: 1rem; margin-left: 4rem; display:inline;">组名<span style="color: red"> {{select_group}} </span>更名为</p>
        <Input style="width: auto; margin-left: 1rem;" v-model="new_group_name" />
      </div>
      <div v-if="g_stat==='init'">
        <p style="font-size: 1rem; margin-left: 4rem; display:inline;"> {{select_ip}} <span style="color: red"> 完成初始化 </span>?</p>
      </div>
    </Modal>
  </Card>
</template>

<script>
import {
  getProdAppData,
  chgProdAppGroup,
  getProdGroup,
  addProdGroup,
  setProdGroup,
  delProdGroup,
} from '@/api/ops-service'

export default {
  name: 'prod_group_mgmt_page',
  components: {
    // PublishStat
  },
  data () {
    return {
      window_height: 500,
      new_group_name: '',
      select_group: '',
      select_ip: '',
      modal_title: '',
      group_modal: false,
      g_stat: '',
      appSearch: '',
      app_name: '',
      all_app_list: [],
      app_list: [],
      app_groups: [],
      app_groups_show: [],
      data: [],
      columns: [
        {
          title: '节点地址',
          width: 150,
          render: (h, params) => {
            var node_env_color = '#17233d'
            if (params.row.environ == '仿真环境') {
              node_env_color = '#2d8cf0'
            } else if (params.row.environ == '生产环境' && params.row.ip.indexOf('10.11.') > -1) {
              node_env_color = '#19be6b'
            }

            if (params.row.group == 'hd') {
              node_env_color = '#ff9900'
            }

            return h('div', [
              h('p', {
                style: {
                  display: 'inline',
                  color: node_env_color
                }
              }, params.row.ip),
            ])
          }
        },
        {
          title: '机房',
          width: 120,
          key: 'region'
        },
        {
          title: '组名',
          width: 150,
          render: (h, params) => {
            let op_list = [];
            let enable_button = false
            if (params.row.group === '默认分组' ) {
              enable_button = true
            }
            this.app_groups.forEach( item => {
              let vnode = h('Option', {
                props: {
                  value: item.name
                }
              });
              // if (item.name !== "默认分组") {
                // op_list.push(vnode)
              // }
              op_list.push(vnode)
            });

            return h('Select', {
              props: {
                placeholder: params.row.group,
                value: params.row.group,
                transfer: true,
                disabled: enable_button,
              },
              style: {
                width: '100px'
              },
              on: {
                'on-change': (val) => {
                  this.appChangeGroup(params.row.ip, val)
                }
              }
            }, op_list);
          }
        },
        {
          title: '状态',
          render: (h, params) => {
            let enable_button = true;
            let button_name = '已初始化';
            if (params.row.group === '默认分组') {
              enable_button = false
              button_name = '完成初始化'
            }
            return h('div', [
              h('Button', {
                attrs: {
                  class: 'ivu-btn ivu-btn-primary ivu-btn-small ivu-btn-ghost'
                },
                props: {
                  disabled: enable_button
                },
                style: {
                  marginRight: '5px'
                },
                on: {
                  click: () => {
                    this.select_ip = params.row.ip
                    this.show('init')
                  }
                }
              }, button_name),
            ]);
          }
        },
      ],
      group_columns: [
        {
          title: '组名',
          width: 120,
          key: 'name'
        },
        {
          title: '操作',
          render: (h, params) => {
            return h('div', [
              h('Button', {
                props: {
                  type: 'primary',
                  size: 'small',
                  disabled: false
                },
                style: {
                  marginRight: '5px'
                },
                on: {
                  click: () => {
                    this.select_group = params.row.name
                    this.show('set')
                  }
                }
              }, '编辑'),
              h('Button', {
                props: {
                  type: 'error',
                  size: 'small',
                  disabled: false
                },
                style: {
                  marginRight: '5px'
                },
                on: {
                  click: () => {
                    this.select_group = params.row.name
                    this.show('del')
                  }
                }
              }, '删除'),
            ]);
          }
        },
      ],
    }
  },
  watch: {
    'appSearch': function(){
      if ( this.appSearch ) {
        this.app_list = this.all_app_list.filter(item => item.indexOf(this.appSearch) > -1);
      } else {
        this.app_list = this.all_app_list
      }
    }
  },
  methods: {
    show (val) {
      if (val === 'add') {
        this.modal_title = '组新建'
        this.g_stat = val
      } else if (val === 'set') {
        this.modal_title = '组更名'
        this.g_stat = val
      } else if (val === 'del') {
        this.modal_title = '组删除'
        this.g_stat = val
      } else if (val === 'init') {
        this.modal_title = '完成初始化'
        this.g_stat = val
      }
      this.group_modal = true
    },
    init_modal (){
      this.new_group_name = ''
      this.select_group = ''
      this.select_ip = ''
      this.g_stat = ''
      this.group_modal = false
    },
    appAddGroup (){
      if (this.$store.state.user.team !== 'app' || this.$store.state.user.role !== 'ops') {
        this.$Message.error("请运维人员进行操作");
        return
      }
      var data = {
        'appName': this.app_name,
        'group_name': this.new_group_name
      }
      addProdGroup(data).then(res => {
        if (res.data.code === 0) {
          this.$Message.success(res.data.msg);
        } else {
          this.$Message.error(res.data.msg);
        }
      })
    },
    appSetGroup (){
      if (this.$store.state.user.team !== 'app' || this.$store.state.user.role !== 'ops') {
        this.$Message.error("请运维人员进行操作");
        return
      }
      var data = {
        'appName': this.app_name,
        'cur_name': this.select_group,
        'new_name': this.new_group_name
      }
      setProdGroup(data).then(res => {
        if (res.data.code === 0) {
          this.$Message.success(res.data.msg);
        } else {
          this.$Message.error(res.data.msg);
        }
      })
    },
    appDelGroup (){
      if (this.$store.state.user.team !== 'app' || this.$store.state.user.role !== 'ops') {
        this.$Message.error("请运维人员进行操作");
        return
      }
      var data = {
        'appName': this.app_name,
        'group_name': this.select_group
      }
      delProdGroup(data).then(res => {
        if (res.data.code === 0) {
          this.$Message.success(res.data.msg);
        } else {
          this.$Message.error(res.data.msg);
        }
      })
    },
    appInitNode (){
      if (this.$store.state.user.team !== 'app' || this.$store.state.user.role !== 'ops') {
        this.$Message.error("请运维人员进行操作");
        return
      }
      var data = {
        'appName': this.app_name,
        'ip': this.select_ip,
        'toGroup': 'ready'
      }
      chgProdAppGroup(data).then(res => {
        if (res.data.code === 0) {
          this.$Message.success(res.data.msg);
        } else {
          this.$Message.error(res.data.msg);
        }
      }).catch(function (err){
        vm.$Message.error('发生异常')
      })
    },
    ok () {
      if (this.g_stat === 'add') {
        this.appAddGroup()
      } else if (this.g_stat === 'set') {
        this.appSetGroup()
      } else if (this.g_stat === 'del') {
        this.appDelGroup()
      } else if (this.g_stat === 'init') {
        this.appInitNode()
      }
      let vm = this
      setTimeout(function(){vm.appGetInfo()}, 400)
      this.init_modal()
    },
    cancel () {
      this.init_modal()
    },
    appGetInfo () {
      var data = {'appName': this.app_name}
      getProdGroup(data).then(res => {
        if (res.data.code === 0) {
          this.app_groups = res.data.data.groups
          this.app_groups_show = []
          for (let n=0; n < this.app_groups.length; n++) {
            let name = this.app_groups[n].name
            if (name === "ready" || name === "默认分组") {
              console.log(this.app_groups[n])
            } else {
              this.app_groups_show.push(this.app_groups[n])
            }
          }
          this.data = res.data.data.nodes
        } else {
          this.app_groups = []
          this.data = []
          this.$Message.error(res.data.msg);
        }
      }).catch(function (err){
        this.app_groups = []
        this.data = []
        vm.$Message.error('发生异常')
      })
    },
    clickPanel(val) {
      if (val.length) {
        this.app_name = val
        this.appGetInfo()
      }
    },
    appChangeGroup(ip, toGroup) {
      if (this.$store.state.user.team !== 'app' || this.$store.state.user.role !== 'ops') {
        this.$Message.error("请运维人员进行操作");
        return
      }
      var data = {
        'appName': this.app_name,
        'ip': ip,
        'toGroup':toGroup
      }
      chgProdAppGroup(data).then(res => {
        if (res.data.code === 0) {
          this.$Message.success(res.data.msg);
        } else {
          this.$Message.error(res.data.msg);
        }
      }).catch(function (err){
        vm.$Message.error('发生异常')
      })
      let vm = this
      setTimeout(function(){vm.appGetInfo()}, 300)
    }
  },
  beforeMount () {
    this.window_height = window.innerHeight - 220
  },
  mounted () {
    getProdAppData({}).then(res => {
      if (res.data.code === 0) {
        this.all_app_list = res.data.data
        this.app_list = this.all_app_list
      } else {
        this.$Message.error('获取应用列表失败');
      }
    })
  },
  destroyed () {
  }
}
</script>

<style scoped lang="less">
.message-page{
  &-con{
    // height: ~"calc(100vh - 176px)";
    min-height: 500;
    display: inline-block;
    vertical-align: top;
    position: relative;
    &.message-category-con{
      border-right: 1px solid #e6e6e6;
      width: 18em;
      height: auto;
    }
    &.message-view-con{
      position: absolute;
      left: 21em;
      top: 1em;
      right: 1em;
      bottom: 2em;
      overflow: auto;
      padding: 1em 1em 0;
      .message-view-header{
        margin-bottom: 20px;
        .message-view-title{
          display: inline-block;
        }
        .message-view-time{
          margin-left: 20px;
        }
      }
    }
  }
}
</style>
