<template>
  <card>
    <But<PERSON> ghost @click="show_add" type="success" style="margin:10px;">
      新增
      <Icon type="ios-add-circle-outline"></Icon>
    </Button>
    <Table :loading="loading" :columns="columns" :data="data"></Table>
    <Modal
      v-model="modal_script">
      <p slot="header" style="color:gray;">
        <Icon type="md-clipboard"></Icon>
        <span> {{ m_title }} </span>
      </p>
      <h4 v-if="m_pk" style="color: black; margin:8px;">ID: {{ m_pk }}</h4>
      <p style="color: darkblue; display:inline;"><span style="color: red"> * </span>关联模块</p>
      <Input style="margin: 5px;" v-model="m_mod" size="large" />
      <p style="color: darkblue; display:inline;"><span style="color: red"> * </span>执行服务器</p>
      <Input style="margin: 5px;" v-model="m_ip" size="large" />
      <p style="color: darkblue; display:inline;"><span style="color: red"> * </span>团队</p>
      <Input style="margin: 5px;" v-model="m_ft" size="large" />
      <p style="color: darkblue; display:inline;"><span style="color: red"> * </span>命令行</p>
      <Input style="margin: 5px;" v-model="m_cmd" size="large" type='textarea' autosize />
      <div slot="footer">
        <Button type="primary" @click="ack">确定</Button>
        <Button @click="cancel">关闭</Button>
      </div>
    </Modal>
  </card>
</template>

<script>
import {
  getScriptManagementData,
  setScriptManagementData,
  addScriptManagementData
} from '@/api/ops-service'

export default {
  name: 'script_management',
  data() {
    return {
      data: [],
      m_title: '',
      m_pk: '',
      m_ip: '',
      m_cmd: '',
      m_ft: '',
      m_mod: '',
      allowAction: true,
      modal_script: false,
      loading: true,
      columns: [
        {
          title: 'ID',
          key: 'pk',
          width: 50,
        },
        {
          title: '关联模块',
          key: 'mod',
          width: 180
        },
        {
          title: '执行服务器',
          key: 'ip',
          width: 130
        },
        {
          title: '团队',
          key: 'ft',
          width: 110,
        },
        {
          title: '命令行',
          key: 'cmd',
          width: 360,
        },
        {
          title: '操作',
          key: 'action',
          align: 'left',
          render: (h, params) => {
            return h('div', [
              h('Button', {
                props: {
                  type: 'primary',
                  disabled: this.allowAction,
                  size: 'small'
                },
                style: {
                  // marginRight: '5px'
                },
                on: {
                  click: () => {
                    this.show(params.index)
                  }
                }
              }, '编辑'),
            ]);
          }
        }
      ],
    }
  },
  methods: {
    ack () {
      let data = {
        'id': this.m_pk,
        'ip': this.m_ip,
        'ft': this.m_ft,
        'cmd': this.m_cmd,
        'mod': this.m_mod
      }
      if (this.m_title === '编辑脚本') {
        setScriptManagementData(data).then(res => {
          if (res.data.code === 0) {
            this.initThisVue()
            this.$Message.success('Success')
          } else {
            this.$Message.error(res.data.msg)
          }
        })
      } else {
        addScriptManagementData(data).then(res => {
          if (res.data.code === 0) {
            this.initThisVue()
            this.$Message.success('Success')
          } else {
            this.$Message.error(res.data.msg)
          }
        })
      }
      this.modal_script = false
    },
    cancel () {
      this.$Message.info('Cancel')
      this.modal_script = false
    },
    show_add () {
      this.m_title = '添加脚本'
      this.m_pk = ''
      this.m_ip = ''
      this.m_mod = ''
      this.m_ft = ''
      this.m_cmd = ''
      this.modal_script = true
    },
    show (index) {
      this.m_title = '编辑脚本'
      this.m_pk = this.data[index].pk
      this.m_ip = this.data[index].ip
      this.m_mod = this.data[index].mod
      this.m_ft = this.data[index].ft
      this.m_cmd = this.data[index].cmd
      this.modal_script = true
    },
    initThisVue () {
      if (this.$store.state.user.access.indexOf('admin') > -1) {
        this.allowAction = false
      }

      getScriptManagementData().then(res => {
        this.data = res.data.data
        this.loading = false
      })
    }
  },
  mounted () {
    this.initThisVue()
  },
  destroyed () {

  }
}
</script>

<style scoped>

</style>
