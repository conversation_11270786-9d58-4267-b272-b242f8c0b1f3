<template>
  <card>
    <h3>产线SQL执行结果</h3>
    <br>
    <p>&nbsp;&nbsp;{{ message }}</p>
  </card>
</template>

<script>
import { ReleaseSQLExecuted } from '@/api/ops-service'

export default {
  name: 'release_sql_executed',
  data () {
    return {
      message: '请稍等...'
    }
  },
  methods: {},
  mounted () {
    let data = {}
    data.business_id = this.$route.query.business_id
    ReleaseSQLExecuted(data).then(res => {
      this.message = res.data
      this.$Message.info(this.message)
    })
  }
}
</script>

<style scoped>
</style>
