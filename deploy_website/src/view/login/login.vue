<style lang="less">
  @import './login.less';
</style>

<template>
  <div class="login">
    <div class="login-con">
      <Card icon="log-in" title="DevOps平台" :bordered="false">
        <div class="form-con">
          <login-form @on-success-valid="handleSubmit"></login-form>
        </div>
        <div>
          <h1><Icon type="ios-color-wand" /> Dev Ops！</h1>
          <p> </p>
        </div>
      </Card>
    </div>
  </div>
</template>

<script>
import LoginForm from '_c/login-form'
import { mapActions } from 'vuex'
export default {
  components: {
    LoginForm
  },
  methods: {
    ...mapActions([  // 从其他文件引入方法 注释by scm
      'handleLogin',
      'getUserInfo'
    ]),
    handleSubmit ({ userName, password }) {
      this.handleLogin({ userName, password }).then(res => {
        if (res) {
            this.$router.push({ // 页面跳转 注释by scm
              name: this.$config.homeName // config文件统一配置 注释by scm
            })
          //})
        } else {
          this.$Message.error('用户名或密码错误!')
        }
      }).catch((e) => {
        this.$Message.error('用户名或密码错误, 请检查修改后重试!');
      });
    }
  }
}
</script>

<style>

</style>
