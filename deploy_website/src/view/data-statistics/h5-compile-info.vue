<template>
  <card>
    <h2 style="margin: 5px">h5编译统计</h2>
    <Table :columns="columns" :data="data"></Table>
    <Page style="margin: 5px;" :page-size=10 :total="total" :current="page" @on-change="changePage" show-total></Page>
  </card>
</template>

<script>
import { getH5CompileInfo } from "@/api/java-compile";

export default {
  name: "h5-compile-info",
  data() {
    return {
      data: [],
      fresh_page: "",
      page: 1,
      total: 1,
      pageSize: 10,
      columns: [
        {
          title: "应用",
          key: "app_type",
          width: 100,
          render: (h, params) => {
            var app_name = ""
            if (params.row.app_type == 'cxg') {
              app_name = '储蓄罐'
            } else {
              app_name = '掌机'
            }
            return h('span', {
            }, app_name)
          }
        },
        {
          title: "状态",
          key: "result",
          width: 100,
          render: (h, params) => {
            let result_color = '#17233d'
            if (params.row.result == 'success') {
              result_color = '#19be6b'
            } else if (params.row.result == 'error') {
              result_color = 'red'
            } else if (params.row.result == 'processing') {
              result_color = '#2d8cf0'
            }
            return h('span', {
              style: {
                color: result_color,
              }
            }, params.row.result)
          }
        },
        {
          title: "拉取",
          key: "pull_time",
          className: 'compile-timer-info',
          width: 80
        },
        {
          title: "编译",
          key: "compile_time",
          className: 'compile-timer-infoII',
          width: 80
        },
        {
          title: "提交",
          key: "commit_time",
          className: 'compile-timer-info',
          width: 80
        },
        {
          title: "环境",
          key: "env",
          width: 120
        },
        {
          title: "编译内容",
          key: "projects",
          width: 150
        },
        {
          title: "操作时间",
          key: "operate_time"
        }
      ],
    };
  },
  watch: {},
  methods: {
    changePage(page) {
      this.page = page;
      this.getTable();
    },
    getTable() {
      getH5CompileInfo(this.page).then(res => {
        this.data = res.data.data
        this.total = res.data.total
        this.current = res.data.current
      });
    }
  },
  mounted() {
    this.getTable()
    this.fresh_page = setInterval(this.getTable, 8000)
  },
  destroyed() {
    clearInterval(this.fresh_page)
  }
};
</script>

<style lang="less">
    .ivu-table .compile-timer-info{
      background-color: #2b85e4;
      color: #fff;
    }
    .ivu-table .compile-timer-infoII{
      background-color: #2db7f5;
      color: #fff;
    }
</style>
