<template>
  <card>
    <i-col span="12">
      <div id="TimeConsume" :style="{width: '500px', height: '300px'}"></div>
    </i-col>
    <i-col span="12">
      <div id="Emergency" :style="{width: '500px', height: '300px'}"></div>
    </i-col>
    <Divider style="margin-top: -15px"/>
    <h2 style="margin: 5px">h5上线包详情</h2>
    <i-col>
      <Input
        prefix="ios-search"
        placeholder="增量版本查询"
        v-model="versionSearch"
        style="margin:5px; width:120px"
      />
      <DatePicker
        v-model="dateSelect"
        type="daterange"
        confirm
        placeholder="选择时间区间"
        @on-ok="handleSelect()"
        style="width: 180px; margin: 5px;"
      ></DatePicker>
      <Select v-model="packSelect" @on-change="handleSelect()" style="width: 100px; margin: 5px;">
        <Option v-for="item in showPack" :value="item.value" :key="item.value">{{ item.label }}</Option>
      </Select>
      <Select v-model="appSelect" @on-change="handleSelect()" style="width:80px; margin: 5px;">
        <Option v-for="item in showApp" :value="item.value" :key="item.value">{{ item.label }}</Option>
      </Select>
    </i-col>
    <i-col>
      <Page style="margin: 5px;" :total="total" :current="page" @on-change="changePage" show-total></Page>
    </i-col>
    <i-col>
      <Table :columns="columns" :data="data"></Table>
    </i-col>
    <i-col>
      <Page style="margin: 5px;" :total="total" :current="page" @on-change="changePage" show-total></Page>
    </i-col>
    <Modal v-model="stat_modal" title="状态变更" @on-ok="ok" @on-cancel="cancel">
      <p>{{m_pack_version}} 是否标记为 {{m_rev_stat}} 上线?</p>
    </Modal>
  </card>
</template>

<script>
import { h5publishInfo, h5StatChange } from "@/api/data";

export default {
  name: "publish_stat_page",
  data() {
    return {
      stat_modal: false,
      m_pack_version: "",
      m_rev_stat: "",
      m_c_stat: 0,
      dateSelect: "",
      date_range: "",
      versionSearch: "",
      packSelect: 1,
      appSelect: 1,
      showPack: [
        {
          value: 1,
          label: "仅全量包"
        },
        {
          value: 0,
          label: "含增量包"
        }
      ],
      showApp: [
        {
          value: 1,
          label: "掌基"
        },
        {
          value: 2,
          label: "储蓄罐"
        }
      ],
      page: 1,
      total: 1,
      pageSize: 10,
      columns: [
        {
          title: "全量版本",
          key: "full_pack_version",
          width: "120px",
          render: (h, params) => {
            if (params.row.color == "red") {
              var app_color = "red";
            } else {
              var app_color = "dark";
            }
            return h("div", [
              h(
                "a",
                {
                  style: {
                    color: app_color
                  },
                  on: {
                    click: () => {
                      this.show(
                        params.row.full_pack_version,
                        params.row.is_emergency
                      );
                    }
                  }
                },
                params.row.full_pack_version + params.row.is_emergency
              )
            ]);
          }
        },
        {
          title: "增量版本",
          key: "update_pack_version",
          width: "100px"
        },
        {
          title: "下载地址",
          key: "download_url",
          width: "380px"
        },
        {
          title: "包大小",
          key: "pack_size",
          width: "100px"
        },
        {
          title: "包占比",
          key: "pack_size_percent",
          width: "100px"
        },
        {
          title: "发布日期",
          key: "publish_date",
          width: "200px"
        }
      ],
      all_data: [],
      data: [],
      pageData: [],
      emergency: [],
      routine: []
    };
  },
  watch: {
    versionSearch: function() {
      this.listFilter();
    }
  },
  methods: {
    getPageList(p, size) {
      return this.pageData.slice((p - 1) * size, p * size);
    },
    listFilter() {
      if (this.versionSearch) {
        this.pageData = this.all_data.filter(
          item => item.update_pack_version.indexOf(this.versionSearch) > -1
        );
      } else {
        this.pageData = this.all_data;
      }
      this.total = this.pageData.length;
      this.changePage(1);
    },
    changePage(p) {
      this.page = p;
      this.data = this.getPageList(p, 10);
    },
    show(pack_version, cur_stat) {
      this.m_pack_version = pack_version;
      if (cur_stat) {
        this.m_rev_stat = "迭代";
        this.m_c_stat = 0;
      } else {
        this.m_rev_stat = "紧急";
        this.m_c_stat = 1;
      }
      this.stat_modal = true;
    },
    ok() {
      var data = {
        'ver': this.m_pack_version,
        'status': this.m_c_stat
      }
      h5StatChange(data)
        .then(res => {
          if (res.data.code === 0) {
            this.handleSelect()
            this.$Message.success(res.data.msg);
          } else {
            this.$Message.error(res.data.msg);
          }
        })
        .catch(function(err) {
          this.$Message.error(err);
        });
    },
    cancel() {
      this.$Message.info("Cancel");
    },
    // 条件筛选
    handleSelect() {
      var drawLine = this.drawLine;
      this.date_range =
        this.getDate(this.dateSelect[0]) +
        "," +
        this.getDate(this.dateSelect[1]);

      var data = {
        is_only_full: this.packSelect,
        app_id: this.appSelect,
        date_range: this.date_range
      };
      h5publishInfo(data)
        .then(res => {
          this.all_data = res.data.h5_ver;
          this.pageData = this.all_data;
          this.total = this.pageData.length;
          this.changePage(1);
          drawLine(
            [res.data.fund_normal, res.data.piggy_normal],
            [res.data.fund_emergency, res.data.piggy_emergency]
          );
        })
        .catch(function(err) {
          drawLine([0, 0], [0, 0]);
        });
    },
    drawLine(routine_list, emergency_list) {
      // 基于准备好的dom，初始化echarts实例
      let TimeConsume = this.$echarts.init(
        document.getElementById("TimeConsume")
      );
      let Emergency = this.$echarts.init(document.getElementById("Emergency"));
      // 绘制图表
      let TimeConsume_option = {
        title: {
          text: "近5个版本打包耗时",
          subtext: "暂为测试数据"
        },
        legend: {
          data: ["Fund", "Piggy"]
        },

        xAxis: {
          type: "category",
          boundaryGap: false,
          data: ["1", "2", "3", "4", "5"]
        },
        yAxis: {
          type: "value",
          axisLabel: {
            formatter: "{value}分钟"
          }
        },
        series: [
          {
            name: "Fund",
            type: "line",
            data: [21, 31, 25, 16, 22],
            markPoint: {
              data: [{ type: "max", name: "最大值" }]
            },
            markLine: {
              data: [{ type: "average", name: "平均值" }]
            }
          },
          {
            name: "Piggy",
            type: "line",
            data: [11, 11, 15, 13, 12],
            markPoint: {
              data: [{ type: "max", name: "最大值" }]
            },
            markLine: {
              data: [{ type: "average", name: "平均值" }]
            }
          }
        ]
      };
      let Emergency_option = {
        title: {
          text: "h5上线统计",
          left: 100
        },
        legend: {
          data: ["日常迭代", "紧急上线"],
          left: 200
        },
        grid: {
          left: 100
        },
        xAxis: {
          type: "value",
          name: "次",
          axisLabel: {
            formatter: "{value}"
          }
        },
        yAxis: {
          type: "category",
          inverse: true,
          data: ["Fund", "Piggy"],
          axisLabel: {
            formatter: function(value) {
              return "{" + value + "| }\n{value|" + value + "}";
            },
            margin: 20,
            rich: {
              value: {
                lineHeight: 30,
                align: "center"
              },
              Fund: {
                height: 40,
                align: "center"
              },
              Piggy: {
                height: 40,
                align: "center"
              }
            }
          }
        },
        series: [
          {
            name: "日常迭代",
            type: "bar",
            data: routine_list,
            label: {
              show: true,
              textBorderColor: "#333",
              textBorderWidth: 1
            }
          },
          {
            name: "紧急上线",
            type: "bar",
            label: {
              show: true,
              textBorderColor: "#333",
              textBorderWidth: 1
            },
            data: emergency_list
          }
        ]
      };
      TimeConsume.setOption(TimeConsume_option);
      Emergency.setOption(Emergency_option);
    },
    getDate(val) {
      var year = val.getFullYear();
      var month = val.getMonth() + 1;
      var day = val.getDate();
      if (month >= 1 && month <= 9) {
        month = "0" + month;
      }
      if (day >= 0 && day <= 9) {
        day = "0" + day;
      }
      return year + "-" + month + "-" + day;
    }
  },
  mounted() {
    var drawLine = this.drawLine;

    var today = new Date();
    var firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
    this.dateSelect = [firstDay, today];
    this.date_range = this.getDate(firstDay) + "," + this.getDate(today);

    var data = {
      is_only_full: this.packSelect,
      app_id: this.appSelect,
      date_range: this.date_range
    };
    h5publishInfo(data)
      .then(res => {
        this.all_data = res.data.h5_ver;
        this.pageData = this.all_data;
        this.total = this.pageData.length;
        this.changePage(1);
        drawLine(
          [res.data.fund_normal, res.data.piggy_normal],
          [res.data.fund_emergency, res.data.piggy_emergency]
        );
      })
      .catch(function(err) {
        drawLine([0, 0], [0, 0]);
      });
  }
};
</script>
<style lang="less">
</style>
