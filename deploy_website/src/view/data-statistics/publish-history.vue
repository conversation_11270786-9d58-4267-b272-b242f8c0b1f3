<template>
  <div>
    <Row :gutter="20">
      <i-col span="24">
        <i-col span="12">
          <h2>上线历史 (默认显示一周数据，时间倒序)</h2>
        </i-col>
        <i-col span="12" style="margin-bottom: 15px">
          <Input prefix="ios-search" placeholder="应用名称" v-model="appSearch" style="width:120px; margin-right:5px"/>
          <DatePicker v-model="dateSelect" type="daterange" confirm placement="bottom-end" placeholder="选择时间区间"
                      @on-change="handleSelect()" style="width: 180px; margin-bottom: 10px; margin-right: 5px;">
          </DatePicker>
          <Select v-model="ftSelect" @on-change="handleSelect()" style="width:100px; margin-right: 5px;" placeholder="选择FT团队">
            <Option v-for="item in ftList" :value="item.value" :key="item.value">{{ item.label }}</Option>
          </Select>
          <Select v-model="statSelect" @on-change="handleSelect()" style="width:100px" placeholder="选择维护状态">
            <Option v-for="item in statList" :value="item.value" :key="item.value">{{ item.label }}</Option>
          </Select>
        </i-col>
      </i-col>
      <i-col span="24">
        <Collapse v-model="publish_details">
          <Panel v-for="item in detail_list" :detail_list="detail_list" :name="item.businessID" :key="item.businessID">
              <Badge :status="isDoneStat[item.isDone]" />
              <span class="title">
                {{ item.bussinessID }}
                <Badge v-if="item.appDetails.sql != '无'" text="sql" class="demo-badge-alone"></Badge>
              </span>
              <p slot="content">
                <table style="font-size: 16px; margin-top: 5px">
                  <thead>
                    <th style="width: 300px; text-align: left">应用名</th>
                    <th style="width: 500px; text-align: left">配置</th>
                  </thead>
                  <tbody>
                    <tr v-for="obj in item.appDetails.appConf" :key="obj.appName">
                      <td  style="border-bottom:1px solid gray;">{{obj.appName}}</td>
                      <td  style="border-bottom:1px solid gray;">{{obj.confModify}}</td>
                    </tr>
                  </tbody>
                </table>
                <table style="font-size:16px; margin-top: 10px">
                  <tr style="text-align: left;">
                    <td style="width: 100px">SQL :</td>
                    <td>{{item.appDetails.sql}}</td>
                  </tr>
                  <tr style="text-align: left;">
                    <td style="width: 100px">功能描述 :</td>
                    <td>{{item.appDetails.description}}</td>
                  </tr>
                  <tr style="text-align: left;">
                    <td style="width: 100px">上线日期 :</td>
                    <td>{{item.appDetails.publishDate}}</td>
                  </tr>
                </table>
                <Button v-if="isDoneStat[item.isDone] === 'processing'" @click="isMaintain(item.bussinessID)" style="margin-top: 10px;" type="success" ghost>
                  维护完成
                </Button>
              </p>
          </Panel>
        </Collapse>
      </i-col>
    </Row>
  </div>
</template>

<script>
  import { getPublishHistoryData, updateMaintainStatus } from '@/api/data_statistics'
  import { getDay } from '@/libs/util'
  export default {
    data () {
      return {
        isDoneStat: {
          '0': 'processing',
          '1': 'success',
          '2': 'default'
        },
        detail_list_origin: [],
        appSearch: '',
        ftSelect: '',
        statSelect: '',
        dateSelect: '',
        ftList: [

        ],
        statList: [
          {
            value: '1',
            label: '已维护'
          },
          {
            value: '0',
            label: '未维护'
          },
        ],
        publish_details: '',
        detail_list: []
      }
    },
    watch: {
      'appSearch': function(){
        this.filter_by_app()
      }
    },
    methods: {
      filter_by_app () {
        if (this.appSearch) {
          let tmp_list = []
          for (let n=0; n<this.detail_list_origin.length; n++) {
            let flag = false
            if (this.detail_list_origin[n]['appDetails']['appConf']) {
              for (let m=0; m<this.detail_list_origin[n]['appDetails']['appConf'].length; m++) {
                if (this.detail_list_origin[n]['appDetails']['appConf'][m]['appName'].indexOf(this.appSearch) > -1) {
                  flag = true
                }
              }
            }
            if (flag) {
              tmp_list.push(this.detail_list_origin[n])
            }
          }
          this.detail_list = tmp_list
        } else {
          this.detail_list = this.detail_list_origin
        }

      },
      // 统一处理该页面两个select项
      handleSelect () {
        let time_zone = this.dateformat(this.dateSelect);
        // 时间筛选
        this.getHistory(time_zone).then(res => {
          if (this.appSearch.length !== 0){
            this.filter_by_app()
          }
          // ft 筛选
          if (this.ftSelect !== 'ALL') {
            this.detail_list = this.detail_list.filter(item => item['bussinessID'].split('_')[0].indexOf(this.ftSelect) > -1)
          }
          // 维护状态筛选
          if (this.statSelect === '1') {
            this.detail_list = this.detail_list.filter(item => item['isDone'].indexOf('2') > -1 || item['isDone'].indexOf('1') > -1)
          } else {
            this.detail_list = this.detail_list.filter(item => item['isDone'].indexOf(this.statSelect) > -1)
          }
        });
      },
      dateformat(data_obj){
        let from_year = data_obj[0].getFullYear();
        let from_monnth = (data_obj[0].getMonth() + 1).toString();
        if (from_monnth.length === 1) {
          from_monnth = '0' + from_monnth
        }
        let from_date = (data_obj[0].getDate()).toString();
        if (from_date.length === 1) {
          from_date = '0' + from_date
        }
        let from = from_year + '-' + from_monnth + '-' + from_date;

        let to_year = data_obj[1].getFullYear();
        let to_monnth = (data_obj[1].getMonth() + 1).toString();
        if (to_monnth.length === 1) {
          to_monnth = '0' + to_monnth
        }
        let to_date = (data_obj[1].getDate()).toString();
        if (to_date.length === 1) {
          to_date = '0' + to_date
        }
        let to = to_year + '-' + to_monnth + '-' + to_date;
        return (from + ' - ' + to)
      },
      getHistory(time_zone) {
        let ft_list = [];
        let tmp_list = [];
        let get_publish_history_data = getPublishHistoryData(time_zone).then(res => {
          this.detail_list_origin = res.data;
          res.data.forEach(business_id => {
            let ft = business_id['bussinessID'].split('_')[0];
            let ft_map = {
              value: ft,
              label: ft
            };
            if (tmp_list.indexOf(ft) === -1) {
              tmp_list.push(ft);
              ft_list.push(ft_map)
            }
          });
          this.ftList = ft_list;
          this.detail_list = this.detail_list_origin
          // 增加all
          this.ftList.push(
            {
              value: 'ALL',
              label: 'ALL'
            }
          );
        });
        return get_publish_history_data
      },
      isMaintain(business_id){
        updateMaintainStatus(business_id).then(res => {
          this.$Message.info(res.data);
          this.handleSelect()
        })
      }
    },
    mounted () {
      let from = getDay(-7);
      let to = getDay(0);
      this.dateSelect = from + ' - ' + to;
      this.getHistory(this.dateSelect)
    }
  }
</script>

<style scoped>
  .demo-badge-alone{
    margin-left: 10px;
  }
  .title{
    font-size: 16px
  }
</style>
