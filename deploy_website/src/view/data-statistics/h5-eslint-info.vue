<template>
  <card>
    <!--<i-col span="12">-->
      <!--<div id="TimeConsume" :style="{width: '500px', height: '300px'}"></div>-->
    <!--</i-col>-->
    <!--<i-col span="12">-->
      <!--<div id="Emergency" :style="{width: '500px', height: '300px'}"></div>-->
    <!--</i-col>-->
    <!--<Divider style="margin-top: -15px"/>-->
    <h2 style="margin: 5px">h5编译报错信息</h2>
    <i-col>
      <!-- <Input

        placeholder="分支版本"
        v-model="versionSearch"
        style="margin:5px; width:120px"
      /> -->

      <Select v-model="versionSearch" filterable style="margin:5px; width:120px">
          <Option v-for="item in all_branch_name"
                  :value="item[0]"
                  :label="item[0]"
                  :key="item[0]">
            {{ item[0] }}</Option>
        </Select>

       <Input

        placeholder="违反规则"
        v-model="ruleSearch"
        style="margin:5px; width:120px"
      />
       <Input

        placeholder="描述信息"
        v-model="desSearch"
        style="margin:5px; width:240px"
      />

      <Button @click="getTable(1)" type="info">查询</Button>

    </i-col>

    <i-col>
      <Page style="margin: 5px;" :total="total" :current="page" @on-change="changePage" show-total></Page>
    </i-col>
    <i-col >
      <Table :columns="columns" :data="data"></Table>
    </i-col>
    <i-col>
      <Page style="margin: 5px;" :total="total" :current="page" @on-change="changePage" show-total></Page>
    </i-col>

  </card>
</template>

<script>
import { h5eslintInfo} from "@/api/data";

export default {
  name: "h5-eslint-info",
  data() {
    return {
      dateSelect: "",
      date_range: "",
      versionSearch: "",
      desSearch:"",
      ruleSearch:"",
      page: 1,
      total: 1,
      pageSize: 10,
      columns: [
        {
          title: "分支名称",
          key: "branch_name"
         },

        {
          title: "key值",
          key: "rule"

        },
        {
          title: "文件路径",
          key: "file_path"

        },
        {
          title: "报错时间",
          key: "error_time"

        },
        {
          title: "报错信息",
          key: "error_info"

        }
      ],
      all_branch_name: [],
      data: [],
      emergency: [],
      routine: []
    };
  },
  watch: {
  },
  methods: {
    changePage(p) {
      this.page = p;
      this.getTable(p)
    },
    ok() {
    },
    cancel() {
      this.$Message.info("Cancel");
    },

    getTable(p){

      let data = {
        "versionSearch": this.versionSearch,
        "desSearch": this.desSearch,
        "date_range": this.date_range,
        "pageSize":this.pageSize,
        "ruleSearch":this.ruleSearch,
        "page":p
      };

      h5eslintInfo(data)
        .then(res => {
          console.log(res.data['table_data']);
          // alert(JSON.stringify(res))
          this.data = res.data['table_data'];
          this.total = res.data['count'];
          this.all_branch_name = res.data['all_branch_name'];
          console.log(this.data)
          console.log(this.total)
        })
    },
  },
  mounted() {
    this.getTable(1)
  }
};
</script>
<style lang="less">
</style>
