<template>
  <card>
     <i-col>
       <DatePicker type="month" placeholder="Select month" v-model="date" style="margin:5px; width:120px"></DatePicker>
             <Button type="primary" @click="handleSubmit()" style="margin:5px; width:120px" :loading="btnDisabled">查询</Button>
       <Button type="primary" size="large" @click="exportData(1)"><Icon type="ios-download-outline"></Icon> 导出表格数据</Button>
    </i-col>
<i-col>
    <h2 style="margin: 5px">共计编译次数 {{total}}；代码扫描报错的编译次数 {{count_error}}。</h2>
   </i-col>
    <i-col>
      <Table ref="table" :columns="columns" :data="data"></Table>
     </i-col>


  </card>
</template>

<script>
  import { getH5Statistics } from "@/api/java-compile";
    export default {
        name: "h5-compile-statistics",
        data() {
    return {
      btnDisabled: false,
      total:"",
      count_error:"",
      date:"",
      columns: [
        {
          title: "key值",
          key: "key_value",


        },
        {
          title: "频次",
          key: "count",
          sortable: true,
          sortType:"desc",

        },
        {
          title: "涉及分支",
          key: "branch_name",

        },
      ],
      data: [],
    };
  },
      methods:{
            add0(m){
              return m<10?'0'+m:m;
          },
          formartDate() {
              var time = new Date();
              var y = time.getFullYear();
              var m = time.getMonth()+1;
              var d = time.getDate();
              var h = time.getHours();
              var mm = time.getMinutes();
              var s = time.getSeconds();
              return y+'-'+this.add0(m)+'-'+this.add0(d)+' '+this.add0(h)+':'+this.add0(mm)+':'+this.add0(s);
          },

          handleSubmit(){
            this.btnDisabled = true
             getH5Statistics(this.date.getFullYear()+"-"+this.date.getMonth()).then(res => {
              // console.log(res.data)
               //console.log(JSON.parse(res.data).data)
               this.data = JSON.parse(res.data).data
               this.total = JSON.parse(res.data).total
               this.count_error = JSON.parse(res.data).count_error
                this.btnDisabled = false
                //console.log(JSON.parse(res.data).data)
               //console.log(this.data)

      });
          },
        exportData (type) {
                if (type === 1) {
                  var str_date = this.formartDate()
                  //this.data=["fafdfd"].concat(this.data)
                    this.$refs.table.exportCsv({
                        filename: str_date,
                        original: false,
                      //data:["fafdfd"].concat(this.data)
                    });
                }
          }
      },
      mounted() {
          this.date = new Date();
          this.handleSubmit()
        }
    }
</script>

<style scoped>

</style>
