<template>
  <card>
    <h2 style="margin: 5px">java编译统计</h2>
    <Table :columns="columns" :data="data"></Table>
    <Page style="margin: 5px;" :page-size=10 :total="total" :current="page" @on-change="changePage" show-total></Page>
    <Modal
      v-model="app_info_modal"
      width="420"
      @on-cancel="cancel">
      <p slot="header" style="color:gray;">
        <Icon type="md-clipboard"></Icon>
        <span> 应用信息</span>
      </p>
      <Card :bordered="false">
        <p v-for="item in app_list" :key="item">{{ item }}</p>
      </Card>
      <div slot="footer">
        <Button @click="cancel">关闭</Button>
      </div>
    </Modal>
  </card>
</template>

<script>
import { getJavaCompileInfo } from "@/api/java-compile";

export default {
  name: "java-compile-info",
  data() {
    return {
      data: [],
      app_info_modal: false,
      app_list: [],
      fresh_page: "",
      page: 1,
      total: 1,
      pageSize: 10,
      columns: [
        {
          title: "迭代版本",
          key: "business_id",
          width: 180,
          render: (h, params) => {
            return h('a', {
                on: {
                  click: () => {
                    this.show_app_info(params.row.compile_id)
                  }
                }
            }, params.row.business_id)
          }
        },
        {
          title: "状态",
          key: "result",
          width: 100,
          render: (h, params) => {
            let result_color = '#17233d'
            if (params.row.result == 'success') {
              result_color = '#19be6b'
            } else if (params.row.result == 'error') {
              result_color = 'red'
            } else if (params.row.result == 'processing') {
              result_color = '#2d8cf0'
            }
            return h('span', {
              style: {
                color: result_color,
              }
            }, params.row.result)
          }
        },
        {
          title: "准备",
          key: "prework_time",
          className: 'compile-timer-info',
          width: 80
        },
        {
          title: "编译",
          key: "compile_time",
          className: 'compile-timer-infoII',
          width: 80
        },
        {
          title: "处理",
          key: "handle_time",
          className: 'compile-timer-info',
          width: 80
        },
        {
          title: "推送",
          key: "push_time",
          className: 'compile-timer-infoII',
          width: 80
        },
        {
          title: "操作人",
          key: "operator",
          width: 120,
          tooltip: true
        },
        {
          title: "操作时间",
          key: "operate_time"
        }
      ],
    };
  },
  watch: {},
  methods: {
    changePage(page) {
      this.page = page;
      this.getTable();
    },
    show_app_info(compile_id) {
      this.app_info_modal = true
      getJavaCompileInfo(this.page, compile_id=compile_id).then(res => {
        this.app_list = res.data
      });
    },
    cancel() {
      this.app_info_modal = false
    },
    getTable() {
      getJavaCompileInfo(this.page).then(res => {
        this.data = res.data.data
        this.total = res.data.total
        this.current = res.data.current
      });
    }
  },
  mounted() {
    this.getTable()
    this.fresh_page = setInterval(this.getTable, 8000)
  },
  destroyed() {
    clearInterval(this.fresh_page)
  }
};
</script>

<style lang="less">
    .ivu-table .compile-timer-info{
      background-color: #2b85e4;
      color: #fff;
    }
    .ivu-table .compile-timer-infoII{
      background-color: #2db7f5;
      color: #fff;
    }
</style>
