<template>
  <div>

    <Tabs v-model="selectedTab">
      <TabPane label="分支创建及管理(内测)" icon="ios-folder" v-if="is_admin">
        <BranchMgmtNew :businessID="businessID" @changeBusinessID="changeBusinessID"></BranchMgmtNew>
      </TabPane>
      <TabPane name="branchMgmt" label="分支管理" icon="ios-folder">
        <BranchMgmt v-if="selectedTab == 'branchMgmt'" :businessID="businessID" @changeBusinessID="changeBusinessID"></BranchMgmt>
      </TabPane>
      <TabPane name="iterativePlan" label="迭代计划" :disabled="tab_disable" icon="md-alarm">
        <IterativePlan v-if="selectedTab == 'iterativePlan'" :businessID="businessID"></IterativePlan>
      </TabPane>
      <TabPane name="javaCompile" label="代码编译" :disabled="tab_disable" icon="ios-construct">
        <JavaCompile v-if="selectedTab == 'javaCompile'" :businessID="businessID"></JavaCompile>
      </TabPane>
      <TabPane name="uatPublish" label="仿真发布" :disabled="tab_disable" icon="ios-cloud-upload">
        <UatPublish v-if="selectedTab == 'uatPublish'" :businessID="businessID"></UatPublish>
      </TabPane>
      <TabPane name="prodPublish" label="产线发布" :disabled="tab_disable" icon="ios-cloud-upload-outline">
        <ProdPublish v-if="selectedTab == 'prodPublish'" :businessID="businessID"></ProdPublish>
      </TabPane>
    </Tabs>

  </div>
</template>

<script>
  import BranchMgmt from '_c/branch-mgmt'
  import BranchMgmtNew from '_c/branch-mgmt-new'
  import JavaCompile from '_c/java-compile'
  import UatPublish from '_c/uat-publish'
  import ProdPublish from '_c/prod-publish'
  import EmailNotify from '_c/email-notify'
  import IterativePlan from '_c/iterative-plan'
  import { compileHistory } from '@/api/data'
  import { mapMutations, mapState } from 'vuex'
  export default {
    name: 'interative_publish',
    components: {
      BranchMgmtNew,
      BranchMgmt,
      UatPublish,
      ProdPublish,
      JavaCompile,
      EmailNotify,
      IterativePlan
    },
    data () {
      return {
        businessID: '',
        tab_disable: true,
        is_admin: false,
        selectedTab: 'branchMgmt',
      }
    },
    methods: {
      changeBusinessID (val) {
        this.businessID = val
      }
    },
    watch: {
      // 监控编辑栏状态
      businessID: {
        handler: function (val) {
          if (val === '' || val === undefined) {
            this.tab_disable = true
          } else {
            this.tab_disable = false
          }
        }
      }
    },
    created () {
      this.businessID = ''
      if (this.$store.state.user.access.indexOf('admin') > -1) {
        this.is_admin = true
      } else {
        this.is_admin = false
      }
    },
  }
</script>

<style lang="less">

</style>
