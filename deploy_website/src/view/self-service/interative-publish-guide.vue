<template>
  <Card>
    <Row style="padding-left: 25px; margin-bottom: -10px">
      <h1>日常迭代发布指南</h1>
    </Row>
    <Divider />
    <Row style="padding-left: 25px; margin-bottom: -10px">
      <h2>分支管理</h2>
      <br>
      <p>发布类型分为迭代和紧急。</p>
      <p>切换发布类型后选择版本，如果不存在，可以进行创建。</p>
      <p>选择了迭代版本才能进行后续操作。</p>
      <br>
    </Row>
    <Row style="padding-left: 25px; margin-bottom: -10px">
      <h3>i) 选择迭代版本</h3>
      <img :src="imgUrl0" />
    </Row>
    <Row style="padding-left: 25px; margin-bottom: -10px">
      <h3>ii) 创建迭代版本</h3>
      <br>
      <p>最终的迭代版本为 团队_版本。</p>
      <br>
      <img :src="imgUrl1" />
    </Row>
    <Row style="padding-left: 25px; margin-bottom: -10px">
      <h3>iii) 创建紧急分支</h3>
      <img :src="imgUrl2" />
    </Row>
    <Divider />
    <Row style="padding-left: 25px; margin-bottom: -10px">
      <h2>代码编译</h2>
      <br>
      <p>点击“配置项编辑”，会列出可编译的模块。</p>
      <p>勾选需要编译的模块，点击“执行”，开始编译。</p>
      <p>详细的编译日志可在执行历史中进行查看。</p>
      <br>
    </Row>
    <Row>
      <img :src="imgUrl3" />
    </Row>
    <Divider />
    <Row style="padding-left: 25px; margin-bottom: -10px">
      <h2>迭代计划</h2>
      <br>
      <p>将变更信息写入迭代计划，点击最下方的“保存变更”进行保存。</p>
      <p>SQL维护、上线完成，可通过邮件方式通知，填入邮件联系人点击邮件按钮即可。</p>
      <br>
    </Row>
    <Row>
      <img :src="imgUrl4" />
    </Row>
    <Divider />
    <Row style="padding-left: 25px; margin-bottom: -10px">
      <h2>仿真发布</h2>
      <br>
      <p>代码成功编译后，会出现可以发布的仿真应用。</p>
      <br>
    </Row>
    <Row>
      <img :src="imgUrl5" />
    </Row>
    <Divider />
    <Row style="padding-left: 25px; margin-bottom: -10px">
      <h2>产线发布</h2>
      <br>
      <p>代码成功编译后，会出现可以发布的产线应用。</p>
      <br>
    </Row>
    <Row>
      <img :src="imgUrl6" />
    </Row>
  </Card>
</template>

<script>
export default {
  name: "ops-publish-guide",
  data() {
    return {
      imgUrl0: require("../../../public/imgs/interative/choose_branch.png"),
      imgUrl1: require("../../../public/imgs/interative/create_branch.png"),
      imgUrl2: require("../../../public/imgs/interative/jinji_branch.png"),
      imgUrl3: require("../../../public/imgs/interative/compile.png"),
      imgUrl4: require("../../../public/imgs/interative/plan.png"),
      imgUrl5: require("../../../public/imgs/interative/uat_publish.png"),
      imgUrl6: require("../../../public/imgs/interative/prod_publish.png")
    };
  }
};
</script>

<style scoped>
</style>
