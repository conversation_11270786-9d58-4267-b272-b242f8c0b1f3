<template>
  <div>
    <div style="margin-bottom: 15px">
      <h2>HOME</h2>
    </div>
    <Card>
      <h3> {{title}} </h3>
      <br>
      <p> {{message}} </p>
      <p> 当前所在spider地址: {{spider_ip}} </p>
      <!-- <tables ref="tables" editable searchable search-place="top" v-model="tableData" :columns="columns" @on-delete="handleDelete"/> -->
    </Card>
  </div>
</template>
<script>
  import Tables from '_c/tables'
  // import { getTableData } from '@/api/data'
  import { SimulateSQLExecuted, ReleaseSQLExecuted } from '@/api/ops-service'
  import {getSpiderIp} from "@/api/user";
  export default {
    name: 'tables_page',
    components: {
      Tables
    },
    data () {
      return {
        title: '',
        message: '',
        spider_ip: '',
        columns: [
          {title: '项目名', key: 'appname'},
          {title: '进度', key: 'stage', sortable: true},
          {
            title: '操作',
            key: 'handle',
            options: ['delete'],
            button: [
              (h, params, vm) => {
                return h('Poptip', {
                  props: {
                    confirm: true,
                    title: '你确定要删除吗?'
                  },
                  on: {
                    'on-ok': () => {
                      vm.$emit('on-delete', params)
                      vm.$emit('input', params.tableData.filter((item, index) => index !== params.row.initRowIndex))
                    }
                  }
                })
              }
            ]
          }
        ],
        tableData: []
      }
    },
    methods: {
      handleDelete (params) {
        console.log(params)
      }
    },
    mounted () {
      // getTableData().then(res => {
        // this.tableData = res.data
      // })
      getSpiderIp().then(res => {
        this.spider_ip = res.data.data.host_ip
      })

      let data = {}
      data.business_id = this.$route.query.business_id
      data.type = this.$route.query.type
      if (data.type == 'uat') {
        this.title = '仿真SQL执行结果'
        this.message = '请稍等...'
        SimulateSQLExecuted(data).then(res => {
          this.message = res.data
          this.$Message.info(this.message)
        })
      } else if (data.type == 'prod') {
        this.title = '产线SQL执行结果'
        this.message = '请稍等...'
        ReleaseSQLExecuted(data).then(res => {
          this.message = res.data
          this.$Message.info(this.message)
        })
      } else {
        this.title = ''
        this.message = ''
      }
    }
  }
</script>

<style>

</style>
