<template>
  <card>
    <h2 style="margin: 10px">应用录入</h2>
    <Row style="margin-left: 1em">
      <i-col style="margin: 1em" span="3">
        <Icon type="md-people" style="margin-right: 1em"/>
        <span style="text-align: left; display: inline-block;">选择团队</span>
      </i-col>
      <i-col style="margin-top: 1em" span="5">
        <Select v-model="team" span="5" style="width: 300px">
          <Option v-for="item in ft_list" :value="item.value" :key="item.value">{{ item.label }}</Option>
        </Select>
      </i-col>
    </Row>

    <Row style="margin-left: 1em">
      <i-col style="margin: 1em" span="3">
        <Icon type="md-git-branch" style="margin-right: 1em"/>
        <span style="text-align: left; display: inline-block;">源码地址</span>
      </i-col>
      <i-col style="margin-top: 1em" span="5">
        <Input type='textarea' v-model="trunk_path" placeholder="填写源码地址" style="width: 300px" />
      </i-col>
    </Row>

    <Button :disabled="parse_btn" type="primary" @click="do_pom_parse" style="margin-top:2em;margin-left:4em;"> 解析 </Button>
    
    <Table :loading="app_load" style="margin-top: 2em" border :columns="app_columns" :data="app_data"></Table>

    <Button type="primary" @click="save_pom_parse" style="margin-top:2em;margin-left:4em;"> 录入 </Button>

    <Modal
      v-model="modal_app">
      <p slot="header" style="color:gray;">
        <Icon type="md-clipboard"></Icon>
        <span> {{m_app_name}} 应用信息编辑 </span>
      </p>
      <div>
        <p align="right" style="font-size: 1rem; width: 5em; display:inline-block;">类型</p>
        <Input style="width: 22em; font-size: 1rem; padding-left: 1em; color: #2d8cf0;" v-model="m_app_type" />
      </div>
      <div style="margin-top: 1em">
        <p align="right" style="font-size: 1rem; width: 5em; display:inline-block;">jar包名</p>
        <Input style="width: 22em; font-size: 1rem; padding-left: 1em; color: #2d8cf0;" v-model="m_jar_name" />
      </div>
      <div style="margin-top: 1em">
        <p align="right" style="font-size: 1rem; width: 5em; display:inline-block;">jdk版本</p>
        <Input style="width: 22em; font-size: 1rem; padding-left: 1em; color: #2d8cf0;" v-model="m_jdk_version" />
      </div>
      <div style="margin-top: 1em">
        <p align="right" style="font-size: 1rem; width: 5em; display:inline-block;">svn路径</p>
        <Input style="width: 22em; font-size: 1rem; padding-left: 1em; color: #2d8cf0;" type='textarea' autosize v-model="m_trunk_path" />
      </div>
      <div style="margin-top: 1em">
        <p align="right" style="font-size: 1rem; width: 5em; display:inline-block;">制品库地址</p>
        <Input style="width: 22em; font-size: 1rem; padding-left: 1em; color: #2d8cf0;" v-model="m_repo_path" />
      </div>
      <div style="margin-top: 1em">
        <p align="right" style="font-size: 1rem; width: 5em; display:inline-block;">团队</p>
        <Input style="width: 22em; font-size: 1rem; padding-left: 1em; color: #2d8cf0;" v-model="m_team" />
      </div>
      <div style="margin-top: 1em">
        <p align="right" style="font-size: 1rem; width: 5em; display:inline-block;">应用编码</p>
        <Input style="width: 22em; font-size: 1rem; padding-left: 1em; color: #2d8cf0;" v-model="m_resource_code" />
      </div>
      <div slot="footer">
        <Button type="primary" @click="app_ack">确定</Button>
        <Button @click="cancel">关闭</Button>
      </div>
    </Modal>
  </card>
</template>

<script>
import {
  getPomParseData,
  savePomParseData,
} from '@/api/ops-service'

export default {
  name: 'team_initial',
  data() {
    return {
      trunk_path: '',
      parse_btn: false,
      app_load: false,
      team: '',
      ft_list: [
        {'value': 'tms', 'label': 'tms'},
        {'value': 'tp', 'label': 'tp'},
        {'value': 'cc', 'label': 'cc'},
        {'value': 'otc', 'label': 'otc'},
        {'value': 'ec', 'label': 'ec'},
        {'value': 'ds', 'label': 'ds'},
        {'value': 'crm', 'label': 'crm'},
        {'value': 'web', 'label': 'web'},
        {'value': 'pa', 'label': 'pa'},
        {'value': 'mojie', 'label': 'mojie'},
        {'value': 'fpc', 'label': 'fpc'},
      ],
      modal_app: false,
      m_app_name: '',
      m_app_type: '',
      m_jar_name: '',
      m_jdk_version: '',
      m_trunk_path: '',
      m_repo_path: '',
      m_team: '',
      m_resource_code: '',
      app_columns: [
        {
          title: '应用名',
          key: 'app_name',
          width: 120,
        },
        {
          title: '类型',
          key: 'app_type',
          width: 70,
        },
        {
          title: 'jar包名',
          key: 'jar_name',
          width: 120,
        },
        {
          title: 'svn路径',
          key: 'trunk_path',
          width: 220,
        },
        {
          title: 'JDK',
          key: 'jdk_version',
          width: 60,
        },
        {
          title: '制品库地址',
          key: 'repo_path',
          width: 120,
        },
        {
          title: '团队',
          key: 'team',
          width: 70,
        },
        {
          title: '应用编码',
          key: 'resource_code',
          width: 110,
        },
        {
          title: '操作',
          key: 'handle',
          render: (h, params) => {
            return h('div', [
              h('Button', {
                attrs: {
                  class: 'ivu-btn ivu-btn-primary ivu-btn-small'
                },
                props: {
                },
                style: {
                  marginRight: '6px'
                },
                on: {
                  click: () => {
                    this.modal_app = true
                    this.m_app_name = params.row.app_name
                    this.m_app_type = params.row.app_type
                    this.m_jar_name = params.row.jar_name
                    this.m_jdk_version = params.row.jdk_version
                    this.m_trunk_path = params.row.trunk_path
                    this.m_repo_path = params.row.repo_path
                    this.m_team = params.row.team
                    this.m_resource_code = params.row.resource_code
                  }
                }
              }, '编辑'),
              h('Button', {
                attrs: {
                  class: 'ivu-btn ivu-btn-error ivu-btn-small'
                },
                props: {
                },
                style: {
                  marginRight: '6px'
                },
              }, [
                h('Poptip', {
                  props: {
                    confirm: true,
                    transfer: true,
                    title: '删除',
                    size: 'small'
                  },
                  on: {
                    'on-ok': () => {
                      var app_name = params.row['app_name']
                      this.deleteData(app_name)
                    },
                    'on-cancel': () => {
                      this.$Message.info('取消')
                    }
                  }
                }, '删除')
              ]),
            ]);
          }
        }
      ],
      app_data: [],
      clone_app_data: [],
    }
  },
  methods: {
    do_pom_parse() {
      var data = {'team': this.team, 'trunk_path': this.trunk_path}
      this.app_load = true
      this.parse_btn = true
      getPomParseData(data).then(res => {
        if (res.data.code === 0) {
          this.app_data = res.data.data
          for (let item in this.app_data) {
            this.app_data[item].editting = false
            this.app_data[item].saving = false
          }
          this.$Message.success('解析成功')
          this.app_load = false
          this.parse_btn = false
        } else {
          this.$Message.error(res.data.msg)
          this.app_load = false
          this.parse_btn = false
        }
      })
    },
    save_pom_parse() {
      if (this.$store.state.user.team !== 'app' || this.$store.state.user.role !== 'ops') {
        this.$Message.error("请运维人员进行操作");
        return
      }
      savePomParseData(this.app_data).then(res => {
        if (res.data.code === 0) {
          this.$Message.success('录入成功')
        } else {
          this.$Message.error(res.data.msg)
        }
      })
    },
    app_ack () {
      var tmp_data = []
      for (var item in this.app_data) {
        if (this.app_data[item]['app_name'] === this.m_app_name) {
          tmp_data.push({
            app_name: this.m_app_name,
            app_type: this.m_app_type,
            jar_name: this.m_jar_name,
            jdk_version: this.m_jdk_version,
            trunk_path: this.m_trunk_path,
            repo_path: this.m_repo_path,
            team: this.m_team,
            resource_code: this.m_resource_code,
          })
        } else {
          tmp_data.push(this.app_data[item])
        }
      }
      this.app_data = tmp_data
      this.$Message.success('保存成功');
      this.modal_app = false
    },
    cancel () {
      this.modal_app = false
    },
    deleteData (app_name) {
      var tmp_data = []
      for (var item in this.app_data) {
        if (this.app_data[item]['app_name'] !== app_name) {
          tmp_data.push(this.app_data[item])
        }
      }
      this.app_data = tmp_data
      this.$Message.success('删除成功');
    },
  },
  mounted () {
  },
  destroyed () {

  }
}
</script>

<style scoped>

</style>
