/*
 * @Author: chao.bao <EMAIL>
 * @Date: 2025-06-30 10:01:08
 * @LastEditors: chao.bao <EMAIL>
 * @LastEditTime: 2025-07-07 13:09:11
 * @FilePath: /website_web/deploy_website/src/view/server_batch_edit/api.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import axios from '@/libs/api.request'

export const get_all_team_lead_info = params => {
    return axios.request({
        url: '/spider/team_mgt/get_all_team_lead_info',
        params,
        method: 'get'
    })
}
export const get_reboot_flow_jenkins = params => {
    return axios.request({
        url: '/spider/publish_mgt/get_reboot_flow_jenkins',
        params,
        method: 'get'
    })
}
export const reboot_flow_action = params => {
    return axios.request({
        url: '/spider/publish_mgt/reboot_flow_action',
        params,
        method: 'get'
    })
}
export const page_reboot_flow = params => {
    return axios.request({
        url: '/spider/publish_mgt/page_reboot_flow/',
        data: params,
        method: 'post'
    })
}
