<style scoped>
.header{
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.row{
    margin-top: 24px;
}
.sub_title{
    margin-bottom: 24px;
}
/* .content{
    background-color: #333333;
    color: #ffffff;
} */
.demo-spin-icon-load{
    animation: ani-demo-spin 1s linear infinite;
}
@keyframes ani-demo-spin {
    from { transform: rotate(0deg);}
    50%  { transform: rotate(180deg);}
    to   { transform: rotate(360deg);}
}
</style>
<style>
.env_init_pipline_details .ivu-collapse{
    border: unset!important;
}
.env_init_pipline_details  .ivu-collapse .ivu-collapse-item .ivu-collapse-header{
    line-height: unset!important;
    padding-left: unset!important;
}
.env_init_pipline_details .ivu-icon{
    margin-right: unset!important;
}
.env_init_pipline_details .ivu-input{
    background-color: #333333;
    color: #ffffff;
}
</style>
<template>
    <div class="env_init_pipline_details">
        <Card style="padding-top: 10px; padding-left: 10px">
            <div class="header">
                <h2>环境操作记录详情页面</h2>
            </div>
            <Row class="row" v-for="(value, index) in logData" :key="index">
                <h3 class="sub_title">{{value.deploy_ip}} (已耗时{{value.time_cost}}s)</h3>
                <Timeline>
                    <TimelineItem v-for="(value2, index2) in value.detail" :key="index2">
                        <Icon v-show="value2.status == '未执行'" title="未执行" size="24" type="ios-remove-circle-outline" color="#2db7f5" slot="dot"></Icon>
                        <!-- <Icon v-show="value2.status == '执行中'" size="24" type="ios-loading" color="#ff9900" slot="dot"></Icon> -->
                        <Spin v-show="value2.status == '执行中'" title="执行中" fix slot="dot">
                            <Icon type="ios-loading" size=24 class="demo-spin-icon-load" color="#ff9900"></Icon>
                        </Spin>
                        <Icon v-show="value2.status == '执行失败'" title="执行失败" size="24" type="ios-close-circle-outline" color="#ed4014" slot="dot"></Icon>
                        <Icon v-show="value2.status == '执行成功'" title="执行成功" size="24" type="ios-checkmark-circle-outline" color="#19be6b" slot="dot"></Icon>
                        <Collapse simple>
                            <Panel name="1">
                                {{value2.step}} (已耗时{{value2.time_cost}}s)
                                <Input 
                                    v-model="value2.log" 
                                    slot="content" 
                                    type="textarea" 
                                    class="content"
                                    :rows="5"
                                />
                            </Panel>
                        </Collapse>
                    </TimelineItem>
                </Timeline>
            </Row>
        </Card>
    </div>
</template>
<script>
import {getEnvLog, getAppRestartLog} from "@/api/test-env"
import router from '@/mixin/router'
import isEmpty from '@/mixin/isEmpty'
import time from '@/mixin/time'
export default {
    mixins: [router,time,isEmpty],
    props:{
        order_code: {
            required: false,
            type: [String,Number],
            default: null,
        },
        sid:{
            required: false,
            type: [String,Number],
            default: null, 
        }
    },
    mounted () {
        let vm = this
        if (this.order_code == null && this.sid == null){
            this.$Message.warning("需要传递order_code或者sid参数，但是order_code和sid均为空,将在跳转回上一个页面")
            vm.goBack()
        }
        //执行初始化操作
        vm.init()//第一次执行，防止第一次延迟
        clearInterval(this.interval)
        this.interval = setInterval(function(){
            if (vm.statusJudge(vm.logData)){
                clearInterval(vm.interval)
            }else{
                vm.init()
            }
        }, 5000)
    },
    destroyed () {
        clearInterval(this.interval)
    },
    data () {
        return {
            interval: null,
            iconType: [
                'ios-arrow-up',
                'ios-arrow-down'
            ],
            logData: [],
        }
    },
    methods: {
        init () {
            if (!this.isEmpty(this.order_code)){
                let data = {
                    order_code: this.order_code
                }
                this.getEnvLogData(data)
            }else if (!this.isEmpty(this.sid)){
                this.getAppRestartLogData(this.sid)
            }
        },
        getEnvLogData (data) {
            let vm = this
            getEnvLog(data)
            .then(function(response){
                vm.logData = vm.dataSerializer(response.data)
            })
            .catch(function(error){
                vm.$Message.error("获取测试环境初始化日志信息失败")
                console.log(error)
            })
        },
        getAppRestartLogData (sid) {
            let vm = this
            getAppRestartLog(sid)
            .then(function(response){
                let temp = []
                temp[0] = response.data
                vm.logData = vm.dataSerializer(temp)
            })
            .catch(function(error){
                vm.$Message.error("获取测试环境应用重启日志信息失败")
                console.log(error)
            })
        },
        statusJudge (data) {
            let vm = this
            let status = null
            let tmp = null
            if (data == null || data.length == 0){
                return false
            }else{
                data.forEach((item,index) => {
                    if (index == 0 && (item.status == "执行成功" || item.status == "执行失败")){
                        status = item.status
                        tmp = true
                    }else{
                        return false
                    }
                    if (index != 0 && item.status == status){
                        tmp = true
                    }else{
                        return false
                    }
                })
                return tmp
            }
        },
        dataSerializer (data) {
            let vm = this
            if (data == null || data.length == 0){
                return []
            }else {
                data.forEach(element => {
                    if (vm.isEmpty(element.start_at)){
                        element.time_cost = 0
                    }else if (vm.isEmpty(element.end_at)){
                        element.time_cost = (new Date() - new Date(element.start_at))/1000 > 0 ? (new Date() - new Date(element.start_at))/1000 : 0
                    }else{
                        element.time_cost = (new Date(element.end_at) - new Date(element.start_at))/1000
                    }
                    element.start_at = vm.UtcDateToLocalDate(new Date(element.start_at))
                    element.end_at = vm.UtcDateToLocalDate(new Date(element.end_at))
                    element.detail.forEach(item => {
                        if (vm.isEmpty(item.start_at)){
                            item.time_cost = 0
                        }else if (vm.isEmpty(item.end_at)){
                            item.time_cost = (new Date() - new Date(item.start_at))/1000 > 0 ? (new Date() - new Date(item.start_at))/1000 : 0
                        }else{
                            item.time_cost = (new Date(item.end_at) - new Date(item.start_at))/1000
                        }
                        item.start_at = vm.UtcDateToLocalDate(new Date(item.start_at))
                        item.end_at = vm.UtcDateToLocalDate(new Date(item.end_at))
                    })
                })
                return data
            }
        },
    }
}
</script>