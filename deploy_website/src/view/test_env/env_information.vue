<style scoped>
  .card {
    padding-top: 10px;
    padding-left: 10px
  }

  .card .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .card .header .select {
    width: 200px;
  }

  .pag {
    display: flex;
    justify-content: center;
    margin-top: 24px;
  }

  .formButton {
    display: flex;
    justify-content: flex-end
  }

  .label {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
</style>
<style>
  .drawer .ivu-form-item-label {
    width: 100%;
    padding-right: 0px !important;
  }

  .drawer .ivu-checkbox-wrapper {
    margin-right: 0px;
  }
</style>
<template>
  <div class="env_information">
    <Card class="card">
      <div class="header">
        <h2>测试环境信息查询</h2>
        <Select v-model="select_team"
                class="select"
                placeholder="选择环境归属团队"
                filterable
                @on-change="searchEnvByTeam">
          <Option v-for="(value,index) in team"
                  :value="value"
                  :label="value"
                  :key="index">
            {{value}}
          </Option>
        </Select>
      </div>
      <Divider/>
      <Table :loading="tableLoading"
             :columns="tableColumns"
             :data="tableData"
             :no-data-text="noDataText"
      >
      </Table>
      <Page class="pag"
            :total="pagination.total"
            :current="pagination.page"
            :page-size="pagination.size"
            show-total
            show-elevator
            show-sizer
            @on-change="pageChange"
            @on-page-size-change="pageSizeChange"
      />
      <Modal
        :title="modal.title"
        v-model="modal.show"
        width="360"
        :mask-closable="true"
      >
        <Form ref="form" :model="form" :rules="rule" inline>
          <FormItem label="测试环境延期日期" prop="delay_time">
            <DatePicker style="width:328px;" :options="dateOption" type="date" placeholder="选择环境使用截止日期"
                        v-model="form.delay_time"></DatePicker>
          </FormItem>
        </Form>
        <div slot="footer">
          <Button @click="handleReset('form')">重置</Button>
          <Button type="primary" @click="handleSubmit('form')">延期</Button>
        </div>
      </Modal>
      <!-- 抽屉部分 -->
      <Drawer
        class="drawer"
        :closable="true"
        :title="drawer.title"
        :width="drawer.width"
        v-model="drawer.show">
        <Form ref="form2" :model="form2" :rules="rule">
          <FormItem prop="TP">
            <div slot="label" class="label">
              <p style="margin-right: 5px;">TP应用</p>
              <Checkbox v-model="TP">全选</Checkbox>
            </div>
            <Select
              v-model="form2.TP"
              filterable
              multiple
            >
              <Option v-for="(item, index) in envAppData.TP"
                      :value="item.id"
                      :label="item.name"
                      :key="index">
                {{item.name}}</Option>
            </Select>
          </FormItem>
          <FormItem prop="TMS">
            <div slot="label" class="label">
              <p style="margin-right: 5px;">TMS应用</p>
              <Checkbox v-model="TMS">全选</Checkbox>
            </div>
            <Select
              v-model="form2.TMS"
              filterable
              multiple
            >
              <Option v-for="(item, index) in envAppData.TMS"
                      :value="item.id"
                      :label="item.name"
                      :key="index">
                {{item.name}}</Option>
            </Select>
          </FormItem>
          <FormItem class="formButton">
            <Button @click="handleReset('form2')">重置表单</Button>
            <Button type="primary" style="margin-left: 8px" icon="ios-refresh" @click="restart('form2')">重启</Button>
          </FormItem>
        </Form>
      </Drawer>
    </Card>
  </div>
</template>
<script>
  import {getEnv, recoverEnv, delayEnv, getEnvApp} from "@/api/test-env"
  import time from '@/mixin/time'
  import isEmpty from '@/mixin/isEmpty'

  export default {
    mixins: [time, isEmpty],
    data() {
      return {
        pagination: {
          page: 1,
          size: 10,
          total: 0,
        },
        team: ['TMS', 'TP'],
        select_team: null,
        select_id: null,
        modal: {
          show: false,
          title: '',
        },
        drawer: {
          show: false,
          title: null,
          width: 360,
        },
        form: {
          delay_time: null,
        },
        rule: {
          delay_time: [
            {type: 'date', required: true, message: '环境延期时间不能为空', trigger: 'change'},
          ]
        },
        dateOption: {
          disabledDate(date) {
            return date && date.valueOf() < Date.now() - 86400000;
          }
        },
        tableLoading: false,
        noDataText: "暂时没有数据呦",
        tableData: [],
        tableColumns: [
          {
            title: '环境名称',
            key: 'name',
            tooltip: true,
            align: 'center',
            width: 100,
          },
          {
            title: 'TP',
            key: 'tp',
            tooltip: true,
            width: 140,
            align: 'center',
          },
          {
            title: 'TMS',
            key: 'tms',
            tooltip: true,
            width: 80,
            align: 'center',
          },
          {
            title: '申请人',
            key: 'applicant',
            tooltip: true,
            align: 'center',
          },
          {
            title: '用途',
            key: 'apply_reason',
            tooltip: true,
            align: 'center',
            tooltip: true,
          },
          {
            title: '申请日期',
            key: 'create_at',
            tooltip: true,
            align: 'center',
            width: 160,
          },
          {
            title: '截止日期',
            key: 'invalid_at',
            tooltip: true,
            align: 'center',
            width: 160,
          },
          {
            title: '操作',
            align: 'center',
            width: 180,
            fixed: 'right',
            render: (h, params) => {
              if (params.row.status == "使用中") {
                return h('div', [
                  h('Button', {
                    props: {
                      type: 'warning',
                      size: 'small'
                    },
                    style: {
                      marginRight: '5px'
                    },
                    on: {
                      click: () => {
                        this.showDelay(params)
                      }
                    }
                  }, '延期'),
                  h('Button', {
                    props: {
                      type: 'error',
                      size: 'small'
                    },
                    style: {
                      marginRight: '5px'
                    },
                    on: {
                      click: () => {
                        this.showRecover(params)
                      }
                    }
                  }, '回收'),
                  h('Button', {
                    props: {
                      type: 'info',
                      size: 'small'
                    },
                    on: {
                      click: () => {
                        this.showRestart(params)
                      }
                    }
                  }, '重启'),
                ]);
              }
            }
          }
        ],
        envAppData: {
          'TP': [],
          'TMS': [],
        },
        form2: {
          "TP": [],
          'TMS': [],
        },
        TP: false,
        TMS: false
      }
    },
    watch: {
      TP(newVal, old) {
        let vm = this
        if (newVal) {
          vm.form2.TP = []
          for (let index in vm.envAppData.TP) {
            vm.form2.TP.push(vm.envAppData.TP[index].id)
            console.log(vm.form2.TP)
          }
        } else {
          vm.form2.TP = []
        }
      },
      TMS(newVal, old) {
        let vm = this
        if (newVal) {
          vm.form2.TMS = []
          for (let index in vm.envAppData.TMS) {
            vm.form2.TMS.push(vm.envAppData.TMS[index].id)
            console.log(vm.form2.TMS)
          }
        } else {
          vm.form2.TMS = []
        }
      }
    },
    mounted() {
      this.init()
    },
    methods: {
      init() {
        let data = {
          page: this.pagination.page,
          size: this.pagination.size,
          team: this.select_team,
        }
        this.getEnvData(data)
        data = {
          team: 'TP'
        }
        this.getEnvAppData(data)
        data = {
          team: 'TMS'
        }
        this.getEnvAppData(data)
      },
      setPagParams(page, size, total) {
        /**
         * 设置分页参数
         */
        if (!this.isEmpty(page)) {
          this.pagination.page = page
        }
        if (!this.isEmpty(size)) {
          this.pagination.size = size
        }
        if (!this.isEmpty(total)) {
          this.pagination.total = total
        }
      },
      getEnvData(data) {
        let vm = this
        vm.tableLoading = true
        getEnv(data)
          .then(function (response) {
            vm.tableLoading = false
            vm.setPagParams('', '', response.data.count)
            vm.tableData = vm.dataSerializer(response.data.results)
          })
          .catch(function (error) {
            vm.tableLoading = false
            vm.$Message.error("获取测试环境使用情况信息失败")
            console.log(error)
          })
      },
      getEnvAppData(data) {
        let vm = this
        getEnvApp(data)
          .then(function (response) {
            vm.envAppData[data.team] = response.data
          })
          .catch(function (error) {
            vm.$Message.error("获取测试环境" + data.team + "应用信息失败")
            console.log(error)
          })
      },
      searchEnvByTeam(value) {
        let data = {
          page: this.pagination.page,
          size: this.pagination.size,
          team: value,
        }
        this.getEnvData(data)
      },
      showDelay(params) {
        this.modal.show = true
        this.modal.title = params.row.name + "延期使用申请"
        this.select_id = params.row.id
      },
      delay(id, data) {
        let vm = this
        delayEnv(id, data)
          .then(function (response) {
            vm.$Message.success("环境延期成功")
            vm.init()
          })
          .catch(function (error) {
            vm.$Message.error("环境延期失败")
            console.log(error)
          })
      },
      showRecover(params) {
        let vm = this
        vm.$Modal.confirm({
          title: '回收测试环境' + params.row.name,
          content: '确定进行回收操作吗?',
          okText: '回收',
          cancelText: '取消',
          loading: true,
          onCancel() {
          },
          onOk() {
            vm.recover(params)
          }
        })
      },
      recover(params) {
        let vm = this
        let data = {}
        data = {
          recycle: true
        }
        recoverEnv(params.row.id, data)
          .then(function (response) {
            vm.$Message.success("环境回收成功")
            vm.$Modal.remove()
            vm.init()
          })
          .catch(function (error) {
            vm.$Message.error("环境回收失败")
            vm.$Modal.remove()
            console.log(error)
          })
      },
      showRestart(params) {
        this.drawer.show = true
        this.drawer.title = params.row.name + "的应用重启"
        this.select_id = params.row.id
      },
      restart(name) {
        let vm = this
        let data = {}
        if (vm.isEmpty(vm.form2['TP']) && vm.isEmpty(vm.form2['TMS'])) {
          vm.$Message.error("请选择至少一个应用，才可进行重启操作")
        } else {
          if (!vm.isEmpty(vm.form2['TP'])) {
            data = {
              restart_tp: {
                applications: vm.form2['TP']
              }
            }
            recoverEnv(vm.select_id, data)
              .then(function (response) {
                vm.$Message.success("应用重启申请提交成功")
                vm.drawer.show = false
                vm.$router.push({name: 'env_pipeline_detail', query: {sid: response.data.sid}})
              })
              .catch(function (error) {
                vm.$Message.error("应用重启申请提交失败")
                console.log(error)
              })
          }
          if (!vm.isEmpty(vm.form2['TMS'])) {
            data = {
              restart_tms: {
                applications: vm.form2['TMS']
              }
            }
            recoverEnv(vm.select_id, data)
              .then(function (response) {
                vm.$Message.success("应用重启申请提交成功")
                vm.drawer.show = false
                vm.$router.push({name: 'env_pipeline_detail', query: {sid: response.data.sid}})
              })
              .catch(function (error) {
                vm.$Message.error("应用重启申请提交失败")
                console.log(error)
              })
          }
        }
      },
      pageChange(page) {
        /**
         * 分页中改变size触发的函数
         */
        this.setPagParams(page, '', '')
        let data = {
          page: this.pagination.page,
          size: this.pagination.size,
          team: this.select_team
        }
        this.getEnvData(data)
      },
      pageSizeChange(size) {
        this.setPagParams('', size, '')
        let data = {
          page: 1,
          size: this.pagination.size,
          team: this.select_team
        }
        this.getEnvData(data)
      },
      handleSubmit(name) {
        let vm = this
        this.$refs[name].validate((valid) => {
          if (valid) {
            let data = {
              delay: {
                invalid_at: vm.UtcDateToLocalDate(new Date(vm.form.delay_time))
              }
            }
            vm.delay(vm.select_id, data)
            vm.modal.show = false
          } else {
            vm.$Message.error("测试环境申请表单提交失败")
          }
        })
      },
      handleReset(name) {
        this.$refs[name].resetFields();
      },
      dataSerializer(data) {
        let vm = this
        if (data == null || data.length == 0) {
          return []
        } else {
          data.forEach(element => {
            if (!vm.isEmpty(element.invalid_at)) {
              element.invalid_at = vm.UtcDateToLocalDate(new Date(element.invalid_at))
            }
            if (!vm.isEmpty(element.create_at)) {
              element.create_at = vm.UtcDateToLocalDate(new Date(element.create_at))
            }
          })
          return data
        }
      }
    }
  }
</script>
