<style scoped>
.card{
    padding-top: 10px; 
    padding-left: 10px
}
.textarea {
    padding-top: 20px;
}
</style>
<style>
.textarea .ivu-input{
    background-color: #333333;
    color: #ffffff;
}
</style>
<template>
    <div class="env_test">
        <Card class="card">
            <h2>docker信息页面</h2>
            <Divider />
            <div>
                <label>环境名: </label>
                <Select v-model="selected_env" style="width:200px">
                    <Option v-for="(item, index) in env_data" :value="item.tms" :key="index">{{ item.name}}:{{item.tms }}</Option>
                </Select>
                <label> 模块: </label>
                <Select v-model="selected_module" style="width:200px;margin-left:10px;">
                    <Option v-for="(item, index) in modules" :value="item" :key="index">{{ item }}</Option>
                </Select>
                <Button style="margin-left:10px;" icon="ios-search" type="primary" @click="search">查询</Button>
            </div>
            <div class="textarea" v-html="docker_state">
                <!-- <Input v-model="docker_state" type="textarea" :rows="5" /> -->
            </div>
        </Card>
    </div>
</template>
<script>
import {getEnv, getDockerState} from "@/api/test-env"
import time from '@/mixin/time'
import isEmpty from '@/mixin/isEmpty'
export default {
    mixins: [time, isEmpty],
    data () {
        return {
            env_data:[],
            modules: ['pods','service','deployment','configmap','pvc','ingress','namespace','secret'],
            selected_env: null,
            selected_module: null,
            docker_state: null,
        }
    },
    mounted () {
        let vm = this
        //执行初始化操作
        vm.init()//第一次执行，防止第一次延迟
        // clearInterval(this.interval)
        // this.interval = setInterval(function(){
        //     vm.init()
        // }, 5000)
    },
    methods: {
        init () {
            let vm = this

            vm.getEnvData()
            // vm.search()
        },
        getEnvData (data = null) {
            let vm = this
            getEnv(data)
            .then(function(response){
                vm.env_data = response.data
                console.log(vm.env_data)
            })
            .catch(function(error){
                console.log(error)
            })
        },
        getDocker (data) {
            let vm = this
            getDockerState(data)
            .then(function(response){
                vm.docker_state = response.data.msg
                console.log(response)
            })
            .catch(function(error){
                vm.$Message.error("获取docker state失败")
                console.log(error)
            })
        },
        search () {
            let vm = this
            let data = {
                env_name: vm.selected_env,
                component: vm.selected_module
            }
            vm.getDocker(data)
        }
    }
}
</script>