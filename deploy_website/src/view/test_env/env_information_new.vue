<style scoped>
  .card {
    padding-top: 10px;
    padding-left: 10px
  }

  .card .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .card .header .select {
    width: 200px;
  }

  .pag {
    display: flex;
    justify-content: center;
    margin-top: 24px;
  }

  .formButton {
    display: flex;
    justify-content: flex-end
  }

  .label {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
</style>
<style>
  .drawer .ivu-form-item-label {
    width: 100%;
    padding-right: 0px !important;
  }

  .drawer .ivu-checkbox-wrapper {
    margin-right: 0px;
  }
</style>
<template>
  <div class="env_information">
    <Card class="card">
      <div class="header">
        <h2>测试环境使用情况</h2>

        <!--<Select v-model="select_team"
            class="select"
            placeholder="选择环境归属团队"
            filterable
            @on-change="searchEnvByTeam">
            <Option v-for="(value,index) in team"
                :value="value" :key="index">{{value}}</Option>
        </Select>-->
      </div>
       <div class="header" style="margin-top: 10px;">
            <div>
              <a href="https://www.processon.com/view/link/5fab3daf6376893d444d80eb" target="abc">配置替换规则说明 <Icon type="ios-alert-outline" size="18"/> </a>&nbsp;&nbsp;&nbsp;&nbsp;
              <a href="http://paas.hongkou.howbuy.com/report/#/notebook/2FQR8B4T3/paragraph/20201119-142029_72964243?asIframe" target="efg">替换结果验证报表 <Icon type="ios-alert-outline" size="18"/> </a>
            </div>
       </div>
      <Divider/>
      <Table :loading="tableLoading"
             :columns="[...tableColumns]"
             :data="tableData"
             :no-data-text="noDataText"
      >
      </Table>
      <Page class="pag"
            :total="pagination.total"
            :current="pagination.page"
            :page-size="pagination.size"
            show-total
            show-elevator
            show-sizer
            @on-change="pageChange"
            @on-page-size-change="pageSizeChange"
      />
      <Modal
        :title="modal.title"
        v-model="modal.show"
        width="360"
        :mask-closable="true"
      >
        <Form ref="form" :model="form" :rules="rule" inline>
          <FormItem label="申请人" prop="apply_user">
            <Input v-model="form.apply_user" style="width:328px;" placeholder="输入环境申请人"></Input>
          </FormItem>
          <FormItem label="申请日期" prop="update_time">
            <DatePicker style="width:328px;" :options="dateOption" type="datetime" placeholder="开始使用日期"
                        v-model="form.update_time"></DatePicker>
          </FormItem>
          <FormItem label="环境用途" prop="apply_reason">
            <Input v-model="form.apply_reason" type="textarea" style="width:328px;" placeholder="输入环境用途"></Input>
          </FormItem>
          <FormItem label="截止日期" prop="invalid_at">
            <DatePicker style="width:328px;" :options="dateOption" type="datetime" placeholder="截止使用日期"
                        v-model="form.invalid_at"></DatePicker>
          </FormItem>
        </Form>
        <div slot="footer">
          <Button @click="handleReset('form')">重置</Button>
          <Button type="primary" @click="handleSubmit('form')">保存</Button>
        </div>
      </Modal>

<!--      <Modal-->
<!--        :title="modal2.title"-->
<!--        v-model="modal2.show"-->
<!--        width="360"-->
<!--        :mask-closable="true"-->
<!--      >-->
<!--        <div slot="footer">-->
<!--          <Button @click="onCancel()">取消</Button>-->
<!--          <Button type="primary" @click="handleSubmit('form2')">重启</Button>-->
<!--        </div>-->
<!--      </Modal>-->
    </Card>
  </div>
</template>
<script>
import {
  getAllEnvInfo,
  updateTestEnv,
  setTimeTestEnv,
  recycleTestEnv,
  getJenkinsLog,
  k8sEnvRecover,
  getManualTestingEnv,
  getAllTestingEnv,
  getSuiteUseAsList,
  updSuiteUseBind, getEditEnvAuth,
} from "@/api/test-env"
  import time from '@/mixin/time'
  import isEmpty from '@/mixin/isEmpty'

  export default {
    mixins: [time, isEmpty],
    data() {
      return {
        pagination: {
          page: 1,
          size: 10,
          total: 0,
        },
        modal: {
          show: false,
          title: '',
        },
        modal2: {
          show: false,
          title: '',
        },
        select_id:null,
        suite_id:null,
        suite_code:null,
        form: {
          apply_user:'',
          update_time:null,
          apply_reason:'',
          invalid_at:null
        },
        form2: {
          target_time: null,
        },
        rule: {
          invalid_at: [
             {type: 'date', required: true, message: '截止时间不能为空', trigger: 'change'}
          ],
          update_time: [
             {type: 'date', required: true, message: '开始时间不能为空', trigger: 'change'}
          ],
          apply_user: [
             {required: true, message: '申请人不能空', trigger: 'change'}
          ],
          apply_reason: [
             {type: 'string', required: true, message: '用途不能为空', trigger: 'change'}
          ]
        },
        rule2: {
          target_time: [
             {type: 'date', required: true, message: '预期时间不能为空', trigger: 'change'}
          ]
        },
        dateOption: {
          disabledDate(date) {
            return date && date.valueOf() < Date.now() - 86400000;
          }
        },
        tableLoading: false,
        noDataText: "暂时没有数据呦",
        tableData: [],
        suiteUseAsList: [],
        can_edit_env: false,
        tableColumns: [
          {
            title: '环境套名',
            key: 'suite_code',
            tooltip: true,
            align: 'center',
            width: 100,
          },
          // {
          //   title: '虚拟机',
          //   key: 'node_ip',
          //   tooltip: true,
          //   width: 140,
          //   align: 'center',
          // },
          {
            title: '容器',
            key: 'node_docker',
            tooltip: true,
            width: 80,
            align: 'center',
          },
          {
            title: '申请人',
            key: 'apply_user',
            tooltip: true,
            width: 180,
            align: 'center',
          },
          {
            title: '申请说明',
            key: 'apply_reason',
            tooltip: true,
            align: 'left',
          },
          {
            title: '环境用途',
            key: 'use_as_id',
            tooltip: true,
            width: 160,
            align: 'center',
            render: (h, params) => {
              // if (this.can_edit_env) {
              let use_as_list = [];
              this.suiteUseAsList.forEach(item => {
                let use_as_option = h('Option', {
                  props: {
                    value: item.value,
                    label: item.label
                  }
                });
                use_as_list.push(use_as_option)
              });
              return h('Select', {
                props: {
                  placeholder: params.row.use_as_name,
                  value: params.row.use_as_name,
                  transfer: true,
                  size: 'small',
                },
                style: {
                  width: '140px'
                },
                on: {
                  'on-change': (val) => {
                    this.suiteUseAsOptionChange(params.row, val)
                  }
                }
              }, use_as_list);
              // } else {
              //   return h('span', params.row.use_as_name);
              // }
            }
          },
          {
            title: '更新日期',
            key: 'update_time',
            tooltip: true,
            align: 'center',
            width: 152,
          },
          {
            title: '截止日期',
            key: 'invalid_at',
            tooltip: true,
            align: 'center',
            width: 152,
          },
          {
            title: '操作',
            align: 'center',
            width: 230,
            fixed: 'right',
            render: (h, params) => {
              // if (params.row.apply_user != null) {
                return h('div', [
                  h('Button', {
                    props: {
                      type: 'error',
                      size: 'small',
                      disabled: params.row.use_as_id !== 1,
                    },
                    style: {
                      marginRight: '5px'
                    },
                    on: {
                      click: () => {
                        this.showDestroyK8s(params)
                      }
                    }
                  }, '销毁'),
                  h('Button', {
                    props: {
                      type: 'warning',
                      size: 'small'
                    },
                    style: {
                      marginRight: '5px'
                    },
                    on: {
                      click: () => {
                        this.showUpdateInfo(params)
                      }
                    }
                  }, '编辑'),
                  h('Button', {
                    props: {
                      type: 'warning',
                      size: 'small',
                      disabled: params.row.use_as_id !== 1,
                    },
                    style: {
                      marginRight: '5px'
                    },
                    on: {
                      click: () => {
                        this.showRecover(params)
                      }
                    }
                  }, '回收'),
                  h('Button', {
                    props: {
                      type: 'info',
                      size: 'small',
                      disabled: params.row.use_as_id !== 1,
                    },
                    on: {
                      click: () => {
                         let vm = this;
                          getJenkinsLog({'job_business_id': params.row.jenkins_url}).then(res => {
                            window.open(
                              res.data.data
                            );
                          }).catch(function (error) {
                            vm.$Message.error("查询jenkins日志失败");
                            console.log(error)
                          });
                      }
                    }
                  }, '详情'),
                ]);
              }
            // }
          }
        ]


      }
    },
    watch: {},
    mounted() {
      this.init()
    },
    methods: {
      init() {
        let data = {
          page: this.pagination.page,
          size: this.pagination.size,
        }
        this.getEnvData(data)

        getSuiteUseAsList({})
          .then(res => {
            if (res.data.status === "success") {
              let use_as_options = []
              res.data.data.forEach(item => {
                use_as_options.push({
                  "value": item.use_as_id,
                  "label": item.use_as_name,
                })
              });
              this.suiteUseAsList = use_as_options
            }
          })
        this.get_user_can_edit_env()
      },
      get_user_can_edit_env() {
        this.can_edit_env = false
        getEditEnvAuth({}).then(res => {
          if (res.data.status === "success") {
            this.can_edit_env = true
          }
        })
      },
      setPagParams(page, size, total) {
        /**
         * 设置分页参数
         */
        if (!this.isEmpty(page)) {
          this.pagination.page = page
        }
        if (!this.isEmpty(size)) {
          this.pagination.size = size
        }
        if (!this.isEmpty(total)) {
          this.pagination.total = total
        }
      },
      getEnvData(data) {
        let vm = this
        vm.tableLoading = true
        getAllTestingEnv(data)
          .then(function (response) {
            vm.tableLoading = false
            vm.setPagParams('', '', response.data.data.count)
            vm.tableData = vm.dataSerializer(response.data.data.results)
          })
          .catch(function (error) {
            vm.tableLoading = false
            vm.$Message.error("获取测试环境使用情况信息失败")
            console.log(error)
          })
      },
      showUpdateInfo(params) {
        this.modal.show = true
        this.modal.title = params.row.suite_code + "环境信息编辑"
        this.select_id = params.row.suite_id
        this.form.apply_user = params.row.apply_user
        this.form.update_time =  this.convertDateFromString(params.row.update_time)
        this.form.apply_reason = params.row.apply_reason
        this.form.invalid_at = this.convertDateFromString(params.row.invalid_at)
      },
      // showSetTimeInfo(params) {
      //   this.modal2.show = true
      //   this.modal2.title = params.row.suite_code + "重启服务（更新环境时间,清除缓存）"
      //   this.suite_id = params.row.suite_id
      //   this.suite_code = params.row.suite_code
      //   this.form2.target_time = null
      // },

      convertDateFromString(dateString) {
        if (dateString) {
          var arr1 = dateString.split(" ");
          var sdate = arr1[0].split('-');
          var date = new Date(sdate[0], sdate[1]-1, sdate[2]);
          return date;
        }
      },
      dateFormat(fmt, date) {
          let ret;
          const opt = {
              "Y+": date.getFullYear().toString(),        // 年
              "m+": (date.getMonth() + 1).toString(),     // 月
              "d+": date.getDate().toString(),            // 日
              "H+": date.getHours().toString(),           // 时
              "M+": date.getMinutes().toString(),         // 分
              "S+": date.getSeconds().toString()          // 秒
              // 有其他格式化字符需求可以继续添加，必须转化成字符串
          };
          for (let k in opt) {
              ret = new RegExp("(" + k + ")").exec(fmt);
              if (ret) {
                  fmt = fmt.replace(ret[1], (ret[1].length == 1) ? (opt[k]) : (opt[k].padStart(ret[1].length, "0")))
              };
          };
          return fmt;
      },
      update_info() {
        let vm = this
        let update_time2 = this.dateFormat("YYYY-mm-dd HH:MM:SS",vm.form.update_time)
        let invalid_at2 = this.dateFormat("YYYY-mm-dd HH:MM:SS",vm.form.invalid_at)
        updateTestEnv(vm.select_id,vm.form.apply_user,update_time2,vm.form.apply_reason,invalid_at2)
          .then(function (response) {
            vm.$Message.success("环境编辑成功")
            vm.init()
          })
          .catch(function (error) {
            vm.$Message.error("环境编辑失败")
            console.log(error)
          })
      },
      changDateTime(suite_id, suite_code, data) {
        let vm = this
        let data2 = this.dateFormat("YYYY-mm-dd HH:MM:SS",data)
        setTimeTestEnv(suite_id, suite_code, data2)
          .then(function (response) {
            vm.$Message.success("设置时间并重启成功")
            vm.init()
          })
          .catch(function (error) {
            vm.$Message.error("设置时间并重启失败")
            console.log(error)
          })
      },
      showRecover(params) {
        let vm = this
        vm.$Modal.confirm({
          title: '回收测试环境' + params.row.suite_code,
          content: '确定进行回收操作吗?',
          okText: '回收',
          cancelText: '取消',
          loading: true,
          onCancel() {
          },
          onOk() {
            vm.recycle(params)
          }
        })
      },
      recycle(params) {
        let vm = this
        recycleTestEnv(params.row.suite_id)
          .then(function (response) {
            vm.$Message.success("环境回收成功")
            vm.$Modal.remove()
            vm.init()
          })
          .catch(function (error) {
            vm.$Message.error("环境回收失败")
            vm.$Modal.remove()
            console.log(error)
          })
      },
      showDestroyK8s(params) {
        let vm = this
        vm.$Modal.confirm({
          title: '销毁测试环境' + params.row.suite_code,
          content: '此操作会清理当前名称空间下所有pod资源，请谨慎操作！！！',
          okText: '销毁',
          cancelText: '取消',
          loading: true,
          onCancel() {
          },
          onOk() {
            vm.recycle(params)
            console.log("userName="+vm.$store.state.user.userName)
            vm.callK8sEnvRecovery(params.row.suite_code, vm.$store.state.user.userName)
          }
        })
      },
      callK8sEnvRecovery(suite_code, operator) {
        let vm = this
        k8sEnvRecover(suite_code, operator)
          .then(function (response) {
            vm.$Message.success("启动销毁jenkins_job成功。")
            vm.init()
          })
          .catch(function (error) {
            vm.$Message.error("启动销毁jenkins_job失败。")
            console.log(error)
          })
      },
      pageChange(page) {
        /**
         * 分页中改变size触发的函数
         */
        this.setPagParams(page, '', '')
        let data = {
          page: this.pagination.page,
          size: this.pagination.size,
        }
        this.getEnvData(data)
      },
      pageSizeChange(size) {
        this.setPagParams('', size, '')
        let data = {
          page: 1,
          size: this.pagination.size,
        }
        this.getEnvData(data)
      },
      handleSubmit(name) {
        let vm = this
        this.$refs[name].validate((valid) => {
          if (valid) {
            if(name=='form'){
              vm.modal.show = false
              vm.update_info()
            }else if(name=='form2'){
              vm.modal2.show = false
              vm.changDateTime(vm.suite_id, vm.suite_code, this.form2.target_time)
            }
          }else{
            vm.$Message.error("验证失败")
          }
        })
      },
      handleReset(name) {
        this.$refs[name].resetFields();
      },
      dataSerializer(data) {
        let vm = this
        if (data == null || data.length === 0) {
          return []
        } else {
          data.forEach(element => {
            if (!vm.isEmpty(element.invalid_at)) {
              element.invalid_at = vm.UtcDateToLocalDate(new Date(element.invalid_at))
            }
            if (!vm.isEmpty(element.create_at)) {
              element.create_at = vm.UtcDateToLocalDate(new Date(element.create_at))
            }
            if (!vm.isEmpty(element.update_time)) {
              element.update_time = vm.UtcDateToLocalDate(new Date(element.update_time))
            }
          })
          return data
        }
      },
      suiteUseAsOptionChange(row, use_as_id) {
        if (!this.can_edit_env) {
          this.$Message.warning('无权限修改环境用途,请找孙双庆、章林楠、刘彬彬、李远相处理')
          return false
        }
        updSuiteUseBind(row.bind_id, use_as_id)
          .then( res => {
            this.$Message.success("修改环境用途成功。")
          })
          .catch( err => {
            this.$Message.error("修改环境用途失败。")
            console.log(err)
          })
      },
    }

  }
</script>
