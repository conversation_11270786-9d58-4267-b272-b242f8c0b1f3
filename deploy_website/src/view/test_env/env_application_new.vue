<style scoped>
  .card {
    padding-top: 10px;
    padding-left: 10px
  }

  .formItem {
    width: 50%;
  }

  .formButton {
    width: 50%;
    display: flex;
    justify-content: flex-end
  }

  .pag {
    display: flex;
    justify-content: center;
    /* margin-top: 24px; */
  }

  /* .env_application /deep/ .ivu-input-group-prepend, .ivu-input-group-append{
      background-color: #2d8cf0;
      color: #ffffff;
  }
  .env_application /deep/ .ivu-input-group-prepend, .ivu-input-group-append .ivu-select, .ivu-select-arrow{
      color: #ffffff;
  }
  .env_application /deep/ .ivu-input-group-prepend .ivu-select{
      color: #ffffff;
  } */
</style>
<style lang='less'>
  .ivu-modal-header {
    text-align: center;
  }
</style>
<template>
  <div class="env_application">
    <Card class="card">
      <h2>测试环境申请</h2>
      <Divider/>
      <Form ref="form" :model="form" :rules="rule" :label-width="120">
        <Row>
          <i-col span="12">
            <FormItem label="申请人" prop="applicant" class="formItem">
              <Input v-model="form.applicant" placeholder="输入测试环境申请人" disabled></Input>
            </FormItem>
          </i-col>
          <i-col span="12">
            <FormItem label="截止日期" prop="invalid_at" class="formItem">
              <DatePicker type="datetime" :options="dateOption" placeholder="选择环境使用截止日期"
                          v-model="form.invalid_at"></DatePicker>
            </FormItem>
          </i-col>
        </Row>
        <Row>
          <i-col span="12">
            <FormItem label="测试环境" prop="env_id" class="formItem">
              <Input v-model="form.env_value" placeholder="选择要申请的测试环境" disabled>
                <Button slot="append" icon="ios-search" @click="searchEnv()">
                </Button>
              </Input>
            </FormItem>
          </i-col>
          <i-col span="12">
            <FormItem label="测试环境用途" prop="apply_reason" class="formItem">
              <Input v-model="form.apply_reason" type="textarea" placeholder="输入测试环境用途"></Input>
            </FormItem>
          </i-col>
        </Row>
        <div
          style="border-width: 1px;border-color: black;border-style: solid;background-color: #f3f3f3;margin-left: 30px;margin-right: 30px;">
          <Row>
            <i-col span="22">
              <FormItem prop="template_name" label="模板名">
                <RadioGroup v-model="form.template_id" @on-change="template_select_change">
                  <Radio v-for="item in template_item_list" :label="item.id">
                    {{item.template_name}}
                  </Radio>
                </RadioGroup>
              </FormItem>
            </i-col>
            <i-col span="2">
              <i-button :icon="collepseAll? 'ios-arrow-up':'ios-arrow-down'" type="text"
                        @click="allHandleToggleCollepse()">{{collepseAll? '收起': '展开'}}
              </i-button>
            </i-col>
          </Row>
          <div
            style="border-width: medium;border-color: black;border-style: dotted; margin-right: 20px; margin-left: 20px; margin-bottom:20px; margin-top: 10px;background-color: white;"
            v-show="collepseAll">
            <FormItem prop="pipeline_list" label="数据库初始化">
              <RadioGroup v-model="form.init_db">
                <Radio label="it300">it300</Radio>
                <Radio label="tp_basic">tp_basic</Radio>
                <Radio label="None">None</Radio>
              </RadioGroup>
            </FormItem>
            <FormItem prop="pipeline_list" label="新流水线应用">
              <CheckboxGroup v-model="form.pipeline_list">
                <Checkbox v-for="pipeline_item in pipeline_item_list" :label="pipeline_item" :key="pipeline_item">
                  {{ pipeline_item }}
                </Checkbox>
              </CheckboxGroup>
            </FormItem>
            <FormItem prop="tms_list" label="tms(容器应用)">
              <CheckboxGroup v-model="form.tms_list">
                <Checkbox v-for="tms_item in tms_item_list" :label="tms_item" :key="tms_item">
                  {{ tms_item }}
                </Checkbox>
              </CheckboxGroup>
            </FormItem>
            <FormItem prop="tp_list" label="虚拟机类应用">
              <CheckboxGroup v-model="form.tp_list">
                <Checkbox v-for="tp_item in tp_item_list" :label="tp_item" :key="tp_item">
                  {{ tp_item }}
                </Checkbox>
              </CheckboxGroup>
            </FormItem>
            <FormItem prop="pa_list" label="中间件应用">
              <CheckboxGroup v-model="form.pa_list">
                <Checkbox v-for="pa_item in pa_item_list" :label="pa_item" :key="pa_item" disabled>
                  {{ pa_item }}
                </Checkbox>
              </CheckboxGroup>
            </FormItem>
            <Row>
              <i-col span="12">
                <FormItem label="设置环境时间" class="formItem">
                  <DatePicker type="datetime" placeholder="选择环境使用截止日期"
                              v-model="form.use_env_time"></DatePicker>
                </FormItem>
              </i-col>
              <i-col span="12">
                <FormItem label="清理缓存" class="formItem">
                  <RadioGroup v-model="form.clean_cache">
                    <Radio label="1">是</Radio>
                    <Radio label="0">否</Radio>
                  </RadioGroup>
                </FormItem>
              </i-col>
            </Row>
            <Row>
              <i-col span="24">
                <FormItem label="邮件列表" class="formItem">
                  <Select
                    placeholder="邮箱地址"
                    style="margin-top: 5px"
                    v-model="form.allSelectedMails"
                    filterable
                    multiple
                  >
                    <Option v-for="item in allFilterMails"
                            :value="item"
                            :label="item"
                            :key="item">
                      {{ item }}</Option>
                  </Select>
                </FormItem>
              </i-col>
            </Row>
            <Row v-show="test_id_show">
              <i-col span="12">
                <FormItem label="测试集ID" class="formItem">
                  <Input v-model="form.test_set_id" placeholder="输入测试集ID"></Input>
                </FormItem>
              </i-col>
            </Row>
            <Row style="margin-bottom: 10px">
              <i-col span="2" offset="21">
                <Button @click="save_modal_show=true">保存为模板</Button>
              </i-col>
            </Row>
          </div>

        </div>

        <FormItem class="formButton">
          <Button @click="handleReset('form')">重置申请</Button>
          <Button type="primary" style="margin-left: 8px" @click="handleSubmit('form')">确认申请</Button>
        </FormItem>
      </Form>
      <Modal
        title="测试环境使用情况"
        v-model="modal.show"
        width="1080"
        :mask-closable="true"
      >
        <Table :loading="tableLoading"
               :columns="tableColumns"
               :data="tableData"
               :no-data-text="noDataText"
        >
        </Table>
        <div slot="footer">
          <Page class="pag"
                :total="pagination.total"
                :current="pagination.page"
                :page-size="pagination.size"
                show-total
                show-elevator
                show-sizer
                @on-change="pageChange"
                @on-page-size-change="pageSizeChange"
          />
        </div>
      </Modal>
      <Modal
        title="保存模板"
        width="400"
        v-model="save_modal_show"
        :mask-closable="true"
        @on-ok="save_template">
        <Form>
          <Row>
            <i-col span="24">
              <FormItem label="新模板名称">
                <Input v-model="form.new_template_name" placeholder="输入新模板名称"></Input>
              </FormItem>
            </i-col>
          </Row>
          <Row>
            <i-col span="24">
              <FormItem label="替换模板名称">
                <Select
                  placeholder="选择替换模板"
                  style="margin-top: 5px"
                  v-model="form.change_template_id"
                  clearable
                >
                  <Option v-for="item in template_item_list"
                          :value="item.id"
                          :key="item.id">
                    {{ item.template_name }}
                  </Option>
                </Select>
              </FormItem>
            </i-col>
          </Row>
        </Form>
      </Modal>
      <Modal
        title="保存模板"
        width="400"
        v-model="jenkins_modal_show"
        :mask-closable="true"
        ok-text="查看详情"
        @on-ok="jenkins_info">
        <div>
          {{jenkins_msg}}
        </div>
      </Modal>
    </Card>
  </div>
</template>
<script>
  import {
    getAllEnvInfo,
    getAllCanCheckboxApp,
    getTemplateInfo,
    getAppsByTemplate,
    updateTestEnvApplyInfo,
    updateTemplateInfo,
    callJenkins,
    getJenkinsLog
  } from "@/api/test-env";
  import {getEmailAddresses} from "@/api/iterative-plan";
  import time from '@/mixin/time'
  import isEmpty from '@/mixin/isEmpty'


  export default {
    mixins: [time, isEmpty],
    data() {
      return {
        save_modal_show: false,
        jenkins_modal_show: false,
        jenkins_msg: '',
        jenkins_url: '',
        test_id_show: false,
        collepseAll: false,
        pagination: {
          page: 1,
          size: 10,
          total: 0,
        },
        form: {
          template_id: 1,
          applicant: this.$store.state.user.userName,
          new_template_name: '',
          change_template_id: null,
          env_id: null,
          env_value: '',
          apply_reason: null,
          invalid_at: null,
          clean_cache: '1',
          test_set_id: '',
          init_db: 'None',
          use_env_time: null,
          template_name: '',
          pipeline_list: [],
          allSelectedMails: [],
          tms_list: [],
          tp_list: [],
          pa_list: [],
        },
        rule: {
          applicant: [
            {required: true, message: '申请人不能为空', trigger: 'blur'}
          ],
          env_id: [
            {type: 'number', required: true, message: '测试环境ID不能为空', trigger: 'blur'},
          ],
          apply_reason: [
            {required: true, message: '测试环境用途不能为空', trigger: 'blur'},
            {max: 100, message: '用途描述不能超过100个字节', trigger: 'blur'}
          ],
          invalid_at: [
            {type: 'date', required: true, message: '环境失效时间不能为空', trigger: 'change'},
          ]
        },
        dateOption: {
          disabledDate(date) {
            return date && date.valueOf() < Date.now() - 86400000;
          }
        },
        modal: {
          show: false,
          title: '',
        },
        noDataText: "无可用环境，可去环境信息页面回收",
        tableLoading: false,
        tableData: [],
        tableColumns: [
          {
            title: '环境套名',
            key: 'suite_code',
            align: 'center',
          },
          {
            title: '虚拟机',
            key: 'node_ip',
            align: 'center',
          },
          {
            title: '容器',
            key: 'node_docker',
            align: 'center',
          },
          {
            title: '申请人',
            key: 'apply_user',
            align: 'center',
          },
          {
            title: '用途',
            key: 'apply_reason',
            align: 'center',
          },
          {
            title: '截止日期',
            key: 'invalid_at',
            align: 'center',
            width: 180,
          },
          {
            title: '操作',
            align: 'center',
            fixed: 'right',
            render: (h, params) => {
              if (params.row.apply_user == null || params.row.apply_user === this.$store.state.user.userName) {
                return h('Button', {
                  props: {
                    type: 'primary',
                    size: 'small'
                  },
                  on: {
                    click: () => {
                      this.selectEnv(params)
                    }
                  }
                }, '选择申请')
              }
            }
          }
        ],

        // 添加新的页面元素
        template_item_list: [],
        app_list: [],
        pipeline_item_list: [],
        tms_item_list: [],
        tp_item_list: [],
        pa_item_list: [],
        allFilterMails: [],
      }
    },
    mounted() {
      this.initThisVue();
    },
    methods: {
      //展开收起
      allHandleToggleCollepse() {
        this.collepseAll = !this.collepseAll;
      },
      setPagParams(page, size, total) {
        /**
         * 设置分页参数
         */
        if (!this.isEmpty(page)) {
          this.pagination.page = page
        }
        if (!this.isEmpty(size)) {
          this.pagination.size = size
        }
        if (!this.isEmpty(total)) {
          this.pagination.total = total
        }
      },
      initThisVue() {
        getEmailAddresses().then(res => {
          if (res.data.code === 0) {
            this.allFilterMails = res.data.data;
          }
        });
      },
      getEnvData(data) {
        let vm = this
        vm.tableLoading = true
        getAllEnvInfo(data)
          .then(function (response) {
            vm.setPagParams('', '', response.data.data.count)
            vm.tableData = vm.dataSerializer(response.data.data.results)
            vm.tableLoading = false
            vm.showModal()
          })
          .catch(function (error) {
            vm.tableLoading = false
            vm.$Message.error("获取环境使用情况信息失败")
            console.log(error)
          })
      },
      searchEnv() {
        let data = {
          page: this.pagination.page,
          size: this.pagination.size
        }
        this.getEnvData(data)
      },
      selectEnv(params) {
        this.form.env_id = params.row.suite_id;
        this.form.env_value = params.row.suite_code;
        this.form.apply_reason = params.row.apply_reason;
        this.form.invalid_at = params.row.invalid_at;
        this.modal.show = false;
        this.init_Template();
        this.init_app_checkbox(params.row.suite_id);
      },
      showModal() {
        this.modal.show = true
      },
      pageChange(page) {
        /**
         * 分页中改变size触发的函数
         */
        this.setPagParams(page, '', '')
        let data = {
          page: this.pagination.page,
          size: this.pagination.size
        }
        this.getEnvData(data)
      },
      pageSizeChange(size) {
        this.setPagParams('', size, '')
        let data = {
          page: 1,
          size: this.pagination.size
        }
        this.getEnvData(data)
      },
      //确认申请
      handleSubmit(name) {
        let vm = this;
        this.$refs[name].validate((valid) => {
          if (valid) {
            //调用更新环境申请记录信息
            updateTestEnvApplyInfo(this.form).then(res => {
              console.log('更新环境申请记录信息成功')
            }).catch(function (error) {
              vm.$Message.error("更新环境申请记录信息失败");
              console.log(error)
            });
            //调用jenkins
            callJenkins(this.form).then(res => {
              vm.jenkins_url = res.data.data.jenkins_url;
              vm.jenkins_msg = res.data.data.msg;
              vm.jenkins_modal_show = true;
            }).catch(function (error) {
              vm.$Message.error("调用jenkins失败");
              console.log(error)
            });

          } else {
            vm.$Message.error("测试环境申请表单提交失败")
          }
        })
      },
      //重置申请
      handleReset(name) {
        this.$refs[name].resetFields();
      },
      dataSerializer(data) {
        let vm = this
        if (data == null || data.length === 0) {
          return []
        } else {
          data.forEach(element => {
            if (!vm.isEmpty(element.invalid_at)) {
              element.invalid_at = vm.UtcDateToLocalDate(new Date(element.invalid_at))
            }
          })
          return data.filter(function (val) {
            return val.apply_user == null;
          });

        }
      },
      // 「模块」改变
      template_select_change() {
        if (this.form.template_id !== null && this.form.template_id !== undefined && this.form.template_id !== '') {
          let vm = this;
          this.form.template_name = this.template_item_list.filter(function (val) {
            return val.id == vm.form.template_id
          })[0].template_name;
          let data = {
            "template_id": this.form.template_id,
          }
          getAppsByTemplate(data).then(res => {
            this.app_list = res.data.data.app_list;
            this.form.clean_cache = res.data.data.template_info.clean_cache;
            this.form.test_set_id = res.data.data.template_info.test_set_id;
            this.form.init_db = res.data.data.template_info.init_db;
            this.form.use_env_time = res.data.data.template_info.use_env_time;
            if (res.data.data.template_info.all_selected_mails != null) {
              this.form.allSelectedMails = res.data.data.template_info.all_selected_mails.split(',');
            } else {
              this.form.allSelectedMails = [];
            }

            this.form.pipeline_list = res.data.data.app_list.filter(function (val) {
              return vm.pipeline_item_list.indexOf(val) > -1
            });
            this.form.tms_list = res.data.data.app_list.filter(function (val) {
              return vm.tms_item_list.indexOf(val) > -1
            });
            this.form.tp_list = res.data.data.app_list.filter(function (val) {
              return vm.tp_item_list.indexOf(val) > -1
            });
            this.form.pa_list = this.pa_item_list;
            /* this.form.pa_list = res.data.data.app_list.filter(function (val) {
               return vm.pa_item_list.indexOf(val) > -1
             });*/

          })
        } else {
          this.form.pipeline_list = [];
          this.form.tms_list = [];
          this.form.tp_list = [];
          this.form.pa_list = [];
        }
      },
      //初始化加载模板信息
      init_Template() {
        getTemplateInfo().then(res => {
          this.template_item_list = res.data.data["data_list"]
        })
        //this.init_template_name_default()
      },
      //初始化加载复选框可选应用信息
      init_app_checkbox(suite_id) {
        getAllCanCheckboxApp({"suite_id": suite_id}).then(res => {
          this.pipeline_item_list = res.data.data["pipeline_item_list"];
          this.tms_item_list = res.data.data["tms_item_list"];
          this.tp_item_list = res.data.data["tp_item_list"];
          this.pa_item_list = res.data.data["pa_item_list"];
          this.template_select_change();
        })
      },
      //保存模板
      save_template() {
        if ((this.form.new_template_name == null || this.form.new_template_name === '') && (this.form.change_template_id == null || this.form.change_template_id === '')) {
          this.$Message.error("请至少填写新模板名或选择要替换模板")
        } else {
          //调用更新模板详情
          updateTemplateInfo(this.form).then(res => {
            this.$Message.success("保存模板成功");
          }).catch(function (error) {
            this.$Message.error("保存模板失败");
            console.log(error)
          });
        }
      },
      //查看jenkins详情
      jenkins_info() {
        let vm = this;
        getJenkinsLog({'job_business_id': this.jenkins_url}).then(res => {
          window.open(
            res.data.data
          );
        }).catch(function (error) {
          vm.$Message.error("查询jenkins日志失败");
          console.log(error)
        });
      },

    }

  }
</script>
