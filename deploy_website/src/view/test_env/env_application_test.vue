<style scoped>
.card{
    padding-top: 10px;
    padding-left: 10px
}
.formItem{
    width: 50%;
}
.formButton{
    width: 50%;
    display: flex;
    justify-content: flex-end
}
.pag{
    display: flex;
    justify-content: center;
    /* margin-top: 24px; */
}
/* .env_application /deep/ .ivu-input-group-prepend, .ivu-input-group-append{
    background-color: #2d8cf0;
    color: #ffffff;
}
.env_application /deep/ .ivu-input-group-prepend, .ivu-input-group-append .ivu-select, .ivu-select-arrow{
    color: #ffffff;
}
.env_application /deep/ .ivu-input-group-prepend .ivu-select{
    color: #ffffff;
} */
</style>
<style lang='less'>
.ivu-modal-header{
    text-align: center;
}
</style>
<template>
    <div class="env_application">
        <Card class="card">
            <h2>测试环境申请+初始化</h2>
            <Divider />
            <Form ref="form" :model="form" :rules="rule" :label-width="120">
                <FormItem label="申请人" prop="applicant" class="formItem">
                    <Input v-model="form.applicant" placeholder="输入测试环境申请人" disabled></Input>
                </FormItem>
                <FormItem label="测试环境" prop="env_id" class="formItem">
                    <Input v-model="env_value" placeholder="选择要申请的测试环境" disabled>
                        <Select v-model="select_team" slot="prepend" style="width: 70px">
                            <Option v-for="(value,index) in team" 
                                :value="value" :key="index"
                                placeholder="归属团队">
                                {{value}}
                            </Option>
                        </Select>
                        <Button slot="append" icon="ios-search" @click="searchEnv(select_team)">
                        </Button>
                    </Input>
                </FormItem>
                <FormItem label="测试环境截止日期" prop="invalid_at" class="formItem">
                    <DatePicker type="datetime" :options="dateOption" placeholder="选择环境使用截止日期" v-model="form.invalid_at"></DatePicker>
                </FormItem>
                <FormItem label="测试环境用途" prop="apply_reason" class="formItem">
                    <Input v-model="form.apply_reason" type="textarea" placeholder="输入测试环境用途"></Input>
                </FormItem>
                <FormItem class="formButton">
                    <Button @click="handleReset('form')">重置申请</Button>
                    <Button type="primary" style="margin-left: 8px" @click="handleSubmit('form')">提交申请</Button>
                </FormItem>
            </Form>
            <Modal
                :title="modal.title"
                v-model="modal.show"
                width="1080"
                :mask-closable="true"
            >
                <Table :loading="tableLoading"
                    :columns="tableColumns" 
                    :data="tableData"
                    :no-data-text="noDataText"
                    >
                </Table>
                <div slot="footer">
                    <Page class="pag"
                        :total="pagination.total" 
                        :current="pagination.page" 
                        :page-size="pagination.size" 
                        show-total 
                        show-elevator 
                        show-sizer 
                        @on-change="pageChange" 
                        @on-page-size-change="pageSizeChange" 
                    />
                </div>
            </Modal>
        </Card>
    </div>
</template>
<script>
import {getEnv, createApplicationOrderTest} from "@/api/test-env";
import time from '@/mixin/time'
import isEmpty from '@/mixin/isEmpty'
export default {
    mixins: [time,isEmpty],
    data () {
        return {
            pagination: {
                page: 1,
                size: 10,
                total: 0,
            },
            select_team: 'TMS',
            env_value: null,
            team: ['TMS', 'TP'],
            form: {
                applicant: this.$store.state.user.userName,
                env_id: null,
                apply_reason: null,
                invalid_at: null,
            },
            rule: {
                applicant: [
                    {required: true, message: '申请人不能为空', trigger: 'blur'}
                ],
                env_id: [
                    {type: 'number', required: true, message: '测试环境ID不能为空', trigger: 'blur'},
                ],
                apply_reason: [
                    {required: true, message: '测试环境用途不能为空', trigger: 'blur'},
                    {max: 100, message: '用途描述不能超过100个字节', trigger: 'blur'}
                ],
                invalid_at: [
                    {type: 'date', required: true, message: '环境失效时间不能为空', trigger: 'change'},
                ]
            },
            dateOption: {
                disabledDate (date) {
                    return date && date.valueOf() < Date.now() - 86400000;
                }
            },
            modal: {
                show: false,
                title: '',
            },
            noDataText: "暂时没有数据呦",
            tableLoading: false,
            tableData: [],
            tableColumns: [
                {
                    title: '环境名称',
                    key: 'name',
                    align: 'center',
                },
                {
                    title: 'TP',
                    key: 'tp',
                    align: 'center',
                },
                {
                    title: 'TMS',
                    key: 'tms',
                    align: 'center',
                },
                {
                    title: '申请人',
                    key: 'applicant',
                    align: 'center',
                },
                {
                    title: '用途',
                    key: 'apply_reason',
                    align: 'center',
                },
                {
                    title: '截止日期',
                    key: 'invalid_at',
                    align: 'center',
                    width: 180,
                },
                {
                    title: '操作',
                    align: 'center',
                    fixed: 'right',
                    render: (h, params) => {
                        if (params.row.status != "使用中"){
                            return h('Button', {
                                props: {
                                    type: 'primary',
                                    size: 'small'
                                },
                                on: {
                                    click: () => {
                                        this.selectEnv(params)
                                    }
                                }
                            }, '申请')
                        }
                    }
                }
            ],
        }
    },
    methods: {
        setPagParams (page, size, total) {
            /**
             * 设置分页参数
             */
            if (!this.isEmpty(page)){
                this.pagination.page = page
            }
            if (!this.isEmpty(size)){
                this.pagination.size = size
            }
            if (!this.isEmpty(total)){
                this.pagination.total = total
            } 
        },
        getEnvData (data) {
            let vm = this
            vm.tableLoading = true
            getEnv(data)
            .then(function(response){
                vm.setPagParams('','',response.data.count)
                vm.tableData = vm.dataSerializer(response.data.results)
                vm.tableLoading = false
                vm.showModal(vm.select_team)
            })
            .catch(function(error){
                vm.tableLoading = false
                vm.$Message.error("获取团队的环境使用情况信息失败")
                console.log(error)
            })
        },
        searchEnv (team) {
            let data = {
                page: this.pagination.page,
                size: this.pagination.size,
                team: team,
            }
            this.getEnvData(data)
        },
        selectEnv (params) {
            this.form.env_id = params.row.id
            this.env_value = params.row.name
            console.log(this.env_value)
            this.modal.show = false
        },
        showModal (team) {
            this.modal.title = team+"的测试环境使用情况"
            this.modal.show = true
        },
        pageChange (page){
            /**
             * 分页中改变size触发的函数
             */
            this.setPagParams(page,'','')
            let data = {
                page: this.pagination.page,
                size: this.pagination.size,
                team: this.select_team
            }
            this.getEnvData(data)
        },
        pageSizeChange (size) {
            this.setPagParams('',size,'')
            let data = {
                page: 1,
                size: this.pagination.size,
                team: this.select_team
            }
            this.getEnvData(data)
        },
        handleSubmit (name) {
            let vm = this
            this.$refs[name].validate((valid) => {
                if (valid) {
                    let data = {}
                    for (let key in vm.form){
                        if (key == "invalid_at"){
                            data[key] = vm.UtcDateToLocalDate(new Date(vm.form[key]))
                        }else{
                            data[key] = vm.form[key]
                        }
                    }
                    console.log(data)
                    createApplicationOrderTest(data)
                    .then(function(response){
                        vm.$Message.success("测试环境申请订单提交成功")
                        vm.$router.push({name: 'env_pipeline_detail', query:{order_code: response.data.code}})
                    })
                    .catch(function(error){
                        vm.$Message.error("测试环境申请订单提交失败")
                    })
                }else {
                    vm.$Message.error("测试环境申请表单提交失败")
                }
            })
        },
        handleReset (name) {
            this.$refs[name].resetFields();
        },
        dataSerializer (data) {
            let vm = this
            if (data == null || data.length == 0){
                return []
            }else{
                data.forEach(element => {
                    if (!vm.isEmpty(element.invalid_at)){
                        element.invalid_at = vm.UtcDateToLocalDate(new Date(element.invalid_at))
                    }
                })
                return data
            }
        }
    }
}
</script>