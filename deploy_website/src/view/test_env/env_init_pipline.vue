<style scoped>
.card{
    padding-top: 10px; 
    padding-left: 10px
}
.pag{
    display: flex;
    justify-content: center;
    margin-top: 24px;
}
</style>
<style>
.env_init_pipline .demo-spin-icon-load{
    animation: ani-demo-spin 1s linear infinite;
}
@keyframes ani-demo-spin {
    from { transform: rotate(0deg);}
    50%  { transform: rotate(180deg);}
    to   { transform: rotate(360deg);}
}
</style>
<template>
    <div class="env_init_pipline">
        <Card class="card">
            <h2>环境操作记录</h2>
            <Divider />
            <Table :loading="tableLoading"
                :columns="tableColumns" 
                :data="tableData"
                :no-data-text="noDataText"
                >
            </Table>
            <Page class="pag"
                :total="pagination.total" 
                :current="pagination.page" 
                :page-size="pagination.size" 
                show-total 
                show-elevator 
                show-sizer 
                @on-change="pageChange" 
                @on-page-size-change="pageSizeChange" 
            />
        </Card>
    </div>
</template>
<script>
import {getEnvLog, getEnvApp} from "@/api/test-env"
import time from '@/mixin/time'
import isEmpty from '@/mixin/isEmpty'
export default {
    mixins: [time, isEmpty],
    data () {
        return {
            interval: null,
            pagination: {
                page: 1,
                size: 10,
                total: 0,
            },
            tableLoading: false,
            noDataText: "暂时没有数据呦",
            tableData: [],
            tableColumns: [
                {
                    title: '状态',
                    width: 70,
                    fixed: 'left',
                    align: 'center',
                    render: (h, params) => {
                        if (params.row.status == "执行成功"){
                            return h('Icon', {
                                attrs: {
                                    title: '执行成功'
                                },
                                props: {
                                    type: 'ios-checkmark-circle-outline',
                                    size: '32',
                                    color: '#19be6b'
                                },
                            })
                        }else if (params.row.status == "执行失败"){
                            return h('Icon', {
                                attrs: {
                                    title: '执行失败'
                                },
                                props: {
                                    type: 'ios-close-circle-outline',
                                    size: '32',
                                    color: '#ed4014'
                                },
                            })
                        }else if (params.row.status == "执行中"){
                            return h('Icon', {
                                'class': {
                                    'demo-spin-icon-load': true,
                                },
                                attrs: {
                                    title: '执行中',
                                },
                                props: {
                                    type: 'ios-loading',
                                    size: '32',
                                    color: '#ff9900'
                                },
                            })
                        }else if (params.row.status == "未执行"){
                            return h('Icon', {
                                attrs: {
                                    title: '未执行'
                                },
                                props: {
                                    type: 'ios-remove-circle-outline',
                                    size: '32',
                                    color: '#2db7f5'
                                },
                            })
                        }
                    }
                },
                {
                    title: '订单编号',
                    key: 'order_code',
                    tooltip: true,
                    align: 'center',
                },
                {
                    title: '部署服务器IP',
                    key: 'deploy_ip',
                    width: 140,
                    tooltip: true,
                    align: 'center',
                },
                {
                    title: '开始时间',
                    key: 'start_at',
                    width: 180,
                    tooltip: true,
                    align: 'center',
                },
                {
                    title: '申请人',
                    key: 'applicant',
                    tooltip: true,
                    align: 'center',
                },
                {
                    title: '已耗时(s)',
                    key: 'time_cost',
                    tooltip: true,
                    align: 'center',
                },
                {
                    title: '操作',
                    align: 'center',
                    width: 80,
                    fixed: 'right',
                    render: (h, params) => {
                        return h('Button', {
                            props: {
                                type: 'info',
                                size: 'small'
                            },
                            on: {
                                click: () => {
                                    this.envStateDetail(params)
                                }
                            }
                        }, '详情')
                    }
                }
            ],
        }
    },
    mounted () {
        let vm = this
        vm.init()//第一次执行，防止第一次延迟
        clearInterval(this.interval)
        this.interval = setInterval(function(){
            vm.init()
        }, 10000)
    },
    destroyed () {
        clearInterval(this.interval)
    },
    methods: {
        init () {
            let data = {
                page: this.pagination.page,
                size: this.pagination.size,
            }
            this.getEnvLogData(data)
        },
        setPagParams (page, size, total) {
            /**
             * 设置分页参数
             */
            if (!this.isEmpty(page)){
                this.pagination.page = page
            }
            if (!this.isEmpty(size)){
                this.pagination.size = size
            }
            if (!this.isEmpty(total)){
                this.pagination.total = total
            } 
        },
        getEnvLogData (data) {
            let vm = this
            vm.tableLoading = true
            getEnvLog(data)
            .then(function(response){
                vm.setPagParams('','',response.data.count)
                vm.tableData = vm.dataSerializer(response.data.results)
                vm.tableLoading = false
            })
            .catch(function(error){
                vm.tableLoading = false
                vm.$Message.error("获取测试环境初始化状态信息失败")
                console.log(error)
            })
        },
        pageChange (page) {
            /**
             * 分页中改变size触发的函数
             */
            this.setPagParams(page,'','')
            let data = {
                page: this.pagination.page,
                size: this.pagination.size,
            }
            this.getEnvLogData(data)
        },
        pageSizeChange (size) {
            this.setPagParams('',size,'')
            let data = {
                page: 1,
                size: this.pagination.size,
            }
            this.getEnvLogData(data)
        },
        envStateDetail (params) {
            if (this.isEmpty(params.row.order.code) && !this.isEmpty(params.row.sid)){
                this.$router.push({name: 'env_pipeline_detail', query:{sid: params.row.sid}})
            }else{
                this.$router.push({name: 'env_pipeline_detail', query:{order_code: params.row.order.code}})
            }
        },
        dataSerializer (data) {
            let vm = this
            if (data == null || data.length == 0){
                return []
            }else {
                data.forEach(element => {
                    if (vm.isEmpty(element.start_at)){
                        element.time_cost = "未知"
                    }else if (vm.isEmpty(element.end_at)){
                        element.time_cost = (new Date() - new Date(element.start_at))/1000 > 0 ? (new Date() - new Date(element.start_at))/1000 : 0
                    }else{
                        element.time_cost = (new Date(element.end_at) - new Date(element.start_at))/1000
                    }
                    element.start_at = vm.UtcDateToLocalDate(new Date(element.start_at))
                    element.end_at = vm.UtcDateToLocalDate(new Date(element.end_at))
                    element.order_code = element.order.code
                    element.applicant = element.order.applicant
                });
                return data
            }
        }
    }
}
</script>
