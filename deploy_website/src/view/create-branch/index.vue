<template>
  <Card class="branch_card" shadow style="overflow-y:auto;">
    <Form
      ref="formValidate"
      :model="formValidate"
      :rules="ruleValidate"
      :label-width="120"
      style="text-align: left"
    >
      <FormItem label="分支名称：" prop="branch_name">
        <Input v-model="formValidate.branch_name" placeholder="填写分支名称"/>
      </FormItem>
      <FormItem label="类型：" prop="branch_style">
        <RadioGroup v-model="formValidate.branch_style">
          <Radio label="dev">开发</Radio>
          <Radio label="test">测试</Radio>
          <Radio label="jinji">紧急</Radio>
        </RadioGroup>
      </FormItem>
      <FormItem label="git库选择：" prop="project_list">
        <Tree :data="data2" ref="treeValidate" show-checkbox v-model="formValidate.project_list"></Tree>
      </FormItem>
      <FormItem label="截止时间：">
        <Row>
          <i-col span="11">
            <FormItem prop="date">
              <DatePicker type="date" placeholder="Select date" v-model="formValidate.date"></DatePicker>
            </FormItem>
          </i-col>
        </Row>
      </FormItem>
      <FormItem label="描述：" prop="desc">
        <Input
          v-model="formValidate.desc"
          type="textarea"
          :autosize="{minRows: 2,maxRows: 5}"
          placeholder="Enter something..."
        />
      </FormItem>
      <FormItem>
        <Button type="primary" @click="handleSubmit('formValidate')" :loading="btnDisabled">提交</Button>
        <Button @click="handleReset('formValidate')" style="margin-left: 8px">重置</Button>
        <Button @click="getGitRopes('true')" style="margin-left: 8px">同步git项目</Button>
      </FormItem>
    </Form>
  </Card>
</template>


<script>
import { createBranch } from "@/api/form-request";
import { getGitRopesApi } from "@/api/git-iterative";

export default {
  data() {
    return {
      data2: [],
      btnDisabled: false,
      formValidate: {
        branch_name: "",
        branch_style: "",
        project_list: "",
        date: "",
        desc: ""
      },
      ruleValidate: {
        branch_name: [
          {
            required: true,
            message: "分支名不能为空",
            trigger: "blur"
          }
        ],
        branch_style: [
          { required: true, message: "请选择迭代类型", trigger: "change" }
        ],
        date: [
          {
            required: true,
            type: "date",
            message: "请选择日期",
            trigger: "change"
          }
        ],
        desc: [
          {
            required: true,
            message: "填写功能描述",
            trigger: "blur"
          },
          {
            type: "string",
            min: 5,
            message: "不可以少于5个字的描述",
            trigger: "blur"
          }
        ]
      }
    };
  },
  methods: {
    handleSubmit(param) {
       this.btnDisabled = true
      console.log(this.btnDisabled)
       this.$Spin.show({
                    render: (h) => {
                        return h('div', [
                            h('Icon', {
                                'class': 'demo-spin-icon-load',
                                props: {
                                    type: 'ios-loading',
                                    size: 18
                                }
                            }),
                            h('div', '分支拉取中请稍等。。。')
                        ])
                    }
                });
      var vm = this;
      console.log(vm.$refs["treeValidate"].getCheckedAndIndeterminateNodes());
      let choicesAll = vm.$refs["treeValidate"].getCheckedAndIndeterminateNodes();
      let choiceData = [];
      let gourp_cout=0
      choicesAll.forEach(item => {
        //alert(JSON.stringify(item))
        if ("children" in item) {
          gourp_cout=gourp_cout+1
        } else {
          choiceData.push(item.value);
        }
      });
      console.log(choiceData);
      //不可以一次申请两个组的分支
      if (gourp_cout>1){
        this.$Message.error("不能同时申请两个以上团队的分支!!!")
        this.$Spin.hide();
        this.btnDisabled = false;
      }
      else{
      vm.$refs[param].validate(valid => {
        if (valid) {
          let branch_info = {
            branch_name: vm.formValidate.branch_name,
            branch_style: vm.formValidate.branch_style,
            deadline: vm.formValidate.date,
            desc: vm.formValidate.desc,
            project_list: choiceData
          };

          createBranch(branch_info).then(result => {
            //alert(JSON.stringify(result))
            if ("error" in result.data){
              this.$Message.error(result.data["error"]);
            }
            else{
            this.$Message.success(result.data["msg"]);
            }
           this.$Spin.hide();
           this.btnDisabled = false;
          });
        } else {
          this.$Message.error("Fail!");
           this.$Spin.hide();
           this.btnDisabled = false;
        }

      });
      }
    },
    handleReset(name) {
      this.$refs[name].resetFields();
    },
    getGitRopes(sync_gitlab){
    let data = {"sync_gitlab":sync_gitlab};
    getGitRopesApi(data)
       .then(res => {
    //alert(JSON.stringify(res))
    this.data2 = res.data['repos_tree'];
  })

    }
  },
  mounted(){

   this.getGitRopes("false")
  }

};
</script>

<style>
</style>
