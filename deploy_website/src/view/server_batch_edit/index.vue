<!--
 * @Author: chao.bao <EMAIL>
 * @Date: 2025-06-27 09:55:26
 * @LastEditors: chao.bao <EMAIL>
 * @LastEditTime: 2025-07-09 13:38:29
 * @FilePath: /website_web/deploy_website/src/view/server_batch_reset/index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <Card class="service-publish-card">
        <div class="card-body">
            <!-- 应用 -->
            <div class="header_box">
                <h3 class="btn_item">选应用</h3>
                <Button class="btn_item" type="primary" @click="handleShowModal">选应用</Button>
            </div>
            <div class="main-container" @dragover="allowDrop">
                <div
                    v-for="container in appList"
                    :key="container.module_name + container.suite_code"
                    class="container-box"
                    @drop="drop($event, container)"
                    @dragover="allowDrop"
                >
                    <div style="height: 42px;display: flex;justify-content: center;align-items: center;">
                        {{ container.module_name }}
                    </div>
                    <div :class="isDragged ? 'container dragged' : 'container'">
                        <div
                            v-for="(node_ip, index) in container.node_ips"
                            :key="index"
                            class="item"
                            draggable="true"
                            @dragstart="drag($event, node_ip, container)"
                        >
                            <div>{{ node_ip }}</div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 编排 -->
            <div class="header_box">
                <h3 class="btn_item">Phase</h3>
                <Button class="btn_item" type="primary" @click="add">添加Phase</Button>
            </div>
            <div class="main-container" @dragover="allowDrop">
                <div
                    v-for="(container, containerIndex) in containers"
                    :key="container.id"
                    class="container-box"
                    @drop="drop($event, container)"
                    @dragover="allowDrop"
                >
                    <div>Phase-{{ containerIndex + 1 }}</div>
                    <div :class="isDragged ? 'container dragged' : 'container'">
                        <div
                            v-for="(item, index) in container.items"
                            :key="index"
                            class="item"
                            draggable="true"
                            @dragstart="drag($event, item, container)"
                        >
                            <div class="item_box">
                                <div>
                                    <div>{{ item.module_name }}</div>
                                    <div>{{ item.node_ip }}</div>
                                </div>
                                <!-- 删除icon -->
                                <Icon
                                    type="ios-close"
                                    size="20"
                                    color="#f56c6c"
                                    style="cursor:auto"
                                    @click="deleteItem(containerIndex, index)"
                                />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="save_btn">
            <Button class="btn_item" type="primary" :loading="loading" @click="saveGroup">保存</Button>
        </div>
        <Modal v-model="showModal" title="选应用" width="1000">
            <Form ref="formInline" :model="formInline" inline>
                <FormItem prop="user">
                    <Select
                        clearable
                        filterable
                        v-model="formInline.module_name"
                        style="width:200px"
                        placeholder="按应用名筛选"
                    >
                        <Option v-for="item in app_name_list" :value="item" :key="item">{{ item }}</Option>
                    </Select>
                </FormItem>
                <FormItem prop="password">
                    <Select
                        clearable
                        filterable
                        v-model="formInline.suite_code"
                        style="width:200px"
                        placeholder="按调用链筛选"
                    >
                        <Option v-for="item in call_chain_list" :value="item.suite_code" :key="item.suite_code">{{
                            item.suite_name
                        }}</Option>
                    </Select>
                </FormItem>
                <FormItem prop="user">
                    <Select
                        clearable
                        filterable
                        v-model="formInline.department_name"
                        style="width:200px"
                        placeholder="按部门筛选"
                    >
                        <Option v-for="item in team_list" :value="item" :key="item">{{ item }}</Option>
                    </Select>
                </FormItem>
                <FormItem prop="password">
                    <Select
                        clearable
                        filterable
                        v-model="formInline.team_name"
                        style="width:200px"
                        placeholder="按团队筛选"
                    >
                        <Option v-for="item in department_list" :value="item" :key="item">{{ item }}</Option>
                    </Select>
                </FormItem>
                <FormItem>
                    <Button type="primary" @click="handleSearch">查询</Button>
                </FormItem>
            </Form>
            <Table
                border
                ref="selection"
                :columns="columns"
                :data="tableData"
                @on-selection-change="handleSelectionChange"
            ></Table>
            <Page
                style="margin-top: 10px"
                :total="total"
                :page-size="page_size"
                :current="page_num"
                show-total
                @on-change="changePage"
            ></Page>
            <div slot="footer">
                <Button type="primary" @click="ok">确定</Button>
                <Button @click="cancel">取消</Button>
            </div>
        </Modal>
    </Card>
</template>

<script>
import {
    get_java_app,
    get_active_suite,
    get_team_name_list,
    batch_publish_app_list,
    save_restart_app,
    get_reboot_app_list,
    save_reboot_flow,
    get_reboot_flow,
    check_reboot_flow
} from './api'
export default {
    data() {
        return {
            id: '',
            loading: false,
            // 上方容器数据
            appList: [
                // {
                //     id: 1,
                //     isApp: true,
                //     items: [
                //         {
                //             node_ip: 'app1',
                //             step_num: 1
                //         }
                //     ]
                // },
                // {
                //     id: 2,
                //     isApp: true,
                //     items: [
                //         {
                //             node_ip: 'app2',
                //             step_num: 2
                //         }
                //     ]
                // }
            ],
            // 下方容器数据
            containers: [
                // {
                //     id: 3,
                //     isApp: false,
                //     items: [
                //         {
                //             node_ip: 'app3',
                //             step_num: 1
                //         }
                //     ]
                // },
                // {
                //     id: 4,
                //     isApp: false,
                //     items: [
                //         {
                //             node_ip: 'app4',
                //             step_num: 2
                //         }
                //     ]
                // }
            ],
            isDragged: false,
            showModal: false,
            page_num: 1,
            page_size: 10,
            total: 0,
            formInline: {
                module_name: '',
                suite_code: '',
                team_name: '',
                department_name: ''
            },
            columns: [
                {
                    type: 'selection',
                    width: 60,
                    align: 'center'
                },
                {
                    title: '应用名',
                    key: 'module_name'
                },
                {
                    title: '调用链',
                    key: 'suite_code'
                },
                {
                    title: '当前版本',
                    key: 'branch_name'
                },
                {
                    title: '所属部门',
                    key: 'department_name'
                },
                {
                    title: '所属团队',
                    key: 'team_name'
                }
            ],
            tableData: [],
            app_name_list: [],
            call_chain_list: [],
            department_list: [],
            team_list: [],
            selectedData: [], // 当前选中的数据
            allSelectedData: [] // 所有选中的数据
        }
    },
    methods: {
        saveGroup() {
            console.log('saveGroup-----------', this.containers)
            // 获取编排数据的数量
            let phase_num = 0
            this.containers.forEach(container => {
                phase_num += container.items.length
            })
            if (phase_num > 2000) {
                this.$Message.warning('编排节点数量不能超过2000')
                return
            }
            const flow_json = {}
            this.containers.map(container => {
                flow_json[`phase_${container.id}`] = container.items
            })
            console.log('flow_json-----------', flow_json)
            check_reboot_flow({
                flow_json
            }).then(res => {
                if (res.data.status === 'success') {
                    // 保存编排
                    save_reboot_flow({
                        id: this.id || '',
                        flow_json
                    }).then(saveRes => {
                        if (saveRes.data.status === 'success') {
                            this.$Message.success('保存成功')
                            this.$router.push('/ops_service/server_batch_reset')
                        } else {
                            this.$Message.error(saveRes.data.msg)
                        }
                    })
                } else {
                    // 弹出确认框，是否保存
                    this.$Modal.confirm({
                        title: '提示',
                        content: res.data.msg,
                        onOk: () => {
                            save_reboot_flow({
                                id: this.id || '',
                                flow_json
                            }).then(saveRes => {
                                if (saveRes.data.status === 'success') {
                                    this.$Message.success('保存成功')
                                    this.$router.push('/ops_service/server_batch_reset')
                                } else {
                                    this.$Message.error(saveRes.data.msg)
                                }
                            })
                        }
                    })
                }
            })
        },
        drag(event, item, container) {
            // container当前拖拽的对象所在的数组集合
            console.log('drag-----拖拽开始', event, item, container)

            this.isDragged = true
            // 暂存当前拖拽对象
            if (typeof item === 'string') {
                event.dataTransfer.setData(
                    'itemData',
                    JSON.stringify({
                        node_ip: item,
                        module_name: container.module_name,
                        suite_code: container.suite_code
                    })
                )
            } else {
                event.dataTransfer.setData('itemData', JSON.stringify(item))
            }
            if (container) {
                event.dataTransfer.setData('isApp', !!container.module_name)
            }
        },
        allowDrop(event) {
            console.log('allowDrop-----拖拽中')
            event.preventDefault()
        },
        drop(event, targetContainer, n) {
            // 上面往下面拖拽！！或者容器间互相拖拽
            // targetContainer是推拽对象的新容器
            console.log('drop-----拖拽鼠标释放', targetContainer)
            event.preventDefault()
            this.isDragged = false
            // 获取当前拖拽对象
            const itemData = JSON.parse(event.dataTransfer.getData('itemData'))
            // 拖拽的容器id
            const sourceIsApp = event.dataTransfer.getData('isApp')
            const targetIsApp = targetContainer.isApp

            if (sourceIsApp === 'true' && targetIsApp === false) {
                // 从顶部移动下来
                console.log('情况1 - 从顶部移动下来', itemData)
                // 复制到目标容器
                this.containers.forEach(container => {
                    if (container.id === targetContainer.id) {
                        // 如果目标容器中没有当前app_name，则将数据插入到最前面
                        const isExist = container.items.find(item => item.node_ip === itemData.node_ip)
                        if (!isExist) {
                            container.items.unshift(itemData)
                        }
                    }
                })
            } else if (sourceIsApp === 'false' && targetIsApp === false) {
                // 容器间互相拖拽
                console.log('情况2 - 容器间互相拖拽', itemData)
                // 从源容器移除item
                const isExit = targetContainer.items.find(item => item.node_ip === itemData.node_ip)
                if (!isExit) {
                    this.containers.forEach(container => {
                        container.items = container.items.filter(item => item.node_ip !== itemData.node_ip)
                    })
                    // 添加item到目标容器
                    this.containers.forEach(container => {
                        if (container.id === targetContainer.id) {
                            container.items.unshift(itemData)
                        }
                    })
                }
            }
            console.log('this.containers-----------', this.containers)
        },
        add() {
            this.containers.push({
                id: this.containers.length + 1,
                isApp: false,
                items: []
            })
        },
        // 删除item
        deleteItem(containerIndex, index) {
            this.containers[containerIndex].items.splice(index, 1)
            console.log('deleteItem-----------', containerIndex, index, this.containers)
        },
        // 弹框--------------------------------------------------------------------------------------------------------
        handleShowModal() {
            // 状态重置
            this.formInline = {
                module_name: '',
                suite_code: '',
                team_name: '',
                department_name: ''
            }
            this.tableData = []
            this.showModal = true
            get_java_app({ had_batch_publish: true }).then(res => {
                if (res.data.status === 'success') {
                    this.app_name_list = res.data.data
                }
            })
            get_active_suite({ region_group: 'prod' }).then(res => {
                if (res.data.status === 'success') {
                    this.call_chain_list = res.data.data
                }
            })
            get_team_name_list({ team_level: 1 }).then(res => {
                if (res.data.status === 'success') {
                    this.team_list = res.data.data
                }
            })
            get_team_name_list({ team_level: 2 }).then(res => {
                if (res.data.status === 'success') {
                    this.department_list = res.data.data
                }
            })
            this.handleSearch()
        },
        handleSearch() {
            console.log('handleSearch-----------')
            batch_publish_app_list({
                page_num: this.page_num,
                page_size: this.page_size,
                ...this.formInline
            }).then(res => {
                if (res.data.status === 'success') {
                    this.tableData = res.data.data.app_list
                    this.total = res.data.data.page.total
                    // 设置默认选中（比如选中第一条数据）
                    this.setDefaultSelection()
                }
            })
        },
        // 设置默认选中的数据
        setDefaultSelection() {
            if (this.tableData.length > 0) {
                // _checked
                this.tableData.forEach(item => {
                    // 如果服务器没有返回 status 字段，则默认为 0（未选中）
                    if (item.status === undefined || item.status === null) {
                        item.status = 0
                    }
                    // 根据 status 设置选中状态
                    item._checked = item.status === 1
                })
            }
        },
        // 处理选择变化
        handleSelectionChange(selection) {
            this.selectedData = selection
            console.log('当前选中的数据:', selection)

            // 遍历tableData，检查每一项是否在selectedData中
            this.tableData.forEach(item => {
                // 检查当前项是否在selectedData中
                const isSelected = this.selectedData.some(
                    selectedItem =>
                        selectedItem.module_name === item.module_name && selectedItem.suite_code === item.suite_code
                )

                if (isSelected) {
                    item._checked = true
                    item.status = 1
                } else {
                    item._checked = false
                    item.status = 0
                }
            })

            save_restart_app({
                app_list: this.tableData
            }).then(res => {
                if (res.data.status === 'success') {
                    this.$Message.success('保存成功')
                } else {
                    this.$Message.error('保存失败')
                }
            })
        },
        ok() {
            // this.allSelectedData = this.tableData.filter(item => item._checked)
            this.loading = true
            get_reboot_app_list({})
                .then(res => {
                    if (res.data.status === 'success') {
                        this.allSelectedData = res.data.data
                        // 处理选中的数据，比如添加到编排中
                        if (this.allSelectedData.length > 0) {
                            // 将选中的数据添加到应用列表中
                            this.appList = this.allSelectedData
                            this.showModal = false
                            this.$Message.success(`已添加 ${this.allSelectedData.length} 个应用`)
                        } else {
                            this.$Message.warning('请选择要添加的应用')
                        }
                    }
                })
                .finally(() => {
                    this.loading = false
                })
        },
        cancel() {
            console.log('cancel-----------')
            this.showModal = false
            this.tableData = []
            this.total = 0
        },
        changePage(page) {
            this.page_num = page
            this.handleSearch()
        },
        getRebootFlow() {
            get_reboot_flow({ id: this.id }).then(res => {
                if (res.data.status === 'success') {
                    for (const key in res.data.data.flow_json) {
                        this.containers.push({
                            id: key.split('_')[1],
                            isApp: false,
                            items: res.data.data.flow_json[key]
                        })
                    }
                }
            })
        }
    },
    mounted() {
        this.id = this.$route.query.id
        if (this.id) {
            // 获取应用数据
            this.ok()
            // 获取编排数据
            this.getRebootFlow()
        }
    }
}
</script>

<style lang="less" scoped>
.service-publish-card {
    background-color: #fff;
    margin-bottom: 20px;
    border: 1px solid #ddd;
    display: block;
    .card-body {
        // padding: 10px;
        background-color: #fff;
        // margin-bottom: 20px;
        margin-bottom: 10px;

        .service-app-list {
            display: flex;
            padding: 10px;
            overflow: auto;
            min-height: 65px;
            margin-bottom: 10px;
            margin-top: 10px;
            border: 1px dashed #fff;
        }

        .dragged {
            border: 1px dashed red;
        }
    }
}
.main-container {
    display: flex;
    border: 1px solid #00000014;
    border-radius: 4px;
    padding: 10px;
    overflow: auto;
    min-height: 300px;
}
.container-box {
    text-align: center;
}
.container {
    border: 1px solid #ccc;
    margin: 5px;
    padding: 10px;
    min-width: 200px;
    height: 250px;
    overflow-y: auto;
}
.item {
    border: 1px solid #00000008;
    background-color: #ddd;
    border-radius: 4px;
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.1);
    padding: 5px;
    margin: 5px;
    cursor: grab;
    min-width: 140px;
}
.item_box {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.save_btn {
    display: flex;
    justify-content: flex-end;
    margin-right: 10px;
}
.btn_item {
    margin-right: 10px;
}
.header_box {
    display: flex;
    margin: 10px 0;
    align-items: center;
}
</style>
