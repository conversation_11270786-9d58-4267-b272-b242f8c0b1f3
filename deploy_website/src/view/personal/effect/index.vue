<!--
 * @Author: chao.bao <EMAIL>
 * @Date: 2024-08-23 14:29:55
 * @LastEditors: chao.bao <EMAIL>
 * @LastEditTime: 2025-02-14 11:13:28
 * @FilePath: /website_web/deploy_website/src/view/personal/effect/index.vue
 * @Description: 我的研效
-->
<template>
    <div class="warapper">
        <Tabs :value="currentTab" @on-click="changeTab">
            <TabPane label="考核季" name="name1">
                <Comprehensive ref="compreRef" />
            </TabPane>
            <TabPane class="scroll_tab" label="我的产出" name="name2">
                <Capacity ref="capacityRef" />
            </TabPane>
            <TabPane class="scroll_tab" label="我的质量" name="name3">
                <Quality ref="qualityRef" />
            </TabPane>
        </Tabs>
    </div>
</template>

<script>
import Comprehensive from './components/Comprehensive.vue'
import Capacity from './components/Capacity.vue'
import Quality from './components/Quality.vue'
export default {
    name: 'effect',
    components: { Comprehensive, Capacity, Quality },
    data() {
        return {
            currentTab: 'name2'
        }
    },
    methods: {
        changeTab(tab_name) {
            if (tab_name === 'name1') {
                this.$refs.compreRef.init()
            } else if (tab_name === 'name2') {
                this.$refs.capacityRef.init()
            } else if (tab_name === 'name3') {
                this.$refs.qualityRef.init()
            }
        }
    }
}
</script>

<style lang="less" scoped>
.warapper {
    position: relative;
    height: 100%;
    /deep/ .scroll_tab {
        .ivu-card-body {
            width: 100%;
        }
    }
}
</style>
