/*
 * @Author: chao.bao <EMAIL>
 * @Date: 2024-11-01 13:53:35
 * @LastEditors: chao.bao <EMAIL>
 * @LastEditTime: 2025-01-03 17:25:43
 * @FilePath: /website_web/deploy_website/src/view/personal/effect/scripts/enum.js
 * @Description: 枚举
 */
export const ENUM_TYPE_NAME = {
    dev_total_time: '填写总时长（小时）',
    test_total_time: '填写总时长（小时）',
    dev_delivery_time: '填写交付工时时长（小时）',
    test_delivery_time: '填写交付工时时长（小时）',
    code_line: '提交代码有效影响行数（行）',
    bug_resolve_time: '缺陷修复平均耗时（分钟）',
    auto_test_case_num: '自动化案例新增数（个）',
    bug_resolve_num: '产线缺陷数量',
    bug_kloc: '千行代码bug数（个）'
}
export const enumCapacity = [
    'cost_time',
    'total_time',
    'test_time',
    'code_line',
    'bug_resolve_time',
    'auto_test_case_num',
    'other'
]
export const enumQuality = ['bug_resolve_num', 'bug_kloc']
