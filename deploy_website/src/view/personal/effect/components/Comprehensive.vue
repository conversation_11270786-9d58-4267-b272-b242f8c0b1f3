<!--
 * @Author: chao.bao <EMAIL>
 * @Date: 2024-12-25 09:41:41
 * @LastEditors: chao.bao <EMAIL>
 * @LastEditTime: 2025-02-14 15:21:14
 * @FilePath: /website_web/deploy_website/src/view/personal/effect/components/Comprehensive.vue
 * @Description: 综合指标
-->
<template>
    <Card style="display: flex;">
        <div class="table_contain">
            <div class="custom_table">
                <div class="table_column">
                    <div
                        v-show="productHeader.length > 0 || qualityHeader.length > 0"
                        class="cell bold table_column_header"
                    ></div>
                    <div
                        v-show="productHeader.length > 0"
                        :style="{ height: 60 * productHeader.length + 'px' }"
                        class="cell bold"
                    >
                        产出
                    </div>
                    <div
                        v-show="qualityHeader.length > 0"
                        :style="{ height: 60 * qualityHeader.length + 'px' }"
                        class="cell bold"
                    >
                        质量
                    </div>
                </div>
                <div class="table_column">
                    <div
                        v-show="productHeader.length > 0 || qualityHeader.length > 0"
                        class="cell bold table_column_header"
                    ></div>
                    <!-- 产出 -->
                    <div v-for="item in productHeader" :key="item.key" class="cell bold table_comumn_left_header">
                        {{ item.text }}
                    </div>
                    <!-- 质量 -->
                    <div v-for="item in qualityHeader" :key="item.key" class="cell bold table_comumn_left_header">
                        {{ item.text }}
                    </div>
                </div>
                <div v-for="item in list" :key="item.title" class="table_column">
                    <div class="cell bold table_column_header">{{ item.title }}</div>
                    <div v-for="product in productHeader" :key="product.key" class="cell">{{ item[product.key] }}</div>
                    <div v-for="quality in qualityHeader" :key="quality.key" class="cell">{{ item[quality.key] }}</div>
                </div>
            </div>
            <Spin v-if="loadding" fix></Spin>
        </div>
    </Card>
</template>
<script>
import { getComprehensive } from '@/spider-api/biz-mgt'
export default {
    data() {
        return {
            loadding: false,
            productHeader: [],
            qualityHeader: [],
            list: []
        }
    },
    methods: {
        init() {
            if (this.list.length > 0 && this.productHeader.length > 0 && this.qualityHeader.length > 0) {
                return
            }
            // 调用接口查询数据
            this.loadding = true
            getComprehensive({
                business_name: 'get_pers_capa_for_check',
                params: {
                    opt_user: this.$store.state.user.userName,
                    time_cycle: ''
                }
            })
                .then(res => {
                    if (res.data.status === 'success') {
                        this.list = res.data.data.list || []
                        this.productHeader = res.data.data.outputHeader || []
                        this.qualityHeader = res.data.data.qualityHeader || []
                    } else {
                        this.list = []
                        this.productHeader = []
                        this.qualityHeader = []
                    }
                })
                .finally(() => {
                    this.loadding = false
                })
            // this.list = [
            //     {
            //         time_cycle: 'this_quarter',
            //         title: '当季截至11月【10.01~11.30】',
            //         total_time: 45,
            //         cost_time: 23,
            //         code_line: 4716,
            //         bug_resolve_time: 375,
            //         bug_resolve_num: 4,
            //         bug_kloc: 0.1
            //     },
            //     {
            //         time_cycle: 'this_year',
            //         title: '当年截至Q3月【01.01~09.30】',
            //         total_time: 451,
            //         cost_time: 231,
            //         code_line: 47161,
            //         bug_resolve_time: 3751,
            //         bug_resolve_num: 41,
            //         bug_kloc: 0.11
            //     }
            // ]
            // this.productHeader = [
            //     {
            //         text: '月均提交代码有效影响行数（行）',
            //         key: 'total_time'
            //     },
            //     {
            //         text: '月均填写总时长（小时）',
            //         key: 'cost_time'
            //     },
            //     {
            //         text: '月均自动化案例新增数（个）',
            //         key: 'bug_resolve_num'
            //     }
            // ]
            // this.qualityHeader = [
            //     {
            //         text: '月均填写交付工时时长（小时）',
            //         key: 'code_line'
            //     },
            //     {
            //         text: '千行代码bug数（个）',
            //         key: 'bug_resolve_time'
            //     }
            // ]
        }
    }
}
</script>

<style lang="less" scoped>
.table_contain {
    min-height: 400px;
    .custom_table {
        display: inline-block;
        border-left: 1px solid #9fa9b4;
        border-top: 1px solid #9fa9b4;
        display: flex;
        .cell {
            border-right: 1px solid #9fa9b4;
            border-bottom: 1px solid #9fa9b4;
            height: 60px;
            // line-height: 30px;
            width: 120px;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .bold {
            font-weight: bold;
        }
        .green {
            background-color: #c3ead5;
        }
    }
}
</style>
