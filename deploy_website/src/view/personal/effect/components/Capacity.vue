<!--
 * @Author: chao.bao <EMAIL>
 * @Date: 2024-09-10 10:08:14
 * @LastEditors: chao.bao <EMAIL>
 * @LastEditTime: 2025-02-18 19:11:29
 * @FilePath: /website_web/deploy_website/src/view/personal/effect/components/Capacity.vue
 * @Description: 我的产出
-->
<template>
    <Card style="display: flex;">
        <div class="table_contain">
            <div class="custom_table">
                <div class="table_column">
                    <div v-show="productHeader.length > 0" class="cell bold table_column_header cell120"></div>
                    <div
                        v-show="productHeader.length > 0"
                        :style="{ height: 60 * productHeader.length + 'px' }"
                        class="cell bold cell120"
                    >
                        产出（个人/团队中值/团队均值）
                    </div>
                </div>
                <div class="table_column">
                    <div v-show="productHeader.length > 0" class="cell bold table_column_header cell120"></div>
                    <!-- 产出 -->
                    <div
                        v-for="item in productHeader"
                        :key="item.key"
                        class="cell bold table_comumn_left_header cell120"
                    >
                        {{ item.text }}
                    </div>
                </div>
                <div v-for="item in list" :key="item.title" class="table_column">
                    <div class="cell bold table_column_header">{{ item.title }}</div>
                    <div v-for="product in productHeader" :key="product.key" class="cell">{{ item[product.key] }}</div>
                </div>
            </div>
            <Spin v-if="loadding" fix></Spin>
        </div>
    </Card>
</template>
<script>
import { getComprehensive } from '@/spider-api/biz-mgt'
export default {
    data() {
        return {
            loadding: false,
            productHeader: [],
            list: []
        }
    },
    methods: {
        init() {
            if (this.list.length > 0 && this.productHeader.length > 0) {
                return
            }
            // 调用接口查询数据
            this.loadding = true
            getComprehensive({
                business_name: 'get_pers_capa_for_output',
                params: {
                    opt_user: this.$store.state.user.userName,
                    time_cycle: ''
                }
            })
                .then(res => {
                    if (res.data.status === 'success') {
                        this.list = res.data.data.list || []
                        this.productHeader = res.data.data.outputHeader || []
                    } else {
                        this.list = []
                        this.productHeader = []
                    }
                })
                .finally(() => {
                    this.loadding = false
                })
        }
    },
    mounted() {
        this.init()
    }
}
</script>

<style lang="less" scoped>
.table_contain {
    position: relative;
    min-height: 400px;
    width: 100%;
    overflow: auto;
    .custom_table {
        width: auto;
        display: inline-block;
        border-left: 1px solid #9fa9b4;
        // border-top: 1px solid #9fa9b4;
        display: flex;
        .table_column_header {
            border-top: 1px solid #9fa9b4;
        }
        .cell {
            border-right: 1px solid #9fa9b4;
            border-bottom: 1px solid #9fa9b4;
            height: 60px;
            // width: 120px;
            width: 180px;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .cell120 {
            width: 120px;
        }
        .bold {
            font-weight: bold;
        }
        .green {
            background-color: #c3ead5;
        }
    }
}
</style>
