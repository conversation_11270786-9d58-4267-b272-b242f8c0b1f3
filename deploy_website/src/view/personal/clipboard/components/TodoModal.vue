<!--
 * @Author: chao.bao <EMAIL>
 * @Date: 2024-10-31 14:32:16
 * @LastEditors: chao.bao <EMAIL>
 * @LastEditTime: 2024-12-02 15:08:38
 * @FilePath: /website_web/deploy_website/src/view/personal/clipboard/components/TodoModal.vue
 * @Description: 待办modal
-->
<template>
    <Modal v-model="showModal" title="产线回滚原因分析" ok-text="保存" @on-ok="ok" @on-cancel="cancel">
        <div class="item_contain">
            <div class="item_key">迭代：</div>
            <div class="item_val">{{ formData.iteration_id }}</div>
        </div>
        <div class="item_contain">
            <div class="item_key">应用名：</div>
            <div class="item_val">{{ formData.module_name }}</div>
        </div>
        <div class="item_contain">
            <div class="item_key">操作：</div>
            <div class="item_val">{{ todoType }}</div>
        </div>
        <div class="item_contain">
            <div class="item_key">详情：</div>
            <div class="item_val">{{ formData.rollback_detail }}</div>
        </div>
        <div class="item_contain">
            <div class="item_key">创建时间：</div>
            <div class="item_val">{{ formData.create_time }}</div>
        </div>
        <div class="item_contain">
            <div class="item_key">现象：</div>
            <div class="item_val">
                <Input v-model="formData.opt_reason" type="textarea" placeholder="请输入文本"></Input>
            </div>
        </div>
        <div class="item_contain">
            <div class="item_key">根本原因：</div>
            <div class="item_val">
                <Input v-model="formData.opt_reason_1" type="textarea" placeholder="请输入文本"></Input>
            </div>
        </div>
        <div class="item_contain">
            <div class="item_key">漏测原因：</div>
            <div class="item_val">
                <Input v-model="formData.opt_reason_2" type="textarea" placeholder="请输入文本"></Input>
            </div>
        </div>
        <div class="item_contain">
            <div class="item_key">解决方式：</div>
            <div class="item_val">
                <Input v-model="formData.opt_reason_3" type="textarea" placeholder="请输入文本"></Input>
            </div>
        </div>
    </Modal>
</template>

<script>
import { submitTodo } from '@/spider-api/biz-mgt'
import { enumType } from '../scripts/enum'
export default {
    name: 'TodoModal',
    model: {
        prop: 'modelValue',
        event: 'update:modelValue'
    },
    props: {
        pid: {
            type: Number,
            Required: true
        },
        modelValue: {
            type: Boolean,
            default: false
        },
        detailData: {
            // 当前待办数据
            type: Object,
            default: () => ({})
        }
    },
    data() {
        return {
            showModal: false,
            formData: {}
        }
    },
    computed: {
        // 操作类型
        todoType() {
            return enumType[this.formData.opt_type]
        }
    },
    watch: {
        modelValue(val) {
            this.showModal = val
        },
        detailData(val) {
            this.formData = val
        }
    },
    methods: {
        ok() {
            submitTodo({ ...this.formData, pid: this.pid }).then(res => {
                if (res.data.status === 'success') {
                    this.$Message.success(res.data.msg)
                    this.$emit('update:modelValue', false)
                    this.$emit('refresh')
                } else {
                    this.$Message.error(res.msg)
                }
            })
        },
        cancel() {
            this.$emit('update:modelValue', false)
        }
    }
}
</script>

<style lang="less" scoped>
.item_contain {
    display: flex;
    align-items: center;
    margin-top: 10px;
    .item_key {
        width: 80px;
        text-align: right;
    }
    .item_val {
        flex: 1;
        white-space: normal;
        word-break: break-all;
        overflow: hidden;
    }
}
</style>
