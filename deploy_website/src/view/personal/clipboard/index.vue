<!--
 * @Author: chao.bao <EMAIL>
 * @Date: 2024-10-31 10:28:21
 * @LastEditors: chao.bao <EMAIL>
 * @LastEditTime: 2024-11-01 11:01:54
 * @FilePath: /website_web/deploy_website/src/view/personal/clipboard/index.vue
 * @Description: 我的待办
-->
<template>
    <div>
        <!-- 列表数据 -->
        <Table stripe :columns="columns" :data="tableData"></Table>
        <!-- 分页起 -->
        <div class="page_wrapper">
            <Page
                :current.sync="queryData.page"
                :total="queryData.total"
                :page-size="queryData.page_size"
                show-sizer
                @on-change="handleChange"
                @on-page-size-change="handlePageSizeChange"
            ></Page>
        </div>
        <TodoModal v-model="showModal" :detailData="detailData" :pid="pid" @refresh="queryList" />
    </div>
</template>

<script>
import TodoModal from './components/TodoModal.vue'
import { getTodoList, getTodoDetail } from '@/spider-api/biz-mgt'
import { enumStatus, enumType } from './scripts/enum'
export default {
    name: 'clipboard',
    components: { TodoModal },
    data() {
        return {
            queryData: {
                page: 1,
                page_size: 10,
                total: 0
            },
            tableData: [{}],
            columns: [
                { type: 'index', title: '序号', width: 100 },
                { title: '事项标题', key: 'to_do_title' },
                {
                    title: '状态',
                    key: 'to_do_status',
                    render: (h, params) => {
                        return h('div', enumStatus[params.row.to_do_status])
                    }
                },
                { title: '创建时间', key: 'create_time' },
                {
                    title: '事件类型',
                    key: 'to_do_type',
                    render: (h, params) => {
                        return h('div', enumType[params.row.to_do_type])
                    }
                },
                {
                    title: '操作',
                    key: 'action',
                    fixed: 'right',
                    width: 120,
                    render: (h, params) => {
                        return h('div', [
                            h(
                                'Button',
                                {
                                    props: {
                                        type: 'primary',
                                        size: 'small'
                                    },
                                    on: {
                                        click: () => {
                                            this.getDetail(params.row.extend_id)
                                        }
                                    }
                                },
                                '处理'
                            )
                        ])
                    }
                }
            ],
            showModal: false,
            detailData: {},
            pid: null
        }
    },
    methods: {
        getDetail(pid) {
            this.pid = pid
            this.showModal = true
            getTodoDetail({ pid }).then(res => {
                if (res.data.status === 'success') {
                    this.detailData = res.data.data
                }
            })
        },
        // 列表查询
        queryList() {
            getTodoList({ page_size: this.queryData.page_size, page_number: this.queryData.page }).then(res => {
                if (res.data.status === 'success') {
                    this.tableData = res.data.data.todo_list
                    this.queryData.total = res.data.data.count
                }
            })
        },
        // 切换页码
        handleChange(page) {
            this.queryData.page = page
            // 调用列表查询
            this.queryList()
        },
        // 切换每页条数
        handlePageSizeChange(pageSize) {
            // 会触发handleChange
            this.queryData.page_size = pageSize
        }
    },
    mounted() {
        this.queryList()
    }
}
</script>

<style lang="less" scoped>
.page_wrapper {
    margin-top: 20px;
    display: flex;
    align-items: center;
    justify-content: right;
}
</style>
