<!--
 * @Author: chao.bao <EMAIL>
 * @Date: 2024-08-23 15:56:14
 * @LastEditors: chao.bao <EMAIL>
 * @LastEditTime: 2024-11-08 10:58:49
 * @FilePath: /website_web/deploy_website/src/view/personal/quality_detail/index.vue
 * @Description: 质量报告邮件模版
-->
<template>
    <div class="contain">
        <div class="warpper">
            <!--自定义table -->
            <div class="custom_table">
                <div class="table_header">
                    <div v-for="item in columns" :key="item.key" class="table_header_key cell_width">
                        {{ item.label }}
                    </div>
                </div>
                <div class="table_body">
                    <div v-for="item in tableData" :key="item.id" class="table_body_row">
                        <!-- 编排线名称 -->
                        <div class="row_id flex_center cell_width">{{ item.biz_flow_name }}</div>
                        <!-- 执行批次 -->
                        <div class="row_id flex_center cell_width">{{ item.run_batch_no }}</div>
                        <!-- 运行环境 -->
                        <div class="row_id flex_center cell_width">{{ item.suite_code }}</div>
                        <div class="row_item">
                            <div v-for="(child, childIndex) in item.run_result" :key="childIndex" class="row_item_app">
                                <!-- 执行顺序-测试集 -->
                                <div class="row_item_app_name flex_center cell_width">{{ child.testset_id }}</div>
                                <div class="row_item_app_wrapper">
                                    <div
                                        v-for="(app, appIndex) in child.appList"
                                        :key="appIndex"
                                        class="row_item_app_item"
                                    >
                                        <!-- 应用 -->
                                        <div class="flex_center cell_width">{{ app.app_name }}</div>
                                        <!-- 部署版本 -->
                                        <div class="flex_center cell_width">{{ app.deploy_branch }}</div>
                                        <!-- 接口版本 -->
                                        <div class="flex_center cell_width">{{ app.srcipt_branch }}</div>
                                        <!-- 接口通过率 -->
                                        <div
                                            class="flex_center cell_width"
                                            :class="{ red_font: app.interface_pass_rate !== '100.00%' }"
                                        >
                                            {{ app.interface_pass_rate }}
                                        </div>
                                        <!-- 用例通过率 -->
                                        <div
                                            class="flex_center cell_width"
                                            :class="{ red_font: app.case_pass_rate !== '100.00%' }"
                                        >
                                            {{ app.case_pass_rate }}
                                        </div>
                                        <!-- 开始时间 -->
                                        <div class="flex_center cell_width">{{ app.start_time }}</div>
                                        <!-- 结束时间 -->
                                        <div class="flex_center cell_width">{{ app.end_time }}</div>
                                        <!-- 运行时长 -->
                                        <div class="flex_center cell_width">{{ app.duration }}</div>
                                        <!-- 报告地址 -->
                                        <a class="flex_center cell_width" :href="app.result_url" target="_blank"
                                            >测试集结果</a
                                        >
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import { getEmailDetailList } from './api'
export default {
    data() {
        return {
            columns: [
                {
                    label: '编排线名称'
                },
                {
                    label: '执行批次'
                },
                {
                    label: '运行环境'
                },
                {
                    label: '执行顺序-测试集'
                },
                {
                    label: '应用'
                },
                {
                    label: '部署版本'
                },
                {
                    label: '接口版本'
                },
                {
                    label: '接口通过率'
                },
                {
                    label: '用例通过率'
                },
                {
                    label: '开始时间'
                },
                {
                    label: '结束时间'
                },
                {
                    label: '运行时长'
                },
                {
                    label: '结果地址'
                }
            ],
            tableData: [
                // {
                //     run_result: [
                //         {
                //             appList: [{}]
                //         }
                //     ]
                // }
            ]
        }
    },
    computed: {
        biz_flow_name() {
            return this.$route.query.biz_flow_name
        }
    },
    mounted() {
        getEmailDetailList({ business_name: 'get_every_auto_test_result' }).then(res => {
            const obj = res.data.data
            for (let key of Object.keys(obj)) {
                obj[key].run_record.map(item => {
                    this.tableData.push({ ...item, biz_flow_name: key })
                })
            }
        })
    }
}
</script>
<style lang="less" scoped>
.contain {
    height: 100%;
    overflow: auto;
}
.warpper {
    padding: 20px;
    min-width: 1400px;
    .title {
        font-weight: bold;
        font-size: 20px;
        margin-bottom: 20px;
    }
    .flex_center {
        // height: 100px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .cell_width {
        width: 130px;
        white-space: normal;
        word-break: break-all;
        overflow: hidden;
        text-align: center;
        padding: 0 5px;
    }
    .red_font {
        color: red;
        font-weight: bold;
    }
    .custom_table {
        display: inline-block;
        width: auto;
        border-left: 1px solid #9fa9b4;
        border-top: 1px solid #9fa9b4;
        .table_header {
            display: flex;
            align-items: center;
            background-color: #cfcfe3;
            height: 40px;
            .table_header_key {
                border-right: 1px solid #9fa9b4;
                border-bottom: 1px solid #9fa9b4;
                line-height: 40px;
                text-align: center;
                color: #515a6e;
                font-weight: 700;
            }
        }
        .table_body {
            .table_body_row {
                background: #eef5f2 !important;
                display: flex;
                .row_id {
                    border-right: 1px solid #9fa9b4;
                    border-bottom: 1px solid #9fa9b4;
                }
                .row_item {
                    .row_item_app {
                        display: flex;
                        .row_item_app_name {
                            border-right: 1px solid #9fa9b4;
                            border-bottom: 1px solid #9fa9b4;
                            text-align: center;
                        }
                        .row_item_app_wrapper {
                            display: flex;
                            flex-direction: column;
                            .row_item_app_item {
                                display: flex;
                                height: 100%;
                                .cell_width {
                                    text-align: center;
                                    min-height: 40px;
                                    border-right: 1px solid #9fa9b4;
                                    border-bottom: 1px solid #9fa9b4;
                                }
                            }
                        }
                    }
                }
            }
        }
        .table_body > .table_body_row:nth-child(even) {
            background: #f1ecee !important;
        }
    }
}
</style>
