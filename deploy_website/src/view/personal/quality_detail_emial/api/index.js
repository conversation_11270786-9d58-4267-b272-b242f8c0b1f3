/*
 * @Author: chao.bao <EMAIL>
 * @Date: 2024-11-04 16:17:14
 * @LastEditors: chao.bao <EMAIL>
 * @LastEditTime: 2024-11-05 13:29:43
 * @FilePath: /website_web/deploy_website/src/view/personal/quality_detail_emial/api/index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import spider_axios from '@/libs/spider_api.request'

export const getEmailDetailList = params => {
    return spider_axios.request({
        url: 'spider/external_interaction/mantis_mgt/mantis_request_api/get_no_auth_request/',
        data: params,
        method: 'post'
    })
}
