<!--
 * @Author: chao.bao <EMAIL>
 * @Date: 2024-11-04 10:52:07
 * @LastEditors: chao.bao <EMAIL>
 * @LastEditTime: 2024-11-06 13:20:56
 * @FilePath: /website_web/deploy_website/src/view/personal/quality/components/Report.vue
 * @Description: 迭代报告组件
-->
<template>
    <div class="reporter_warpper">
        <Card class="card_custom card_table">
            <Select
                v-model="searchData.params.time_cycle"
                style="width:200px; margin-bottom: 10px;"
                @on-change="changeHanlder"
            >
                <Option v-for="item in optionList" :value="item.value" :key="item.value">{{ item.label }}</Option>
            </Select>
            <Table border :columns="columns" :data="tableData" height="280" @on-row-click="rowClick"></Table>
            <div class="page_wrapper">
                <Page
                    :current.sync="searchData.params.page_num"
                    :total="searchData.params.total"
                    :page-size="searchData.params.page_size"
                    show-sizer
                    @on-change="handleChange"
                    @on-page-size-change="handlePageSizeChange"
                ></Page>
            </div>
        </Card>
        <Card class="card_custom card_echarts">
            <div class="echart_contain" ref="lineRef"></div>
        </Card>
    </div>
</template>

<script>
import { getPersonDetailList, getLineEchartsData } from '../api'
import { deepClone } from '@/utils'
export default {
    data() {
        return {
            searchData: {
                business_name: 'get_person_iteration_dashboard',
                params: {
                    opt_user: this.$store.state.user.userName,
                    time_cycle: 'last_week',
                    page_num: 1,
                    page_size: 10,
                    total: 0
                }
            },
            optionList: [
                {
                    label: '近一周',
                    value: 'last_week'
                },
                {
                    label: '近一月',
                    value: 'last_month'
                },
                {
                    label: '近一季',
                    value: 'last_quarter'
                },
                {
                    label: '近一年',
                    value: 'last_year'
                }
            ],
            columns: [
                {
                    title: '应用名',
                    key: 'app_name'
                },
                {
                    title: '分支',
                    key: 'branch_name'
                },
                {
                    title: '最新批次号',
                    key: 'run_batch_number'
                },
                {
                    title: '测试集id',
                    key: 'testset_id'
                },
                {
                    title: '案例版本号',
                    key: 'script_branch'
                },
                {
                    title: '接口通过率',
                    key: 'interface_pass_rate'
                },
                {
                    title: '案例通过率',
                    key: 'case_pass_rate'
                }
            ],
            tableData: [],
            lineEcharts: null,
            lineOptions: {
                title: {
                    // text: 'Stacked Line'
                },
                tooltip: {
                    trigger: 'axis'
                },
                legend: {
                    // data: ['Email', 'Union Ads', 'Video Ads', 'Direct', 'Search Engine']
                    data: []
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    // data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
                    data: []
                },
                yAxis: {
                    type: 'value'
                },
                series: [
                    // {
                    //     name: 'Email',
                    //     type: 'line',
                    //     connectNulls: true,
                    //     data: [10, 101, 134, null, 110]
                    // },
                    // {
                    //     name: 'Union Ads',
                    //     type: 'line',
                    //     connectNulls: true,
                    //     data: [220, 191, 234, 290, 10]
                    // }
                ]
            }
        }
    },
    methods: {
        setLine({ app_name, branch_name }) {
            this.lineEcharts && this.lineEcharts.dispose()
            this.$nextTick(() => {
                this.lineEcharts = this.$echarts.init(this.$refs.lineRef)
                getLineEchartsData({
                    business_name: 'get_person_iteration_Linechart',
                    params: {
                        opt_user: this.$store.state.user.userName,
                        time_cycle: this.searchData.params.time_cycle,
                        app_name,
                        branch_name
                    }
                }).then(res => {
                    if (res.data.status === 'success') {
                        const obj = res.data.data
                        let xAxis = []
                        const yAxis = []
                        const legend = []
                        const options = deepClone(this.lineOptions)
                        for (let key of Object.keys(obj)) {
                            xAxis = obj[key].map(item => item.x_axis)
                            const yAxisArray = obj[key].map(item => item.y_axis)
                            yAxis.push({
                                name: key,
                                type: 'line',
                                connectNulls: true,
                                data: yAxisArray
                            })
                            legend.push(key)
                        }
                        options.legend.data = legend
                        options.xAxis.data = xAxis
                        options.series = yAxis
                        console.log('options------------------', options)

                        this.lineEcharts.setOption(options)
                    }
                })
            })
        },
        rowClick(curRow) {
            this.setLine(curRow)
        },
        queryList() {
            // 列表查询
            getPersonDetailList(this.searchData)
                .then(res => {
                    console.log('res', res)

                    if (res.data.status === 'success') {
                        this.tableData = res.data.data.dashboard_data_list
                        this.searchData.params.total = res.data.data.total
                        if (this.tableData.length > 0) {
                            this.setLine(this.tableData[0])
                        }
                    } else {
                        this.$Message.error(res.data.msg)
                    }
                })
                .catch(err => {
                    console.log('err', err)

                    this.$Message.error('请求失败')
                })
        },
        changeHanlder() {
            this.queryList()
        },
        // 切换页码
        handleChange(page) {
            this.searchData.params.page_num = page
            // 调用列表查询
            this.queryList()
        },
        // 切换每页条数
        handlePageSizeChange(pageSize) {
            // 会触发handleChange
            this.searchData.params.page_size = pageSize
        }
    },
    mounted() {
        this.queryList()
    },
    beforeDestroy() {
        this.lineEcharts && this.lineEcharts.dispose()
    }
}
</script>

<style lang="less" scoped>
.reporter_warpper {
    height: ~'calc(100vh - 210px)';
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    .card_custom {
        margin-bottom: 20px;
        .page_wrapper {
            margin-top: 20px;
            display: flex;
            align-items: center;
            justify-content: right;
        }
        .echart_contain {
            width: 1000px;
            // height: 260px;
            height: 460px;
            margin: 0 auto;
        }
    }
    .card_table {
        // flex: 0 0 200px;
    }
    .card_echarts {
        // flex: 1 0 300px;
        flex: 1 0 500px;
    }
}
</style>
