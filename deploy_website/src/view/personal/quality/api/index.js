/*
 * @Author: chao.bao <EMAIL>
 * @Date: 2024-11-04 15:03:18
 * @LastEditors: chao.bao <EMAIL>
 * @LastEditTime: 2024-11-05 18:49:16
 * @FilePath: /website_web/deploy_website/src/view/personal/quality/api/index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import spider_axios from '@/libs/spider_api.request'

export const getPersonDetailList = param => {
    return spider_axios.request({
        url: 'spider/external_interaction/mantis_mgt/mantis_request_api/get_mantis_request/',
        data: param,
        method: 'post'
    })
}
export const getLineEchartsData = param => {
    return spider_axios.request({
        url: 'spider/external_interaction/mantis_mgt/mantis_request_api/get_mantis_request/',
        data: param,
        method: 'post'
    })
}
