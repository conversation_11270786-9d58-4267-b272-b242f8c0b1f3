<!--
 * @Author: chao.bao <EMAIL>
 * @Date: 2024-08-23 14:29:55
 * @LastEditors: chao.bao <EMAIL>
 * @LastEditTime: 2024-11-04 11:15:13
 * @FilePath: /website_web/deploy_website/src/view/personal/effect/index.vue
 * @Description: 我的质量报告
-->
<template>
    <div class="warapper">
        <Tabs value="name1">
            <TabPane label="昨天到现在" name="name1">
                <Table border :columns="columns" :data="tableData">
                    <template slot-scope="{ row, index }" slot="biz_flow_name">
                        <a @click="goDetail(row.biz_flow_name)">{{ row.biz_flow_name }}</a>
                    </template>
                </Table>
                <div class="middle_line"></div>
                <Page
                    :total="pageInfo.total"
                    :current="pageInfo.page_num"
                    :page-size="pageInfo.page_size"
                    @on-change="changePage"
                />
            </TabPane>
            <TabPane label="我的迭代报告" name="name2">
                <Report />
            </TabPane>
        </Tabs>
    </div>
</template>

<script>
import { getPersonList } from '@/spider-api/biz-mgt'
import Report from './components/Report'
export default {
    components: { Report },
    data() {
        return {
            pageInfo: {
                total: 0,
                page_num: 1,
                page_size: 10
            },
            tableData: [],
            columns: [
                {
                    title: '编排线名称',
                    key: 'biz_flow_name',
                    slot: 'biz_flow_name',
                    align: 'center'
                },
                {
                    title: '执行批次',
                    key: 'run_batch_number'
                },
                {
                    title: '环境',
                    key: 'suite_code'
                },
                {
                    title: '执行时间',
                    key: 'run_time'
                }
                // {
                //   title: '测试集',
                //   key: 'testset_id'
                // },
                // {
                //   title: '应用',
                //   key: 'app_name'
                // },
                // {
                //   title: '部署版本',
                //   key: 'app_deploy_branch'
                // },
                // {
                //   title: '接口版本',
                //   key: 'script_branch'
                // },
                // {
                //   title: '接口通过率',
                //   key: 'interface_pass_rate'
                // },
                // {
                //   title: '开始时间',
                //   key: 'start_time'
                // }
            ]
        }
    },
    methods: {
        goDetail(biz_flow_name) {
            let routeUrl = this.$router.resolve({
                path: '/quality_detail',
                query: {
                    biz_flow_name
                }
            })
            window.open(routeUrl.href, '_blank')
        },
        getList() {
            getPersonList({
                business_name: 'get_person_quality_dashboard',
                params: {
                    opt_user: this.$store.state.user.userName,
                    page_num: this.pageInfo.page_num,
                    page_size: this.pageInfo.page_size
                }
            }).then(res => {
                console.log('res-------', res)
                this.tableData = res.data.data.dashboard_data_list
                this.pageInfo.total = res.data.data.total
            })
        },
        changePage(num) {
            this.pageInfo.page_num = num
            this.tableData = []
            // 分页查询数据
            this.getList()
        }
    },
    mounted() {
        this.getList()
    }
}
</script>

<style lang="less" scoped>
.warapper {
    height: 100%;
    background-color: #fff;
    padding: 20px;
    .middle_line {
        height: 10px;
    }
}
</style>
