import {getCreateIterStatusApi } from "@/spider-api/create-iter";


export const getStatus = (sid) =>{
       getCreateIterStatusApi({"sid":sid}).then(res => {
                if (res.data.msg=="running"){
                  let vm = this
                  setTimeout(function(){vm.getStatus(sid)},2000)
                }
                else{
                   this.$Message.success(res.data["msg"]);
                   alert(res.data["msg"])
                   this.$Spin.hide();
                   this.btnDisabled = false;
                }
              })
                .catch(err => {
                  this.$Message.error(err.response.data.msg)
                  alert(err.response.data.msg)
                  this.$Spin.hide()
                  this.btnDisabled = false
                })
    }
