import HttpRequest from '@/libs/axios'
import config from '@/config'
var spider_baseUrl
if(process.env.NODE_ENV == 'development'){
  spider_baseUrl= config.baseUrl.spider_dev
}else{
  if(process.env.VUE_APP_ENV == 'test'){
    spider_baseUrl = config.baseUrl.spider_test
  }else if(process.env.VUE_APP_ENV == 'prod'){
    spider_baseUrl = config.baseUrl.spider_prod
  }else{
    spider_baseUrl = config.baseUrl.spider_test
  }
}

console.log(spider_baseUrl)
const spider_axios = new HttpRequest(spider_baseUrl)
export default spider_axios
