import HttpRequest from '@/libs/axios'
import config from '@/config'
var baseUrlApi
if(process.env.NODE_ENV == 'development'){
  baseUrlApi= config.baseUrl.dev
}else{
  if(process.env.VUE_APP_ENV == 'test'){
    baseUrlApi = config.baseUrl.test
  }else if(process.env.VUE_APP_ENV == 'prod'){
    baseUrlApi = config.baseUrl.prod
  }else{
    baseUrlApi = config.baseUrl.test
  }
}
console.log(baseUrlApi)
const axios = new HttpRequest(baseUrlApi)
export default axios
