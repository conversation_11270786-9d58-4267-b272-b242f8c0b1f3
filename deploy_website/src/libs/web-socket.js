import config from '@/config'
const { socket_host } = config

/*
obj: vue对象
params: ws链接参数： ？xx=xx
func: 如果有需要可以传入函数
*/

export const initWebSocket = (obj, params, func) => {
  let socket = new WebSocket('ws://' + socket_host + params)
  socket.onopen = function open() {
    console.log('WebSockets connection created.')
    obj.socket = socket
    if (typeof(func) != 'undefined') {
      func(obj)
    }
  }

  socket.onmessage = function message(event) {
    obj.wb_data = event.data
  }

  if (socket.readyState == WebSocket.OPEN) {
    socket.onopen()
  }
}

export const closeWebSocket = (obj) => {
  if (obj.socket != null) {
    obj.socket.onclose = function () {
      console.log("Disconnected to socks socket")
    }
    obj.socket.close()
    obj.wb_data = []
  }
}
