import spider_axios from '@/libs/spider_api.request'
import store from '../store'

//获取日志详情
export const getSaltLogInfo = (param) => {
  return spider_axios.request({
    params: param,
    url: 'spider/task_mgt/salt_log_api',
    method: 'get'
  })
};
//获取节点差异详情
export const getNodeConsistentInfo = (param) => {
  return spider_axios.request({
    data: param,
    url: 'spider/app_mgt/app_compare_api',
    method: 'post'
  })
};

