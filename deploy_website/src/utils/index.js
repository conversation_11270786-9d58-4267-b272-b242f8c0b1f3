/*
 * @Author: chao.bao <EMAIL>
 * @Date: 2024-09-12 19:56:56
 * @LastEditors: chao.bao <EMAIL>
 * @LastEditTime: 2024-09-12 20:01:26
 * @FilePath: /website_web/deploy_website/src/utils/index.js
 * @Description: 公共方法
 */
/**
 * 深拷贝
 */
export const deepClone = (data) => {
  if (!data || typeof data !== 'object') {
    return data
  }

  const newData = data instanceof Array ? [] : {}
  for (const key in data) {
    const tmpKey = typeof data[key] === 'object' ? deepClone(data[key]) : data[key]
    newData[key] = tmpKey
  }
  return newData
}
