<template>
  <div id="app">
    <router-view/>
  </div>
</template>

<script>
export default {
  name: 'App',
  data () {
    return {

    }
  },
  methods: {
    GlobalSocket () {
      let vm = this
      let socket_host = this.$store.state.socket_host + '/java_logs'
      let socket = ''
      let init_flag = true

      setInterval(() => {
        // if (! vm.$store.state.gSocket || socket.readyState === 3) {
        if (! socket || socket.readyState === 3) {
          init_flag = true
          socket = new WebSocket('ws://' + socket_host + '?stocks')
          // vm.$store.commit('setGSocket', socket)
        }
        if (init_flag && socket && socket.readyState === 1) {
          socket.onopen = function open() {
            // console.log('WebSockets connection created.')
          }
          socket.onmessage = function message(event) {
            if (vm.$store.state.user.token) {
              vm.$Notice.info({
                title: 'Notification title',
                desc: event.data,
                duration: 0
              });
            }
          }
          socket.onclose = function close() {
            // console.log('gSocket closed.')
          }
          socket.onerror = function error() {
            // console.log('gSocket error.')
          }
          init_flag = false
        }
      },2000)
    },
  },
  created () {
    // this.GlobalSocket()
  },
 //   mounted() {
 //   this.getConfigJson()
 // }
}
</script>

<style lang="less">
.size{
  width: 100%;
  height: 100%;
}
html,body{
  .size;
  overflow: hidden;
  margin: 0;
  padding: 0;
}
#app {
  .size;
}
</style>
