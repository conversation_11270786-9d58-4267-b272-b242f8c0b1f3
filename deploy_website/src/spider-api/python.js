/*
 * @Author: chao.bao <EMAIL>
 * @Date: 2025-08-14 17:40:16
 * @LastEditors: chao.bao <EMAIL>
 * @LastEditTime: 2025-08-29 16:22:37
 * @FilePath: /website_web/deploy_website/src/spider-api/python.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import spider_axios from '@/libs/spider_api.request'
/**
 * 编译python项目
 * @param req
 * @returns {*|AxiosPromise<any>|HttpOptions|void}
 */
export const PythonPipelineApi = req => {
    return spider_axios.request({
        url: 'spider/py_pipeline_mgt/python_pipeline_api/',
        method: 'post',
        data: req
    })
}
/**
 * 申请按钮
 * @param req
 * @returns {*|AxiosPromise<any>|HttpOptions|void}
 */
export const pyPublishApplyApi = req => {
    return spider_axios.request({
        // url: 'spider/py_pipeline_mgt/python_pipeline_api/',
        url: 'spider/py_pipeline_mgt/python_apply_api/',
        method: 'post',
        data: req
    })
}

export const getIterPublishAppInfoApi = params => {
    return spider_axios.request({
        url: 'spider/py_pipeline_mgt/python_publish_app/get_publish_app_list',
        params: params,
        method: 'get'
    })
}

export const getLastApplyInfoApi = params => {
    return spider_axios.request({
        url: 'spider/ci_cd_mgt/h5/get_last_apply_info_api',
        method: 'get',
        params
    })
}

export const pyProdPublishInfo = params => {
    return spider_axios.request({
        url: 'spider/publish/publish_info_api',
        method: 'get',
        params
    })
}
