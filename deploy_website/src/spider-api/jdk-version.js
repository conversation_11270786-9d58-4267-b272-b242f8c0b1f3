import store from '@/store'
import spider_axios from '@/libs/spider_api.request'


export const setIterAppJdk = (data) => {
  return spider_axios.request({
    url: 'spider/git_iterative/set_iter_app_jdk',
    headers: { 'token': store.state.user.token },
    data: {
      'iterative_id': data['iterative_id'],
      'app_name': data['app_name'],
      'jdk_version': data['jdk_version']
    },
    method: 'put'
  })
};
