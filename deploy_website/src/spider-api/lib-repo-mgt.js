import spider_axios from '@/libs/spider_api.request'


/**
 * 获取lib url
 * @param req
 * @returns {*|AxiosPromise<any>|HttpOptions|void}
 */
export const getLibUrlApi = (iteration_id, app_name, lib_type, suite_code) => {
  return spider_axios.request({
    url: 'spider/lib_repo_mgt/get_lib_url/',
    method: 'get',
    params: {
      iteration_id: iteration_id,
      app_name: app_name,
      lib_type: lib_type,
      suite_code: suite_code
    },
  })
}
