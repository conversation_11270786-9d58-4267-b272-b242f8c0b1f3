import spider_axios from '@/libs/spider_api.request'



export const updateConfirmInfo = (data) => {
  return spider_axios.request({
    url: '/spider/iter_mgt/product_publish_confirm_api',
    data: data,
    method: 'put'
  })
}

export const productPublishConfirmApi = (data) => {

  return spider_axios.request({
    url: '/spider/iter_mgt/product_publish_confirm_api',
    data: data,
    method: 'post'
  })
}

export const getConfirmIterationApi = (md5_str) => {

  return spider_axios.request({
    url: '/spider/iter_mgt/product_publish_confirm_api',
     params: {
      'md5_str': md5_str
    },
    method: 'get'
  })
}




export const getReposPlanApi = (repos_info) => {
  return spider_axios.request({
    url: '/spider/iter_mgt/product_publish_save_api',
    params: {
      'repos_info': repos_info
    },
    method: 'get'
  })
}

export const saveReposPlanApi = (data) => {
  return spider_axios.request({
    url: '/spider/iter_mgt/product_publish_save_api',
    data: data,
    method: 'put'
  })
}

