import spider_axios from '@/libs/spider_api.request'

// 获取我的流水线列表
export const createPipeLineLog = (opt_desc,pipeline_id,job_name) => {
  const info = new URLSearchParams()
  info.append('opt_desc', opt_desc)
  info.append('job_name', job_name)
  info.append('pipeline_id', pipeline_id)

  return spider_axios.request({
    url: '/spider/iter_mgt/pipeline_log_view',
    params: info,
    method: 'post'
  })
}


