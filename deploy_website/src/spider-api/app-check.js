import spider_axios from '@/libs/spider_api.request'

/**
 * app 回合校验
 * @param req
 * @returns {*|AxiosPromise<any>|HttpOptions|void}
 */
export const AppMergeCheckApi = (req) => {
  return spider_axios.request({
    url: 'spider/task_mgt/app_merge_check_api',
    method: 'get',
    params: {
      iteration_id: req.iteration_id,
      app_name_list: req.app_name_list
    },
  })
}

/**
 * app 制品代码版本是否为最新
 * @param req
 * @returns {*|AxiosPromise<any>|HttpOptions|void}
 */
export const AppCommitCheckApi = (req) => {
  return spider_axios.request({
    url: 'spider/task_mgt/app_commit_check',
    method: 'get',
    params: {
      iteration_id: req.iteration_id,
      app_name_list: req.app_name_list
    },
  })
}



