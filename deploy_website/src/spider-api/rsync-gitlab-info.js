import spider_axios from '@/libs/spider_api.request'


export const rsyncGitlabReposApi = (req) => {
  // 返回一个promise对象
  return spider_axios.request({
    url: 'spider/iter_mgt/rsync_gitlab_repos_api',
    method: 'put',
  })
}

export const rsyncGitlabMembersApi = (req) => {
  // 返回一个promise对象
  return spider_axios.request({
    url: 'spider/iter_mgt/rsync_gitlab_members_api',
    method: 'put',
  })
}
