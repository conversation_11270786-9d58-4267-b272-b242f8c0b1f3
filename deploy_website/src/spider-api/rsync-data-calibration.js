import spider_axios from '@/libs/spider_api.request'

/**
 * 数据刷新接口
 * @param req {"app_name":"vendor","iter_id":"h5-pkg-fund_0622_zhongou","refresh_action_item_list":["publish_apply"]}
 * @returns {
    "status": "success",
    "data": {
        "publish_apply": {
            "is_refresh": false,
            "status": "script_failure",
            "msg": "状态相同不用更新"
        }
    },
    "msg": "执行成功"
}
 */
export const RefreshExecStatusApi = (req) => {
  return spider_axios.request({
    url: 'spider/task_mgt/refresh_exec_status_api/',
    method: 'post',
    data: req,
  })
}
