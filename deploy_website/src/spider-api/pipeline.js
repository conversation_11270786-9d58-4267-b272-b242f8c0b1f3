import spider_axios from '@/libs/spider_api.request'

// 迭代归档
export const pipelineArchive = pipeline_id => {
    const info = new URLSearchParams()
    info.append('pipeline_id', pipeline_id)

    return spider_axios.request({
        url: '/spider/iter_mgt/archive',
        data: info,
        method: 'post'
    })
}

// 获取jenkins构建流水线列表
export const getPipelineInfo = (pipeline_id, get_pipeline_info_type) => {
    return spider_axios.request({
        url: '/spider/iter_mgt/pipeline',
        params: { pipeline_id: pipeline_id, get_pipeline_info_type: get_pipeline_info_type },
        method: 'get'
    })
}

// 执行全量构建
export const doJenkinsCompileJob = (job_name, skip, is_junit, is_mock_agent, biz_base_db, db_exec_type) => {
    const info = new URLSearchParams()
    info.append('job_name', job_name)
    info.append('skip', skip)
    info.append('is_junit', is_junit)
    info.append('is_mock_agent', is_mock_agent)
    info.append('biz_base_db', biz_base_db)
    info.append('db_exec_type', db_exec_type)

    return spider_axios.request({
        url: '/spider/iter_mgt/pipeline',
        data: info,
        method: 'post'
    })
}
// 保存测试环境
export const saveOrUpdateEnvBind = (pipeline_id, app_name, env) => {
    const info = new URLSearchParams()
    info.append('pipeline_id', pipeline_id)
    info.append('app_name', app_name)
    info.append('env', env)

    return spider_axios.request({
        url: '/spider/iter_mgt/pipeline',
        data: info,
        method: 'put'
    })
}

// 保存测试环境
export const saveOrUpdateBatchEnvBind = param => {
    return spider_axios.request({
        url: '/spider/iter_mgt/pipeline/batch_bind_env',
        data: param,
        method: 'post'
    })
}

// 保存测试集id
export const saveOrUpdateEnvTestsuiteBind = (pipeline_id, app_name, env, testsuite_id) => {
    const info = new URLSearchParams()
    info.append('pipeline_id', pipeline_id)
    info.append('app_name', app_name)
    info.append('env', env)
    info.append('testsuite_id', testsuite_id)

    return spider_axios.request({
        url: '/spider/pipeline/env_testsuite_bind',
        data: info,
        method: 'put'
    })
}

// 保存应用环境和agent的绑定关系
export const saveOrUpdateEnvAppAgentBind = (env, app_name, agent_use_info) => {
    const info = new URLSearchParams()
    info.append('suite_code', env)
    info.append('app_name', app_name)
    info.append('agent_use_info', agent_use_info)

    return spider_axios.request({
        url: '/spider/pipeline/agent_env_app_bind',
        data: info,
        method: 'put'
    })
}

// 解析构建应用
export const parseCompileApp = job_name => {
    return spider_axios.request({
        url: '/spider/pipeline/custom_build',
        params: { job_name: job_name },
        method: 'get'
    })
}

export const get_app_db_info = module_name => {
    return spider_axios.request({
        url: '/spider/db_mgt/app_db_info/',
        params: { module_name: module_name },
        method: 'get'
    })
}

// 执行自定义构建
export const doCustomCompile = (job_name, compile_list) => {
    const info = new URLSearchParams()
    info.append('job_name', job_name)
    info.append('compile_list', compile_list)
    return spider_axios.request({
        url: '/spider/pipeline/custom_build',
        data: info,
        method: 'post'
    })
}

// 测试环境绑定信息
export const getEnvBindInfo = pipeline_id => {
    return spider_axios.request({
        url: '/spider/pipeline/env_bind',
        params: { pipeline_id: pipeline_id },
        method: 'get'
    })
}

// 迭代所属组的最近一次完成（归档）信息
export const getAppLatestArchiveInfo = pipeline_id => {
    return spider_axios.request({
        url: '/spider/iter_mgt/iter_app_latest_archive_info_api',
        params: { pipeline_id: pipeline_id },
        method: 'get'
    })
}

// 迭代所属组的正在上线中的应用信息
export const getAppOnlineingAppInfo = pipeline_id => {
    return spider_axios.request({
        url: '/spider/iter_mgt/iter_app_online_info_api',
        params: { pipeline_id: pipeline_id },
        method: 'get'
    })
}

// 测试环境绑定
export const doEnvBind = (job_name, app_name, env) => {
    const info = new URLSearchParams()
    info.append('job_name', job_name)
    info.append('app_name', app_name)
    info.append('env', env)
    return spider_axios.request({
        url: '/spider/pipeline/env_bind',
        data: info,
        method: 'post'
    })
}

// 推送测试环境
export const pushTestEnv = (
    pipeline_id,
    node_code,
    app_name,
    env,
    test_set_id,
    jacoco_type,
    mock_type,
    arex_type,
    pinpoint_type,
    agent_info
) => {
    const info = new URLSearchParams()
    info.append('pipeline_id', pipeline_id)
    info.append('node_code', node_code)
    info.append('app_name', app_name)
    info.append('env', env)
    info.append('test_set_id', test_set_id)
    info.append('jacoco_type', jacoco_type)
    info.append('mock_type', mock_type)
    info.append('arex_type', arex_type)
    info.append('pinpoint_type', pinpoint_type)
    info.append('agent_info', agent_info)
    return spider_axios.request({
        url: '/spider/pipeline/env_bind',
        data: info,
        method: 'put'
    })
}

// 测试环境推送结果
export const getEnvPushHistory = (env_name, pipeline_id, app_name) => {
    return spider_axios.request({
        url: '/spider/pipeline/env_push',
        params: {
            env_name: env_name,
            pipeline_id: pipeline_id,
            app_name: app_name
        },
        method: 'get'
    })
}

export const getRecoverDumpInfo = app_name => {
    return spider_axios.request({
        url: '/spider/pipeline/recover_dump_info',
        params: {
            app_name: app_name
        },
        method: 'get'
    })
}

export const getBisInfo = () => {
    return spider_axios.request({
        url: 'spider/pipeline/test_data_dev',
        params: {},
        method: 'get'
    })
}

export const doJenkinsTestDataDeploy = (job_name, bis_pipeline_id, suite_code, app_list, biz_br_name = '') => {
    const info = {}
    info['job_name'] = job_name
    info['bis_pipeline_id'] = bis_pipeline_id
    info['suite_code'] = suite_code
    info['app_list'] = app_list
    info['biz_br_name'] = biz_br_name
    // 新页面，基础库集不需要再传
    // info['biz_base_db_code'] = biz_base_db
    // info['biz_base_db_br'] = biz_base_db_br
    return spider_axios.request({
        url: '/spider/db_mgt/db_iter_mgt_pipeline/',
        data: info,
        method: 'post'
    })
}

export const getBisLabelInfo = bis_code => {
    return spider_axios.request({
        url: '/spider/db_mgt/get_bis_label_info',
        method: 'get',
        params: {
            bis_code: bis_code
        }
    })
}

export const getBisLabelInfoByBisPipelineId = bis_pipeline_id => {
    return spider_axios.request({
        url: '/spider/db_mgt/get_bis_label_info_pipeline_id',
        method: 'get',
        params: {
            bis_pipeline_id: bis_pipeline_id
        }
    })
}

export const saveIterBislab = (bis_pipeline_id, bis_lab_list) => {
    return spider_axios.request({
        url: '/spider/db_mgt/save_iter_bis_lab/',
        method: 'post',
        data: {
            bis_pipeline_id: bis_pipeline_id,
            bis_lab_list: bis_lab_list
        }
    })
}
// 查询应用可部署的环境列表
export const getAppSuiteList = data => {
    return spider_axios.request({
        url: '/spider/iter_mgt/pipeline/get_app_suite_list',
        method: 'post',
        data
    })
}
// 应用测试环境批量编译发布
export const saveBatchComPile = data => {
    return spider_axios.request({
        url: '/spider/iter_mgt/pipeline/batch_compile_deploy_app',
        method: 'post',
        data
    })
}
// 查询应用在环境下的默认agent配置
export const getAgentUseInfoRequest = params => {
    return spider_axios.request({
        url: '/spider/pipeline/get_agent_use_info',
        method: 'get',
        params
    })
}
