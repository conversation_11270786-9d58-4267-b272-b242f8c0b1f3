import spider_axios from '@/libs/spider_api.request'
import store from '../store'

export const getGitRopesApi = req => {
    // 获取用户拥有权限的所有git仓库列表
    return spider_axios.request({
        url: 'spider/iter_mgt/repos_tree_api',
        method: 'get'
    })
}

export const getIterListApi = project_type => {
    // 获取用户拥有权限的所有迭代信息列表
    return spider_axios.request({
        url: 'spider/iter_mgt/iter_list_api',
        method: 'get',
        params: {
            project_type: project_type
        }
    })
}

export const iterListAllApi = req => {
    // 获取用户拥有权限的所有迭代信息列表
    return spider_axios.request({
        url: 'spider/iter_mgt/iter_list_all_api',
        method: 'get',
        params: {
            days: req['days']
        }
    })
}

export const getH5Group = () => {
    // 获取移动端组信息
    return spider_axios.request({
        url: 'spider/iter_mgt/get_h5_group',
        method: 'get'
    })
}
export const getPyGroup = () => {
    // 获取移动端组信息
    return spider_axios.request({
        url: 'spider/iter_mgt/get_py_group',
        method: 'get'
    })
}

export const getHistoryIterListApi = () => {
    // 获取用户拥有权限的所有迭代信息列表
    return spider_axios.request({
        url: 'spider/iter_mgt/history_iter_list_api',
        method: 'get'
    })
}

export const getIterListSearchApi = req => {
    // 获取用户拥有权限的所有迭代信息列表
    return spider_axios.request({
        url: 'spider/iter_mgt/iter_list_search_api',
        method: 'get',
        params: {
            pipeline_id_search: req['pipeline_id_search'],
            project_group_search: req['project_group_search'],
            br_style_search: req['br_style_search'],
            pageNum: req['pageNum'],
            pageSize: req['pageSize'],
            pageTotal: req['pageTotal'],
            description_content_search: req['description_content_search'],
            branch_is_new: req['branch_is_new']
        }
    })
}

export const getIterGitRopesApi = req => {
    // 获取迭代下的所有仓库信息
    return spider_axios.request({
        url: 'spider/iter_mgt/git_repos_info_api',
        method: 'get',
        params: {
            iterationID: req['iterationID']
        }
    })
}

export const getGitGroupRopesApi = req => {
    // 获取一个gitlab组下的 仓库信息
    return spider_axios.request({
        url: 'spider/iter_mgt/group_repos_info_api',
        method: 'get',
        params: {
            iterationID: req['iterationID']
        }
    })
}

export const iterGroupOtherReposInfoApi = req => {
    // 迭代所属组的 其他仓库信息
    return spider_axios.request({
        url: 'spider/iter_mgt/iter_group_other_repos_info_api',
        method: 'get',
        params: {
            iterationID: req['iterationID']
        }
    })
}

export const getGitlabMenberApi = req => {
    // 返回一个promise对象
    return spider_axios.request({
        url: 'git_iterative/rsync_gitlab_members',
        headers: { token: store.state.user.token },
        method: 'get'
    })
}

export const getPackageTypeInIterApi = iter_id => {
    // 迭代所属组的 其他仓库信息
    return spider_axios.request({
        url: 'spider/iter_mgt/package_type_in_iter_Api',
        method: 'get',
        params: {
            iter_id: iter_id
        }
    })
}

export const getIterInfo = (pipeline_id, group_name) => {
    // 迭代所属组的 其他仓库信息
    return spider_axios.request({
        url: 'spider/iter_mgt/get_iter_info',
        method: 'get',
        params: {
            pipeline_id: pipeline_id,
            group_name: group_name
        }
    })
}

export const saveSelfTestFlag = row => {
    // 保存迭代是否研发自测
    return spider_axios.request({
        url: 'spider/iter_mgt/save_self_test_flag/',
        method: 'post',
        data: {
            pipeline_id: row['pipeline_id'],
            self_test_flag: row['self_test_flag'],
            br_style: row['br_style']
        }
    })
}

export const publishPlanExecuteImmediately = (batch_no, pipeline_id) => {
    // 保存迭代是否研发自测
    return spider_axios.request({
        url: 'spider/iter_mgt/publish_plan_execute_immediately/',
        method: 'post',
        data: {
            batch_no: batch_no,
            pipeline_id: pipeline_id
        }
    })
}

export const getIterQualityReportApi = data => {
    return spider_axios.request({
        url: 'spider/test_mgt/report/get_agg_report/',
        method: 'post',
        data: data
    })
}
