import store from '@/store'
import spider_axios from '@/libs/spider_api.request'



export const syncConfigApi = (pipeline_id, app_name_list, env) => {
  // 同步配置接口
  return spider_axios.request({
    url: 'spider/task_mgt/sync_config_api/',
    method: 'post',
    data: {
      'iteration_id': pipeline_id,
      'app_name': app_name_list,
      'env': env
    },
  })
}

export const checkConfigConsistentApi = (pipeline_id, app_name_list, env) => {
  // 检查配置一致性
  return spider_axios.request({
    url: 'spider/task_mgt/check_config_consistent_api/',
    method: 'get',
    params: {
      'iteration_id': pipeline_id,
      'app_name': app_name_list,
      'env': env
    },
  })
}

export const CheckConfigSyncApi = (pipeline_id, app_name_list, env) => {
  //检查配置是否同步
  return spider_axios.request({
    url: 'spider/task_mgt/check_config_sync_api/',
    method: 'get',
    params: {
      'iteration_id': pipeline_id,
      'app_name': app_name_list,
      'env': env
    },
  })
}

export const CheckConfigMergeApi = (pipeline_id, app_name_list) => {
  //检查配置是否同步
  return spider_axios.request({
    url: 'spider/task_mgt/check_config_merge_api/',
    method: 'get',
    params: {
      'iteration_id': pipeline_id,
      'app_name': app_name_list
    },
  })
}

export const publishConfigBranch = (pipeline_id, app_name, env) => {
  // 发布配置接口
  return spider_axios.request({
    url: 'spider/task_mgt/publish_config_branch_api/',
    method: 'post',
    data: {
      'iteration_id': pipeline_id,
      'app_name': app_name,
      'env': env
    },
  })
}

export const fileConfigBranch = (pipeline_id, app_name_list, env) => {
  // 归档配置接口
  return spider_axios.request({
    url: 'spider/task_mgt/file_config_branch_api/',
    method: 'put',
    data: {
      'iteration_id': pipeline_id,
      'app_name': app_name_list,
      'env': env
    },
  })
}


export const createConfigBranch = (req) => {
  // 返回一个promise对象
  return spider_axios.request({
    url: 'spider/task_mgt/create_config_branch_api/',
    method: 'post',

    data: {
      'branch_name': req['branch_name'],
      'desc': req['desc'],
      'repos_str': req['repos_str'],
    },
  })
}
