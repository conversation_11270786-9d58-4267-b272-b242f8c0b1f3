import store from '@/store'
import spider_axios from '@/libs/spider_api.request'

/**
 * 检查脚本分支合并状态接口
 */
export const checkScriptBranchMergeStatus = data => {
    return spider_axios.request({
        url: '/spider/iter_mgt/iter_publish_plan/get_script_branch_merge_status/',
        method: 'post',
        data: data
    })
}

/**
 * 写入原因接口
 */
export const createPublishReasonApi = data => {
    return spider_axios.request({
        url: 'spider/iter_mgt/publish_reason/create_publish_reason/',
        method: 'post',
        data: data
    })
}

/**
 * 归档时查询是否需要填写理由的接口
 */
export const getNeedReasonNodeInfoApi = params => {
    return spider_axios.request({
        url: 'spider/iter_mgt/publish_reason/get_need_reason_node_info',
        method: 'get',
        params: params
    })
}

export const publishApplyCheck = (pipeline_id, env, app_name_list) => {
    const info = new URLSearchParams()
    info.append('pipeline_id', pipeline_id)
    info.append('env', env)
    info.append('app_name_list', app_name_list)

    return spider_axios.request({
        url: '/spider/iter_mgt/apply_check',
        data: info,
        method: 'post'
    })
}

export const publishApply = (pipeline_id, env, app_name_list, send_email) => {
    const info = new URLSearchParams()
    info.append('pipeline_id', pipeline_id)
    info.append('env', env)
    info.append('app_name_list', app_name_list)
    info.append('send_email', send_email)

    return spider_axios.request({
        url: '/spider/iter_mgt/apply',
        data: info,
        method: 'post'
    })
}

export const getProdApplyInfo = pipeline_id => {
    return spider_axios.request({
        url: '/spider/iter_mgt/apply',
        params: {
            pipeline_id: pipeline_id
        },
        method: 'get'
    })
}

export const confirmProdApply = md5_str => {
    const info = new URLSearchParams()
    info.append('md5_str', md5_str)

    return spider_axios.request({
        url: '/spider/iter_mgt/apply_notice',
        data: info,
        method: 'put'
    })
}

export const h5ConfirmProdApply = (md5_str, suite_name) => {
    const info = new URLSearchParams()
    info.append('md5_str', md5_str)
    info.append('suite_name', suite_name)

    return spider_axios.request({
        url: '/spider/iter_mgt/h5_apply_notice',
        data: info,
        method: 'put'
    })
}

export const proApplyNotice = (pipeline_id, receiver, domain, warn_info, suite_name) => {
    const info = new URLSearchParams()
    info.append('pipeline_id', pipeline_id)
    info.append('receiver', receiver)
    info.append('domain', domain)
    info.append('warn_info', warn_info)
    info.append('suite_name', suite_name)

    return spider_axios.request({
        url: '/spider/iter_mgt/apply_notice',
        data: info,
        method: 'post'
    })
}

export const h5ProApplyNotice = (
    pipeline_id,
    receiver,
    domain,
    suite_name,
    app_name_list,
    fund_end_ver,
    piggy_end_ver,
    Version,
    branch,
    fundh5ZipVersion,
    piggyh5ZipVersion,
    branch_type,
    prod_content
) => {
    const info = new URLSearchParams()
    info.append('pipeline_id', pipeline_id)
    info.append('receiver', receiver)
    info.append('domain', domain)
    info.append('suite_name', suite_name)
    info.append('app_name_list', app_name_list)
    info.append('fund_end_ver', fund_end_ver)
    info.append('piggy_end_ver', piggy_end_ver)
    info.append('Version', Version)
    info.append('branch', branch)
    info.append('fundh5ZipVersion', fundh5ZipVersion)
    info.append('piggyh5ZipVersion', piggyh5ZipVersion)
    info.append('branch_type', branch_type)
    info.append('prod_content', prod_content)
    return spider_axios.request({
        url: '/spider/iter_mgt/h5_apply_notice',
        data: info,
        method: 'post'
    })
}

export const cancelProApply = pipeline_id => {
    const info = new URLSearchParams()
    info.append('pipeline_id', pipeline_id)

    return spider_axios.request({
        url: '/spider/iter_mgt/apply',
        data: info,
        method: 'delete'
    })
}

export const getPlanInfo = pipeline_id => {
    return spider_axios.request({
        url: '/spider/iter_mgt/plan',
        params: {
            pipeline_id: pipeline_id
        },
        method: 'get'
    })
}

export const setPlanInfo = data => {
    return spider_axios.request({
        url: '/spider/iter_mgt/plan',
        data: data,
        method: 'put'
    })
}

export const getRepoDiff = (pipeline_id, app_name) => {
    return spider_axios.request({
        url: '/spider/iter_mgt/repo_diff',
        params: {
            pipeline_id: pipeline_id,
            app_name: app_name
        },
        method: 'get'
    })
}

export const cancel_prod_apply = pipeline_id => {
    return spider_axios.request({
        url: '/spider/iter_mgt/apply/cancel_apply',
        method: 'post',
        data: {
            pipeline_id: pipeline_id
        }
    })
}

export const publishConfigBranch = req => {
    // 发布配置接口
    return spider_axios.request({
        url: 'spider/task_mgt/publish_config_branch_api/',
        method: 'post',
        data: {
            iteration_id: req['iteration_id'],
            env: req['env']
        }
    })
}

export const getApplyNotice = (pipeline_id, env_name) => {
    return spider_axios.request({
        url: '/spider/iter_mgt/apply_notice',
        params: {
            pipeline_id: pipeline_id,
            env_name: env_name
        },
        method: 'get'
    })
}

export const getArchiveNotice = pipeline_id => {
    return spider_axios.request({
        url: '/spider/iter_mgt/archive',
        params: {
            pipeline_id: pipeline_id
        },
        method: 'get'
    })
}

export const getServiceResult = sid => {
    return spider_axios.request({
        url: '/spider/task_mgt/external_service_result',
        params: {
            sid: sid
        },
        method: 'get'
    })
}

export const archiveCheckApi = iteration_id => {
    // 归档检查接口
    return spider_axios.request({
        url: 'spider/iter_mgt/archive_check_api',
        params: {
            iteration_id: iteration_id
        },
        method: 'get'
    })
}

export const getDependRelation = iteration_id => {
    // 归档检查接口
    return spider_axios.request({
        url: 'spider/iter_mgt/get_depend_relation',
        params: {
            iteration_id: iteration_id
        },
        method: 'get'
    })
}
/* export const archiveCheckApi = (iteration_id,app_name_list) => {
  // 归档检查接口
  return spider_axios.request({
    url: 'spider/iter_mgt/archive_check_api',
    params: {
      'iteration_id': iteration_id,
      'app_name_list': app_name_list
    },
    method: 'get'
  })
} */

export const archiveApi = iteration_id => {
    // 调用归档接口
    return spider_axios.request({
        url: 'spider/iter_mgt/archive_api',
        method: 'put',
        data: {
            iteration_id: iteration_id
        }
    })
}
export const applyCheckEmail = iteration_id => {
    // 是否当天有发送确认邮件接口
    return spider_axios.request({
        url: 'spider/iter_mgt/apply_check_email',
        params: {
            pipeline_id: iteration_id
        },
        method: 'get'
    })
}
export const getOnlineApplyTableInfo = iteration_id => {
    // 是否当天有发送确认邮件接口
    return spider_axios.request({
        url: 'spider/iter_mgt/online_apply_api',
        params: {
            pipeline_id: iteration_id
        },
        method: 'get'
    })
}

export const getOnlineSqlApplyApi = iteration_id => {
    // 获取待上线的SQL文件列表
    return spider_axios.request({
        url: 'spider/iter_mgt/online_sql_apply_api',
        params: {
            pipeline_id: iteration_id
        },
        method: 'get'
    })
}

export const getUserArcheryTokenIsTimeout = () => {
    // 用户产线archerytoken是否过期接口
    return spider_axios.request({
        url: 'spider/user/user_archery_token_is_timeout',
        method: 'get'
    })
}
export const getEmailAddresses = () => {
    return spider_axios.request({
        url: 'spider/publish/get_email_addresses/',
        method: 'get'
    })
}

export const loginArchery = req => {
    // 登录archery
    return spider_axios.request({
        url: 'spider/user/login_archery/',
        method: 'post',
        data: req
    })
}

/**
 * 申
 * @param req
 * @returns {*|AxiosPromise<any>|HttpOptions|void}
 */
export const ServersPublishApplyApi = req => {
    return spider_axios.request({
        url: 'spider/ci_cd_mgt/server/servers_publish_apply_api/',
        method: 'post',
        data: req
    })
}

export const getCheckServiceMobile = req => {
    // 是否当天有发送确认邮件接口
    return spider_axios.request({
        url: 'spider/iter_mgt/check_service_mobile',
        params: {
            project_list: req
        },
        method: 'get'
    })
}

export const syncIterationToTapd = req => {
    // 是否当天有发送确认邮件接口
    return spider_axios.request({
        url: 'spider/tapd_mgt/sync_iteration_to_tapd/',
        data: { pipeline_id: req },
        method: 'post'
    })
}

export const getIterPublishPlanByPage = data => {
    // 是否当天有发送确认邮件接口
    return spider_axios.request({
        url: '/spider/iter_mgt/iter_publish_plan/get_by_page/',
        data: data,
        method: 'post'
    })
}

export const stopIterPublishPlan = data => {
    // 是否当天有发送确认邮件接口
    return spider_axios.request({
        url: '/spider/iter_mgt/iter_publish_plan/stop/',
        data: data,
        method: 'post'
    })
}

export const getCurrentIterAppPublishPlanLatestHistory = data => {
    // 是否当天有发送确认邮件接口
    return spider_axios.request({
        url: '/spider/iter_mgt/iter_publish_plan/get_publish_plan_history/',
        data: data,
        method: 'post'
    })
}
export const get_publish_plan_nodes = data => {
    // 是否当天有发送确认邮件接口
    return spider_axios.request({
        url: '/spider/iter_mgt/iter_publish_plan/get_publish_plan_nodes/',
        data: data,
        method: 'post'
    })
}
export const update_publish_plan_node_default_param = data => {
    // 是否当天有发送确认邮件接口
    return spider_axios.request({
        url: '/spider/iter_mgt/iter_publish_plan/update_publish_plan_node_default_param/',
        data: data,
        method: 'post'
    })
}

export const get_publish_plan_node_default_param = data => {
    // 是否当天有发送确认邮件接口
    return spider_axios.request({
        url: '/spider/iter_mgt/iter_publish_plan/get_publish_plan_node_default_param/',
        data: data,
        method: 'post'
    })
}

export const getBusinessBranch = module_name => {
    return spider_axios.request({
        url: 'spider/iter_mgt/iter_publish_plan/get_app_relative_test_iter_list/',
        params: { module_name: module_name },
        method: 'get'
    })
}
export const getFlowTableList = data => {
    return spider_axios.request({
        url: '/spider/iter_mgt/iter_publish_plan/get_test_set_detail/',
        data,
        method: 'post'
    })
}
export const getDataDevelopmentEvn = params => {
    return spider_axios.request({
        url: 'spider/test_env_mgt/get_data_development_env',
        params,
        method: 'get'
    })
}
export const getAppProdSuites = params => {
    return spider_axios.request({
        url: 'spider/env_mgt/get_app_prod_suites/',
        params,
        method: 'get'
    })
}
export const getEmailAddressesRequest = params => {
    return spider_axios.request({
        url: '/spider/publish/get_email_addresses/',
        params,
        method: 'get'
    })
}
export const getCommitSql = params => {
    return spider_axios.request({
        url: '/spider/iter_mgt/iter_publish_plan/get_commit_sql',
        params,
        method: 'get'
    })
}
export const checkBeforeCreate = data => {
    return spider_axios.request({
        url: 'spider/iter_mgt/iter_publish_restrict/',
        data,
        method: 'post'
    })
}
export const submitPublishPlan = data => {
    return spider_axios.request({
        url: 'spider/iter_mgt/iter_publish_plan/create/',
        data,
        method: 'post'
    })
}
export const getEnvBindInfo = data => {
    return spider_axios.request({
        url: '/spider/pipeline/env_bind',
        params: data,
        method: 'get'
    })
}
// -------------normal多应用
export const getNormalBtnStatus = data => {
    return spider_axios.request({
        url: '/spider/iter_mgt/get_no_group_publish_app/',
        params: data,
        method: 'get'
    })
}
export const getPublishConfig = data => {
    return spider_axios.request({
        url: '/spider/iter_mgt/iter_publish_plan/get_publish_plan_config/',
        data,
        method: 'post'
    })
}
export const getTestSuitCode = data => {
    return spider_axios.request({
        url: '/spider/biz_mgt/get_test_suite_code/',
        params: data,
        method: 'get'
    })
}
export const getFlowList = params => {
    return spider_axios.request({
        url: 'spider/biz_mgt/get_test_flow_list/',
        params,
        method: 'get'
    })
}
export const getFlowListNew = params => {
    return spider_axios.request({
        url: 'spider/biz_mgt/get_biz_flow_list/',
        params,
        method: 'get'
    })
}
export const getSuitByIterId = params => {
    return spider_axios.request({
        url: '/spider/iter_mgt/group_publish/get_suite_by_iter_id/',
        params,
        method: 'get'
    })
}
export const getGroupPublishAppList = params => {
    return spider_axios.request({
        url: '/spider/iter_mgt/group_publish/get_group_publish_app_list/',
        params,
        method: 'get'
    })
}
export const saveGroupPbulishMode = data => {
    return spider_axios.request({
        url: '/spider/iter_mgt/group_publish/save_group_publish_mode/',
        data,
        method: 'post'
    })
}
export const createIterPublishPlan = data => {
    // 是否当天有发送确认邮件接口
    return spider_axios.request({
        url: '/spider/iter_mgt/iter_publish_plan/create/',
        data: data,
        method: 'post'
    })
}
export const getAutoTestReport = params => {
    return spider_axios.request({
        url: '/spider/iter_mgt/get_iter_auto_test_report',
        params,
        method: 'get'
    })
}
export const get_mantis_request = data => {
    return spider_axios.request({
        url: '/spider/external_interaction/mantis_mgt/mantis_request_api/get_mantis_request/',
        data: data,
        method: 'post'
    })
}
