import spider_axios from '@/libs/spider_api.request'


export const changeAppGroupNodeApi = (data) => {
  return spider_axios.request({
    url: 'spider/env_mgt/app_group_node_mgt_api/',

    data: {
      'app_name': data['app_name'],
      'ip': data['ip'],
      'to_group': data['to_group']
    },
    method: 'put'
  })
};

export const getAppGroupNodeApi = (data) => {
  return spider_axios.request({
    url: 'spider/env_mgt/app_group_node_mgt_api/',

    params: {
      'app_name': data['app_name']
    },
    method: 'get'
  })
};

export const getAppGroupApi = (data) => {
  return spider_axios.request({
    url: 'spider/env_mgt/app_group_mgt_api/',

    params: {
      'app_name': data['app_name']
    },
    method: 'get'
  })
};

export const addAppGroupApi = (data) => {
  return spider_axios.request({
    url: 'spider/env_mgt/app_group_mgt_api/',

    data: {
      'app_name': data['app_name'],
      'group_name': data['group_name'],
    },
    method: 'post'
  })
};

export const setAppGroupApi = (data) => {
  return spider_axios.request({
    url: 'spider/env_mgt/app_group_mgt_api/',

    data: {
      'app_name': data['app_name'],
      'cur_name': data['cur_name'],
      'new_name': data['new_name'],
    },
    method: 'put'
  })
};

export const delAppGroupApi = (data) => {
  return spider_axios.request({
    url: 'spider/env_mgt/app_group_mgt_api/',

    params: {
      'app_name': data['app_name'],
      'group_name': data['group_name'],
    },
    method: 'delete'
  })
};
