import spider_axios from '@/libs/spider_api.request'

export const getBisInfo = req => {
    // 返回一个promise对象
    return spider_axios.request({
        url: 'spider/db_mgt/test_data_bis_info',
        method: 'get'
    })
}

export const getBisVersionInfo = req => {
    // 返回一个promise对象
    return spider_axios.request({
        url: 'spider/db_mgt/test_data_bis_version_info',
        method: 'get',
        params: {
            bis_type: req['bis_type'],
            sql_branch_name: req['sql_branch_name']
        }
    })
}

export const getBisBranchInfo = req => {
    // 返回一个promise对象
    return spider_axios.request({
        url: 'spider/db_mgt/test_data_bis_branch_info',
        method: 'get',
        params: {
            bis_type: req['bis_type']
        }
    })
}

export const getBisBranchAppInfo = req => {
    // 返回一个promise对象
    return spider_axios.request({
        url: 'spider/db_mgt/test_data_bis_app_info',
        method: 'get',
        params: {
            bis_type: req['bis_type'],
            sql_branch_name: req['sql_branch_name']
        }
    })
}

export const getBisExecPlanInfo = req => {
    // 返回一个promise对象
    return spider_axios.request({
        url: 'spider/db_mgt/test_data_exec_plan_info',
        method: 'get'
    })
}

export const testDataSaveExecPlan = (select_row_test_plan, formValidate, setting_time, test_id, form) => {
    // 返回一个promise对象
    return spider_axios.request({
        url: 'spider/db_mgt/test_data_save_exec_plan/',
        method: 'post',

        data: {
            select_row_test_plan: select_row_test_plan,
            formValidate: formValidate,
            setting_time: setting_time,
            test_id: test_id,
            form: form
        }
    })
}

export const dbTestInitMultiPushNew = data => {
    // 返回一个promise对象
    return spider_axios.request({
        url: 'spider/db_mgt/db_test_init_multi_push/',
        method: 'post',

        data
    })
}
export const dbTestInitMultiPush = req => {
    // 返回一个promise对象
    return spider_axios.request({
        url: 'spider/db_mgt/db_test_init_multi_push/',
        method: 'post',

        data: {
            env_id: req['env_id'],
            env_name: req['env_name'],
            init_db: req['init_db'],
            ccms_type: req['ccms_type'],
            use_env_time: req['use_env_time'],
            clean_cache: req['clean_cache'],
            test_set_id: req['test_set_id'],
            app_dict_list: req['app_dict_list'],
            bis_pipeline_id: req['bis_pipeline_id']
        }
    })
}

export const getTestExecPlanRecord = req => {
    // 返回一个promise对象
    return spider_axios.request({
        url: 'spider/test_mgt/test_exec_plan_record',
        method: 'get',
        params: {
            bis_type: req['bis_type'],
            sql_branch_name: req['sql_branch_name']
        }
    })
}

export const getTestExecPlanBisRecord = req => {
    // 返回一个promise对象
    return spider_axios.request({
        url: 'spider/test_mgt/test_exec_plan_bis_record',
        method: 'get',
        params: {
            bis_type: req['bis_type'],
            sql_branch_name: req['sql_branch_name']
        }
    })
}

export const getTestExecPlanPipelineRecord = req => {
    // 返回一个promise对象
    return spider_axios.request({
        url: 'spider/test_mgt/test_exec_plan_change_pipeline',
        method: 'get',
        params: {
            bis_type: req['bis_type'],
            sql_branch_name: req['sql_branch_name']
        }
    })
}
