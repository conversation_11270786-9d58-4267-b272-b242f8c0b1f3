import spider_axios from '@/libs/spider_api.request'
import store from '../store'

export const addRepos = (req) => {
  // 返回一个promise对象
  return spider_axios.request({
    url: 'spider/iter_mgt/iter_mgt_api',
    method: 'post',

    data: {
      'iteration_id': req['iteration_id'],
      'repos_str': req['repos_str']
    }
  })
}

export const delRepos = (req) => {
  // 返回一个promise对象
  return spider_axios.request({
    url: 'spider/iter_mgt/iter_mgt_api',
    method: 'delete',

    data: {
      'iteration_id': req['iteration_id'],
      'repos_str': req['repos_str']
    }
  })
}

export const modIterInfo = (req) => {
  // 返回一个promise对象
  return spider_axios.request({
    url: 'spider/iter_mgt/iter_mgt_api',
    method: 'put',

    data: {
      'iteration_id': req['iteration_id'],
      'deadline': req['deadline'],
      'desc': req['description']
    }
  })
}

/**
 * 获取应用的分支信息
 * @param req
 * @returns {*|AxiosPromise<any>|HttpOptions|void}
 */
export const getAppBranchInfoApi = (req) => {
  return spider_axios.request({
    url: 'spider/iter_mgt/app_branch_info_api',
    method: 'get',
    params: {
      app_name: req.app_name
    }
  })
}

/**
 * 获取测试结束时间
 * @param req
 * @returns {*|AxiosPromise<any>|HttpOptions|void}
 */
export const getIterTestEndDateApi = (iter_id) => {
  return spider_axios.request({
    url: 'spider/iter_mgt/get_iter_test_end_date_api',
    method: 'get',
    params: {
      'iter_id': iter_id
    }
  })
}

/**
 * 修改测试结束时间
 * @param req
 * @returns {*|AxiosPromise<any>|HttpOptions|void}
 */
export const updateIterTestEndDateApi = (req) => {
  // 返回一个promise对象
  return spider_axios.request({
    url: 'spider/iter_mgt/update_iter_test_end_date_api/',
    method: 'post',

    data: {
      'iter_id': req['iter_id'],
      'test_end_date': req['test_end_date']
    }
  })
}

export const iterLockApi = (req) => {
  // 返回一个promise对象
  return spider_axios.request({
    url: 'spider/iter_mgt/iter_lock_info/',
    method: 'post',

    data: {
      'iteration_id': req['iteration_id'],
      'lock_type': req['lock_type'],
      'lock_status': true
    }
  })
}

export const iterUnlockApi = (req) => {
  // 返回一个promise对象
  return spider_axios.request({
    url: 'spider/iter_mgt/iter_lock_info/',
    method: 'post',

    data: {
      'iteration_id': req['iteration_id'],
      'lock_type': req['lock_type'],
      'lock_status': false
    }
  })
}

export const getIterLockInfoApi = (req) => {
  // 返回一个promise对象
  return spider_axios.request({
    url: 'spider/iter_mgt/iter_lock_info/',
    method: 'get',

    params: {
      'iteration_id': req['iteration_id'],
      'lock_type': req['lock_type']
    }
  })
}
