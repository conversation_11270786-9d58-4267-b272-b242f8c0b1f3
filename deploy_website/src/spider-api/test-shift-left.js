import spider_axios from '@/libs/spider_api.request'

// 调用测试左移流水线
export const callShiftLeftTestJob = (data) => {
  return spider_axios.request({
    url: '/spider/iter_mgt/call_shift_left_test_job_api/',
    data: {
      job_name: data.job_name,
      iteration_id: data.iteration_id,
      app_name_str: data.app_name_str,
      APIDOC_TYPE: data.APIDOC_TYPE,
      SQL_TYPE: data.SQL_TYPE,
      ES_TYPE: data.ES_TYPE
    },
    method: 'post'
  })
}

// 获取迭代下的应用列表
export const getAppListByIterationId = (iterationId) => {
  return spider_axios.request({
    url: '/spider/iter_mgt/get_app_list_by_iteration_id/',
    params: {
      iteration_id: iterationId
    },
    method: 'get'
  })
}

// 验证迭代ID是否有效
export const validateIterationId = (iterationId) => {
  return spider_axios.request({
    url: '/spider/iter_mgt/validate_iteration_id/',
    params: {
      iteration_id: iterationId
    },
    method: 'get'
  })
}