import spider_axios from '@/libs/spider_api.request'

/* 绑定应用信息 */
export const getSuiteBindApp = (data) => {
  return spider_axios.request({
    url: '/spider/test_env_mgt/get_suite_bind_app',
    params: data,
    method: 'get'
  })
}

// 调用环境初始化
export const TestSuiteInitApi = (data) => {
  let d = new Date(data.use_env_time);
  let datetime = d.getFullYear() + '-' + (d.getMonth() + 1) + '-' + d.getDate() + ' ' + d.getHours() + ':' + d.getMinutes() + ':' + d.getSeconds();

  return spider_axios.request({
    url: '/spider/test_env_mgt/test_suite_init_api',
    data: {
      'SUITE_ID': data.env_id,
      'SUITE_CODE': data.env_value,
      'DB_ENV': data.init_db,
      'SYS_DATETIME': datetime,
      'IS_CLEAR_CACHE': data.clean_cache,
      'TEST_SET': data.test_set_id,
      'H5_LIST': data.h5_list,
      'TMS_LIST': data.tms_list,
      'TP_LIST': data.tp_list,
      'PA_LIST': data.pa_list,
      'APP_OPS_LIST': data.app_ops_list,
      'FPC_LIST': data.fpc_list,
      'CRM_LIST': data.crm_list,
      'OTHER_LIST': data.other_list
    },
    method: 'post'
  })
}

/* 获取服务端应用列表信息 */
export const getJavaAppList = () => {
  return spider_axios.request({
    url: '/spider/app_mgt/get_java_app',
    method: 'get'
  })
}

export const getAppList = (param) => {
  return spider_axios.request({
    url: '/spider/app_mgt/get_apps/retrieve',
    method: 'get',
    params: param
  })
}


// 获取环境下的mock应用列表
export const get_need_mock_app_list_by_env = (data) => {
  const info = new URLSearchParams()
  info.append('env_id', data.env_id)
  info.append('tms_list', data.tms_list)
  info.append('tp_list', data.tp_list)
  info.append('pa_list', data.pa_list)
  info.append('fpc_list', data.fpc_list)
  info.append('crm_list', data.crm_list)
  info.append('other_list', data.other_list)
  return spider_axios.request({
    url: '/spider/test_env_mgt/get_need_mock_app_list_by_env',
    params: info,
    method: 'get'
  })
}
