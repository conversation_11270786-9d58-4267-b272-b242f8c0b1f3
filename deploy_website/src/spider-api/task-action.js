import spider_axios from '@/libs/spider_api.request'

/**
 * 任务停止接口
 * @param req {"app_name":"vendor","iter_id":"h5-pkg-fund_0622_zhongou","refresh_action_item_list":["publish_apply"]}
 * @returns {
    "status": "success",
    "data": {
        "publish_apply": {
            "is_stop": false,
            "msg": ""
        }
    },
    "msg": "执行成功"
}
 */
export const stopTaskApi = (req) => {
  return spider_axios.request({
    url: 'spider/task_mgt/stop_task_api/',
    method: 'post',
    data: req,
  })
}
