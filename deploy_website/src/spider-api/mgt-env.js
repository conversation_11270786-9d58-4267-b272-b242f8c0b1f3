import spider_axios from '@/libs/spider_api.request'

export const getEnvInfo = () => {
    return spider_axios.request({
        url: '/spider/env_mgt/env_mgt_api',
        params: { suite_is_active: 1 },
        method: 'get'
    })
}

export const createEnvMgt = envMgtForm => {
    return spider_axios.request({
        url: '/spider/env_mgt/env_mgt_api',
        data: {
            region_id: envMgtForm['region_id'],
            suite_code: envMgtForm['suite_code'],
            suite_name: envMgtForm['suite_name'],
            suite_desc: envMgtForm['suite_desc'],
            suite_is_active: envMgtForm['suite_is_active']
        },
        method: 'post'
    })
}

export const getRegionInfo = () => {
    return spider_axios.request({
        url: '/spider/env_mgt/env_mgt_region_api',
        params: { suite_is_active: 1 },
        method: 'get'
    })
}

export const getSuiteInfo = () => {
    return spider_axios.request({
        url: '/spider/env_mgt/env_mgt_suite_api',
        params: { suite_is_active: 1 },
        method: 'get'
    })
}

export const getSuiteList = search_params => {
    return spider_axios.request({
        url: '/spider/env_mgt/suite_list_api',
        params: {
            suite_is_active: search_params['suite_is_active'],
            region_id: search_params['region_id']
        },
        method: 'get'
    })
}

export const getLogicSuiteList = search_params => {
    return spider_axios.request({
        url: '/spider/env_mgt/get_logic_suites',
        params: {
            app_type: search_params['app_type']
        },
        method: 'get'
    })
}

export const getDockerInfo = () => {
    return spider_axios.request({
        url: '/spider/env_mgt/env_mgt_suite_api',
        params: { suite_is_active: 1 },
        method: 'get'
    })
}

// 获取「测试环境发布应用」列表 zt@2020-08-17
export const getTestPublishList = search_params => {
    return spider_axios.request({
        url: '/spider/iter_mgt/test_publish_list',
        params: {
            test_template: search_params['test_template']
        },
        method: 'get'
    })
}

export const createTestPublishSuite = search_params => {
    return spider_axios.request({
        url: '/spider/iter_mgt/test_publish_suite',
        data: {
            suite_name: search_params['suite_name']
        },
        method: 'post'
    })
}

// 获取测试环境节点绑定信息 zt@2020-08-19
export const getTestNodeBind = search_params => {
    return spider_axios.request({
        url: '/spider/env_mgt/test_node_bind_api',
        params: {
            app_name: search_params['app_name'],

            pageNum: 1,
            pageSize: 1000,
            pageTotal: 1000
        },
        method: 'get'
    })
}

export const getProdRegin = () => {
    return spider_axios.request({
        url: 'spider/env_mgt/get_prod_regin',
        params: {},
        method: 'get'
    })
}

export const getNameServer = env_id => {
    return spider_axios.request({
        url: 'spider/env_mgt/get_name_server',
        params: {
            env_id: env_id
        },
        method: 'get'
    })
}

export const getAppVersion = app_name => {
    return spider_axios.request({
        url: 'spider/env_mgt/get_app_version',
        params: {
            app_name: app_name
        },
        method: 'get'
    })
}

export const createRmqOrder = applyFormItem => {
    return spider_axios.request({
        url: 'spider/env_mgt/create_rmq_order',
        data: {
            applyFormItem: applyFormItem
        },
        method: 'post'
    })
}

export const getAppProdSuites = module_name => {
    return spider_axios.request({
        url: 'spider/env_mgt/get_app_prod_suites',
        params: {
            module_name: module_name
        },
        method: 'get'
    })
}
// 获取

export const getRmqOrderInfo = data => {
    return spider_axios.request({
        url: 'spider/env_mgt/get_rmq_order_info',
        params: {
            data: data
        },
        method: 'get'
    })
}
// 获取测试环境节点绑定信息 zt@2020-08-19
export const testPublishNodeList = post_params => {
    return spider_axios.request({
        url: '/spider/pipeline/test_publish_node_list',
        data: {
            pipeline_id: post_params['pipeline_id'],
            app_name: post_params['app_name'],
            br_name: post_params['br_name'],
            node_list: post_params['node_list']
        },
        method: 'post'
    })
}

// 历史制品多环境批量推送 zt@2020-09-02
export const pipelineTestPublishSuiteApi = post_params => {
    return spider_axios.request({
        url: '/spider/pipeline/pipeline_test_publish_suite',
        data: {
            pipeline_id: post_params['pipeline_id'],
            app_name: post_params['app_name'],
            br_name: post_params['br_name'],
            suite_list: post_params['suite_list'],
            node_list: post_params['node_list'],
            jacoco_type: post_params['jacoco_type'],
            mock_type: post_params['mock_type'],
            api_type: post_params['api_type'],
            arex_type: post_params['arex_type'],
            pinpoint_type: post_params['pinpoint_type'],
            ptp_type: 'H'
        },
        method: 'post'
    })
}

export const getAgentUseInfo = (suite_code, app_name, agent_type) => {
    return spider_axios.request({
        url: '/spider/pipeline/get_agent_use_info',
        params: {
            suite_code: suite_code,
            app_name: app_name,
            agent_type: agent_type
        },
        method: 'get'
    })
}

export const recordProdApply = (action_item, request_data) => {
    return spider_axios.request({
        url: '/spider/iter_mgt/record_prod_apply',
        data: {
            action_item: action_item,
            request_data: request_data
        },
        method: 'post'
    })
}
