import spider_axios from '@/libs/spider_api.request'
// UAT
export const getUatPublishOrderInfo = (pipeline_id) => {
  return spider_axios.request({
    url: '/spider/publish/be_script_publish_order_uat_api',
    params: { 'pipeline_id': pipeline_id },
    method: 'get'
  })
}

export const uatPublishDeploy = (pipeline_id) => {
  return spider_axios.request({
    url: '/spider/publish/be_script_publish_order_uat_deploy',
    params: { 'pipeline_id': pipeline_id },
    method: 'get'
  })
}

export const uatPublishReboot = (pipeline_id) => {
  return spider_axios.request({
    url: '/spider/publish/be_script_publish_order_uat_reboot',
    params: { 'pipeline_id': pipeline_id },
    method: 'get'
  })
}

// PRO
export const getProPublishOrderInfo = (pipeline_id) => {
  return spider_axios.request({
    url: '/spider/publish/be_script_publish_order_pro_api',
    params: { 'pipeline_id': pipeline_id },
    method: 'get'
  })
}

export const proPublishDeploy = (pipeline_id) => {
  return spider_axios.request({
    url: '/spider/publish/be_script_publish_order_pro_deploy',
    params: { 'pipeline_id': pipeline_id },
    method: 'get'
  })
}

export const proPublishReboot = (pipeline_id) => {
  return spider_axios.request({
    url: '/spider/publish/be_script_publish_order_pro_reboot',
    params: { 'pipeline_id': pipeline_id },
    method: 'get'
  })
}
