import spider_axios from '@/libs/spider_api.request'

// 添加应用
export const addAppInfo = (req) => {
  return spider_axios.request({
    url: 'spider/app_mgt/app_register',
    method: 'post',
    data: req,
  })
}

// 解析应用
export const parseAppInfo = (req) => {
  return spider_axios.request({
    url: 'spider/app_mgt/app_register',
    method: 'put',
    data: req,
  })
}

export const getAppInfo = () => {
  return spider_axios.request({
    url: 'spider/app_mgt/app_register',
    method: 'get',
    params: {},
  })
}


export const getProductReposApi = (req) => {
  // 获取一个gitlab组下的 仓库信息
  return spider_axios.request({
    url: 'spider/iter_mgt/product_repos_api',
    method: 'get',
    params: {
      'status': req['status'],
      'page_data': req['page_data'],
      'app_name': req['app_name'],
      'br_name': req['br_name'],
    },
  })
}


// 获取「测试环境发布模板」列表 zt@2020-08-17
export const getTemplateInfo = () => {
  return spider_axios.request({
    url: '/spider/app_mgt/template_info_api',
    params: {'template_is_active': 1},
    method: 'get'
  })
}
