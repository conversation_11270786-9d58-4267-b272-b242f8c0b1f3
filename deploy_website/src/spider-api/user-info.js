import spider_axios from '@/libs/spider_api.request'
import store from '../store'


/**
 * 获取是不是beta用户

 * @returns {*|AxiosPromise<any>|HttpOptions|void}
 */
// export const getIsBetaUserApi = () => {
//   return spider_axios.request({
//     url: 'spider/user/is_beta_user_api/',
//     method: 'get',
//
//   })
// }


/**
 * 获取是不是QA

 * @returns {*|AxiosPromise<any>|HttpOptions|void}
 */
export const getIsQaApi = () => {
  return spider_axios.request({
    url: 'spider/user/is_qa_api/',
    method: 'get',

  })
}
