import spider_axios from '@/libs/spider_api.request'
import store from '../store'
import log from "@/view/ops-service/log/log";


export const createIter = (req) => {
    // 返回一个promise对象
    return spider_axios.request({
        url: 'spider/iter_mgt/iter_apply_api',
        method: 'post',

        data: {
            'branch_name': req['branch_name'],
            'branch_type': req['branch_type'],
            'is_update_out_dep': req['is_update_out_dep'],
            'deadline': req['deadline'],
            'gitlab_group': req['gitlab_group'],
            'desc': req['desc'],
            'tapd_id': req['tapd_id'],
            'repos_str': req['repos_str'],
        },
    })
}

export const createConfigBranch = (req) => {
    // 返回一个promise对象
    return spider_axios.request({
        url: 'spider/task_mgt/create_config_branch_api/',
        method: 'post',

        data: {
            'branch_name': req['branch_name'],
            'desc': req['desc'],
            'repos_str': req['repos_str'],
        },
    })
}

export const getCreateIterStatusApi = (req) => {
    // 返回一个promise对象
    return spider_axios.request({
        url: 'spider/iter_mgt/iter_apply_status_api',
        method: 'get',
        params: {
            'sid': req['sid'],
        },

    })
}
export const getIterApplyTapdIdApi = (params) => {
    // 返回一个promise对象
    return spider_axios.request({
        url: 'spider/iter_mgt/iter_apply_tapd_id_api',
        method: 'get',
        params: params,

    })
}

export const get_breach_info_for_apply = (params) => {
    // 返回一个promise对象
    return spider_axios.request({
        url: 'spider/iter_mgt/iter_apply_api',
        method: 'get',
        params: params,
    })
}


export const syncAppBranchInfo = () => {
    // 返回一个promise对象
    return spider_axios.request({
        url: 'spider/task_mgt/sync_app_branch_to_nacos_api/',
        method: 'get',
    })
}

export const testDataDevMgtBranch = (req) => {
    // 返回一个promise对象
    return spider_axios.request({
        url: 'spider/iter_mgt/test_data_dev_mgt_branch',
        method: 'post',

        data: {
            'biz_test_iter_br': req['biz_test_iter_br'],
            'biz_code': req['biz_code'],
            'biz_base_db_set': req['biz_base_db_set']
        }
    })
}

export const getTestDataDevBranchInfo = () => {
    // 返回一个promise对象
    return spider_axios.request({
        url: 'spider/iter_mgt/test_data_dev_mgt_branch',
        method: 'get',
    })
}

export const getTestDataDevBranchDetailInfo = (bis_pipeline_id) => {
    // 返回一个promise对象
    return spider_axios.request({
        url: 'spider/iter_mgt/test_data_dev_mgt_branch_detail',
        method: 'get',
        params: {
            'bis_pipeline_id': bis_pipeline_id,
        },
    })
}

export const getIterBisLabInfo = (bis_pipeline_id) => {
    // 返回一个promise对象
    return spider_axios.request({
        url: 'spider/iter_mgt/get_iter_bis_lab_info',
        method: 'get',
        params: {
            'bis_pipeline_id': bis_pipeline_id,
        },
    })
}

export const check_iter_app_had_archive = (bis_pipeline_id) => {
  // 返回一个promise对象
  return spider_axios.request({
    url: 'spider/iter_mgt/test_sql_archive_api',
    method: 'get',
    params: {
      'bis_pipeline_id': bis_pipeline_id,
    },
  })
}

export const testDataDevMgtBranchArchive = (bis_pipeline_id) => {
    // 返回一个promise对象
    return spider_axios.request({
        url: 'spider/iter_mgt/test_sql_archive_api',
        method: 'post',

        data: {
            'sql_branch_name': bis_pipeline_id,
            'bis_type': ''
        }
    })
}

export const testDataMakeDump = (bis_pipeline_id, suite_code) => {
  // 返回一个promise对象
  return spider_axios.request({
    url: 'spider/iter_mgt/make_dump',
    method: 'post',

    data: {
      'bis_pipeline_id': bis_pipeline_id,
      'suite_code': suite_code
    }
  })
}

export const getCanMakeDump = (bis_pipeline_id) => {
  // 返回一个promise对象
  return spider_axios.request({
    url: 'spider/iter_mgt/make_dump',
    method: 'get',
    params: {
      'bis_pipeline_id': bis_pipeline_id
    },
  })
}

export const testDataDevMgtBranchCommit = (job_name, bis_pipeline_id) => {
    // 返回一个promise对象
    return spider_axios.request({
        url: 'spider/db_mgt/db_commit_diff_sql_pipeline/',
        method: 'post',
        data: {
            'job_name': job_name,
            'bis_pipeline_id': bis_pipeline_id,
        }
    })
}

export const get_latest_diff_recode = ( bis_pipeline_id) => {
  // 返回一个promise对象
  return spider_axios.request({
    url: 'spider/db_mgt/db_commit_diff_sql_pipeline/get_latest_diff_recode/',
    method: 'get',
    params: {
      'bis_pipeline_id': bis_pipeline_id,
    },
  })
}

export const testDataDevMgtBranchVerify = (formValidate) => {
    // 返回一个promise对象
    return spider_axios.request({
        url: 'spider/db_mgt/test_data_dev_mgt_branch_verify/',
        method: 'get',
        params: {
            'biz_test_iter_br': formValidate['biz_test_iter_br'],
            'biz_code': formValidate['biz_code']
        }
    })
}
