import spider_axios from '@/libs/spider_api.request'

export const getNodeInfo = (search_params) => {
  return spider_axios.request({
    url: '/spider/env_mgt/env_mgt_node_api',
    params: {
      'module_name': search_params['module_name'],
      'node_ip': search_params['node_ip'],
      'suite_code': search_params['suite_code'],
      'node_status': search_params['node_status'],
      "pageNum":search_params['pageNum'],
      "pageSize":search_params['pageSize'],
      "pageTotal":search_params['pageTotal'],
    },
    method: 'get'
  })
}

export const getNodeApply = (search_params) => {
  return spider_axios.request({
    url: '/spider/env_mgt/node_apply_api',
    params: {
      'module_name': search_params['module_name'],
      'order_code': search_params['order_code'],

      "pageNum":search_params['pageNum'],
      "pageSize":search_params['pageSize'],
      "pageTotal":search_params['pageTotal'],
    },
    method: 'get'
  })
}

export const getRecycleOrderApi = (search_params) => {
  return spider_axios.request({
    url: 'spider/env_mgt/recycle_node_apply_api',
    params: {
      'recycle_order_code': search_params['recycle_order_code'],

      "pageNum":search_params['pageNum'],
      "pageSize":search_params['pageSize'],
     // "pageTotal":search_params['pageTotal'],
    },
    method: 'get'
  })
}


export const getNodeZone = (search_params) => {
  return spider_axios.request({
    url: '/spider/env_mgt/node_zone_api',
    params: {
      'region_name': search_params['region_name'],
    },
    method: 'get'
  })
}

export const getNodeVm = (search_params) => {
  return spider_axios.request({
    url: '/spider/env_mgt/node_vm_api',
    params: {

    },
    method: 'get'
  })
}

export const getZoneVm = (search_params) => {
  return spider_axios.request({
    url: '/spider/env_mgt/zone_vm_api',
    params: {
      'zone_code': search_params['zone_code'],
    },
    method: 'get'
  })
}

export const createNodeApply = (nodeApplyForm) => {
  return spider_axios.request({
    url: '/spider/env_mgt/node_apply_api',
    data: {
      'order_code': nodeApplyForm['order_code'],
      'module_name': nodeApplyForm['module_name'],
      'module_code': nodeApplyForm['module_code'],

      'team_name': nodeApplyForm['team_name'],
      'team_short_name': nodeApplyForm['team_short_name'],
      'cmdb_team_name': nodeApplyForm['cmdb_team_name'],

      'region_name': nodeApplyForm['region_name'],
      'region_id': nodeApplyForm['region_id'],
      'addr_name': nodeApplyForm['addr_name'],
      'type_name': nodeApplyForm['type_name'],

      'suite_code': nodeApplyForm['suite_code'],

      'zone_id': nodeApplyForm['zone_id'],
      'zone_code': nodeApplyForm['zone_code'],

      'cmdb_provider_code': nodeApplyForm['cmdb_provider_code'],
      'cmdb_region_code': nodeApplyForm['cmdb_region_code'],
      'cmdb_zone_code': nodeApplyForm['cmdb_zone_code'],
      'cmdb_env_code': nodeApplyForm['cmdb_env_code'],
      'bread_domain_code': nodeApplyForm['bread_domain_code'],

      'vm_id': nodeApplyForm['vm_id'],
      'vm_code': nodeApplyForm['vm_code'],
      'vm_count': nodeApplyForm['vm_count'],
      'apply_reason': nodeApplyForm['apply_reason'],

      'vm_cpu': nodeApplyForm['vm_cpu'],
      'vm_memory': nodeApplyForm['vm_memory'],
      'vm_disk': nodeApplyForm['vm_disk'],
      'vm_network': nodeApplyForm['vm_network'],
      'vm_os': nodeApplyForm['vm_os'],
      'vm_desc': nodeApplyForm['vm_desc'],
    },
    method: 'post'
  })
}
export const createRecycleNodeApply = (recyclenodeApplyForm) => {
  return spider_axios.request({
    url: 'spider/env_mgt/recycle_node_apply_api',
    data: {
      'node_name': recyclenodeApplyForm['node_name'],
      'node_ip': recyclenodeApplyForm['node_ip'],
      'module_name': recyclenodeApplyForm['module_name'],
      'valid_at': recyclenodeApplyForm['valid_at'],
      'recycle_order_reason': recyclenodeApplyForm['recycle_order_reason'],
      'recycle_order_desc':recyclenodeApplyForm['recycle_order_desc']

    },
    method: 'post'
  })
}

export const editNodeBind = (nodebinginfo) => {
  return spider_axios.request({
    url: 'spider/env_mgt/node_bind',
    data: {
      'node_name': nodebinginfo['node_name'],
      'node_ip': nodebinginfo['node_ip'],
      'module_name': nodebinginfo['module_name'],
      'suite_code': nodebinginfo['suite_code'],
      'editnodebind':nodebinginfo['editnodebind'],
      'node_minion_id':nodebinginfo['node_minion_id'],
      'node_port_edit':nodebinginfo['node_port_edit'],
      'bind_id':nodebinginfo['bind_id'],
      'deploy_type':nodebinginfo['deploy_type'],
      'node_docker':nodebinginfo['node_docker']
    },
    method: 'post'
  })
}

export const createNodeBind = (nodebinginfo) => {
  return spider_axios.request({
    url: 'spider/env_mgt/node_bind_create',
    data: {
      'node_name': nodebinginfo['node_name_create'],
      'node_ip': nodebinginfo['node_ip_create'],
      'module_name': nodebinginfo['module_name_create'],
      'suite_code': nodebinginfo['suite_code_create'],
      'minion_id':nodebinginfo['minion_id_create'],
      'createnodebind':nodebinginfo['createnodebind'],
      'node_port':nodebinginfo['node_port_create'],
      'node_docker':nodebinginfo['node_docker_create'],
    },
    method: 'post'
  })
}

export const getcreateNodeBind = (node_ip) => {
  return spider_axios.request({
    url: 'spider/env_mgt/node_bind_create',
    params: { 'node_ip': node_ip },
    method: 'get'
  })
}

export const getaccessstatus = (params) => {
  return spider_axios.request({
    url: 'spider/env_mgt/node_bind',
    params: {
      'access_status': params['access_status'],
    },
    method: 'get'
  })
}

// export const getRegionZone = (search_params) => {
//   return spider_axios.request({
//     url: '/spider/env_mgt/region_zone_api',
//     params: {
//       'region_code': search_params['region_code'],
//
//       "pageNum":1,
//       "pageSize":100,
//       "pageTotal":100,
//     },
//     method: 'get'
//   })
// }

export const getRegionZone = (search_params) => {
  return spider_axios.request({
    url: '/spider/env_mgt/new_region_zone_api',
    params: {
      'region_code': search_params['region_code'],
      'zone_is_active': search_params['zone_is_active']
    },
    method: 'get'
  })
}

// export const getLogInfo = (node_bind_id) => {
//   return spider_axios.request({
//     url: '/spider/publish/log_info_bind_api',
//     params: {
//       'node_bind_id': node_bind_id,
//     },
//     method: 'get'
//   })
// }
//
//
// export const createLogInfo = (node_bind_id,log_info) => {
//   return spider_axios.request({
//     url: 'spider/publish/log_info_bind_api',
//     data: {
//       'node_bind_id': node_bind_id,
//       'log_info': log_info,
//     },
//     method: 'post'
//   })
// }

export const getVmCountAndDisk = ()=>{
  return spider_axios.request({
    url:'spider/env_mgt/get_vm_count_and_disk',
    method:'get',
  })
}

export const zoneVmApi = (zone_code)=>{
  return spider_axios.request({
    url:'spider/env_mgt/new_zone_vm_api',
    params:{
      'zone_code':zone_code
    },
    method:'get',
  })
}

export const envNodeMgtRegionZoneApi = (region_code)=>{
  return spider_axios.request({
    url:'spider/env_mgt/new_region_zone_api',
    params:{
      'region_code': region_code
    },
    method:'get',
  })
}


export const saveNodeApplyOrder = (data)=>{
    return spider_axios.request({
    url:'spider/env_mgt/save_node_apply_order/',
    data:data,
    method:'post',
  })
}

export const GetOpsPermission = () => {
  return spider_axios.request({
    url: 'spider/env_mgt/get_ops_permission',
    params: {

    },
    method: 'get'
  })
}


export const opsOperateNodeApplyOrder = (operate_type,order) => {
  return spider_axios.request({
    url: 'spider/env_mgt/ops_operate_node_apply/',
    data: {
      'operate_type': operate_type,
      'order': order,
    },
    method: 'post'
  })
}

export const NodeApply = (order_code) => {
  return spider_axios.request({
    url: 'spider/env_mgt/node_apply/',
    data: {
      'order_code': order_code,
    },
    method: 'post'
  })
}

export const getRecycleNodeInfo = () =>{
  return spider_axios.request({
    url: 'spider/env_mgt/get_recycle_node_info',
    params: {

    },
    method: 'get'
  })
}

export const saveNodeRecycleOrder = (recycleOrderCreateForm) =>{
  return spider_axios.request({
    url: 'spider/env_mgt/save_node_recycle_order/',
    data: {
      'data': recycleOrderCreateForm,
    },
    method: 'post'
  })
}

export const GetNodeRecycleOrderList = (search_params) =>{
  return spider_axios.request({
    url: 'spider/env_mgt/get_node_recycle_order_info',
    params: {
      'recycle_batch_code': search_params['recycle_batch_code'],
      "pageNum":search_params['pageNum'],
      "pageSize":search_params['pageSize'],
    },
    method: 'get'
  })
}

export const NodeRecycle = (recycle_batch_code) =>{
  return spider_axios.request({
    url: 'spider/env_mgt/node_recycle/',
    data: {
      'recycle_batch_code': recycle_batch_code,
    },
    method: 'post'
  })
}

export const CancelNodeRecycleBeforeApprove = (recycle_batch_code) =>{
  return spider_axios.request({
    url: 'spider/env_mgt/cancel_node_recycle_before_approve/',
    data: {
      'recycle_batch_code': recycle_batch_code,
    },
    method: 'post'
  })
}

export const CancelNodeRecycleAfterApprove = (recycle_order_code) =>{
  return spider_axios.request({
    url: 'spider/env_mgt/cancel_node_recycle_after_approve/',
    data: {
      'recycle_order_code': recycle_order_code,
    },
    method: 'post'
  })
}

export const RejectNodeRecycleByAppOps = (recycle_batch_code,recycle_reject_reason) =>{
  return spider_axios.request({
    url: 'spider/env_mgt/reject_node_recycle_by_app_ops/',
    data: {
      "recycle_batch_code": recycle_batch_code,
    "recycle_reject_reason": recycle_reject_reason
    },
    method: 'post'
  })
}
