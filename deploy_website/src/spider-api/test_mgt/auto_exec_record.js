/*
 * @Author: chao.bao <EMAIL>
 * @Date: 2025-04-24 14:25:51
 * @LastEditors: chao.bao <EMAIL>
 * @LastEditTime: 2025-04-29 13:57:55
 * @FilePath: /website_web/deploy_website/src/spider-api/test_mgt/auto_exec_record.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import spider_axios from '@/libs/spider_api.request'

/**
 * 获取环境列表
 * @param {Object} params - 查询参数
 * @returns {Promise} 返回Promise对象
 */
export const getAllTestingEnv = (params = {}) => {
    return spider_axios.request({
        url: '/spider/test_env_mgt/get_all_testing_env',
        method: 'get',
        params
    })
}

/**
 * 获取业务列表
 * @returns {Promise} 返回Promise对象
 */
export const getBizNameList = () => {
    return spider_axios.request({
        url: 'spider/biz_mgt/get_biz_name_list/',
        method: 'get'
    })
}

/**
 * 获取业务分支列表
 * @param {Object} params - 查询参数
 * @param {string} params.biz_code - 业务代码
 * @returns {Promise} 返回Promise对象
 */
export const getBizBranchList = params => {
    return spider_axios.request({
        url: 'spider/biz_mgt/get_biz_test_iter_list/',
        method: 'get',
        params
    })
}

/**
 * 获取自动化执行记录列表
 * @param {Object} params - 查询参数
 * @param {string} params.bizName - 业务名称
 * @param {string} params.branch - 分支
 * @param {string} params.env - 环境
 * @param {string} params.startDate - 开始日期
 * @param {string} params.endDate - 结束日期
 * @param {string} params.executor - 执行人
 * @param {string} params.batchNo - 批次号
 * @param {string} params.status1 - 状态1
 * @param {string} params.status2 - 状态2
 * @param {number} params.pageNum - 页码
 * @param {number} params.pageSize - 每页条数
 * @returns {Promise} 返回Promise对象
 */
export const getAutoTestRecordList = (params = {}) => {
    return spider_axios.request({
        // url: 'spider/test_mgt/get_auto_test_record',
        url: 'spider/biz_mgt/get_auto_exec_record',
        method: 'get',
        params
    })
}
