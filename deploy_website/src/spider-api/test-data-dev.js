import spider_axios from '@/libs/spider_api.request'

export const getTestDataDevStatusApi = (param) => {
  // 返回一个promise对象
  return spider_axios.request({
    url: 'spider/biz_mgt/get_test_data_dev_status',
    method: 'get',
    params: param
  })
}

export const updateTestDataDevStatusApi = (param) => {
  return spider_axios.request({
    url: 'spider/biz_mgt/update_test_data_dev_status/',
    method: 'post',
    data: param
  })
}

export const testSqlArchiveCheckApi = (param) => {
  // 返回一个promise对象
  return spider_axios.request({
    url: 'spider/iter_mgt/test_sql_archive_check/',
    method: 'get',
    params: param
  })
}
