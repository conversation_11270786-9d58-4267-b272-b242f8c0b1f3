import spider_axios from '@/libs/spider_api.request'

/**
 * 建立h5 dist 应用和 nf新框架的绑定关系
 * @param req
 * @returns {*|AxiosPromise<any>|HttpOptions|void}
 */
export const createH5AppBindNfAppApi = (req) => {
  return spider_axios.request({
    url: 'spider/ci_cd_mgt/h5/h5_app_bind_nf_app_api/',
    method: 'post',
    data: {
      iteration_id: req.iteration_id,
      app_name: req.app_name,
      nf_br_name: req.nf_br_name,
      nf_app_name: req.nf_app_name,
      stage:req.stage
    },
  })
}


/**
 * 获取h5 dist 应用和 nf新框架的绑定关系
 * @param req
 * @returns {*|AxiosPromise<any>|HttpOptions|void}
 */
export const getH5AppBindNfAppApi = (req) => {
  return spider_axios.request({
    url: 'spider/ci_cd_mgt/h5/h5_app_bind_nf_app_api/',
    method: 'get',
    params: {
      iteration_id: req.iteration_id,
      app_name: req.app_name,
      suite_code:req.suite_code
    },
  })
}

export const getH5NfAppStatuApi = (req) => {
  return spider_axios.request({
    url: 'spider/ci_cd_mgt/h5/h5_nf_app_status_api/',
    method: 'get',
    params: {
      iteration_id: req.iteration_id,
      app_name: req.app_name,
    },
  })
}

export const h5PublishApplyStatus = (iteration_id,app_name) => {
  return spider_axios.request({
    url: 'spider/ci_cd_mgt/h5/h5_publish_apply_status/',
    method: 'get',
    params: {
      iteration_id: iteration_id,
      app_name: app_name,
    },
  })
}

/**
 * 归档时查询是否需要填写理由的接口
 */
export const getNeedReasonNodeInfoApi = (params) => {
  return spider_axios.request({
    url: 'spider/iter_mgt/publish_reason/get_need_reason_node_info',
    method: 'get',
    params: params,
  })
}

/**
 * 写入原因接口
 */
export const createPublishReasonApi = (data) => {
  return spider_axios.request({
    url: 'spider/iter_mgt/publish_reason/create_publish_reason/',
    method: 'post',
    data: data,
  })
}
