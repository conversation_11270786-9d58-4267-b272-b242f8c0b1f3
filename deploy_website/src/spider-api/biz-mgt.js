import spider_axios from '@/libs/spider_api.request'

export const getBisNameLIstInfo = () => {
    // 返回一个promise对象
    return spider_axios.request({
        url: 'spider/biz_mgt/get_biz_name_list/',
        method: 'get'
    })
}

export const get_test_iter_list = params => {
    // 返回一个promise对象
    return spider_axios.request({
        url: 'spider/biz_mgt/get_test_iter_list/',
        method: 'get',
        params
    })
}
export const get_dump_file_list = params => {
    // 返回一个promise对象
    return spider_axios.request({
        url: 'spider/iter_mgt/get_dump_file',
        method: 'get',
        params
    })
}
export const post_dump_restore = params => {
    // 返回一个promise对象
    return spider_axios.request({
        url: 'spider/iter_mgt/dump_restore',
        method: 'post',
        data: params
    })
}
export const get_dump_restore = params => {
    // 返回一个promise对象
    return spider_axios.request({
        url: 'spider/iter_mgt/dump_restore',
        method: 'get',
        params
    })
}

export const get_using_test_iter_list = param => {
    // 返回一个promise对象
    return spider_axios.request({
        url: 'spider/biz_mgt/get_test_iter_list/',
        method: 'post',
        data: param
    })
}

export const get_dump_jenkins_url = bis_pipeline_id => {
    // 返回一个promise对象
    return spider_axios.request({
        url: 'spider/iter_mgt/get_dump_jenkins_url',
        method: 'get',
        params: {
            bis_pipeline_id: bis_pipeline_id
        }
    })
}
export const get_test_flow_list = param => {
    // 返回一个promise对象
    return spider_axios.request({
        url: 'spider/biz_mgt/get_test_flow_list/',
        method: 'get',
        params: param
    })
}

export const get_test_flow_name_list = param => {
    // 返回一个promise对象
    return spider_axios.request({
        url: 'spider/biz_mgt/get_test_flow_name_list/',
        method: 'get',
        params: param
    })
}
export const get_biz_app_list = param => {
    // 返回一个promise对象
    return spider_axios.request({
        url: 'spider/biz_mgt/get_biz_app_list/',
        method: 'get',
        params: param
    })
}

export const get_biz_test_iter_list = param => {
    // 返回一个promise对象
    return spider_axios.request({
        url: 'spider/biz_mgt/get_biz_test_iter_list/',
        method: 'get',
        params: param
    })
}

export const get_biz_base_db_list = param => {
    return spider_axios.request({
        url: 'spider/biz_mgt/get_base_db_bind_list/',
        method: 'get',
        params: param
    })
}
export const save_flow_schedule_config = param => {
    // 返回一个promise对象
    return spider_axios.request({
        url: 'spider/biz_mgt/flow_schedule_config/',
        method: 'post',
        data: param
    })
}

export const del_flow_schedule_config = param => {
    // 返回一个promise对象
    return spider_axios.request({
        url: 'spider/biz_mgt/flow_schedule_config/del/',
        method: 'post',
        data: param
    })
}

export const get_flow_schedule_config = param => {
    // 返回一个promise对象
    return spider_axios.request({
        url: 'spider/biz_mgt/flow_schedule_config/',
        method: 'get',
        params: param
    })
}

export const save_biz_rel_apps = param => {
    return spider_axios.request({
        url: 'spider/biz_mgt/get_biz_app_list/',
        method: 'post',
        data: param
    })
}

export const get_biz_relative_schedule = param => {
    // 返回一个promise对象
    return spider_axios.request({
        url: 'spider/biz_mgt/get_biz_relative_schedule/',
        method: 'get',
        params: param
    })
}

export const save_biz_pipeline = param => {
    // 返回一个promise对象
    return spider_axios.request({
        url: 'spider/biz_mgt/save_biz_pipeline/',
        method: 'post',
        data: param
    })
}

export const get_base_db_bind = biz_code => {
    // 返回一个promise对象
    return spider_axios.request({
        url: 'spider/biz_mgt/get_base_db_bind/',
        method: 'get',
        params: {
            biz_code: biz_code
        }
    })
}

export const execute_biz_pipeline = param => {
    // 返回一个promise对象
    return spider_axios.request({
        url: 'spider/biz_mgt/execute_biz_pipeline/',
        method: 'post',
        data: param
    })
}

export const execute_jenkins_composition = param => {
    // 返回一个promise对象
    return spider_axios.request({
        url: 'spider/jenkins_mgt/jenkins_composition/',
        method: 'post',
        data: param
    })
}
export const execute_history = param => {
    // 返回一个promise对象
    return spider_axios.request({
        url: 'spider/biz_mgt/execute_history/',
        method: 'post',
        data: param
    })
}

export const execute_history_retrieve = param => {
    // 返回一个promise对象
    return spider_axios.request({
        url: 'spider/biz_mgt/execute_history/retrieve',
        method: 'get',
        params: param
    })
}

export const execute_history_list = param => {
    // 返回一个promise对象
    return spider_axios.request({
        url: 'spider/biz_mgt/execute_history',
        method: 'get',
        params: param
    })
}

export const get_biz_pipeline = param => {
    // 返回一个promise对象
    return spider_axios.request({
        url: 'spider/biz_mgt/get_biz_pipeline',
        method: 'get',
        params: param
    })
}

export const get_biz_pipeline_detail = param => {
    // 返回一个promise对象
    return spider_axios.request({
        url: 'spider/biz_mgt/get_biz_pipeline_detail',
        method: 'get',
        params: param
    })
}

export const get_test_set_list = () => {
    return spider_axios.request({
        url: 'spider/biz_mgt/get_test_set_list',
        method: 'get'
    })
}
// 个人面板列表查询
export const getPersonList = param => {
    return spider_axios.request({
        url: 'spider/external_interaction/mantis_mgt/mantis_request_api/get_mantis_request/',
        data: param,
        method: 'post'
    })
}
export const submitDump = param => {
    return spider_axios.request({
        url: 'spider/iter_mgt/make_dump',
        data: param,
        method: 'post'
    })
}
export const getDumpDetail = param => {
    return spider_axios.request({
        url: 'spider/iter_mgt/make_dump',
        params: param,
        method: 'get'
    })
}
export const submitArchDump = param => {
    return spider_axios.request({
        url: 'spider/iter_mgt/archive_make_dump',
        data: param,
        method: 'post'
    })
}
export const getArchDumpDetail = param => {
    return spider_axios.request({
        url: 'spider/iter_mgt/archive_make_dump',
        params: param,
        method: 'get'
    })
}
export const getLastUser = param => {
    return spider_axios.request({
        url: 'spider/db_mgt/db_iter_mgt_pipeline/',
        params: param,
        method: 'get'
    })
}
export const submitDumpRestore = param => {
    return spider_axios.request({
        url: 'spider/iter_mgt/dump_restore',
        data: param,
        method: 'post'
    })
}
// 待办列表查询
export const getTodoList = param => {
    return spider_axios.request({
        url: 'spider/publish/get_user_todo_list/',
        params: param,
        method: 'get'
    })
}
// 待办列表详情
export const getTodoDetail = param => {
    return spider_axios.request({
        url: 'spider/iter_mgt/publish_reason/get_rollback_detail_by_pid/',
        params: param,
        method: 'get'
    })
}
// 待办保存
export const submitTodo = param => {
    return spider_axios.request({
        url: 'spider/iter_mgt/publish_reason/create_rollback_reason/',
        data: param,
        method: 'post'
    })
}
export const getPersonDetailList = param => {
    return spider_axios.request({
        url: 'spider/external_interaction/mantis_mgt/mantis_request_api/get_mantis_request/',
        data: param,
        method: 'post'
    })
}
export const getComprehensive = param => {
    return spider_axios.request({
        url: 'spider/external_interaction/mantis_mgt/mantis_request_api/get_mantis_request/',
        data: param,
        method: 'post'
    })
}
export const get_app_list_by_iteration_id = param => {
    return spider_axios.request({
        url: 'spider/iter_mgt/get_app_list_by_iteration_id/',
        data: param,
        method: 'post'
    })
}
export const get_iteration_id_list = param => {
    return spider_axios.request({
        url: 'spider/iter_mgt/get_iteration_id_list/',
        data: param,
        method: 'post'
    })
}

// 删除业务流水线编排
export const delete_biz_pipeline_flow = param => {
    return spider_axios.request({
        url: 'spider/biz_mgt/delete_biz_pipeline_flow/',
        data: param,
        method: 'post'
    })
}
