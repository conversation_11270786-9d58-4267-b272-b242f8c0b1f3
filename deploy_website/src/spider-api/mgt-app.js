import spider_axios from '@/libs/spider_api.request'

export const getAppInfo = (pipeline_id) => {
  return spider_axios.request({
    url: '/spider/app_mgt/app_mgt_api',
    params: { 'pipeline_id': pipeline_id },
    method: 'get'
  })
}

export const getAppModule = (module_name) => {
  return spider_axios.request({
    url: '/spider/app_mgt/app_module_api',
    params: { 'module_name': module_name },
    method: 'get'
  })
}

export const get_app_info_detail = (app_name,suite_code) => {
  return spider_axios.request({
    url: '/spider/db_mgt/get_app_branch_and_db_info',
    params: { 'app_name': app_name ,
  'suite_code':suite_code},
    method: 'get'
  })
}

export const save_quick_sql_info = (sql_info_dict) => {
  return spider_axios.request({
    url: '/spider/db_mgt/quick_sql_iter_api/',
    data: {
      'app_name': sql_info_dict['app_name'],
      'pipeline_id': sql_info_dict['pipeline_id'],
      'db_name': sql_info_dict['db_name'],
      'sql_dict': sql_info_dict['sql_dict'],
      'db_type': sql_info_dict['db_type'],
    },
    method: 'post'
  })
}

export const commit_quick_sql = (sql_info_dict) => {
  return spider_axios.request({
    url: '/spider/db_mgt/quick_sql_api/',
    data: {
      'app_name': sql_info_dict['app_name'],
      'db_name': sql_info_dict['db_name'],
      'branch_name': sql_info_dict['branch_name'],
      'suite_code':sql_info_dict['suite_code'],
      'db_type': sql_info_dict['db_type'],
    },
    method: 'post'
  })
}

export const get_app_sql_suite_info = (app_name,region_group) => {
  return spider_axios.request({
    url: '/spider/db_mgt/app_sql_suite_info',
    params: { 'app_name': app_name,
              'region_group':region_group
  },
    method: 'get'
  })
}

export const get_app_sql_branch_list = (app_name) => {
  return spider_axios.request({
    url: '/spider/db_mgt/get_sql_br_name',
    params: { 'app_name': app_name },
    method: 'get'
  })
}

export const get_app_sql_info_list = (app_name, sql_branch_name) => {
  return spider_axios.request({
    url: '/spider/db_mgt/get_sql_detail_info',
    params: {
      'app_name': app_name,
      'sql_branch_name': sql_branch_name
    },
    method: 'get'
  })
}

export const archive_sql_iter = (archive_info) => {
  return spider_axios.request({
    url: '/spider/db_mgt/quick_sql_iter_archive/',
    data: {
      'business_name': archive_info['business_name'],
      'app_name': archive_info['app_name'],
      'branch_name': archive_info['branch_name'],
      'sql_dict': archive_info['sql_dict']
    },
    method: 'post'
  })
}

export const editAppModule = (EditAppInfo) => {
  return spider_axios.request({
    url: 'spider/app_mgt/app_mgt_api',
    data: {
      'module_name':EditAppInfo['module_name'],
      'need_online':EditAppInfo['need_online'],
      'need_check':EditAppInfo['need_check'],
      'lib_repo':EditAppInfo['lib_repo'],
      'container_name':EditAppInfo['container_name'],
      'module_jdk_version':EditAppInfo['module_jdk_version'],
      'app_port':EditAppInfo['app_port'],
      'create_path':EditAppInfo['create_path'],
      'deploy_path':EditAppInfo['deploy_path'],
      'url':EditAppInfo['url'],
      'platform_type':EditAppInfo['platform_type'],
      'path':EditAppInfo['path'],
      'package_name':EditAppInfo['package_name'],
      'team_name':EditAppInfo['team_name']
    },
    method: 'post'
  })
}

export const getEditAppInfo = (module_name) => {
  return spider_axios.request({
    url: '/spider/app_mgt/app_mgt_api',
    params: {
      'module_name': search_params['module_name'],
      // 'node_ip': search_params['node_ip'],
      'suite_code': search_params['suite_code'],
      'node_status': search_params['node_status'],
      "pageNum":search_params['pageNum'],
      "pageSize":search_params['pageSize'],
      "pageTotal":search_params['pageTotal'],
    },
    method: 'get'
  })
}
