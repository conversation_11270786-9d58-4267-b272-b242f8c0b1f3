import store from '@/store'
import spider_axios from '@/libs/spider_api.request'

// UAT申请的应用
export const getUatPublishOrderInfo = (pipeline_id) => {
  return spider_axios.request({
    url: '/spider/publish/publish_uat_api',
    params: { 'pipeline_id': pipeline_id },
    method: 'get'
  })
}

// UAT操作
export const uatPublishOperate = (app_name, ip, op_type) => {
  const info = new URLSearchParams()
  info.append('app_name', app_name)
  info.append('op_type', op_type)
  info.append('ip', ip)

  return spider_axios.request({
    url: '/spider/publish/uat_operate',
    data: info,
    method: 'post'
  })
}

// UAT操作历史
export const uatPublishInfo = (app_name, ip) => {
  return spider_axios.request({
    url: '/spider/publish/uat_operate',
    params: {
      'app_name': app_name,
      'ip': ip
    },
    method: 'get'
  })
}

// PROD节点是否可以回滚
export const getProdRollbackStat = (app_name, ip) => {
  return spider_axios.request({
    url: '/spider/publish/prod_rollback_stat',
    params: { 'app_name': app_name, 'ip': ip },
    method: 'get'
  })
}

// PRO申请的应用
export const getProPublishOrderInfo = (pipeline_id) => {
  return spider_axios.request({
    url: '/spider/publish/publish_pro_api',
    params: { 'pipeline_id': pipeline_id },
    method: 'get'
  })
}

// PRO操作
export const proPublishOperate = (pipeline_id, app_name, ip, op_type) => {
  const info = new URLSearchParams()
  info.append('pipeline_id', pipeline_id)
  info.append('app_name', app_name)
  info.append('op_type', op_type)
  info.append('ip', ip)

  return spider_axios.request({
    url: '/spider/publish/pro_operate',
    data: info,
    method: 'post'
  })
}

// PRO操作历史
export const proPublishInfo = (app_name) => {
  return spider_axios.request({
    url: '/spider/publish/pro_operate',
    params: {
      'app_name': app_name
    },
    method: 'get'
  })
}

// 新的产线操作历史记录
export const getPublishHistory = (app_name, iteration_id, region_group) => {
  return spider_axios.request({
    url: '/spider/publish/get_publish_history',
    params: {
      'app_name': app_name,
      'iteration_id': iteration_id,
      'region_group': region_group
    },
    method: 'get'
  })
}

// HD申请的应用
export const getHdPublishOrderInfo = (pipeline_id) => {
  return spider_axios.request({
    url: '/spider/publish/publish_hd_api',
    params: { 'pipeline_id': pipeline_id },
    method: 'get'
  })
}

// HD操作
export const hdPublishOperate = (app_name, ip, op_type) => {
  const info = new URLSearchParams()
  info.append('app_name', app_name)
  info.append('op_type', op_type)
  info.append('ip', ip)

  return spider_axios.request({
    url: '/spider/publish/hd_operate',
    data: info,
    method: 'post'
  })
}

// HD操作历史
export const hdPublishInfo = (app_name) => {
  return spider_axios.request({
    url: '/spider/publish/hd_operate',
    params: {
      'app_name': app_name
    },
    method: 'get'
  })
}

// 获取确认状态
export const iterConfirmStatusApi = (iteration_id) => {
  return spider_axios.request({
    url: '/spider/publish/iter_confirm_status_api',
    params: {
      'iteration_id': iteration_id
    },
    method: 'get'
  })
}

// 获取发布许可
export const iterPublishAllowApi = (iteration_id) => {
  return spider_axios.request({
    url: '/spider/publish/iter_publish_allow_api',
    params: {
      'iteration_id': iteration_id
    },
    method: 'get'
  })
}
/**
 * 移动端 获取确认状态
 * @param iteration_id
 * @returns {*|AxiosPromise<any>|HttpOptions|void}
 */
export const h5IterConfirmStatusApi = (iteration_id, suite_code) => {
  return spider_axios.request({
    url: '/spider/publish/h5_iter_confirm_status_api',
    params: {
      'iteration_id': iteration_id,
      'suite_code': suite_code
    },
    method: 'get'
  })
}

/**
 * 安卓 推包
 * @param iteration_id
 * @returns {*|AxiosPromise<any>|HttpOptions|void}
 */
export const android_push_package = (params) => {
  return spider_axios.request({
    url: '/spider/publish/android_push_package/',
    method: 'post',
    data: params
  })
}

export const android_push = (android_app_name, android_iter_id, android_suite_code) => {
    return spider_axios.request({
        url: '/spider/publish/android_push_package/',
        data: {
            "app_name": android_app_name,
            "iteration_id": android_iter_id,
            "suite_code": android_suite_code,
        },
        method: 'post',
    })
}

/**
 * 安卓 发布检查
 * @param iteration_id
 * @returns {*|AxiosPromise<any>|HttpOptions|void}
 */
export const android_publish_check_api = (params) => {
  return spider_axios.request({
    url: '/spider/publish/android_publish_check_api/',
    method: 'post',
    data: params
  })
}

export const android_check = (android_app_name, android_iter_id, android_suite_code) => {
    return spider_axios.request({
        url: '/spider/publish/android_publish_check_api/',
        data: {
            "app_name": android_app_name,
            "iteration_id": android_iter_id,
            "suite_code": android_suite_code,
        },
        method: 'post',
    })
}

// 产线发布检查
export const AppPublishCheckApi = (iteration_id, app_name, env) => {
  return spider_axios.request({
    url: '/spider/publish/app_publish_check_api',
    params: {
      'iteration_id': iteration_id,
      'app_name': app_name,
      'env': env
    },
    method: 'get'
  })
}

// 产线应用列表发布检查
export const AppListPublishCheckApi = (iteration_id, app_name_list, env) => {
  return spider_axios.request({
    url: '/spider/task_mgt/app_list_publish_check_api',
    params: {
      'iteration_id': iteration_id,
      'app_name_list': app_name_list,
      'env': env
    },
    method: 'get'
  })
}
