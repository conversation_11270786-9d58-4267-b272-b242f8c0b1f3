import spider_axios from '@/libs/spider_api.request'

/**
 * 获取发布状态接口
 */
export const getGroupPublishAppStatusApi = params => {
    return spider_axios.request({
        url: 'spider/iter_mgt/group_publish/get_group_publish_app_status',
        params: params,
        method: 'get'
    })
}

/**
 * 获取Jenkins地址接口
 */
export const getJenkinsUrlApi = params => {
    return spider_axios.request({
        url: 'spider/iter_mgt/group_publish/get_jenkins_url_by_iteration_id/',
        params: params,
        method: 'get'
    })
}

/**
 * 发布提交接口
 */
export const publishSubmitApi = data => {
    return spider_axios.request({
        url: 'spider/publish/group_publish_pipeline_api/',
        data: data,
        method: 'post'
    })
}

/**
 * 发布查询接口
 */
export const getPublishInfoApi = params => {
    return spider_axios.request({
        url: 'spider/iter_mgt/group_publish/get_group_publish_detail/',
        data: params,
        method: 'post'
    })
}

/**
 * 发布配置保存接口
 */
export const savePublishConfigApi = data => {
    return spider_axios.request({
        url: 'spider/iter_mgt/group_publish/save_group_publish_mode/',
        data: data,
        method: 'post'
    })
}

/**
 * 根据选择的环境获取当前环境下的应用信息
 */
export const getPublishAppListByEnvApi = params => {
    return spider_axios.request({
        url: 'spider/iter_mgt/group_publish/get_group_publish_app_list/',
        params: params,
        method: 'get'
    })
}

/**
 * 请求查询当前环境列表
 */
export const getEnvListApi = params => {
    return spider_axios
        .request({
            url: 'spider/iter_mgt/group_publish/get_suite_by_iter_id',
            params: params,
            method: 'get'
        })
        .then(res => {
            if (res.data.status === 'success') {
                return res.data.data.map(item => {
                    return {
                        label: item.suite_code,
                        value: item.suite_code
                    }
                })
            } else {
                return []
            }
        })
        .catch(ex => console.log(ex))
}

export const getIterPublishAppInfoApi = iter_id => {
    return spider_axios.request({
        url: 'spider/iter_mgt/get_iter_publish_app_info_api',
        params: {
            iter_id: iter_id
        },
        method: 'get'
    })
}

export const getPublishAppBindApi = (region_group, app_name, iteration_id) => {
    return spider_axios.request({
        url: '/spider/publish/get_publish_app_bind_info',
        params: {
            region_group: region_group,
            app_name: app_name,
            iteration_id: iteration_id
        },
        method: 'get'
    })
}

export const getIpListBySuiteCode = (app_name, suite_code_list) => {
    return spider_axios.request({
        url: '/spider/publish/get_ip_list_by_suite_code',
        params: {
            suite_code_list: suite_code_list,
            app_name: app_name
        },
        method: 'get'
    })
}

export const getPublishAppBindApiByRegionGroupList = (region_group_list, app_name) => {
    return spider_axios.request({
        url: '/spider/publish/get_publish_app_bind_info_by_region_group_list',
        params: {
            region_group_list: region_group_list,
            app_name: app_name
        },
        method: 'get'
    })
}

export const getPublishStatusApi = (iteration_id, app_name) => {
    let params = {
        iteration_id: iteration_id,
        app_name: app_name
    }

    return spider_axios.request({
        url: '/spider/publish/get_publish_status_by_app_name',
        params: params,
        method: 'get'
    })
}

export const getProdMultiNodeOperateInfoApi = (iteration_id, app_name, node_list, region_group) => {
    let params = {
        iteration_id: iteration_id,
        app_name: app_name,
        node_list: node_list,
        region_group: region_group
    }
    return spider_axios.request({
        url: '/spider/publish/multi_node_operate_api',
        params: params,
        method: 'get'
    })
}

export const getModuleName = appSearch => {
    return spider_axios.request({
        url: '/spider/publish_mgt/get_salt_cmd_app_name',
        params: { module_name: appSearch },
        method: 'get'
    })
}

export const getNoPermissionModuleName = appSearch => {
    return spider_axios.request({
        url: '/spider/publish_mgt/get_app_name_list',
        params: { module_name: appSearch },
        method: 'get'
    })
}

export const getPublishExecSaltCmdApi = (app_name, bind_id) => {
    return spider_axios.request({
        url: '/spider/publish_mgt/publish_exec_salt_cmd_api',
        params: { app_name: app_name, bind_id: bind_id },
        method: 'get'
    })
}

export const publishExecSaltCmdApi = data => {
    return spider_axios.request({
        url: '/spider/publish_mgt/publish_exec_salt_cmd_api/',
        data: data,
        method: 'post'
    })
}

export const getPublishAppListApi = () => {
    return spider_axios.request({
        url: '/spider/publish_mgt/get_publish_app_list',
        method: 'get'
    })
}

export const getAppInterfaceApi = (app_name, branch_name, interface_path, interface_method, interface_type) => {
    return spider_axios.request({
        url: 'spider/app_mgt/app_interface_api',
        params: {
            app_name: app_name,
            branch_name: branch_name,
            interface_path: interface_path,
            interface_method: interface_method,
            interface_type: interface_type
        },
        method: 'get'
    })
}

export const getAppBranchExistInterfaceApi = () => {
    return spider_axios.request({
        url: 'spider/app_mgt/get_app_branch_exist_interface_api',
        method: 'get'
    })
}

export const getAppBranchInfoApi = app_name => {
    return spider_axios.request({
        url: 'spider/app_mgt/app_branch_info_api',
        params: { module_name: app_name },
        method: 'get'
    })
}

export const getInterfaceParams = (app_name, branch_name, interface_path, interface_method, interface_type) => {
    return spider_axios.request({
        url: '/spider/app_mgt/get_interface_params',
        params: {
            app_name: app_name,
            branch_name: branch_name,
            interface_path: interface_path,
            interface_method: interface_method,
            interface_type: interface_type
        },
        method: 'get'
    })
}

export const updateOrCreateInterfaceParams = data => {
    return spider_axios.request({
        url: 'spider/app_mgt/update_or_create_interface_params',
        data: data,
        method: 'post'
    })
}

export const updateInterfaceName = data => {
    return spider_axios.request({
        url: 'spider/app_mgt/update_interface_name',
        data: data,
        method: 'post'
    })
}

export const getBatchDeployLastStatusApi = (iteration_id, app_name) => {
    return spider_axios.request({
        url: 'spider/publish/get_batch_deploy_last_status',
        params: {
            iteration_id: iteration_id,
            app_name: app_name
        },
        method: 'get'
    })
}
// bugfix，交易时段是否当天首次发布
export const checkFirstPublish = params => {
    return spider_axios.request({
        url: '/spider/iter_mgt/group_publish/check_first_publish/',
        params,
        method: 'get'
    })
}
export const queryPublishDetail = params => {
    return spider_axios.request({
        url: '/spider/iter_mgt/group_publish/get_group_publish_app_status/',
        params,
        method: 'get'
    })
}

// 发起回滚信息准备请求
export const postRollback = data => {
    return spider_axios.request({
        url: '/spider/iter_mgt/rollback_api',
        data,
        method: 'post'
    })
}
// 回滚过程结束请求
export const postEndRollback = data => {
    return spider_axios.request({
        url: '/spider/iter_mgt/end_rollback_api',
        data,
        method: 'post'
    })
}
// 获取当前应用的已经回滚的信息
export const getCurRollInfo = params => {
    return spider_axios.request({
        url: '/spider/publish/get_cur_day_rollback_info',
        params,
        method: 'get'
    })
}
// 查询宙斯版本间配置差异接口
export const postZeusConfigDiff = data => {
    return spider_axios.request({
        url: '/spider/publish/zeus_config_diff/',
        data,
        method: 'post'
    })
}
export const get_auto_test_result = params => {
    return spider_axios.request({
        url: '/spider/iter_mgt/group_publish/get_auto_test_result/',
        params,
        method: 'get'
    })
}
