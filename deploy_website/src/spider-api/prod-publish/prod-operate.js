/*
 * @Author: chao.bao <EMAIL>
 * @Date: 2024-09-27 17:59:27
 * @LastEditors: chao.bao <EMAIL>
 * @LastEditTime: 2024-11-01 09:17:03
 * @FilePath: /website_web/deploy_website/src/spider-api/prod-publish/prod-operate.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import spider_axios from '@/libs/spider_api.request'

export const prodMultiNodeOperateApi = data => {
    // 返回一个promise对象
    return spider_axios.request({
        url: 'spider/publish/multi_node_operate_api/',
        method: 'post',

        data: data
    })
}

export const prodMultiNodePublishPipelineApi = data => {
    // 返回一个promise对象
    return spider_axios.request({
        url: 'spider/publish/multi_node_publish_pipeline_api/',
        method: 'post',

        data: data
    })
}

export const jenkinsPipelinePublishInfo = (app_name, iteration_id) => {
    // 返回一个promise对象
    return spider_axios.request({
        url: 'spider/publish/jenkins_pipeline_publish_info/',
        method: 'get',
        params: {
            app_name: app_name,
            iteration_id: iteration_id
        }
    })
}

export const GetServiceStatus = (app_name, node_ip, suite_code) => {
    return spider_axios.request({
        url: 'spider/publish/get_service_status/',
        method: 'get',
        params: {
            app_name: app_name,
            node_ip: node_ip,
            suite_code: suite_code
        }
    })
}

export const BatchPublishAccessStatus = app_name => {
    return spider_axios.request({
        url: 'spider/publish/batch_publish_access_status/',
        method: 'get',
        params: {
            app_name: app_name
        }
    })
}

/**
 * 写入原因接口
 */
export const createPublishReasonApi = data => {
    return spider_axios.request({
        url: 'spider/iter_mgt/publish_reason/create_publish_reason/',
        method: 'post',
        data: data
    })
}
export const createPublishReasonNewApi = data => {
    return spider_axios.request({
        url: 'spider/publish/create_rollback_todo_list/',
        method: 'post',
        data: data
    })
}

export const RollBackInfo = (ip, app_name, suite_code, iteration_id) => {
    return spider_axios.request({
        url: 'spider/publish/roll_back_info/',
        method: 'get',
        params: {
            ip: ip,
            app_name: app_name,
            suite_code: suite_code,
            iteration_id: iteration_id
        }
    })
}

export const GetBindInfoBySuite = (app_name, suite_code) => {
    return spider_axios.request({
        url: 'spider/publish/get_bind_info_by_suite/',
        method: 'get',
        params: {
            app_name: app_name,
            suite_code: suite_code
        }
    })
}
