import spider_axios from '@/libs/spider_api.request'
import store from '../store'

/**
 * h5编译
 * @param req
 * @returns {*|AxiosPromise<any>|HttpOptions|void}
 */
export const h5CompileApi = req => {
    return spider_axios.request({
        url: 'spider/ci_cd_mgt/h5/h5_compile_api/',
        method: 'post',
        data: req
    })
}

/**
 * 测试环境 h5发布
 * @param req
 * @returns {*|AxiosPromise<any>|HttpOptions|void}
 */
export const h5TestPublishApi = req => {
    return spider_axios.request({
        url: 'spider/ci_cd_mgt/h5/h5_test_publish_api/',
        method: 'post',
        data: req
    })
}
export const h5TestPublishApiCommon = req => {
    return spider_axios.request({
        // url: 'spider/ci_cd_mgt/h5/h5_remote_test_publish_api/',
        url: 'spider/ci_cd_mgt/h5/h5_remote_test_beta_prod_compile_api/',
        method: 'post',
        data: req
    })
}

/**
 * 小程序发布
 * @param req
 * @returns {*|AxiosPromise<any>|HttpOptions|void}
 */
export const mobileMiniPublishApi = req => {
    return spider_axios.request({
        url: 'spider/ci_cd_mgt/h5/h5_mini_compile_api/',
        method: 'post',
        data: req
    })
}

/**
 * 停止jenkins
 * @param req
 * @returns {*|AxiosPromise<any>|HttpOptions|void}
 */
export const h5PipelineStop = req => {
    return spider_axios.request({
        url: 'spider/ci_cd_mgt/h5/h5_pipeline_stop/',
        method: 'post',
        data: req
    })
}

/**
 * 判断jenkins 是否已经开始运行
 * @param req
 * @returns {*|AxiosPromise<any>|HttpOptions|void}
 */
export const h5PipelineIsRunning = req => {
    return spider_axios.request({
        url: 'spider/ci_cd_mgt/h5/h5_pipeline_stop/',
        method: 'get',
        params: {
            iteration_id: req.iteration_id,
            app_name: req.app_name,
            status: req.status
        }
    })
}

/**
 * 测试环境全部数据接口
 * @param req
 * @returns {*|AxiosPromise<any>|HttpOptions|void}
 */
export const h5TestPublishApiGet = req => {
    return spider_axios.request({
        url: 'spider/ci_cd_mgt/h5/h5_test_publish_api/',
        method: 'get',
        params: {
            iteration_id: req
        }
    })
}

/**
 * 编译 + 发布
 * @param req
 * @returns {*|AxiosPromise<any>|HttpOptions|void}
 */
export const h5CiPipelineApi = req => {
    return spider_axios.request({
        url: 'spider/ci_cd_mgt/h5/h5_ci_pipeline_api/',
        method: 'post',
        data: req
    })
}

/**
 * 测试环境 用于查询状态
 * @param req
 * @returns {*|AxiosPromise<any>|HttpOptions|void}
 */
export const h5CiPipelineApiGet = req => {
    return spider_axios.request({
        url: 'spider/ci_cd_mgt/h5/h5_ci_pipeline_api/',
        method: 'get',
        params: {
            iteration_id: req
        }
    })
}

/**
 * 生产环境 h5发布
 * @param req
 * @returns {*|AxiosPromise<any>|HttpOptions|void}
 */
export const h5ProdPublishApi = req => {
    return spider_axios.request({
        url: 'spider/publish/publish_operate',
        method: 'post',
        data: req
    })
}

/**
 * h5发布状态查询接口
 * @param req
 * @returns {*|AxiosPromise<any>|HttpOptions|void}
 */
export const h5ProPublishStateApi = req => {
    return spider_axios.request({
        url: 'spider/publish/publish_operate',
        method: 'get',
        params: {
            iteration_id: req
        }
    })
}

export const mobile_branch_status = req => {
    return spider_axios.request({
        url: 'spider/ci_cd_mgt/h5/mobile_branch_status',
        method: 'get',
        params: {
            iteration_id: req
        }
    })
}

/**
 * 产线发布的环境查询
 * @param req
 * @returns {*|AxiosPromise<any>|HttpOptions|void}
 */
export const h5ProPublishEnv = req => {
    return spider_axios.request({
        url: 'spider/publish/publish_2_env_api',
        method: 'get',
        params: {
            iteration_id: req.iteration_id,
            app_name_list: req.app_name_list
        }
    })
}

export const h5HdPublishEnv = req => {
    return spider_axios.request({
        url: 'spider/publish/publish_2_env_api',
        method: 'get',
        params: {
            iteration_id: req.iteration_id,
            app_name_list: req.app_name_list,
            env: req.env
        }
    })
}

/**
 * 查看发布信息 环境和状态
 * @param req
 * @returns {*|AxiosPromise<any>|HttpOptions|void}
 */
export const h5ProdPublishInfo = req => {
    return spider_axios.request({
        url: 'spider/publish/publish_info_api',
        method: 'get',
        params: {
            iteration_id: req
        }
    })
}

/**
 * h5 灰度发布
 * @param req
 * @returns {*|AxiosPromise<any>|HttpOptions|void}
 */
export const h5HdPublishApi = req => {
    return spider_axios.request({
        url: 'spider/publish/hd_publish_operate',
        method: 'post',
        data: req
    })
}
export const mobileAppPublishApi = req => {
    return spider_axios.request({
        url: 'spider/publish/mobileAppPublishApi',
        method: 'post',
        data: req
    })
}

/**
 * h5灰度发布状态查询接口
 * @param req
 * @returns {*|AxiosPromise<any>|HttpOptions|void}
 */
export const h5HdPublishStateApi = req => {
    return spider_axios.request({
        url: 'spider/publish/hd_publish_operate',
        method: 'get',
        params: {
            iteration_id: req
        }
    })
}

/**
 * 查看灰度发布信息 环境和状态
 * @param req
 * @returns {*|AxiosPromise<any>|HttpOptions|void}
 */
export const h5HdPublishInfo = req => {
    return spider_axios.request({
        url: 'spider/publish/hd_publish_info_api',
        method: 'get',
        params: {
            iteration_id: req
        }
    })
}

/**
 * 查看可发布的环境
 * @param req
 * @returns {*|AxiosPromise<any>|HttpOptions|void}
 */
export const getOnlineRepoDiff = req => {
    return spider_axios.request({
        url: 'spider/git_iterative/get_online_repo_diff',
        method: 'get',
        params: {
            app_name: req.app_name,
            iteration_id: req.iteration_id,
            suite_code: req.suite_code
        }
    })
}

/**
 * 编译 检测一阶段
 * @param req
 * @returns {*|AxiosPromise<any>|HttpOptions|void}
 */
export const compileCheckApi = req => {
    return spider_axios.request({
        url: 'spider/task_mgt/compile_check_api/v1/',
        method: 'get',
        params: {
            iteration_id: req.iteration_id,
            app_name_list: req.app_name_list
        }
    })
}

/**
 * 测试发布 & 灰度申请 检测一阶段
 * @param req
 * @returns {*|AxiosPromise<any>|HttpOptions|void}
 */
export const testPublishCheckApi = req => {
    return spider_axios.request({
        url: 'spider/task_mgt/test_publish_check_api',
        method: 'get',
        params: {
            iteration_id: req.iteration_id,
            app_name_list: req.app_name_list
        }
    })
}

/**
 * 产线申请 (不支持环境套) 检测一阶段
 * @param req
 * @returns {*|AxiosPromise<any>|HttpOptions|void}
 */
export const applyCheck = req => {
    return spider_axios.request({
        url: 'spider/iter_mgt/apply_check',
        method: 'post',
        data: {
            pipeline_id: req.iteration_id,
            app_name_list: req.app_name_list,
            env: req.env
        }
    })
}

/**
 * 产线发布 (不支持环境套，不支持多应用) 检测一阶段
 * @param req
 * @returns {*|AxiosPromise<any>|HttpOptions|void}
 */
export const appPublishCheckApi = req => {
    return spider_axios.request({
        url: 'spider/publish/app_publish_check_api',
        method: 'get',
        data: {
            iteration_id: req.iteration_id,
            app_name_list: req.app_name_list
        }
    })
}

/**
 * 编译和发布之前执行的检测二阶段
 * @param req
 * @returns {*|AxiosPromise<any>|HttpOptions|void}
 */
export const externalServiceResult = req => {
    return spider_axios.request({
        url: 'spider/task_mgt/external_service_result',
        method: 'get',
        params: {
            sid: req
        }
    })
}

/**
 * 申请发布页面查询接口
 * @param req
 * @returns {*|AxiosPromise<any>|HttpOptions|void}
 */
export const h5PublishApplyApiGet = (iteration_id, suite_code = '', branch_code = '') => {
    return spider_axios.request({
        url: 'spider/ci_cd_mgt/h5/h5_publish_apply_api',
        method: 'get',
        params: {
            iteration_id: iteration_id,
            suite_code: suite_code,
            branch_code: branch_code
        }
    })
}

/**
 * 申请按钮
 * @param req
 * @returns {*|AxiosPromise<any>|HttpOptions|void}
 */
export const h5PublishApplyApi = req => {
    return spider_axios.request({
        url: 'spider/ci_cd_mgt/h5/h5_publish_apply_api/',
        method: 'post',
        data: req
    })
}

/**
 * 申请按钮
 * @param req
 * @returns {*|AxiosPromise<any>|HttpOptions|void}
 */
export const miniPublishApplyApi = req => {
    return spider_axios.request({
        url: 'spider/ci_cd_mgt/h5/mini_publish_apply_api/',
        method: 'post',
        data: req
    })
}

export const NoticeApplyByWechat = (iteration_id, env) => {
    return spider_axios.request({
        url: 'spider/ci_cd_mgt/h5/notice_apply_by_wechat/',
        method: 'get',
        params: { iteration_id: iteration_id, env: env }
    })
}

/**
 * 生产发布
 * @param req
 * @returns {*|AxiosPromise<any>|HttpOptions|void}
 * @constructor
 */
export const PublishOperate = req => {
    return spider_axios.request({
        url: 'spider/publish/publish_operate',
        method: 'post',
        data: {
            op_type: req.op_type,
            app_name: req.app_name,
            ip: req.ip
        }
    })
}

/**
 * 环境
 * @param req
 * @returns {*|AxiosPromise<any>|HttpOptions|void}
 */
export const appSuiteCodeApi = req => {
    return spider_axios.request({
        url: 'spider/ci_cd_mgt/h5/app_suite_code_api',
        method: 'get',
        params: {
            iteration_id: req
        }
    })
}

export const platformSuiteCodeApi = req => {
    return spider_axios.request({
        url: 'spider/ci_cd_mgt/h5/platform_suite_code_api',
        method: 'get',
        params: {
            iteration_id: req
        }
    })
}
/**
 * 下载
 * @param req
 * @returns {*|AxiosPromise<any>|HttpOptions|void}
 */
export const getResourceInfo = req => {
    return spider_axios.request({
        url: 'spider/publish/get_resource_info',
        method: 'get',
        params: {
            app_name: req.app_name,
            iteration_id: req.iteration_id,
            suite_code: req.suite_code
        }
    })
}

/**
 * 用户行为插入
 * action_item:记录操作的唯一键
 * action_value:操作瞬间的页面数据，为了查询时方便，尽可能全一点 Object
 * @param req
 * @returns {*|AxiosPromise<any>|HttpOptions|void}
 */
export const userAction = req => {
    return spider_axios.request({
        url: 'spider/user/action/',
        method: 'post',
        data: {
            action_item: req.action_item,
            action_value: req.action_value
        }
    })
}

/**
 * 用户行为查询
 * @param req
 * @returns {*|AxiosPromise<any>|HttpOptions|void}
 */
export const userActionGet = req => {
    return spider_axios.request({
        url: 'spider/user/action',
        method: 'get',
        params: {
            action_item: req
        }
    })
}

/**
 * 环境绑定用的用户行为查询 --> 不关联用户，只是用一用用户行为记录表罢了
 * @param req
 * @returns {*|AxiosPromise<any>|HttpOptions|void}
 */
export const userActionEnvGet = req => {
    return spider_axios.request({
        url: 'spider/user/actionEnv',
        method: 'get',
        params: {
            action_item: req
        }
    })
}

export const userLatestActionEnvGet = req => {
    return spider_axios.request({
        url: 'spider/user/latestActionEnv',
        method: 'get',
        params: {
            action_item: req
        }
    })
}

export const pipelineEnvBindGet = iter_id => {
    return spider_axios.request({
        url: 'spider/ci_cd_mgt/h5/pipeline_env_bind',
        method: 'get',
        params: {
            iter_id: iter_id
        }
    })
}

export const pipelineEnvBind = req => {
    console.log(req)
    return spider_axios.request({
        url: 'spider/ci_cd_mgt/h5/pipeline_env_bind/',
        method: 'post',
        data: {
            data: req
        }
    })
}

/**
 * 分支归档页面查询
 * @param req
 * @returns {*|AxiosPromise<any>|HttpOptions|void}
 */
export const h5BranchFile = req => {
    console.log(req)
    return spider_axios.request({
        url: 'spider/ci_cd_mgt/h5/h5_branch_file',
        method: 'get',
        params: {
            iteration_id: req
        }
    })
}

/**
 * 检查有没有其他迭代在灰度
 * @param req
 * @returns
 */
export const HdStatusCheckApi = req => {
    return spider_axios.request({
        url: 'spider/ci_cd_mgt/h5/hd_status_check_api',
        method: 'get',
        params: {
            iteration_id: req.iteration_id,
            app_name_list: req.app_name_list
        }
    })
}

/**
 * 查询迭代分支和环境的最近一次申请详情
 * @param req
 * @returns
 */
export const getLastApplyInfoApi = req => {
    return spider_axios.request({
        url: 'spider/ci_cd_mgt/h5/get_last_apply_info_api',
        method: 'get',
        params: {
            branch_name: req.branch_version,
            suite_code: req.suite_code,
            group_name: req.group_name,
            tag_name: req.tag_name
        }
    })
}

/**
 * spider/piggy 起始版本&结束版本
 * @param req
 * @returns {*|AxiosPromise<any>|HttpOptions|void}
 * @constructor
 */
export const findDistVersion = req => {
    return spider_axios.request({
        url: 'spider/publish/find_dist_version',
        method: 'get',
        params: {
            iteration_id: req.iteration_id,
            br_name: req.br_name,
            app_name: req.app_name,
            suite_code: req.suite_code
        }
    })
}
export const findH5ZipVersionByEnv = (appName, env) => {
    return spider_axios.request({
        url: 'spider/ci_cd_mgt/h5/findH5ZipVersionByEnv',
        method: 'get',
        params: {
            app_name: appName,
            suite_code: env
        }
    })
}

export const findH5ZipVersionByEnvForPlatform = (h5PlatFormCode, env) => {
    return spider_axios.request({
        url: 'spider/ci_cd_mgt/h5/findH5ZipVersionByEnvForPlatform',
        method: 'get',
        params: {
            platform_code: h5PlatFormCode,
            suite_code: env
        }
    })
}

export const findH5HDDistVersionByEnv = (h5_platform_code, env, end_ver) => {
    return spider_axios.request({
        url: 'spider/ci_cd_mgt/h5/findH5HDDistVersionByEnv',
        method: 'get',
        params: {
            h5_platform_code: h5_platform_code,
            suite_code: env,
            end_ver: end_ver
        }
    })
}

export const findTestSuiteCodeOfH5Zip = () => {
    return spider_axios.request({
        url: 'spider/ci_cd_mgt/h5/findTestSuiteCodeOfH5Zip',
        method: 'get',
        params: {}
    })
}

export const get_env_h5_app_api = (appName, env) => {
    return spider_axios.request({
        url: 'spider/lib_repo_mgt/get_env_h5_app_api',
        method: 'get',
        params: {
            app_name: appName,
            suite_code: env
        }
    })
}

export const CheckH5ZipVersionWhetherPublish = params => {
    return spider_axios.request({
        url: 'spider/publish/CheckH5ZipVersionWhetherPublish',
        method: 'get',
        params: params
    })
}
/**
 * 客户端测试发布接口
 * @param req
 * @returns {*|void|never|AxiosPromise<any>|HttpOptions}
 */
export const appTestPublishApi = req => {
    return spider_axios.request({
        url: 'spider/ci_cd_mgt/h5/appTestPublishApi/',
        method: 'post',
        data: {
            data: req
        }
    })
}
/**
 * 客户端发布申请接口
 * @param req
 * @returns {*|void|never|AxiosPromise<any>|HttpOptions}
 */
export const appPublishApplyApi = req => {
    return spider_axios.request({
        url: 'spider/ci_cd_mgt/h5/appPublishApplyApi/',
        method: 'post',
        data: {
            data: req
        }
    })
}

/**
 * tag客户端测试发布接口
 * @param req
 * @returns {*|void|never|AxiosPromise<any>|HttpOptions}
 */
export const appTagTestPublishApi = req => {
    return spider_axios.request({
        url: 'spider/ci_cd_mgt/h5/app_tag_test_publish_api/',
        method: 'post',
        data: {
            data: req
        }
    })
}

/**
 * tag客户端发布申请接口
 * @param req
 * @returns {*|void|never|AxiosPromise<any>|HttpOptions}
 */
export const appTagPublishApplyApi = req => {
    return spider_axios.request({
        url: 'spider/ci_cd_mgt/h5/app_tag_publish_apply_api/',
        method: 'post',
        data: {
            data: req
        }
    })
}

export const getLastProdAndroidInfo = appName => {
    return spider_axios.request({
        url: 'spider/ci_cd_mgt/h5/get_last_prod_android_info/',
        method: 'get',
        params: {
            app_name: appName
        }
    })
}

export const getLastProdDistInfo = select_list => {
    return spider_axios.request({
        url: 'spider/ci_cd_mgt/h5/get_last_prod_dist_info',
        method: 'get',
        params: {
            select_list: select_list
        }
    })
}

export const getLastProdDistAppInfo = diff_app_list => {
    return spider_axios.request({
        url: 'spider/ci_cd_mgt/h5/get_last_prod_dist_app_info',
        method: 'get',
        params: {
            fund_h5_res: diff_app_list['fund-h5-res'],
            piggy_h5_res: diff_app_list['piggy-h5-res']
        }
    })
}

export const findPlatformDistVersion = req => {
    return spider_axios.request({
        url: 'spider/publish/find_platform_dist_version',
        method: 'get',
        params: {
            iteration_id: req.iteration_id,
            br_name: req.br_name,
            app_name: req.app_name,
            suite_code: req.suite_code
        }
    })
}

export const getTestPublishDistInfo = select_list => {
    return spider_axios.request({
        url: 'spider/ci_cd_mgt/h5/get_test_publish_dist_info',
        method: 'get',
        params: {
            select_list: select_list
        }
    })
}

/**
 * 错误信息显示时间
 * @returns {number}
 */
export const getErrNoticeShowTime = () => {
    return 10
}
/**
 * 成功信息显示时间
 * @returns {number}
 */
export const getSuccessNoticeShowTime = () => {
    return 3
}

/**
 * 常规信息显示时间
 * @returns {number}
 */
export const getInfoNoticeShowTime = () => {
    return 3
}
/**
 * 时间格式化
 */

export const formatDate = (date, format) => {
    let o = {
        'M+': date.getMonth() + 1, // 月份
        'd+': date.getDate(), // 日
        'h+': date.getHours(), // 小时
        'm+': date.getMinutes(), // 分
        's+': date.getSeconds(), // 秒
        S: date.getMilliseconds() // 毫秒
    }
    if (/(y+)/.test(format)) {
        format = format.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length))
    }
    for (var k in o) {
        if (new RegExp('(' + k + ')').test(format)) {
            format = format.replace(RegExp.$1, RegExp.$1.length === 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length))
        }
    }
    return format
}
