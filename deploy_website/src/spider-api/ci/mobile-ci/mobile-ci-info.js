import spider_axios from '@/libs/spider_api.request'


/**
 * 查询移动端的ci信息
 * @param req
 * @returns {*|AxiosPromise<any>|HttpOptions|void}
 */
export const GetMobileCiInfo = (req) => {
  return spider_axios.request({
    url: 'spider/ci_cd_mgt/h5/h5_jenkins_info_api/',
    method: 'get',
    params: req
  })
}

/**
 * 查询打包的版本信息
 * @param req
 * @returns {*|AxiosPromise<any>|HttpOptions|void}
 */
export const getPackageVersionApi = (req) => {
  return spider_axios.request({
    url: 'spider/lib_repo_mgt/get_package_version_api/',
    method: 'get',
    params: req
  })
}
