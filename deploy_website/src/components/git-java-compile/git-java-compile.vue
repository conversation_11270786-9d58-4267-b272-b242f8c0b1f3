<template>
  <card shadow style="height: 100%">
    <GitProcessHeader
      :buildHistory="buildHistory"
      ref="indexImportOrder"
      v-on:buildEdit="changeBuildEdit"
      v-on:toRunScript="toRunScript"
      v-on:changeCompileEnv="changeCompileEnv"
      :process="process"
      :current_status="current_status"
      :compile_env="compile_env"
    >
      <Tree
        :data="gitCompileTree"
        show-checkbox
        @on-check-change="getCompileAppList"
        ref="compilelist"
      ></Tree>
    </GitProcessHeader>

    <Row :gutter="24">
      <i-col span="24">
        <Collapse simple v-model="value1">
          <Panel name="1">
            编译详情
            <div slot="content">
              <Table stripe :columns="columns1" :data="execution_data"></Table>
            </div>
          </Panel>
        </Collapse>
      </i-col>
    </Row>
    <Modal v-model="notice_modal" width="60em">
      <span>
        <div style="font-size: 18px;color: red">{{notice}}</div>
      </span>

      <div class="demo-drawer-footer">
        <Button style="margin-right: 8px" type="primary" @click="to_archive">去归档</Button>
        <Button style="margin-right: 8px" @click="startCompile">继续编译</Button>
      </div>
    </Modal>
    <!--<Modal v-draggable="options" v-model="historyDetail" :title="detail_title">-->
    <!--<Table :columns="buildHistoryDetailColumns" :data="buildHistoryDetailData" class="buildHitoryModal"></Table>-->
    <!--</Modal>-->
  </card>
</template>

<script>
import { execHistory } from "@/api/data";
import GitProcessHeader from "_c/git-process-header";
import {
  gitcompileHandle,
  resetProcess,
  getCompileEditList,
  getGitCompileCheck
} from "@/api/java-compile";
import { getIterAppJdk } from "@/api/git-iterative";
import { initWebSocket, closeWebSocket } from "@/libs/web-socket";
import store from "../../store";
export default {
  name: "GitJavaCompile",
  components: {
    GitProcessHeader
  },
  data() {
    return {
      value1: "",
      socket: null,
      notice: "",
      notice_modal: false,
      appList: [],
      // 构建进度
      compile_process: 0,
      archive_pipelineid: "",
      // 构建按钮初始
      buildStatus: "构建",
      buildStatusBtn: "primary",
      buildStatusStyle: "marginRight",
      current_status_style:
        "font-size: 18px; font-weight: bold; color: #1ABB9C",
      // 构建历史
      buildHistoryData: [],
      historyDetail: false,
      options: {
        trigger: ".ivu-modal-body",
        body: ".ivu-modal",
        recover: true
      },
      detail_title: "",
      detail_content: "",
      // 编译列表
      compileList: [],
      // 编译详情table
      columns1: [
        {
          title: "应用名",
          key: "app"
        },
        {
          title: "构建状态",
          key: "execStatus"
        },
        {
          title: "JDK版本",
          key: "jdkVersion"
        },
      ],

      // 编译历史table
      buildHistoryDetailColumns: [
        {
          title: "应用名",
          key: "app"
        },
        {
          title: "构建状态",
          width: 250,
          align: "center",
          render: (h, params) => {
            if (params.row.execStatus === "错误日志") {
              return h("div", [
                h(
                  "Button",
                  {
                    props: {
                      type: "error",
                      size: "small",
                      ghost: true
                    },
                    style: {
                      width: "70px"
                    },
                    on: {
                      click: () => {
                        this.show(params.index);
                      }
                    }
                  },
                  params.row.execStatus
                )
              ]);
            } else {
              return h("div", [
                h(
                  "Button",
                  {
                    props: {
                      type: "primary",
                      size: "small",
                      ghost: true
                    },
                    style: {
                      width: "70px"
                    },
                    on: {
                      click: () => {
                        this.show(params.index);
                      }
                    }
                  },
                  params.row.execStatus
                )
              ]);
            }
          }
        }
      ],
      buildHistoryDetailData: [],

      buildEdit: false,
      styles: {
        height: "calc(100% - 55px)",
        overflow: "auto",
        paddingBottom: "53px",
        position: "static"
      },
      formData: {
        name: ""
      }
    };
  },
  props: [
    "gitCompileTree",
    "current_status",
    "process",
    "execution_data",
    "wb_data",
    "execDoneHandle",
    "exec_execution_data",
    "buildHistory",
    "compile_env",
    "changeTab"
  ],
  methods: {
    // 选择测试或灰度编译
    changeCompileEnv(env) {
      this.$emit('updateCompileEnv', env)
    },
    // 保存编译列表
    getCompileAppList() {
      let execution_data = [];
      let choiceAll = this.$refs.compilelist.getCheckedAndIndeterminateNodes();
      let compileList = [];

      choiceAll.forEach(item => {
        if (item.value) {
          let data = {
            'iterative_name': store.state.businessID,
            'app_name': item.value,
          }
          getIterAppJdk(data).then(res => {
            let jdkVersion = ''
            if (res.data.code === 0) {
              jdkVersion = res.data.data
            }
            execution_data.push({ app: item.value, execStatus: "等待", jdkVersion: jdkVersion });
            compileList.push(item.value);
          });
        }
      });
      this.compileList = compileList;
      //alert(execution_data)
      this.$emit("exec_execution_data", execution_data);
    },
    // 构建成功通知
    successNotice(title, nodesc) {
      this.$Notice.success({
        title: title,
        desc: nodesc ? "" : "你的操作成功，请查看",
        duration: 0
      });
    },
    // 构建失败通知
    errorNotice(title, nodesc) {
      this.$Notice.error({
        title: title,
        desc: nodesc ? "" : "你的操作失败，请查看",
        duration: 0
      });
    },

    // 查看build历史
    showBuildHistoryDetail: function(event) {
      this.historyDetail = true;
      let title = event.currentTarget.innerText.split(" ");
      let search_id = title[1];
      this.detail_title = "历史查询: " + "#" + search_id;
      this.buildHistoryDetailData = this.buildHistoryData[search_id].content;
    },

    // 父组件触发打开配置栏
    changeBuildEdit(val) {
      this.buildEdit = val;
    },

    // 配置校验
    checkConf() {
      let is_ok = true;
      if (this.compileList.length === 0) {
        is_ok = false;
      }
      return is_ok;
    },
    startCompile() {
      this.notice_modal = false;
      this.current_status = "忙碌中";
      gitcompileHandle(this.compileList, this.compile_env).then(res => {
        // 返回结果必须通过websocket进行广播，这里只能对请求的浏览器作出回应
      });
    },
    //跳转到归档页面
    to_archive() {
      this.notice_modal = false;
      store.commit("setBusinessID", this.archive_pipelineid);
      this.$emit("changeTab");

      //this.$router.push({name:"git_iteration_page",params:{"iterative_name":this.archive_pipelineid,"br_style":"jinji"}})
    },
    // 点击执行的处理
    toRunScript(val) {
      let is_ok = this.checkConf();
      // 构建
      if (val === true) {
        if (is_ok === true) {
          //打生产包进行校验
          if (this.compile_env == "prod") {
            this.current_status = "忙碌中";
            getGitCompileCheck().then(res => {
              // 返回结果必须通过websocket进行广播，这里只能对请求的浏览器作出回应
              if (res.data.status === "error") {
                alert(res.data.result);
                this.current_status = "空闲中";
              } else if (res.data.status === "judge") {
                this.current_status = "空闲中";
                this.archive_pipelineid = res.data.result;
                this.notice =
                  "请确认" +
                  res.data.result +
                  "迭代是否为上线后忘记归档，继续编译会导致上线代码丢失，请谨慎操作！！！";
                this.notice_modal = true;
              }
              //校验通过开始编译
              else if (res.data.status === "success") {
                this.startCompile();
              }
            });
          }
          //打测试包直接编译
          else {
            this.current_status = "忙碌中";
            this.startCompile();
          }
        } else {
          this.$Message.error("请选择编译列表");
        }
        // 重置
      } else {
        resetProcess(this.businessID).then(res => {
          console.log(res);
          this.$emit("execDoneHandle");
        });
      }
    }
  }
};
</script>

<style lang="less">
.count-style {
  font-size: 50px;
}

.marginRight {
  margin-right: 5px;
}

.marginBottom {
  margin-bottom: 10px;
}

.demo-drawer-footer {
  width: 100%;
  position: absolute;
  bottom: 0;
  left: 0;
  border-top: 1px solid #e8e8e8;
  padding: 10px 16px;
  text-align: right;
  background: #fff;
}
.circle {
  font-size: 14px;
}

.newslist ul li p {
  font-size: 14px;
  color: #555;
  line-height: 25px;
  height: 50px;
  overflow: hidden;
  transition: height 0.3s;
}

/* 构建历史特效 */

.hover:hover {
  background-color: #e4e7ea;
  text-decoration-line: underline;
}

/* 构建历史 模态框 */
.buildHitoryModal {
  max-height: 378px;
  overflow-y: auto;
  overflow-x: hidden;
  width: 100%;
}

/* 编译按钮闪烁效果 */
.build_blink {
  animation: changeshadow 2s ease-in infinite;
  /* 其它浏览器兼容性前缀 */
  -webkit-animation: changeshadow 2s linear infinite;
  -moz-animation: changeshadow 2s linear infinite;
  -ms-animation: changeshadow 2s linear infinite;
  -o-animation: changeshadow 2s linear infinite;
}
@keyframes changeshadow {
  0% {
    text-shadow: 0 0 1px red;
  }
}
/* 添加兼容性前缀 */
@-webkit-keyframes changeshadow {
  0% {
    text-shadow: 0 0 1px red;
  }
}
@-moz-keyframes changeshadow {
  0% {
    text-shadow: 0 0 1px red;
  }
}
@-ms-keyframes changeshadow {
  0% {
    text-shadow: 0 0 1px red;
  }
}
@-o-keyframes changeshadow {
  0% {
    text-shadow: 0 0 1px red;
  }
}
</style>
