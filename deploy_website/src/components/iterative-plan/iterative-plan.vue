<template>
  <!-- <Card shadow style="height: 600px"> -->
  <Card shadow>
    <h2 style="margin: 10px">{{ this.businessID }}</h2>

    <Row style="margin: 5px">
      <i-col style="margin: 5px">
        项目涉及人员:
        <Select placeholder="邮箱地址" style="width:100%; margin-top: 5px" v-model="allSelectedMails" filterable multiple>
          <Option v-for="item in allFilterMails"
                  :value="item"
                  :label="item"
                  :key="item">
            {{ item }}</Option>
        </Select>
      </i-col>
      <i-col style="margin-top: 10px; margin-left: 5px">
        <Button ghost icon="md-create" @click="saveEmail" type='info'>保存列表</Button>
        &nbsp;
        <Button ghost icon="md-mail" @click="sendMergeEmail" type='primary'>分支合并申请</Button>
        &nbsp;
        <Button ghost icon="md-mail" @click="sendUatEmail" type='primary'>仿真申请</Button>
        &nbsp;
        <Button ghost icon="md-mail" @click="sendProdEmail" type='primary'>产线申请</Button>
        &nbsp;
        <Button ghost icon="md-mail" @click="sendFinishEmail" type='success'>上线完成</Button>
      </i-col>
    </Row>
    <Button style="margin-left: 2ex" type="dashed" @click="show_img">流程图 <Icon type="ios-alert-outline" size="18"/> </Button>
    <span style=" text-align: left; display: inline-block;">（产线pom线下提交流程说明）</span>
    <!-- <Button type="primary" @click="show_img">查看流程说明</Button> -->
    <Modal
      title=" "
      width="1000"
      v-model="show_img_val"
      :mask-closable="true">
      <div style="width: 100%">
      <img :src="imgUrl0" style="width: 100%"/>
      </div>
    </Modal>
    <Divider />
    <Tag>预计上线时间</Tag>
    <Row style="margin: 5px">
      <i-col style="margin: 5px">
        <DatePicker type="date" v-model="date" placeholder="Select date" style="width: 200px"></DatePicker>
        &nbsp;
        <TimePicker format="HH:mm" v-model="time" placeholder="Select time" style="width: 112px"></TimePicker>
      </i-col>
    </Row>
    <Tag>功能描述</Tag>
    <Row style="margin: 5px">
      <i-col style="margin: 5px">
        <Input type="textarea" placeholder="功能描述" v-model="release_description" />
      </i-col>
    </Row>
    <Tag>上线应用</Tag>
      <Tag><span style="color: gray">应用：待上线</span></Tag>
      <Tag><span style="color: green">应用：已上线</span></Tag>
      <Tag><span style="color: gray">配置：无变更(点击可修改)</span></Tag>
      <Tag><span style="color: orange">配置：有变更</span></Tag>
    <Row style="margin: 5px">
      <i-col style="margin: 5px">
        <Table :columns="app_columns" :data="app_detail"></Table>
      </i-col>
    </Row>
    <Tag>SQL</Tag>
    <Tag style="color: blue">{{ sql_status }}</Tag>
    <Row style="margin: 5px">
      <i-col style="margin: 5px">
        <Input type="textarea" placeholder="SQL内容" v-model="sql_content" />
      </i-col>
    </Row>
    <Tag>注意事项</Tag>
    <Row style="margin: 5px">
      <i-col style="margin: 5px">
        <Input type="textarea" :autosize="{minRows: 2,maxRows: 5}" placeholder="注意事项" v-model="release_notice" />
      </i-col>
    </Row>
    <Row>
      <i-col>
        <Button @click="savePlan" style="margin: 1px" type='primary'>保存变更</Button>
      </i-col>
    </Row>
    <Modal v-model="modal_git">
      <p slot="header" style="color:gray;">
        <Icon type="md-clipboard"></Icon>
        <span> 版本差异 </span>
      </p>
      <p style="color: darkblue; display:inline;">{{ m_git_diff }}</p>
      <div slot="footer">
        <Button @click="cancel">关闭</Button>
      </div>
    </Modal>
    <Modal v-model="modal_config">
      <p slot="header" style="color:gray;">
        <Icon type="md-clipboard"></Icon>
        <span> 配置变更 </span>
      </p>
      <Input style="margin: 5px;" v-model="m_app_config" size="large" type='textarea' autosize />
      <div slot="footer">
        <Button type="primary" @click="configSave">保存</Button>
        <Button @click="cancel">关闭</Button>
      </div>
    </Modal>
    <Modal
      v-model="email_modal"
      width="60em"
      @on-cancel="cancelEmail"
      @on-ok="sendEmail">
      <p slot="header">
        <span v-if="stage==='merge'"> 确认发送 分支合并申请 邮件 </span>
        <span v-if="stage==='uat'"> 确认发送 仿真申请 邮件 </span>
        <span v-if="stage==='prod'"> 确认发送 产线申请 邮件 </span>
      </p>
      <span v-html="mail_content"></span>
    </Modal>
    <Modal
      v-model="finish_modal"
      width="60em"
      @on-cancel="cancelEmail"
      @on-ok="sendEmail">
      <p slot="header">上线完成确认</p>
      <Table :columns="finish_table_columns" :data="finish_table_data"></Table>
    </Modal>
  </Card>
</template>

<script>
import CommonIcon from '_c/common-icon'
import {
  getIterativePlan, editIterativePlan, getGitDiff, setAppConfig,
  getEmailAddresses, sendEmailNotify, getEmailPreview, saveEmailAddress,
} from "@/api/iterative-plan";
import {
  getAppVersionInfo
} from "@/api/data"
export default {
  name: 'IterativePlan',
  data () {
    return {
      imgUrl0: require("../../img/offonlinecommitpom.png"),
      show_img_val: false,
      index: '',
      email_modal: false,
      finish_modal: false,
      mail_content: '',
      stage: '',
      sql_content: '',
      sql_status: '',
      date: '',
      time: '',
      modal_git: false,
      modal_config: false,
      m_app_config: '',
      m_git_diff: '',
      app_detail: [],
      release_notice: '',
      release_description: '',
      compile_done_list: this.$store.state.compile_done_list,
      // subject: '',
      // content: '',
      // addresses: [],
      // mailSearch: '',
      // MailStr: '',
      // ReceiverList: [],
      allMails: [],
      allSelectedMails: [],
      allFilterMails: [],
      // mailListShow: false,
      finish_table_columns: [
        {
          title: '应用名',
          key: 'app_name'
        },
        {
          title: 'IP地址',
          key: 'ip'
        },
        {
          title: '已发布版本',
          key: 'pro_version',
          width: 300
        },
        {
          title: '制品库版本',
          key: 'git_version',
          width: 300
        },
        {
          title: '版本比较',
          key: 'status',
          render: (h, params) => {
            let tag_color = ''
            let tag_text = ''

            switch(params.row.status)
            {
              case 0:
                tag_color = 'success'
                tag_text = '一致'
                break
              case 1:
                tag_color = 'error'
                tag_text = '不一致'
                break
              case 2:
                tag_color = 'warning'
                tag_text = '未发布'
                break
            }

            return h('Tag', {
              props: {
                color: tag_color
              }
            }, tag_text)
          }
        },
      ],
      finish_table_data: [],
      app_columns: [
        {
          title: '上线应用',
          key: 'app_name',
          width: 220,
          render: (h, params) => {
            if (params.row.app_stat == 'UAT') {
              var app_color = '#515a6e'
            } else {
              var app_color = 'green'
            }
            return h('div', [
              h('p', {
                style: {
                  color: app_color
                },
              }, params.row.app_name),
            ]);
          }
        },
        {
          title: '配置变更',
          key: 'app_config',
          align: 'left',
          width: 110,
          render: (h, params) => {
            if (params.row.app_config) {
              var show_color = 'orange';
              var show_logo = "ivu-icon ivu-icon-ios-create"
            } else {
              var show_color = '#515a6e';
              var show_logo = "ivu-icon ivu-icon-ios-create-outline"
            }
            return h('div', [
              h('a', {
                props: {
                  size: 'small',
                },
                style: {
                  color: show_color
                },
                on: {
                  click: () => {
                    this.showAppConfig(params.index)
                  }
                }
              }, '配置信息'),
              h('a', {
                attrs: {
                  class: show_logo,
                },
                style: {
                  "font-size": "20px",
                  "margin-left": "5px",
                  "margin-top": "-4px"
                },
                on: {
                  click: () => {
                    this.showAppConfig(params.index)
                  }
                }
              }, '')
            ]);
          }
        },
        {
          title: 'git版本',
          key: 'app_git',
          align: 'left',
          width: 320,
        },
        {
          title: '版本差异',
          align: 'left',
          render: (h, params) => {
            return h('div', [
              h('Button', {
                props: {
                  size: 'small',
                },
                style: {
                  // marginRight: '5px'
                },
                on: {
                  click: () => {
                    this.showGitDiff(params.index)
                  }
                }
              }, '详细信息'),
            ]);
          }
        },
      ]
    }
  },
  props: ['businessID'],
  watch: {
    businessID: {
      handler: function(val) {
        this.initThisVue()
      }
    },
    compile_done_list: {
      handler: function(val) {
        if (this.$store.state.compile_done_list.indexOf(this.businessID) !== -1) {
          this.initThisVue()
          this.$store.commit('reset_compile_status', this.businessID)
        }
      }
    }
    // 'mailSearch': function(){
      // if ( this.mailSearch ) {
        // this.allFilterMails = this.allMails.filter(item => item.indexOf(this.mailSearch) > -1);
      // } else {
        // this.allFilterMails = this.allMails
      // }
    // }
  },
  methods: {
    savePlan() {
      let data = {}
      data['businessID'] = this.businessID
      data['date'] = this.date
      data['time'] = this.time
      data['sql_content'] = this.sql_content
      data['release_notice'] = this.release_notice
      data['release_description'] = this.release_description
      data['receivers'] = this.allSelectedMails.toString()
      editIterativePlan(data).then(res => {
        if (res.data.code === 0) {
          this.$Message.success("保存成功");
          var dba_index = this.allSelectedMails.indexOf('<EMAIL>')
          if (this.sql_content !== "" && this.sql_content !== "无") {
            if (dba_index === -1) {
              this.allSelectedMails.push('<EMAIL>')
            }
          } else {
            if ( dba_index > -1) {
              this.allSelectedMails.splice(dba_index, 1)
            }
          }
        } else {
          this.$Message.error("保存失败");
        }
      });
    },
    show_img(){
  this.show_img_val = true;
},
    initThisVue() {
      let data = {}
      data['businessID'] = this.businessID
      getIterativePlan(data).then(res => {
        if (res.data.code === 0) {
          if (res.data.data['date']) {
            this.date = res.data.data['date']
            this.time = res.data.data['time']
          } else {
            var now = new Date();
            var year = now.getFullYear();
            var month = now.getMonth() + 1;
            var day = now.getDate();
            if (month >= 1 && month <= 9) {
              month = "0" + month;
            }
            if (day >= 0 && day <= 9) {
              day = "0" + day;
            }
            this.date = year + "-" + month + "-" + day
            this.time = now.getHours() + ":" + now.getMinutes()
          }
          this.sql_content = res.data.data['sql_content']
          this.sql_status = res.data.data['sql_status']
          this.release_notice = res.data.data['release_notice']
          this.release_description = res.data.data['release_description']
          this.app_detail = res.data.data['app_detail']
          this.allSelectedMails = res.data.data['receivers']
        } else {
        }
      });
      getEmailAddresses().then(res => {
        if (res.data.code === 0) {
          this.allMails = res.data.data
          this.allFilterMails = this.allMails
        }
      });
    },
    showAppConfig(val) {
      this.index = val
      this.m_app_config = this.app_detail[this.index].app_config
      this.modal_config = true
    },
    showGitDiff(val) {
      this.m_git_diff = ''
      this.modal_git = true
      let data = {}
      data['business_id'] = this.businessID
      data['app_name'] = this.app_detail[val].app_name
      data['app_git'] = this.app_detail[val].app_git
      this.m_git_diff = '正在获取信息 ...'
      getGitDiff(data).then(res => {
        if (res.data.code === 0) {
          this.m_git_diff = res.data.data
        } else {
          this.m_git_diff = res.data.msg
        }
      });
    },
    cancel () {
      this.modal_git = false
      this.modal_config = false
    },
    configSave () {
      this.modal_git = false
      this.modal_config = false
      let data = {}
      data['business_id'] = this.businessID
      data['app_name'] = this.app_detail[this.index].app_name
      data['app_config'] = this.m_app_config
      setAppConfig(data).then(res => {
        if(res.data.code === 0) {
          this.app_detail[this.index].app_config = this.m_app_config
          this.$Message.success('更改配置成功')
        } else {
          this.$Message.error('更改配置失败')
        }
      });
    },
    // showSelectedMail() {
      // this.MailStr = "";
      // for (let i = 0; i < this.ReceiverList.length; i++) {
        // if (this.MailStr) {
          // this.MailStr += "\n" + this.ReceiverList[i];
        // } else {
          // this.MailStr = this.ReceiverList[i];
        // }
      // }
    // },
    saveEmail() {
      let vm = this
      let data = {}
      data['addresses'] = this.allSelectedMails.join(',')
      data['business_id'] = this.businessID
      saveEmailAddress(data).then(res => {
        if (res.data.code === 0) {
          this.$Message.success(res.data.msg)
        } else {
          this.$Message.error(res.data.msg)
        }
      }).catch(function (err){
        vm.$Message.error('邮件发送失败')
      })

    },
    sendEmail () {
      let vm = this
      let data = {}
      data['addresses'] = this.allSelectedMails.join(',')
      data['business_id'] = this.businessID
      data['stage'] = this.stage
      sendEmailNotify(data).then(res => {
        if (res.data.code === 0) {
          this.$Message.success(res.data.msg)
        } else {
          this.$Message.error(res.data.msg)
        }
      }).catch(function (err){
        vm.$Message.error('邮件发送失败')
      })
      this.email_modal = false
      this.mail_content = ''
    },
    cancelEmail () {
      this.email_modal = false
      this.mail_content = ''
    },
    sendMergeEmail () {
      this.email_modal = true
      this.stage = 'merge'
      let data = {}
      data['business_id'] = this.businessID
      data['stage'] = this.stage
      getEmailPreview(data).then(res => {
        if (res.data.code === 0) {
          this.mail_content = res.data.data
        } else {
          this.mail_content = ''
        }
      })
    },
    sendUatEmail () {
      this.savePlan()
      this.email_modal = true
      this.stage = 'uat'
      let data = {}
      data['business_id'] = this.businessID
      data['stage'] = this.stage
      getEmailPreview(data).then(res => {
        if (res.data.code === 0) {
          this.mail_content = res.data.data
        } else {
          this.mail_content = ''
        }
      })
    },
    sendProdEmail () {
      this.savePlan()
      this.email_modal = true
      this.stage = 'prod'
      let data = {}
      data['business_id'] = this.businessID
      data['stage'] = this.stage
      getEmailPreview(data).then(res => {
        if (res.data.code === 0) {
          this.mail_content = res.data.data
        } else {
          this.mail_content = ''
        }
      })
    },
    sendFinishEmail () {
      // this.email_modal = true
      // this.stage = 'finish'
      // this.mail_content = '<p style="font-size: 1rem; margin-left: 8rem; margin-right: 1rem;"><span style="color: blue">' + this.businessID + '</span> 上线完成?</p>'
      this.stage = 'finish'
      let type = 'svn'
      let business_id = this.businessID
      getAppVersionInfo(type, business_id).then(res => {
        this.finish_table_data = res.data;
        console.log(this.finish_table_data)
      })
      this.finish_modal = true
    },
  },
  mounted () {
    this.initThisVue()
  }
}
</script>
