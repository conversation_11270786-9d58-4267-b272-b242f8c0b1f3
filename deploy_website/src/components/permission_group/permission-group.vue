<template>
  <Card shadow>
    <div>
      <div class="message-page-con message-category-con">
        <Scroll :height="window_height">
          <h2 style="margin-bottom: 5px">
            权限分组列表
            <Icon type="ios-create-outline" @click="roleListEdit" />
          </h2>
          <ul class="ivu-menu ivu-menu-light ivu-menu-vertical">
            <li
              class="ivu-menu-item"
              style="margin-left: -15px;font-size: large;"
              @click="clickPanel('所有权限组')"
            >所有权限组</li>
          </ul>
          <ul
            class="ivu-menu ivu-menu-light ivu-menu-vertical"
            v-for="item in group_list"
            :group_list="group_list"
            :key="item"
          >
            <li class="ivu-menu-item" @click="clickPanel(item)">{{item}}</li>
          </ul>
        </Scroll>
      </div>

      <div class="message-page-con message-view-con">
        <Divider>{{chosen_group}}</Divider>
        <i-col style="margin-bottom: 5px">
          <Input
            prefix="ios-search"
            placeholder="按权限组名搜索"
            v-model="auth_group_search"
            style="width:16em; margin-right: 1em"
          />
          <Button @click="GroupSearch" type="default">查询</Button>
        </i-col>
        <i-col>
          <Table :columns="columns" :data="data" style="width: 700px;"></Table>
          <!-- <Page -->
            <!-- style="margin: 5px;" -->
            <!-- :total="total" -->
            <!-- :current="page" -->
            <!-- @on-change="changePage" -->
            <!-- show-total -->
          <!-- ></Page> -->
        </i-col>
      </div>
    </div>
    <Modal
      title="角色管理"
      v-model="rolelist_edit"
      :mask-closable="false"
      width="350px"
    >
      <Input placeholder="填如需要新增的权限组" v-model="add_group" style="width:200px; margin-right: 5px" />
      <Button @click="addGroup">新增</Button>
      <Divider></Divider>
      <table style="margin-right: 5px;">
        <tr
          v-for="item in group_list"
          :group_list="group_list"
          :key="item"
        >
          <td style="width:200px; padding-left: 10px">
            <span style="margin-right: 20px"> - - {{ item }}</span>
          </td>
          <td>
            <span>
              <Button @click="delete_group(item)">删除</Button>
            </span>
          </td>
        </tr>
      </table>
    </Modal>
    <Modal
      title="应用绑定权限组"
      v-model="role_edit"
      width="650px"
      height="800px"
      :mask-closable="false"
      @on-ok="_groupUpdate"
    >
      <Transfer
        :data="appLableList"
        filterable
        :target-keys="targetKeys"
        :titles="app_transfer"
        :render-format="render"
        :list-style="listStyle"
        @on-change="SelectApp">
      </Transfer>
    </Modal>
  </Card>
</template>

<script>

  import {
    addAuthGroupList,
    deleteGroupList,
    getAuthGroupInfo,
    getAuthGroupList,
    authGroupSearch,
    getApplist,
    searchAppAuthGroup,
    groupUpdate
    
  } from "@/api/auth";
  export default {
    name: "RoleAssignment",
    components: {
      // PublishStat
    },
    data() {
      return {
        // 左边结构
        chosen_group: "所有权限组",
        group_list: [],
        rolelist_edit: false,
        add_group:'',
        // 穿梭框
        app_transfer: ['未分配', '已分配'],
        targetKeys: [],
        appLableList: [],
        // 穿梭框样式
        listStyle: {
          width: '250px',
          height: '350px'
        },

        // 右边结构
        appList: [],
        appListStable: [],
        current_group: "",
        roleSelect: "",
        role_edit: false,
        auth_group_search: "",
        data: [],
        // page: 1,
        // total: 1,
        // pageSize: 8,
        columns: [
          {
            title: "权限组",
            key: "auth_group_name"
          },          {
            title: "权限描述",
            key: "description"
          },
          {
            title: "操作",
            key: "action",
            render: (h, params) => {
              return h("div", [
                h(
                  "Button",
                  {
                    props: {
                      type: "primary",
                      size: "small",
                      ghost: true
                    },
                    style: {
                      marginRight: "5px"
                    },
                    on: {
                      click: () => {
                        this.current_group = params.row.auth_group_name;
                        this.role_edit = true;
                        let data = {
                          auth_group_name: this.current_group
                        };
                        searchAppAuthGroup(data).then(res => {
                          try {
                            let appLableListSelected = [];
                            for (let i = 0; i < Object.keys(res.data['data']).length; i++ ) {
                              appLableListSelected.push(res.data["data"][i].app_name);
                            }
                            this.getTargetKeys(appLableListSelected);
                          } catch (err) {
                            this.getTargetKeys = [];
                          }
                        });
                      }
                    }
                  },
                  "绑定应用"
                )
              ]);
            }
          }
        ]
      };
    },
    watch: {},
    methods: {

      // 处理穿梭框
      SelectApp (newTargetKeys) {
        this.targetKeys = newTargetKeys;
      },
      // 渲染穿梭框
      render (item) {
        return item.label;
      },
      getTargetKeys (appLabelListSelected){
        this.targetKeys = [];
        appLabelListSelected.forEach( item => {
          this.targetKeys.push((this.appList.indexOf(item)+1).toString())
        });
       },

      addGroup() {
        if (this.$store.state.user.team !== 'app' || this.$store.state.user.role !== 'ops') {
          this.$Message.error("请运维人员进行操作");
          return
        }
        let data = {
          "auth_group_name": this.add_group
        };
        addAuthGroupList(data).then(res=> {
          this.$Message.info(res.data['msg']);
          this._AuthGroupList();
        })
      },
      delete_group(auth_group_name) {
        if (this.$store.state.user.team !== 'app' || this.$store.state.user.role !== 'ops') {
          this.$Message.error("请运维人员进行操作");
          return
        }
        let data = {
          "auth_group_name": auth_group_name
        };
        deleteGroupList(data).then(res=> {
          this.$Message.info(res.data['msg']);
          this._AuthGroupList();
        });
      },
      roleListEdit() {
        this.rolelist_edit = true;
      },
      GroupSearch() {
        let data = {
          auth_group_name: this.auth_group_search
        };
        authGroupSearch(data).then(res => {
          this.data = res.data["data"];
        });
      },
      _groupUpdate() {
        if (this.$store.state.user.team !== 'app' || this.$store.state.user.role !== 'ops') {
          this.$Message.error("请运维人员进行操作");
          return
        }
        let applist = [];
        this.targetKeys.forEach(item => {
          applist.push(this.appList[item-1])
        });
        let data = {
          app_name: applist.join(","),
          auth_group_name: this.current_group,
        };

        groupUpdate(data).then(res => {
          this.$Message.info(res.data["msg"]);
          this.getTable(1);
        });

      },
      clickPanel(val) {
        if (val.length) {
          this.chosen_group = val;
          this.getTable(1);
        }
      },
      changePage(p) {
        this.page = p;
        this.getTable(p);
      },

      getTable(p) {
        let chosen_group = this.chosen_group;
        if (this.chosen_group === "所有权限组") {
          chosen_group = "";
        }
        let data = {
          auth_group_name: chosen_group,
        };
        authGroupSearch(data).then(res => {
          this.data = res.data["data"];
        });
      },

      // 获取权限列表
      _AuthGroupList() {
        getAuthGroupList().then(res => {
          let array = Array();
          for (let i = 0; i < Object.keys(res.data['data']).length; i++) {
            let role_name = res.data["data"][i]["auth_group_name"];
            array.push(role_name);
            this.group_list = array;
          }
        });
        this.getTable(1);
      },
    },

    beforeMount() {
      this.window_height = window.innerHeight - 220;
    },

    mounted() {
      getApplist().then(res => {
        this.appList = res.data['data'];
        for (let i = 0; i < this.appList.length; i++) {
          let key = i + 1;
          let _map = { "key": key.toString() , "label": this.appList[i]}
          this.appLableList.push(_map);
        }
      });
      this._AuthGroupList();
    },
    destroyed() {}
  };

</script>

<style scoped lang="less">
  .message-page {
    &-con {
      // height: ~"calc(100vh - 176px)";
      min-height: 700;
      display: inline-block;
      vertical-align: top;
      position: relative;
      &.message-category-con {
        border-right: 1px solid #e6e6e6;
        width: 14em;
        height: auto;
      }
      &.message-view-con {
        position: absolute;
        left: 21em;
        top: 1em;
        right: 1em;
        bottom: 2em;
        overflow: auto;
        padding: 1em 1em 0;
        .message-view-header {
          margin-bottom: 20px;
          .message-view-title {
            display: inline-block;
          }
          .message-view-time {
            margin-left: 20px;
          }
        }
      }
    }
  }
</style>
