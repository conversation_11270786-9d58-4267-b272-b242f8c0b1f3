<!--
 * @Author: chao.bao <EMAIL>
 * @Date: 2024-08-26 11:11:33
 * @LastEditors: chao.bao <EMAIL>
 * @LastEditTime: 2024-10-31 11:20:20
 * @FilePath: /website_web/deploy_website/src/components/serachForm/index.vue
 * @Description: 搜索公共组件
-->
<template>
    <Form :model="searchData">
        <FormItem v-for="item in serachConfig" :key="item.key" class="custom_formitem" :label-width="labelWidth">
            <template #label>
                <div class="custom_label">
                    <Icon v-if="item.icon" :type="item.icon" size="16" />
                    <span>{{ item.label }}</span>
                </div>
            </template>
            <Row v-if="item.type === 'select'">
                <Col span="8">
                    <Select
                        class="custom_select common_width"
                        v-model="searchData[item.key]"
                        :disabled="item.disabled"
                        filterable
                        clearable
                        v-bind="$attrs"
                        @on-change="(...args) => handleCommon('on-change', item.key, ...args)"
                    >
                        <Option v-for="sel in item.options" :key="sel.label" :value="sel.value">{{ sel.label }}</Option>
                    </Select>
                </Col>
            </Row>
            <Row v-if="item.type === 'input'">
                <Col span="8">
                    <Input
                        v-model="searchData[item.key]"
                        class="common_width"
                        :disabled="item.disabled"
                        v-bind="$attrs"
                        :placeholder="item.placeholder"
                        @focus="(...args) => handleCommon('focus', item.key, ...args)"
                    />
                </Col>
            </Row>
            <Row v-if="item.type === 'radio'">
                <Col span="8">
                    <RadioGroup
                        v-model="searchData[item.key]"
                        v-bind="$attrs"
                        @on-change="(...args) => handleCommon('on-change', item.key, ...args)"
                    >
                        <Radio
                            v-for="sel in item.options"
                            :key="sel.label"
                            :value="sel.value"
                            :label="sel.value"
                            :disabled="sel.disabled"
                            >{{ sel.label }}</Radio
                        >
                    </RadioGroup>
                </Col>
            </Row>
            <Row v-if="item.type === 'checkbox'">
                <Col span="8">
                    <CheckboxGroup
                        v-model="searchData[item.key]"
                        v-bind="$attrs"
                        @on-change="(...args) => handleCommon('on-change', item.key, ...args)"
                    >
                        <Checkbox
                            v-for="sel in item.options"
                            :key="sel.label"
                            :label="sel.value"
                            :disabled="sel.disabled"
                            >{{ sel.label }}</Checkbox
                        >
                    </CheckboxGroup>
                </Col>
            </Row>
        </FormItem>
    </Form>
</template>

<script>
export default {
    name: 'serachForm',
    model: {
        prop: 'modelValue',
        event: 'modelValue:update'
    },
    props: {
        modelValue: {
            type: Object,
            required: true
        },
        serachConfig: {
            type: Array,
            required: true
        },
        labelWidth: {
            type: Number,
            default: 95
        }
    },
    data() {
        return {
            searchData: {}
        }
    },
    watch: {
        searchData: {
            deep: true,
            handler(val) {
                this.$emit('modelValue:update', val)
            }
        },
        modelValue: {
            immediate: true,
            handler(val) {
                this.searchData = val
            }
        }
    },
    methods: {
        handleCommon(type, key, ...args) {
            this.$emit(type, key, ...args)
        }
    }
}
</script>

<style lang="less" scoped>
.custom_formitem {
    margin-bottom: 0;
    /deep/ .ivu-form-item-content {
        display: flex;
        margin-left: 0 !important;
    }
}
.custom_label {
    display: flex;
    align-items: center;
    // 处理build和dev模式不一致问题
    margin-right: 10px;
    span {
        margin-left: 5px;
    }
}
.common_width {
    width: 300px;
}
.custom_select.ivu-select-disabled {
    /deep/ .ivu-select-selection {
        background: rgba(243, 243, 243, 0.5);
        color: rgb(81, 90, 110);
    }
}
</style>
