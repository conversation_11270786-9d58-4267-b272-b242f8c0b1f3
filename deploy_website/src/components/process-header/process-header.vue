<template>
  <div>
    <Row type="flex" align="bottom" class="code-row-bg marginBottom" style="width: 1000px; margin-left: 15px">
      <Col style="width: 300px">
        <Row :gutter="24" class="marginBottom">
          <Col span="24">
          <h2>{{ businessID_CN }}</h2>
          </Col>
        </Row>
        <Row class="marginBottom">
          当前状态： <span :style="current_status_style">{{ status }}</span>
        </Row>
        <Row class="marginBottom">
          <span style="width: 100px; margin-right: 20px">操作人：{{ this.$store.state.user.userName }}</span>
        </Row>
        <Row>
          <Button type="info" ghost class="marginRight" @click="changeBuildEdit" :disabled="conf_disabled">
            配置项编辑<Icon type="ios-arrow-forward"></Icon>
          </Button>
          <Button :type="buildStatusBtn"  ghost :class="buildStatusStyle" @click="execAction">
            {{ buildStatus }} <Icon type="ios-play" />
          </Button>
          <Button type="warning" ghost @click="reset">
            重置<Icon type="md-refresh" />
          </Button>
        </Row>
      </Col>

      <Col style="width: 350px; margin-left: 30px">
      <Row style="margin-bottom: 5px">
        <Col span="9">
        <Icon type="logo-buffer" /> 执行历史：
        </Col>
      </Row>
      <div style="height: 105px; overflow-y: auto;">
        <Row v-for="item in buildHistoryData">
          <Row class="hover">
            <Col span="2">
            <Badge :status="item.status"/>
            </Col>
            <Col span="22" class="hover" @click.native="showBuildHistoryDetail">
            {{ item.title }}&nbsp;&nbsp;&nbsp;<Icon type="md-person" />&nbsp;{{ item.user }}
            </Col>
          </Row>
        </Row>
      </div>
      </Col>

      <Col style="margin-top: -25px; width: 250px; padding-left: 70px">
      <i-circle :percent=exec_process>
                    <span class="demo-Circle-inner" style="font-size:24px">
                      <div class="demo-Circle-custom">
                        <span class="circle">
                            执行完成度
                        </span>
                        <br>
                        <span class="circle">
                            <i>{{ exec_process }}%</i>
                        </span>
                      </div>
                    </span>
      </i-circle>
      </Col>
    </Row>

    <Modal v-model="historyDetail" :title="detail_title" width="750px">
        <Button type="default" style="margin-bottom: 15px" @click="showFullLog">查看完整日志</Button>
        <pre style="margin-top: -5px">{{ detail_type }}</pre>
        <Table :columns="buildHistoryDetailColumns" :data="buildHistoryDetailData" class="buildHitoryModal"></Table>
    </Modal>

    <Modal v-model="fullLog" title="完整日志" width="750px">
      <pre style="margin-top: -5px">{{ full_log }}</pre>
    </Modal>

  </div>

</template>

<script>
  import { execHistory, resetEvent, getFullLog } from '@/api/data'

  export default {
    name: 'ProcessHeader',
    data () {
      return {
        // 执行进度
        exec_process: this.process,
        // 配置栏可编辑
        conf_disabled: false,
        // 执行按钮初始
        buildStatus: '执行',
        buildStatusBtn: 'primary',
        buildStatusStyle: 'marginRight',
        status: this.current_status,
        current_status_style: 'font-size: 18px; font-weight: bold; color: #1ABB9C',
        // 执行历史
        buildHistoryData: [],
        build_total: 1000,
        historyDetail: false,
        fullLog: false,
        detail_title: '',
        detail_type: '',
        detail_content: '',
        full_log: '',
        wb_data: [],
        // 编译历史table
        buildHistoryDetailColumns: [
          {
            title: '应用名',
            key: 'app'
          },
          {
            title: '执行状态',
            align: 'center',
            key: 'execStatus'
            // render: (h, params) => {
            //   if (params.row.execStatus === '错误日志') {
            //     return h('div', [
            //       h('Button', {
            //         props: {
            //           type: 'error',
            //           size: 'small',
            //           ghost: true,
            //         },
            //         style: {
            //           width: '70px'
            //         },
            //         on: {
            //           click: () => {
            //             this.show(params.index)
            //           }
            //         }
            //       }, params.row.execStatus),
            //     ]);
            //   } else {
            //     return h('div', [
            //       h('Button', {
            //         props: {
            //           type: 'primary',
            //           size: 'small',
            //           ghost: true,
            //         },
            //         style: {
            //           width: '70px'
            //         },
            //         on: {
            //           click: () => {
            //             this.show(params.index)
            //           }
            //         }
            //       }, params.row.execStatus),
            //     ]);
            //   }
            // }
          }
        ],
        buildHistoryDetailData: [],

        styles: {
          height: 'calc(100% - 55px)',
          overflow: 'auto',
          paddingBottom: '53px',
          position: 'static'
        },
        formData: {
          name: '',
        }
      }
    },
    props: ['businessID', 'current_status', 'process', 'businessID_CN'],
    methods: {
      // 重置
      reset (){
        this.$emit('toRunScript', false);
        resetEvent(this.businessID).then(res => {
          //
        })
      },
      // 配置栏
      changeBuildEdit (){
        this.$emit('buildEdit', true);
      },
      // 操作
      execAction () {
        if (this.buildStatus === '执行') {
          this.$emit('toRunScript', true);
        }
      },
      // 查看build历史
      showBuildHistoryDetail:function (event) {
        this.historyDetail = true;
        let title = event.currentTarget.innerText.split(' ');
        let search_id = title[1];
        this.detail_title =  '历史查询: ' + '#' + search_id;

        // 通过title 来寻找对应的历史记录
        this.buildHistoryData.forEach((item) => {
          let current_title = event.currentTarget.innerText.split('    ')[0];
          if (item.title === current_title) {
            this.buildHistoryDetailData = item.content;
            this.detail_type = item.operateType.replace('[', '').replace(']', '').replace(/{/g, '').
            replace(/'/g, '').replace(/\s/g, '').replace(/},/g, '\n').replace('}', '').replace(/:/g, ': ')
          }
        })
      },
      // 查看完整日志
      showFullLog:function (event) {
        this.fullLog = true;
        let buildID = this.detail_title.split('#')[1];
        getFullLog(this.businessID, buildID).then(res => {
          this.full_log = res.data['full_log']
        })
      },
    },
    watch: {
      // businessid切换时更新历史记录
      businessID: {
        handler: function(val) {
          // 获取编译历史
          this.build_total = 1000
          execHistory(this.businessID).then(res => {
            this.buildHistoryData = res.data
          })
        },
      },
      buildStatus: {
        handler: function() {
          if (this.buildStatus === '执行中') {
            this.build_total = this.buildHistoryData.length
          }
        },
      },
      // 同步父子组件里的当前状态
      current_status(val) {
        this.status = val;
        if (val === '空闲中') {
          this.conf_disabled = false;
          this.buildStatus = '执行';
          this.buildStatusBtn = 'primary';
          this.buildStatusStyle = 'marginRight';
          this.current_status_style = 'font-size: 18px; font-weight: bold; color: #1ABB9C;';
          this.exec_process = 0;
          // 执行结束后同步历史
          execHistory(this.businessID).then(res => {
            this.buildHistoryData = res.data
          })
          // 执行结束后同步迭代计划
          this.$store.commit('notice_compile_done', this.businessID)
        } else if (val === '忙碌中') {
          this.conf_disabled = true;
          this.buildStatus = '执行中';
          this.buildStatusBtn = 'error';
          this.buildStatusStyle = 'marginRight build_blink';
          this.current_status_style = 'font-size: 18px; font-weight: bold; color: red;'
        }
      },
      process(val) {
        this.exec_process = val;
      },
      buildHistoryData: {
        handler: function() {
          if (this.buildHistoryData.length && this.buildHistoryData.length > this.build_total) {
            if (this.buildHistoryData[0].status === 'success') {
              this.$Notice.success({
                title: this.businessID + '构建成功',
                desc: '请查看执行历史了解详情',
                duration: 0
              });
              this.build_total = this.buildHistoryData.length
            } else if (this.buildHistoryData[0].status === 'error') {
              this.$Notice.error({
                title: this.businessID + '构建失败',
                desc: '请查看执行历史了解详情',
                duration: 0
              });
              this.build_total = this.buildHistoryData.length
            }
          }
        },
      },
    },
    mounted() {
      execHistory(this.businessID).then(res => {
        this.buildHistoryData = res.data
      })
    }
  }
</script>

<style lang="less">

  .count-style{
    font-size: 50px;
  }

  .marginRight {
    margin-right: 5px;
  }

  .marginBottom {
    margin-bottom: 10px;
  }

  .demo-drawer-footer {
    width: 100%;
    position: absolute;
    bottom: 0;
    left: 0;
    border-top: 1px solid #e8e8e8;
    padding: 10px 16px;
    text-align: right;
    background: #fff;
  }
  .circle {
    font-size: 14px;
  }

  .newslist ul li p {
    font-size: 14px;
    color: #555;
    line-height: 25px;
    height: 50px;
    overflow: hidden;
    transition: height .3s;
  }

  /* 执行历史特效 */

  .hover:hover {
    background-color: #E4E7EA;
    text-decoration-line: underline;
  }

  /* 执行历史 模态框里的表格 */
  .buildHitoryModal {
    max-height: 378px;
    overflow-y: auto;
    overflow-x: hidden;
    width: 100%;
    margin-top: 15px;
  }

  /* 编译按钮闪烁效果 */
  .build_blink{
    animation: changeshadow 2s  ease-in  infinite ;
    /* 其它浏览器兼容性前缀 */
    -webkit-animation: changeshadow 2s linear infinite;
    -moz-animation: changeshadow 2s linear infinite;
    -ms-animation: changeshadow 2s linear infinite;
    -o-animation: changeshadow 2s linear infinite;
  }
  @keyframes changeshadow {
    0%{ text-shadow: 0 0 1px red}
  }
  /* 添加兼容性前缀 */
  @-webkit-keyframes changeshadow {
    0%{ text-shadow: 0 0 1px red}
  }
  @-moz-keyframes changeshadow {
    0%{ text-shadow: 0 0 1px red}
  }
  @-ms-keyframes changeshadow {
    0%{ text-shadow: 0 0 1px red}
  }
  @-o-keyframes changeshadow {
    0%{ text-shadow: 0 0 1px red}
  }

  pre {
    white-space: pre-wrap; /*css-3*/
    white-space: -moz-pre-wrap; /*Mozilla,since1999*/
    white-space: -o-pre-wrap; /*Opera7*/
    word-wrap: break-word; /*InternetExplorer5.5+*/
  }

</style>
