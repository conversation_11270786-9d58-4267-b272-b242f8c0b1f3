<template>
  <Card shadow style="height: 600px">
    <h3 style="margin: 10px">邮件通知</h3>
    <Row style="margin-left: 5px">
      <i-col style="margin: 5px" span="2">
        <Icon type="ios-pricetags-outline"></Icon>&nbsp;
        <span style="text-align: left; display: inline-block; width:90px;">标题&nbsp;</span>
      </i-col>
      <i-col style="margin: 5px" span="18">
        <Input type="textarea" placeholder="标题" v-model="subject" style="width: 300px"/>
      </i-col>
    </Row>
    <Row style="margin-left: 5px">
      <i-col style="margin: 5px" span="2">
        <Icon type="ios-chatboxes-outline"></Icon>&nbsp;
        <span style="text-align: left; display: inline-block; width:90px;">内容&nbsp;</span>
      </i-col>
      <i-col style="margin: 5px" span="18">
        <Input type="textarea" placeholder="内容" v-model="content" style="width: 300px"/>
      </i-col>
    </Row>
    <i-col style="margin-left: 20px">
      <Button
        ghost
        type="primary"
        style="text-align: left; display: inline-block; width:90px;"
        @click="mailListShow = true"
      >邮箱列表
        <Icon type="ios-arrow-forward"></Icon>
      </Button>
      <Drawer title="邮箱列表" placement="left" :closable="false" v-model="mailListShow">
        <Input prefix="ios-search" placeholder="邮箱地址" v-model="mailSearch" />
        <CheckboxGroup v-model="ReceiverList" @on-change="showSelectedMail">
          <Row v-for="item in allFilterMails" :key="item">
            <Checkbox :label="item"></Checkbox>
          </Row>
        </CheckboxGroup>
      </Drawer>&nbsp;
      <Input type="textarea" readonly v-model="MailStr" autosize style="width: 300px;"/>
    </i-col>
    <Row style="margin-left: 5px">
      <i-col style="margin: 5px" span="2">
        <Button @click="sendMail" style="margin: 1px" type='primary'>发送邮件</Button>
      </i-col>
    </Row>
  </Card>
</template>

<script>
import CommonIcon from '_c/common-icon'
import { getEmailAddresses, sendEmailNotify } from "@/api/email-notify"
export default {
  name: 'EmailNotify',
  data () {
    return {
      subject: '',
      content: '',
      addresses: [],
      mailSearch: '',
      MailStr: '',
      ReceiverList: [],
      allMails: [],
      allFilterMails: [],
      mailListShow: false,
    }
  },
  props: ['businessID'],
  watch: {
    'mailSearch': function(){
      if ( this.mailSearch ) {
        this.allFilterMails = this.allMails.filter(item => item.indexOf(this.mailSearch) > -1);
      } else {
        this.allFilterMails = this.allMails
      }
      console.log(this.ReceiverList)
    }
  },
  methods: {
    showSelectedMail() {
      this.MailStr = "";
      for (let i = 0; i < this.ReceiverList.length; i++) {
        if (this.MailStr) {
          this.MailStr += "\n" + this.ReceiverList[i];
        } else {
          this.MailStr = this.ReceiverList[i];
        }
      }
    },
    sendMail () {
      let data = {}
      data['subject'] = this.subject
      data['content'] = this.content
      data['addresses'] = this.ReceiverList.join(',')
      sendEmailNotify(data).then(res => {
        if (res.data.code == 0) {
          this.$Message.success(res.data.msg)
        } else {
          this.$Message.error(res.data.msg)
        }
      })
    },
  },
  mounted () {
    getEmailAddresses().then(res => {
      if (res.data.code === 0) {
        this.allMails = res.data.data
        this.allFilterMails = this.allMails
      }
    });
  }
}
</script>
