<template>
  <Card shadow style="height: 100%">
     <Row style="margin-left: 5px">
        <i-col style="margin: 5px" span="2">
        <Icon type="md-crop" />&nbsp;
        <span style="text-align: left; display: inline-block; width:60px;">发布类型</span>
      </i-col>
      <i-col style="margin: 5px" span="5">
        <Select v-model="iterative_type" @on-change="changeSelect" style="width: 300px">
          <Option  v-for="item in iterative_type_list" :value="item.value" :key="item.value">{{ item.label }}</Option>
        </Select>
      </i-col>
    </Row>
   <Row style="margin-left: 5px">
      <i-col style="margin: 5px" span="2">
        <Icon type="ios-list-box" style="margin-right: 8px"/>
        <span style="text-align: left; display: inline-block; width:60px;">迭代版本</span>
      </i-col>
      <i-col  style="margin: 5px" span="5">
            <Select v-model="iterative_name" style="width: 300px" @on-change="getGitrepos" filterable>
                <Option v-for="(item,index) in iterativeList "
                        :value="item"
                        :label="item"
                        :key="item+index">
                  {{ item }}</Option>
            </Select>
       </i-col>
     </Row>
        <!--<Row style="margin-left: 5px">-->
      <!--<i-col style="margin: 5px" span="2">-->
         <!--<Icon type="md-person"></Icon>&nbsp;-->
        <!--<span style="text-align: left; display: inline-block; width:60px;">申请人&nbsp;</span>-->
      <!--</i-col>-->
      <!--<i-col  style="margin: 5px" span="5">-->
           <!--<p style="width: 300px" >刘帅</p>-->
       <!--</i-col>-->
     <!--</Row>-->

    <Row style="margin-left: 5px">
      <i-col style="margin: 5px" span="2">
        <Icon type="ios-list-box" style="margin-right: 8px"/>
        <span style="text-align: left; display: inline-block; width:60px;">申请时间</span>
      </i-col>
      <i-col  style="margin: 5px" span="5">
           <p style="width: 300px" >{{iterativeInfo.br_start_date}}</p>
       </i-col>
     </Row>
    <Row style="margin-left: 5px">
      <i-col style="margin: 5px" span="2">
        <Icon type="md-text"></Icon>&nbsp;
        <span style="text-align: left; display: inline-block; width:60px;">版本描述&nbsp;</span>
      </i-col>
      <i-col style="margin: 5px" span="18">
        <Input type="textarea" placeholder="版本描述" v-model="iterativeInfo.description" style="width: 300px"/>
      </i-col>
    </Row>
     <Row style="margin-left: 5px">
      <i-col style="margin: 5px" span="2">
        <Icon type="md-text"></Icon>&nbsp;
        <span style="text-align: left; display: inline-block; width:60px;">仓库列表</span>
      </i-col>
      <i-col style="margin: 5px" span="18">
         <tables  v-model="repoTableData" :columns="columns2" @on-delete="handleDelete"/>
    </i-col>
    </Row>
 <Row style="margin-left: 5px">
    <i-col style="margin: 10px">
      <Icon type="md-list"></Icon>&nbsp;
      <Button
        ghost
        type="primary"
        style="text-align: left; display: inline-block; width:80px;"
        @click="showAddRepo"
      >追加仓库</Button>
    </i-col>
</Row>
<Row>
     <Divider orientation="left">仓库列表：</Divider>
    <tables  v-model="projecttableData" :columns="columns"/>
</Row>
    <Drawer title="应用列表" placement="left" :closable="false" v-model="appListShow" width="30%">
      <CheckboxGroup ref="chekboxList" v-model="appList">
        <Row v-for="item in allApps" :key="item.title">
          <Checkbox :label="item.value" ><span>{{item.title}}</span></Checkbox>
        </Row>
        <div class="demo-drawer-footer">
          <Button style="margin-right: 8px" @click="appListShow = false">Cancel</Button>
          <Button type="primary" @click="addSyss">Submit</Button>
        </div>
      </CheckboxGroup>
    </Drawer>
  </Card>
</template>

<script>
import Tables from "_c/tables";
import { addSys, delBranch } from "@/api/form-request";
import { getTableData } from '@/api/data'
import { getIterativeGitRopesApi, getGitGroupRopesApi, setIterAppJdk } from '@/api/git-iterative'
import store from '../../store'
import {getUserName} from "@/libs/util";

export default {
  name: "IterativeOverview",
  components: {
    Tables
  },
  data() {
    return {
      reposList:[],
      appListShow: false,
      iterative_name:"",
      iterative_type:"",

      iterative_list:[],
      appList: [],
      allApps: [
      ],
         iterative_type_list: [
        {
          value: 'test',
          label: '测试'
        },
        {
          value: 'jinji',
          label: '紧急'
        },
            {
          value: 'dev',
          label: '研发'
        }
      ],
      projecttableData:[],
      columns: [
        { title: "系统名称", key: "appName" },
        { title: "应用状态", key: "sys_status" },
        { title: "包类型", key: "appType" },
        { title: "JDK版本",
          render: (h, params) => {
            let op_list = [
              h('Option', { props: { value: '1.8' } }),
              h('Option', { props: { value: '1.7' } }),
              h('Option', { props: { value: '1.6' } })
            ]

            return h('Select', {
              props: {
                placeholder: params.row.jdkVersion,
                value: params.row.jdkVersion,
                transfer: true,
              },
              style: {
                width: '100px'
              },
              on: {
                'on-change': (val) => {
                  this.specify_iter_app_jdk(this.iterative_name, params.row.appName, val)
                }
              }
            }, op_list);
          }
        },
      ],

      repoTableData: [],
       columns2: [
        { title: "仓库名", key: "gitRepo" },
        { title: "申请人", key: "proposer" },
        { title: "中文名", key: "cname" },
        {
          title: "删除仓库",
          key: "handle",
          options: ["delete"],
          button: [
            (h, params, vm) => {
              return h(
                "Poptip",
                {
                  props: {
                    confirm: true,
                    title: "你确定要删除吗?"
                  },
                  on: {

                    "on-ok": () => {
                      vm.$emit("on-delete", params);
                      vm.$emit(
                        "input",
                        params.repoTableData.filter(
                          (item, index) => index !== params.row.initRowIndex
                        )
                      );

                    }
                  }
                },
               // [h("Button", "自定义删除")]
              );
            }
          ]
        }
      ],
    };
  },


   computed:{
        iterativeList(){
          let iterativeList=[]
          for (let i =0;i<this.iterative_list.length;i++)
          {
           //console.log(this.iterative_list[i].br_style)
            if (this.iterative_type==this.iterative_list[i].br_style)
            {
              console.log(this.iterative_list[i].pipeline_id)
              iterativeList.push(this.iterative_list[i].pipeline_id)
            }
          }
          console.log(iterativeList)
          return iterativeList
        },
         iterativeInfo(){
                  let iterativeInfo= { br_start_date:"",duedate:"",description:""}
                     for (let i =0;i<this.iterative_list.length;i++){
                         if (this.iterative_name==this.iterative_list[i].pipeline_id){
                            iterativeInfo.br_start_date=this.iterative_list[i].br_start_date,
                          iterativeInfo.duedate=this.iterative_list[i].duedate,
                          iterativeInfo.description=this.iterative_list[i].description
                         }
                     }
           return iterativeInfo
                },
      },

  methods: {
    changeSelect(params){
      this.iterative_name=""
      store.commit("setBusinessID","")
    },
    //左侧框
    showAddRepo(){
      this.appListShow = true;
      if (this.iterative_name !== "") {
        getGitGroupRopesApi({pipeline_id: this.iterative_name}).then(res => {
          //获取相同组下的git库信息
          let midRepos =[]
          let flag=0
          //如果迭代中已经存在对应git库，则不展示
          for (let i =0;i<res.data.repos_list.length;i++){
            flag=0
            for (let j =0;j<this.repoTableData.length;j++){
              if (res.data.repos_list[i].value==this.repoTableData[j].gitRepo){
                flag=1
              }
            }
            if (flag==0){
              midRepos.push(res.data.repos_list[i])
            }
          }
          this.allApps=midRepos
         // alert(JSON.stringify(res.data.repos_list))
        })
      }
    },

    //onchange 迭代列表事件
     getGitrepos(params){
      store.commit("setBusinessID",this.iterative_name)

     // this.$emit("changeBusinessID", this.iterative_name)
      // alert(store.state.businessID)
     // alert(params);
       getIterativeGitRopesApi({pipeline_id:params}).then(res => {
         //获取git仓库信息
     this.repoTableData=res.data.git_repo_list
       //获取应用信息
      this.projecttableData=res.data.appname_list
    // alert(JSON.stringify(this.projecttableData))
   })
    },
    handleDelete(params) {
      //分支所有人 和当前用户相同 可以删除
        if (params.row.proposer==getUserName()){
      delBranch({ gitRepo: params.row.gitRepo, pipeline_id: this.iterative_name,proposer:params.row.proposer }).then(
        result => {
          if (result.data.code === 0){
            this.$Message.success(result.data.msg)
          }
          else(
           this.$Message.error(result.data.error)
          )
        }
      );}
      else{
          this.$Message.error("没有权限删除"+params.row.proposer+"用户申请的分支")
        }

    // params.row.proposer
    //  alert(JSON.stringify(params.row.gitRepo));
    },
    // exportExcel() {
    //   this.$refs.tables.exportCsv({
    //     filename: `table-${new Date().valueOf()}.csv`
    //   });
    // },
    addSyss() {
      console.log(this.businessID);

               this.$Spin.show({
                    render: (h) => {
                        return h('div', [
                            h('Icon', {
                                'class': 'demo-spin-icon-load',
                                props: {
                                    type: 'ios-loading',
                                    size: 18
                                }
                            }),
                            h('div', '分支拉取中请稍等。。。')
                        ])
                    }
                });

      this.appListShow = false;
      for (let i =0;i<this.appList;i++){
      this.repoTableData.push(
       {gitRepo: this.appList[i], proposer: "", cname: ""}
      )}

      addSys({ repos_list: this.appList, pipeline_id: this.iterative_name }).then(
        result => {
          this.$Message.success(result.data.msg)
          getIterativeGitRopesApi({pipeline_id:this.iterative_name}).then(res => {
            //获取git仓库信息
            this.repoTableData=res.data.git_repo_list
            //获取应用信息
            this.projecttableData=res.data.appname_list
            // alert(JSON.stringify(this.projecttableData))
          })
          this.$Spin.hide();
          this.appList=[]
        }
      );
    },
    specify_iter_app_jdk(iterative_name, app_name, jdk_version) {
      let data = {
        'iterative_name': iterative_name,
        'app_name': app_name,
        'jdk_version': jdk_version
      }
      setIterAppJdk(data).then(res => {
        if (res.data.code === 0) {
          this.$Message.success(res.data.msg)
        } else {
          this.$Message.error(res.data.msg)
        }
        this.getGitrepos(this.iterative_name)
      })
    },
    initThisVue() {
      this.iterative_type=store.state.businessID.split("_")[1]
      this.iterative_name =store.state.businessID
      this.getGitrepos(this.iterative_name)
    }
  },
   created(){

       //    // var param = this.$route.params;
       // alert (JSON.stringify(this.$route.params))
       //    this.iterative_type=this.$route.params["iterative_type"]
       //   alert (this.iterative_type)
       //    this.iterative_name = this.$route.params["iterative_name"]
         //this.iterative_type=this.$route.iterative_type
          //如果使用query方式传入的参数使用this.$route.query 接收
          //如果使用params方式传入的参数使用this.$router.params接收
      },

 mounted () {

   getTableData().then(res => {
     this.iterative_list = res.data.iterative_list;
     //alert(JSON.stringify(this.iterative_list))
     //this.columns[2].filters = res.data.group_list
     //this.groupList=res.data.group_list;
     //alert(this.groupList)

   })
   // alert (JSON.stringify(this.$route.params))
          this.iterative_type=this.$route.params.br_style
        // alert (this.iterative_type)
    //  alert (this.$route.params["br_style"])
          this.iterative_name = this.$route.params["iterative_name"]
       if (this.iterative_name!=""){
        this.getGitrepos(this.iterative_name)
   }
 }
};
</script>

<style scoped>


    .demo-spin-icon-load{
        animation: ani-demo-spin 1s linear infinite;
    }

</style>
