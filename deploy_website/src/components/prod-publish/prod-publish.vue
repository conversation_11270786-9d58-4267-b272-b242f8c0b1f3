<template>
  <Card shadow >
    <h2 style="margin: 10px">{{ this.businessID }}</h2>
    <Tag><span style="color: #17233d">产线节点：黑色</span></Tag>
    <Tag><span style="color: #19be6b">灾备节点：绿色</span></Tag>
    <span>节点是否一致：</span>
    <span style="color: green" v-if="consistentNode">一致 </span>
    <span style="color: red" v-else v-bind:title="nodeInfo">不一致</span>
    <Button type="primary" shape="circle" icon="ios-refresh" size="small"
            @click="refreshNodeConsistent"></Button>
    <span style="color:#a59f9f">{{consistentTime}}</span>
    <Table :loading="loading" :columns="columns" :data="data"></Table>
    <Row style="margin-top: 2em">
      <i-col span="15">备注：当天没有发布，不支持回滚操作。如果需要回滚，请联系系统运维人员</i-col>
    </Row>
    <Modal
      v-model="prod_modal"
      width="680"
      @on-cancel="cancel">
      <p slot="header" style="color:gray;">
        <Icon type="md-clipboard"></Icon>
        <span> {{ m_app_name }} 健康状态</span>
      </p>
      <Table border stripe :columns="health_columns" :data="health_data"></Table>
      <div slot="footer">
        <Button @click="cancel">关闭</Button>
      </div>
    </Modal>
    <Modal
      v-model="ops_operate_modal"
      width="680"
      @on-cancel="cancel">
      <p slot="header" style="color:gray;">
        <Icon type="md-clipboard"></Icon>
        <span> {{ m_app_name }} 执行历史</span>
      </p>
      <div style="height:300px; overflow-y: auto;">
        <table style="margin: 10px;" v-for="cont in historyCont" :key="cont.index">
            <tr>
              <td width="15px"><Icon type="md-arrow-round-forward"></Icon></td>
              <td width="100px" style="color: darkblue;">{{ cont.operator }}</td>
              <td width="50px" style="color: black">{{ cont.type }}</td>
              <td width="400px">{{ cont.operateTime }}</td>
            </tr>
            <tr>
              <td width="15px"></td>
              <td width="100px" style="border-bottom: #DDDDDD solid 2px; color: black;">{{ cont.ip }}</td>
              <td width="450px" colspan="2" style="border-bottom: #DDDDDD solid 2px;" v-if="cont.detail !== 'error'"> {{ cont.detail }}</td>
              <td width="450px" colspan="2" style="border-bottom: #DDDDDD solid 2px; color: red;" v-else> {{ cont.detail }}</td>
            </tr>
        </table>
      </div>
      <div slot="footer">
        <Button @click="cancel">关闭</Button>
      </div>
    </Modal>
    <Modal
      v-model="ack_modal"
      @on-cancel="cancel"
      @on-ok="doOperate">
      <p slot="header">
        <span> {{ app_name }} 操作 </span>
      </p>
      <div v-if="opt_type ==='deploy'">
        <div v-for="ip in ips" :key="ip" >
        <p style="font-size: 1rem; margin-left: 6rem; margin-right: 1rem; display:inline;"><span style="color: blue">{{ip}}</span></p>
        <p style="font-size: 1rem; display:inline;">执行<span style="color: red"> 发布 </span>?</p>
        </div>
      </div>
      <div v-if="opt_type ==='restart'">
        <div v-for="ip in ips" :key="ip" >
        <p style="font-size: 1rem; margin-left: 6rem; margin-right: 1rem; display:inline;"><span style="color: blue">{{ip}}</span></p>
        <p style="font-size: 1rem; display:inline;">执行<span style="color: red"> 重启 </span>?</p>
        </div>
      </div>
      <div v-if="opt_type ==='stop'">
        <div v-for="ip in ips" :key="ip" >
        <p style="font-size: 1rem; margin-left: 6rem; margin-right: 1rem; display:inline;"><span style="color: blue">{{ip}}</span></p>
        <p style="font-size: 1rem; display:inline;">执行<span style="color: red"> 停止 </span>?</p>
        </div>
      </div>
      <div v-if="opt_type ==='rollback'">
        <div v-for="ip in ips" :key="ip" >
        <p style="font-size: 1rem; margin-left: 6rem; margin-right: 1rem; display:inline;"><span style="color: blue">{{ip}}</span></p>
        <p style="font-size: 1rem; display:inline;">执行<span style="color: red"> 回滚 </span>?</p>
        </div>
      </div>
      <div v-if="opt_type ==='update'">
        <div v-for="ip in ips" :key="ip" >
        <p style="font-size: 1rem; margin-left: 6rem; margin-right: 1rem; display:inline;"><span style="color: blue">{{ip}}</span></p>
        <p style="font-size: 1rem; display:inline;">执行<span style="color: red"> 配置更新 </span>?</p>
        </div>
      </div>
      <div v-if="opt_type ==='code_update'">
        <div v-for="ip in ips" :key="ip" >
        <p style="font-size: 1rem; margin-left: 6rem; margin-right: 1rem; display:inline;"><span style="color: blue">{{ip}}</span></p>
        <p style="font-size: 1rem; display:inline;">执行<span style="color: red"> 代码更新 </span>?</p>
        </div>
      </div>
    </Modal>
  </Card>
</template>

<script>
import CommonIcon from '_c/common-icon'
import { formatDateHour } from '@/libs/util'
import { getNodeConsistentInfo } from '@/log/log'
import { getProdPublishData, getSVNRollbackStat } from '@/api/iterative-publish'
import { doOpsOperate, getOpsOperateData, getGroupIpsData } from '@/api/ops-service'
export default {
    name: 'ProdPublish',
    data() {
        return {
            consistentTime: '',
            consistentNode: true,
            nodeInfo: '',
            app_name: '',
            opt_type: '',
            ack_modal: false,
            switch_history: '',
            disable_btn: {},
            env: 'prod',
            scrollTop: 0,
            shadow: false,
            historyCont: [],
            loading: true,
            data: [],
            params: '',
            prod_modal: false,
            ops_operate_modal: false,
            deploy_node: {},
            m_app_name: '',
            m_ip: '',
            socket: null,
            compile_done_list: this.$store.state.compile_done_list,
            health_columns: [
                {
                    title: 'IP',
                    key: 'ip',
                    width: 150
                },
                {
                    title: '状态',
                    render: (h, params) => {
                        return h(
                            'div',
                            {
                                domProps: {
                                    innerHTML: params.row.status
                                }
                            },
                            ''
                        )
                    }
                }
            ],
            health_data: [],
            columns: [
                {
                    title: '应用名称',
                    key: 'app_name',
                    width: 130,
                    tooltip: true
                },
                {
                    title: '服务器',
                    key: 'nodeSelect',
                    width: 150,
                    align: 'left',
                    render: (h, params) => {
                        let tags = ['按节点发', '按组发']
                        let op_grp = []
                        let node_list = []
                        let group_list = []

                        params.row.node.forEach(item => {
                            let ip_color = '#17233d'
                            if (item.indexOf('10.11.') > -1) {
                                ip_color = '#19be6b'
                            }
                            let vnode = h('Option', {
                                props: {
                                    value: item
                                },
                                style: {
                                    color: ip_color
                                }
                            })
                            node_list.push(vnode)
                        })

                        params.row.group.forEach(item => {
                            let vnode = h('Option', {
                                props: {
                                    value: item
                                }
                            })
                            group_list.push(vnode)
                        })

                        tags.forEach(item => {
                            if (item === '按节点发') {
                                let vnode = h(
                                    'OptionGroup',
                                    {
                                        props: {
                                            label: '按节点发'
                                        }
                                    },
                                    node_list
                                )
                                op_grp.push(vnode)
                            } else if (item === '按组发') {
                                let vnode = h(
                                    'OptionGroup',
                                    {
                                        props: {
                                            label: '按组发'
                                        }
                                    },
                                    group_list
                                )
                                op_grp.push(vnode)
                            }
                        })

                        return h(
                            'Select',
                            {
                                style: {
                                    zIndex: '99999'
                                },
                                props: {
                                    placeholder: '',
                                    value: '',
                                    transfer: true,
                                    size: 'small'
                                },
                                on: {
                                    'on-change': val => {
                                        getSVNRollbackStat(params.row.app_name, val)
                                            .then(res => {
                                                if (res.data.status === 'success') {
                                                    // 回滚可用
                                                    params.row.rollbackStatus = false
                                                    this.$set(this.columns, params)
                                                } else {
                                                    // 回滚不可用
                                                    params.row.rollbackStatus = true
                                                    this.$set(this.columns, params)
                                                }
                                            })
                                            .catch(err => {
                                                this.$Message.error(err.response.data.msg)
                                            })

                                        if (val === 'zaibei_not_start') {
                                            this.$set(this.disable_btn, params.row.app_name, true)
                                        } else {
                                            this.$set(this.disable_btn, params.row.app_name, false)
                                        }
                                        this.deployNodeChange(params, val)
                                    }
                                }
                            },
                            op_grp
                        )
                    }
                },
                {
                    title: '发布',
                    width: 90,
                    align: 'center',
                    render: (h, params) => {
                        return h('div', [
                            h(
                                'Button',
                                {
                                    attrs: {
                                        class: 'ivu-btn ivu-btn-primary ivu-btn-small ivu-btn-ghost'
                                    },
                                    props: {
                                        disabled: this.disable_btn[params.row.app_name]
                                    },
                                    on: {
                                        click: () => {
                                            this.app_name = params.row.app_name
                                            this.params = params
                                            this.opt_type = 'deploy'
                                            let node = this.deploy_node[params.row.app_name]
                                            this.ips = []
                                            if (node && node.startsWith('10')) {
                                                this.ips = [node]
                                                this.ack_modal = true
                                            } else if (node) {
                                                getGroupIpsData({
                                                    app_name: params.row.app_name,
                                                    group_name: node
                                                }).then(res => {
                                                    if (res.data.code === 0) {
                                                        this.ips = res.data.data
                                                        this.ack_modal = true
                                                    } else {
                                                        this.$Message.error('获取IP失败')
                                                    }
                                                })
                                            } else {
                                                this.$Message.error('请选择服务器')
                                            }
                                        }
                                    }
                                },
                                '发布+启动'
                            )
                        ])
                    }
                },
                {
                    title: '重启',
                    width: 60,
                    align: 'center',
                    render: (h, params) => {
                        return h('div', [
                            h(
                                'Button',
                                {
                                    attrs: {
                                        class: 'ivu-btn ivu-btn-primary ivu-btn-small ivu-btn-ghost'
                                    },
                                    props: {
                                        disabled: this.disable_btn[params.row.app_name]
                                    },
                                    on: {
                                        click: () => {
                                            this.app_name = params.row.app_name
                                            this.opt_type = 'restart'
                                            let node = this.deploy_node[params.row.app_name]
                                            this.ips = []
                                            if (node && node.startsWith('10')) {
                                                this.ips = [node]
                                                this.ack_modal = true
                                            } else if (node) {
                                                getGroupIpsData({
                                                    app_name: params.row.app_name,
                                                    group_name: node
                                                }).then(res => {
                                                    if (res.data.code === 0) {
                                                        this.ips = res.data.data
                                                        this.ack_modal = true
                                                    } else {
                                                        this.$Message.error('获取IP失败')
                                                    }
                                                })
                                            } else {
                                                this.$Message.error('请选择服务器')
                                            }
                                        }
                                    }
                                },
                                '重启'
                            )
                        ])
                    }
                },
                {
                    title: '停止',
                    width: 60,
                    align: 'center',
                    render: (h, params) => {
                        return h('div', [
                            h(
                                'Button',
                                {
                                    attrs: {
                                        class: 'ivu-btn ivu-btn-primary ivu-btn-small ivu-btn-ghost'
                                    },
                                    on: {
                                        click: () => {
                                            this.app_name = params.row.app_name
                                            this.opt_type = 'stop'
                                            let node = this.deploy_node[params.row.app_name]
                                            this.ips = []
                                            if (node && node.startsWith('10')) {
                                                this.ips = [node]
                                                this.ack_modal = true
                                            } else if (node) {
                                                getGroupIpsData({
                                                    app_name: params.row.app_name,
                                                    group_name: node
                                                }).then(res => {
                                                    if (res.data.code === 0) {
                                                        this.ips = res.data.data
                                                        this.ack_modal = true
                                                    } else {
                                                        this.$Message.error('获取IP失败')
                                                    }
                                                })
                                            } else {
                                                this.$Message.error('请选择服务器')
                                            }
                                            // this.stop(params)
                                        }
                                    }
                                },
                                '停止'
                            )
                        ])
                    }
                },
                {
                    title: '回滚',
                    width: 60,
                    align: 'center',
                    render: (h, params) => {
                        return h('div', [
                            h(
                                'Button',
                                {
                                    attrs: {
                                        class: 'ivu-btn ivu-btn-primary ivu-btn-small ivu-btn-ghost'
                                    },
                                    props: {
                                        disabled: params.row.rollbackStatus
                                        // disabled: this.disable_btn[params.row.app_name]
                                    },
                                    on: {
                                        click: () => {
                                            this.app_name = params.row.app_name
                                            this.opt_type = 'rollback'
                                            let node = this.deploy_node[params.row.app_name]
                                            this.ips = []
                                            if (node && node.startsWith('10')) {
                                                this.ips = [node]
                                                this.ack_modal = true
                                            } else if (node) {
                                                getGroupIpsData({
                                                    app_name: params.row.app_name,
                                                    group_name: node
                                                }).then(res => {
                                                    if (res.data.code === 0) {
                                                        this.ips = res.data.data
                                                        this.ack_modal = true
                                                    } else {
                                                        this.$Message.error('获取IP失败')
                                                    }
                                                })
                                            } else {
                                                this.$Message.error('请选择服务器')
                                            }
                                        }
                                    }
                                },
                                '回滚'
                            )
                        ])
                    }
                },
                {
                    title: '配置更新',
                    width: 90,
                    align: 'center',
                    render: (h, params) => {
                        return h('div', [
                            h(
                                'Button',
                                {
                                    attrs: {
                                        class: 'ivu-btn ivu-btn-primary ivu-btn-small ivu-btn-ghost'
                                    },
                                    on: {
                                        click: () => {
                                            this.app_name = params.row.app_name
                                            this.opt_type = 'update'
                                            let node = this.deploy_node[params.row.app_name]
                                            this.ips = []
                                            if (node && node.startsWith('10')) {
                                                this.ips = [node]
                                                this.ack_modal = true
                                            } else if (node) {
                                                getGroupIpsData({
                                                    app_name: params.row.app_name,
                                                    group_name: node
                                                }).then(res => {
                                                    if (res.data.code === 0) {
                                                        this.ips = res.data.data
                                                        this.ack_modal = true
                                                    } else {
                                                        this.$Message.error('获取IP失败')
                                                    }
                                                })
                                            } else {
                                                this.$Message.error('请选择服务器')
                                            }
                                        }
                                    }
                                },
                                '配置更新'
                            )
                        ])
                    }
                },
                {
                    title: '代码更新',
                    width: 90,
                    align: 'center',
                    render: (h, params) => {
                        return h('div', [
                            h(
                                'Button',
                                {
                                    attrs: {
                                        class: 'ivu-btn ivu-btn-primary ivu-btn-small ivu-btn-ghost'
                                    },
                                    on: {
                                        click: () => {
                                            this.app_name = params.row.app_name
                                            this.opt_type = 'code_update'
                                            let node = this.deploy_node[params.row.app_name]
                                            this.ips = []
                                            if (node && node.startsWith('10')) {
                                                this.ips = [node]
                                                this.ack_modal = true
                                            } else if (node) {
                                                getGroupIpsData({
                                                    app_name: params.row.app_name,
                                                    group_name: node
                                                }).then(res => {
                                                    if (res.data.code === 0) {
                                                        this.ips = res.data.data
                                                        this.ack_modal = true
                                                    } else {
                                                        this.$Message.error('获取IP失败')
                                                    }
                                                })
                                            } else {
                                                this.$Message.error('请选择服务器')
                                            }
                                        }
                                    }
                                },
                                '代码更新'
                            )
                        ])
                    }
                },
                {
                    title: '健康状态',
                    width: 90,
                    align: 'left',
                    render: (h, params) => {
                        return h('div', [
                            h(
                                'Button',
                                {
                                    attrs: {
                                        class: 'ivu-btn ivu-btn-success ivu-btn-small ivu-btn-ghost'
                                    },
                                    on: {
                                        click: () => {
                                            this.show(params.index)
                                        }
                                    }
                                },
                                '查看状态'
                            )
                        ])
                    }
                },
                {
                    title: '执行历史',
                    width: 90,
                    align: 'left',
                    render: (h, params) => {
                        return h('div', [
                            h(
                                'Button',
                                {
                                    attrs: {
                                        class: 'ivu-btn ivu-btn-success ivu-btn-small ivu-btn-ghost'
                                    },
                                    on: {
                                        click: () => {
                                            this.app_name = params.row.app_name
                                            this.history()
                                        }
                                    }
                                },
                                '执行历史'
                            )
                        ])
                    }
                },
                {
                    title: '服务日志',
                    width: 90,
                    align: 'center',
                    render: (h, params) => {
                        return h('div', [
                            h(
                                'Button',
                                {
                                    attrs: {
                                        class: 'ivu-btn ivu-btn-primary ivu-btn-small ivu-btn-ghost'
                                    },
                                    props: {
                                        disabled: this.disable_btn[params.row.app_name]
                                    },
                                    on: {
                                        click: () => {
                                            let node = this.deploy_node[params.row.app_name]
                                            let ipList = []
                                            if (node && node.startsWith('10')) {
                                                ipList = [node]
                                                this.renderFunc(ipList[0], params.row.app_name)
                                            } else if (node) {
                                                getGroupIpsData({
                                                    app_name: params.row.app_name,
                                                    group_name: node
                                                }).then(res => {
                                                    if (res.data.code === 0) {
                                                        ipList = res.data.data
                                                        this.renderFunc(ipList[0], params.row.app_name)
                                                    } else {
                                                        this.$Message.error('获取IP失败')
                                                    }
                                                })
                                            } else {
                                                this.$Message.error('请选择服务器')
                                            }
                                        }
                                    }
                                },
                                '服务日志'
                            )
                        ])
                    }
                }
            ]
        }
    },
    props: ['businessID'],
    methods: {
        //打开日志查看tab
        renderFunc(ip, app_name) {
            let routeData = this.$router.resolve({
                path: '/log',
                query: {
                    ip: ip,
                    app_name: app_name
                }
            })
            window.open(routeData.href, '_blank')
        },
        //刷新节点是否一致状态
        refreshNodeConsistent() {
            if (this.data.length > 0) {
                let ipList = []
                for (let nodeInfo of this.data) {
                    ipList.push(nodeInfo.node)
                }
                getNodeConsistentInfo({ ip_list: ipList, app_name: this.app_name }).then(res => {
                    if (res.data.status === 'success') {
                        if (res.data.data.diff_num > 0) {
                            this.consistentNode = false
                            this.nodeInfo = res.data.data.res_dict
                        } else {
                            this.consistentNode = true
                            this.nodeInfo = ''
                        }
                    } else {
                        this.$Message.error(res.data.msg)
                    }
                    this.consistentTime = formatDateHour(new Date())
                })
            }
        },
        check_node_select(app_name) {
            if (app_name in this.deploy_node) {
                return 1
            } else {
                return 0
            }
        },
        get_history_data() {
            getOpsOperateData({ app_name: this.app_name }).then(res => {
                if (res.data.code === 0) {
                    this.historyCont = res.data.data
                } else {
                    this.historyCont = []
                }
            })
        },
        history() {
            this.ops_operate_modal = true
            this.get_history_data()
            this.switch_history = setInterval(this.get_history_data, 5000)
        },
        show(index) {
            this.m_app_name = this.data[index].app_name
            this.m_ip = this.data[index].ip
            this.prod_modal = true
            this.initWebSocket()
        },
        initWebSocket() {
            let ip = this.m_ip
            let app_name = this.m_app_name
            let socket_host = this.$store.state.socket_host + '/java_health'
            let app_type = this.env
            let socket = new WebSocket(
                'ws://' + socket_host + '?ip=' + ip + '&app_name=' + app_name + '&app_type=' + app_type
            )
            this.socket = socket
            let vm = this
            vm.health_data = []
            socket.onopen = function open() {
                console.log('WebSockets connection created.')
            }
            socket.onmessage = function message(event) {
                var result = JSON.parse(event.data)
                if (result.code === 0) {
                    if (result.data) {
                        vm.health_data = JSON.parse(result.data)
                    }
                } else {
                    vm.$Message.error(result.msg)
                }
            }
            if (socket.readyState == WebSocket.OPEN) {
                socket.onopen()
            }
        },
        closeWebSocket() {
            try {
                this.socket.onclose = function() {
                    console.log('Disconnected to socks socket')
                }
                this.socket.close()
                this.socket = null
            } catch (error) {}
        },
        cancel() {
            this.prod_modal = false
            this.ops_operate_modal = false
            this.ack_modal = false
            this.m_ip = ''
            this.m_app_name = ''
            if (this.switch_history) {
                clearInterval(this.switch_history)
            }
            this.closeWebSocket()
        },
        deployNodeChange(params, val) {
            let app_name = params.row.app_name
            this.deploy_node[app_name] = val

            // if (val == "zaibei_not_start"){
            //   this.disable_btn = true
            //   console.log(this.disable_btn)
            // }else{
            //   this.disable_btn = false
            // }
            // this.$forceUpdate()
        },
        doOperate() {
            let data = {
                app_name: this.app_name,
                ips: this.ips,
                optType: this.opt_type
            }
            doOpsOperate(data).then(res => {
                if (res.data.code === 0) {
                    this.$Message.info(res.data.msg)
                    this.params.row.rollbackStatus = false
                    this.$set(this.columns, this.params)
                    this.history()
                } else {
                    this.$Message.error(res.data.msg)
                }
            })
        },
        initThisVue() {
            getProdPublishData(this.businessID).then(res => {
                let tmpList = []
                if (res.data.code === 0) {
                    for (let item in res.data.data) {
                        res.data.data[item]['stat'] = '执行状态'
                        tmpList.push(res.data.data[item])
                    }
                }
                this.data = tmpList
                this.loading = false
            })
            // getVerInfoData (this.businessID).then(res => {
            // this.creator = res.data.data.creator
            // })
        }
    },
    watch: {
        businessID: {
            handler: function(val) {
                this.initThisVue()
            }
        },
        compile_done_list: {
            handler: function(val) {
                this.initThisVue()
            }
        }
    },
    beforeMount() {},
    mounted() {
        this.initThisVue()
    }
}
</script>

<style>
.ivu-tabs {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    position: relative;
    color: #515a6e;
    overflow: unset;
    zoom: 1;
}
</style>
