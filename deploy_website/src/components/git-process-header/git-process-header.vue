<template>
  <div>
    <Row type="flex" align="bottom" class="code-row-bg marginBottom" style="width: 1000px; margin-left: 15px">
      <i-col style="width: 300px">
        <Row :gutter="24" class="marginBottom">
          <i-col span="24">
          <h2>{{ this.$store.state.businessID }}</h2>
          </i-col>
        </Row>
        <Row class="marginBottom">
          当前状态： <span :style="current_status_style">{{ status }}</span>
        </Row>
        <Row class="marginBottom">
          <span style="width: 100px; margin-right: 20px">操作人：{{ this.$store.state.user.userName }}</span>
        </Row>
        <Row>
          <Button type="info" ghost class="marginRight" @click="changeBuildEdit" >
            配置项编辑<Icon type="ios-arrow-forward"></Icon>
          </Button>
          <Button :type="buildStatusBtn"  ghost :class="buildStatusStyle" @click="execAction">
            {{ buildStatus }} <Icon type="ios-play" />
          </Button>
          <Button type="warning" ghost @click="reset">
            重置<Icon type="md-refresh" />
          </Button>
        </Row>
      </i-col>

      <i-col style="width: 350px; margin-left: 30px">
        <Row style="margin-bottom: 5px">
          <i-col span="9">
          <Icon type="logo-buffer" /> 执行历史：
          </i-col>
        </Row>
        <div style="height: 105px; overflow-y: auto;">
          <Row v-for="item in buildHistoryData">
            <Row class="hover">
              <i-col span="2">
              <Badge :status="item.status"/>
              </i-col>
              <i-col span="22" class="hover" @click.native="showBuildHistoryDetail">
              {{ item.title }}&nbsp;&nbsp;&nbsp;<Icon type="md-person" />&nbsp;{{ item.user }}
              </i-col>
            </Row>
          </Row>
        </div>
      </i-col>

      <i-col style="margin-top: -25px; width: 250px; padding-left: 70px">
        <i-circle :percent=exec_process>
          <span class="demo-Circle-inner" style="font-size:24px">
            <div class="demo-Circle-custom">
              <span class="circle">
                执行完成度
              </span>
              <br>
              <span class="circle">
                <i>{{ exec_process }}%</i>
              </span>
            </div>
          </span>
        </i-circle>
      </i-col>
    </Row>

    <Row v-if="compile_env !== 'prod'" style="margin-left: 15px">
      <RadioGroup v-model="select_env" @on-change="change_env">
        <Radio label="test">
            <span>测试</span>
        </Radio>
        <Radio label="hd">
            <span>灰度</span>
        </Radio>
      </RadioGroup>
      <p v-if="compile_env === 'test'">测试包地址：\\192.168.221.96\release\testpackage\{{ this.$store.state.businessID }}</p>
    </Row>

    <Modal v-model="historyDetail" :title="detail_title" width="750px">
      <Button type="default" style="margin-bottom: 15px" @click="showFullLog">查看完整日志</Button>
      <pre style="margin-top: -5px">{{ detail_type }}</pre>
      <Table :columns="buildHistoryDetailColumns" :data="buildHistoryDetailData" class="buildHitoryModal"></Table>
    </Modal>

    <Modal v-model="fullLog" title="完整日志" width="750px">
      <pre style="margin-top: -5px">{{ full_log }}</pre>
    </Modal>
    <Drawer
      title="编译列表"
      v-model="conf_disabled"
      :closable="false"
      placement="right"
      width="450px"
    >
      <slot>
      </slot>
    </Drawer>
  </div>

</template>

<script>

 import { execHistory, getFullLog,resetEvent } from '@/api/data'
 import store from '../../store'
  export default {
    name: 'GitProcessHeader',
    data () {
      return {
        select_env: 'test',
        // 执行进度
        exec_process: this.process,

        // 配置栏可编辑
        conf_disabled: false,
        // 执行按钮初始
        buildStatus: '执行',
        buildStatusBtn: 'primary',
        buildStatusStyle: 'marginRight',
        status: this.current_status,
        current_status_style: 'font-size: 18px; font-weight: bold; color: #1ABB9C',
        // 执行历史
        buildHistoryData:this.buildHistory,
        historyDetail: false,
        fullLog: false,
        detail_title: '',
        detail_type: '',
        detail_content: '',
        full_log: '',
        wb_data: [],
        // 编译历史table
        buildHistoryDetailColumns: [
          {
            title: '应用名',
            key: 'app'
          },
          {
            title: '执行状态',
            align: 'center',
            key: 'execStatus'
          }
        ],
        buildHistoryDetailData: [],

        styles: {
          height: 'calc(100% - 55px)',
          overflow: 'auto',
          paddingBottom: '53px',
          position: 'static'
        },
        formData: {
          name: '',
        }
      }
    },
    props: ['current_status', 'process', "buildHistory", "compile_env"],
    methods: {
      changeBuildEdit(){
        this.conf_disabled=true;
      },
      change_env(env) {
        this.$emit('changeCompileEnv', env)
      },
      // 重置
      reset (){
        this.$emit('toRunScript', false);
        resetEvent(this.businessID, this.compile_env).then(res => {
          //
        })
      },

      // 操作
      execAction () {
        if (this.buildStatus === '执行') {
          this.$emit('toRunScript', true);
        }
      },
      // 查看build历史
      showBuildHistoryDetail:function (event) {
        this.historyDetail = true;
        let title = event.currentTarget.innerText.split(' ');
        let search_id = title[1];
        this.detail_title =  '历史查询: ' + '#' + search_id;

        // 通过title 来寻找对应的历史记录
        this.buildHistoryData.forEach((item) => {
          let current_title = event.currentTarget.innerText.split('    ')[0];
          if (item.title === current_title) {
            this.buildHistoryDetailData = item.content;
            this.detail_type = item.operateType.replace('[', '').replace(']', '').replace(/{/g, '').
            replace(/'/g, '').replace(/\s/g, '').replace(/},/g, '\n').replace('}', '').replace(/:/g, ': ')
          }
        })
      },
      // 查看完整日志
      showFullLog:function (event) {
        this.fullLog = true;
        let buildID = this.detail_title.split('#')[1];
        // alert(this.compile_env)
        getFullLog(store.state.businessID, buildID, this.compile_env).then(res => {
          this.full_log = res.data['full_log']
        })
      },
    },
    watch: {
      buildHistory(val){
        this.buildHistoryData=val
      },
      current_status(val) {
        this.status = val;
        if (val === '空闲中') {
          //this.conf_disabled = false;
          this.buildStatus = '执行';
          this.buildStatusBtn = 'primary';
          this.buildStatusStyle = 'marginRight';
          this.current_status_style = 'font-size: 18px; font-weight: bold; color: #1ABB9C;';
          this.exec_process = 0;
          // 执行结束后同步历史
          execHistory(store.state.businessID, this.compile_env).then(res => {
            this.buildHistoryData = res.data
          })
          // 执行结束后同步迭代计划
          this.$store.commit('notice_compile_done', store.state.businessID)
        } else if (val === '忙碌中') {
          //this.conf_disabled = true;
          this.buildStatus = '执行中';
          this.buildStatusBtn = 'error';
          this.buildStatusStyle = 'marginRight build_blink';
          this.current_status_style = 'font-size: 18px; font-weight: bold; color: red;'
        }
      },
      process(val) {
        this.exec_process = val;
      },
      compile_env() {
        this.select_env = this.compile_env
      }
    },
  }
</script>

<style lang="less">

  .count-style{
    font-size: 50px;
  }

  .marginRight {
    margin-right: 5px;
  }

  .marginBottom {
    margin-bottom: 10px;
  }

  .demo-drawer-footer {
    width: 100%;
    position: absolute;
    bottom: 0;
    left: 0;
    border-top: 1px solid #e8e8e8;
    padding: 10px 16px;
    text-align: right;
    background: #fff;
  }
  .circle {
    font-size: 14px;
  }

  .newslist ul li p {
    font-size: 14px;
    color: #555;
    line-height: 25px;
    height: 50px;
    overflow: hidden;
    transition: height .3s;
  }

  /* 执行历史特效 */

  .hover:hover {
    background-color: #E4E7EA;
    text-decoration-line: underline;
  }

  /* 执行历史 模态框里的表格 */
  .buildHitoryModal {
    max-height: 378px;
    overflow-y: auto;
    overflow-x: hidden;
    width: 100%;
    margin-top: 15px;
  }

  /* 编译按钮闪烁效果 */
  .build_blink{
    animation: changeshadow 2s  ease-in  infinite ;
    /* 其它浏览器兼容性前缀 */
    -webkit-animation: changeshadow 2s linear infinite;
    -moz-animation: changeshadow 2s linear infinite;
    -ms-animation: changeshadow 2s linear infinite;
    -o-animation: changeshadow 2s linear infinite;
  }
  @keyframes changeshadow {
    0%{ text-shadow: 0 0 1px red}
  }
  /* 添加兼容性前缀 */
  @-webkit-keyframes changeshadow {
    0%{ text-shadow: 0 0 1px red}
  }
  @-moz-keyframes changeshadow {
    0%{ text-shadow: 0 0 1px red}
  }
  @-ms-keyframes changeshadow {
    0%{ text-shadow: 0 0 1px red}
  }
  @-o-keyframes changeshadow {
    0%{ text-shadow: 0 0 1px red}
  }

  pre {
    white-space: pre-wrap; /*css-3*/
    white-space: -moz-pre-wrap; /*Mozilla,since1999*/
    white-space: -o-pre-wrap; /*Opera7*/
    word-wrap: break-word; /*InternetExplorer5.5+*/
  }

</style>
