<template>
  <Card shadow style="height: 100%">
    <Row style="margin-left: 5px">
      <i-col style="margin: 5px" span="2">
        <Icon type="md-crop" />&nbsp;
        <span style="text-align: left; display: inline-block; width:60px;">发布类型</span>
      </i-col>
      <i-col style="margin: 5px" span="9">
        <Select v-model="iterative_type" style="width: 380px">
          <Option v-for="item in iterative_type_list" :value="item.value" :key="item.value">{{ item.label }}</Option>
        </Select>
      </i-col>
    </Row>
    <Row style="margin-left: 5px">
      <i-col style="margin: 5px" span="2">
      </i-col>
      <i-col style="margin: 5px" span="9">
        <span style="text-align: left; display: inline-block; color: blue">迭代</span><span style="margin-left: 1em">该版本在平台可以跨天使用；</span>
        <br>
        <span style="margin-left: 3em; text-align: left; display: inline-block;">编译前需通知洁敏分支合并主干；</span>
        <br>
        <span style="text-align: left; display: inline-block; color: blue">紧急</span><span style="margin-left: 1em">该版本在平台仅保留一天，跨天需要重新申请分支；</span>
        <br>
        <span style="margin-left: 3em; text-align: left; display: inline-block;">编译前不需要通知洁敏合并主干，紧急分支直接上线；</span>
      </i-col>
    </Row>

    <Row style="margin-left: 5px">
      <i-col style="margin: 5px" span="2">
        <Icon type="md-person"></Icon>&nbsp;
        <span style="text-align: left; display: inline-block; width:60px;">申请人&nbsp;</span>
      </i-col>
      <i-col style="margin: 5px" span="9">
        <Input disabled :value="operator" style="width: 380px"/>
      </i-col>
    </Row>

    <Row v-if="iterative_type==='iterative'" style="margin-left: 5px;">
      <i-col style="margin: 5px" span="2">
        <Icon type="ios-list-box" style="margin-right: 8px"/>
        <span style="text-align: left; display: inline-block; width:60px;">迭代版本</span>
      </i-col>
      <i-col v-if="mod_change==='创建迭代'" style="margin: 5px" span="9">
        <Input v-model="iterative_version" placeholder="填写版本号，如：1.0.0" style="width: 380px" />
      </i-col>
      <i-col v-if="mod_change==='迭代分支管理'" style="margin: 5px" span="5">
        <Select v-model="iterative_version" filterable style="width: 380px" @on-change="chooseVersion">
          <Option v-for="item in businessid_about_me_list"
                  :value="item.value"
                  :label="item.label"
                  :key="item.value">
            {{ item.label }}</Option>
        </Select>
      </i-col>

    </Row>
    <Row v-if="iterative_type==='iterative'" style="margin-left: 5px;">
      <i-col style="margin: 5px" span="2"></i-col>
      <i-col style="margin: 5px" span="10">
        <RadioGroup v-model="mod_change">
          <Radio label="创建迭代"></Radio>
          <Radio label="迭代分支管理"></Radio>
        </RadioGroup>
      </i-col>
    </Row>
    <Row v-if="iterative_type==='iterative' && mod_change==='创建迭代'" style="margin-left: 5px">
      <i-col style="margin: 5px" span="2">
        <Icon type="md-people" style="margin-right: 8px"/>
        <span style="text-align: left; display: inline-block; width:60px;">选择团队</span>
      </i-col>
      <i-col style="margin: 5px" span="5">
        <Select v-model="ft" style="width: 380px">
          <Option v-for="item in ft_list" :value="item.value" :key="item.value">{{ item.label }}</Option>
        </Select>
      </i-col>
    </Row>

    <!--<Row v-if="iterative_type==='iterative'" style="margin-left: 5px">-->
      <!--<i-col style="margin: 5px" span="2">-->
        <!--<Icon type="md-options" style="margin-right: 8px"/>-->
        <!--<span style="text-align: left; display: inline-block; width:60px;">分支地址</span>-->
      <!--</i-col>-->
      <!--<i-col style="margin: 5px" span="5">-->
        <!--<Input type='textarea' v-model="branch_path" placeholder="填写分支地址" style="width: 380px" />-->
      <!--</i-col>-->
    <!--</Row>-->

    <Row v-if="iterative_type==='emergency'" style="margin-left: 5px">
      <i-col style="margin: 5px" span="2">
        <Icon type="md-pricetags"></Icon>&nbsp;
        <span style="text-align: left; display: inline-block; width:60px;">选择版本&nbsp;</span>
      </i-col>
      <i-col style="margin: 5px" span="5">
        <Select :value="businessID" @on-change="chooseVersion" style="width: 380px">
          <OptionGroup label="新增">
            <Option :value="newVersion">{{ newVersion }}</Option>
          </OptionGroup>
          <OptionGroup label="现有">
            <Option v-for="item in curVersion" :value="item" :key="item.index">{{ item }}</Option>
          </OptionGroup>
        </Select>
          <Input v-if="iterative_type==='iterative'" v-model="value" placeholder="填写分支地址" />
      </i-col>
    </Row>

    <Row style="margin-left: 5px" v-if="iterative_type==='iterative' && mod_change==='创建迭代'">
      <i-col style="margin: 5px" span="2">
        <Icon type="md-text"></Icon>&nbsp;
        <span style="text-align: left; display: inline-block; width:60px;">版本描述&nbsp;</span>
      </i-col>
      <i-col style="margin: 5px" span="18">
        <Input type="textarea" placeholder="版本描述" v-model="description" style="width: 380px"/>
      </i-col>
    </Row>

    <Row style="margin-left: 5px" v-if="iterative_type==='iterative' && mod_change==='创建迭代'">
      <i-col style="margin: 5px" span="2"></i-col>
      <i-col style="margin: 5px" span="18">
        <div style="width: 380px; text-align:right">
          <Button @click="createIterative" style="margin: 1px" type="success" ghost>创建迭代</Button>
        </div>
      </i-col>
    </Row>
    <!--<Row style="margin-left: 5px" v-if="iterative_type==='emergency'">-->
    <Row style="margin-left: 5px" v-if="mod_change!=='创建迭代' || iterative_type==='emergency'">
      <i-col style="margin: 5px" span="2">
        <Icon type="md-apps"></Icon>&nbsp;
        <span style="text-align: left; display: inline-block; width:60px;">应用选择&nbsp;</span>
      </i-col>
      <i-col style="margin: 5px" span="18">
        <Transfer
          :data="app_data"
          :target-keys="targetKeys"
          filterable
          :render-format="render"
          @on-change="handleChange"
          :titles="branch_transfer"
          :list-style="listStyle"
        >
        </Transfer>
      </i-col>
    </Row>

    <Row style="margin-left: 5px" v-if="iterative_type==='emergency'|| mod_change==='迭代分支管理'">
      <i-col style="margin: 5px" span="2">
        <Button @click="addBranch" style="margin: 1px" type="success" ghost>新建分支</Button>
      </i-col>
      <i-col style="margin: 5px" span="18">
        <Input type="textarea" :rows="textarea_row" disabled v-model="response" style="width: 660px; resize: none"/>
      </i-col>
    </Row>
  </Card>
</template>

<script>

import {
  addEmergencyBranch,
  getPipelineData,
  getVerInfoData,
  createIterativeAPI,
  getIdAboutMe,
  createIterativeBranch
} from "@/api/iterative-publish";

export default {
  name: "BranchMgmtNew",
  data() {
    return {
      iterative_type: "iterative",
      iterative_type_list: [
        {
          value: 'iterative',
          label: '迭代'
        },
        {
          value: 'emergency',
          label: '紧急'
        }
      ],
      ft_list: [
        {
          value: 'TP',
          label: 'TP'
        },
        {
          value: 'TMS',
          label: 'TMS'
        },
        {
          value: 'FPC',
          label: 'FPC'
        },
        {
          value: 'CRM',
          label: 'CRM'
        },
        {
          value: 'MRING',
          label: 'MRING'
        },
        {
          value: 'devops',
          label: 'devops'
        },
        {
          value: 'MOFANG',
          label: 'MOFANG'
        },
        {
          value: 'middleware',
          label: 'middleware'
        },
        {
          value: 'webtest',
          label: 'webtest'
        },
        {
          value: 'OTC',
          label: 'OTC'
        },
      ],
      ft: '',
      branch_path: '',
      iterative_version: '',
      businessid_about_me_list: [],
      response: "",
      textarea_row: 6,
      description: "",
      choiceVersion: "",
      newVersion: "",
      // 穿梭框样式
      listStyle: {
        width: '300px',
        height: '350px'
      },
      branch_transfer: ['未出分支', '已出分支'],
      curVersion: [],
      allApps: [],
      appListShow: false,
      operator: this.$store.state.user.token,
      shadow: false,
      logCont: [],
      loading: true,
      data: [],
      m_app_name: "",
      m_ip: "",
      app_data: [],
      app_stable_data: [],
      targetKeys: [],
      mod_change: '迭代分支管理',
      columns: [
        {
          title: "应用名称",
          key: "app_name",
          width: 220
        },
        {
          title: "部署服务器",
          key: "ip",
          align: "left"
        }
      ]
    };
  },
  props: ["businessID"],
  methods: {
    // 新建分支
    addBranch() {
      let applist = [];

      // 内测版本 只有运维人员可操作
      if (this.$store.state.user.team !== 'app' || this.$store.state.user.role !== 'ops') {
        this.$Message.error("请运维人员进行操作");
        return
      }

      this.targetKeys.forEach(item => {
        if (this.app_stable_data[item-1] !==undefined) {
          applist.push(this.app_stable_data[item-1])
        }
      });
      this.$Spin.show({
        render: (h) => {
          return h('div', [
            h('Icon', {
              'class': 'demo-spin-icon-load',
              props: {
                type: 'ios-loading',
                size: 18
              }
            }),
            h('div', '创建分支中，请稍后。。。')
          ])
        }
      });
      // 创建迭代分支
      if (this.iterative_type === "iterative") {
        if (this.choiceVersion === "") {
          this.$Message.error("迭代版本不能为空")
          return
        }
        let data = {
          id: this.choiceVersion,
          app: applist,
        };

        createIterativeBranch(data).then(res=> {
          this.$Spin.hide();
          if (res.data["status"] === "error") {
            this.$Message.error(res.data["info"])
          } else {
            this.$Message.info(res.data["info"])
          }
          this.response = res.data["info"]
        });
      // 创建紧急分支
      } else if (this.iterative_type === "emergency"){
        let data = {
          id: this.choiceVersion,
          des: this.description,
          app: applist,
          operator: this.operator
        };
        addEmergencyBranch(data).then(res => {
          this.$Spin.hide();
          this.response = "";
          if (res.data.code === 0) {
            this.$Message.success("创建成功.");
            this.response = res.data.msg;
          } else {
            this.$Message.error("创建失败:" + res.data.msg);
            this.response = res.data.msg;
          }
        });
      } else {
        console.log("错误的迭代类型")
      }
    },
    // 创建迭代
    createIterative(){
      let data = {
        'featureTeam': this.ft,
        'businessID': this.ft + "_" + this.iterative_version,
        'svnPath': this.branch_path,
        'description': this.description
      };
      createIterativeAPI(data).then(res => {
        if (res.data["status"] === 'success') {
          this.$Message.success(res.data["info"]);
          this.$emit("changeBusinessID", data["businessID"]);
        } else {
          this.$Message.error(res.data["info"]);
        }
      });
    },
    // 版本切换
    chooseVersion(val) {
      this.choiceVersion = val;
      this.$emit("changeBusinessID", val);
    },
    initThisVue() {
      getPipelineData().then(res => {
        if (res.data.code === 0) {
          this.curVersion = res.data.data.cur_version;
          this.newVersion = res.data.data.new_version;
          this.allApps = res.data.data.apps;
          this.app_data = this.getBranchData()
          this.app_data.forEach(item => {
            this.app_stable_data.push(item.label)
          })
        } else {
        }
      });
    },
    // 获取所有的应用列表
    getBranchData () {
      let choiceData = [];
      let _index = 1;
      this.allApps.forEach(item => {
        choiceData.push({
          key: _index.toString(),
          label: item,
        });
        _index = _index + 1
      });
      return choiceData;
    },
    // 处理穿梭框
    handleChange (newTargetKeys) {
      this.targetKeys = newTargetKeys;
    },
    // 渲染穿梭框
    render (item) {
      return item.label;
    },
    getTargetKeys (appList){
      this.targetKeys = [];
      appList.forEach( item => {
        this.targetKeys.push((this.app_stable_data.indexOf(item)+1).toString())
      })
    },
    get_id_about_me (){
        getIdAboutMe(this.operator).then(res => {
          this.businessid_about_me_list = res.data
        });
    }
  },
  watch: {
    businessID: {
      handler: function(val) {
        if (val !== ''){
          // 根据选择的businessID获取对应信息
          getVerInfoData(val).then(res => {
            if (res.data.code === 0) {
              if (res.data.data.creator) {
                this.operator = res.data.data.creator;
              } else {
                this.operator = this.$store.state.user.token;
              }
              this.appList = res.data.data.apps;
              this.getTargetKeys(this.appList);
              this.description = res.data.data.description;
              this.response = "";
              for (let i = 0; i < this.appList.length; i++) {
                if (!this.response) {
                  this.response = "已出分支的应用:\n";
                }
                this.response += this.appList[i] + "\n";
              }
            } else {
              this.operator = this.$store.state.user.token;
              // this.operator = '无法获取用户数据';
              this.appList = [];
              this.description = "";
            }
          });
        } else {
          this.targetKeys = [];
          this.response = "";
        }

      }
    },
    iterative_type: {
      handler: function(val) {
        this.iterative_version = '';
        this.$emit("changeBusinessID", '');
      }
    },
    iterative_version: {
      handler: function(val) {
        if (this.iterative_version !=='' && this.mod_change==='迭代分支管理') {
          this.$emit("changeBusinessID", this.iterative_version);
        } else {
          this.$emit("changeBusinessID", '');
        }
      }
    },
    mod_change: {
      handler: function(val) {
        this.iterative_version = '';
        this.get_id_about_me();
      }
    }
  },
  mounted() {
    // 初始化紧急页面
    this.initThisVue();
    // 初始化迭代页面
    this.get_id_about_me();
  }
};
</script>

<style>
.demo-split {
  height: 400px;
  border: 1px solid #dcdee2;
}
.demo-split-pane {
  padding: 10px;
}
</style>
