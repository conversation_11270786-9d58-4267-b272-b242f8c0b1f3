<template>
  <div>
    <Card shadow style="height: 100%">
       <!--<Row :gutter="24" type="flex" align="bottom" justify="space-between" class="code-row-bg marginBottom">-->
      <Row>
          <Form :model="formItem" >
         <FormItem>
           <Row>
             <Col span="8">
           <h2>版本：{{ businessID }}</h2>
           </Col>
             <Col span="6">
             </Col>
               <Col span="2">
                <h2>收件人：</h2>
             </Col>
           <Col span="8">
          <Select v-model="model14" multiple filterable remote :remote-method="remoteMethod2" :loading="loading2">
            <Option v-for="(option, index) in options2"
                    :value="option.value"
                    :label="option.label"
                    :key="index">
              {{option.label}}</Option>
        </Select>
           </Col>
           </Row>
         </FormItem>
          </Form>
</Row>
       <Divider ></Divider>
      <Row>
        <Button type="info" ghost class="marginRight" @click="value1 = true">
          配置文件<Icon type="ios-arrow-forward"></Icon>
        </Button>
        <Button type="info" ghost class="marginRight" @click="value2 = true">
          数据库脚本<Icon type="ios-arrow-forward"></Icon>
        </Button>
        <Button type="warning" ghost class="marginRight">
          提交申请<Icon type="md-refresh" />
        </Button>
      </Row>

 <Divider></Divider>
      <Row>
          <Col span="16">
      <div class="drag-box-card">
        <!-- 切记设置list1和list2属性时，一定要添加.sync修饰符 -->
        <drag-list :list1.sync="list1" :list2.sync="list2" :dropConClass="dropConClass" @on-change="handleChange">
          <h3 slot="left-title">待上线列表</h3>
          <Card class="drag-item" slot="left" slot-scope="left">{{ left.itemLeft.name }}</Card>
          <h3 slot="right-title">计划上线列表</h3>
          <Card class="drag-item" slot="right" slot-scope="right">{{ right.itemRight.name }}</Card>
        </drag-list>
      </div>
          </Col>
      <!--<div class="handle-log-box">-->
        <!--<h3>上线记录</h3>-->
        <!--<div class="handle-inner-box">-->
          <!--<p v-for="(item, index) in handleList" :key="`handle_item_${index}`">{{ item }}</p>-->
        <!--</div>-->
          <Col span="8">
  <Timeline>
        <TimelineItem>
            <p class="time">1976年</p>
            <p class="content">Apple I 问世</p>
        </TimelineItem>
        <TimelineItem>
            <p class="time">1984年</p>
            <p class="content">发布 Macintosh</p>
        </TimelineItem>
        <TimelineItem>
            <p class="time">2007年</p>
            <p class="content">发布 iPhone</p>
        </TimelineItem>
        <TimelineItem>
            <p class="time">2010年</p>
            <p class="content">发布 iPad</p>
        </TimelineItem>
        <TimelineItem>
            <p class="time">2011年10月5日</p>
            <p class="content">史蒂夫·乔布斯去世</p>
        </TimelineItem>
    </Timeline>
          </Col>
        </Row>
      <!--</div>-->
      <div style="display:none;" class="res-show-box">
        <pre>{{ list1 }}</pre>
      </div>
      <div style="display:none;" class="res-show-box">
        <pre>{{ list2 }}</pre>
      </div>

       <Drawer title="添加配置文件"  :mask-closable="false"  :styles="styles" v-model="value1" width="720" >
       <div>
    <editor ref="editor" :value="content" @on-change="editorHandleChange"/>
    <Button @click="changeContent">修改编辑器内容</Button>
  </div>
            <div class="demo-drawer-footer">
                <Button style="margin-right: 8px" @click="value1 = false">Cancel</Button>
                <Button type="primary" @click="value1 = false">Submit</Button>
            </div>
      </Drawer>

        <Drawer title="数据库脚本"  :mask-closable="false"  :styles="styles" v-model="value2" width="720" >
       <div>
    <editor ref="editor" :value="content" @on-change="editorHandleChange"/>
    <Button @click="changeContent">修改编辑器内容</Button>
  </div>
            <div class="demo-drawer-footer">
                <Button style="margin-right: 8px" @click="value2 = false">Cancel</Button>
                <Button type="primary" @click="value2 = false">Submit</Button>
            </div>
      </Drawer>

    </Card>
  </div>


</template>
<script>
import DragList from '_c/drag-list'
import { getInstalledList } from '@/api/data'
import Editor from '_c/editor'
export default {
  name: 'PlanOnline',
  components: {
    DragList,
    Editor
  },
  data () {
    return {
      formItem: {
                    select: '',
                },
      value1:false,
      value2:false,
      content:"",
      list1: [],
      list2: [],
        styles: {
                    height: 'calc(100% - 55px)',
                    overflow: 'auto',
                    paddingBottom: '53px',
                    position: 'static'
                },
      dropConClass: {
        left: ['drop-box', 'left-drop-box'],
        right: ['drop-box', 'right-drop-box']
      },
      handleList: [],
      model14: [],
      loading2: false,
      options2: [],
      list: ['Alabama', 'Alaska', 'Arizona', 'Arkansas', 'California', 'Colorado', 'Connecticut',
    'Delaware', 'Florida', 'Georgia', 'Hawaii', 'Idaho', 'Illinois', 'Indiana', 'Iowa', 'Kansas', 'Kentucky',
        'Louisiana', 'Maine', 'Maryland', 'Massachusetts', 'Michigan', 'Minnesota', 'Mississippi', 'Missouri',
        'Montana', 'Nebraska', 'Nevada', 'New hampshire', 'New jersey', 'New mexico', 'New york', 'North carolina',
        'North dakota', 'Ohio', 'Oklahoma', 'Oregon', 'Pennsylvania', 'Rhode island', 'South carolina', 'South dakota',
        'Tennessee', 'Texas', 'Utah', 'Vermont', 'Virginia', 'Washington', 'West virginia', 'Wisconsin', 'Wyoming']
    }
  },
  props: ['businessID'],
  methods: {
    handleChange ({ src, target, oldIndex, newIndex }) {
      this.handleList.push(`${src} => ${target}, ${oldIndex} => ${newIndex}`)
    },
     editorHandleChange (html, text) {
      console.log(html, text)
    },
    changeContent () {
      this.$refs.editor.setHtml('<p>powered by wangeditor</p>')
    },
    remoteMethod2 (query) {
                if (query !== '') {
                    this.loading2 = true;
                    setTimeout(() => {
                        this.loading2 = false;
                        const list = this.list.map(item => {
                            return {
                                value: item,
                                label: item
                            };
                        });
                        this.options2 = list.filter(item => item.label.toLowerCase().indexOf(query.toLowerCase()) > -1);
                    }, 200);
                } else {
                    this.options2 = [];
                }
            }
  },
  mounted () {
    getInstalledList().then(res => {
      this.list1 = res.data;
      console.log("等待发布列表"+res.data);
      //this.list2 = [res.data[0]];
      this.list2 = [];
    })
  }

}
</script>
<style lang="less">
   .time{
        font-size: 14px;
        font-weight: bold;
    }
    .content{
        padding-left: 5px;
    }
  .demo-drawer-footer{
        width: 100%;
        position: absolute;
        bottom: 0;
        left: 0;
        border-top: 1px solid #e8e8e8;
        padding: 10px 16px;
        text-align: right;
        background: #fff;
    }
.drag-box-card{
  display: inline-block;
  width: 600px;
  height: 560px;
  .drag-item{
    margin: 10px;
  }
  h3{
    padding: 10px 15px;
  }
  .drop-box{
    border: 1px solid #eeeeee;
    height: 455px;
    border-radius: 5px;
  }
  .left-drop-box{
    margin-right: 10px;
  }
  .right-drop-box{
    //
  }
}
.handle-log-box{
  display: inline-block;
  margin-left: 20px;
  border: 1px solid #eeeeee;
  vertical-align: top;
  width: 200px;
  height: 500px;
  h3{
    padding: 10px 14px;
  }
  .handle-inner-box{
    height: ~"calc(100% - 44px)";
    overflow: auto;
    p{
      padding: 14px 0;
      margin: 0 14px;
      border-bottom: 1px dashed #eeeeee;
    }
  }
}
.res-show-box{
  display: inline-block;
  margin-left: 20px;
  border: 1px solid #eeeeee;
  vertical-align: top;
  width: 350px;
  height: 570px;
}
</style>
