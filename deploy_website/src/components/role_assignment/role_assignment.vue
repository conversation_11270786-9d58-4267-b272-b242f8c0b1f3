<template>
  <Card shadow>
    <div>
      <div class="message-page-con message-category-con">
        <Scroll :height="window_height">
          <h2 style="margin-bottom: 5px">
            角色列表
            <Icon type="ios-create-outline" @click="roleListEdit" />
          </h2>
          <ul class="ivu-menu ivu-menu-light ivu-menu-vertical">
            <li
              class="ivu-menu-item"
              style="margin-left: -15px;font-size: large;"
              @click="clickPanel('所有角色')"
            >所有角色</li>
          </ul>
          <ul
            class="ivu-menu ivu-menu-light ivu-menu-vertical"
            v-for="item in role_list"
            :role_list="role_list"
            :key="item"
          >
            <li class="ivu-menu-item" @click="clickPanel(item)">{{item}}</li>
          </ul>
        </Scroll>
      </div>

      <div class="message-page-con message-view-con">
        <Divider>{{chosen_role}}</Divider>
        <i-col style="margin-bottom: 5px">
          <Input
            prefix="ios-search"
            placeholder="按用户名搜索"
            v-model="userSearch"
            style="width:16em; margin-right: 1em"
          />
          <Button @click="user_search" type="default">查询</Button>
          <Button style="margin-left: 15px" @click="no_role_user_search" type="default">未分角色用户查询</Button>
        </i-col>
        <i-col>
          <Table :columns="columns" :data="data" style="width: 700px;"></Table>

          <Page
            style="margin: 5px;"
            :total="total"
            :current="page"
            :page-size="pageSize"
            @on-change="changePage"
            show-total
          ></Page>
        </i-col>
      </div>
    </div>
    <Modal
      title="角色管理"
      v-model="rolelist_edit"
      width="350px"
      :mask-closable="false"
    >
      <Input placeholder="填如需要新增的角色" v-model="add_role" style="width:200px; margin-right: 5px" />
      <Button @click="addRole">新增</Button>
      <Divider></Divider>
      <table style="margin-right: 5px;">
        <tr
          v-for="item in role_list"
          :role_list="role_list"
          :key="item"
        >
          <td style="width:200px; padding-left: 10px">
            <span style="margin-right: 20px"> - - {{ item }}</span>
          </td>
          <td>
            <span>
              <Button @click="delete_role(item)">删除</Button>
            </span>
          </td>
        </tr>
      </table>
    </Modal>
    <Modal
      title="分配角色"
      v-model="role_edit"
      width="450px"
      :mask-closable="false"
      @on-ok="roleUpdate"
    >
      <Select
        placeholder="角色列表"
        style="width:100%; margin-top: 5px"
        v-model="roleSelect"
        filterable
        multiple
      >
        <Option v-for="item in role_list" :value="item" :key="item">{{ item }}</Option>
      </Select>
    </Modal>
  </Card>
</template>

<script>
import {
  getRoleInfo,
  getRoleList,
  deleteRole,
  updateRole,
  searchUser,
  addRoleList,
  deleteRoleList,
  getNoRoleInfo

} from "@/api/auth";

export default {
  name: "RoleAssignment",
  components: {
    // PublishStat
  },
  data() {
    return {
      // 左边结构
      chosen_role: "所有角色",
      role_list: [],
      rolelist_edit: false,
      add_role:'',

      // 右边结构
      is_no_auth_search: false,
      current_user: "",
      roleSelect: "",
      role_edit: false,
      userSearch: "",
      data: [],
      page: 1,
      total: 1,
      pageSize: 8,
      columns: [
        {
          title: "用户名",
          key: "username"
        },
        // {
          // title: "所属部门",
          // key: "department"
        // },
        {
          title: "角色",
          key: "role_name"
        },

        {
          title: "操作",
          key: "action",
          render: (h, params) => {
            return h("div", [
              h(
                "Button",
                {
                  props: {
                    type: "primary",
                    size: "small",
                    ghost: true
                  },
                  style: {
                    marginRight: "5px"
                  },
                  on: {
                    click: () => {
                      this.current_user = params.row.username;
                      this.role_edit = true;
                      let data = {
                        username: params.row.username,
                        role_name: ""
                      };
                      searchUser(data).then(res => {
                        try {
                          let role_name_str = res.data["data"][0].role_name;
                          this.roleSelect = role_name_str.split(",");
                        } catch (err) {
                          this.roleSelect = "";
                        }
                      });
                    }
                  }
                },
                "分配"
              ),
              h(
                "Poptip",
                {
                  props: {
                    confirm: true,
                    transfer: true,
                    title:
                      "是否将" +
                      params.row.username +
                      '从"' +
                      this.chosen_role +
                      '"的角色中删除',
                    size: "small"
                  },
                  on: {
                    "on-ok": () => {
                      if (this.$store.state.user.team !== 'app' || this.$store.state.user.role !== 'ops') {
                        this.$Message.error("请运维人员进行操作");
                        return
                      }
                      let data = {
                        username: params.row.username,
                        role_name: this.chosen_role
                      };
                      if (this.chosen_role.indexOf("所有") >= 0){
                        this.$Message.error("\"所有组\"不能删除")
                      } else {
                        deleteRole(data).then(res => {
                          this.getTable(1);
                        });
                      }
                    },
                    "on-cancel": () => {
                      //
                    }
                  }
                },
                [
                  h(
                    "Button",
                    {
                      props: {
                        ghost: true,
                        type: "error",
                        size: "small"
                      },
                      style: {
                        marginRight: "5px",
                        color: "red"
                      }
                    },
                    ["删除"]
                  )
                ]
              )
            ]);
          }
        }
      ]
    };
  },
  watch: {},
  methods: {
    no_role_user_search() {
      if (this.is_no_auth_search === false ) {
        this.is_no_auth_search = true
      } else {
        this.is_no_auth_search = false
      }
      let data = {
        page_size: this.pageSize,
        current_page: 1
      }
      getNoRoleInfo(data).then(res=> {
        this.data = res.data['data'];
        this.total = res.data["total_count"];
      })
    },
    addRole() {
      if (this.$store.state.user.team !== 'app' || this.$store.state.user.role !== 'ops') {
        this.$Message.error("请运维人员进行操作");
        return
      }
      let data = {
        "role_name": this.add_role
      };
      addRoleList(data).then(res=> {
          this.$Message.info(res.data['msg']);
          this._getRoleList();
      })
    },
    delete_role(role_name) {
      if (this.$store.state.user.team !== 'app' || this.$store.state.user.role !== 'ops') {
        this.$Message.error("请运维人员进行操作");
        return
      }
      let data = {
        "role_name": role_name
      };
      deleteRoleList(data).then(res=> {
        this.$Message.info(res.data['msg']);
        this._getRoleList();
      });
    },
    roleListEdit() {
      this.rolelist_edit = true;
    },
    user_search(p=1) {
      let data = {
        username: this.userSearch,
        page_size: this.pageSize,
        page: p
      };
      searchUser(data).then(res => {
        this.data = res.data["data"];
        this.total = res.data['total_count'];
        this.page = res.data['page'];
      });
    },
    roleUpdate() {
      if (this.$store.state.user.team !== 'app' || this.$store.state.user.role !== 'ops') {
        this.$Message.error("请运维人员进行操作");
        return
      }
      let data = {
        username: this.current_user,
        role_name: this.roleSelect.join(",")
      };
      updateRole(data).then(res => {
        this.$Message.info(res.data["msg"]);
        this.getTable(1);
      });
    },
    clickPanel(val) {
      if (val.length) {
        this.chosen_role = val;
        this.userSearch = '';
        this.getTable(1);
      }
    },
    changePage(p) {
      this.page = p;
      this.getTable(p);
    },
    getTable(p) {
      let chosen_role = this.chosen_role;
      if (this.chosen_role === "所有角色") {
        chosen_role = "";
      }
      if (this.userSearch) {
        this.user_search(p)
      } else {
        let data = {
          role_name: chosen_role,
          page_size: this.pageSize,
          current_page: p
        };
        if (this.is_no_auth_search === false ) {
          getRoleInfo(data).then(res => {
            this.data = res.data["data"];
            this.total = res.data["total_count"];
          });
        } else {
          getNoRoleInfo(data).then(res=> {
            this.data = res.data['data'];
            this.total = res.data["total_count"];
          })
        }
      }
    },
    _getRoleList() {
      getRoleList().then(res => {
        let array = Array();
        for (let i = 0; i < Object.keys(res.data['data']).length; i++) {
          let role_name = res.data["data"][i]["role_name"];
          array.push(role_name);
          this.role_list = array;
        }
      });
    }
  },
  beforeMount() {
    this.window_height = window.innerHeight - 220;
  },

  mounted() {
    // 获取角色列表
    this._getRoleList();
    this.getTable(1); // 获取人和角色的信息
  },
  destroyed() {}
};
</script>

<style scoped lang="less">
.message-page {
  &-con {
    // height: ~"calc(100vh - 176px)";
    min-height: 700;
    display: inline-block;
    vertical-align: top;
    position: relative;
    &.message-category-con {
      border-right: 1px solid #e6e6e6;
      width: 14em;
      height: auto;
    }
    &.message-view-con {
      position: absolute;
      left: 21em;
      top: 1em;
      right: 1em;
      bottom: 2em;
      overflow: auto;
      padding: 1em 1em 0;
      .message-view-header {
        margin-bottom: 20px;
        .message-view-title {
          display: inline-block;
        }
        .message-view-time {
          margin-left: 20px;
        }
      }
    }
  }
}
</style>
