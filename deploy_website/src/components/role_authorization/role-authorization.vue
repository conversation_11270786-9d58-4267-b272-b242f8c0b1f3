<template>
  <Card shadow>
    <div>
      <div class="message-page-con message-category-con">
        <Scroll :height="window_height">
          <h2 style="margin-bottom: 5px">
            角色授权
          </h2>
          <ul class="ivu-menu ivu-menu-light ivu-menu-vertical">
            <li
              class="ivu-menu-item"
              style="margin-left: -15px;font-size: large;"
              @click="clickPanel('所有权限组')"
            >所有权限组</li>
          </ul>
          <ul
            class="ivu-menu ivu-menu-light ivu-menu-vertical"
            v-for="item in group_list"
            :group_list="group_list"
            :key="item"
          >
            <li class="ivu-menu-item" @click="clickPanel(item)">{{item}}</li>
          </ul>
        </Scroll>
      </div>

      <div class="message-page-con message-view-con">
        <Divider>{{chosen_group}}</Divider>
        <i-col style="margin-bottom: 5px">
          <Input
            prefix="ios-search"
            placeholder="按角色名搜索"
            v-model="roleSearch"
            style="width:16em; margin-right: 1em"
          />
          <Button @click="role_search" type="default">查询</Button>
        </i-col>
        <i-col>
          <Table :columns="columns" :data="data" style="width: 700px;"></Table>
          <Page
            style="margin: 5px;"
            :total="total"
            :current="page"
            @on-change="changePage"
            show-total
          ></Page>
        </i-col>
      </div>
    </div>

    <Modal title="分配授权组" :mask-closable="false" v-model="role_edit" width="450px" @on-ok="groupUpdate">
      <Select
        placeholder="授权组列表"
        style="width:100%; margin-top: 5px"
        v-model="groupSelect"
        filterable
        multiple
      >
        <Option v-for="item in group_list" :value="item" :key="item">{{ item }}</Option>
      </Select>
    </Modal>
  </Card>
</template>

<script>
import {
  // searchUser,
  getRoleAuthGroupInfo,
  // addRoleAuthGroup,
  deleteRoleAuthGroup,
  getAuthGroupList,
  updateRoleAuthGroup,
  searchRole
} from "@/api/auth";

export default {
  name: "RoleAuthorization",
  data() {
    return {
      // 左边结构
      chosen_group: "所有权限组",
      group_list: [],
      add_role: "",

      // 右边结构
      current_role: "",
      groupSelect: "",
      role_edit: false,
      roleSearch: "",
      data: [],
      page: 1,
      total: 1,
      pageSize: 8,
      columns: [
        {
          title: "角色",
          key: "role_name"
        },
        {
          title: "权限组名",
          key: "auth_group_name"
        },
        {
          title: "操作",
          key: "action",
          render: (h, params) => {
            return h("div", [
              h(
                "Button",
                {
                  props: {
                    type: "primary",
                    size: "small",
                    ghost: true
                  },
                  style: {
                    marginRight: "5px"
                  },
                  on: {
                    click: () => {
                      this.current_role = params.row.role_name;
                      this.role_edit = true;
                      let data = {
                        "role_name": this.current_role
                      }
                      getRoleAuthGroupInfo(data).then(res => {
                        try {
                          let auth_group_name_str = res.data["data"][0].auth_group_name;
                          this.groupSelect = auth_group_name_str.split(",");
                        } catch (err) {
                          this.groupSelect = "";
                        }
                      });
                    }
                  }
                },
                "分配"
              ),
              h(
                "Poptip",
                {
                  props: {
                    confirm: true,
                    transfer: true,
                    title:
                      "是否将" +
                      this.chosen_group +
                      '从"' +
                      params.row.role_name +
                      '"的角色中删除',
                    size: "small"
                  },
                  on: {
                    "on-ok": () => {
                      if (this.$store.state.user.team !== 'app' || this.$store.state.user.role !== 'ops') {
                        this.$Message.error("请运维人员进行操作");
                        return
                      }
                      let data = {
                        auth_group_name: this.chosen_group,
                        role_name: params.row.role_name
                      };
                      if (this.chosen_group.indexOf("所有") >= 0){
                        this.$Message.error("\"所有组\"不能删除")
                      } else {
                        deleteRoleAuthGroup(data).then(res => {
                          this.$Message.info("删除成功")
                          this.getTable(1);
                        });
                      }
                    },
                    "on-cancel": () => {
                      //
                    }
                  }
                },
                [
                  h(
                    "Button",
                    {
                      props: {
                        ghost: true,
                        type: "error",
                        size: "small"
                      },
                      style: {
                        marginRight: "5px",
                        color: "red"
                      }
                    },
                    ["删除"]
                  )
                ]
              )
            ]);
          }
        }
      ]
    };
  },
  watch: {},
  methods: {

    role_search() {
      let data = {
        role_name: this.roleSearch
      };
      searchRole(data).then(res => {
        this.data = res.data["data"];
      });
    },
    groupUpdate() {
      let data = {
        auth_group_name: this.groupSelect.join(","),
        role_name: this.current_role
      };
      if (this.$store.state.user.team !== 'app' || this.$store.state.user.role !== 'ops') {
        this.$Message.error("请运维人员进行操作");
        return
      }
      updateRoleAuthGroup(data).then(res => {
        this.$Message.info(res.data["msg"]);
        this.getTable(1);
      });
    },
    clickPanel(val) {
      if (val.length) {
        this.chosen_group = val;
        this.getTable(1);
      }
    },
    changePage(p) {
      this.page = p;
      this.getTable(p);
    },

    getTable(p) {
      let chosen_group = this.chosen_group;
      if (this.chosen_group === "所有权限组") {
        chosen_group = "";
      }
      let data = {
        auth_group_name: chosen_group,
        page_size: this.pageSize,
        page: p
      };

      getRoleAuthGroupInfo(data).then(res => {
          this.data = res.data['data'];
          this.total = res.data['total_count'];
          this.page = res.data['page'];
      })
    },

    // 获取权限列表
    _AuthGroupList() {
      getAuthGroupList().then(res => {
        let array = Array();
        for (let i = 0; i < Object.keys(res.data['data']).length; i++) {
          let role_name = res.data["data"][i]["auth_group_name"];
          array.push(role_name);
          this.group_list = array;
        }
      });
      this.getTable(1);
    },
  },
  beforeMount() {
    this.window_height = window.innerHeight - 220;
  },

  mounted() {
    // 获取角色列表
    this._AuthGroupList();
  },
  destroyed() {}
};
</script>

<style scoped lang="less">
.message-page {
  &-con {
    // height: ~"calc(100vh - 176px)";
    min-height: 700;
    display: inline-block;
    vertical-align: top;
    position: relative;
    &.message-category-con {
      border-right: 1px solid #e6e6e6;
      width: 14em;
      height: auto;
    }
    &.message-view-con {
      position: absolute;
      left: 21em;
      top: 1em;
      right: 1em;
      bottom: 2em;
      overflow: auto;
      padding: 1em 1em 0;
      .message-view-header {
        margin-bottom: 20px;
        .message-view-title {
          display: inline-block;
        }
        .message-view-time {
          margin-left: 20px;
        }
      }
    }
  }
}
</style>
