<template>

  <card shadow style="height: 100%">

    <ProcessHeader :businessID="businessID" :businessID_CN="businessID_CN" ref="indexImportOrder"
                     v-on:buildEdit="changeBuildEdit" v-on:toRunScript="toRunScript" :process="process"
                     :current_status="current_status">
    </ProcessHeader>

    <Row :gutter="24">
      <Col span="24">
        <Collapse simple v-model="value1">
          <Panel name="1">
            编译详情
            <div slot="content">
            <Table stripe :columns="columns1" :data="execution_data"></Table>
            </div>
          </Panel>
        </Collapse>
      </Col>
    </Row>
    <Drawer
      title="编译列表 (紧急上线请全选单应用下所有子目录)"
      v-model="buildEdit"
      :closable="false"
      placement="right"
      width="450px"
    >
    <Tree :data="compileTree" show-checkbox @on-check-change="getCompileAppList" ref="compilelist"></Tree>
    </Drawer>
    <!--<Modal v-draggable="options" v-model="historyDetail" :title="detail_title">-->
      <!--<Table :columns="buildHistoryDetailColumns" :data="buildHistoryDetailData" class="buildHitoryModal"></Table>-->
    <!--</Modal>-->
  </card>
</template>

<script>

  import { execHistory } from '@/api/data'
  import ProcessHeader from '_c/process-header'
  import { compileHandle, isParentPom, resetProcess, getCompileEditList } from '@/api/java-compile'
  import { initWebSocket, closeWebSocket } from '@/libs/web-socket'

  export default {
    name: 'JavaCompile',
    components: {
      ProcessHeader
    },
    data () {
      return {
        value1: '',
        socket: null,
        appList: [],
        // 构建进度
        compile_process: 0,
        businessID_CN: '',
        process: 0,
        // 构建按钮初始
        buildStatus: '构建',
        buildStatusBtn: 'primary',
        buildStatusStyle: 'marginRight',
        current_status: '空闲中',
        current_status_style: 'font-size: 18px; font-weight: bold; color: #1ABB9C',
        // 构建历史
        buildHistoryData: [],
        historyDetail: false,
        options: {
          trigger: '.ivu-modal-body',
          body: '.ivu-modal',
          recover: true
        },
        detail_title: '',
        detail_content: '',
        // 编译列表
        compileList: [],
        // 编译详情table
        columns1: [
          {
            title: '应用名',
            key: 'app'
          },
          {
            title: '构建状态',
            key: 'execStatus'
          },
        ],
        execution_data: [],
        wb_data: [],
        // 编译历史table
        buildHistoryDetailColumns: [
          {
            title: '应用名',
            key: 'app'
          },
          {
            title: '构建状态',
            width: 250,
            align: 'center',
            render: (h, params) => {
              if (params.row.execStatus === '错误日志') {
                return h('div', [
                  h('Button', {
                    props: {
                      type: 'error',
                      size: 'small',
                      ghost: true,
                    },
                    style: {
                      width: '70px'
                    },
                    on: {
                      click: () => {
                        this.show(params.index)
                      }
                    }
                  }, params.row.execStatus),
                ]);
              } else {
                return h('div', [
                  h('Button', {
                    props: {
                      type: 'primary',
                      size: 'small',
                      ghost: true,
                    },
                    style: {
                      width: '70px'
                    },
                    on: {
                      click: () => {
                        this.show(params.index)
                      }
                    }
                  }, params.row.execStatus),
                ]);
              }
            }
          }
        ],
        buildHistoryDetailData: [],
        compileTree: [],
        buildEdit: false,
        styles: {
          height: 'calc(100% - 55px)',
          overflow: 'auto',
          paddingBottom: '53px',
          position: 'static'
        },
        formData: {
          name: '',
        }
      }
    },
    props: {
      businessID: {
        type: [String, Number],
        default: null,
      }
    },
    mounted () {
      if (this.businessID){
          this.businessID_CN = this.businessID;
          this.execution_data = [];
          closeWebSocket(this);
          // 不为空则接入新的频道
          initWebSocket(this, '/java_compile?businessID=' + this.businessID)
      }
    },
    methods: {

      // 保存编译列表
      getCompileAppList() {
        this.execution_data = [];
        let choiceAll = this.$refs.compilelist.getCheckedAndIndeterminateNodes();
        let parent = [];
        let compileList = [];
        this.compileList = compileList;
        choiceAll.forEach(item => {
          if (item.children.length === 0) {
            if (item.title !== "trunk") {
              this.execution_data.push({'app': item.title, 'execStatus': '等待'});
              compileList.push(item.title)
            }
          } else {
          	parent.push(item.title)
          }
        });
        // 判断tree的主节点的应用是否是数据库里pom类型的应用名，是的进入编译列表
        isParentPom(parent).then(res => {
          if (res.data.length !== 0 ) {
            res.data.forEach(item => {
              console.log(compileList.indexOf(item))
              if (compileList.indexOf(item) < 0) {
              	compileList.push(item);
              	this.execution_data.push({'app': item, 'execStatus': '等待'});
                this.compileList = compileList;
              }
            })
          }
	//else {
        //    this.compileList = compileList
          //}
        })
	      // console.log(this.compileList)
      },

      // 构建成功通知
      successNotice (title, nodesc) {
        this.$Notice.success({
          title: title,
          desc: nodesc ? '' : '你的操作成功，请查看',
          duration: 0
        });
      },
      // 构建失败通知
      errorNotice (title, nodesc) {
        this.$Notice.error({
          title: title,
          desc: nodesc ? '' : '你的操作失败，请查看',
          duration: 0
        });
      },

      // 查看build历史
      showBuildHistoryDetail:function (event) {
        this.historyDetail = true;
        let title = event.currentTarget.innerText.split(' ');
        let search_id = title[1];
        this.detail_title =  '历史查询: ' + '#' + search_id;
        this.buildHistoryDetailData = this.buildHistoryData[search_id].content
      },

      // 父组件触发打开配置栏
      changeBuildEdit(val) {
        this.buildEdit = val
      },

      // 配置校验
      checkConf() {
        let is_ok = true;
        if (this.compileList.length === 0) {
          is_ok = false
        }
        return is_ok
      },

      // 点击执行的处理
      toRunScript(val) {
        let is_ok = this.checkConf();
        // 构建
        if (val === true) {
          if (is_ok === true) {
            this.current_status = '忙碌中';
            compileHandle(this.businessID, this.compileList).then(res => {
              // 返回结果必须通过websocket进行广播，这里只能对请求的浏览器作出回应
              // if (res.data.result === 'success') {
                // this.successNotice(this.businessID + '成功')
              // } else {
                // this.errorNotice(this.businessID + '失败')
              // }
              // 通知所有组件该id的编译完成
            })
          } else {
            this.$Message.error('请选择编译列表')
          }
          // 重置
        } else {
          resetProcess(this.businessID).then(res => {
            console.log(res)
            this.execDoneHandle()
          });
        }
      },

      // 执行完成的后续操作
      execDoneHandle(){
        if (this.current_status !== '空闲中') {
          this.current_status = '空闲中'
        }
        this.process = 0;
        this.execution_data = []

      },
    },
    watch: {
      // 监控编辑栏状态
      buildEdit: {
        handler: function (val) {
          if (this.buildEdit === true) {
            this.compileTree = [{"title": '数据加载中...'}];
              getCompileEditList(this.businessID).then(res => {
                this.compileTree = res.data
              })
          }
        }
      },

      // 监控business_id 连入对应的频道
      businessID: {
        handler: function(val) {
          this.businessID = val;
          this.businessID_CN = val;
          this.execution_data = [];
          closeWebSocket(this);
          // 不为空则接入新的频道
          if (val !== '') {
            initWebSocket(this, '/java_compile?businessID=' + this.businessID)
          }
        },
      },

      // 实时获取状态
      wb_data(val) {
        try {
          if (JSON.parse(val) !== '') {
            let _data = JSON.parse(val);
            this.current_status = _data['status'];
            this.process = _data['percent'];
            this.execution_data = _data['app_status']
          }
        }
        catch(err){
          // 不能json解析的不做处理
        }
      }
    }
  }
</script>

<style lang="less">

  .count-style{
    font-size: 50px;
  }

  .marginRight {
    margin-right: 5px;
  }

  .marginBottom {
    margin-bottom: 10px;
  }

  .demo-drawer-footer {
    width: 100%;
    position: absolute;
    bottom: 0;
    left: 0;
    border-top: 1px solid #e8e8e8;
    padding: 10px 16px;
    text-align: right;
    background: #fff;
  }
  .circle {
    font-size: 14px;
  }

  .newslist ul li p {
    font-size: 14px;
    color: #555;
    line-height: 25px;
    height: 50px;
    overflow: hidden;
    transition: height .3s;
  }

  /* 构建历史特效 */

  .hover:hover {
    background-color: #E4E7EA;
    text-decoration-line: underline;
  }

  /* 构建历史 模态框 */
  .buildHitoryModal {
    max-height: 378px;
    overflow-y: auto;
    overflow-x: hidden;
    width: 100%;
  }

  /* 编译按钮闪烁效果 */
  .build_blink{
    animation: changeshadow 2s  ease-in  infinite ;
    /* 其它浏览器兼容性前缀 */
    -webkit-animation: changeshadow 2s linear infinite;
    -moz-animation: changeshadow 2s linear infinite;
    -ms-animation: changeshadow 2s linear infinite;
    -o-animation: changeshadow 2s linear infinite;
  }
  @keyframes changeshadow {
    0%{ text-shadow: 0 0 1px red}
  }
  /* 添加兼容性前缀 */
  @-webkit-keyframes changeshadow {
    0%{ text-shadow: 0 0 1px red}
  }
  @-moz-keyframes changeshadow {
    0%{ text-shadow: 0 0 1px red}
  }
  @-ms-keyframes changeshadow {
    0%{ text-shadow: 0 0 1px red}
  }
  @-o-keyframes changeshadow {
    0%{ text-shadow: 0 0 1px red}
  }

</style>
