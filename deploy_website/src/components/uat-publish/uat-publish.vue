<template>
  <Card shadow style="height: 100%">
    <h2 style="margin: 10px">{{ this.businessID }}</h2>
    <Table :loading="loading" :columns="columns" :data="data"></Table>
    <Modal
      v-model="uat_modal"
      title="健康状态"
      width="680"
      @on-cancel="cancel">
      <p slot="header" style="color:gray;">
        <Icon type="md-clipboard"></Icon>
        <span> {{ m_app_name }} 健康状态</span>
      </p>
      <Table border stripe :columns="health_columns" :data="health_data"></Table>
      <div slot="footer">
        <Button @click="cancel">关闭</Button>
      </div>
    </Modal>
  </Card>
</template>

<script>
import {
    doOpsOperate,
  } from '@/api/ops-service'
import CommonIcon from '_c/common-icon'
import { getUatPublishData, uatPublishOperate } from '@/api/iterative-publish'
export default {
  name: 'UatPublish',
  data () {
    return {
      env: 'uat',
      scrollTop: 0,
      shadow: false,
      loading: true,
      data: [],
      uat_modal: false,
      m_app_name: '',
      m_ip: '',
      socket: null,
      compile_done_list: this.$store.state.compile_done_list,
      health_columns: [
        {
          title: 'IP',
          key: 'ip',
          width: 150
        },
        {
          title: '状态',
          render: (h, params) => {
            return h('div', {
              domProps: {
                innerHTML: params.row.status
              }
            }, '')
          }
        }
      ],
      health_data: [],
      columns: [
        {
          title: '应用名称',
          key: 'app_name',
          width: 200,
        },
        {
          title: '服务器',
          key: 'ip',
          align: 'left',
          width: 160,
        },
        {
          title: '发布',
          width: 80,
          align: 'center',
          render: (h, params) => {
            return h('div', [
              h('Button', {
                props: {
                  size: 'small'
                },
                style: {
                  // marginRight: '5px'
                },
                on: {
                  click: () => {
                    this.deploy(params)
                  }
                }
              }, '发布'),
            ]);
          }
        },
        {
          title: '重启',
          width: 80,
          align: 'center',
          render: (h, params) => {
            return h('div', [
              h('Button', {
                props: {
                  size: 'small',
                },
                style: {
                  // marginRight: '5px'
                },
                on: {
                  click: () => {
                    this.reboot(params)
                  }
                }
              }, '重启'),
            ]);
          }
        },
        {
          title: '配置更新',
          width: 80,
          align: 'center',
          render: (h, params) => {
            return h('div', [
              console.log(params.row),
              h('Poptip' , {
                    props: {
                      confirm: true,
                      transfer: true,
                      title: '配置更新 (' + params.row.ip + ')',
                      size: 'small'
                    },
                    style: {
                  marginRight: '5px',
                  
                },
                    on: {
                      'on-ok': () => {
                        this.select_group = params.row.name
                        this.app_name = params.row.app_name
                        this.opt_type = 'update'
                        this.ips = [params.row.ip]
                        this.doOperate()
                      },
                      'on-cancel': () => {
                        this.$Message.info('取消')
                      }
                    }
                  }, '配置更新'),
            ]);
          }
        },

        {
          title: '脚本状态',
          key: 'stat',
          width: 160,
          align: 'center',
          render: (h, params) => {
            return h('div', [
              h('p', {
                props: {
                  // size: 'small',
                  // type: 'text'
                },
                style: {
                  color: params.row.color,
                  // className: 'uat-stat-red',
                  // marginRight: '5px'
                }
              }, params.row.stat),
            ]);
          }
        },
        {
          title: '健康状态',
          align: 'left',
          render: (h, params) => {
            return h('div', [
              h('Button', {
                props: {
                  size: 'small',
                },
                style: {
                  // marginRight: '5px'
                },
                on: {
                  click: () => {
                    this.show(params.index)
                  }
                }
              }, '查看状态'),
            ]);
          }
        },
      ]
    }
  },
  props: ['businessID'],
  methods: {
    deploy (index) {
      let data = {
        'type': 'deploy',
        'ip': index.row.ip,
        'appName': index.row.app_name,
        'businessID': this.businessID,
        'user': this.$store.state.user.token
      }
      index.row.color = 'black'
      index.row.stat = '发布中...'
      uatPublishOperate(data).then(res => {
        if (res.data.code === 0) {
          index.row.stat = res.data.msg
          index.row.color = 'green'
        } else {
          index.row.stat = res.data.msg
          index.row.color = 'red'
        }
      })
    },
    reboot (index) {
      let data = {
        'type': 'restart',
        'ip': index.row.ip,
        'appName': index.row.app_name,
        'businessID': this.businessID,
        'user': this.$store.state.user.token
      }
      index.row.color = 'black'
      index.row.stat = '重启中...'
      uatPublishOperate(data).then(res => {
        if (res.data.code === 0) {
          index.row.stat = res.data.msg
          index.row.color = 'green'
        } else {
          index.row.stat = res.data.msg
          index.row.color = 'red'
        }
      })
    },
    initWebSocket () {
      let ip = this.m_ip
      let app_name = this.m_app_name
      let app_type = this.env
      let socket_host = this.$store.state.socket_host + '/java_health'
      let socket = new WebSocket('ws://' + socket_host + '?ip=' + ip + '&app_name=' + app_name + '&app_type=' + app_type)
      this.socket = socket
      let vm = this
      vm.health_data = []
      socket.onopen = function open() {
        console.log('WebSockets connection created.')
      }
      socket.onmessage = function message(event) {
        var result = JSON.parse(event.data)
        if (result.code === 0) {
          if (result.data) {
            vm.health_data = JSON.parse(result.data)
          }
        } else {
          vm.$Message.error(result.msg)
        }
      }
      if (socket.readyState == WebSocket.OPEN) {
        socket.onopen()
      }
    },
    closeWebSocket () {
      try {
        this.socket.onclose = function () {
          console.log("Disconnected to socks socket")
        }
        this.socket.close()
        this.socket = null
      } catch (error) {
      }
    },
    cancel () {
      this.uat_modal = false
      // this.$Message.info('Cancel')
      this.closeWebSocket()
    },
    show (index) {
      this.m_app_name = this.data[index].app_name;
      this.m_ip = this.data[index].ip;
      this.uat_modal = true
      this.initWebSocket()
    },
    doOperate() {
        let data = {
          'app_name': this.app_name,
          'ips': this.ips,
          'optType': this.opt_type
        }
        doOpsOperate(data).then(res => {
          if (res.data.code === 0) {
            this.$Message.info(res.data.msg)
            // this.history()
          } else {
            this.$Message.error(res.data.msg)
          }
        })
      },
    initThisVue() {
      getUatPublishData(this.businessID).then(res => {
        let tmpList = []
        if (res.data.code === 0) {
          for (let item in res.data.data) {
            tmpList.push({app_name: item, ip: res.data.data[item], stat: '执行状态', color: 'black'})
          }
        }
        this.data = tmpList
        this.loading = false
      })
    }
  },
  watch: {
    businessID: {
      handler: function(val) {
        this.initThisVue()
      }
    },
    compile_done_list: {
      handler: function(val) {
        this.initThisVue()
      }
    }
  },
  mounted () {
    this.initThisVue()
  }
}
</script>
