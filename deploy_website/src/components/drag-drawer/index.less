@prefix: ~"drag-drawer";
@drag-drawer-trigger-height: 100px;
@drag-drawer-trigger-width: 8px;

.@{prefix}-wrapper{
  &.no-select{
    user-select: none;
  }
  &.pointer-events-none{
    pointer-events: none;
    & .@{prefix}-trigger-wrapper{
      pointer-events: all;
    }
  }
  .ivu-drawer{
    &-header{
      overflow: hidden !important;
      box-sizing: border-box;
    }
    &-body{
      padding: 0;
      overflow: visible;
      position: static;
      display: flex;
      flex-direction: column;
    }
  }
  .@{prefix}-body-wrapper{
    width: 100%;
    height: 100%;
    padding: 16px;
    overflow: auto;
  }
  .@{prefix}-trigger-wrapper{
    top: 0;
    height: 100%;
    width: 0;
    .@{prefix}-move-trigger{
      position: absolute;
      top: 50%;
      height: @drag-drawer-trigger-height;
      width: @drag-drawer-trigger-width;
      background: rgb(243, 243, 243);
      transform: translate(-50%, -50%);
      border-radius: ~"4px / 6px";
      box-shadow: 0 0 1px 1px rgba(0, 0, 0, .2);
      line-height: @drag-drawer-trigger-height;
      cursor: col-resize;
      &-point{
        display: inline-block;
        width: 50%;
        transform: translateX(50%);
        i{
          display: block;
          border-bottom: 1px solid rgb(192, 192, 192);
          padding-bottom: 2px;
        }
      }
    }
  }
  .@{prefix}-footer{
    flex-grow: 1;
    width: 100%;
    bottom: 0;
    left: 0;
    border-top: 1px solid #e8e8e8;
    padding: 10px 16px;
    background: #fff;
  }
}
