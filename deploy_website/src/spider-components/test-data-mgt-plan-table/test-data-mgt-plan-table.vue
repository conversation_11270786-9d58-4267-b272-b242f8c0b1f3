<template>
  <div>
    <tables stripe v-model="test_data_info" :columns="columns" @on-selection-change="selectChange" ref="my_table">
    </tables>
  </div>
</template>

<script>
  import Tables from "@/components/tables";
  import store from "@/spider-store";
  export default {
    name: "TestDataMgtPlanTable",
    components: {
      Tables
    },
    data() {
      return {
        
        test_data_info:[],
        columns: [
          {
            type: 'selection',
            width: 50,
          },
          {title: "应用", key: "app_name"},
          /*{title: "仓库", key: "git_path"},*/
          {title: "业务类型", key: "bis_code"},
          {
            title: "业务归档版本",
            key: "bis_archive_version",
            
          },
          {
            title: "应用归档版本",
            key: "app_branch_archive",
          },
          {
            title: "应用在途分支",
            key: "app_branch",
            render: (h, params) => {
            let branch_list = [];
            params.row.app_branch.forEach( item => {
              console.log('333333333333333333333')
              console.log(params.row.app_branch)
              console.log(item.b)
              let branch = h('Option', {
                props: {
                  lable:item.b,
                  value: item.b
                }
              });
              branch_list.push(branch)
              console.log(branch_list)
            });
            return h('Select', {
              props: {
                value: params.row.app_branch,
              },
              style: {
                width: '100px'
              },
              on: {
                'on-change': (val) => {
                  // this.appChangeGroup(params.row.ip, val)
                }
              }
            }, branch_list);
          }
          },
        ],
      
      };
    },

    computed: {
      init(){
        this.test_data_info= [{app_name:'A',bis_code:'XXX',bis_archive_version:'XXX',app_branch_archive:'XXX',app_branch:[{'b':'3'},{'b':'4'}]},
        {app_name:'B',bis_code:'XXX',bis_archive_version:'XXX',app_branch_archive:'XXX',app_branch:[{'b':'3'},{'b':'4'}]},
        {app_name:'C',bis_code:'XXX',bis_archive_version:'XXX',app_branch_archive:'XXX',app_branch:[{'b':'36'},{'b':'47'}]}]
      },
    },

    methods: {
      // init(){
      //   this.test_data_info= [{app_name:'A',bis_code:'XXX',bis_archive_version:'XXX',app_branch_archive:'XXX',app_branch:[{'b':'3'},{'b':'4'}]},
      //   {app_name:'B',bis_code:'XXX',bis_archive_version:'XXX',app_branch_archive:'XXX',app_branch:[{'b':'3'},{'b':'4'}]},
      //   {app_name:'C',bis_code:'XXX',bis_archive_version:'XXX',app_branch_archive:'XXX',app_branch:[{'b':'36'},{'b':'47'}]}]
      // },
      selectChange(){

      },
      
    }
    
  };
</script>

<style scoped>
  .demo-spin-icon-load {
    animation: ani-demo-spin 1s linear infinite;
  }
</style>
