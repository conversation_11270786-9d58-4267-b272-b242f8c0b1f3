<template>
  <Card shadow style="height: 60rem;">
    <Row style="margin-left: 5px;display: flex;align-items: center;position: relative;">
      <i-col style="margin: 5px" span="2">
        <Icon type="ios-trending-up" style="margin-right: 5px"/>
        <span style="text-align: left; display: inline-block; width:60px;">业务分支</span>
      </i-col>
      <i-col style="margin: 5px" span="8">
        {{ biz_param.biz_br_name }}
      </i-col>
      <div style="position: absolute;right: 0px; margin-right: 5px;">
        <Icon type="ios-bookmarks-outline" style="margin-right: 5px"/>
        <span style="margin-right: 25px ;width:60px;">执行详情</span>

        <Select v-model="exec_history" filterable clearable size="small" @on-change="execHistoryChangeSelect"
                style="width: 150px;">
          <Option
            v-for="item in exec_history_list"
            :value="item.value"
            :key="item.value"
          >{{ item.label }}
          </Option>
        </Select>
        <Tooltip content="仅跳转到你最后一次执行的流水线的详情" placement="right-start">
          <Button type="primary" icon="ios-search" ghost size="small" @click="goExecHistory">跳转详情</Button>
        </Tooltip>
      </div>

    </Row>
    <!--    <Row style="margin-left: 5px;display: flex;align-items: center;">-->
    <!--      <i-col style="margin: 5px" span="2">-->
    <!--        <Icon type="ios-alarm-outline" style="margin-right: 5px"/>-->
    <!--        <span style="text-align: left; display: inline-block; width:60px;">采集详情</span>-->
    <!--      </i-col>-->
    <!--      <i-col style="margin: 5px" span="8">-->
    <!--        <a href="http://kibana7.it98.k8s.howbuy.com/app/discover#/?_g=(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:now-3h,to:now))&_a=(columns:!(),filters:!(),index:'28274e70-ced3-11ee-9405-2dcd5e795d1b',interval:auto,query:(language:kuery,query:''),sort:!(!('@timestamp',desc)))"  target="_blank">-->
    <!--          跳转查询-->
    <!--        </a>-->
    <!--      </i-col>-->
    <!--    </Row>-->

    <Row style="margin-left: 5px;display: flex;align-items: center;">
      <i-col style="margin: 5px" span="2">
        <Icon type="logo-youtube" style="margin-right: 5px"/>
        <span style="text-align: left; display: inline-block; width:60px;">操作&nbsp;</span>
      </i-col>
      <div style="margin: 5px;width: 200px;display: flex;justify-content: space-between">
        <Tooltip content="采集数据库的变更数据" placement="top-end">
          <Button type="primary" size="small" @click="queryDiffRecord()"
                  :loading="commitBtnDisabled">
            数据提交
          </Button>
        </Tooltip>
      </div>
      <div style="margin: 5px;width: 200px;display: flex;justify-content: space-between">
        <Tooltip content="采集数据库的变更数据" placement="top-end">
          <Button v-show="!is_pause_recording" type="primary" size="small" @click="pauseRecording()"
                  :loading="commitBtnDisabled">
            暂停
          </Button>
          <Button v-show="is_pause_recording" type="primary" size="small" @click="startRecording()"
                  :loading="commitBtnDisabled">
            取消暂停
          </Button>
        </Tooltip>
      </div>
    </Row>
    <Modal
      v-model="showDiffModel"
      title="最新采集记录"
      @on-ok="handleSubmit"
    >
      <tables  v-model="diffList" :columns="columns"/>
      <Alert style="margin-top: 6px" type="warning">如过你在数据库最后采集时间之后有变更数据，待下个采集周期完成后再提交数据，预计等待10分钟左右</Alert>
      <div slot="footer">
        <Button  type="primary" size="middle"  @click="handleSubmit">数据提交</Button>
      </div>
    </Modal>
  </Card>
</template>

<script>
import Tables from '@/components/tables'
import {
  getRecoverDumpInfo
} from '@/spider-api/pipeline'
import {
  get_latest_diff_recode,
  testDataDevMgtBranchArchive, testDataDevMgtBranchCommit
} from '@/spider-api/create-iter'
import { execute_history_retrieve, get_test_iter_list } from '@/spider-api/biz-mgt'
import { getTestDataDevStatusApi, updateTestDataDevStatusApi } from '@/spider-api/test-data-dev'
export default {
  name: 'TestdataCommit',
  components: {
    Tables
  },
  props: {
    biz_param: Object
  },
  data () {
    return {
      diffList: [],
      showDiffModel: false,
      showModel: false,
      archiveStatus: undefined,
      tag_url: undefined,
      spinShow: false,
      br_show: true,
      bis_type: '',
      dump_type_list: [],
      bis_pipeline_id: '',
      biz_br_name: '',
      env_name: undefined,
      commitBtnDisabled: false,
      is_pause_recording: false,
      btnDisabled: false,
      test_data_dev_info_list: [],
      job_name: 'commit_diff_sql',
      start_datetime: undefined,
      exec_history: "",
      exec_history_list: [{
        value: 'diff_sql',
        label: '数据提交'
      }],
      columns: [
        { title: '数据库', key: 'db_name' },
        { title: '最后采集时间', key: 'create_time' },
        { title: '采集日志',
          key: 'cdc_pipeline_url',
          render: (h, params) => {
            return h('a', {
              attrs: {
                href: params.row.cdc_pipeline_url,
                target: '_blank'
              }
            }, '跳转采集日志')
          } }
      ]
    }
  },

  computed: {},

  methods: {
    pauseRecording () {
      updateTestDataDevStatusApi({
        'biz_test_iter_id': this.biz_param.biz_test_iter_id,
        'is_pause': true
      }).then(res => {
        if (res.data.status !== 'success') {
          this.$Message.warning(res.data.msg)
          return false
        }
        this.is_pause_recording = true
        this.$Message.success('暂停采集成功')
      })
    },
    startRecording () {
      updateTestDataDevStatusApi({
        'biz_test_iter_id': this.biz_param.biz_test_iter_id,
        'is_pause': false
      }).then(res => {
        if (res.data.status !== 'success') {
          this.$Message.warning(res.data.msg)
          return false
        }
        this.is_pause_recording = false
        this.$Message.success('开启采集成功')
      })
    },
    getTestDataDevStatus () {
      getTestDataDevStatusApi({ 'biz_test_iter_id': this.biz_param.biz_test_iter_id }).then(res => {
        if (res.data.status !== 'success') {
          this.$Message.warning(res.data.msg)
          return false
        }
        this.is_pause_recording = res.data.msg
      })
    },
    revertShowModel () {
      this.showModel = !this.showModel
    },
    revertShowDiffModel () {
      this.showDiffModel = !this.showDiffModel
    },
    execHistoryChangeSelect (params) {
      this.exec_history = params
    },
    goExecHistory () {
      if (!this.exec_history || this.exec_history.trim().length == 0) {
        this.$Message.warning('请先选择执行类型')
        return false
      }
      if (!this.biz_param.biz_test_iter_id || this.biz_param.biz_test_iter_id.trim().length == 0) {
        this.$Message.warning('请先选择分支')
        return false
      }
      execute_history_retrieve({
        'exec_action_type': this.exec_history,
        'biz_test_iter_id': this.biz_param.biz_test_iter_id,
        'env_name': this.biz_param.env_name
      }).then(res => {
        if (res.data.status != 'success') {
          this.$Message.warning(res.data.msg)
          return false
        }
        if (res.data.data) {
          if (this.exec_history == 'archive_sql') {
            this.showModel = !this.showModel
            this.archiveStatus = res.data.data.split(':************************:')[0]
            this.tag_url = 'http://gitlab-lib.howbuy.pa/' + res.data.data.split(':************************:')[1]
            return false
          }
          window.open(res.data.data, '_blank')
          return false
        } else {
          this.$Message.warning('没有查询到你的执行记录')
        }
      })
    },
    get_test_iters () {
      get_test_iter_list().then(res => {
        let data_list = res.data.data
        if (data_list) {
          this.biz_br_name_obj_list = data_list
          this.biz_br_name_list = data_list.map((item) => {
            return {
              value: item.biz_test_iter_br,
              label: item.biz_test_iter_br
            }
          })
        } else {
          this.$Message.error('业务分支获取失败！')
        }
      }).catch(err => {
        console.log(err)
      })
    },
    bizBrNameChangeSelect (params, flow_change) {
      this.biz_br_name = params
      for (let bizObj in this.biz_br_name_obj_list) {
        if (this.biz_br_name_obj_list[bizObj].biz_test_iter_br == params) {
          let biz_test_iter_id = ''
          for (let bizObj in this.biz_br_name_obj_list) {
            if (this.biz_br_name_obj_list[bizObj].biz_test_iter_br == this.biz_br_name) {
              biz_test_iter_id = this.biz_br_name_obj_list[bizObj].biz_test_iter_id
            }
          }
          this.get_biz_app_lists({
            biz_code: this.biz_br_name_obj_list[bizObj].biz_code,
            'biz_test_iter_id': biz_test_iter_id
          })
        }
      }
    },
    getRecoverDumpTypeInfo () {
      getRecoverDumpInfo().then(res => {
        this.dump_type_list = res.data.data
      })
    },
    execute_confirm_handle_submit () {
      this.$Modal.confirm({
        title: '提示',
        content: '<p>确认执行数据提交？</p>',
        onOk: () => {
          this.queryDiffRecord()
          // this.handleSubmit()
        }
      })
    },
    execute_confirm_archive_data () {
      this.$Modal.confirm({
        title: '提示',
        content: '<p>确认执行数据归档？</p>',
        onOk: () => {
          this.archiveData()
        }
      })
    },
    handleSubmit () {
      if (!this.biz_param.biz_test_iter_id || this.biz_param.biz_test_iter_id.trim().length == 0) {
        this.$Message.warning('请先选择分支')
        return false
      }
      this.commitBtnDisabled = true
      this.$emit('handle-loading', !this.spinShow)
      this.showDiffModel = !this.showDiffModel
      testDataDevMgtBranchCommit(this.job_name, this.biz_param.biz_test_iter_id).then(res => {
        if (res.data.status === 'success') {
          this.$Notice.success({
            title: 'success',
            desc: '数据提交命令已发出,稍后点击跳转详情'
          })
          this.exec_history = 'diff_sql'
        } else {
          this.$Notice.error({
            title: 'error',
            desc: '数据提交启动失败'
          })
        }
        this.commitBtnDisabled = false
      }).finally(() => {
        this.$emit('handle-loading', !this.spinShow)
      })
    },
    queryDiffRecord () {
      if (!this.biz_param.biz_test_iter_id || this.biz_param.biz_test_iter_id.trim().length == 0) {
        this.$Message.warning('请先选择分支')
        return false
      }
      get_latest_diff_recode(this.biz_param.biz_test_iter_id).then(res => {
        if (res.data.status === 'success') {
          if (res.data.data.length == 0) {
            this.$Notice.warning({
              title: '警告',
              desc: '当前分支无任何采集记录,无需提交'
            })
          }else if (res.data.data.includes('衍生业务正在打dump')) {
            this.$Notice.success({
              title: '提醒',
              desc: '当前分支业务属于衍生业务，提交会触发打dump,请稍后！'
            })
          }
          else {
            this.diffList = res.data.data
            this.showDiffModel = !this.showDiffModel
          }
          console.log('data:%o', res.data.data)
        } else {
          this.$Notice.error({
            title: 'error',
            desc: '数据提交检查失败'
          })
        }
      }).finally(() => {
      })
    },
    archiveData () {
      this.btnDisabled = true
      if (!this.biz_param.biz_test_iter_id || this.biz_param.biz_test_iter_id.trim().length == 0) {
        this.$Message.warning('请先选择分支')
        return false
      }
      try {
        testDataDevMgtBranchArchive(this.biz_param.biz_test_iter_id).then(res => {
          console.log(res.data.data)
          if (res.data.status === 'success') {
            this.$Notice.success({
              title: 'success',
              desc: '数据归档命令已发出,稍后点击跳转详情'
            })
            this.exec_history = 'archive_sql'
          } else {
            this.$Notice.error({
              title: 'error',
              desc: '数据归档启动失败'
            })
          }
          this.btnDisabled = false
        })
      } catch (error) {
        this.btnDisabled = false
        this.$Notice.error({
          title: 'error',
          desc: 'error'
        })
      }

    },
    init () {
      this.showModel = false
      this.archiveStatus = undefined
      this.tag_url = undefined

      if (!this.biz_param.biz_test_iter_id || this.biz_param.biz_test_iter_id.trim().length == 0) {
        this.$Message.warning('请先选择分支')
        return false
      }
      this.getTestDataDevStatus()
    }
  }

}

</script>

<style scoped>

</style>
