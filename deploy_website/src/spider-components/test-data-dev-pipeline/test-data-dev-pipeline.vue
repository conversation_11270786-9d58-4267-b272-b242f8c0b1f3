<template>
  <Card shadow style="height: 100%;overflow: auto">
    <Row style="margin-left: 5px;display: flex;align-items: center;">
      <i-col style="margin: 5px" span="2">
        <Icon type="ios-trending-up" style="margin-right: 5px"/>
        <span style="text-align: left; display: inline-block; width:60px;">选择分支</span>
      </i-col>
      <i-col style="margin: 5px" span="8">
        <Select v-model="biz_br_name" filterable clearable size="small" @on-change="bizBrNameChangeSelect"
                style="width: 300px">
          <Option
            v-for="item in biz_br_name_list"
            :value="item.value"
            :key="item.value"
          >{{ item.label }}
          </Option>
        </Select>
      </i-col>


    </Row>
    <Row style="margin-left: 5px;display: flex;align-items: center;">
      <i-col style="margin: 5px" span="2">
        <Icon type="ios-aperture-outline" style="margin-right: 5px"/>
        <span style="text-align: left; display: inline-block; width:60px;">执行环境</span>
      </i-col>
      <i-col style="margin: 5px" span="5">
        <Select v-model="env_name" filterable clearable size="small" @on-change="envChangeSelect" style="width: 300px">
          <Option
            v-for="item in env_name_list"
            :value="item.value"
            :key="item.value"
          >{{ item.label }}
          </Option>
        </Select>
      </i-col>
    </Row>

    <Row style="margin-left: 5px;display: flex;align-items: center;">
      <i-col style="margin: 5px" span="2">
        <Icon type="ios-color-filter-outline" style="margin-right: 5px"/>
        <span style="text-align: left; display: inline-block; width:60px;">基础库集</span>
      </i-col>
      <i-col style="margin: 5px" span="5">
        <Select v-model="biz_base_db" filterable clearable size="small" @on-change="bizBaseDbChangeSelect" style="width: 300px">
          <Option
            v-for="item in biz_base_db_list"
            :value="item"
            :key="item"
          >{{ item }}
          </Option>
        </Select>
      </i-col>
      <i-col style="margin: 5px" span="5">
        <Select v-model="biz_base_db_br" filterable clearable size="small" style="width: 300px">
          <Option
            v-for="item in biz_base_db_br_list"
            :value="item"
            :key="item"
          >{{ item }}
          </Option>
        </Select>
      </i-col>
    </Row>

    <Row style="margin-left: 5px;display: flex;align-items: center;">
      <i-col style="margin: 5px" span="2">
        <Icon type="ios-apps-outline" style="margin-right: 5px"/>
        <span style="text-align: left; display: inline-block; width:60px;">应用分支</span>
      </i-col>
      <i-col style="margin: 5px" span="18">
        <tables v-if="reLoadTable" class="cutom-table" @on-selection-change="selectApp" ref="selection" v-model="appList" :columns="columns" :rowClassName="rowClassName" />
      </i-col>
    </Row>

    <Row style="margin-left: 5px;display: flex;align-items: center;">
      <i-col style="margin: 5px" span="2">
        <Icon type="logo-youtube" style="margin-right: 5px"/>
        <span style="text-align: left; display: inline-block; width:60px;">操作&nbsp;</span>
      </i-col>
      <i-col style="margin: 5px" span="4">
        <Tooltip content="初始化应用关联的数据库到拉业务分支时" placement="top-end">
        <Button type="primary" size="small" @click="execute_confirm">开发测试数据初始化</Button>
        </Tooltip>
      </i-col>

      <Tooltip content="仅跳转到你最后一次初始化的详情" placement="top-end">
        <Button type="primary" icon="ios-search" ghost size="small" @click="goExecHistory">跳转初始化详情</Button>
      </Tooltip>
    </Row>
  </Card>

</template>

<script>

import {doJenkinsTestDataDeploy} from '@/spider-api/pipeline'

import Tables from "@/components/tables";
import {
  execute_history_retrieve,
  get_biz_app_list,
  get_biz_base_db_list,
  get_biz_test_iter_list,
  get_test_iter_list,
} from "@/spider-api/biz-mgt";
import {getDataDevelopmentEnv,} from "@/api/test-env";

export default {
  name: 'TestdataPipeline',
  components: {Tables},
  data() {
    return {
      reLoadTable: true,
      spinShow: false,
      env_name: "",
      biz_base_db: "",
      biz_base_db_br: "",
      env_name_list: [],
      appList: [],
      bizApps: [],
      biz_br_name: "",
      biz_br_name_list: [],
      biz_base_db_list: [],
      biz_base_db_br_list: [],
      columns: [
        {
          type: 'selection',
          align: 'center'
        },
        {title: "应用名", key: "app_module_name"},
        {title: "数据库", key: "db_names", tooltip: true, tooltipMaxWidth: 900},
        {title: "线上版本", key: "online_br"},
        {title: "归档版本", key: "archive_br"},
        {
          title: "选择版本",
          key: "br_name",
          render: (h, params) => {
            let op_list = params.row.br_names.map((item) => {
              return h('Option', { // 下拉框的值
                props: {
                  value: item,
                  label: item
                }
              })
            })
            return h(
              "Select",
              {
                props: {
                  placeholder: params.row.br_name,
                  value: params.row.br_name,
                  transfer: true,
                  clearable: true
                },
                style: {
                  width: "100px"
                },
                on: {
                  "on-change": val => {
                    params.row.br_name = val // 改变下拉框赋值
                    console.log(" params.row.br_name:%o", params.row.br_name)
                    this.updateAppList[params.row.app_module_name] = params.row.br_name
                    this.appList[params.index].cur_br_name = val
                    this.appList[params.index].br_name = val
                    console.log(" updateAppList:%o", this.updateAppList)
                    this.reLoadTable = false
                    this.$nextTick(() => {
                      this.reLoadTable = true
                    })
                  }
                }
              },
              op_list
            );
          }
        }
      ],
      newest_flag: false,
      app_selection_columns: [
        {title: "应用名", key: "app_module_name"},
        {title: "部署版本", key: "br_name"},
      ],
      biz_br_name_obj_list: [],
      updateAppList: {},
      app_selection: [],
      select_version_apps: []
    }
  },
  methods: {
    // 控制颜色
    rowClassName (row, index) {
        if (row.cur_br_name && row.cur_br_name !== row.archive_br) {
          return 'custom-table-tr-style'
        }
        if (this.updateAppList[row.app_module_name] && row.archive_br !== this.updateAppList[row.app_module_name]) {
          return 'custom-table-tr-style'
        }
        return '';
    },
    selectApp(selection) {
      this.app_selection = selection
    },
    bizBrNameChangeSelect(params, flow_change) {
      this.biz_base_db = ""
      this.biz_base_db_br = ""
      this.biz_br_name = params
      for (let bizObj in this.biz_br_name_obj_list) {
        if (this.biz_br_name_obj_list[bizObj].biz_test_iter_br == params) {
          let biz_test_iter_id = ""
          for (let bizObj in this.biz_br_name_obj_list) {
            if (this.biz_br_name_obj_list[bizObj].biz_test_iter_br == this.biz_br_name) {
              biz_test_iter_id = this.biz_br_name_obj_list[bizObj].biz_test_iter_id
            }
          }
          this.get_biz_app_lists({
            biz_code: this.biz_br_name_obj_list[bizObj].biz_code,
            "biz_test_iter_id": biz_test_iter_id
          })
          this.$emit('send-data', {
            env_name: this.env_name,
            biz_br_name: this.biz_br_name,
            biz_test_iter_id: biz_test_iter_id
          })
        }
      }
      this.get_biz_base_db_lists({"biz_test_iter": params})
    },
    bizBaseDbChangeSelect(params, flow_change){
      this.biz_base_db_br = ""
      get_biz_test_iter_list({"biz_base_db": params}).then(res => {
        this.biz_base_db_br_list = res.data.data
      })
    },
    envChangeSelect(params) {
      this.env_name = params;
      let biz_test_iter_id = ""
      for (let bizObj in this.biz_br_name_obj_list) {
        if (this.biz_br_name_obj_list[bizObj].biz_test_iter_br == this.biz_br_name) {
          biz_test_iter_id = this.biz_br_name_obj_list[bizObj].biz_test_iter_id
        }
      }
      this.$emit('send-data', {
        env_name: this.env_name,
        biz_br_name: this.biz_br_name,
        biz_test_iter_id: biz_test_iter_id
      })
    },
    init_suite_info() {
      let data = {
        page: 1,
        size: 200,
        type_name: "data_development"
      }
      // getAllEnvInfo(data).then(res => {
      getDataDevelopmentEnv(data).then(res => {
        let env_list = res.data.data["results"]
        // console.log(">>>> env_list = " + env_list)
        if (env_list) {
          this.env_name_list = env_list.filter((item) => {
            return item.suite_code.indexOf('it') !== -1
          }).map((item) => {
            return {
              value: item.suite_code,
              label: item.suite_code,
            }
          })
        } else {
          this.$Message.error('环境获取失败！')
        }
      })
    },
    get_test_iters() {
      get_test_iter_list().then(res => {
        let data_list = res.data.data
        if (data_list) {
          this.biz_br_name_obj_list = data_list
          this.biz_br_name_list = data_list.map((item) => {
            return {
              value: item.biz_test_iter_br,
              label: item.biz_test_iter_br
            }
          })
        } else {
          this.$Message.error('业务分支获取失败！')
        }
      }).catch(err => {
        console.log(err)
      })
    },
    get_biz_app_lists(param) {
      get_biz_app_list(param).then(res => {
        let data_list = res.data.data
        if (data_list) {
          this.bizApps = data_list
          this.appList = data_list.filter((item) => {
            let br_name_list = []
            if (item.archive_br) {
              br_name_list = [item.archive_br]
            }
            let temp_br_name_list = item.br_name_fifteen ? item.br_name_fifteen.split(",") : []
            br_name_list.push(...temp_br_name_list)
            item.br_names = br_name_list
            return item.br_names.length > 0 && (item.biz_app_bind_type === 1 || item.biz_app_bind_type === 2)
          }).map((item) => {
            let br_name_list = [item.biz_iter_app_archive_br_name]
            item.br_name = item.biz_iter_app_archive_br_name
            if (item.archive_br) {
              br_name_list.push(item.archive_br)
            }
            if (item.online_br) {
              br_name_list.push(item.online_br)
            }
            let temp_br_name_list = item.br_name_fifteen ? item.br_name_fifteen.split(",") : []
            br_name_list.push(...temp_br_name_list)
            item.br_names = br_name_list
            if (!item.br_name) {
              item.br_name = item.br_names[0]
            }
            this.updateAppList[item.app_module_name] = item.br_name
            return item
          })
          // 过滤掉null的数据？
          this.appList.map((item) => {
            item.br_names = item.br_names.filter((item) => {
              return item
            })
          })
        } else {
          this.$Message.error('业务被测应用获取失败！')
        }
      }).catch(err => {
        console.log(err)
      })
    },
    get_biz_base_db_lists(param){
      console.log("我在这里",param)
      get_biz_base_db_list(param).then(res => {

        let data_list = res.data.data
        if (data_list) {
          this.biz_base_db_list = data_list
        } else {
          this.$Message.error('业务基础库集获取失败！')
        }
      })
    },
    execute_confirm() {
      this.$Modal.confirm({
        title: '提示',
        content: '<p>应用分支请选择归档版本，否则影响归档</p>',
        onOk: () => {
          this.saveConfig()
        }
      });
    },
    saveConfig() {
      console.log("biz_br_name:%o", this.biz_br_name)
      console.log("env_name:%o", this.env_name)
      let app_list = []
      this.app_selection.map((item) => {
        app_list.push({"module_name": item.app_module_name, "branch_name":  this.updateAppList[item.app_module_name]})
      })
      console.log("app_list:%o", app_list)
      let biz_test_iter_id = ""
      for (let bizObj in this.biz_br_name_obj_list) {
        if (this.biz_br_name_obj_list[bizObj].biz_test_iter_br == this.biz_br_name) {
          biz_test_iter_id = this.biz_br_name_obj_list[bizObj].biz_test_iter_id
        }
      }
      console.log("biz_test_iter_id:%o", biz_test_iter_id)
      console.log("biz_base_db:%o", this.biz_base_db)
      console.log("biz_base_db_br:%o", this.biz_base_db_br)

      if (!this.biz_br_name || this.biz_br_name.trim().length == 0) {
        this.$Message.warning('请选择分支')
        return false;
      }
      if (!this.env_name || this.env_name.trim().length == 0) {
        this.$Message.warning('请选择执行环境')
        return false;
      }
      if (!this.biz_base_db || this.biz_base_db.trim().length == 0) {
        this.$Message.warning('请选择基础库集')
        return false;
      }
      if (!this.biz_base_db_br || this.biz_base_db_br.trim().length == 0) {
        this.$Message.warning('请选择基础库集版本')
        return false;
      }
      if (app_list.length == 0) {
        this.$Message.warning('请至少选择一个应用及其版本')
        return false;
      }
      this.$emit('handle-loading', !this.spinShow)
      doJenkinsTestDataDeploy('test_data_init', biz_test_iter_id, this.env_name, false, app_list, this.biz_base_db, this.biz_base_db_br).then(res => {
        if (res.data.status === 'success') {
          this.$Notice.success({
            title: 'success',
            desc: '初始化命令已发出，稍后点击跳转详情'
          });
        } else {
          this.$Message.error(res.data.msg)
        }
      }).catch(err => {
        this.$Message.error(err.response.data.msg)
      }).finally(() => {
        this.$emit('handle-loading', !this.spinShow)
      })
    },
    init() {
      this.init_suite_info();
      this.get_test_iters()
    },
    goExecHistory() {
      let biz_test_iter_id = ""
      for (let bizObj in this.biz_br_name_obj_list) {
        if (this.biz_br_name_obj_list[bizObj].biz_test_iter_br == this.biz_br_name) {
          biz_test_iter_id = this.biz_br_name_obj_list[bizObj].biz_test_iter_id
        }
      }
      console.log(this.biz_br_name)
      if (biz_test_iter_id.trim().length == 0) {
        this.$Message.warning("请先选择分支")
        return false
      }
      execute_history_retrieve({
        "exec_action_type": "test_data_init",
        "biz_test_iter_id": biz_test_iter_id,
      }).then(res => {
        if (res.data.status != "success") {
          this.$Message.warning(res.data.msg)
          return false
        }
        if (res.data.data) {
          window.open(res.data.data, '_blank');
          return false
        } else {
          this.$Message.warning("没有查询到你的执行记录")
        }
      })
    }
  },
  mounted() {
    this.init()
  },
  destroyed() {
  }
}
</script>
<style lang="less" scoped>
.cutom-table {
  /deep/ .ivu-table {
    /* 自定义表格样式 */
    .custom-table-tr-style td {
      background-color: #F79F9FFF;
    }
  }
}
</style>
