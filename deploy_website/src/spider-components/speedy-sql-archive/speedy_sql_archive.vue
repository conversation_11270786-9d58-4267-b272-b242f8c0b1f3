<template>
  <Card shadow style="height: 100%">
    <p slot="title" style="width: 100%;text-align: center;">
      SQL快速通道
    </p>
    <Form
      :label-width="120"
      style="text-align: left"
    >
      <FormItem v-show="br_show" label="应用：" prop="app_name">
        <Row>
          <Col span="7">
            <Select placeholder="选择应用" v-model="app_name" filterable clearable style="width:200px"
                    @on-change="get_app_sql_branch">
              <Option v-for="item in app_info_list"
                      :value="item.module_name"
                      :key="item.module_name"
                      :label="item.module_name">
                {{ item.module_name }}
              </Option>
            </Select>
          </Col>

        </Row>
      </FormItem>
      <FormItem v-show="br_show" label="sql分支：" prop="sql_branch_name">
        <Row>
          <Col span="7">
            <Select placeholder="选择迭代" v-model="sql_branch_name" filterable clearable style="width:200px"
                    @on-change="get_app_sql_info">
              <Option v-for="item in br_name_list"
                      :value="item"
                      :key="item"
                      :label="item">
                {{ item }}
              </Option>
            </Select>
          </Col>
        </Row>
      </FormItem>

      <FormItem label-position="top">
        <Tables stripe v-model="app_sql_info_list" :columns="columns" style="width:1052px"
                @on-selection-change="selectChange" ref="my_table">
        </Tables>
      </FormItem>
      <FormItem>
        <Button type="primary" @click="showArchiveAckModal()" :loading="btnDisabled">归档</Button>
      </FormItem>
    </Form>

    <Modal v-model="modal_archive_ack">
      <p slot="header" style="color:gray;">
        <Icon type="md-clipboard"></Icon>
        <span>SQL分支归档</span>
      </p>
      <span>确定将 {{ this.sql_branch_name }} 归档?</span>
      <Row>
      </Row>
      <div slot="footer">
        <Button @click="closeArchiveAckModal">关闭</Button>
        <Button ghost type="success" @click="do_archive_apply">确定</Button>
      </div>
    </Modal>

  </Card>
</template>
<script>
import Tables from "@/components/tables";
import {getAppModule, get_app_sql_branch_list, get_app_sql_info_list, archive_sql_iter} from "@/spider-api/mgt-app";

export default {
  name: "SpeedySqlArchive",
  components: {
    Tables
  },
  data() {
    return {
      br_show: true,
      btnDisabled: false,
      app_sql_info_list: [],
      app_name: '',
      sql_branch_name: '',
      table_select_row: [],
      modal_archive_ack: false,
      db_name: '',
      sql_dict: {
        DDL: '',
        DML: ''
      },
      columns: [{
        type: 'selection',
        width: 50,
      },
        {title: "序号", key: "sql_order", width: 100},
        {title: "内容", key: "sql_content", width: 800},
        {title: "类型", key: "sql_type", width: 100},
      ],
      br_name_list: [],
      app_info_list: [],
    };
  },

  computed: {},

  methods: {
    get_app_name() {
      getAppModule().then(res => {
        this.app_info_list = res.data.data["data_list"]
      })
    },
    get_app_sql_branch() {
      this.br_name_list = []
      get_app_sql_branch_list(this.app_name).then(res => {
        this.br_name_list = res.data.data
      })
    },
    get_app_sql_info() {
      get_app_sql_info_list(this.app_name, this.sql_branch_name).then(res => {
        this.app_sql_info_list = res.data.data
      })
    },
    showArchiveAckModal() {
      // 循环取出this.app_sql_info_list中_checked为true的行记录，并存入this.table_select_row
      let table_checked_true_list = []
      for (let it in this.app_sql_info_list) {
        if (this.app_sql_info_list[it]._checked === true) {
          table_checked_true_list.push(this.app_sql_info_list[it])
        }
      }
      if (this.table_select_row.length === 0 && table_checked_true_list.length === 0) {
        alert("请选择要归档的SQL！")
      } else if (this.table_select_row.length === 0 && table_checked_true_list.length > 0) {
        this.table_select_row = table_checked_true_list
        this.modal_archive_ack = true
      } else {
        this.modal_archive_ack = true
      }
      console.log("this.table_select_row===" + JSON.stringify(this.table_select_row))
    },
    closeArchiveAckModal() {
      this.modal_archive_ack = false
    },
    do_archive_apply() {
      this.modal_archive_ack = false
      let vm = this.table_select_row
      let ddl_order_list = []
      let dml_order_list = []
      for (let it in vm) {
        if (vm[it].sql_type === "DDL") {
          ddl_order_list.push(vm[it].sql_order.toString())
        } else {
          dml_order_list.push(vm[it].sql_order.toString())
        }
      }
      let sql_dict = {"DDL": ddl_order_list, "DML": dml_order_list}
      console.log("sql_dict===" + sql_dict)
      let archive_info = {
        "business_name": "quick_sql_archive",
        "app_name": this.app_name,
        "branch_name": this.sql_branch_name,
        "sql_dict": sql_dict
      }
      archive_sql_iter(archive_info).then(res => {
        if (res.data.status === "success") {
          alert("归档成功！")
          this.init()
        } else {
          alert("归档失败！")
        }
      })
    },
    selectChange(selectRow) {
      this.table_select_row = selectRow
    },

    init() {
      this.app_name = ''
      this.get_app_name()
      this.sql_branch_name = ''
      this.app_sql_info_list = []
    },

    handleSubmit() {
      this.btnDisabled = true

    },

  },

  created() {
    this.get_app_name()
  },
};
</script>

<style scoped>
.demo-spin-icon-load {
  animation: ani-demo-spin 1s linear infinite;
}
</style>
