<template>
  <Card shadow style="height: 100%">
    <p slot="title" style="width: 100%;text-align: center;">
      测试数据开发分支管理
    </p>
    <Form
      ref="formValidate"
      :model="formValidate"

      :label-width="80"
      style="text-align: left"
    >
      <FormItem v-show="br_show" label="分支号：" prop="sql_branch_name">
        <Row>
          <Col span="7">
            <Select placeholder="填写分支号" v-model="bis_pipeline_id" filterable clearable @on-change="chooseVersion"
                    style="width:200px">
              <Option v-for="item in test_data_dev_info_list"
                      :value="item.bis_pipeline_id"
                      :key="item.bis_pipeline_id"
                      :label="item.bis_pipeline_id">
                {{ item.bis_pipeline_id }}
              </Option>
            </Select>
          </Col>
        </Row>
      </FormItem>
    </Form>
    <Table
      style="margin-top: 1em"
      border
      width="605"
      :columns="pipeline_columns"
      :data="iter_info_detail"

    ></Table>
    <Modal v-model="modal_bis_lab">
      <p slot="header" style="color:gray;">
        <Icon type="md-clipboard"></Icon>
        <span>数据标签列表</span>
      </p>
      <Tables stripe v-model="label_list" :columns="lab_columns" style="width:200px" @on-selection-change="selectChange"
              ref="my_table">
      </Tables>
      <Row>
      </Row>
      <div slot="footer">
        <Button @click="closeBisLabModal">关闭</Button>
        <Button ghost type="success" @click="updateIterBisLab">确定</Button>
      </div>
    </Modal>
  </Card>
</template>

<script>
import Tables from "@/components/tables";
import store from "@/spider-store";
import {
  getBisLabelInfoByBisPipelineId, saveIterBislab
} from '@/spider-api/pipeline'
import {
  getTestDataDevBranchInfo, getTestDataDevBranchDetailInfo, getIterBisLabInfo
} from '@/spider-api/create-iter'
import {get_test_iter_list} from "@/spider-api/biz-mgt";

export default {
  name: "TestDataBranchManager",
  components: {
    Tables
  },
  data() {
    return {
      br_show: true,
      dump_type_list: [],
      test_data_dev_info_list: '',
      modal_bis_lab: false,
      iter_info_detail: [],
      table_select_row: [],
      bis_pipeline_id: '',
      btnDisabled: false,
      bis_lab: '',
      label_list: [],
      lab_columns: [{
        type: 'selection',
        width: 50,
      },
        {title: "数据标签", key: "bis_lab_code", width: 148},
      ],
      formValidate: {
        sql_branch_name: '',
      },
      pipeline_columns: [
        {
          title: '应用名',
          key: 'module_name',
          width: 150,
          align: 'center'
        },
        {
          title: '数据开发迭代号',
          key: 'bis_pipeline_id',
          width: 150,
          align: 'center'
        },
        {
          title: '最近归档分支',
          key: 'archive_branch_name',
          width: 150,
          align: 'center'
        },
        {
          title: '创建人',
          key: 'creator',
          width: 150,
          align: 'center'
        },
      ]
    };
  },

  computed: {

    // ruleValidate () {
    //     return {
    //       sql_branch_name: [
    //         { required: true, message: '请选择分支源', trigger: 'change' }
    //       ],
    // }
    // }
  },

  methods: {
    checkBranchNameLenth() {
    },
    query_recommend_version() {
    },
    chooseVersion() {
      getTestDataDevBranchDetailInfo(this.bis_pipeline_id).then(res => {
        this.iter_info_detail = res.data.data
      })
      store.state.bis_pipeline_id = this.bis_pipeline_id
    },
    getIterInfo() {
      get_test_iter_list().then(res => {
        let data_list = res.data.data
        if (data_list) {
          this.test_data_dev_info_list = data_list.map((item) => {
            return {"bis_pipeline_id": item.biz_test_iter_id}
          })
        } else {
          this.$Message.error('业务分支获取失败！')
        }
      }).catch(err => {
        console.log(err)
      })
    },
    showBisLabModal() {
      if (this.bis_pipeline_id === '') {
        alert("请选择分支号！")
      } else {
        this.modal_bis_lab = true
        getBisLabelInfoByBisPipelineId(this.bis_pipeline_id).then(res => {
          this.label_list = res.data.data
          console.log("this.table_select_row===" + this.table_select_row)
        })
      }
    },
    closeBisLabModal() {
      this.modal_bis_lab = false
      console.log("this.table_select_row===" + this.table_select_row)
    },
    selectChange(selectRow) {
      this.table_select_row = selectRow
    },
    updateIterBisLab() {
      this.modal_bis_lab = false
      if (this.table_select_row.length === 0) {
        alert("迭代数据标签保存成功！")
      } else {
        let vm = this.table_select_row
        let selectd_bis_lab = []
        for (let it in vm) {
          selectd_bis_lab.push(vm[it].bis_lab_code)
        }
        saveIterBislab(this.bis_pipeline_id, selectd_bis_lab).then(res => {
          if (res.data.status === "success") {
            alert("迭代数据标签保存成功！")
            this.chooseVersion()
          } else {
            alert("迭代数据标签保存失败！")
          }
        })

      }
    },
    init() {
      this.getIterInfo()
    },
  }

};
</script>

<style scoped>
.demo-spin-icon-load {
  animation: ani-demo-spin 1s linear infinite;
}
</style>
