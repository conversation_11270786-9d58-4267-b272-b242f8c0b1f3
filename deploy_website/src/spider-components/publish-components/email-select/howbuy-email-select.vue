<template>
  <div>
    <i-col style="margin: 10px;" span="2">
      <slot></slot>
    </i-col>
    <Col span="10">
      <Select  v-model="selectValue" v-bind:multiple="isMultiple" filterable clearable @on-change="$emit('selectChange',isMultiple,selectValue)">
        <Option v-for="(item,index) in allFilterMails"
                :value="item"
                :label="item"
                :key="index">
          {{ item }}</Option>
      </Select>
    </Col>
  </div>
</template>

<script>

  import {
    getEmailAddresses,
  } from "@/spider-api/iter-plan";

  export default {
    name: 'EmailSelect',
    data() {
      return {
        allFilterMails: [],
        selectValue:[]
      }
    },
    props: {
      isMultiple:{
        type:Boolean,
        defaults:false
      },
      selectChange:Function
    },
    watch: {},
    methods: {
      init(){
          getEmailAddresses().then(res => {
        if (res.data.status === 'success') {
          // console.log('9999999999999999999999999999999999')
          // console.log(res.data.data)
          this.allFilterMails = res.data.data
        }
      });
      }
    },
    mounted() {

    }
  }
</script>
