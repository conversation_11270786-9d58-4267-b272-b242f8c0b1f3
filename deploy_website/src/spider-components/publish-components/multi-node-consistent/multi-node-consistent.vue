<template>
    <div>

        <span>节点是否一致：</span>
        <span style="color: green" v-if="compare_result == '一致'">{{compare_result}} </span>
        <span style="color:  #ff9900" v-else-if="compare_result == '无法检验'">{{compare_result}} </span>
        <span style="color: red" v-else v-bind:title="node_info">{{compare_result}}</span>
        <Button type="primary" shape="circle" icon="ios-refresh" size="small"
                @click="refreshNodeConsistent"></Button>
        <span style="color:#a59f9f">{{consistent_time}}</span>

      </div>
</template>

<script>
    import {formatDateHour} from '@/libs/util'
    import {getNodeConsistentInfo} from '@/log/log'
    export default {
        name: "MultipleNodeConsistent",
        props:{
          app_name:String,
          node_list:Array,
        },
        data(){
          return{
            consistent_time: '',
            compare_result: "未对比",
            node_info: '',
          }
        },
        methods:{
          refreshNodeConsistent() {
          console.info(this.node_list);
          if (this.node_list.length > 0) {

            getNodeConsistentInfo({'ip_list': this.node_list, 'app_name': this.app_name}).then(res => {
               console.log(res)
                if (res.data.status === 'success') {
                  if (res.data.data.diff_num > 0) {
                     this.compare_result = "不一致";
                     this.node_info = res.data.data.res_dict
                  }
                  else {
                    this.compare_result = "一致";
                  }
                }
                else {
                  this.$Message.error(res.data.msg);
                }
              })

              if (typeof this.node_info == "undefined" || this.node_info == null || this.node_info === "") {
                this.compare_result = "无法检验";
              }
            this.consistent_time = formatDateHour(new Date());
          }
      },
        }
    }
</script>

<style scoped>

</style>
