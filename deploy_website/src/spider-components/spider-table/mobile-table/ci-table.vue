<template>
  <div>
    <tables stripe v-model="ci_info" :columns="columns" @on-selection-change="selectChange" ref="my_table">
    </tables>
  </div>
</template>


<script>
  import Tables from "@/components/tables";
  import store from "@/spider-store"
  import {
    GetMobileCiInfo
  } from "@/spider-api/ci/mobile-ci/mobile-ci-info";

  export default {
    name: "CITable",
    components: {
      Tables,
    },
    props: {
      columns: {
        type: Array,
        default() {
          return []
        }
      },
      is_refresh: Boolean,

    },
    data() {
      return {
        env_modal_val: false,
        ci_info: [],
        bind_env_list: {},
        refresh_rate: 5,
        selected_table: [],
        is_open: true
      }
    },
    methods: {

      get_ci_info(iter_id, action_item_list, app_type_list, selectAppList) {

        this.selected_table = selectAppList;
        GetMobileCiInfo({
          'iteration_id': iter_id,
          'package_type': app_type_list,
          'action_item': action_item_list
        }).then(res => {
          let new_data = [];
          let selectRow = [];
          // 数据刷新时候处理选中数据 20210901 by 帅
          for (let row of res.data.data.publish_info) {
            let is_row_select = false
            for (let select_row of this.selected_table) {
              if (row.app_name === select_row.app_name) {
                is_row_select = true
              }
            }
            if (is_row_select) {
              row["_checked"] = true
              selectRow.push(row)
            }
            new_data.push(row)
          }
          store.state.ci_info = this.ci_info
          this.$emit('set_table_info', selectRow)
          this.selectChange(selectRow);
          this.ci_info = new_data
          if (this.is_refresh && this.is_open && this.ci_info.length > 0) {
            let vm = this;
            setTimeout(function () {
              vm.get_ci_info(iter_id, action_item_list, app_type_list, vm.selected_table)
            }, 3000)
          }
        })

      },
      selectChange(selection, row) {
        this.selected_table = selection
        store.state.ci_info = this.ci_info
        this.$emit('set_table_info', selection)
      },

    },
    destroyed() {
      this.is_open = false
    }

  }
</script>

<style scoped>

</style>
