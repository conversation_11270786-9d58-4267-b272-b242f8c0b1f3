<template>
    <Card>
        <div>
            <!--      <MultipleNodeConsistent :app_name="app_name" :node_list="node_list"-->
            <!--                              ref="multiple_node_consistent" style="width: 60%"></MultipleNodeConsistent>-->
            <!--      老功能下线 20240326 by fwm-->
            <!--      <Button @click="showBatchDeployModal" class="search-btn" type="primary" :disabled="batch_publish_btnDisabled"-->
            <!--              style="margin-right: 10px">-->
            <!--        <Icon type="search"/>-->
            <!--        批量发布-->
            <!--      </Button>-->
            <Button
                @click="showJenkinsBatchDeployModal"
                class="search-btn"
                type="primary"
                :disabled="jenkins_batch_publish_btnDisabled"
                style="margin-right: 10px"
            >
                <Icon type="search" />
                流水线批量发布
            </Button>
            <Button
                @click="getJenkinsPipelinePublishInfo"
                class="search-btn"
                type="primary"
                :disabled="jenkins_batch_publish_btnDisabled"
                style="background-color: #4CAF50; /* Green */
      border: none;
      color: white;
      text-align: center;
      text-decoration: none;
      display: inline-block;
      margin: 4px 2px;
      cursor: pointer;"
            >
                <Icon type="search" />
                jenkins跳转详情
            </Button>
            <tables stripe v-model="app_data" :columns="columns" ref="my_table" :searchable="searchable"> </tables>
        </div>
        <Modal v-model="detail_modal" title="详情" width="70%" :styles="{ height: '600px' }" footer-hide>
            <Card>
                <Row v-html="publish_detail_info"> </Row>
            </Card>
        </Modal>
        <ConfigUpdateNotice
            :model_notice="model_notice"
            @prod_node_publish="prodMultiNodeOperate"
            @reset_loading_btn="resetLoadingBtn"
            ref="config_update_notice"
        ></ConfigUpdateNotice>
        <CheckNotice
            v-model="check_notice"
            :check_notice_title="check_notice_title"
            :check_table_data="check_notice_data"
            :check_notice_msg="check_notice_msg"
        ></CheckNotice>

        <Modal v-model="roll_back_modal" title="回滚制品信息" width="50%" :footer="showFooter" :footer-hide="true">
            <div v-if="!shouldDisable" style="margin-top: 20px; margin-bottom: 20px;">
                <Table :columns="product_columns" :data="product_items"></Table>
            </div>
            <div v-if="!shouldDisable" style="margin-top: 20px; margin-bottom: 20px;">
                <Table height="200" :columns="diffValColumns" :data="dffValTableData"></Table>
            </div>
            <!-- <div v-if="!shouldDisable">
                <Form ref="formValidate" :model="formValidate" :rules="ruleValidate" :label-width="0">
                    <FormItem prop="opt_reason">
                        <Input
                            maxlength="100"
                            v-model="formValidate.opt_reason"
                            type="textarea"
                            :autosize="{ minRows: 2, maxRows: 5 }"
                            placeholder="请输入本次回滚原因"
                        />
                    </FormItem>
                </Form>
            </div> -->
            <div v-if="shouldDisable" class="button-group">
                <span style="font-size: 20px; color:red"
                    >当天无发布，不支持从平台回滚!确实需要回滚，找运维手动处理！</span
                >
            </div>
            <div class="button-container">
                <Button type="default" @click="cancelRollBack" style="margin-right: 20px;">取消</Button>
                <Button type="primary" @click="onRollBack" :disabled="shouldDisable">确认</Button>
            </div>
        </Modal>
        <!--  @on-ok="this[onOkMethod]" -->
        <Modal
            v-model="showBatchDeployModalVisible"
            :title="titleText"
            width="800"
            @on-cancel="handleBatchDeployCancel"
        >
            <div style="margin-bottom:10px">
                <div>
                    <span align="right" style="font-size: 1rem; width: 5em; display:inline-block;">环境套名： </span>
                    <CheckboxGroup v-model="selectSuiteCodeList" @on-change="selectSuiteCodeCheckbox">
                        <Checkbox v-for="item in suiteCodeList" :key="item" :label="item">{{ item }}</Checkbox>
                    </CheckboxGroup>
                </div>
                <template>
                    <div style="display: flex; flex-wrap: wrap; margin-bottom: 10px;">
                        <!--            <draggable v-model="draggable_data" @end="onDragEnd" axis="x" style="max-height: 1000px">-->
                        <template v-for="(item, index) in draggable_data">
                            <i-col :span="12" :key="index">
                                <div>
                                    <i-col>{{ item.title }}</i-col>
                                    <i-col span="8">
                                        <span style="margin-right: 10px;">环境串并行策略：</span>
                                    </i-col>
                                    <i-col span="16">
                                        <div>
                                            <RadioGroup v-model="item.isParallel">
                                                <Radio label="0">串行</Radio>
                                                <Radio label="1">并行</Radio>
                                            </RadioGroup>
                                        </div>
                                    </i-col>
                                    <i-col span="8">
                                        <span style="margin-right: 10px;">节点串并行策略：</span>
                                    </i-col>
                                    <i-col span="16">
                                        <div>
                                            <RadioGroup v-model="item.nodeParallel">
                                                <Radio label="0">串行</Radio>
                                                <Radio label="1">并行1+N</Radio>
                                                <Radio label="2">并行均分</Radio>
                                            </RadioGroup>
                                        </div>
                                    </i-col>
                                    <template>
                                        <div>
                                            <i-col style="margin: 5px 5px 5px 0px; max-height: 500px;" :span="20">
                                                <draggable
                                                    v-model="item.checkboxGroups"
                                                    @end="onDragEnd"
                                                    axis="y"
                                                    style="overflow-y: auto; height: 100%;"
                                                >
                                                    <div
                                                        v-for="(group, groupIndex) in item.checkboxGroups"
                                                        :key="groupIndex"
                                                        style="border: 1px solid #2d8cf0; padding: 5px; margin-bottom: 5px;"
                                                    >
                                                        <i-col>{{ group.suite_code }}</i-col>
                                                        <CheckboxGroup
                                                            v-model="group.ip_list"
                                                            :key="groupIndex"
                                                            style="width: 100%;text-align: left"
                                                        >
                                                            <div
                                                                v-for="option in group.options"
                                                                :key="option"
                                                                style="display: flex; align-items: center;"
                                                            >
                                                                <Checkbox :label="option">
                                                                    <span>{{ option }}</span>
                                                                </Checkbox>
                                                            </div>
                                                        </CheckboxGroup>
                                                    </div>
                                                </draggable>
                                            </i-col>
                                        </div>
                                    </template>
                                </div>
                            </i-col>
                        </template>
                        <!--            </draggable>-->
                    </div>
                    <div style="flex: 0 0 100%; text-align: left; margin-bottom: 10px;">
                        <span style="text-align: left; display: inline-block;">说明： </span>
                    </div>
                    <div style="flex: 0 0 100%; text-align: left;">
                        <span
                            >1、执行顺序：配置校验->发布校验->节点1配置更新->节点1发布+启动->节点1服务验证->节点2配置更新->节点2+启动->节点2服务验证->...</span
                        >
                    </div>
                    <div style="flex: 0 0 100%; text-align: left;">
                        <span>2、服务验证命令需要找运维配置，否则报错。</span>
                    </div>
                    <div style="flex: 0 0 100%; text-align: left;">
                        <span>3、串行发布时，同机房多环境可以上下拖拽来调整环境间的先后顺序。</span>
                    </div>
                </template>
            </div>

            <template slot="footer">
                <div>
                    <Button @click="showBatchDeployModalVisible = false">取消</Button>
                    <Button type="primary" :loading="Confloading" @click="confirmHandler">确定</Button>
                </div>
            </template>
        </Modal>
        <Modal v-model="beforeCheck" title="提示" ok-text="确认" cancel-text="取消" @on-ok="confirmCheck">
            <p class="tips_content">当前时间为交易时间，现在发布可能影响交易，请谨慎</p>
        </Modal>
        <Modal v-model="checkServe" title="提示">
            <p class="tips_content">{{ checkServeErr }}</p>
            <div slot="footer">
                <Button @click="checkServe = false">关闭</Button>
            </div>
        </Modal>
    </Card>
</template>

<script>
import Tables from '@/components/tables'
import {
    getPublishAppBindApi,
    getProdMultiNodeOperateInfoApi,
    getBatchDeployLastStatusApi,
    getIpListBySuiteCode,
    checkFirstPublish,
    postZeusConfigDiff,
    postRollback
} from '@/spider-api/prod-publish/publish-info'
import {
    prodMultiNodeOperateApi,
    GetServiceStatus,
    prodMultiNodePublishPipelineApi,
    jenkinsPipelinePublishInfo,
    BatchPublishAccessStatus,
    RollBackInfo,
    createPublishReasonApi,
    createPublishReasonNewApi
} from '@/spider-api/prod-publish/prod-operate'
import MultipleNodeConsistent from '@/spider-components/publish-components/multi-node-consistent'
import ConfigUpdateNotice from '@/spider-components/spider-notice/config-update-notice'
import CheckNotice from '@/spider-components/spider-notice/check-notice'
import store from '@/spider-store'
import draggable from 'vuedraggable'
import { getServiceResult } from '@/spider-api/iter-plan'

export default {
    name: 'PublishTable',
    components: {
        draggable,
        Tables,
        MultipleNodeConsistent,
        ConfigUpdateNotice,
        CheckNotice
    },
    props: {
        iteration_id: String,
        is_prod_refresh: Boolean
    },
    data() {
        return {
            btnType: '',
            beforeCheck: false,
            Confloading: false,
            draggable_data: [
                // {
                //   title: '灾备发布',
                //   checkboxGroups: [
                //     {
                //       suite_code: 'bs-zb',
                //       options: ['***********', '***********']
                //     },
                //     {
                //       suite_code: 'wgq-zb',
                //       options: ['***********', '***********']
                //     }
                //     ],
                //   isParallel: 0
                // },
                // {
                //   title: '产线发布',
                //   checkboxGroups: [
                //     {
                //       suite_code: 'prod',
                //       options: ['192.168.2.1', '192.168.2.2']
                //     },
                //     {
                //       suite_code: 'pd-pord',
                //       options: ['***********', '***********']
                //     }
                //     ],
                //   isParallel: 0
                // }
            ],
            check_notice: false,
            check_notice_data: [],
            check_notice_title: '校验失败',
            check_notice_msg: '',
            env_modal_val: false,
            detail_modal: false,
            ipListZb: [],
            ipListProd: [],
            searchable: true,
            model_notice: false,
            shouldDisable: false,
            showFooter: '',
            is_open: true,
            disable_publish: false,
            showBatchDeployModalVisible: false,
            selectSuiteCode: '',
            selectSuiteCodeList: [],
            suiteCodeList: [],
            btnDisabled: false,
            batch_publish_btnDisabled: true,
            jenkins_batch_publish_btnDisabled: true,
            ipData: [],
            checkbox: [],
            app_name: '',
            publish_detail_info: '',
            node_list: [],
            titleText: '批量发布',
            onOkMethod: 'handleBatchDeploy',
            jenkinsDetails: '',
            roll_back_modal: false,
            roll_bacck_ip: '',
            roll_back_suite_code: '',
            headers: [
                { text: '制品迭代', value: 'iteration_id' },
                { text: '制品分支', value: 'lib_repo_branch' },
                { text: '制品发布时间', value: 'publish_time' },
                { text: '制品构建时间', value: 'lib_build_time' },
                { text: '制品发布人', value: 'op_user' },
                { text: '选项', value: 'radio', width: 80 }
            ],
            product_items: [],
            columns: [
                {
                    title: '节点地址',
                    align: 'center',
                    key: 'node_ip',
                    width: 140,
                    render: (h, params) => {
                        let suite_code = params.row.suite_code
                        let node_env_color = '#17233d'
                        if (suite_code) {
                            if (suite_code.indexOf('-zb') != -1) {
                                node_env_color = '#19be6b'
                            } else if (suite_code.indexOf('-prod') != -1) {
                                node_env_color = '#17233d'
                            } else if (suite_code.indexOf('-hd') != -1) {
                                node_env_color = '#ff9900'
                            }
                        }

                        return h('div', [
                            h(
                                'p',
                                {
                                    style: {
                                        display: 'inline',
                                        color: node_env_color
                                    }
                                },
                                params.row.node_ip
                            ),
                            h(
                                'a',
                                {
                                    attrs: {
                                        class: 'ivu-icon ivu-icon-ios-information-circle-outline'
                                    },
                                    style: {
                                        color: node_env_color,
                                        'font-size': '18px',
                                        'margin-left': '3px'
                                    }
                                },
                                ''
                            )
                        ])
                    }
                },
                {
                    title: '环境套',
                    align: 'center',
                    width: 80,
                    key: 'suite_code',
                    tooltip: true
                },
                {
                    title: '组名',
                    align: 'center',
                    width: 100,
                    key: 'deploy_group_name',
                    tooltip: true
                },
                {
                    title: '状态',
                    align: 'center',
                    width: 100,
                    key: 'publish_status',
                    tooltip: true,
                    render: (h, params) => {
                        let stat_dict = {
                            running: '执行中',
                            success: '执行成功',
                            failure: '执行失败',
                            compile_running: '编译中',
                            publish_success: '发布成功',
                            script_failure: '校验失败',
                            aborted: '已终止',
                            warning: '未知结果'
                        }
                        let status = params.row.publish_status
                        if (status) {
                            if (stat_dict.hasOwnProperty(status)) {
                                var status_display = stat_dict[status]
                            } else {
                                var status_display = status
                            }
                            if (status.indexOf('success') != -1) {
                                return h('p', { style: { color: 'green' } }, status_display)
                            } else if (status.indexOf('failure') != -1) {
                                return h(
                                    'Tooltip',
                                    {
                                        props: {
                                            placement: 'top',
                                            content: params.row.deploy_stages,
                                            'max-width': 500,
                                            transfer: true
                                        },
                                        style: { color: 'red' }
                                    },
                                    status_display
                                )
                            } else if (status.indexOf('warning') != -1) {
                                return h('p', { style: { color: '#ff9900' } }, status_display)
                            } else {
                                return h('p', { style: { color: '#515a6e' } }, status_display)
                            }
                        }
                    }
                },
                {
                    title: '操作',
                    align: 'center',
                    key: 'operate',
                    render: (h, params) => {
                        let operate_disable_publish = params.row.batch_publish === 1
                        // this.disable_publish = false
                        console.info('----------------disable_publish---------------')
                        console.info(this.iteration_id)
                        console.info(params.row.batch_publish)
                        if (this.iteration_id === '') {
                            this.disable_publish = true
                            operate_disable_publish = true
                        }
                        // if (params.row.batch_publish == 1) {
                        //   this.disable_publish = true
                        // }

                        // if (this.app_name === 'elephant-h5' || this.app_name === 'elephant-weapp'){
                        //   this.disable_publish =false
                        // }
                        return h('div', [
                            h(
                                'Button',
                                {
                                    attrs: {
                                        class: 'ivu-btn ivu-btn-primary ivu-btn-small ivu-btn-ghost'
                                    },
                                    props: {
                                        disabled: operate_disable_publish,
                                        loading: params.row.loading_btn
                                    },
                                    style: {
                                        margin: '2px'
                                    }
                                },
                                [
                                    h(
                                        'Poptip',
                                        {
                                            props: {
                                                confirm: true,
                                                transfer: true,
                                                title: '发布 (' + params.row.node_ip + ')',
                                                size: 'small'
                                            },
                                            on: {
                                                'on-ok': () => {
                                                    // let node_list = [params.row.node_ip]
                                                    // params.row.loading_btn = true
                                                    // 增加一个发布的时候 配置更新的提示 by帅 20220328
                                                    // this.$refs.config_update_notice.open('deploy', node_list, params.row.suite_code)
                                                    // this.prodMultiNodeOperate(
                                                    //     'update_and_deploy',
                                                    //     node_list,
                                                    //     params.row.suite_code
                                                    // )
                                                    this.btnType = '1'
                                                    this.onOkHandler(params)
                                                },
                                                'on-cancel': () => {
                                                    this.model_notice = true
                                                    this.$Message.info('取消')
                                                    console.log(params.row)
                                                }
                                            }
                                        },
                                        '配置更新+发布+启动'
                                    )
                                ]
                            ),
                            h(
                                'Button',
                                {
                                    attrs: {
                                        class: 'ivu-btn ivu-btn-primary ivu-btn-small ivu-btn-ghost'
                                    },
                                    props: {
                                        disabled: operate_disable_publish,
                                        loading: params.row.loading_btn
                                    },
                                    style: {
                                        margin: '2px'
                                    }
                                },
                                [
                                    h(
                                        'Poptip',
                                        {
                                            props: {
                                                confirm: true,
                                                transfer: true,
                                                title: '发布 (' + params.row.node_ip + ')',
                                                size: 'small'
                                            },
                                            on: {
                                                'on-ok': () => {
                                                    // let node_list = [params.row.node_ip]
                                                    // params.row.loading_btn = true
                                                    // // 增加一个发布的时候 配置更新的提示 by帅 20220328
                                                    // this.$refs.config_update_notice.open(
                                                    //     'deploy',
                                                    //     node_list,
                                                    //     params.row.suite_code
                                                    // )
                                                    // // this.prodMultiNodeOperate('deploy', node_list, params.row.suite_code)

                                                    this.btnType = '2'
                                                    this.onOkHandler(params)
                                                },
                                                'on-cancel': () => {
                                                    this.model_notice = true
                                                    this.$Message.info('取消')
                                                    console.log(params.row)
                                                }
                                            }
                                        },
                                        '发布+启动'
                                    )
                                ]
                            ),

                            h(
                                'Button',
                                {
                                    attrs: {
                                        class: 'ivu-btn ivu-btn-primary ivu-btn-small ivu-btn-ghost'
                                    },
                                    props: {
                                        loading: params.row.loading_btn
                                    },
                                    style: {
                                        margin: '2px'
                                    }
                                },
                                [
                                    h(
                                        'Poptip',
                                        {
                                            props: {
                                                confirm: true,
                                                transfer: true,
                                                title: '重启 (' + params.row.node_ip + ')',
                                                size: 'small'
                                            },
                                            on: {
                                                'on-ok': () => {
                                                    let node_list = [params.row.node_ip]
                                                    params.row.loading_btn = true
                                                    // this.$refs.config_update_notice.open('restart', node_list, params.row.suite_code)
                                                    this.prodMultiNodeOperate(
                                                        'restart',
                                                        node_list,
                                                        params.row.suite_code
                                                    )
                                                },
                                                'on-cancel': () => {
                                                    this.$Message.info('取消')
                                                }
                                            }
                                        },
                                        '重启'
                                    )
                                ]
                            ),
                            h(
                                'Button',
                                {
                                    attrs: {
                                        class: 'ivu-btn ivu-btn-primary ivu-btn-small ivu-btn-ghost'
                                    },
                                    props: {
                                        // disabled: default_btn
                                        loading: params.row.loading_btn
                                    },
                                    style: {
                                        margin: '2px'
                                    }
                                },
                                [
                                    h(
                                        'Poptip',
                                        {
                                            props: {
                                                confirm: true,
                                                transfer: true,
                                                title: '停止 (' + params.row.node_ip + ')',
                                                size: 'small'
                                            },
                                            on: {
                                                'on-ok': () => {
                                                    let node_list = [params.row.node_ip]
                                                    params.row.loading_btn = true
                                                    this.prodMultiNodeOperate('stop', node_list, params.row.suite_code)
                                                },
                                                'on-cancel': () => {
                                                    this.$Message.info('取消')
                                                }
                                            }
                                        },
                                        '停止'
                                    )
                                ]
                            ),
                            h(
                                'Button',
                                {
                                    attrs: {
                                        class: 'ivu-btn ivu-btn-primary ivu-btn-small ivu-btn-ghost'
                                    },
                                    props: {
                                        // disabled: disable_btn
                                        loading: params.row.loading_btn
                                    },
                                    style: {
                                        margin: '2px'
                                    },
                                    on: {
                                        click: () => {
                                            // params.row.loading_btn = true
                                            this.roll_bacck_ip = params.row.node_ip
                                            this.roll_back_suite_code = params.row.suite_code
                                            this.RollBackInfoDetail(
                                                params.row.node_ip,
                                                this.app_name,
                                                params.row.suite_code,
                                                this.iteration_id
                                            )
                                        }
                                    }
                                },
                                [
                                    h(
                                        'Poptip',
                                        {
                                            props: {
                                                // confirm: true,
                                                transfer: true,
                                                title: '回滚 (' + params.row.node_ip + ')',
                                                size: 'small'
                                            },
                                            on: {}
                                        },
                                        '回滚'
                                    )
                                ]
                            ),
                            h(
                                'Button',
                                {
                                    attrs: {
                                        class: 'ivu-btn ivu-btn-primary ivu-btn-small ivu-btn-ghost'
                                    },
                                    props: {
                                        disabled: operate_disable_publish,
                                        loading: params.row.loading_btn
                                    },
                                    style: {
                                        margin: '2px'
                                    }
                                },
                                [
                                    h(
                                        'Poptip',
                                        {
                                            props: {
                                                confirm: true,
                                                transfer: true,
                                                title: '配置更新 (' + params.row.node_ip + ')',
                                                size: 'small'
                                            },
                                            on: {
                                                'on-ok': () => {
                                                    let node_list = [params.row.node_ip]
                                                    params.row.loading_btn = true
                                                    this.prodMultiNodeOperate(
                                                        'update',
                                                        node_list,
                                                        params.row.suite_code
                                                    )
                                                },
                                                'on-cancel': () => {
                                                    this.$Message.info('取消')
                                                }
                                            }
                                        },
                                        '配置更新'
                                    )
                                ]
                            ),
                            h(
                                'Button',
                                {
                                    attrs: {
                                        class: 'ivu-btn ivu-btn-primary ivu-btn-small ivu-btn-ghost'
                                    },
                                    props: {
                                        // disabled: this.disable_publish,
                                        // 临时方案，让elephant-h5能在产线发布页发布
                                        disabled: operate_disable_publish,
                                        loading: params.row.loading_btn
                                    },
                                    style: {
                                        margin: '2px'
                                    }
                                },
                                [
                                    h(
                                        'Poptip',
                                        {
                                            props: {
                                                confirm: true,
                                                transfer: true,
                                                title: '代码更新 (' + params.row.node_ip + ')',
                                                size: 'small'
                                            },
                                            on: {
                                                'on-ok': () => {
                                                    let node_list = [params.row.node_ip]
                                                    params.row.loading_btn = true
                                                    this.prodMultiNodeOperate(
                                                        'code_update',
                                                        node_list,
                                                        params.row.suite_code
                                                    )
                                                },
                                                'on-cancel': () => {
                                                    this.$Message.info('取消')
                                                }
                                            }
                                        },
                                        '代码更新'
                                    )
                                ]
                            ),
                            h(
                                'Button',
                                {
                                    props: {
                                        type: 'default',
                                        size: 'small'
                                    },
                                    style: {
                                        margin: '2px'
                                    },
                                    on: {
                                        click: () => {
                                            this.showDetail(params.row.publish_message)
                                        }
                                    }
                                },
                                '详情'
                            )
                        ])
                    }
                },
                {
                    title: '上线验证',
                    align: 'center',
                    key: 'operate',
                    width: 150,
                    render: (h, params) => {
                        if (this.iteration_id === '') {
                            this.disable_publish = true
                        }

                        // if (this.app_name === 'elephant-h5' || this.app_name === 'elephant-weapp'){
                        //   this.disable_publish =false
                        // }
                        return h('div', [
                            h(
                                'Button',
                                {
                                    attrs: {
                                        class: 'ivu-btn ivu-btn-primary ivu-btn-small ivu-btn-ghost'
                                    },
                                    props: {
                                        disabled: this.disable_publish,
                                        loading: params.row.loading_btn
                                    },
                                    style: {
                                        margin: '2px'
                                    },
                                    on: {
                                        click: () => {
                                            let node_list = [params.row.node_ip]
                                            this.prodMultiNodeOperate('verify', node_list, params.row.suite_code)
                                        }
                                    }
                                },
                                '服务验证'
                            ),
                            h(
                                'Button',
                                {
                                    props: {
                                        type: 'default',
                                        size: 'small'
                                    },
                                    style: {
                                        margin: '2px'
                                    },
                                    on: {
                                        click: () => {
                                            let node_ip = params.row.node_ip
                                            let suite_code = params.row.suite_code
                                            this.getServiceStatusInfo(this.app_name, node_ip, suite_code)
                                        }
                                    }
                                },
                                '应用服务状态详情'
                            )
                        ])
                    }
                }
            ],
            app_data: [],
            product_columns: [
                {
                    title: '制品迭代',
                    key: 'iteration_id'
                },
                {
                    title: '制品分支',
                    key: 'lib_repo_branch'
                },
                {
                    title: '制品发布时间',
                    key: 'publish_time'
                },
                {
                    title: '制品构建时间',
                    key: 'lib_build_time'
                },
                {
                    title: '制品发布人',
                    key: 'op_user'
                },
                {
                    title: '选项',
                    key: 'is_selected',
                    render: (h, params) => {
                        return h(
                            'Checkbox',
                            {
                                props: {
                                    value: params.row.is_selected
                                },
                                on: {
                                    'on-change': val => {
                                        console.log('val: %o', val, params)
                                        this.product_items.forEach(item => {
                                            if (item.iteration_id === params.row.iteration_id) {
                                                item.is_selected = val
                                            } else {
                                                item.is_selected = false
                                            }
                                            console.log('item.is_selected: %o', this.product_items)
                                        })
                                        // 查询diff表格数据，接口入参怎么取？
                                        this.getDiffData(params.row)
                                    }
                                }
                            },
                            ''
                        )
                    }
                }
            ],
            ruleValidate: {
                opt_reason: [
                    { required: true, message: '请输入回滚原因', trigger: 'blur' },
                    { type: 'string', min: 10, message: '最小输入10个字', trigger: 'blur' }
                ]
            },
            formValidate: {
                opt_reason: '', // 回滚原因
                module_name: '',
                suite_code: '',
                node_ip: '',
                iteration_id: ''
            },
            current_version: '',
            rollback_version: '',
            dffValTableData: [],
            copyParam: {},
            isCHange: false,
            checkServe: false,
            checkServeErr: ''
        }
    },
    computed: {
        diffValColumns() {
            return [
                {
                    title: '宙斯key',
                    key: 'key'
                },
                {
                    title: this.current_version || ' ',
                    key: 'currentVersionVal'
                },
                {
                    title: this.rollback_version || ' ',
                    key: 'rollbackVersionVal'
                }
            ]
        }
    },
    methods: {
        getDiffData(row) {
            postZeusConfigDiff({
                node_ip: row.node_ip,
                module_name: row.module_name,
                suite_code: row.suite_code,
                backup_lib_branch: row.lib_repo_branch
            }).then(res => {
                if (res.data.code === '0000') {
                    this.dffValTableData = res.data.data.config_diff_info
                    this.current_version = res.data.data.current_version
                    this.rollback_version = res.data.data.rollback_version
                }
            })
        },
        submitHandler(params) {
            if (this.btnType === '1') {
                // 配置更新+发布+启动
                let node_list = [params.row.node_ip]
                params.row.loading_btn = true
                // 增加一个发布的时候 配置更新的提示 by帅 20220328
                this.prodMultiNodeOperate('update_and_deploy', node_list, params.row.suite_code)
            } else if (this.btnType === '2') {
                // 发布+启动
                let node_list = [params.row.node_ip]
                params.row.loading_btn = true
                // 增加一个发布的时候 配置更新的提示 by帅 20220328
                this.$refs.config_update_notice.open('deploy', node_list, params.row.suite_code)
            }
        },
        confirmCheck() {
            this.submitHandler(this.copyParam)
        },
        onOkHandler(params) {
            // bugfix，交易时段是否当天首次发布
            checkFirstPublish({ iteration_id: this.iteration_id }).then(res => {
                if (res.data.status === 'success') {
                    if (res.data.data.first_publish) {
                        this.copyParam = params
                        this.beforeCheck = true
                    } else {
                        this.submitHandler(params)
                    }
                }
            })
        },
        confirmHandler() {
            this.Confloading = true
            this[this.onOkMethod]()
        },
        get_check_service_result(sid) {
            getServiceResult(sid)
                .then(res => {
                    console.log('res: %o', res)
                    if (res.data.status === 'success') {
                        var status = res.data.data.status
                        var detail = res.data.data.detail
                        if (status === 'success') {
                            const [curItem] = this.product_items.filter(item => {
                                return item.is_selected
                            })
                            // const node_list = [curItem.node_ip]
                            if (!this.isCHange) {
                                this.prodMultiNodeOperate(
                                    'rollback_update_and_deploy',
                                    [this.formValidate.node_ip],
                                    curItem.suite_code
                                )
                            }
                            this.$emit('reloadStatus')
                            this.$Spin.hide()
                        } else if (status === 'failure') {
                            this.$Spin.hide()
                            this.checkServe = true
                            this.checkServeErr = detail || '执行失败'
                        } else {
                            setTimeout(() => {
                                this.get_check_service_result(sid)
                            }, 2000)
                        }
                    } else {
                        this.$Message.error(res.data.msg)
                    }
                })
                .catch(err => {
                    this.$Message.error('接口调用异常')
                    this.$Spin.hide()
                })
        },
        onRollBack() {
            const _this = this
            createPublishReasonNewApi({
                app_name: this.formValidate.module_name,
                suite_code: this.formValidate.suite_code,
                node_ip: this.formValidate.node_ip
            })
                .then(res => {
                    if (res.data && res.data.status === 'success') {
                        const temp = JSON.parse(JSON.stringify(_this.app_data))
                        _this.roll_back_modal = false
                        temp.forEach(item => {
                            if (item.node_ip === _this.roll_bacck_ip) {
                                item.loading_btn = true
                            }
                        })
                        let node_list = [_this.roll_bacck_ip]
                        _this.app_data = temp
                        // item.loading_btn = true
                        const [curItem] = this.product_items.filter(item => {
                            return item.is_selected
                        })
                        console.log('curItem=============', curItem)
                        if (curItem.is_cur_day) {
                            _this.prodMultiNodeOperate('rollback', node_list, _this.roll_back_suite_code)
                        } else {
                            this.$Spin.show({
                                render: h => {
                                    return h('div', [
                                        h('Icon', {
                                            class: 'demo-spin-icon-load',
                                            props: {
                                                type: 'ios-loading',
                                                size: 18
                                            }
                                        }),
                                        h('div', '加载中...')
                                    ])
                                }
                            })
                            // 新增逻辑
                            postRollback({
                                iteration_id: curItem.iteration_id,
                                module_name: curItem.module_name,
                                lib_repo_version: curItem.lib_repo_version
                            }).then(res => {
                                if (res.data.code === '0000') {
                                    _this.get_check_service_result(res.data.data.sid)
                                } else if (res.data.code === '9998' && !this.isCHange) {
                                    // const node_list = [curItem.node_ip]
                                    this.prodMultiNodeOperate(
                                        'rollback_update_and_deploy',
                                        [this.formValidate.node_ip],
                                        curItem.suite_code
                                    )
                                    this.$emit('reloadStatus')
                                    this.$Spin.hide()
                                } else {
                                    this.$Spin.hide()
                                    _this.$Message.error(res.data.message)
                                    this.$emit('reloadStatus')
                                }
                            })
                        }
                    } else {
                        this.$Message.error(res.data.msg)
                    }
                })
                .catch(ex => console.log(ex))
        },
        cancelRollBack() {
            this.app_data.forEach(item => {
                if (item.node_ip === this.roll_bacck_ip) {
                    item.loading_btn = false
                    this.$set(item, 'loading_btn', false)
                }
            })
            console.log(this.app_data)
            this.roll_back_modal = false
        },
        RollBackInfoDetail(ip, app_name, suite_code, iteration_id, isCHange = false) {
            this.isCHange = isCHange
            this.formValidate.iteration_id = iteration_id
            this.formValidate.module_name = app_name
            this.formValidate.suite_code = suite_code
            this.formValidate.node_ip = ip

            RollBackInfo(ip, app_name, suite_code, iteration_id).then(res => {
                if (res.data.status === 'success') {
                    if (res.data.data.length > 0 && res.data.data[0].config_diff_detail) {
                        // 对比数据
                        const {
                            current_version = '',
                            rollback_version = '',
                            config_diff_info = []
                        } = res.data.data[0].config_diff_detail
                        this.current_version = current_version
                        this.rollback_version = rollback_version
                        this.dffValTableData = config_diff_info
                    }
                    this.product_items = res.data.data
                    if (this.product_items.length > 0) {
                        this.shouldDisable = this.product_items[0]['show_disable']
                    } else {
                        this.shouldDisable = false
                    }
                    this.roll_back_modal = true
                } else {
                    this.$Message.error(res.data.msg)
                }
            })
        },
        showBatchDeployModal() {
            getBatchDeployLastStatusApi(this.iteration_id, this.app_name).then(res => {
                if (res.data.status === 'success') {
                    this.onOkMethod = 'handleBatchDeploy'
                    this.titleText = '批量发布'
                    this.Confloading = false
                    this.showBatchDeployModalVisible = true
                    this.selectSuiteCode = ''
                    // console.log("this.app_data====" + JSON.stringify(this.app_data))
                    // 循环取this.app_data中的suite_code
                    let suiteCodeList = []
                    this.app_data.forEach(item => {
                        suiteCodeList.push(item.suite_code)
                    })
                    this.suiteCodeList = [...new Set(suiteCodeList)]
                } else {
                    this.$Message.error(res.data.msg)
                }
            })
        },
        showJenkinsBatchDeployModal() {
            this.draggable_data = []
            this.selectSuiteCodeList = []
            getBatchDeployLastStatusApi(this.iteration_id, this.app_name).then(res => {
                if (res.data.status === 'success') {
                    this.onOkMethod = 'handleJenkinsBatchDeploy'
                    this.titleText = '流水线批量发布'
                    this.Confloading = false
                    this.showBatchDeployModalVisible = true
                    this.selectSuiteCode = ''
                    let suiteCodeList = []
                    this.app_data.forEach(item => {
                        suiteCodeList.push(item.suite_code)
                    })
                    this.suiteCodeList = [...new Set(suiteCodeList)]
                } else {
                    this.$Message.error(res.data.msg)
                }
            })
        },
        handleBatchDeploy() {
            let node_list = this.checkbox
            let suite_code = this.selectSuiteCode
            if (suite_code === '') {
                this.$Message.error('请选择环境套')
                return
            }
            if (node_list.length === 0) {
                this.$Message.error('请选择ip')
                return
            }
            this.prodMultiNodeOperate('update_and_deploy_and_verify', node_list, suite_code)
        },
        handleJenkinsBatchDeploy() {
            this.showBatchDeployModalVisible = false
            // 循环取出选中的ip
            let count = 0
            let suite_and_node_dict = {}
            let all_node_list = []
            for (let item of this.draggable_data) {
                count += 1
                let suite_and_ip = {}
                console.log('draggable_data=== %o', this.draggable_data)
                for (let group of item.checkboxGroups) {
                    let node_list = []
                    for (let ip of group.ip_list) {
                        if (ip) {
                            node_list.push(ip)
                            all_node_list.push(ip)
                        }
                    }
                    if (node_list.length > 0) {
                        suite_and_ip[group.suite_code] = node_list
                    }
                }
                suite_and_node_dict[count + '-' + item.title] = suite_and_ip
                suite_and_node_dict[count + '-' + item.title]['parallel'] = item.isParallel
                suite_and_node_dict[count + '-' + item.title]['node_parallel'] = item.nodeParallel
            }
            console.log('suite_and_node_dict=== %o', suite_and_node_dict)
            this.prodMultiNodePublishPipeline('update_and_deploy_and_verify', all_node_list, suite_and_node_dict)
        },
        handleBatchDeployCancel() {
            this.showBatchDeployModalVisible = false
        },
        selectSuiteCodeCheckbox() {
            console.log('this.selectSuiteCodeList: %o', this.selectSuiteCodeList)
            getIpListBySuiteCode(this.app_name, this.selectSuiteCodeList).then(res => {
                if (res.data.status === 'success') {
                    this.draggable_data = res.data.data
                } else {
                    this.$Message.error(res.data.msg)
                }
            })
        },
        // 打开日志查看tab
        renderFunc(ip, app_name) {
            let routeData = this.$router.resolve({
                path: '/log',
                query: {
                    ip: ip,
                    app_name: app_name
                }
            })
            window.open(routeData.href, '_blank')
        },

        getAppPublishInfo(app_name) {
            let vm = this

            this.app_name = app_name
            console.info('iteration_id' + this.iteration_id)
            // 获取节点绑定信息
            getPublishAppBindApi('prod', app_name, this.iteration_id)
                .then(res => {
                    console.log(res)
                    if (res.data.status === 'success') {
                        vm.app_data = res.data.data
                        let node_list = []
                        for (let row of vm.app_data) {
                            node_list.push(row['node_ip'])
                        }
                        this.node_list = node_list
                        // 注释掉一上来 就对比节点的功能 by 帅 20220330
                        // this.$refs.multiple_node_consistent.refreshNodeConsistent()
                        this.queryStatus(app_name, node_list, 'prod')
                    } else {
                        vm.app_data = []
                        vm.$Message.error(res.data.msg)
                    }
                })
                .catch(function(err) {
                    console.log(err)
                    vm.app_data = []
                    vm.$Message.error('发生异常')
                })
        },
        getServiceStatusInfo(app_name, suite_code, ip) {
            GetServiceStatus(app_name, suite_code, ip).then(res => {
                if (res.data.status === 'success') {
                    let data = res.data.data
                    this.publish_detail_info = data.result
                } else {
                    this.publish_detail_info = res.data.msg
                }
                // let data = res.data.data
                // this.publish_detail_info = data.result
                this.detail_modal = true
            })
        },
        prodMultiNodeOperate(op_type, node_list, suite_code) {
            let data = {
                op_type: op_type,
                iteration_id: this.iteration_id,
                node_list: node_list,
                app_name: this.app_name,
                suite_code: suite_code
            }
            console.log(data)
            prodMultiNodeOperateApi(data).then(res => {
                if (res.data.status === 'success') {
                    this.$Message.info(res.data.msg)
                    this.queryStatus(this.app_name, node_list, 'prod')
                } else {
                    this.$Message.error(res.data.msg)
                }
            })
        },
        prodMultiNodePublishPipeline(op_type, node_list, suite_and_node_dict) {
            let data = {
                op_type: op_type,
                iteration_id: this.iteration_id,
                suite_and_node_dict: suite_and_node_dict,
                app_name: this.app_name
            }
            console.log(data)
            prodMultiNodePublishPipelineApi(data).then(res => {
                if (res.data.status === 'success') {
                    this.$Message.info(res.data.msg)
                    this.queryStatus(this.app_name, node_list, 'prod')
                } else {
                    this.$Message.error(res.data.msg)
                }
            })
        },
        // 重置 节点的刷新状态 by 帅 20220328
        resetLoadingBtn(node_list) {
            let vm = this
            let new_data = []
            console.log(node_list[0])
            for (let row of vm.app_data) {
                if (row['node_ip'] == node_list[0]) {
                    console.log('重置状态')
                    row['loading_btn'] = false
                }
                new_data.push(row)
            }
            vm.app_data = new_data
        },

        queryStatus(app_name, node_list, region_group) {
            let vm = this
            vm.getBatchPublishAccessStatus(app_name)
            getProdMultiNodeOperateInfoApi(this.iteration_id, app_name, node_list, region_group)
                .then(res => {
                    console.log(res)
                    let is_running = false
                    let new_data = []
                    let running_list = []
                    if (res.data.status === 'success') {
                        for (let row of vm.app_data) {
                            let status_mark = 0
                            for (let res_row of res.data.data) {
                                if (res_row['node_ip'] == row['node_ip']) {
                                    status_mark = 1
                                    row['publish_status'] = res_row['publish_status']
                                    row['publish_message'] = res_row['publish_message']
                                    row['deploy_stages'] = res_row['deploy_stages']
                                    row['request_status'] = res_row['request_status']
                                    running_list.push(row['publish_status'])

                                    if (row['publish_status'] === 'running') {
                                        row['loading_btn'] = true
                                        is_running = true
                                    } else {
                                        if (res_row['lock_status'] == true) {
                                            row['loading_btn'] = true
                                        } else {
                                            row['loading_btn'] = false
                                        }
                                        // row["loading_btn"] = false
                                    }

                                    new_data.push({ ...row, op_time: res_row['op_time'] })
                                }
                            }
                            if (status_mark == 0) {
                                new_data.push(row)
                            }
                        }
                        console.log('new_data--------------', new_data)
                        // 对new_data进行数据过滤，过滤条件：IP+应用 相同的只保留一条，保留的一条取重复数据中op_time最大的
                        let filter_data = []
                        for (let row of new_data) {
                            let existingIndex = -1

                            // 查找是否存在相同的 IP+应用 组合
                            for (let i = 0; i < filter_data.length; i++) {
                                if (
                                    filter_data[i]['node_ip'] == row['node_ip'] &&
                                    filter_data[i]['app_name'] == row['app_name']
                                ) {
                                    existingIndex = i
                                    break
                                }
                            }

                            if (existingIndex !== -1) {
                                // 存在重复数据，比较时间并保留最新的
                                if (filter_data[existingIndex]['op_time'] < row['op_time']) {
                                    // 直接替换整个对象，避免引用污染
                                    filter_data[existingIndex] = { ...row }
                                }
                            } else {
                                // 不存在重复，直接添加新数据的副本
                                filter_data.push({ ...row })
                            }
                        }
                        vm.app_data = filter_data
                        console.log('prod refresh status' + vm.is_prod_refresh)
                        console.log('prod is_running status' + is_running)
                        console.log(vm.app_data)
                        if (is_running == true && vm.is_open && vm.is_prod_refresh) {
                            console.log('存在正在运行的应用，重新查询')
                            setTimeout(function() {
                                vm.queryStatus(app_name, node_list, region_group)
                            }, 10000)
                        }
                    } else {
                        vm.$Message.error(res.data.msg)
                        // 发布异常 处理 20220330
                        this.alertErrorNotice(res.data.msg)
                    }
                })
                .catch(function(err) {
                    console.log(err)
                    vm.$Message.error('发生异常')
                })
        },
        // 提取 失败告警
        alertErrorNotice(publish_apply_message) {
            this.$Spin.hide()
            console.log(publish_apply_message)
            console.log(typeof publish_apply_message)
            // 返回的错误类型可能不是json格式 20220329 by 帅
            try {
                this.check_notice_data = JSON.parse(publish_apply_message.replace(/'/g, '"'))
            } catch (e) {
                // 无法解析 需要清除 table数据 防止无法显示msg提示
                this.check_notice_data = []
                this.check_notice_msg = publish_apply_message
                console.log(e)
            }
            this.check_notice_title = '！！！！！！！！失败告警！！！！！！！！！'
            this.check_notice = true
        },

        showDetail(data_msg) {
            console.log(data_msg)
            this.publish_detail_info = data_msg.replaceAll('\n', '<br/>')
            this.detail_modal = true
        },
        init() {
            if (this.$props.iteration_id === '' || this.$props.iteration_id === undefined) {
                this.btnDisabled = true
            } else {
                this.btnDisabled = false
            }
        },
        getBatchPublishAccessStatus(app_name) {
            let vm = this
            BatchPublishAccessStatus(app_name).then(res => {
                if (res.data.status === 'success') {
                    if (res.data.data.batch_publish == 1) {
                        vm.batch_publish_btnDisabled = false
                    } else {
                        vm.batch_publish_btnDisabled = true
                    }
                    if (
                        res.data.data.jenkins_batch_publish == 1 &&
                        this.iteration_id !== '' &&
                        this.iteration_id !== undefined
                    ) {
                        vm.jenkins_batch_publish_btnDisabled = false
                    } else {
                        vm.jenkins_batch_publish_btnDisabled = true
                    }
                } else {
                    vm.batch_publish_btnDisabled = true
                    vm.jenkins_batch_publish_btnDisabled = true
                }
            })
        },
        getJenkinsPipelinePublishInfo() {
            let vm = this
            jenkinsPipelinePublishInfo(this.app_name, this.iteration_id)
                .then(res => {
                    if (res.data.status === 'success') {
                        vm.jenkinsDetails = res.data.data
                        if (vm.jenkinsDetails) {
                            const win = window.open(vm.jenkinsDetails, '_blank')
                            if (win) {
                                win.focus()
                            } else {
                                alert('请允许弹出窗口')
                            }
                        } else {
                            alert('Jenkins 详情 URL 未设置')
                        }
                    } else {
                        vm.$Message.info(res.data.msg)
                        console.log('获取Jenkins 详情 URL 失败')
                    }
                })
                .catch(function(err) {
                    console.log(err)
                    vm.app_data = []
                    vm.$Message.error('发生异常')
                })
        },
        onDragEnd() {
            // 拖拽结束后的回调函数
        }
    },

    destroyed() {
        this.is_open = false
    },

    mounted() {
        this.init()
    }
}
</script>

<style lang="less" scoped>
.table-bordered {
    border-collapse: collapse;
}

.table-bordered th,
.table-bordered td {
    border: 1px solid black;
    padding: 10px;
}

.button-container {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 10px;
    margin-top: 20px;
    padding: 20px;
}
</style>
