<template>
  <Card shadow>

    <Row style="margin-top: 2em">
      <i-col span="3">数据开发流程</i-col>
    </Row>
    <Table
      style="margin-top: 1em"
      border
      width="1150"
      :columns="pipeline_columns"
      :data="pipeline_data"
      @on-selection-change="selectList"
    ></Table>
  </Card>
</template>

<script>
import {getLibUrlApi} from '@/spider-api/lib-repo-mgt'
import {
  doJenkinsCompileJob,
  getEnvBindInfo,
  getRecoverDumpInfo,
  saveOrUpdateEnvBind,
  saveOrUpdateEnvTestsuiteBind
} from '@/spider-api/pipeline'
import {createPipeLineLog} from '@/spider-api/pipeline-log'

export default {
  name: 'TestDataRollBack',
  data () {
    return {
      pagination: {
        page: 1,
        size: 10,
        total: 0
      },
      is_junit_dict:{},
      tempArr: [],
      test_end_date: '',
      table_data_filter: '',
      suite_code_filters: [],
      container_filters: [],
      is_qa: false,
      pipeline_id: '',
      show_img_modal: false,
      show_img_ccn:false,
      modal_archive_app_info: false,
      modal_onlineing_app_info: false,
      select_env: '',
      dump_bis_code:0,
      dump_type_list:'',
      sharding_agent_show: false,
      collect_agent_show: false,
      is_collect_agent: 1,
      select_param: '',
      select_env_list: [],
      select_env_modal_show: false,

      select_pipeline_name: '',
      bind_env_modal_show: false,
      bind_testsuit_show: false,

      testsuit_id: '',
      rowdata: '',
      modal_compile: false,
      drawer_compile: false,
      custom_job_name: '',
      pipeline_data: [],
      continuous_integration_data: [],
      switch_pipeline_info: '',
      selectApps: [],
      compile_list: [],
      compile_data: [],
      envtableData: [],
      envtablePageData: [],

      archive_app_data: [],

      onlineing_app_data: [],

      pipeline_columns: [
        {
          title: '最近执行时间',
          key: 'last_time',
          width: 150,
          align: 'center'
        },

        {
          title: '状态',
          width: 100,
          align: 'center',
          render: (h, params) => {
            let color = ''
            if (params.row.status === 'SUCCESS') {
              color = 'green'
            } else if (params.row.status === 'FALIED') {
              color = 'red'
            } else if (params.row.status === 'RUNNING') {
              color = '#ff9900'
            }
            return h('div', {
              style: {
                color: color
              },
              domProps: {
                innerHTML: params.row.status
              }
            }, '')
          }
        },
        {
          title: '流水线操作',
          align: 'center',

          render: (h, params) => {
            return h('div', [
              h(
                'Button',
                {
                  props: {
                    // disabled: "disabled"
                  },
                  style: {
                    marginRight: '1em'
                  },
                  on: {
                    click: () => {

                    }
                  }
                },
                '环境初始化'
              ),
              h(
                'Button',
                {
                  props: {},
                  style: {
                    marginRight: '1em'
                  },
                  on: {
                    click: () => {

                    }
                  }
                },
                '开发环境准备'
              ),
              h(
                'Button',
                {
                  attrs: {
                    class: 'ivu-btn  ivu-btn-primary ivu-btn-ghost',
                    title: '构建详情BlueOcean'
                  },
                  props: {},
                  style: {
                    marginRight: '1em'
                  },
                  on: {
                    click: () => {
                      window.open(
                        this.pipeline_data[params.index].jenkins_job_url
                      )
                    }
                  }
                },
                '详情'
              ),

            ])
          }
        }
      ],

    }
  },
  methods: {
    getRecoverDumpTypeInfo(){
      getRecoverDumpInfo().then(res =>{
        this.dump_type_list = res.data.data
      })
    },
    closeCompileModal () {
      this.compile_data = []
      this.modal_compile = false
    },
    pageChange (page) {
      /**
         * 分页中改变size触发的函数
         */
      this.setPagParams(page, '', '')
      let data = {
        page: this.pagination.page,
        size: this.pagination.size
      }
      this.getEnvData(data)
    },
    pageSizeChange (size) {
      this.setPagParams('', size, '')
      let data = {
        page: 1,
        size: this.pagination.size
      }
      this.getEnvData(data)
    },
    setPagParams (page, size, total) {
      /**
         * 设置分页参数
         */
      if (page !== '') {
        this.pagination.page = page
      }
      if (size !== '') {
        this.pagination.size = size
      }
      if (total !== '') {
        this.pagination.total = total
      }
    },
    getEnvData () {
      this.setPagParams('', '', this.envtableData.length)
      if (this.pagination.page * this.pagination.size >= this.envtableData.length) {
        this.envtablePageData = this.envtableData.slice((this.pagination.page - 1) * this.pagination.size)
      } else {
        this.envtablePageData = this.envtableData.slice((this.pagination.page - 1) * this.pagination.size, this.pagination.page * this.pagination.size)
      }
      let vm = this
      let t1 = window.setTimeout(function () {
        vm.init_select_env()
      }, 100)
    },
    openLibUrl(app_name, lib_type, suite_code){

      getLibUrlApi(this.pipeline_id, app_name, lib_type, suite_code) .then(res => {
          if (res.data.status === 'success') {
            this.$Message.success(res.data.data.lib_url);
            window.open(res.data.data.lib_url)
          } else {
            this.$Message.error(res.data.msg)
          }
      })
        .catch(err => {
          this.$Message.error(err.response.data.msg)
        })
    },


    save_bind_env () {
      /* 保存环境绑定 */
      this.tempArr = Array.from(new Set(this.tempArr)) // 去重
      console.info("绑定环境后的this.tempArr===="+this.tempArr)
      let envList = this.tempArr.join(',')
      console.info("转换成环境列表===="+envList)
      for (let pipelineDatum of this.pipeline_data) {
        if (pipelineDatum.job_name === this.select_pipeline_name) {
          pipelineDatum.env_list = envList
          saveOrUpdateEnvBind(pipelineDatum.pipeline_id, pipelineDatum.app_name, pipelineDatum.env_list).then(res => {
            if (res.data.status === 'success') {
              this.$Message.success(res.data.msg)
              this.getEnvInfo()
            }
          }).catch(err => {
            this.$Message.error('更新服务异常！')
          })
        }
      }
      // console.info(this.pipeline_data)
      this.bind_env_modal_show = false
    },
    save_testsuit_id () {
      /* 保存测试集id */
      let rowdata = this.rowdata
      let pipeline_id = this.pipeline_id
      let testsuite_id = this.testsuit_id
      saveOrUpdateEnvTestsuiteBind(pipeline_id, rowdata.app_name, rowdata.env, testsuite_id).then(res => {
        if (res.data.status === 'success') {
          this.$Message.success(res.data.msg)
          this.getEnvInfo()
        }
      })
        .catch(err => {
          this.$Message.error('更新测试集ID异常！')
        })
    },

    beginStrengthenContinuousIntegration () {
      let rowdata = this.rowdata
      let jacoco_type = this.jacoco_type ? 1 : 0
      let mock_type = this.mock_type ? 1 : 0
      let mock_agent_version = this.select_mock_agent_version
      console.log('jacoco_type======' + jacoco_type)
      console.log('mock_type======' + mock_type)
      let agent_info = { 'agent_name': 'howbuy-mock-agent', 'agent_version': mock_agent_version }

      rowdata['agent_info'] = JSON.stringify(agent_info)

      if (rowdata.deploy_type === 1) {
        alert('暂不支持虚机部署方式的增强CI，请切换为容器部署后尝试！')
      } else {
        this.strengthen_CI_show = false
        this.beginContinuousIntegration(rowdata, jacoco_type, mock_type)
        let job_name = this.pipeline_id + '_' + rowdata.app_name
        let opt_desc = job_name + "'推包-CI-增强'流水线" + '(' + rowdata.env + ')'
        let pipeline_id = this.pipeline_id
        createPipeLineLog(opt_desc, pipeline_id,job_name).then(res => {
        }).catch(err => {
          this.$Message.error(err.response.data.msg)
        })
      }
    },
    sortToSelectToUnshift () {
      // console.info('已选择数据置前排序');
      if (this.tempArr != null && this.tempArr.length > 0) {
        for (let tempArgument of this.tempArr) {
          this.envtableData.forEach((e, index) => {
            if (e.suite_code === tempArgument) {
              this.envtableData.splice(index, 1)
              this.envtableData.unshift(e)
            }
          })
        }
      }
    },
    table_filters () {
      // console.log(this.table_data_filter)
      let tmp_arry = []
      for (let i of this.envtableData) {
        // console.log(i.suite_code)
        if (i.suite_code && i.suite_code.indexOf(this.table_data_filter) >= 0) {
          tmp_arry.push(i)
        } else if (i.node && i.node.indexOf(this.table_data_filter) >= 0) {
          tmp_arry.push(i)
        }
      }
      this.envtablePageData = tmp_arry
    },

    getEnvInfo () {
      /* 获取环境信息详情 */
      getEnvBindInfo(this.pipeline_id)
        .then(res => {
          if (res.data.status === 'success') {
            this.envtableData = res.data.data['suite_info_list']
            this.getEnvData()
            // this.suite_code_selection = res.data.data["bind_env"];
            this.continuous_integration_data = res.data.data['push_list']
          } else {
            this.$Message.error(res.data.msg)
          }
        })
        .catch(err => {
          this.$Message.error(err.response.data.msg)
        })
    },

    closeJenkinsStatus () {
      if (this.switch_pipeline_info) {
        clearInterval(this.switch_pipeline_info)
      }
    },
    showAppConfig (val) {
      this.index = val
      this.m_app_config = this.app_detail[this.index].app_config
      this.modal_config = true
    },

    cancel () {
      this.modal_git = false
      this.modal_config = false
    },

    jenkinsCompileExec (job_name, need_mock, skip, build_mock) {
      this.$Spin.show({
        render: h => {
          return h('div', [
            h('Icon', {
              class: 'demo-spin-icon-load',
              props: {
                type: 'ios-loading',
                size: 18
              }
            }),
            h('div', '调用构建job...')
          ])
        }
      })
      if (this.dump_bis_code === undefined){
          this.dump_bis_code = 0
      }
      doJenkinsCompileJob(job_name, skip, this.is_junit_dict[job_name], this.is_mock_agent, this.dump_bis_code)
        .then(res => {
          if (res.data.status === 'success') {
            this.compile_data = res.data.data
            this.modal_compile = true
          } else {
            this.$Message.error(res.data.msg)
          }
          this.$Spin.hide()
        })
        .catch(err => {
          this.$Spin.hide()
          this.$Message.error(err.response.data.msg)
        })
    },

    selectList (selection) {
      this.selectApps = []
      for (let i = 0; i < selection.length; i++) {
        this.selectApps.push(selection[i].job_name)
      }
    },





  },
  mounted () {
    // this.initThisVue();
  },
  destroyed () {
    if (this.switch_pipeline_info) {
      clearInterval(this.switch_pipeline_info)
    }
  }
}
</script>
