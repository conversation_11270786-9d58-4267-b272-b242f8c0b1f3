<style>
.demo-spin-icon-load {
  animation: ani-demo-spin 1s linear infinite;
}
@keyframes ani-demo-spin {
  from {
    transform: rotate(0deg);
  }
  50% {
    transform: rotate(180deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
<template>
  <Card shadow>
    <Row style="margin: 1em">
      <i-col span="2">迭代版本</i-col>
      <i-col span="2">{{ this.pipeline_id }}</i-col>
    </Row>
    <br />
    <Divider orientation="left">{{suite_code_name}}</Divider>
    <CheckboxGroup v-model="suite_code_selection" style="margin: 1em">
      <Checkbox v-for="item in suite_code_list" :label="item" :key="item"></Checkbox>
    </CheckboxGroup>
    <br />
    <Button style="margin: 1em;" @click="envBindSave" type="primary">保存</Button>
    <br />
    <Row style="margin: 5px">
      <i-col style="margin: 1em" span="20">
        <Table :columns="env_columns" :data="env_data" width="800"></Table>
      </i-col>
    </Row>
    <Modal :mask-closable="false" :closable="false" width="560" v-model="modal_push" title="推送消息">
      <div v-scrolltop style="height:300px; overflow-y: auto;">
        <p v-for="(item, index) in push_log" :key="index">{{item}}</p>
      </div>
      <div slot="footer">
        <Button @click="closePushModal">关闭</Button>
      </div>
    </Modal>
  </Card>
</template>

<script>
import store from "@/spider-store";
import {
  getEnvBindInfo,
  doEnvBind,
  pushTestEnv,
  getEnvPushHistory
} from "@/spider-api/pipeline";

export default {
  name: "SpiderTestPublish",
  data() {
    return {
      suite_code_name: "测试环境套",
      suite_code_list: [],
      switch_history: "",
      env_name: "",
      app_name: "",
      suite_code_selection: [],
      pipeline_id: "",
      app_detail: [],
      modal_push: false,
      push_log: [],
      env_columns: [
        {
          title: "环境套",
          key: "env",
          width: 120
        },
        {
          title: "节点",
          key: "node",
          width: 130
        },
        {
          title: "应用名",
          key: "app_name",
          width: 160
        },
        {
          title: "操作",
          width: 120,
          render: (h, params) => {
            return h("div", [
              h(
                "Button",
                {
                  attrs: {
                    class: "ivu-btn ivu-btn-primary ivu-btn-ghost"
                  },
                  props: {},
                  style: {},
                  on: {
                    click: () => {
                      this.testEnvPublish(params.row.node, params.row.app_name, params.row.env);
                    }
                  }
                },
                "推送"
              )
            ]);
          }
        },
        {
          title: "详情",
          render: (h, params) => {
            return h("div", [
              h(
                "Button",
                {
                  attrs: {
                    class: "ivu-btn ivu-btn-success ivu-btn-ghost"
                  },
                  style: {
                    "margin-right": "1em"
                  },
                  on: {
                    click: () => {
                      // this.getEnvPushlishHistory(
                      //   params.row.node,
                      //   params.row.app_name
                      // );
                      window.open(params.row.job_url);
                    }
                  }
                },
                "详情"
              )
            ]);
          }
        }
      ],
      env_data: []
    };
  },
  methods: {
    getEnvInfo() {
      getEnvBindInfo(this.pipeline_id)
        .then(res => {
          if (res.data.status === "success") {
            this.suite_code_list = res.data.data["suite_code"];
            this.suite_code_selection = res.data.data["bind_env"];
            this.env_data = res.data.data["push_list"];
          } else {
            this.$Message.error(res.data.msg);
          }
        })
        .catch(err => {
          this.$Message.error(err.response.data.msg);
        });
    },
    initThisVue() {
      this.pipeline_id = store.state.iterationID;
      this.suite_code_list = [];
      this.getEnvInfo();
    },
    get_history_data() {
      getEnvPushHistory(this.env_name, this.pipeline_id, this.app_name)
        .then(res => {
          this.push_log = res.data.data;
        })
        .catch(err => {});
    },
    getEnvPushlishHistory(env_name, app_name) {
      this.env_name = env_name;
      this.app_name = app_name;
      this.modal_push = true;
      this.get_history_data();
      this.switch_history = setInterval(this.get_history_data, 3000);
    },
    testEnvPublish(env_name, app_name, env) {
      this.$Spin.show({
        render: h => {
          return h("div", [
            h("Icon", {
              class: "demo-spin-icon-load",
              props: {
                type: "ios-loading",
                size: 32,
                color: "#2d8cf0"
              }
            })
          ]);
        }
      });
      pushTestEnv(this.pipeline_id, env_name, app_name, env)
        .then(res => {
          this.$Spin.hide();
          if (res.data.status === "success") {
            this.$Message.success(res.data.msg);
          } else {
            this.$Message.error(res.data.msg);
          }
        })
        .catch(err => {
          this.$Spin.hide();
          this.$Message.error("发生异常");
        });
    },
    closePushModal() {
      this.modal_push = false;
      if (this.switch_history) {
        clearInterval(this.switch_history);
      }
    },
    envBindSave() {
      let env = this.suite_code_selection;
      doEnvBind(this.pipeline_id, env)
        .then(res => {
          if (res.data.status === "success") {
            this.$Message.success(res.data.msg);
          } else {
            this.$Message.error(res.data.msg);
          }
          this.getEnvInfo();
        })
        .catch(err => {
          this.$Message.error(err.response.data.msg);
          this.getEnvInfo();
        });
    }
  },
  mounted() {}
};
</script>
