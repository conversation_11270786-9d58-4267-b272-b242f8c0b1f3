import { getEnv<PERSON>ist<PERSON><PERSON>, getPublishInfo<PERSON>pi, publishSubmit<PERSON>pi } from '@/spider-api/prod-publish/publish-info'

/**
 * 节点列表项模型
 */
class NodeItemModel {
    value = ''
    isChecked = false

    merge(data) {
        if (!data) return this

        this.value = data

        return this
    }
}

/**
 * 应用列表项模型
 */
class AppItemModel {
    /**
     * 应用名
     */
    app_name = ''
    /**
     * 节点列表
     */
    node_list = []
    show_type = true

    merge(data) {
        if (!data) return this

        const { app_name, node_list, show_type } = data

        if (app_name) this.app_name = app_name
        this.show_type = show_type
        node_list &&
            node_list.forEach(nodeItem => {
                this.node_list.push(new NodeItemModel().merge(nodeItem))
            })

        return this
    }

    toJSON() {
        return {
            show_type: this.show_type,
            app_name: this.app_name,
            node_list: this.node_list.filter(nodeItem => nodeItem.isChecked).map(nodeItem => nodeItem.value)
        }
    }
}

/**
 * 环境列表项模型
 */
class EnvItemModel {
    /**
     * 环境
     */
    suite_code = ''
    /**
     * 排序
     */
    sequence = 0
    /**
     * 应用列表
     */
    app_list = []

    merge(data) {
        if (!data) return this

        const { app_list, suite_code, sequence } = data

        if (suite_code) this.suite_code = suite_code
        if (sequence) this.sequence = sequence

        app_list &&
            app_list.forEach(appItem => {
                this.app_list.push(new AppItemModel().merge(appItem))
            })

        return this
    }

    toJSON() {
        return {
            suite_code: this.suite_code,
            sequence: this.sequence,
            app_list: this.app_list.map(appItem => appItem.toJSON())
        }
    }
}

/**
 * 主/备 环境模型
 */
class NodeModel {
    /**
     * 多环境策略
     */
    strategy = '0'

    get isParallel() {
        return this.strategy === '1'
    }

    get isSerial() {
        return this.strategy === '0'
    }

    /**
     * 环境信息列表
     */
    app_suite_list = []

    merge(data) {
        if (!data) return this

        const { app_suite_list, strategy } = data

        if (strategy) this.strategy = strategy

        app_suite_list &&
            app_suite_list.forEach(envItem => {
                this.app_suite_list.push(new EnvItemModel().merge(envItem))
            })

        return this
    }

    sort() {
        this.app_suite_list.map((envItem, index) => {
            envItem.sequence = index + 1
            return envItem
        })
    }

    toJSON() {
        return {
            strategy: this.strategy,
            app_suite_list: this.app_suite_list.map(envItem => envItem.toJSON())
        }
    }
}

class ResultItemModel {
    app_name = ''
    suite_code = ''
    node_ip = ''
    reason = ''

    merge(data) {
        if (!data) return this

        const { app_name, suite_code, node_ip, reason } = data

        if (app_name) this.app_name = app_name
        if (suite_code) this.suite_code = suite_code
        if (node_ip) this.node_ip = node_ip
        if (reason) this.reason = reason

        return this
    }
}

/**
 * 发布结果数据模型 - 用于展示发布结果
 */
class ResultDataModel {
    publish_time = []
    verify_cmd = []
    publishing = []

    merge(data) {
        if (!data) return this

        const { publish_time, verify_cmd, publishing } = data

        if (publish_time && publish_time.length > 0) {
            this.publish_time = []
            publish_time.forEach(item => {
                this.publish_time.push(new ResultItemModel().merge(item))
            })
        }

        if (verify_cmd && verify_cmd.length > 0) {
            this.verify_cmd = []
            verify_cmd.forEach(item => {
                this.verify_cmd.push(new ResultItemModel().merge(item))
            })
        }

        if (publishing && publishing.length > 0) {
            this.publishing = []
            publishing.forEach(item => {
                this.publishing.push(new ResultItemModel().merge(item))
            })
        }

        return this
    }
}

/**
 * 发布抽屉视图模型
 */
export default class ServicePublishDrawerViewModel {
    /**
     * 服务发布视图模型
     */
    iteration_id = '' // 迭代ID
    /**
     * 环境列表 - 下拉框选项列表 options
     */
    envListOptions = []
    envList = []
    /**
     * 主环境
     */
    main_node = new NodeModel()
    /**
     * 备环境
     */
    reserve_node = new NodeModel()

    resultData = new ResultDataModel()

    setIterationId(iterationId) {
        if (!iterationId) return this
        this.iteration_id = iterationId
        return this
    }

    merge(data) {
        if (!data) return this

        const { main_node, reserve_node, iteration_id } = data
        this.main_node = new NodeModel()
        this.reserve_node = new NodeModel()
        if (main_node) this.main_node.merge(main_node)
        if (reserve_node) this.reserve_node.merge(reserve_node)
        if (iteration_id) this.iteration_id = iteration_id
        return this
    }

    onReserveNodeDraggableEnd() {
        this.reserve_node.sort()
    }

    onMainNodeDraggableEnd() {
        this.main_node.sort()
    }

    /**
     * 获取环境列表
     */
    fetchEnvListDataAsync() {
        getEnvListApi({
            iteration_id: this.iteration_id
        })
            .then(res => {
                this.envListOptions = res
            })
            .catch(ex => {
                console.log(ex)
            })
    }

    resetPublishInfoData() {
        this.main_node = new NodeModel()
        this.reserve_node = new NodeModel()
        this.resultData = new ResultDataModel()
    }

    /**
     * 获取发布页面详情数据
     */
    fetchPublishInfoDataAsync() {
        getPublishInfoApi({
            iteration_id: this.iteration_id,
            suite_list: this.envList
        })
            .then(res => {
                if (res && res.data && res.data.data) {
                    this.merge(res.data.data)
                }
            })
            .catch(ex => {
                console.log(ex)
            })
    }

    /**
     * 提交发布
     */
    publishSubmitAsync() {
        let data = {
            iteration_id: this.iteration_id,
            main_node: this.main_node.toJSON(),
            reserve_node: this.reserve_node.toJSON()
        }

        return publishSubmitApi(data).then(res => {
            console.info(res.data.data)
            if (res.data && res.data.status === 'success') {
                return true
            }
            if (res.data && res.data.status === 'failed' && res.data.data !== '') {
                this.resultData.merge(res.data.data)
                return false
            }
            if (res.data && res.data.status === 'failed') {
                throw new Error(res.data.msg)
            }
        })
    }
}
