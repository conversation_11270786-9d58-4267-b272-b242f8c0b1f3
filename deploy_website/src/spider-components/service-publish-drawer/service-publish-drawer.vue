<template>
  <div>
    <Drawer title="发布" width="40" :mask-closable="false" v-model="isShow" closable @on-close="handleDrawerClose">
        <div style="display: inline-flex; align-items: center">
            <Icon type="md-alert" size="18" />
            <span style="margin-right: 10px;color: #f02d55">勾选环境后，无可发布节点的原因有，① 请先点击保存模式 ② 应用要求自动化测试，没有通过会隐藏生产节点</span>
        </div>
      <div class="env-list-box">
        <CheckboxGroup v-model="servicePublishDrawerViewModel.envList" @on-change="handleCheckboxGroupChange">
          <Checkbox v-for="option, index in servicePublishDrawerViewModel.envListOptions" :key="index" :label="option.value">{{ option.label }}</Checkbox>
        </CheckboxGroup>
      </div>
      <div class="config-box">
        <div class="left">
          <div>灾备</div>
          <div class="strategy-box">
            <span>多环境策略：</span>
            <RadioGroup v-model="servicePublishDrawerViewModel.reserve_node.strategy">
              <Radio label="0">串行</Radio>
              <Radio label="1">并行</Radio>
            </RadioGroup>
          </div>
          <div v-if="servicePublishDrawerViewModel.reserve_node.isParallel" class="env-app-box">
            <div v-for="item,index in servicePublishDrawerViewModel.reserve_node.app_suite_list" :key="index" class="env-item-box">
              <div class="suite-box">{{ item.suite_code }}</div>
                <div style="display: flex; justify-content: flex-start;">
                    <Button type="text" size="small" style="margin-right: 5px;" @click="checkAll('reserve_node', index)">全选</Button>
                    <Button type="text" size="small" style="margin-right: 5px;" @click="checkAllCancel('reserve_node', index)">清空</Button>
                    <Button type="text" size="small" style="margin-right: 5px;" @click="checkOther('reserve_node', index)">反选</Button>
                </div>
              <div v-for="app,idx in item.app_list" :key="idx" class="app-item-box">
                <div class="app-name-box">{{ app.app_name }}</div>
                <div class="node-item-box">
                  <Checkbox v-for="node,i in app.node_list" :key="i" v-model="node.isChecked" :disabled="!app.show_type" class="ck">{{ node.value }}</Checkbox>
                </div>
              </div>
            </div>
          </div>
          <div v-if="servicePublishDrawerViewModel.reserve_node.isSerial" class="env-app-box serial">
            <draggable v-model="servicePublishDrawerViewModel.reserve_node.app_suite_list" @end="handleReserveDraggableEnd">
              <div v-for="item,index in servicePublishDrawerViewModel.reserve_node.app_suite_list" :key="index" class="env-item-box">
                <div class="suite-box">{{ item.suite_code }}</div>
                <div style="display: flex; justify-content: flex-start;">
                    <Button type="text" size="small" style="margin-right: 5px;" @click="checkAll('reserve_node', index)">全选</Button>
                    <Button type="text" size="small" style="margin-right: 5px;" @click="checkAllCancel('reserve_node', index)">清空</Button>
                    <Button type="text" size="small" style="margin-right: 5px;" @click="checkOther('reserve_node', index)">反选</Button>
                </div>
                <div v-for="app,idx in item.app_list" :key="idx" class="app-item-box">
                  <div class="app-name-box">{{ app.app_name }}</div>
                  <div class="node-item-box">
                    <Checkbox v-for="node,i in app.node_list" :key="i" v-model="node.isChecked" :disabled="!app.show_type" class="ck">{{ node.value }}</Checkbox>
                  </div>
                </div>
              </div>
            </draggable>
          </div>
        </div>
        <div class="right">
          <div>产线</div>
          <div class="strategy-box">
            <span>多环境策略：</span>
            <RadioGroup v-model="servicePublishDrawerViewModel.main_node.strategy">
              <Radio label="0">串行</Radio>
              <Radio label="1">并行</Radio>
            </RadioGroup>
          </div>
          <div v-if="servicePublishDrawerViewModel.main_node.isParallel" class="env-app-box">
            <div v-for="item,index in servicePublishDrawerViewModel.main_node.app_suite_list" :key="index" class="env-item-box">
              <div class="suite-box">{{ item.suite_code }}</div>
                <div style="display: flex; justify-content: flex-start;">
                    <Button type="text" size="small" style="margin-right: 5px;" @click="checkAll('main_node', index)">全选</Button>
                    <Button type="text" size="small" style="margin-right: 5px;" @click="checkAllCancel('main_node', index)">清空</Button>
                    <Button type="text" size="small" style="margin-right: 5px;" @click="checkOther('main_node', index)">反选</Button>
                </div>
              <div v-for="app,idx in item.app_list" :key="idx" class="app-item-box">
                <div class="app-name-box">{{ app.app_name }}</div>
                <div class="node-item-box">
                  <Checkbox v-for="node,i in app.node_list" :key="i" v-model="node.isChecked" :disabled="!app.show_type" class="ck">{{ node.value }}</Checkbox>
                </div>
              </div>
            </div>
          </div>
          <div v-if="servicePublishDrawerViewModel.main_node.isSerial" class="env-app-box serial">
            <draggable v-model="servicePublishDrawerViewModel.main_node.app_suite_list" @end="handleMainDraggableEnd">
              <div v-for="item,index in servicePublishDrawerViewModel.main_node.app_suite_list" :key="index" class="env-item-box">
                <div class="suite-box">{{ item.suite_code }}</div>
                <div style="display: flex; justify-content: flex-start;">
                    <Button type="text" size="small" style="margin-right: 5px;" @click="checkAll('main_node', index)">全选</Button>
                    <Button type="text" size="small" style="margin-right: 5px;" @click="checkAllCancel('main_node', index)">清空</Button>
                    <Button type="text" size="small" style="margin-right: 5px;" @click="checkOther('main_node', index)">反选</Button>
                </div>
                <div v-for="app,idx in item.app_list" :key="idx" class="app-item-box">
                  <div class="app-name-box">{{ app.app_name }}</div>
                  <div class="node-item-box">
                    <Checkbox v-for="node,i in app.node_list" :key="i" v-model="node.isChecked" :disabled="!app.show_type" class="ck">{{ node.value }}</Checkbox>
                  </div>
                </div>
              </div>
            </draggable>
          </div>
        </div>
      </div>
      <div class="demo-drawer-footer">
        <Button style="margin-right: 8px" @click="isShow = false">取消</Button>
        <Button type="primary" :disabled="this.isSubmitting" @click="handleSubmitButtonClick">发布</Button>
      </div>
      <Modal v-model="isShowResult">
        <p slot="header" style="color:gray;">
          <span> 发布结果 </span>
        </p>
        <div class="body">
          <table style="width: 100%;margin-top: 20px;margin-bottom: 20px;">
            <thead>
              <th class="row">
                <td>应用</td>
                <td>环境</td>
                <td>节点</td>
                <td>原因</td>
              </th>
            </thead>
            <tbody>
              <tr v-for="item, index in servicePublishDrawerViewModel.resultData.publish_time" v-bind:key="index" class="row">
                <td>{{ item.app_name }}</td>
                <td>{{ item.suite_code }}</td>
                <td>{{ item.node_ip }}</td>
                <td>{{ item.reason }}</td>
              </tr>
              <tr v-for="item, index in servicePublishDrawerViewModel.resultData.verify_cmd" v-bind:key="index" class="row">
                <td>{{ item.app_name }}</td>
                <td>{{ item.suite_code }}</td>
                <td>{{ item.node_ip }}</td>
                <td>{{ item.reason }}</td>
              </tr>
              <tr v-for="item, index in servicePublishDrawerViewModel.resultData.publishing" v-bind:key="index" class="row">
                <td>{{ item.app_name }}</td>
                <td>{{ item.suite_code }}</td>
                <td>{{ item.node_ip }}</td>
                <td>{{ item.reason }}</td>
              </tr>
            </tbody>
          </table>
        </div>
        <div slot="footer">
          <Button @click="isShowResult = false">关闭</Button>
        </div>
      </Modal>
    </Drawer>
    <Modal v-model="beforeCheck" title="提示" ok-text="确认" cancel-text="取消" @on-ok="confirmCheck">
        <p class="tips_content">当前时间为交易时间，现在发布可能影响交易，请谨慎</p>
    </Modal>
  </div>
</template>

<script>
import draggable from 'vuedraggable'
import ServicePublishDrawerViewModel from './servicePublishDrawerViewModel'
import { checkFirstPublish } from '@/spider-api/prod-publish/publish-info'

export default {
    props: {
        value: {
            type: Boolean,
            default: false
        },
        iterationId: {
            // 迭代ID
            type: String,
            required: true,
            default: ''
        }
    },
    components: {
        draggable
    },
    data() {
        return {
            beforeCheck: false,
            servicePublishDrawerViewModel: new ServicePublishDrawerViewModel().setIterationId(this.iterationId),
            disabledGroup: [],
            isShowResult: false,
            isSubmitting: false // 添加isSubmitting变量
        }
    },
    computed: {
        isShow: {
            get() {
                return this.value
            },
            set(val) {
                this.$emit('input', val)
            }
        }
    },
    created() {
        this.servicePublishDrawerViewModel.fetchEnvListDataAsync()
        // this.servicePublishDrawerViewModel.fetchPublishInfoDataAsync();
    },
    methods: {
        checkAll(type, index) {
            console.log('servicePublishDrawerViewModel', this.servicePublishDrawerViewModel);
            
            // 全选
            if (type === 'reserve_node') {
                this.servicePublishDrawerViewModel.reserve_node.app_suite_list[index].app_list.forEach(app => {
                    if (app.show_type) {
                        app.node_list.forEach(node => {
                            node.isChecked = true
                        })
                    }
                })
            } else if (type === 'main_node') {
                this.servicePublishDrawerViewModel.main_node.app_suite_list[index].app_list.forEach(app => {
                    if (app.show_type) {
                        app.node_list.forEach(node => {
                            node.isChecked = true
                        })
                    }
                })
            }
            
        },
        checkAllCancel(type, index) {
            // 清除
            if (type === 'reserve_node') {
                this.servicePublishDrawerViewModel.reserve_node.app_suite_list[index].app_list.forEach(app => {
                    if (app.show_type) {
                        app.node_list.forEach(node => {
                            node.isChecked = false
                        })
                    }
                })
            } else if (type === 'main_node') {
                this.servicePublishDrawerViewModel.main_node.app_suite_list[index].app_list.forEach(app => {
                    if (app.show_type) {
                        app.node_list.forEach(node => {
                            node.isChecked = false
                        })
                    }
                })
            }
        },
        checkOther(type, index) {
            // 反选
            if (type === 'reserve_node') {
                this.servicePublishDrawerViewModel.reserve_node.app_suite_list[index].app_list.forEach(app => {
                    if (app.show_type) {
                        app.node_list.forEach(node => {
                            node.isChecked = !node.isChecked
                        })
                    }
                })
            } else if (type === 'main_node') {
                this.servicePublishDrawerViewModel.main_node.app_suite_list[index].app_list.forEach(app => {
                    if (app.show_type) {
                        app.node_list.forEach(node => {
                            node.isChecked = !node.isChecked
                        })
                    }
                })
            }
        },
        submitHandler() {
            if (this.isSubmitting) {
                return // 如果正在提交中，则不处理
            }
            this.isSubmitting = true // 设置为提交中状态
            this.servicePublishDrawerViewModel
                .publishSubmitAsync()
                .then(result => {
                    if (result === false) {
                        this.isShowResult = true
                    } else {
                        this.$Message.success('触发成功，请点击Jenkins按钮跳转流水线')
                        this.isShow = false
                    }
                    this.isSubmitting = false // 设置为提交完成状态
                })
                .catch(ex => {
                    console.log(ex)
                    this.isShow = true
                    this.isSubmitting = false // 设置为提交完成状态
                    this.$Message.error(ex.message)
                })
        },
        confirmCheck() {
            this.submitHandler()
        },
        handleSubmitButtonClick() {
            // bugfix，交易时段是否当天首次发布
            checkFirstPublish({ iteration_id: this.iterationId }).then(res => {
                if (res.data.status === 'success') {
                    if (res.data.data.first_publish) {
                        this.beforeCheck = true
                    } else {
                        this.submitHandler()
                    }
                }
            })
        },
        handleMainDraggableEnd() {
            // 排序后重新设置序号
            this.servicePublishDrawerViewModel.onMainNodeDraggableEnd()
        },
        handleReserveDraggableEnd() {
            // 排序后重新设置序号
            this.servicePublishDrawerViewModel.onReserveNodeDraggableEnd()
        },
        handleCheckboxGroupChange(data) {
            if (data.length > 0) {
                this.servicePublishDrawerViewModel.fetchPublishInfoDataAsync()
            } else {
                this.servicePublishDrawerViewModel.resetPublishInfoData()
            }
        },
        handleDrawerClose() {
            this.isShow = false
        }
    }
}
</script>

<style lang="less" scoped>
.tips_content {
    margin: 20px 0;
}
.env-list-box {
    padding-left: 10px;
}
.config-box {
    display: flex;
    justify-content: space-between;
    margin-bottom: 80px;
    .left,
    .right {
        width: 50%;
        padding: 10px;
        border: 1px solid #ccc;
        border-radius: 3px;
        margin: 10px;
        background-color: #cccccccc;
        .strategy-box {
            margin: 10px 0;
        }
        .serial {
            cursor: pointer;
        }
        .env-app-box {
            .env-item-box {
                border: 1px solid #dddddddd;
                background-color: #ffffffff;
                padding: 10px;
                margin-bottom: 10px;
                .suite-box {
                    font-size: 16px;
                    font-weight: bold;
                    margin-bottom: 10px;
                }
                .app-item-box {
                    background-color: #ffffff;
                    border: 1px solid #eeeeeeee;
                    border-radius: 3px;
                    margin-bottom: 5px;
                    .app-name-box {
                        font-size: 15px;
                        font-weight: bold;
                        border-bottom: 1px solid #dddddddd;
                        padding-left: 10px;
                    }
                    .node-item-box {
                        padding-left: 10px;
                        display: flex;
                        flex-direction: column;
                        .ck {
                            margin: 5px 0;
                        }
                    }
                }
            }
        }
    }
}
.demo-drawer-footer {
    width: 40%;
    position: fixed;
    bottom: 0;
    right: 0;
    border-top: 1px solid #e8e8e8;
    padding: 10px 16px;
    text-align: right;
    background: #fff;
}
.row {
    display: flex;
    justify-content: space-around;
    align-items: center;
    min-height: 30px;
    border-bottom: 1px solid #e8e8e8e8;
    &:hover {
        background-color: #e8e8e8e8;
    }
    td {
        width: 100%;
        text-align: center;
    }
}
tbody {
    tr.row:not(:last-child) {
        border-bottom: 1px solid transparent;
    }
}
</style>
