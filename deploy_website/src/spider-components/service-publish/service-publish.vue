<!-- App.vue -->
<template>
    <Card class="service-publish-card">
        <div slot="title">
            <div class="card-header">
                <span style="font-weight: 500">发布面板: {{ this.iterationId }}</span>
            </div>
        </div>
        <div slot="extra">
            <Button
                v-if="!isExpand"
                style="float: right; padding: 3px 0; cursor: pointer"
                type="text"
                @click="isExpand = true"
                >展开</Button
            >
            <Button v-else style="float: right; padding: 3px 0; cursor: pointer" type="text" @click="isExpand = false"
                >收起 <icon type="el-icon-arrow-down"
            /></Button>
        </div>
        <div v-show="isExpand" class="card-body">
            <div class="service-actions">
                <div class="left">
                    <div>
                        <label>当前迭代通过产线申请的应用：</label>
                        <Select
                            v-model="servicePublishViewModel.suiteCode"
                            placeholder="请选择"
                            style="width: 220px"
                            @on-change="handleEnvSelectInputChange"
                        >
                            <Option
                                v-for="item in servicePublishViewModel.envListOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            />
                        </Select>
                    </div>
                </div>
                <div class="right">
                    <Button
                        type="default"
                        style="margin-right: 20px"
                        :disabled="!servicePublishViewModel.suiteCode"
                        @click="handleSaveModeButtonClick"
                        >保存模式</Button
                    >
                    <Button
                        type="primary"
                        style="margin-right: 20px"
                        :disabled="!servicePublishViewModel.suiteCode"
                        @click="handlePublishButtonClick"
                        >发布</Button
                    >
                    <Button type="default" :loading="loading" @click="showPublishDetail">查看发布详情</Button>
                    <!-- <Button type="default" style="margin-right: 20px" @click="handleJumpJenkinsUrlButtonClick"
                        >跳转Jenkins</Button
                    >
                    <div>
                        <div style="display: inline-block; margin-right: 10px; font-size: 16px">
                            <span style="margin-right: 8px">发布状态:</span>
                            <Poptip transfer trigger="hover" title="发布状态">
                                <div slot="content" style="min-height: 110px; max-height: 500px; overflow: auto">
                                    <Table
                                        :columns="servicePublishViewModel.getColumns()"
                                        :data="servicePublishViewModel.fail_data"
                                    ></Table>
                                </div>
                                <span
                                    style="color: green; cursor: pointer"
                                    v-if="servicePublishViewModel.publish_status === 'green'"
                                    >{{
                                        servicePublishViewModel.PUBLISH_STATUS_TEXT[
                                            servicePublishViewModel.publish_status
                                        ]
                                    }}</span
                                >
                                <span
                                    style="color: #d7d743; cursor: pointer"
                                    v-if="servicePublishViewModel.publish_status === 'yellow'"
                                    >{{
                                        servicePublishViewModel.PUBLISH_STATUS_TEXT[
                                            servicePublishViewModel.publish_status
                                        ]
                                    }}</span
                                >
                                <span
                                    style="color: red; cursor: pointer"
                                    v-if="servicePublishViewModel.publish_status === 'red'"
                                    >{{
                                        servicePublishViewModel.PUBLISH_STATUS_TEXT[
                                            servicePublishViewModel.publish_status
                                        ]
                                    }}</span
                                >
                            </Poptip>
                        </div>
                        <Icon
                            type="md-refresh-circle"
                            style="font-size: 24px; cursor: pointer"
                            @click="handleGetPublishAppStatusAsync"
                        />
                    </div> -->
                </div>
            </div>
            <div class="tip" style="margin: 20px 0">
                <div style="display: inline-flex; align-items: center">
                    <Icon type="md-alert" size="18" />
                    <span style="margin-right: 10px">应用可拖拽进入顺序</span>
                </div>
                <div style="display: inline-flex; align-items: center">
                    <Icon type="md-alert" size="18" />
                    <span>双击顺序中的应用可修改发布模式</span>
                </div>
            </div>
            <div
                v-if="servicePublishViewModel.suiteCode"
                :class="isDragged ? 'service-app-list dragged' : 'service-app-list'"
                @dragover="allowDrop"
            >
                <div
                    v-for="(item, index) in servicePublishViewModel.appList"
                    :key="index"
                    class="item"
                    draggable="true"
                    @dragstart="drag($event, item)"
                >
                    <div>{{ item.app_name }} - {{ item.node_count }}节点</div>
                    <div>【{{ PublishModelEnumOptions[item.publish_mode] }}】</div>
                </div>
            </div>
            <div v-if="servicePublishViewModel.suiteCode" class="service-publish-config">
                <div class="main-container" @dragover="allowDrop">
                    <div
                        v-for="(container, idx) in servicePublishViewModel.containers"
                        :key="container.id"
                        class="container-box"
                        @drop="drop($event, container)"
                        @dragover="allowDrop"
                    >
                        <div>顺序{{ ++idx }}</div>
                        <div :class="isDragged ? 'container dragged' : 'container'">
                            <div
                                v-for="(item, index) in container.items"
                                :key="index"
                                class="item"
                                draggable="true"
                                @dragstart="drag($event, item, container)"
                                @dragend="dragEnd"
                                @dblclick="handleItemDblClick(item, container)"
                            >
                                <div>{{ item.app_name }} - {{ item.node_count }}节点</div>
                                <div>【{{ PublishModelEnumOptions[item.publish_mode] }}】</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 发布模式更改 -->
        <Modal v-model="isDialogVisible" v-if="publishModelItem">
            <div slot="header">
                <span style="font-size: 18px; font-weight: bold">
                    发布模式更改：应用{{ publishModelItem && publishModelItem.app_name }}
                    {{ servicePublishViewModel.suiteCodeName }}
                </span>
            </div>
            <div class="modal-body">
                <div class="form-item">
                    <span class="label mode">发布模式：</span>
                    <Select v-model="publishModelItem.publish_mode" placeholder="请选择" style="width: 220px">
                        <!-- 行情好，防止产线节点性能扛不住，暂时注释掉1+N模式 20241018 by fwm -->
                        <!-- <Option :value="PublishModelEnum.ONE_PLUS_MORE">1+N</Option> -->
                        <Option :value="PublishModelEnum.SERIAL">串行</Option>
                        <Option :value="PublishModelEnum.EQUAL">均分</Option>
                    </Select>
                </div>
                <div class="form-item">
                    <span class="label">应用节点：</span>
                    <div class="node-list">
                        <span v-for="(node, index) in publishModelItem.node_list" :key="index">
                            {{ node }}
                        </span>
                    </div>
                </div>
            </div>
            <div slot="footer">
                <Button type="primary" @click="handleModalSubmitButtonClick">确认</Button>
            </div>
        </Modal>
        <div v-if="isShowDrawer">
            <ServicePublishDrawer v-model="isShowDrawer" :iterationId="iterationId" />
        </div>
        <Modal v-model="beforeCheck" title="提示" ok-text="确认" cancel-text="取消">
            <p>Something...</p>
            <p>Something...</p>
            <p>Something...</p>
        </Modal>
        <!-- 发布详情 -->
        <Modal
            v-model="modalVisible"
            @on-ok="clearTableData"
            @on-cancel="clearTableData"
            width="800"
            title="发布结果（发布失败，请先进入Jenkins查看AI助手）"
        >
            <Table height="500" :columns="tableColumns" :data="tableData"></Table>
            <div slot="footer">
                <Button type="primary" @click="clearTableData">关闭</Button>
            </div>
        </Modal>
    </Card>
</template>

<script>
import ServicePublishViewModel, { PublishModelEnum, PublishModelEnumOptions } from './ServicePublishViewModel.js'
import ServicePublishDrawer from '@/spider-components/service-publish-drawer/service-publish-drawer.vue'
import { queryPublishDetail, get_auto_test_result } from '@/spider-api/prod-publish/publish-info'

export default {
    props: {
        iterationId: {
            // 迭代ID
            type: String,
            required: true,
            default: ''
        }
    },
    components: {
        ServicePublishDrawer
    },
    data() {
        return {
            modalVisible: false,
            loading: false,
            tableColumns: [
                {
                    title: '应用',
                    key: 'app_name'
                },
                {
                    title: '环境',
                    key: 'suite_code'
                },
                {
                    title: '状态',
                    key: 'status',
                    render: (h, params) => {
                        let color = '#ff9900' // 默认橙色
                        const status = params.row.status

                        // 根据状态设置颜色
                        if (status && status === 'success') {
                            color = '#19be6b' // 绿色
                        } else if (status && status === 'failure') {
                            color = '#ed4014' // 红色
                        }

                        return h(
                            'span',
                            {
                                style: {
                                    color: color,
                                    fontWeight: 'bold'
                                }
                            },
                            params.row.status
                        )
                    }
                },
                {
                    title: '发布时间',
                    key: 'opt_time'
                },
                {
                    title: '发布人',
                    key: 'opt_user'
                },
                {
                    title: '详情',
                    key: 'detail',
                    render: (h, params) => {
                        if (params.row.detail_type === 'text') {
                            return h(
                                'Tooltip',
                                {
                                    props: {
                                        content: params.row.detail,
                                        placement: 'top', // 提示框出现的位置
                                        transfer: true // 将提示框放置于 body 内
                                    }
                                },
                                params.row.detail.length > 15
                                    ? params.row.detail.substring(0, 15) + '...'
                                    : params.row.detail
                            )
                        } else {
                            return h(
                                'a',
                                {
                                    attrs: {
                                        href: params.row.detail,
                                        target: '_blank'
                                    }
                                },
                                'Jenkins详情'
                            )
                        }
                    }
                }
            ],
            tableData: [],
            servicePublishViewModel: new ServicePublishViewModel().setIterationId(this.iterationId),
            isExpand: true, // 是否收起展开发布配置面板
            isDragged: false,
            draggedItem: null,
            draggedFromContainer: null,
            isDialogVisible: false, // 发布模式更改弹窗是否显示
            publishModelItem: null, // 当前发布模式
            PublishModelEnum: PublishModelEnum,
            PublishModelEnumOptions: PublishModelEnumOptions,
            isShowDrawer: false, // 是否展示抽屉页面
            beforeCheck: false,
            jenkinsUrl: '',
            timer: null, // 定时器
            queryTimer: null // 定时器
        }
    },
    created() {
        // 监听全局的drop事件
        // window.addEventListener('drop', this.dropOutside);
        // 请求接口获取当前环境列表
        // console.info('进入发布页')
        this.servicePublishViewModel.fetchEnvListDataAsync()
        clearInterval(this.timer)
        this.timer = null
        // this.setIntervalGetPublishAppStatusAsync()
    },
    beforeDestroy() {
        // 组件销毁时移除事件监听
        // window.removeEventListener('drop', this.dropOutside);
        clearInterval(this.timer)
        this.timer = null
        clearInterval(this.queryTimer)
        this.queryTimer = null
    },
    methods: {
        clearTableData() {
            this.tableData = []
            this.loading = false
            this.modalVisible = false
            clearInterval(this.queryTimer)
            this.queryTimer = null
        },
        querySetInterval() {
            this.queryTimer = setInterval(() => {
                queryPublishDetail({ iteration_id: this.iterationId }).then(res => {
                    if (res.data.status === 'success') {
                        this.tableData = res.data.data
                    }
                })
            }, 30000)
        },
        showPublishDetail() {
            this.loading = true
            queryPublishDetail({ iteration_id: this.iterationId })
                .then(res => {
                    if (res.data.status === 'success') {
                        this.tableData = res.data.data
                        this.modalVisible = true
                        this.querySetInterval()
                    } else {
                        this.$Message.error(res.data.msg)
                    }
                })
                .finally(() => {
                    this.loading = false
                })
        },
        // async handleGetPublishAppStatusAsync() {
        //     clearInterval(this.timer)
        //     this.timer = null
        //     await this.servicePublishViewModel.fetchGroupPublishAppStatusAsync({ iteration_id: this.iterationId })

        //     await this.setIntervalGetPublishAppStatusAsync()
        // },
        // setIntervalGetPublishAppStatusAsync() {
        //     this.timer = setInterval(() => {
        //         this.servicePublishViewModel.fetchGroupPublishAppStatusAsync({ iteration_id: this.iterationId })
        //     }, 20000)
        // },
        handleJumpJenkinsUrlButtonClick() {
            this.jenkinsUrl = ''
            this.servicePublishViewModel.fetchJenkinsUrlDataAsync({ iteration_id: this.iterationId }).then(res => {
                if (res.data.status === 'success') {
                    this.jenkinsUrl = res.data.data
                    if (this.jenkinsUrl) {
                        window.open(this.jenkinsUrl)
                        return
                    }
                }
                this.$Message.warning('流水线正在生成中，请稍后')
            })
        },
        handlePublishButtonClick() {
            get_auto_test_result({ iteration_id: this.iterationId }).then(res => {
                if (res.data.status === 'failed') {
                    this.$Modal.confirm({
                        title: '提示',
                        content: res.data.msg,
                        onOk: () => {
                            this.isShowDrawer = true
                        }
                    })
                } else {
                    this.isShowDrawer = true
                }
            })
        },
        handleSaveModeButtonClick() {
            try {
                this.servicePublishViewModel.savePublishConfigApiAsync()
                this.$Message.success('保存成功')
            } catch (ex) {
                // console.error(ex.message)
                this.$Message.error(ex.message)
            }
        },
        handleModalSubmitButtonClick() {
            this.isDialogVisible = false
        },
        handleItemDblClick(item, container) {
            this.publishModelItem = item
            this.isDialogVisible = true
        },
        // 环境下拉框选择事件
        handleEnvSelectInputChange(value) {
            // console.info('this.iterationId')
            // console.info(this.iterationId)
            this.servicePublishViewModel.fetchPublishAppListByEnvDataAsync(this.iterationId)
        },
        allowDrop(event) {
            event.preventDefault()
        },
        drag(event, item, container) {
            this.isDragged = true
            this.draggedItem = item
            this.draggedFromContainer = container
            event.dataTransfer.setData('item', JSON.stringify(item))
            if (container) {
                event.dataTransfer.setData('containerId', container.id)
            }
        },
        drop(event, targetContainer) {
            event.preventDefault()
            this.isDragged = false
            const itemData = JSON.parse(event.dataTransfer.getData('item'))
            const sourceContainerId = event.dataTransfer.getData('containerId')
            const sourceContainer = this.servicePublishViewModel.containers.find(
                container => container.id === Number(sourceContainerId)
            )
            // 如果没有源Container即从应用列表拖拽过来
            if (!sourceContainerId && sourceContainerId !== '0') {
                itemData.step_num = targetContainer.id + 1
                targetContainer.addItem(itemData)
                this.servicePublishViewModel.removeAppItem(itemData)
            } else {
                if (sourceContainer && targetContainer && sourceContainer.id !== targetContainer.id) {
                    // 从源容器移除item
                    sourceContainer.removeItem(itemData)
                    // 添加item到目标容器
                    itemData.step_num = targetContainer.id + 1
                    targetContainer.addItem(itemData)
                }
            }
            this.draggedItem = null
            this.draggedFromContainer = null
        },
        dragEnd(event) {
            this.isDragged = false
            if (this.draggedItem && this.draggedFromContainer) {
                const mainContainer = this.$el.querySelector('.main-container')
                const mainContainerRect = mainContainer.getBoundingClientRect()
                const isOutside =
                    event.clientX < mainContainerRect.left ||
                    event.clientX > mainContainerRect.right ||
                    event.clientY < mainContainerRect.top ||
                    event.clientY > mainContainerRect.bottom

                if (isOutside) {
                    this.draggedFromContainer.removeItem(this.draggedItem)
                    this.servicePublishViewModel.addAppItem(this.draggedItem)
                }

                this.draggedItem = null
                this.draggedFromContainer = null
            }
        }
        // dropOutside(event) {
        // }
    }
}
</script>

<style lang="less" scoped>
.service-publish-card {
    background-color: #fff;
    margin-bottom: 20px;
    border: 1px solid #ddd;
    height: auto;
    display: block;
    .card-header {
        min-height: 40px;
        background-color: #f8f8f9;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 10px;
        font-size: 14px;
    }
    .card-body {
        padding: 10px;
        background-color: #fff;
        margin-bottom: 20px;

        .service-actions {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            .left {
                margin-right: 80px;
            }
            .right {
                display: inline-flex;
                justify-content: space-between;
                align-items: center;
            }
        }

        .service-app-list {
            display: flex;
            padding: 10px;
            overflow: auto;
            min-height: 80px;
            margin-bottom: 10px;
            margin-top: 10px;
        }

        .service-publish-config {
            overflow: auto;
        }

        .dragged {
            border: 1px dashed red;
        }
    }
}
.main-container {
    display: flex;
    border: 1px solid #00000014;
    border-radius: 4px;
    padding: 10px;
}
.container-box {
    text-align: center;
}
.container {
    border: 1px solid #ccc;
    margin: 5px;
    padding: 10px;
    min-width: 200px;
    min-height: 345px;
    max-height: 345px;
    overflow-y: auto;
}
.item {
    border: 1px solid #00000008;
    background-color: #ddd;
    border-radius: 4px;
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.1);
    padding: 5px;
    margin: 5px;
    cursor: grab;
    min-width: 140px;
}

.modal-body {
    display: flex;
    flex-direction: column;
    margin: 20px;
    padding: 0 20px;
    .form-item {
        display: inline-flex;
        &:not(:last-child) {
            margin-bottom: 20px;
        }
        .label {
            width: 80px;
        }
        .mode {
            display: inline-flex;
            align-items: center;
        }
        .node-list {
            display: inline-flex;
            flex-direction: column;
            span:not(:last-child) {
                margin-bottom: 10px;
            }
        }
    }
}
</style>

<style lang="less">
.service-publish-card {
    .ivu-card-body {
        padding: 0;
    }
    .ivu-card-extra {
        height: 40px;
        top: 0px;
        display: inline-flex;
        align-items: center;
    }
}
.ivu-tooltip-inner {
    max-width: 300px; /* 设置最大宽度 */
    white-space: normal; /* 允许文本换行 */
    word-wrap: break-word; /* 长单词换行 */
}
</style>
