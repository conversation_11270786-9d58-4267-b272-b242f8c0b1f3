import {
    getEnvListApi,
    getPublishAppListByEnvApi,
    savePublishConfigApi,
    getJenkinsUrlApi,
    getGroupPublishAppStatusApi
} from '@/spider-api/prod-publish/publish-info'

// 顺序
class Container {
    constructor(id) {
        this.id = id
        this.items = []
    }

    addItem(item) {
        this.items.push(item)
    }

    removeItem(item) {
        this.items = this.items.filter((i) => i.app_name !== item.app_name)
    }
}

// 应用
class Item {
    /**
     * 应用名称
     */
    app_name = ''
    /**
     * 当前顺序
     */
    step_num = 0
    /**
     * 发布模式
     */
    publish_mode = ''
    /**
     * 应用节点
     */
    node_list = []

    get node_count() {
        return this.node_list.length
    }

    merge(data) {
        if (!data) return this

        Object.assign(this, data)

        return this
    }
}

class FailDataModel {
    /**
     * 应用名
     */
    app_name = ''
    /**
     * 节点
     */
    ip = ''
    /**
     * 失败原因
     */
    message = ''
    /**
     * 状态
     */
    status = ''
    /**
     * 环境
     */
    suite_code = ''

    merge(data) {
        if (!data) return this

        Object.assign(this, data)

        return this
    }
}

/**
 * 服务发布视图模型
 */
export default class ServicePublishViewModel {
    iterationId = '' // 迭代ID
    /**
     * 环境列表 - 下拉框选项列表 options
     */
    envListOptions = []

    suiteCode = '' // 选中的环境

    get suiteCodeName() {
        let item = this.envListOptions.find((i) => i.value === this.suiteCode)
        return (item && item.label) || ''
    }

    appList = [] // 应用列表
    /**
     * 顺序列表
     */
    containers = [] // 顺序列表 - 最后保存数据会保存这个'

    publish_status = '' // 发布状态

    fail_data = [] // 失败数据

    PUBLISH_STATUS_TEXT = {
        yellow: '发布中',
        red: '发布失败',
        green: '发布成功'
    }

    getColumns() {
        return [
            {
                title: '应用名称',
                key: 'app_name',
                width: '120px'
            },
            {
                title: '节点',
                key: 'ip',
                width: '120px'
            },
            {
                title: '环境',
                key: 'suite_code',
                width: '80px'
            },
            {
                title: '状态',
                key: 'status',
                width: '80px'
            },
            {
                title: '结果详情',
                key: 'message',
                width: '500px'
            }
        ]
    }

    removeAppItem(item) {
        this.appList = this.appList.filter((i) => i.app_name !== item.app_name)
    }
    addAppItem(item) {
        this.appList.push(item)
    }

    /**
     * 根据当前选择的环境获取应用信息
     */
    fetchPublishAppListByEnvDataAsync(iterationId) {
        this.appList = []
        this.containers = []
        getPublishAppListByEnvApi({
            iteration_id: iterationId,
            suite_code: this.suiteCode
        })
            .then((res) => {
                this.appList = []
                res &&
                    res.data &&
                    res.data.data &&
                    res.data.data
                        .filter((item) => item.step_num === 0)
                        .forEach((item) => {
                            this.appList.push(new Item().merge(item))
                        })
                res &&
                    res.data &&
                    res.data.data &&
                    res.data.data.forEach((item, index) => {
                        this.containers.push(new Container(index))
                    })
                console.log(this.containers, 'this.containers')

                res &&
                    res.data &&
                    res.data.data &&
                    res.data.data
                        .filter((item) => item.step_num !== 0)
                        .forEach((item) => {
                            let container = this.containers.find((i) => Number(i.id) + 1 === item.step_num)
                            if (container) {
                                container.addItem(new Item().merge(item))
                            }
                        })
            })
            .catch((ex) => {
                console.log(ex)
            })
    }

    setIterationId(iterationId) {
        if (!iterationId) return this
        this.iterationId = iterationId
        return this
    }

    /**
     * 获取环境列表
     */
    fetchEnvListDataAsync() {
        console.log('查询环境列表')
        getEnvListApi({
            iteration_id: this.iterationId
        })
            .then((res) => {
                this.envListOptions = res
            })
            .catch((ex) => {
                console.log(ex)
            })
    }

    /**
     * 保存
     */
    savePublishConfigApiAsync() {
        let appNodeDict = []
        this.containers.forEach((container) => {
            if (container.items.length > 0) {
                container.items.forEach((item) => {
                    if (item) {
                        if (!item.publish_mode) {
                            console.log('应用未选择发布模式')
                            throw new Error(
                                '应用' + item.app_name + '没有选择发布模式,可双击顺序中的应用可修改发布模式'
                            )
                        }
                        appNodeDict.push({
                            app_name: item.app_name,
                            publish_mode: item.publish_mode,
                            step_num: item.step_num
                        })
                    }
                })
            }
        })
        const params = {
            suite_code: this.suiteCode,
            iteration_id: this.iterationId,
            app_node_dict: appNodeDict
        }
        savePublishConfigApi(params)
            .then((res) => {
                console.log(res, 'res')
                if (res.data && res.data.status === 'failed') {
                    throw new Error('保存失败')
                }
            })
            .catch((ex) => {
                console.log(ex)
                throw new Error(ex)
            })
    }

    /**
     * 获取Jenkins地址
     */
    fetchJenkinsUrlDataAsync({ iteration_id }) {
        console.log('查询Jenkins地址')
        return getJenkinsUrlApi({
            iteration_id: iteration_id || this.iteration_id
        })
    }

    /**
     * 获取批量发布状态
     */
    fetchGroupPublishAppStatusAsync({ iteration_id }) {
        getGroupPublishAppStatusApi({
            iteration_id: iteration_id || this.iteration_id
        })
            .then((res) => {
                if (res.data.status === 'success') {
                    if (res.data.data) {
                        if (res.data.data.publish_status) this.publish_status = res.data.data.publish_status
                        if (res.data.data.fail_data && res.data.data.fail_data.length > 0) {
                            this.fail_data = []
                            res.data.data.fail_data.forEach((item) => {
                                this.fail_data.push(new FailDataModel().merge(item))
                            })
                        }
                    }
                }
            })
            .catch((ex) => console.error(ex))
    }
}

export const PublishModelEnum = {
    SERIAL: '0',
    ONE_PLUS_MORE: '1',
    EQUAL: '2'
}

export const PublishModelEnumOptions = {
    [PublishModelEnum.ONE_PLUS_MORE]: '1+N',
    [PublishModelEnum.SERIAL]: '串行',
    [PublishModelEnum.EQUAL]: '均分'
}
