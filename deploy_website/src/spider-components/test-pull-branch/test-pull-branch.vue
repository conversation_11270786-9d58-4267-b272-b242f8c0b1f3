<template>
  <Card shadow style="height: 100%;position: relative;">

    <Row style="margin-left: 5px;display: flex;align-items: center;">
      <i-col style="margin: 5px" span="2">
        <Icon type="ios-link-outline" style="margin-right: 5px"/>
        <span style="text-align: left; display: inline-block; width:60px;">业务类型&nbsp;</span>
      </i-col>
      <i-col style="margin: 5px  5px  5px 0px;" span="4">
        <Select placeholder="填写业务类型"  size="small" v-model="formValidate.biz_code" filterable clearable style="width:300px"
                @on-change="getBaseDbInfo">
          <Option v-for="item in bis_name_list"
                  :value="item.biz_code"
                  :key="item.biz_code"
                  :label="item.biz_name">
            {{ item.biz_name }}
          </Option>
        </Select>
      </i-col>
    </Row>
    <Row style="margin-left: 5px;display: flex;align-items: center;">
      <i-col style="margin: 5px" span="2">
        <Icon type="ios-trending-up" style="margin-right: 5px"/>
        <span style="text-align: left; display: inline-block; width:120px;">分支号(禁用中文)&nbsp;</span>
      </i-col>
      <i-col style="margin: 5px  5px  5px 0px;" span="4">
        <Input v-model="formValidate.biz_test_iter_br" size="small" :maxlength="50" placeholder="填写分支名称" style="width:300px"
               @on-blur='checkBranchNameLenth'/>
      </i-col>
    </Row>

    <Row style="margin-left: 5px;display: flex;align-items: center;">
      <i-col style="margin: 5px" span="2">
        <Icon type="ios-color-filter-outline" style="margin-right: 5px"/>
        <span style="text-align: left; display: inline-block; width:60px;">基础库集&nbsp;</span>
      </i-col>
      <i-col style="margin: 5px  5px  5px 0px;" span="4">
        <span style="text-align: left; display: inline-block;">{{ biz_base_db }}</span>
      </i-col>
    </Row>

    <Row style="margin-left: 5px;display: flex;align-items: center;">
      <i-col style="margin: 5px" span="2">
        <Icon type="logo-youtube" style="margin-right: 5px"/>
        <span style="text-align: left; display: inline-block; width:60px;">操作&nbsp;</span>
      </i-col>
      <i-col style="margin: 5px  5px  5px 0px;" span="4">
        <Button type="primary" size="small" @click="handleSubmit('formValidate')" :loading="btnDisabled">提交</Button>
      </i-col>
    </Row>

    <Spin size="large" fix v-if="spinShow">
      <Icon type="ios-loading" size=32 class="demo-spin-icon-load"></Icon>
      <div>请求中...</div>
    </Spin>
  </Card>
</template>

<script>
import Tables from "@/components/tables";
import {
  testDataDevMgtBranch,
  testDataDevMgtBranchVerify
} from '@/spider-api/create-iter'

import {get_base_db_bind, getBisNameLIstInfo} from "@/spider-api/biz-mgt";

export default {
  name: "TestPullBranch",
  components: {
    Tables
  },
  data() {
    return {
      spinShow: false,
      br_show: true,
      biz_base_db: '',
      bis_name_list: '',
      btnDisabled: false,
      formValidate: {
        biz_test_iter_br: '',
        biz_code: '',
        biz_base_db_set: ''
      }
    };
  },

  computed: {

    ruleValidate() {
      return {
        biz_test_iter_br: [
          {required: true, message: '请输入分支版本号', trigger: 'blur'}
        ],
        biz_code: [{required: true, message: '请选择业务类型', trigger: 'change'}]
      }
    }
  },

  methods: {
    getBaseDbInfo() {
      console.log("this.formValidate.biz_code==" + this.formValidate.biz_code)
      get_base_db_bind(this.formValidate.biz_code).then(res => {
        if (res.data.status === 'success') {
          this.biz_base_db = res.data.data["biz_base_db_code"]
        }
      })
    },
    getBisNameInfo() {
      console.log("这里开始。。。")
      getBisNameLIstInfo().then(res => {
        console.log("res.data.data===" + JSON.stringify(res.data.data))
        this.bis_name_list = res.data.data
      })
    },
    init() {
    },
    checkBranchNameLenth() {
      let isChinese = /[\u4e00-\u9fa5]/.test(this.formValidate.biz_test_iter_br);
      if (isChinese) {
        console.log(this.formValidate.biz_test_iter_br);
        this.$Message.error('分支号不可使用中文')
      }
    },
    query_recommend_version() {
    },
    handleSubmit() {

      if (this.formValidate['biz_test_iter_br'] === '') {
        console.log('branch is none')
      } else if (this.formValidate['biz_code'] === '') {
        console.log('biz_code is none')
      } else if (this.formValidate['biz_test_iter_br'].indexOf("_") != -1) {
        console.log(this.formValidate['biz_test_iter_br'].indexOf("_"))
        // alert('分支号不能包含下划线_')
        this.$Message.error('分支号不能包含下划线_')
        return false
        this.btnDisabled = false
      } else {
        let isChinese = /[\u4e00-\u9fa5]/.test(this.formValidate.biz_test_iter_br);
        if (isChinese) {
          console.log(this.formValidate.biz_test_iter_br);
          this.$Message.error('分支号不可使用中文')
          return false
        }
        this.btnDisabled = true
        this.spinShow = !this.spinShow
        testDataDevMgtBranchVerify(this.formValidate).then(res => {
          console.log(res.data.status)
          this.btnDisabled = false
          if (res.data.status === 'success') {
            console.log("this.formValidate===" + JSON.stringify(this.formValidate))
            this.formValidate.biz_base_db_set = this.biz_base_db
            testDataDevMgtBranch(this.formValidate).then(res => {
              if (res.data.status === 'success') {
                alert("分支创建成功")
                this.btnDisabled = false
              }
            }).finally(() => {
              this.spinShow = !this.spinShow
            })
          } else {
            this.spinShow = !this.spinShow
            this.btnDisabled = false
            alert("分支号已存在,请重新申请")
          }
        }).catch(err => {
          this.spinShow = !this.spinShow
        })

      }
    }

  },

  created() {
    this.getBisNameInfo()
  }
};
</script>

<style scoped>
.demo-spin-icon-load {
  animation: ani-demo-spin 1s linear infinite;
}
</style>
