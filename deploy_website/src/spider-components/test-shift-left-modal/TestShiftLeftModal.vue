<template>
  <Modal
    v-model="modalVisible"
    title="测试左移"
    width="700"
    @on-ok="handleSubmit"
    @on-cancel="handleCancel"
    :loading="loading"
  >
    <!-- 测试类型选择区域 -->
    <div class="test-type-section">
      <h4>测试类型选择</h4>
      <CheckboxGroup v-model="selectedTestTypes" @on-change="handleTestTypeChange">
        <Checkbox label="APIDOC">
          <Icon type="ios-document" />
          Apidoc接口文档
        </Checkbox>
        <Checkbox label="SQL">
          <Icon type="ios-filing" />
          SQL脚本
        </Checkbox>
        <Checkbox label="ES">
          <Icon type="ios-search" />
          ES索引
        </Checkbox>
      </CheckboxGroup>
    </div>
    
    <!-- 应用列表选择区域 -->
    <div class="app-list-section" v-if="showAppList">
      <h4>应用列表选择</h4>
      <div class="app-list-header">
        <Checkbox 
          :indeterminate="indeterminate"
          :value="checkAll"
          @click.prevent.native="handleCheckAll"
        >
          全选
        </Checkbox>
        <span class="app-count">共 {{ appList.length }} 个应用</span>
      </div>
      <CheckboxGroup v-model="selectedApps" @on-change="handleAppChange">
        <div class="app-list-container">
          <Checkbox 
            v-for="app in appList" 
            :key="app.app_name" 
            :label="app.app_name"
            class="app-item"
          >
            <div class="app-info">
              <span class="app-name">{{ app.app_name }}</span>
              <span class="app-desc">{{ app.app_desc || '暂无描述' }}</span>
            </div>
          </Checkbox>
        </div>
      </CheckboxGroup>
    </div>
    
    <!-- 提示信息 -->
    <div class="tips-section">
      <Alert type="info" show-icon>
        <div slot="desc">
          <p>• 请选择需要进行测试左移的类型和对应的应用列表</p>
          <p>• APIDOC测试支持多应用并行处理，提升执行效率</p>
          <p>• SQL测试按迭代维度统一管理，确保数据一致性</p>
          <p>• ES测试复用现有稳定流程，保证处理质量</p>
        </div>
      </Alert>
    </div>
  </Modal>
</template>

<script>
import { getAppListByIterationId, callShiftLeftTestJob } from '@/spider-api/test-shift-left'

export default {
  name: 'TestShiftLeftModal',
  data() {
    return {
      modalVisible: false,
      selectedTestTypes: [], // 选中的测试类型
      selectedApps: [], // 选中的应用列表
      appList: [], // 当前迭代的应用列表
      iterationId: '', // 当前迭代ID
      loading: false,
      checkAll: false, // 全选状态
      indeterminate: false, // 半选状态
      showAppList: false // 是否显示应用列表
    }
  },
  methods: {
    // 显示Modal
    show(iterationId) {
      this.iterationId = iterationId
      this.modalVisible = true
      this.resetForm()
      // 不在初始化时加载应用列表，而是在用户勾选APIDOC时动态加载
    },
    
    // 测试类型变化处理
    handleTestTypeChange(selectedTypes) {
      this.showAppList = selectedTypes.includes('APIDOC')
      if (this.showAppList) {
        // 当用户勾选APIDOC时，动态加载当前迭代下的应用列表
        this.loadAppList()
      } else {
        // 取消勾选APIDOC时，清空应用选择
        this.selectedApps = []
        this.checkAll = false
        this.indeterminate = false
        this.appList = []
      }
    },
    
    // 加载应用列表
    async loadAppList() {
      try {
        const response = await getAppListByIterationId(this.iterationId)
        if (response.data.status === 'success') {
          this.appList = response.data.data || []
        } else {
          throw new Error(response.data.msg || '获取应用列表失败')
        }
      } catch (error) {
        this.$Message.error('加载应用列表失败: ' + error.message)
        this.appList = []
      }
    },
    
    // 全选处理
    handleCheckAll() {
      if (this.indeterminate) {
        this.checkAll = false
      } else {
        this.checkAll = !this.checkAll
      }
      this.indeterminate = false
      
      if (this.checkAll) {
        this.selectedApps = this.appList.map(app => app.app_name)
      } else {
        this.selectedApps = []
      }
    },
    
    // 应用选择变化处理
    handleAppChange(selectedApps) {
      if (selectedApps.length === 0) {
        this.indeterminate = false
        this.checkAll = false
      } else if (selectedApps.length === this.appList.length) {
        this.indeterminate = false
        this.checkAll = true
      } else {
        this.indeterminate = true
        this.checkAll = false
      }
    },
    
    // 提交处理
    async handleSubmit() {
      if (!this.validateForm()) {
        return false // 阻止Modal关闭
      }
      
      const requestData = this.buildRequestData()
      
      try {
        this.loading = true
        const response = await callShiftLeftTestJob(requestData)
        if (response.data.status === 'success') {
          this.$Message.success('测试左移流水线启动成功')
          this.modalVisible = false
          this.$emit('success', response.data)
        } else {
          throw new Error(response.data.msg || '启动失败')
        }
      } catch (error) {
        this.$Message.error('启动测试左移流水线失败: ' + error.message)
      } finally {
        this.loading = false
      }
    },
    
    // 构建请求数据
    buildRequestData() {
      return {
        job_name: 'shift_left_test',
        iteration_id: this.iterationId,
        app_name_str: this.selectedApps.join(','),
        APIDOC_TYPE: this.selectedTestTypes.includes('APIDOC') ? 1 : 0,
        SQL_TYPE: this.selectedTestTypes.includes('SQL') ? 1 : 0,
        ES_TYPE: this.selectedTestTypes.includes('ES') ? 1 : 0
      }
    },
    
    // 表单验证
    validateForm() {
      if (this.selectedTestTypes.length === 0) {
        this.$Message.error('请至少选择一种测试类型')
        return false
      }
      
      if (this.selectedTestTypes.includes('APIDOC') && this.selectedApps.length === 0) {
        this.$Message.error('选择APIDOC测试时，请至少选择一个应用')
        return false
      }
      
      return true
    },
    
    // 重置表单
    resetForm() {
      this.selectedTestTypes = []
      this.selectedApps = []
      this.appList = []
      this.checkAll = false
      this.indeterminate = false
      this.showAppList = false
      this.loading = false
    },
    
    // 取消处理
    handleCancel() {
      this.modalVisible = false
      this.resetForm()
    }
  }
}
</script>

<style scoped>
.test-type-section {
  margin-bottom: 20px;
}

.test-type-section h4 {
  margin-bottom: 10px;
  font-weight: bold;
  color: #333;
}

.app-list-section {
  margin-bottom: 20px;
}

.app-list-section h4 {
  margin-bottom: 10px;
  font-weight: bold;
  color: #333;
}

.app-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  padding: 8px 0;
  border-bottom: 1px solid #e8eaec;
}

.app-count {
  color: #999;
  font-size: 12px;
}

.app-list-container {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #dcdee2;
  border-radius: 4px;
  padding: 10px;
}

.app-item {
  display: block;
  margin-bottom: 8px;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.app-item:hover {
  background-color: #f8f8f9;
}

.app-info {
  display: flex;
  flex-direction: column;
}

.app-name {
  font-weight: bold;
  color: #333;
  margin-bottom: 2px;
}

.app-desc {
  color: #999;
  font-size: 12px;
}

.tips-section {
  margin-top: 20px;
}
</style>