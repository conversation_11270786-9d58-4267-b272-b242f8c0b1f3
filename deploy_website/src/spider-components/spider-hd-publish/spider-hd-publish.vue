<template>
  <Card shadow>
    <Row style="margin-top: 1em">
      <i-col span="2">迭代版本</i-col>
      <i-col span="2">{{ this.pipeline_id }}</i-col>
    </Row>
    <Row style="margin-top: 2em">
      <i-col span="3">灰度应用列表</i-col>
    </Row>
    <Table
      style="margin-top: 1em"
      width="900"
      :columns="publish_columns"
      :data="publish_data"
    ></Table>
    <Modal
      v-model="ops_operate_modal"
      width="680"
      :mask-closable="false"
      @on-cancel="cancelHistory">
      <p slot="header" style="color:gray;">
        <Icon type="md-clipboard"></Icon>
        <span> {{ app_name }} 执行历史 </span>
      </p>
      <div style="height:300px; overflow-y: auto;">
        <table style="margin: 10px;" v-for="cont in historyCont" :key="cont.index">
          <tr>
            <td width="15px">
              <Icon type="md-arrow-round-forward"></Icon>
            </td>
            <td width="100px" style="color: darkblue;">{{ cont.operator }}</td>
            <td width="50px" style="color: black">{{ cont.type }}</td>
            <td width="400px">{{ cont.operateTime }}</td>
          </tr>
          <tr>
            <td width="15px"></td>
            <td width="100px" style="border-bottom: #DDDDDD solid 2px; color: black;">{{ cont.ip }}</td>
            <td width="450px" colspan="2" style="border-bottom: #DDDDDD solid 2px;" v-if="cont.detail !== 'error'"><span
              v-html="cont.detail"></span></td>
            <td width="450px" colspan="2" style="border-bottom: #DDDDDD solid 2px; color: red;" v-else><span
              v-html="cont.detail"></span></td>
          </tr>
        </table>
      </div>
      <div slot="footer">
        <Button @click="cancelHistory">关闭</Button>
      </div>
    </Modal>
  </Card>
</template>

<script>
  import store from "@/spider-store";

  import {
    getHdPublishOrderInfo,
    hdPublishOperate,
    hdPublishInfo,
    AppPublishCheckApi
  } from "@/spider-api/publish"
  import {
    checkConfigConsistentApi,
    publishConfigBranch,
  } from "@/spider-api/zues"
  import {
    getServiceResult
  } from "@/spider-api/iter-plan";

  export default {
    name: "SpiderHDPublish",
    data() {
      return {
        pipeline_id: "",
        ops_operate_modal: false,
        switch_history: '',
        app_name: '',
        historyCont: [],
        publish_data: [],
        deploy_node: [],
        publish_columns: [
          {
            title: "应用名",
            key: "appName",
            width: 190
          },
          {
            title: "服务器",
            key: "ip",
            width: 150,
            render: (h, params) => {
              let nodes = [];
              params.row.ip.forEach(item => {
                let vnode = h("Option", {
                  props: {
                    value: item
                  }
                });
                nodes.push(vnode);
              });
              return h(
                "Select",
                {
                  style: {},
                  props: {
                    placeholder: "",
                    value: "",
                    size: "small",
                    transfer: true
                  },
                  on: {
                    'on-change': (val) => {
                      this.deployNodeChange(params, val)
                    }
                  }
                },
                nodes
              );
            }
          },
          {
            title: "操作",
            width: 430,
            align: "center",
            render: (h, params) => {
              return h("div", [
                h(
                  "Button",
                  {
                    attrs: {
                      class: "ivu-btn ivu-btn-primary ivu-btn-small ivu-btn-ghost"
                    },
                    props: {
                      size: "small"
                    },
                    style: {
                      marginRight: "1em"
                    },
                    on: {
                      click: () => {
                        this.hdDeploy(params, 'deploy')
                      }
                    }
                  },
                  "发布+启动"
                ),
                h(
                  "Button",
                  {
                    attrs: {
                      class: "ivu-btn ivu-btn-primary ivu-btn-small ivu-btn-ghost"
                    },
                    props: {
                      size: "small"
                    },
                    style: {
                      marginRight: "1em"
                    },
                    on: {
                      click: () => {
                        this.doOperate(params, 'restart')
                      }
                    }
                  },
                  "重启"
                ),
                h(
                  "Button",
                  {
                    attrs: {
                      class: "ivu-btn ivu-btn-primary ivu-btn-small ivu-btn-ghost"
                    },
                    props: {
                      size: "small"
                    },
                    style: {
                      marginRight: "1em"
                    },
                    on: {
                      click: () => {
                        this.doOperate(params, 'stop')
                      }
                    }
                  },
                  "停止"
                ),
                h(
                  "Button",
                  {
                    attrs: {
                      class: "ivu-btn ivu-btn-primary ivu-btn-small ivu-btn-ghost"
                    },
                    props: {
                      size: "small"
                    },
                    style: {
                      marginRight: "1em"
                    },
                    on: {
                      click: () => {
                        this.doOperate(params, 'rollback')
                      }
                    }
                  },
                  "回滚"
                ),
                h(
                  "Button",
                  {
                    attrs: {
                      class: "ivu-btn ivu-btn-primary ivu-btn-small ivu-btn-ghost"
                    },
                    props: {
                      size: "small"
                    },
                    style: {
                      marginRight: "1em"
                    },
                    on: {
                      click: () => {
                        this.doOperate(params, 'update')
                      }
                    }
                  },
                  "配置更新"
                ),
                h(
                  "Button",
                  {
                    attrs: {
                      class: "ivu-btn ivu-btn-primary ivu-btn-small ivu-btn-ghost"
                    },
                    props: {
                      size: "small"
                    },
                    style: {
                      marginRight: "1em"
                    },
                    on: {
                      click: () => {
                        this.doOperate(params, 'code_update')
                      }
                    }
                  },
                  "代码更新"
                ),
              ]);
            }
          },
          {
            title: "执行历史",
            align: "center",
            render: (h, params) => {
              return h("div", [
                h(
                  "Button",
                  {
                    attrs: {
                      class: "ivu-btn ivu-btn-success ivu-btn-small ivu-btn-ghost",
                    },
                    props: {
                      size: "small"
                    },
                    style: {},
                    on: {
                      click: () => {
                        this.showHistory(params.row.appName)
                      }
                    }
                  },
                  "执行历史"
                )
              ]);
            }
          }
        ],
      };
    },
    methods: {
      initThisVue() {
        this.pipeline_id = store.state.iterationID;
        getHdPublishOrderInfo(this.pipeline_id)
          .then(res => {
            if (res.data.status === "success") {
              this.publish_data = res.data.data["publish_list"];
              // console.log(this.publish_data);
            } else {
              this.$Message.error(res.data.msg);
            }
          })
          .catch(err => {
            this.$Message.error(err.response.data.msg);
          });
      },
      deployNodeChange(params, val) {
        let app_name = params.row.app_name
        this.deploy_node[app_name] = val
      },
      getStatus(sid, params) {
        getServiceResult(sid).then(res => {
          if (res.data.status == "success") {
            var status = res.data.data.status
            var detail = res.data.data.detail
            if (status == "success") {
              this.$Message.success(detail);
              this.doOperate(params, 'deploy')
            } else if (status == "failure") {
              //this.$Message.error(err.response.data.msg)
              alert(detail)
            } else {
              let vm = this
              setTimeout(function () {
                vm.getStatus(sid, params)
              }, 2000)
            }
          } else {
            alert(res.data.msg);
          }
        })
          .catch(err => {
            this.$Message.error(err.response.data.msg)
            alert(err.response.data.msg)
          })
      },
      hdDeploy(params, op_type) {
        checkConfigConsistentApi(store.state.iterationID, params.row.appName, "hd").then(result => {
          if (result.data.status === "success") {
            this.$Message.success(result.data.msg);
            publishConfigBranch(store.state.iterationID, params.row.appName, "hd").then(result => {
              if (result.data.status === "success") {
                this.$Message.success(result.data.msg);
                AppPublishCheckApi(store.state.iterationID, params.row.appName, "uat").then(res => {
                    if (res.data.status === "success") {
                      this.$Message.success(res.data.msg);
                      this.getStatus(res.data.data["sid"], params)
                    } else {
                      alert(res.data.msg);
                    }
                  }
                )
              } else {
                this.$Message.error(result.data.msg);
                alert(result.data.msg)
              }
            })
          } else {
            this.$Message.error(result.data.msg);
            alert(result.data.msg)
          }
        })
        // let node = this.deploy_node[params.row.app_name]
        // hdPublishOperate(params.row.appName, node, op_type)
        //   .then(res => {
        //     if (res.data.status === "success") {
        //       this.$Message.success(res.data.msg);
        //     } else {
        //       this.$Message.error(res.data.msg);
        //     }
        //   })
        //   .catch(err => {
        //     this.$Message.error(err.response.data.msg);
        //   });
      },
      doOperate(params, op_type) {
        let node = this.deploy_node[params.row.app_name]
        hdPublishOperate(params.row.appName, node, op_type)
          .then(res => {
            if (res.data.status === "success") {
              this.$Message.success(res.data.msg);
            } else {
              this.$Message.error(res.data.msg);
            }
          })
          .catch(err => {
            this.$Message.error(err.response.data.msg);
          });
      },
      get_history_data(app_name) {
        hdPublishInfo(app_name)
          .then(res => {
            if (res.data.status === "success") {
              this.historyCont = res.data.data
            } else {
              this.historyCont = []
            }
          })
          .catch(err => {
            this.$Message.error(err.response.data.msg);
          });
      },
      showHistory(app_name) {
        this.app_name = app_name
        this.ops_operate_modal = true
        this.get_history_data(app_name)
        this.switch_history = setInterval(this.get_history_data(app_name), 5000)
      },
      cancelHistory() {
        this.app_name = ''
        this.ops_operate_modal = false
        this.historyCont = []
        if (this.switch_history) {
          clearInterval(this.switch_history)
        }
      }
    },
    mounted() {
    }
  };
</script>
