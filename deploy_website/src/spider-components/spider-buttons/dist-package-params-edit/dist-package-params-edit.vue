<template>
  <div>
    <Card style="text-align: left;">
      <div>
        <Row>
          <i-col style="margin: 10px;text-align: right" span="6">
            <span style="text-align: right; display: inline-block;">资源包版本范围：</span>
          </i-col>
          <Col span="37">
            <Select v-model="begin_ver" @on-change="fund_version_change" style="width: 30%; display: inline-block;">
              <Option 
                v-for="(item,index) in begin_ver_list"
                :value="item"
                :key="index"
                >{{ item }}
                <span v-if="item == begin_ver " style="float:right;color:#ccc">默认</span>
              </Option>
            </Select>
            <span style="display: inline-block; margin: 0 5px;">~</span>
            <Input v-model="end_ver" placeholder="请输入" style="width: 30%; display: inline-block;" ></Input>
          </Col>
        </Row>
        <Row>
          <i-col style="margin: 10px;text-align: right" span="6">
            <span style="text-align: right; display: inline-block;">发布环境：</span>
          </i-col>
          <Col span="37">
            <Select v-model="suite_code" @on-change="changeSuiteCode" style="width: 30%; display: inline-block;"  filterable>
              <Option
                v-for="item in app_env_list"
                :value="item"
                :label="item"
                :key="item"
              >{{ item }}
              </Option>
            </Select>
          </Col>
        </Row>
      </div>
      <div v-for="(app, index) in select_data" :key="index">
        
        <Row >
          <i-col style="margin: 10px;text-align: right" span="6">
            <span style="text-align: right; display: inline-block;">{{app.app_name}}分支：</span>
          </i-col>
          <Col span="37">
            <Input v-model="app.br_name" placeholder="请输入" style="width: 30%; display: inline-block;" v-bind:readonly="true" :disabled="true"></Input>
          </Col>
          </Row>
        </Row>
      </div>
      <Row v-show="change_android">
              <i-col style="margin: 10px;text-align: right" span="6">
                <span style="text-align: right; display: inline-block;">{{app_name}}-android换壳版本:</span>
              </i-col>
              <Col span="14">
                <Select v-model="android_last_prod_apk_list_index" @on-change="changeApkVersion"  span="10" style="margin:10px" clearable filterable>
                  <Option
                    v-for="(item,index) in android_last_prod_apk_list"
                    :value="index"
                    :label="item.apk"
                    :key="item.apk"
                  >{{ item.apk }}
                  </Option>
                </Select>
              </Col>
            </Row>
      <Row v-show="is_silent != undefined">
              <i-col style="margin: 10px;text-align: right" span="6">
                <span style="text-align: right; display: inline-block;">打包类型:</span>
              </i-col>
              <Col span="37">
                <Checkbox v-model="is_silent" @on-change="changeIsSilent" :true-value="1" :false-value="0" style="margin-top: 10px">静默
                </Checkbox>
              </Col>
            </Row>
    </Card>
  </div>
</template>

<script>
    import store from "@/spider-store"
    import {
    findDistVersion,
  } from "@/spider-api/h5";
  import {
    getPackageVersionApi,
  } from "@/spider-api/ci/mobile-ci/mobile-ci-info";
  import {
  appSuiteCodeApi,
  pipelineEnvBind,
  platformSuiteCodeApi,
  getLastProdAndroidInfo
} from "@/spider-api/h5"
  import {getAppBranchInfoApi} from "@/spider-api/mgt-iter"
  import {createH5AppBindNfAppApi,getH5AppBindNfAppApi} from "@/spider-api/h5-ci-cd/nf-app"
    export default {
        name: "DistPackageParamsEdit",
        props: {
            // environment: {
            // type: String,
            // defaults: ''
            iter_id: {
            type: String,
            defaults: ''
            },
            app_env_list: {
            type: Array,
            },
            select_data: {
            type: Array,
            defaults: ''
            }
        },
        data() {
            return {
                showFundParams: true,
                app_name:'',
                begin_ver_list:[],
                nf_branch_list:[],
                end_ver_list:'',
                nf_ver_list:'',
                environment:'',
                stage:'',
                fund_platform: [],
                piggy_platform: [],
                begin_ver:'',
                suite_code:'',
                end_ver:'',
                is_silent:0,
                android_last_prod_apk_list:[],
                android_last_prod_apk_list_index:'',
                change_android:false,
            }
        },
        watch: {
          end_ver(val){
          this.select_data.forEach(item =>{
            item.end_ver = this.end_ver
          })
          },
          checked(val){
            this.getAndroidLastProdInfo()
          },
          select_data(val){
            this.select_data.forEach(item => {
              item.suite_code = this.suite_code
              item.is_silent = this.is_silent
              item.begin_ver = this.begin_ver
              item.end_ver = this.end_ver
              item.android_last_prod_apk_list_index = this.android_last_prod_apk_list_index
            })
            this.getAndroidLastProdInfo()
          },
      },
        mounted() {
            this.getAndroidLastProdInfo()
      },
    methods:{
      changeNfVer(){
        
      },
      changeApkVersion(){
            console.log(this.android_last_prod_apk_list[this.android_last_prod_apk_list_index])
            console.log(this.android_last_prod_apk_list[this.android_last_prod_apk_list_index].apk)
            this.select_data.forEach(item => {
              item.apk = this.android_last_prod_apk_list[this.android_last_prod_apk_list_index].apk
              item.apk_url = this.android_last_prod_apk_list[this.android_last_prod_apk_list_index].apk_url
              if (item.platform_code == 'fund-h5-res'){
                item['android_name'] = 'fund-android'
              } else if (item.platform_code == 'piggy-h5-res'){
                item['android_name'] = 'piggy-android'
              }
            })
            
          },
      changeData(){
        this.changeIsSilent()
        this.changeSuiteCode()
      },
        changeIsSilent(){
          this.select_data.forEach(item =>{
                    item.is_silent = this.is_silent
                  })
                  console.log(this.select_data)
                  this.$emit('set_select_change',this.select_data)
        },
        fund_log(select_data){
            
      },
      add_one_upper(arr) {
        
      },
      fund_version_change(){
        this.select_data.forEach(item =>{
                    item.begin_ver = this.begin_ver
                  })
      },
      version_change(){
        
      },
      changeSuiteCode(){
                  this.select_data.forEach(item =>{
                    item.suite_code = this.suite_code
                  })
                  console.log(this.select_data)
                  this.$emit('set_select_change',this.select_data)
          },
      init(){
        this.getDistBeginVersion()
      },
      getNfBranch(){
        
        },
        getDistBeginVersion() {
            console.info("==========================getDistBeginVersion=========================")
            this.begin_ver = store.state.h5_branch_version
            this.end_ver = store.state.h5_branch_version
              getPackageVersionApi({"br_name": store.state.h5_branch_version,
                "suite_code": this.suite_code,
                "app_name": this.app_name}).then(res => {
                  this.begin_ver_list = res.data.data["version_list"]
                    this.begin_ver_list.push("40.0.0")
              }).catch(err => {
                console.log('============= err =========')
                console.log(err)
                this.$Message.error(err)
              })
              // 获取绑定的nf信息
            console.log("ending______________")
            
            },
            getAndroidLastProdInfo(){
              this.select_data.forEach(item => {
                if (item.platform_code == 'fund-h5-res'){
                  this.change_android = false
                  this.app_name = 'fund'
                  getLastProdAndroidInfo('fund').then(res =>{
                  item['apk']= '0'
                  item['apk_url'] = '0'
                  item['android_name'] = '0'
                  this.android_last_prod_apk_list = res.data.data
                })
                } else if (item.platform_code == 'piggy-h5-res'){
                  this.change_android = false
                  this.app_name = 'piggy'
                  getLastProdAndroidInfo('piggy').then(res =>{
                  item['apk']= '0'
                  item['apk_url'] = '0'
                  item['android_name'] = '0'
                  this.android_last_prod_apk_list = res.data.data
                })
                }
                
              })
            },
      },
    created(){
      this.init()
      this.getAndroidLastProdInfo()
    },
    mounted() {
    },

    }
</script>

<style scoped>

</style>
