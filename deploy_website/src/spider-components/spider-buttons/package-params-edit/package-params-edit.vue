<template>
  <div v-show="item.app_name != 'nf-fund' && item.app_name != 'nf-piggy'">
      <Row style="margin-top: 10px">
          <Card>
            <Row v-show="item.begin_ver != undefined">
              <i-col style="margin: 10px;text-align: right" span="8">
                <span style="text-align: right; display: inline-block;">{{item.app_name}}起始版本:</span>
              </i-col>
              <Col span="14">
                <Select v-model="item.begin_ver"  span="10" style="margin:10px"  filterable>
                  <Option
                    v-for="(item,index) in begin_ver_list"
                    :value="item"
                    :label="item"
                    :key="index"
                  >{{ item }}
                  </Option>
                </Select>
              </Col>
            </Row>
            <Row  v-show="item.end_ver != undefined">
              <i-col style="margin: 10px;text-align: right" span="8">
                <span style="text-align: right; display: inline-block;">{{item.app_name}}结束版本:</span>
              </i-col>
              <Col span="14">
                <Input v-model="item.end_ver" placeholder="结束版本" style="margin: 10px"/>
              </Col>
            </Row>
            <Row v-show="item.nf_br_name != undefined">
              <i-col style="margin: 10px;text-align: right" span="8">
                <span style="text-align: right; display: inline-block;">nf-{{item.app_name}}版本：</span>
              </i-col>
                <Col span="14">
                      <Select  v-model="item.nf_br_name" @on-change="changeNfVer" span="10" style="margin:10px"  filterable>
                        <Option
                          v-for="item in nf_branch_list"
                          :value="item.br_name_value"
                          :label="item.br_name_label"
                          :key="item.br_name_value"
                        >{{ item.br_name_label }}
                        </Option>
                      </Select>
                    </Col>
            </Row>
            <Row v-show="item.suite_code != undefined">
              <i-col style="margin: 10px;text-align: right" span="8">
                <span style="text-align: right; display: inline-block;">{{item.app_name}}发布环境:</span>
              </i-col>
              <Col span="14">
                <Select v-model="item.suite_code" @on-change="changeSuiteCode"  span="10" style="margin:10px"  filterable>
                  <Option
                    v-for="item in app_env_list"
                    :value="item"
                    :label="item"
                    :key="item"
                  >{{ item }}
                  </Option>
                </Select>
              </Col>
            </Row>
            <Row v-show="item.is_silent != undefined">
              <i-col style="margin: 10px;text-align: right" span="8">
                <span style="text-align: right; display: inline-block;">打包类型:</span>
              </i-col>
              <Col span="14">
                <Checkbox v-model="item.is_silent" @on-change="changeIsSilent" :true-value="1" :false-value="0" style="margin-top: 10px">静默
                </Checkbox>
              </Col>
            </Row>
            <Row v-show="change_android">
              <i-col style="margin: 10px;text-align: right" span="8">
                <span style="text-align: right; display: inline-block;">{{item.app_name}}-android换壳版本:</span>
              </i-col>
              <Col span="14">
                <Select v-model="android_last_prod_apk_list_index" @on-change="changeApkVersion"  span="10" style="margin:10px" clearable filterable>
                  <Option
                    v-for="(item,index) in android_last_prod_apk_list"
                    :value="index"
                    :label="item.apk"
                    :key="item.apk"
                  >{{ item.apk }}
                  </Option>
                </Select>
              </Col>
            </Row>
            <Row v-show="item.package_type == 'mini-program'">
                <i-col style="margin: 10px;text-align: right" span="8">
                  <span style="text-align: right; display: inline-block;margin-top: 5px">上传版本:</span>
                </i-col>
                <Col span="14">
                  <Input v-model="item.upload_version" placeholder="上传版本" style="margin: 10px"/>
                </Col>
              </Row>
            <Row v-show="item.package_type == 'mini-program'">
                <i-col style="margin: 10px;text-align: right" span="8">
                  <span style="text-align: right; display: inline-block;margin-top: 5px">actionId:</span>
                </i-col>
                <Col span="14">
                  <Input v-model="item.actionId" placeholder="actionId" style="margin: 10px"/>
                </Col>
              </Row>
            <Row v-show="item.package_type == 'mini-program'">
                <i-col style="margin: 10px;text-align: right" span="8">
                  <span style="text-align: right; display: inline-block;margin-top: 5px">上传描述:</span>
                </i-col>
                <Col span="14">
                  <Input v-model="item.upload_desc" placeholder="上传描述" style="margin: 10px"/>
                </Col>
              </Row>
          </Card>
      </Row>
  </div>
</template>

<script>
    import {
    getPackageVersionApi,
  } from "@/spider-api/ci/mobile-ci/mobile-ci-info";
    import {getAppBranchInfoApi} from "@/spider-api/mgt-iter"
      import {createH5AppBindNfAppApi, getH5AppBindNfAppApi} from "@/spider-api/h5-ci-cd/nf-app"
import {
  appSuiteCodeApi,
  pipelineEnvBind,
  pipelineEnvBindGet,
  getLastProdAndroidInfo
} from "@/spider-api/h5"

    export default {
        name: "PackageParamsEdit",

        props: {
            item: Object,
            iter_id: String,
            br_name:String,
            checked: Boolean,

        },
        computed:{
        },
        data() {
            return {
              begin_ver_list: [],
              nf_branch_list:[],
              app_env_list:[],
              android_last_prod_apk_list:[],
              android_last_prod_apk_list_index:'',
              change_android:false,
            }
          },
      watch: {
          checked(val){

            if (val){
              this.getDistBeginVersion()
              this.getNfBranch()
              this.getEnvInfo()
              this.getAndroidLastProdInfo()
            }
          }
      },
        mounted() {
          this.getDistBeginVersion()
          this.getNfBranch()
          this.getEnvInfo()
          this.getAndroidLastProdInfo()
      },
        methods: {
          changeApkVersion(){
            console.log(this.android_last_prod_apk_list[this.android_last_prod_apk_list_index])
            console.log(this.android_last_prod_apk_list[this.android_last_prod_apk_list_index].apk)
            this.item['apk']= this.android_last_prod_apk_list[this.android_last_prod_apk_list_index].apk
            this.item['apk_url']= this.android_last_prod_apk_list[this.android_last_prod_apk_list_index].apk_url
            if (this.item.app_name == 'fund'){
                this.item['android_name'] = 'fund-android'
            }
            if ((this.item.app_name == 'piggy')){
                this.item['android_name'] = 'piggy-android'
            }
          },
          changeSuiteCode(suite_code){
                 pipelineEnvBind(
                  {"iter_id": this.iter_id,
                  "bind_env_list": {[this.item.app_name]: [suite_code]}
                  }
                ).then(res =>{
                   this.$Message.success(res.data.msg)
                   //this.$emit("suite_code",suite_code)
                })
                .catch(err => {
                    console.log('============= err =========')
                    console.log(err)
                    this.$Message.error(err)
                  })
          },
          changeBeginVer(begin_ver){
              this.$emit("begin_ver",begin_ver)
          },
          changeNfVer(nf_ver){
                createH5AppBindNfAppApi({
                          iteration_id: this.iter_id,
                          app_name: this.item.app_name,
                          nf_br_name: nf_ver,
                          nf_app_name: "nf-"+this.item.app_name,
                          stage:'测试'
                          }).then(res => {
                              //this.$emit("nf_ver",nf_ver)
                              this.$Message.success(res.data.msg)
                            })
                        .catch(err => {
                          console.log('============= err =========')
                          console.log(err)
                          this.$Message.error(err)
                        })
          },

          changeEndVer(end_ver){

              //this.$emit("end_ver",end_ver)
          },
          changeIsSilent(is_silent){
              //this.$emit("is_silent",is_silent)
          },




          getDistBeginVersion() {
            console.info("testststts ")
              if (this.br_name & this.item.suite_code & this.item.app_name){
                  getPackageVersionApi({"br_name": this.br_name,
                      "suite_code": this.item.suite_code,
                      "app_name": this.item.app_name}).then(res => {
                      console.log(res.data.data["version_list"])
                      this.begin_ver_list = res.data.data["version_list"]
                      this.begin_ver_list.push("40.0.0")
                  }).catch(err => {
                      console.log('============= err =========')
                      console.log(err)
                      this.$Message.error(err)
                  })
              }
              // 获取绑定的nf信息
            console.log("ending______________")
              // getH5AppBindNfAppApi({"iteration_id":this.iter_id,"app_name":this.item.app_name,"suite_code":this.item.h5_env}).then(res => {
              //   console.log('获取nf版本')
              //   this.item.nf_br_name= res.data.data.nf_br_name
              //   if (typeof this.item.nf_br_name == 'undefined' && this.item.package_type == 'dist'){
              //     this.item.nf_br_name = ''
              //   }
              //   console.log(this.item)
              //   console.log('获取nf=====================版本')
              // })

            },
          getNfBranch(){
             getAppBranchInfoApi({
                "app_name":"nf-"+this.item.app_name}).then(res => {
                  console.log(res.data.data["br_info"])
                this.nf_branch_list = res.data.data["br_info"]
              }).catch(err => {
                console.log('============= err =========')
                console.log(err)
                this.$Message.error(err)
              })
          },
          getEnvInfo(){
             console.log(this.iter_id)
             console.log(this.br_name)
            appSuiteCodeApi(this.iter_id).then(res => {
              console.log(res.data.data.app_suite_info)
              this.app_env_list = res.data.data.app_suite_info[this.item.app_name]
            })
            },
            getAndroidLastProdInfo(){
                if (this.item.app_name == 'fund' || this.item.app_name == 'piggy'){
                  this.change_android = true
                  getLastProdAndroidInfo(this.item.app_name).then(res =>{
                  this.item['apk']= '0'
                  this.item['apk_url'] = '0'
                  this.item['android_name'] = '0'
                  this.android_last_prod_apk_list = res.data.data
                })
                }
            }

            }

    }

</script>

<style scoped>

</style>
