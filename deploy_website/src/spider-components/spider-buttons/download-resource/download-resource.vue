<template>
  <div>
 <Modal
      :styles="{width: '60%'}"
      v-model="download_modal"
      title="下载（右键点击【下载】按钮选择【链接另存为】可直接保存下载）"
      footer-hide
    >
      <Card>
        <Tabs ref="download_tabs" name="download_tabs">
          <TabPane label="资源包" name="DownloadZip" tab="download_tabs">
            <DownloadZip :download_file_props=download_zip_props></DownloadZip>
          </TabPane>
        </Tabs>
      </Card>
    </Modal>
  </div>
</template>

<script>

  import {
    getResourceInfo,
  } from "@/spider-api/h5";
    import DownloadZip from '@/spider-view/ci-cd-mgt/h5/h5-download/h5-download-zip'

    export default {
        name: "DownloadResource",
        components: {
          DownloadZip
        },
          props: {

         },
      data() {
            return {
              download_modal: false ,
              download_zip_props: [],

            }
          },
           methods: {
            showDownload(iter_id, app_name, suite_code) {
              let param = {
                'app_name': app_name,
                'iteration_id': iter_id,
                'suite_code': suite_code
              };//
              //下载信息
              this.download_modal= true
              getResourceInfo(param).then(res => {
                console.log(res.data.data)
                this.download_zip_props = res.data.data

              })
            },

       }

    }
</script>

<style scoped>

</style>
