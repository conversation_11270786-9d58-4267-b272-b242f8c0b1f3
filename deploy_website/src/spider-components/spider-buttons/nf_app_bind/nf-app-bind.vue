<template>
  <div>
    <Button
        ghost
        type="primary"
        style="text-align: left; display: inline-block; width:120px; margin:5px"
        @click="show_nf_bind"
      >绑定 NF 应用分支
      </Button>
     <Modal
        title="绑定nf 应用"
        :styles="{width: '70%'}"
        v-model="bind_nf_modal"
        :mask-closable="true"
      >
       <Row style="margin-top: 10px">
        <Col v-if="fund_show" span="8">
          <Card>
            <Row>
              <i-col style="margin: 10px;text-align: right" span="8">
                <span style="text-align: right; display: inline-block;">nf-fund版本:</span>
              </i-col>
              <Col span="14">
                <Select v-model="nf_fund_ver" span="10" style="margin:10px" clearable filterable>
                  <Option
                    v-for="(item,index) in nf_fund_ver_list"
                    :value="item.value"
                    :label="item.label"
                    :key="item.value"
                  >{{ item.label }}
                  </Option>
                </Select>
              </Col>
            </Row>
          </Card>
        </Col>
        <Col v-if="piggy_show" span="8">
          <Card>
            <Row>
              <i-col style="margin: 10px;text-align: right" span="8">
                <span style="text-align: right; display: inline-block;">nf-piggy版本:</span>
              </i-col>
              <Col span="14">
                <Select v-model="nf_piggy_ver" span="10" style="margin:10px" clearable filterable>
                  <Option
                    v-for="(item,index) in nf_piggy_ver_list"
                    :value="item.value"
                    :label="item.label"
                    :key="item.value"
                  >{{ item.label }}
                  </Option>
                </Select>
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>

        <div slot="footer">
          <Button @click="createH5AppBindNfApp(fund_show, piggy_show)">保存</Button>
          <Button @click="closeBindModal">关闭</Button>
        </div>
    </Modal>
  </div>
</template>

<script>
  import {createH5AppBindNfAppApi, getH5AppBindNfAppApi} from "@/spider-api/h5-ci-cd/nf-app"
    export default {
        name: "nf-app-bind",
          props: {
             app_name: String,
            iter_id: String,
            suite_code: String,
            end_ver: String,
            begin_ver: String,
            nf_ver: String,
      },
      data() {
            return {

              nf_fund_ver_list: [],
              nf_piggy_ver: '',
              nf_piggy_ver_list: [],
              fund_is_silent: '',
              piggy_is_silent: '',
              fund_param: {},
              piggy_param: {},
              add_app: [],
              download_zip_props: [],
              columns: [
                {
                  type: 'selection',
                  width: 50,
                },
                {title: "应用", key: "app_name"},
                {title: "仓库", key: "git_path"},
                {title: "起始版本", key: "begin_ver", /*sortable: true*/},
                {title: "结束版本", key: "end_ver", /*sortable: true */},
                {
                  title: "操作时间",
                  key: "operate_time",
                  //sortable: true,
                  render: (h, params) => {
                    let value = params.row.operate_time
                    if (value == '' || value == null) {

                    } else {
                      value = formatDate(new Date(params.row.operate_time), 'yyyy-MM-dd hh:mm:ss')
                    }
                    return h('div', value)
                  }
                },
                {
                    title: "绑定环境",
                    key: "suite_code",
                    render: (h, params) => {
                      let value = params.row.suite_code
                      if(params.row.suite_code == null || params.row.suite_code == ''){
                        return h('div','无')
                      }else {
                        let str = '';
                        if(value instanceof Array){
                          console.log('i am Array')
                          console.log('=========== value =========')
                          console.log(value)
                          for(let i of value){
                            str += i + ','
                          }
                        }
                        if(typeof value === 'string'){
                          console.log('i am String')
                          str += value
                        }
                        //有多选却不输入值的
                        let returnArray = []
                        let returnStr = ''
                        returnArray = str.split(',')
                        for(let i of returnArray){
                          if(i != '' && i != null && i != undefined){
                            returnStr += i + ','
                          }
                        }
                        if(returnStr != '' && returnStr.charAt(returnStr.length -1) == ','){
                          returnStr = returnStr.substr(0,returnStr.length-1)
                        }
                        return h('div',returnStr)
                      }
                    }
                  },
                /*{title: "申请状态", key: "email_status" },*/
                {
                  title: "发布状态",
                  key: "status",
                  render: (h, params) => {
                    let stat_dict = {
                      'running': '执行中',
                      'success': '执行成功',
                      'failure': '执行失败',
                      'compile_running': '编译中',
                      'compile_success': '编译成功',
                      'compile_failure': '编译失败',
                      'publish_running': '发布中',
                      'publish_success': '发布成功',
                      'publish_failure': '发布失败',
                      'aborted': '已终止',
                    }
                    let status = params.row.status
                    if (stat_dict.hasOwnProperty(status)) {
                      var status_display = stat_dict[status]
                    } else {
                      var status_display = status
                    }
                    let action_type = ""
                    if (status == 'compile_running') {
                      action_type = 'compile'
                    } else if (status == 'publish_running') {
                      action_type = 'dist'
                    }
                    if (action_type) {
                      return h('div',
                        [h('a',
                          [h('Poptip',
                            {
                              props: {
                                transfer: true,
                                trigger: 'click',
                                title: '上次耗时: ' + this.last_elapsed,
                                content: '当前耗时：' + this.current_elapsed,
                                size: 'small'
                              },
                              on: {
                                'on-popper-show': () => {
                                  h5ElapseStatsApiGet(params.row.app_name, action_type, store.state.h5_branch_version).then(res => {
                                    if (res.data.status === "success") {
                                      this.last_elapsed = res.data.data['last_elapsed']
                                      this.current_elapsed = res.data.data['current_elapsed']
                                    }
                                  })
                                },
                                'on-popper-hide': () => {
                                  this.last_elapsed = 0
                                  this.current_elapsed = 0
                                }
                              }
                            }, status_display)
                          ])
                        ])
                    } else if(status == 'compile_success' || status == 'publish_success'){
                      return h('p',
                        {style: {color: 'green'}},
                        status_display)
                    } else if(status == 'compile_failure' || status == 'publish_failure'){
                      return h('p',
                        {style: {color: 'red'}},
                        status_display)
                    } else {
                      return h('p',
                        {style: {color: '#515a6e'}},
                        status_display)
                    }
                  }
                },
                {title: "操作人", key: "username"},
                {
                  title: 'NF迭代页面',
                  key: 'action',
                  width: 100,
                  align: 'center',
                  render: (h, params) => {
                    return h('div', [
                      h('a', {
                        props: {
                          type: 'default',
                          size: 'small',
                          value:this.nf_piggy_ver_list
                        },
                        style: {
                          marginRight: '5px',
                          display: params.row.app_name == 'nf-fund' || params.row.app_name == 'nf-piggy' ? 'none' : 'inline-block'
                        },
                        on: {
                          click: () => {
                             if (params.row.app_name === 'fund') {
                               let routeDate= this.$router.resolve({name:"h5_pipeline",
                                 query: {"iteration_id":"h5_" +this.nf_fund_ver, "branch_version":this.nf_fund_ver}})
                               window.open(routeDate.href, "_blank")
                             }
                              if (params.row.app_name === 'piggy') {
                               let routeDate= this.$router.resolve({name:"h5_pipeline",
                                 query: {"iteration_id":"h5_" +this.nf_piggy_ver, "branch_version": this.nf_piggy_ver}})
                               window.open(routeDate.href, "_blank")
                             }

                          }
                        }
                      }, '跳转到...'),
                    ]);
                  }
                },
                {
                  title: '操作',
                  key: 'action',
                  width: 250,
                  align: 'center',
                  render: (h, params) => {
                    return h('div', [
                      h('Button', {
                        props: {
                          type: 'default',
                          size: 'small'
                        },
                        style: {
                          marginRight: '5px'
                        },
                        on: {
                          click: () => {
                            if (params.row.status == undefined) {
                              this.$Notice.info({
                                desc: '未执行',
                                duration: getInfoNoticeShowTime(),
                              });
                              return;
                            }
                            if (params.row.status.indexOf('running') < 0) {
                              this.$Notice.info({
                                desc: '不处于运行中',
                                duration: getInfoNoticeShowTime(),
                              });
                              return;
                            }
                            this.stopRun(params.row)
                          }
                        }
                      }, '终止'),
                      h('Button', {
                        props: {
                          type: 'default',
                          size: 'small'
                        },
                        /*attrs: {
                          //按钮禁用
                          disabled: params.row.need_online == 1 ? false : true
                        },*/
                        style: {
                          marginRight: '5px',
                          display: params.row.app_name == 'nf-fund' || params.row.app_name == 'nf-piggy' ? 'none' : 'inline-block'
                        },
                        on: {
                          click: () => {
                            this.showDownload(params.row.app_name)

                          }
                        }
                      }, '下载'),
                      h('Button', {
                        props: {
                          type: 'default',
                          size: 'small'
                        },
                        style: {
                          marginRight: '5px'
                        },
                        on: {
                          click: () => {
                            let job_url = params.row.job_url
                            if (job_url != null) {
                              window.open(params.row.job_url)
                            } else {
                              this.$Notice.info({
                                title: '暂无详情',
                                duration: getInfoNoticeShowTime(),
                              });
                            }
                          }
                        }
                      }, '详情')
                    ]);
                  }
                },
              ],
            }
          },
           methods: {
          //fixme 特殊的组装逻辑 20210811 帅
        createH5AppBindNfApp(fund_show, piggy_show){
          let params_list = []
          if (fund_show){
            params_list.push({
          iteration_id: store.state.iterationID,
          app_name: "fund",
          nf_br_name: this.nf_fund_ver,
          nf_app_name: "nf-fund"
          })
          }
          if (piggy_show){
            params_list.push({
          iteration_id: store.state.iterationID,
          app_name: "piggy",
          nf_br_name: this.nf_piggy_ver,
          nf_app_name: "nf-piggy"
          })
          }
          for (let row of params_list) {
            createH5AppBindNfAppApi(row).then(res => {
              this.$Message.success(res.data.msg)
            })
              .catch(err => {
                console.log('============= err =========')
                console.log(err)
                this.$Message.error(err)
              })
          }
          this.bind_nf_modal= false

        },
        closeBindModal(){
            this.bind_nf_modal= false
          },
        show_nf_bind() {
          this.get_table_select()
          this.bind_nf_modal = true
        },
       }

    }
</script>

<style scoped>

</style>
