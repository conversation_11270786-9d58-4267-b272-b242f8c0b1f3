<template>
  <div>
      <Row style="margin-top: 10px">
          <Card>
            <Row>
              <i-col style="margin: 10px;text-align: right" span="8">
                <span style="text-align: right; display: inline-block;margin-top: 5px">{{item.appName}}选h5资源包测试环境:</span>
              </i-col>\
              <Col span="14">
                <Select v-model="item.h5Env" filterable style="margin:10px" ref="select1"
                        placeholder="请选择h5资源包测试环境" @on-change="onChangeFindH5ZipVersionByEnv">
                  <Option v-for="(item,index) in testSuiteCodeOfH5ZipList "
                          :value="item"
                          :label="item"
                          :key="index">
                    {{ item }}</Option>
                </Select>
              </Col>
            </Row>
            <Row>
              <i-col style="margin: 10px;text-align: right" span="8">
                <span style="text-align: right; display: inline-block;margin-top: 5px">{{item.appName}}选H5资源包版本:</span>
              </i-col>
              <Col span="14">
                <Select v-model="item.h5ZipVersion" filterable style="margin:10px" ref="select2"
                        placeholder="请选择H5全量包版本">
                  <Option v-for="(item,index) in h5ZipVersionList"
                          :value="item.value"
                          :label="item.label"
                          :key="index">
                    {{ item.label }}</Option>
                </Select>
              </Col>
            </Row>
            <Row>
              <i-col style="margin: 10px;text-align: right" span="8">
                <span style="text-align: right; display: inline-block;margin-top: 5px">{{item.appName}}客户端构建版本:</span>
              </i-col>
              <Col span="14">
                <Input v-model="item.appVersion" placeholder="请填写app构建版本" style="margin: 10px"/>
              </Col>
            </Row>

              <Row v-show="item.tradeCoopId != undefined">
                <i-col style="margin: 10px;text-align: right" span="8">
                  <span style="text-align: right; display: inline-block;margin-top: 5px">{{item.appName}}交易渠道号:</span>
                </i-col>
                <Col span="14">
                  <Input v-model="item.tradeCoopId" placeholder="请填写交易渠道号" style="margin: 10px"/>
                </Col>
              </Row>
              <Row v-show="item.tradeActionId != undefined">
                <i-col style="margin: 10px;text-align: right" span="8">
                  <span style="text-align: right; display: inline-block;margin-top: 5px">{{item.appName}}交易活动号:</span>
                </i-col>
                <Col span="14">
                  <Input v-model="item.tradeActionId" placeholder="请填写交易活动号" style="margin: 10px"/>
                </Col>
              </Row>
              <Row v-show="item.channelId != undefined">
                <i-col style="margin: 10px;text-align: right" span="8">
                  <span style="text-align: right; display: inline-block;margin-top: 5px">{{item.appName}}App渠道号:</span>
                </i-col>
                <Col span="14">
                  <Input v-model="item.channelId" placeholder="请填写App渠道号" style="margin: 10px"/>
                </Col>
              </Row>
              <Row v-show="item.scan != undefined">
                <i-col style="margin: 10px;text-align: right" span="8">
                  <span style="text-align: right; display: inline-block;margin-top: 5px">sonar扫描:</span>
                </i-col>
                <Col span="14">
                  <Checkbox v-model="item.scan" style="margin: 15px">扫描请勾选</Checkbox>
                </Col>
              </Row>

          </Card>
      </Row>
  </div>
</template>

<script>

import {
  findH5ZipVersionByEnv,
  findH5ZipVersionByEnvForPlatform,
  findTestSuiteCodeOfH5Zip,

} from "@/spider-api/h5"

    export default {
        name: "appPublishParamsEdit",

        props: {
            item: Object,
            scan: String,
            checked: Boolean
        },
        computed:{
        },
        data() {
            return {
              testSuiteCodeOfH5ZipList: [],
              h5ZipVersionList:[]

            }
          },
      watch: {
          checked(val){

            if (val){
              this.getTestSuiteCodeOfH5Zip()
            }
          }
      },
        mounted() {
          this.getTestSuiteCodeOfH5Zip()
      },
        methods: {

          onChangeFindH5ZipVersionByEnv(val){
            delete this.item.h5ZipVersion
            this.h5ZipVersionList=[]
            let env = val?val:'test'
          //查询h5资源包版本
            
            findH5ZipVersionByEnvForPlatform(this.item.h5PlatFormCode ,env).then(res => {
              this.h5ZipVersionList = res.data.data
            }).catch(err =>{
              console.info("findH5ZipVersionByEnvForPlatform:"+err.data)
            })
          },

          getTestSuiteCodeOfH5Zip() {
            findTestSuiteCodeOfH5Zip().then(res => {
              this.testSuiteCodeOfH5ZipList = res.data.data?res.data.data:[{}]
            }).catch(err => {
              console.info("findTestSuiteCodeOfH5Zip:"+err.data)
              })
            },

            }

    }

</script>

<style scoped>

</style>
