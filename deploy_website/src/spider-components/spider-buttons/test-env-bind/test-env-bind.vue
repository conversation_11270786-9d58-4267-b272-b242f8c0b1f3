<template>
  <div>
      <Button
        ghost
        type="primary"
        style="text-align: left; display: inline-block; width:80px; margin:5px"
        ref="env_bind"
        @click="env_bind"
      >环境绑定</Button>

      <Modal
      v-model="env_modal_val"
      title="批量环境绑定"
      @on-ok="env_modal_ok"
      @on-cancel="env_modal_cancel">
        <Row v-for="app_name in app_list" :key="app_name">
          <i-col style="margin: 10px" span="4">
            <span style="text-align: left; display: inline-block;">{{ app_name }}</span>
          </i-col>
          <Col span="12" style="margin: 5px">
            <Select multiple v-model="bind_env_list[app_name]" >
              <Option
                v-for="item in app_env_list[app_name]"
                :value="item"
                :key="item"
              >{{ item }}</Option>
            </Select>
          </Col>
        </Row>
    </Modal>
  </div>
</template>

<script>
      import {
        pipelineEnvBind,
        pipelineEnvBindGet,
      } from "@/spider-api/h5"

    export default {
        name: "testEnvBind",
        props: {
          app_list: Array,
          iter_id: String,
        },
        data(){
          return{
            env_modal_val: false,
            app_env_list:{},
            bind_env_list:{}
          }
        },
        methods: {
            getEnvInfo(){

            appSuiteCodeApi(this.iter_id).then(res => {
              this.app_env_list = res.data.data.app_suite_info
            })
            },
            env_bind(){
               this.getEnvInfo()
              this.env_modal_val = true
            },
            env_modal_ok(){
              console.log(this.bind_env_list)
              pipelineEnvBind(
                {"iter_id": this.iter_id,
                "bind_env_list": this.bind_env_list}
              ).then(res =>{
                 this.$Message.success(res.data.msg)
                  this.$emit('setBindEnvList', this.bind_env_list)
              })
              .catch(err => {
                  console.log('============= err =========')
                  console.log(err)
                  this.$Message.error(err)
                })
              this.env_modal_val = false
            },
            env_modal_cancel(){
              this.env_modal_val = false
            }
        },
        created() {
          pipelineEnvBindGet(this.iter_id).then(res =>{

            console.log(res.data.data["bind_env_list"])
            this.pipelineEnvBindGetpipelineEnvBindGet = res.data.data["bind_env_list"]
            console.log(this.bind_env_list)
            this.$emit('setBindEnvList', this.bind_env_list)
            })
        },
    }
</script>

<style scoped>

</style>
