<template>
    <div>
      <Button
        type="primary"
        :loading="button_disabled"
        style="text-align: left; display: inline-block;  margin:5px"
        @click="batch_compile"

      >{{button_name}}</Button>
    <Modal
        v-model="compile_modal"
        title="编译提醒"
        :loading="loading"
        @on-ok="sameRepoBatchCompile">
        <p>代码同仓库下的应用会默认勾选一起编译！！！！</p>
        <div v-for="(item, index) in table_data" :key="index">
        <Checkbox v-model="item.check" :default-checked="item.check" :disabled="item.check">{{ item.app_name }}</Checkbox>
      </div>
    </Modal>
      </div>
</template>

<script>
  import {
        compileCheckApi,
        h5CompileApi,
        getInfoNoticeShowTime,
        getErrNoticeShowTime,
        externalServiceResult,
      } from "@/spider-api/h5"

    export default {
      name: "Compile",
      props: {
        table_data: Array,
        ci_info: Array,
        action_item: String,
        iter_id: String,
        br_name: String,
      },
      data() {
        return {
          button_disabled:false,
          button_name:"批量编译",
          compile_modal: false,
          loading:false,
          check:true,
        }
      },
      watch: {
        table_data: {
          handler: function (val, oldVal) {
            this.$Spin.hide()
          },
          
        }
      },
      methods: {
        /**
         * 编译状态检查
         */
        compile_status_check(build_info_list) {
         console.log(build_info_list)
          for (let row of build_info_list) {
            if (row.compile_status ==="compile_running" || row.compile_status === "running") {
              this.$Notice.error({
                desc: '以下应用' + row.app_name + '正在进行'+row.compile_status+'，请等待编译结束后再次发布',
                duration: 100,
              })
              return false

            }
            if (row.publish_status === "publish_running" || row.publish_status ==="running") {
              this.$Notice.error({
                desc: '以下应用' + row.app_name + '正在进行'+row.publish_status+'，请等待发布结束后再次发布',
                duration: 100,
              })
              return false
            }
          }
          return true
        },

        buttonWait(num){
          let vm = this
           setTimeout(function () {
            if (num>0){
              num= num-1
               vm.button_name="Loading "+num+"s"
               vm.buttonWait(num)
            }
            else{
              vm.button_name="批量编译";
              vm.button_disabled=false
            }
              }, 1000)
        },
        batch_compile() {
          if (this.compile_status_check(this.table_data) == false) {
            return false
          }
          if (this.table_data.length == 0) {
            this.$Spin.show({
                render: (h) => {
                  return h('div', [
                    h('Icon', {
                      'class': 'demo-spin-icon-load',
                      props: {
                        type: 'ios-loading',
                        size: 18
                      }
                    }),
                    h('div', '行为数据回填中请稍等。。。')
                  ])
                  }
                  })
          }
          // this.$Spin.hide()
          this.compile_modal = true
          // this.checkBeforeCompile({'iteration_id': this.iter_id, 'app_name_list': app_name_list})
          // this.compile(build_info_list)
        },
        sameRepoBatchCompile(){
          this.button_disabled=true
          this.buttonWait(10)
          // 获取选中的列表数据
          let build_info_list = []
          console.log(this.table_data)
          for (let row of this.table_data){
            build_info_list.push({
                    'iteration_id': this.iter_id,
                    'br_name': this.br_name,
                    'app_name': row.app_name,
                    'action_item': this.action_item,
                    })
          }

          if (build_info_list.length == 0) {
            this.$Notice.error({
              desc: '至少选择一个应用',
              duration: getErrNoticeShowTime(),
            })
            return false
          }
          if (this.compile_status_check(this.table_data) == false) {
            return false
          }
          let app_name_list = []
          for (let row of build_info_list) {
            app_name_list.push(row.app_name)
          }
          this.compile_modal = true
          // this.sameProjectCompile(app_name_list)
          this.checkBeforeCompile({'iteration_id': this.iter_id, 'app_name_list': app_name_list})
          this.compile(build_info_list)
          this.compile_modal = false
        },
        /**
         * 编译前检查项目
         */
        checkBeforeCompile(req) {
          compileCheckApi(req).then(res => {
            if (res.data.status === "success") {
              console.log("============res =========")
              console.log(res.data.data)
              let sid = res.data.data.sid
              this.checkCompileResult(sid)
            }
            else {
              this.$Notice.error({
                desc: res.data.msg,
                duration: getErrNoticeShowTime(),
              });
              return false
            }
          })
        },
        /**
         * 编译检查结果轮训查看
         */
        checkCompileResult(sid) {
          externalServiceResult(sid).then(res => {
            console.log("======== externalServiceResult ===========")
            console.log(res)
            let status = res.data.data.status;
            let detail = res.data.data.detail;
            if (status == "success") {
            }
            else if (status == "failure") {
              this.$Notice.error({
                desc: detail,
                duration: getErrNoticeShowTime(),
              });
            }
            else {
              let vm = this;
              setTimeout(function () {
                vm.checkCompileResult(sid)
              }, 2000)
            }
          })
        },

        /**
         * 编译
         */
        compile(build_info_list) {
          console.log('========= compile START ============')
          let vm = this
          console.log(build_info_list)
          h5CompileApi(build_info_list).then(res => {
            console.log(res)
            if (res.status == '200') {
              return true
            }
            else {
              //失败
              vm.$Notice.error({
                desc: res.data.msg,
                duration: getErrNoticeShowTime(),
              });
              return false
            }
          })
        },
      sameProjectCompile(build_info_list) {
        console.log('同仓库一下应用一起编译')
        console.log(this.$store.state.ci_info_select)
        console.log(build_info_list)
        console.log(this.table_data)
        console.log(this.table_data)
      },

      },
    }
</script>


<style scoped>

</style>
