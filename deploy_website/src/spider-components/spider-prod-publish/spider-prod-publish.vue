<template>
  <Card shadow>
    <Row style="margin-top: 1em">
      <i-col span="2">迭代版本</i-col>
      <i-col span="2">{{ this.pipeline_id }}</i-col>
    </Row>
    <Row style="margin-top: 2em">
      <i-col span="3">产线应用列表</i-col>
    </Row>
    <Row style="margin-top: 2em">
      <i-col span="6">
        <span>节点是否一致：</span>
        <span style="color: green" v-if="consistentNode">一致 </span>
        <span style="color: red" v-else v-bind:title="nodeInfo">不一致</span>
        <Button type="primary" shape="circle" icon="ios-refresh" size="small"
                @click="refreshNodeConsistent"></Button>
        <span style="color:#a59f9f">{{consistentTime}}</span>
      </i-col>
    </Row>
    <Table style="margin-top: 1em" :columns="publish_columns" :data="publish_data"></Table>
    <Row style="margin-top: 2em">
      <i-col span="15">备注：当天没有发布，不支持回滚操作。如果需要回滚，请联系系统运维人员</i-col>
    </Row>
    <Modal
      v-model="ops_operate_modal"
      width="680"
      :mask-closable="false"
      @on-cancel="cancelHistory"
    >
      <p slot="header" style="color:gray;">
        <Icon type="md-clipboard"></Icon>
        <span>{{ app_name }} 执行历史</span>
      </p>
      <div style="height:300px; overflow-y: auto;">
        <table style="margin: 10px;" v-for="cont in historyCont" :key="cont.index">
          <tr>
            <td width="15px">
              <Icon type="md-arrow-round-forward"></Icon>
            </td>
            <td width="100px" style="color: darkblue;">{{ cont.operator }}</td>
            <td width="50px" style="color: black">{{ cont.type }}</td>
            <td width="400px">{{ cont.operateTime }}</td>
          </tr>
          <tr>
            <td width="15px"></td>
            <td width="100px" style="border-bottom: #DDDDDD solid 2px; color: black;">{{ cont.ip }}</td>
            <td
              width="450px"
              colspan="2"
              style="border-bottom: #DDDDDD solid 2px;"
              v-if="cont.detail !== 'error'"
            >
              <span v-html="cont.detail"></span>
            </td>
            <td
              width="450px"
              colspan="2"
              style="border-bottom: #DDDDDD solid 2px; color: red;"
              v-else
            >
              <span v-html="cont.detail"></span>
            </td>
          </tr>
        </table>
      </div>
      <div slot="footer">
        <Button @click="cancelHistory">关闭</Button>
      </div>
    </Modal>
    <Modal
        v-model="modal1"
        title="发布前提醒"
        @on-ok="Ok"
        @on-cancel="Cancel">
        <p style="color: crimson;font-size: 16px"  >1、宙斯应用外移文件必须更新！请确认已经点过【配置更新】按钮！</p>
        <p style="color: crimson;font-size: 16px"  >2、请确认SQL审核通过，并已产线库执行！</p>
        <div slot="footer">
        <Button @click="Cancel">关闭</Button>
        <Button @click="Ok">确定发布，已经点过【配置更新】按钮！</Button>
      </div>
    </Modal>
  </Card>
</template>

<script>
import store from '@/spider-store'
import {
  getProPublishOrderInfo,
  proPublishOperate,
  proPublishInfo,
  AppPublishCheckApi,
  getProdRollbackStat
} from '@/spider-api/publish'
import {
  getServiceResult
} from '@/spider-api/iter-plan'
import {
  checkConfigConsistentApi,
  publishConfigBranch
} from '@/spider-api/zues'
import { getNodeConsistentInfo } from '@/log/log'
import { formatDateHour } from '@/libs/util'

export default {
  name: 'SpiderPROPublish',
  data () {
    return {
      consistentTime: '',
      consistentNode: true,
      nodeInfo: '',
      pipeline_id: '',
      ops_operate_modal: false,
      modal1: false,
      switch_history: '',
      app_name: '',
      historyCont: [],
      publish_data: [],
      deploy_node: [],
      params: '',
      ok: false,
      cancel: '',
      publish_columns: [
        {
          title: '应用名',
          key: 'appName',
          width: 190
        },
        {
          title: '环境',
          key: 'env',
          width: 90
        },
        {
          title: '服务器',
          key: 'ip',
          width: 190,
          render: (h, params) => {
            let nodes = []
            params.row.ip.forEach((item) => {
              let vnode = h('Option', {
                props: {
                  value: item
                }
              })
              nodes.push(vnode)
            })
            return h(
              'Select',
              {
                style: {},
                props: {
                  placeholder: '',
                  value: '',
                  size: 'small',
                  transfer: true
                },
                on: {
                  'on-change': (val) => {
                    params.row.showLogIp = val
                    getProdRollbackStat(params.row.appName, val)
                      .then((res) => {
                        if (res.data.status === 'success') {
                          // 回滚可用
                          params.row.rollbackStatus = false
                          this.$set(this.publish_columns, params)
                        } else {
                          // 回滚不可用
                          params.row.rollbackStatus = true
                          this.$set(this.publish_columns, params)
                        }
                      })
                      .catch((err) => {
                        this.$Message.error(err.response.data.msg)
                      })
                    this.deployNodeChange(params, val)
                  }
                }
              },
              nodes
            )
          }
        },
        {
          title: '操作',
          width: 430,
          align: 'center',
          render: (h, params) => {
            return h('div', [
              h(
                'Button',
                {
                  attrs: {
                    class:
                        'ivu-btn ivu-btn-primary ivu-btn-small ivu-btn-ghost'
                  },
                  props: {
                    size: 'small'
                  },
                  style: {
                    marginRight: '1em'
                  },
                  on: {
                    click: () => {
                      this.modal1 = true
                      this.params = params
                      // this.prodPublish(params, "deploy");
                    }
                  }
                },
                '发布+启动'
              ),
              h(
                'Button',
                {
                  attrs: {
                    class:
                        'ivu-btn ivu-btn-primary ivu-btn-small ivu-btn-ghost'
                  },
                  props: {
                    size: 'small'
                  },
                  style: {
                    marginRight: '1em'
                  },
                  on: {
                    click: () => {
                      this.doOperate(params, 'restart')
                    }
                  }
                },
                '重启'
              ),
              h(
                'Button',
                {
                  attrs: {
                    class:
                        'ivu-btn ivu-btn-primary ivu-btn-small ivu-btn-ghost'
                  },
                  props: {
                    size: 'small'
                  },
                  style: {
                    marginRight: '1em'
                  },
                  on: {
                    click: () => {
                      this.doOperate(params, 'stop')
                    }
                  }
                },
                '停止'
              ),
              h(
                'Button',
                {
                  attrs: {
                    class:
                        'ivu-btn ivu-btn-primary ivu-btn-small ivu-btn-ghost'
                  },
                  props: {
                    disabled: params.row.rollbackStatus,
                    size: 'small'
                  },
                  style: {
                    marginRight: '1em'
                  },
                  on: {
                    click: () => {
                      this.doOperate(params, 'rollback')
                    }
                  }
                },
                '回滚'
              ),
              h(
                'Button',
                {
                  attrs: {
                    class:
                        'ivu-btn ivu-btn-primary ivu-btn-small ivu-btn-ghost'
                  },
                  props: {
                    size: 'small'
                  },
                  style: {
                    marginRight: '1em'
                  },
                  on: {
                    click: () => {
                      this.doOperate(params, 'update')
                    }
                  }
                },
                '配置更新'
              ),
              h(
                'Button',
                {
                  attrs: {
                    class:
                        'ivu-btn ivu-btn-primary ivu-btn-small ivu-btn-ghost'
                  },
                  props: {
                    size: 'small'
                  },
                  style: {
                    marginRight: '1em'
                  },
                  on: {
                    click: () => {
                      this.doOperate(params, 'code_update')
                    }
                  }
                },
                '代码更新'
              )
            ])
          }
        },
        {
          title: '执行历史',
          width: 90,
          align: 'center',
          render: (h, params) => {
            return h('div', [
              h(
                'Button',
                {
                  attrs: {
                    class:
                        'ivu-btn ivu-btn-success ivu-btn-small ivu-btn-ghost'
                  },
                  props: {
                    size: 'small'
                  },
                  style: {},
                  on: {
                    click: () => {
                      this.showHistory(params.row.appName)
                    }
                  }
                },
                '执行历史'
              )
            ])
          }
        },
        {
          title: '服务日志',
          width: 90,
          align: 'center',
          render: (h, params) => {
            return h('div', [
              h('Button', {
                attrs: {
                  class: 'ivu-btn ivu-btn-primary ivu-btn-small ivu-btn-ghost'
                },
                props: {},
                style: {
                  marginRight: '6px'
                },
                on: {
                  click: () => {
                    this.renderFunc(params.row.showLogIp, params.row.appName)
                  }
                }
              }, '服务日志')
            ])
          }
        }
      ]
    }
  },
  methods: {
    // 打开日志查看tab
    renderFunc (ip, app_name) {
      if (ip !== '') {
        let routeData = this.$router.resolve({
          path: '/log',
          query: {
            ip: ip,
            app_name: app_name
          }
        })
        window.open(routeData.href, '_blank')
      } else {
        this.$Message.error('请选择节点IP！')
      }
    },
    // 刷新节点是否一致状态
    refreshNodeConsistent () {
      console.info(this.publish_data)
      if (this.publish_data.length > 0) {
        let ipList = []
        let appName = ''
        let res_dict = ''
        for (let nodeInfo in this.publish_data) {
          ipList = (this.publish_data[nodeInfo].ip)
          appName = this.publish_data[nodeInfo].appName
          getNodeConsistentInfo({ 'ip_list': ipList, 'app_name': appName }).then(res => {
            if (res.data.status === 'success') {
              if (res.data.data.diff_num > 0) {
                res_dict = res_dict.concat(res.data.data.res_dict)
              } else {

              }
            } else {
              this.$Message.error(res.data.msg)
            }
          })
        }
        if (typeof res_dict === 'undefined' || res_dict == null || res_dict === '') {
          this.consistentNode = true
          this.nodeInfo = ''
        } else {
          this.consistentNode = false
          this.nodeInfo = res_dict
        }
        this.consistentTime = formatDateHour(new Date())
      }
    },
    getStatus (sid, params) {
      getServiceResult(sid)
        .then((res) => {
          if (res.data.status === 'success') {
            var status = res.data.data.status
            var detail = res.data.data.detail
            if (status === 'success') {
              this.$Message.success(detail)
              this.doOperate(params, 'deploy')
            } else if (status === 'failure') {
              // this.$Message.error(err.response.data.msg)
              alert(detail)
            } else {
              let vm = this
              setTimeout(function () {
                vm.getStatus(sid, params)
              }, 2000)
            }
          } else {
            alert(res.data.msg)
          }
        })
        .catch((err) => {
          this.$Message.error(err.response.data.msg)
          alert(err.response.data.msg)
        })
    },

    initThisVue () {
      this.pipeline_id = store.state.iterationID
      getProPublishOrderInfo(this.pipeline_id)
        .then((res) => {
          if (res.data.status === 'success') {
            this.publish_data = res.data.data['publish_list']
          } else {
            this.$Message.error(res.data.msg)
          }
        })
        .catch((err) => {
          this.$Message.error(err.response.data.msg)
        })
    },
    deployNodeChange (params, val) {
      let app_name = params.row.appName
      let env = params.row.env
      this.deploy_node[app_name + '_' + env] = val
      console.info(this.deploy_node)
    },
    doOperate (params, op_type) {
      console.info(this.deploy_node)
      let node = this.deploy_node[params.row.appName + '_' + params.row.env]
      proPublishOperate(this.pipeline_id, params.row.appName, node, op_type)
        .then((res) => {
          if (res.data.status === 'success') {
            // 回滚可用
            params.row.rollbackStatus = false
            this.$set(this.publish_columns, params)
            this.$Message.success(res.data.msg)
          } else {
            this.$Message.error(res.data.msg)
          }
        })
        .catch((err) => {
          this.$Message.error(err.response.data.msg)
        })
    },

    // prodPublish(params, op_type) {
    //   this.params = params;
    //   AppPublishCheckApi(store.state.iterationID, params.row.appName).then(
    //     (res) => {
    //       if (res.data.status === "success") {
    //         this.$Message.success(res.data.msg);
    //         this.getStatus(res.data.data["sid"]);
    //       } else {
    //         alert(res.data.msg);
    //       }
    //     }
    //   );
    // },
    prodPublish (params, op_type) {
      // this.params = params;
      if (!this.ok) {
        // alert('liuwei')
        return
      }
      checkConfigConsistentApi(store.state.iterationID, params.row.appName, 'prod').then(result => {
        if (result.data.status === 'success') {
          this.$Message.success(result.data.msg)
          publishConfigBranch(store.state.iterationID, params.row.appName, 'prod').then(result => {
            if (result.data.status === 'success') {
              this.$Message.success(result.data.msg)
              AppPublishCheckApi(store.state.iterationID, params.row.appName, 'prod').then(res => {
                if (res.data.status === 'success') {
                  this.$Message.success(res.data.msg)
                  this.getStatus(res.data.data['sid'], params)
                } else {
                  alert(res.data.msg)
                }
              }
              )
            } else {
              this.$Message.error(result.data.msg)
              alert(result.data.msg)
            }
          })
        } else {
          this.$Message.error(result.data.msg)
          alert(result.data.msg)
        }
      })
      // AppPublishCheckApi(store.state.iterationID,params.row.appName).then(res =>{
      //    if (res.data.status === "success") {
      //      this.$Message.success(res.data.msg);
      //      this.getStatus(res.data.data["sid"])
      //    } else {
      //      alert(res.data.msg);
      //    }
      //   }
      //      )
    },
    get_history_data (app_name) {
      proPublishInfo(app_name)
        .then((res) => {
          if (res.data.status === 'success') {
            this.historyCont = res.data.data
          } else {
            this.historyCont = []
          }
        })
        .catch((err) => {
          this.$Message.error(err.response.data.msg)
        })
    },
    showHistory (app_name) {
      this.app_name = app_name
      this.ops_operate_modal = true
      this.get_history_data(app_name)
      this.switch_history = setInterval(this.get_history_data(app_name), 5000)
    },
    Ok () {
      this.ok = true
      // this.modal1 = true;
      this.prodPublish(this.params, 'deploy')
      this.ok = false
      this.modal1 = false
    },
    Cancel () {
      this.ok = false
      this.modal1 = false
    },
    cancelHistory () {
      this.app_name = ''
      this.ops_operate_modal = false
      this.historyCont = []
      if (this.switch_history) {
        clearInterval(this.switch_history)
      }
    }
  },
  mounted () {
  }
}
</script>
