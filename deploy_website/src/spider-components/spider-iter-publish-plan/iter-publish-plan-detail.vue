<template>
  <div ref="publishPlanDetail" style="
    display: flex;
    justify-content: center;
    align-items: center;
">
    <div class="publish_plan" v-if="node_show" style="width: 80%; margin-top: 20em;">
      <Steps :current="current">
        <Step v-if="current === 1" title="已完成" id="publish_plan_start" content="发起"></Step>

        <Step v-else title="已完成" content="发起"></Step>
        <Step v-for="node in plan_nodes" v-if="node.node_status == 'SUCCESS'" :title="node.node_name"
              id="publish_plan_start"
              :node-id="'node:'+node.id" :content="node.node_status"></Step>

        <Step v-for="node in plan_nodes" v-if="node.node_status == 'RUNNING'" id="running_node"
              :title="node.node_name" :node-id="'node:'+node.id" :content="node.node_status"></Step>

        <Step v-for="node in plan_nodes" v-if="node.node_status  == 'FAIL'" id="fail_node"
              :title="node.node_name" :node-id="'node:'+node.id" :content="node.node_status"></Step>

        <Step v-for="node in plan_nodes" v-if="node.node_status  == 'READY'"
              :title="node.node_name" :node-id="'node:'+node.id" :content="node.node_status"></Step>
      </Steps>
    </div>

    <Modal
      :title="curren_node.node_name"
      v-model="node_result_show"
      :closable="true"
      :width="300"
      :mask-closable="false"
      class="publish_plan_modal_class"
    >
      <div>
        <div> 详情: {{ curren_node.node_run_result }}</div>
      </div>
      <div slot="footer">
      </div>
    </Modal>

  </div>
</template>

<script>
import {get_publish_plan_nodes} from "@/spider-api/iter-plan";

export default {
  name: 'IterPublishPlanDetail',
  data() {
    return {
      node_result_show: false,
      curren_node: {},
      current: 1,
      node_show: true,
      publishPlanDetailHeight: 800,
      batch_no: "",
      plan_nodes: [],
      interval: 0
    }
  },
  props: {
    pipeline_id: {
      type: String,
      default: ''
    }
  },
  methods: {
    get_publish_plan_nodes_new_status(batch_no) {
      this.batch_no = batch_no
      get_publish_plan_nodes({"iter_id": this.pipeline_id, "batch_no": batch_no}).then(res => {
        this.node_show=!this.node_show
        this.plan_nodes = []

        //获取应用信息
        if (res.data.status === "success") {
          this.plan_nodes = res.data.data
          this.plan_nodes.push({
            "id": "1000",
            "node_name": "结束",
            "node_status": "READY"
          })
          console.log(this.plan_nodes)
        } else {
          this.$Notice.error({
            title: 'Error',
            desc: res.data.message,
            duration: 0
          });
        }
        this.node_show=!this.node_show
        let _this = this
        setTimeout(() => {
          let node_index = 1
          let current = 0
          _this.plan_nodes.forEach(function (node, index) {
            if ((node.node_status === 'RUNNING') && current === 0) {
              current = index+1
            }
            if (node.node_status === 'FAIL' || node.node_status === 'STOP') {
              current = index + 1
            }
            node_index = index + 1
            // 绑定点击事件
            const element = document.querySelector(('[node-id="node:' + node.id + '"] .ivu-steps-head'));
            element.addEventListener('click', function () {
              _this.curren_node = node
              if (node.node_run_result) {
                if (node.node_run_result.show_type === 'external_link') {
                  window.open(node.node_run_result.external_link_url)
                }
                if (node.node_run_result.show_type === "Modal") {
                  _this.node_result_show = true
                }
              } else {
                let detail = "等待运行"
                if (node.node_status === 'RUNNING') {
                  detail = "运行中"
                } else if (node.node_status === 'STOP') {
                  detail = "已终止"
                }
                _this.curren_node.node_run_result = {"detail": detail}
                _this.node_result_show = true
              }
            }.bind(null, node));
            // 绑定进度条
            element.style.cursor = 'pointer'
          })
          if (current > 0) {
            _this.current = current
          } else {
            _this.current = node_index
          }
          const running_node_progress_element = document.querySelector('#publish_plan_start .ivu-steps-tail');
          if (running_node_progress_element) {
            running_node_progress_element.classList.add('progress-bar-animation')
          }
          // 绑定动画
          const running_node_element = document.querySelector('#publish_plan_running .ivu-steps-head-inner');
          if (running_node_element) {
            running_node_element.classList.add('running_node_class')
          }
          const fail_node_element = document.querySelector('#fail_node  .ivu-steps-head-inner');
          if (fail_node_element) {
            fail_node_element.classList.add('fail_node_class')
          }
          const running_node_element1 = document.querySelector('#running_node  .ivu-steps-head-inner');
          if (running_node_element1) {
            running_node_element1.classList.add('running_node_class')
          }
        }, 100)
        this.publishPlanDetailHeight = window.innerHeight * 0.7
        this.$refs.publishPlanDetail.style.height = this.publishPlanDetailHeight + 'px'

      }).catch(err => {
        this.$Notice.error({
          title: 'Error',
          desc: err.message,
          duration: 0
        });
      })
    },
    initThisVue(batch_no) {
      let _this = this
      _this.get_publish_plan_nodes_new_status(batch_no)
      this.interval = setInterval(function () {
        _this.get_publish_plan_nodes_new_status(batch_no)
      }, 10000)
    },
    cancel_interval() {
      clearInterval(this.interval)
    }
  }
  ,
  mounted() {

  }
  ,
  destroyed() {
    clearInterval(this.interval)
  }
}
</script>

<style>
.publish_plan {
  height: 100%;
}

.running_node_class {
  animation: pulse 2s infinite;
}

@keyframes progress-bar-animation {
  0% {
    width: 0%;
  }
  80% {
    width: 80%;
    background-color: rgba(255, 255, 255, 0.7);
  }
  100% {
    width: 100%;
    background-color: rgba(255, 255, 255, 0);
  }
}

.progress-bar {
  width: 0%;
  height: 30px;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 5px;
  animation: progress-bar-animation 2s linear infinite;
}

.fail_node_class {
  background-color: #f02d55 !important;
  border-color: #f02d55;
}
.running_node_class {
  background-color: #2d8cf0 !important;
  border-color: #2d8cf0;
}


</style>
