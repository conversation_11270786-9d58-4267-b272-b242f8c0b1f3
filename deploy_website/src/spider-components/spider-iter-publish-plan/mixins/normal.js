/*
 * @Author: chao.bao <EMAIL>
 * @Date: 2024-09-13 10:41:04
 * @LastEditors: chao.bao <EMAIL>
 * @LastEditTime: 2025-01-03 14:42:46
 * @FilePath: /website_web/deploy_website/src/spider-components/spider-iter-publish-plan/mixins/common.js
 * @Description: 发布弹框公共逻辑
 */
// import resData from './test.json'
import {
    getFlowListNew,
    getFlowTableList,
    getEmailAddressesRequest,
    getOnlineSqlApplyApi,
    getPublishConfig,
    getTestSuitCode,
    getSuitByIterId,
    getGroupPublishAppList,
    saveGroupPbulishMode
} from '@/spider-api/iter-plan'
export default {
    data() {
        return {
            optionsList: {
                email_address_options: [], // 邮件地址
                tactics_options: [], // 归档策略
                suite_by_iter_id_options: [] // 查询迭代发布的环境列表
            },
            enumType: {
                Input: 'text',
                Select: 'select',
                DatePicker: 'time_select',
                Dragger: 'draggable_order_list',
                Table: 'table'
            },
            loadding: false,
            publishData: {
                sqlData: [] // 选择提交SQL  列表数据
            },
            labelWidth: 100,
            graphyColumns: [
                {
                    title: '测试集',
                    key: 'testSetName'
                },
                {
                    title: '应用',
                    key: 'appName'
                },
                {
                    title: '测试脚本(案例)分支',
                    key: 'script_branch',
                    render: (h, params) => {
                        const optionsArr = params.row.select_branch_list.map(item => {
                            return h(
                                'Option',
                                {
                                    props: {
                                        value: item
                                    }
                                },
                                item
                            )
                        })
                        return h(
                            'Select',
                            {
                                props: {
                                    transfer: true,
                                    clearable: true,
                                    value: params.row.script_branch,
                                    filterable: true
                                },
                                on: {
                                    'on-change': value => {
                                        // 当选择项改变时更新数据
                                        this.applyData[this.curTableIndex].flowTableData[
                                            params.index
                                        ].script_branch = value
                                        console.log('change', params, this.applyData)
                                    }
                                }
                            },
                            optionsArr
                        )
                    }
                }
            ],
            columns: [
                {
                    title: '库',
                    key: 'sql_ver_db'
                },
                {
                    title: '脚本',
                    key: 'sql_ver_name'
                },
                {
                    type: 'selection',
                    width: 60,
                    align: 'center'
                }
            ],
            // ---------------------------normal多应用
            // 表单数据
            publish_plan_phases: [],
            publish_before_config: [],
            curTableIndex: 0,
            applyList: [],
            applyData: [],
            dragSuite: '',
            dragData: [],
            dragUpData: [],
            group: {
                name: 'site',
                pull: true, //可以拖从
                put: true //可以拖出
            }
        }
    },
    props: {
        iter_app_options: {
            // 应用列表
            type: Array,
            default: () => []
        },
        env_options: {
            // 环境列表
            type: Array,
            default: () => []
        },
        user_options: {
            // 审核人列表
            type: Array,
            default: () => []
        },
        plan_type: {
            type: String,
            default: ''
        },
        pipeline_id: {
            type: String,
            default: ''
        }
    },
    methods: {
        // 校验
        validate() {
            // 1.多应用校验
            let befoeConfigSubmit = true
            let before_phase = {}
            this.publish_before_config.map((phase, index) => {
                phase.nodes.map(node => {
                    node.node_params.map(param => {
                        if (param.execute_auto_test) {
                            param.module_params.map(module => {
                                if (
                                    module.param_show_flag &&
                                    ![this.enumType.Table].includes(module.param_show_type) &&
                                    !module.param_value
                                ) {
                                    befoeConfigSubmit = false
                                    before_phase = param
                                }
                            })
                        }
                    })
                })
            })
            console.log('before校验', befoeConfigSubmit, before_phase)
            if (!befoeConfigSubmit) {
                this.$Message.warning({
                    content: `${before_phase.module_name}-自动化 阶段参数配置不完整！！！`,
                    duration: 2
                })
                return false
            }

            // 2.其他校验
            let can_submit = true
            let block_phase = {}
            this.publish_plan_phases.map((phase, index) => {
                phase.nodes.map(node => {
                    node.node_params.map(param => {
                        if (
                            phase.required &&
                            param.param_show_flag &&
                            ![this.enumType.Table, this.enumType.Dragger].includes(param.param_show_type) &&
                            !param.param_value
                        ) {
                            can_submit = false
                            block_phase = phase
                        }
                    })
                })
            })
            console.log('can_submit', can_submit)

            if (!can_submit) {
                this.$Message.warning({
                    content: `${block_phase.phase_name} 阶段参数配置不完整！！！`,
                    duration: 2
                })
                return false
            }
            return true
        },
        selectTableHandler(selectArr) {
            const strArr = selectArr.map(item => item.sql_ver_name)
            console.log('strArr---', strArr)

            // 将数据设置到publish_plan_phases中 - 列表数据放到param_value中还是选中的值放到param_value中???还是把选中项放到param_value中
            this.publish_plan_phases.forEach(phase => {
                phase.nodes.forEach(node => {
                    node.node_params.forEach(param => {
                        if (param.param_key === 'commit_sql') {
                            param.param_value = strArr
                        }
                    })
                })
            })
        },
        showSnapshot(time) {
            const now = new Date(time)
            const year = now.getFullYear()
            const month = String(now.getMonth() + 1).padStart(2, '0') // 月份从 0 开始，所以要加 1，并确保两位数显示
            const day = String(now.getDate()).padStart(2, '0')
            const hours = String(now.getHours()).padStart(2, '0')
            const minutes = String(now.getMinutes()).padStart(2, '0')
            const seconds = String(now.getSeconds()).padStart(2, '0')

            const res = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
            return res
        },
        // 发布计划数据
        getPublishData() {
            this.publish_plan_phases.map((phase, index) => {
                phase.nodes.map(node => {
                    node.node_params.map(param => {
                        // 格式化日期
                        if (param.param_key === 'schedule_time' && param.param_value) {
                            param.param_value = this.showSnapshot(new Date(param.param_value).getTime())
                        }
                    })
                })
            })
            // applyData
            this.publish_before_config.map(phase => {
                phase.nodes.map(node => {
                    node.node_params.map((param, index) => {
                        // 将编排table数据挂到publish_before_config中
                        param.testset_detail_list = this.applyData[index].flowTableData
                    })
                })
            })
            const data = {
                iteration_id: this.pipeline_id,
                plan_type: this.plan_type,
                publish_before_config: this.publish_before_config,
                publish_config: this.publish_plan_phases
            }
            console.log('发布数据------------', data)

            return data
        },
        // ---------------------------normal多应用
        clearData() {
            this.publish_plan_phases = []
            this.publish_before_config = []
            this.applyList = []
            this.applyData = []
            this.dragData = []
            this.dragUpData = []
        },
        rowClick(index) {
            console.log('rowClick', index)
            // 存储当前触发的是哪个Table，用于在select的change事件中判断修改数据
            this.curTableIndex = index
        },
        saveGroup() {
            // 保存策略
            saveGroupPbulishMode({
                suite_code: this.dragSuite,
                iteration_id: this.pipeline_id,
                app_node_dict: this.dragData.flat()
            }).then(res => {
                if (res.data.status === 'success') {
                    this.$Message.success(res.data.msg)
                } else {
                    this.$Message.error(res.data.msg)
                }
            })
        },
        // 拖拽完成
        add() {
            // 处理顺序问题 - todo
            this.dragData.forEach((dragItem, dragIndex) => {
                dragItem.forEach(item => {
                    item.step_num = dragIndex + 1
                })
            })
            console.log('abc---', this.dragData)
        },
        // 拖拽区域筛选
        changeDrageSuite(val) {
            this.dragSuite = val
            this.dragData = []
            this.dragUpData = []
            getGroupPublishAppList({ iteration_id: this.pipeline_id, suite_code: val }).then(res => {
                if (res.data.status === 'success') {
                    res.data.data.forEach(item => {
                        this.dragData.push([])
                    })
                    res.data.data.forEach(item => {
                        const obj = { ...item }
                        obj.publish_mode = '2'
                        if (item.step_num === 0) {
                            this.dragUpData.push(obj)
                        } else {
                            this.dragData[item.step_num - 1].push(obj)
                        }
                    })
                }
            })
        },
        // 一键指定归档版本
        branchReset(index) {
            this.applyData[index].flowTableData = this.applyData[index].flowTableData.map(item => {
                item.script_branch = 'lastArchived'
                return item
            })
        },
        // 下拉回调方法
        selectApplyHandler(val, key, index) {
            const obj = {}
            // 获取当前应用的“业务分支”和“环境名称”
            this.publish_before_config[0].nodes[0].node_params[index].module_params.forEach(item => {
                if (item.param_key === 'suite_code' && item.param_value) {
                    obj.suite_code = item.param_value
                } else if (item.param_key === 'biz_iter_branch' && item.param_value) {
                    obj.biz_iter_branch = item.param_value
                }
            })
            if (key === 'biz_iter_branch' && obj.biz_iter_branch) {
                // 重置当前应用“环境名称”的数据
                this.applyData[index].fdevelopment_evn_options = []
                this.publish_before_config[0].nodes[0].node_params[index].module_params.forEach(item => {
                    if (item.param_key === 'suite_code') {
                        item.param_value = ''
                    }
                })
                // 重置当前应用“编排名称”的数据
                this.applyData[index].flow_list_options = []
                this.publish_before_config[0].nodes[0].node_params[index].module_params.forEach(item => {
                    if (item.param_key === 'biz_flow_name') {
                        item.param_value = ''
                    }
                })
                getTestSuitCode({ biz_test_iter_id: obj.biz_iter_branch }).then(res => {
                    if (res.data.status === 'success') {
                        this.applyData[index].fdevelopment_evn_options = res.data.data.map(item => {
                            return {
                                value: item,
                                name: item
                            }
                        })
                    }
                })
            } else if (key === 'suite_code' && obj.suite_code && obj.biz_iter_branch) {
                // 重置当前应用“编排名称”的数据
                this.applyData[index].flow_list_options = []
                this.publish_before_config[0].nodes[0].node_params[index].module_params.forEach(item => {
                    if (item.param_key === 'biz_flow_name') {
                        item.param_value = ''
                    }
                })
                // 查自动化编排下拉数据
                getFlowListNew({ suite_code: obj.suite_code, biz_test_iter_id: obj.biz_iter_branch }).then(res => {
                    if (res.data.status === 'success') {
                        this.applyData[index].flow_list_options = res.data.data.map(item => {
                            return {
                                value: item.biz_pipeline_name,
                                name: item.biz_pipeline_name
                            }
                        })
                    }
                })
            } else if (key === 'biz_flow_name') {
                // 查自动化编排-table数据
                getFlowTableList({
                    biz_pipeline_name: val,
                    iteration_id: this.pipeline_id
                }).then(res => {
                    if (res.data.status === 'success') {
                        this.applyData[index].flowTableData = res.data.data
                    }
                })
            }
        },
        // 设置展开/收起
        setShowFlow(index) {
            this.applyData[index].showFlow = !this.applyData[index].showFlow
        },
        // 获取配置
        getDefaultConfig() {
            // 查询模版
            getPublishConfig({ iteration_id: this.pipeline_id, plan_type: 'normal' }).then(res => {
                if (res.data.status === 'success') {
                    this.publish_plan_phases = res.data.data.publish_config
                    this.publish_before_config = res.data.data.publish_before_config

                    // 默认值逻辑
                    this.publish_plan_phases.forEach(phase => {
                        phase.nodes.forEach(node => {
                            node.node_params.forEach(param => {
                                if (param.param_key === 'iter_id') {
                                    param.param_value = this.pipeline_id
                                }
                                // 归档策略
                                if (param.param_key === 'archive_strategy') {
                                    this.optionsList.tactics_options = param.param_options
                                }
                            })
                        })
                    })
                    this.applyList = []
                    this.publish_before_config.forEach(item => {
                        item.nodes.forEach(node => {
                            node.node_params.forEach((node_param, nodeIndex) => {
                                // 1.获取应用列表
                                this.applyList.push(node_param.module_name)

                                // 2.默认带出业务分支的下拉数据
                                let iter_list_options = []
                                node_param.module_params.forEach(module_param => {
                                    if (module_param.param_key === 'biz_iter_branch') {
                                        iter_list_options = module_param.param_options.map(option => {
                                            return {
                                                name: option,
                                                value: option
                                            }
                                        })
                                        if (module_param.param_value) {
                                            // 2.5.对于有默认值的，如果业务分支有默认值，就查环境的下拉数据；环境一定没有默认值，所以编排及编排的table也不会有默认值
                                            // 业务分支有默认值时，需要查询环境名称的下拉数据
                                            getTestSuitCode({ biz_test_iter_id: module_param.param_value }).then(
                                                res => {
                                                    if (res.data.status === 'success') {
                                                        this.applyData[
                                                            nodeIndex
                                                        ].fdevelopment_evn_options = res.data.data.map(item => {
                                                            return {
                                                                value: item,
                                                                name: item
                                                            }
                                                        })
                                                    }
                                                }
                                            )
                                        }
                                    }
                                })
                                // 3.维护多应用的下拉数据和table数据
                                this.applyData.push({
                                    module_name: node_param.module_name,
                                    showFlow: true, // 展开/收起
                                    flowTableData: [], // table数据
                                    iter_list_options, // 业务分支 下拉数据
                                    fdevelopment_evn_options: [], // 环境名称 下拉数据
                                    flow_list_options: [] // 自动化编排 下拉数
                                })
                            })
                        })
                    })
                }
            })
        },
        initOptions() {
            // 审核人 - 邮件列表
            getEmailAddressesRequest().then(res => {
                if (res.data.status === 'success') {
                    this.optionsList.email_address_options = res.data.data.map(item => {
                        return {
                            name: item,
                            value: item
                        }
                    })
                }
            })
            // 选择提交SQL  列表数据
            getOnlineSqlApplyApi(this.pipeline_id).then(res => {
                if (res.data.status === 'success') {
                    this.publishData.sqlData = res.data.data
                }
            })
            // 查询迭代发布的环境列表
            getSuitByIterId({ iteration_id: this.pipeline_id }).then(res => {
                if (res.data.status === 'success') {
                    this.optionsList.suite_by_iter_id_options = res.data.data.map(item => {
                        return {
                            name: item.suite_code,
                            value: item.suite_code
                        }
                    })
                }
            })
        },
        // 数据初始化
        initData() {
            // 1. 查询默认配置
            this.getDefaultConfig()
            this.initOptions()
        }
    }
}
