/*
 * @Author: chao.bao <EMAIL>
 * @Date: 2024-09-13 10:41:04
 * @LastEditors: chao.bao <EMAIL>
 * @LastEditTime: 2024-11-19 15:41:07
 * @FilePath: /website_web/deploy_website/src/spider-components/spider-iter-publish-plan/mixins/common.js
 * @Description: rapid逻辑
 */
// import resData from '../components/rapid.json'
import {
    getCurrentIterAppPublishPlanLatestHistory,
    getBusinessBranch,
    getFlowList,
    getAppProdSuites,
    getEmailAddressesRequest,
    checkBeforeCreate,
    getEnvBindInfo
} from '@/spider-api/iter-plan'
import { getDataDevelopmentEnv } from '@/api/test-env'
export default {
    data() {
        return {
            submitStatus: {
                msg: '',
                disabled: true
            },
            optionsList: {
                iter_list_options: [], // 业务分支
                flow_list_options: [], // 自动化编排
                fdevelopment_evn_options: [], // 选择环境（自动化）
                email_address_options: [], // 选择环境（自动化）
                tactics_options: [], // 归档策略
                build_suite_code_options: []
            },
            enumType: {
                Input: 'text',
                Select: 'select',
                DatePicker: 'time_select',
                Dragger: 'draggable_order_list',
                Table: 'table'
            },
            loadding: false,
            // 表单数据
            publish_plan_phases: [],
            publishData: {
                iter_app_name: '' // 应用
            },
            labelWidth: 100
        }
    },
    props: {
        iter_app_options: {
            // 应用列表
            type: Array,
            default: () => []
        },
        plan_type: {
            type: String,
            default: ''
        },
        pipeline_id: {
            type: String,
            default: ''
        }
    },
    methods: {
        clearData() {
            this.publishData.iter_app_name = ''
            this.publish_plan_phases = []
        },
        goDetail(row) {
            window.open(row.test_set_url, '_blank')
        },
        selectHandler(val, key) {
            // suite_code
            // if (key === 'biz_iter_branch') {
            //     // 查自动化编排
            //     getFlowList({ biz_code: val.split('_')[0] }).then(res => {
            //         if (res.data.status === 'success') {
            //             this.optionsList.flow_list_options = res.data.data.map(item => {
            //                 return {
            //                     value: item.biz_pipeline_name,
            //                     name: item.biz_pipeline_name
            //                 }
            //             })
            //         }
            //     })
            // }
            const obj = {}
            this.publish_plan_phases[2].nodes[0].node_params.forEach(item => {
                if (item.param_key === 'suite_code' && item.param_value) {
                    obj.suite_code = item.param_value
                } else if (item.param_key === 'biz_iter_branch' && item.param_value) {
                    obj.biz_iter_branch = item.param_value
                }
            })
            if (['suite_code', 'biz_iter_branch'].includes(key) && obj.suite_code && obj.biz_iter_branch) {
                // 查自动化编排
                this.optionsList.flow_list_options = []
                this.publish_plan_phases[2].nodes[0].node_params.forEach(item => {
                    if (item.param_key === 'biz_flow_name') {
                        item.param_value = ''
                    }
                })
                getFlowList({ biz_code: obj.biz_iter_branch.split('_')[0], ...obj }).then(res => {
                    if (res.data.status === 'success') {
                        this.optionsList.flow_list_options = res.data.data.map(item => {
                            return {
                                value: item.biz_pipeline_name,
                                name: item.biz_pipeline_name
                            }
                        })
                    }
                })
            }
        },
        init(iter_app_name) {
            // 查询业务分支
            getBusinessBranch(iter_app_name).then(res => {
                if (res.data.status === 'success') {
                    this.optionsList.iter_list_options = res.data.data.map(item => {
                        return {
                            name: item.biz_test_iter_id,
                            value: item.biz_test_iter_id
                        }
                    })
                }
            })
            // 选择环境（自动化） 下拉数据
            getDataDevelopmentEnv({
                page: 1,
                size: 200,
                type_name: 'auto_testing'
            }).then(res => {
                if (res.data.status === 'success') {
                    this.optionsList.fdevelopment_evn_options = res.data.data.results.map(item => {
                        return {
                            value: item.suite_code,
                            name: item.suite_code
                        }
                    })
                }
            })
            // 选择环境 - build_suite_code
            getEnvBindInfo({
                pipeline_id: this.pipeline_id,
                module_name: iter_app_name
            }).then(res => {
                if (res.data.status === 'success') {
                    this.optionsList.build_suite_code_options = res.data.data.push_list.map(item => {
                        return {
                            value: item.env,
                            name: item.env
                        }
                    })
                }
            })
            // // 审核人 - 邮件列表
            getEmailAddressesRequest().then(res => {
                if (res.data.status === 'success') {
                    this.optionsList.email_address_options = res.data.data.map(item => {
                        return {
                            name: item,
                            value: item
                        }
                    })
                }
            })
        },
        getPublishPlanHistory(iter_app_name) {
            // mock
            // this.publish_plan_phases = resData.data
            // console.log('publish_plan_phases----', this.publish_plan_phases)
            // ==============
            this.loadding = true
            getCurrentIterAppPublishPlanLatestHistory({
                module_name: iter_app_name,
                plan_type: this.plan_type,
                iteration_id: this.pipeline_id
            })
                .then(res => {
                    // 获取应用信息
                    if (res.data.status === 'success') {
                        this.publish_plan_phases = res.data.data
                        this.publish_plan_phases.forEach(phase => {
                            phase.nodes.forEach(node => {
                                node.node_params.forEach(param => {
                                    if (param.param_key === 'module_name') {
                                        param.param_value = iter_app_name
                                    }
                                    if (param.param_key === 'iter_id') {
                                        param.param_value = this.pipeline_id
                                    }
                                    // 归档策略
                                    if (param.param_key === 'archive_strategy') {
                                        this.optionsList.tactics_options = param.param_options
                                    }
                                })
                            })
                        })

                        this.publish_plan_phases.forEach(phase => {
                            phase.nodes.forEach(node => {
                                node.node_params.forEach(param => {
                                    if (
                                        param.param_key === 'biz_iter_branch' &&
                                        param.param_show_flag &&
                                        param.param_value
                                    ) {
                                        // 查自动化编排
                                        getFlowList({ biz_code: param.param_value.split('_')[0] }).then(res => {
                                            if (res.data.status === 'success') {
                                                this.optionsList.flow_list_options = res.data.data.map(item => {
                                                    return {
                                                        value: item.biz_pipeline_name,
                                                        name: item.biz_pipeline_name
                                                    }
                                                })
                                            }
                                        })
                                    }
                                })
                            })
                        })

                        // 查询拖拽数据
                        getAppProdSuites({ module_name: iter_app_name }).then(res => {
                            if (res.data.status === 'success') {
                                const app_prod_suites = res.data.data.map(item => {
                                    if (item.node_ip_list) {
                                        return {
                                            env: item.suite_code,
                                            name: {
                                                suite_name: item.suite_name,
                                                node_list: item.node_ip_list
                                            }
                                        }
                                    }
                                })
                                this.publish_plan_phases.forEach(phase => {
                                    phase.nodes.forEach(node => {
                                        node.node_params.forEach(param => {
                                            if (
                                                param.param_show_type === 'draggable_order_list' &&
                                                param.param_show_flag &&
                                                param.draggable_order_list_type === 'prod-suites'
                                            ) {
                                                param.param_value = app_prod_suites
                                            }
                                        })
                                    })
                                })
                            }
                        })
                    } else {
                        this.$Notice.error({
                            title: 'Error',
                            desc: res.data.message,
                            duration: 0
                        })
                    }
                })
                .catch(err => {
                    this.$Notice.error({
                        title: 'Error',
                        desc: err.message,
                        duration: 0
                    })
                })
                .finally(() => {
                    this.loadding = false
                })
        },
        change_iter_app_name(iter_app_name) {
            this.publishData.iter_app_name = iter_app_name
            if (!iter_app_name) {
                this.clearData()
                return
            }
            // // 前置校验
            checkBeforeCreate({
                plan_type: this.plan_type,
                iteration_id: this.pipeline_id,
                app_name: iter_app_name
            }).then(res => {
                if (res.data.status === 'failed') {
                    this.normalLoading = false
                    this.submitStatus = {
                        msg: res.data.msg,
                        disabled: true
                    }
                } else {
                    this.submitStatus = {
                        msg: '',
                        disabled: false
                    }
                }
                this.$emit('btnStatus', this.submitStatus)
            })
            // 查询下拉列表数据
            this.init(iter_app_name)
            //   查询配置
            this.getPublishPlanHistory(iter_app_name)
        },
        // 校验
        validate() {
            let can_submit = true
            let block_phase = {}
            this.publish_plan_phases.map((phase, index) => {
                phase.nodes.map(node => {
                    node.node_params.map(param => {
                        if (
                            phase.required &&
                            param.param_show_flag &&
                            ![this.enumType.Table, this.enumType.Dragger].includes(param.param_show_type) &&
                            !param.param_value
                        ) {
                            can_submit = false
                            block_phase = phase
                        }
                    })
                })
            })

            if (!this.publishData.iter_app_name) {
                this.$Message.warning({
                    content: `基础 阶段参数配置不完整！！！`,
                    duration: 2
                })
                return false
            } else if (!can_submit) {
                this.$Message.warning({
                    content: `${block_phase.phase_name} 阶段参数配置不完整！！！`,
                    duration: 2
                })
                return false
            }
            return can_submit
        },
        selectTableHandler(selectArr) {
            const strArr = selectArr.map(item => item.sql_ver_name)
            // 将数据设置到publish_plan_phases中 - 列表数据放到param_value中还是选中的值放到param_value中???还是把选中项放到param_value中
            this.publish_plan_phases.forEach(phase => {
                phase.nodes.forEach(node => {
                    node.node_params.forEach(param => {
                        if (param.param_key === 'commit_sql') {
                            param.param_value = strArr
                        }
                    })
                })
            })
        },
        showSnapshot(time) {
            const now = new Date(time)
            const year = now.getFullYear()
            const month = String(now.getMonth() + 1).padStart(2, '0') // 月份从 0 开始，所以要加 1，并确保两位数显示
            const day = String(now.getDate()).padStart(2, '0')
            const hours = String(now.getHours()).padStart(2, '0')
            const minutes = String(now.getMinutes()).padStart(2, '0')
            const seconds = String(now.getSeconds()).padStart(2, '0')

            const res = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
            return res
        },
        getPublishData() {
            // 发布计划数据
            // // 将“报告阈值”添加到“自动化”模块中 - 默认值顺序固定
            const phases = this.publish_plan_phases
            const obj = phases[4].nodes[0].node_params.filter(item => item.param_key === 'threshold')[0]
            const thresholdVal = obj.param_value
            phases[2].nodes[0].node_params.forEach(item => {
                if (item.param_key === 'threshold') {
                    item.param_value = thresholdVal
                }
            })
            phases[3].nodes[0].node_params.forEach(item => {
                if (item.param_key === 'schedule_time' && item.param_value) {
                    item.param_value = this.showSnapshot(new Date(item.param_value).getTime())
                }
            })

            const data = {
                iter_id: this.pipeline_id,
                plan_type: this.plan_type,
                module_name: this.publishData.iter_app_name, // 应用名
                branch_name: this.pipeline_id.split('_')[1],
                phases
            }
            return data
        }
    }
}
