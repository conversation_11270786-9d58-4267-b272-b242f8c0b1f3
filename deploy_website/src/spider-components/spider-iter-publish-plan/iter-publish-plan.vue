<template>
    <div>
        <Row>
            <Col span="18">
                <Card>
                    <CustomTable :tableData="test_data" :pipeline_id="pipeline_id" />
                </Card>
            </Col>
            <Col span="6">
                <Card>
                    <Table border :columns="person_columns" :data="person_data"></Table>
                </Card>
            </Col>
        </Row>
        <Card style="height: auto; min-height: 75%; overflow-y: hidden">
            <Row style="margin-top: 1em">
                <div
                    style="
                        display: flex;
                        justify-content: flex-start;
                        align-items: center;
                        font-weight: bold;
                        font-size: 16px;
                    "
                >
                    迭代版本: {{ this.pipeline_id }}
                </div>
            </Row>
            <div class="filter_param">
                <div class="filter_param_item">
                    <div class="filter_param_item_name">发布应用:</div>
                    <Select filterable clearable size="small" v-model="filter.module_name" style="width: 200px">
                        <Option v-for="option in iter_app_options" :value="option.value">{{ option.name }}</Option>
                    </Select>
                </div>
                <div class="filter_param_item">
                    <div class="filter_param_item_name">计划状态:</div>
                    <Select filterable clearable size="small" v-model="filter.plan_status" style="width: 200px">
                        <Option v-for="option in plan_status_options" :value="option.value">{{ option.name }}</Option>
                    </Select>
                </div>

                <div class="filter_param_item">
                    <Button type="primary" size="small" @click="search_publish_plan()" icon="ios-search">搜索</Button>
                </div>

                <!-- <div class="filter_param_item">
                    <Button type="warning" size="small" ghost @click="show_publish_plan_modal('rapid')">
                        发起【Rapid】发布计划
                    </Button>
                </div> -->
                <!-- 暂时隐藏 -->
                <!-- <div class="filter_param_item">
                    <Button type="warning" size="small" ghost @click="show_publish_plan_modal('rapid_new')">
                        发起【Rapid】发布计划-新
                    </Button>
                </div>
                <div class="filter_param_item">
                    <Button type="primary" size="small" ghost @click="show_publish_plan_modal('express')">
                        发起【全流程】发布计划
                    </Button>
                </div> -->
                <div class="filter_param_item">
                    <Button
                        type="primary"
                        size="small"
                        ghost
                        :disabled="!canShowNormalModel"
                        @click="show_publish_plan_modal('normal')"
                    >
                        Normal【人工测试】
                    </Button>
                </div>
            </div>
            <Table
                title="交付计划列表"
                style="margin-top: 1em"
                border
                width="100%"
                :columns="publish_plan_columns"
                :data="publish_plan_data"
            ></Table>
            <div style="margin: 6px 2px 2px 2px; overflow: hidden">
                <div style="float: right" class="pagination_class">
                    <Page
                        size="small"
                        :total="total_num"
                        :current="current_page"
                        @on-change="changePage"
                        show-total
                    ></Page>
                </div>
            </div>
            <Modal
                :title="detail_title"
                v-model="publish_plan_detail_show"
                fullscreen
                :closable="true"
                :width="publishPlanModalWidth"
                :mask-closable="false"
                :footer-hide="true"
                @on-cancel="close_publish_plan_detail"
                class="publish_plan_modal_class"
            >
                <!-- <IterPublishPlanDetail ref="publish_plan_detail" :pipeline_id="pipeline_id"></IterPublishPlanDetail> -->
                <IterPublishPlanPipeline ref="publish_plan_detail" :pipeline_id="pipeline_id"></IterPublishPlanPipeline>
                <div slot="footer"></div>
            </Modal>

            <Modal
                fullscreen
                v-model="create_publish_plan_show"
                :closable="true"
                :mask-closable="false"
                class="publish_plan_create_modal_class"
                @on-cancel="close_show_publish_plan"
                style="overflow: hidden"
            >
                <p slot="header" style="color: #f60">
                    <Icon type="ios-information-circle"></Icon>
                    <span>{{ create_publish_plan_modal_title }}</span>
                </p>
                <IterPublishPlanNode
                    v-if="publish_plan_basic_phases.length > 0"
                    ref="iterPublishPlanNode"
                    @getPublishPlanHistory="getPublishPlanHistory"
                    @create_publish_plan_modal="create_publish_plan_modal"
                    @clear_publish_plan_phases="clear_publish_plan_phases"
                    @clearPublishPlanForm="clearPublishPlanForm"
                    @search_publish_plan="search_publish_plan"
                    :pipeline_id="pipeline_id"
                    :plan_type="publish_plan_type"
                    :env_options="env_options"
                    :iter_app_options="iter_app_options"
                    :user_options="user_options"
                    :biz_iter_branch_options="biz_iter_branch_options"
                    :publish_plan_basic_phases="publish_plan_basic_phases"
                ></IterPublishPlanNode>
                <div slot="footer">
                    <!--        <Button  type="primary"-->
                    <!--                 style="float: left;" ghost  @click="add_publish_app">新增应用-->
                    <!--        </Button>-->
                    <Button type="warning" @click="close_show_publish_plan">取消</Button>
                    <Button type="primary" @click="submit_publish_plan">发起</Button>
                </div>
            </Modal>
            <!-- Rapid-new -->
            <Modal
                v-if="false"
                fullscreen
                v-model="create_publish_rapid_plan_show"
                :closable="true"
                :mask-closable="false"
                class="publish_plan_create_modal_class"
                style="overflow: hidden"
                @on-cancel="closeModal(enumPlanType.Rapid)"
            >
                <p slot="header" style="color: #f60">
                    <Icon type="ios-information-circle"></Icon>
                    <span>{{ create_publish_plan_modal_title }}</span>
                </p>
                <iterPublishRapid
                    v-if="false"
                    ref="rapidPlanRef"
                    :iter_app_options="iter_app_options"
                    :plan_type="enumPlanType.Rapid"
                    :pipeline_id="pipeline_id"
                    @btnStatus="obj => commonBtnStatus(obj, enumPlanType.Rapid)"
                />
                <div slot="footer">
                    <Button type="warning" @click="closeModal(enumPlanType.Rapid)">取消</Button>
                    <Button
                        type="primary"
                        :disabled="rapidBtnStauts"
                        :loading="rapidLoading"
                        @click="submitCommon(enumPlanType.Rapid)"
                        >发起</Button
                    >
                </div>
            </Modal>
            <!-- 发布[全流程]发布计划 -->
            <Modal
                v-if="false"
                fullscreen
                v-model="create_publish_express_plan_show"
                :closable="true"
                :mask-closable="false"
                class="publish_plan_create_modal_class"
                style="overflow: hidden"
                @on-cancel="closeModal(enumPlanType.Express)"
            >
                <p slot="header" style="color: #f60">
                    <Icon type="ios-information-circle"></Icon>
                    <span>发布[全流程]发布计划</span>
                </p>
                <iterPublishAllPlanNode
                    v-if="false"
                    ref="expressPlanRef"
                    :iter_app_options="iter_app_options"
                    :env_options="env_options"
                    :user_options="user_options"
                    plan_type="express"
                    :pipeline_id="pipeline_id"
                    @btnStatus="obj => commonBtnStatus(obj, enumPlanType.Express)"
                />
                <div slot="footer">
                    <Button type="warning" @click="closeModal(enumPlanType.Express)">取消</Button>
                    <Button type="primary" :loading="expressLoading" @click="submitCommon(enumPlanType.Express)"
                        >发起</Button
                    >
                </div>
            </Modal>
            <!-- Normal -->
            <Modal
                fullscreen
                v-model="create_publish_normal_plan_show"
                :closable="true"
                :mask-closable="false"
                class="publish_plan_create_modal_class"
                style="overflow: hidden"
                @on-cancel="closeModal(enumPlanType.Normal)"
            >
                <p slot="header" style="color: #f60">
                    <Icon type="ios-information-circle"></Icon>
                    <span>Normal【人工测试】</span>
                </p>
                <iterPublishNormalPlanNode
                    ref="normalPlanRef"
                    :iter_app_options="iter_app_options"
                    :env_options="env_options"
                    :user_options="user_options"
                    plan_type="normal"
                    :pipeline_id="pipeline_id"
                />
                <div slot="footer">
                    <Button type="warning" @click="closeModal(enumPlanType.Normal)">取消</Button>
                    <Button type="primary" :loading="normalLoading" @click="submitCommon(enumPlanType.Normal)"
                        >发起</Button
                    >
                </div>
            </Modal>
            <Modal v-model="publishErr" width="600">
                <p slot="header">发起失败</p>
                <Table :height="400" :columns="publishErrColumns" :data="publishErrData"></Table>
                <div slot="footer">
                    <Button type="primary" @click="publishErr = false">确定</Button>
                </div>
            </Modal>
        </Card>
    </div>
</template>

<script>
import store from '@/spider-store'
import IterPublishPlanDetail from '@/spider-components/spider-iter-publish-plan/iter-publish-plan-detail.vue'
import IterPublishPlanPipeline from '@/spider-components/spider-iter-publish-plan/iter-publish-plan-pipeline.vue'
import IterPublishPlanNode from '@/spider-components/spider-iter-publish-plan/iter-publish-plan-node.vue'
import iterPublishAllPlanNode from './components/iterPublishAllPlanNode'
import iterPublishNormalPlanNode from './components/iterPublishNormalPlanNode'
import iterPublishRapid from './components/iterPublishRapid'
import { getTestSuiteCode } from '@/api/data'
import {
    createIterPublishPlan,
    getCurrentIterAppPublishPlanLatestHistory,
    getEmailAddresses,
    getIterPublishPlanByPage,
    stopIterPublishPlan,
    submitPublishPlan,
    getNormalBtnStatus,
    getAutoTestReport,
    get_mantis_request
} from '@/spider-api/iter-plan'
import { getIterGitRopesApi } from '@/spider-api/get-iter-info'
import { get_test_iter_list } from '@/spider-api/biz-mgt'
import CustomTable from './components/CustomTable.vue'

function deepCopy(obj) {
    if (typeof obj !== 'object' || obj === null) {
        // 如果 obj 不是对象或为 null，则直接返回
        return obj
    }

    const copy = Array.isArray(obj) ? [] : {}

    for (let key in obj) {
        if (Object.prototype.hasOwnProperty.call(obj, key)) {
            copy[key] = deepCopy(obj[key])
        }
    }

    return copy
}

export default {
    name: 'IterPublishPlan',
    components: {
        IterPublishPlanNode,
        IterPublishPlanDetail,
        IterPublishPlanPipeline,
        iterPublishAllPlanNode,
        iterPublishNormalPlanNode,
        iterPublishRapid,
        CustomTable
    },
    data() {
        return {
            enumPlanType: {
                Rapid: 'rapid',
                Normal: 'normal',
                Express: 'express'
            },
            create_publish_plan_modal_title: '',
            curr_batch_no: '',
            detail_title: '',
            filter: { plan_status: undefined, module_name: undefined },
            total_num: 0,
            current_page: 1,
            page_size: 10,
            publish_plan_type: 'rapid',
            env_options: [],
            user_options: [],
            biz_iter_branch_options: [],
            iter_app_options: [],
            plan_status_options: [
                { name: 'RUNNING', value: 'RUNNING' },
                { name: 'SUCCESS', value: 'SUCCESS' },
                {
                    name: 'FAIL',
                    value: 'FAIL'
                },
                { name: 'STOP', value: 'STOP' }
            ],
            publish_plan_basic_phases_init: [
                {
                    phase_name: '基础',
                    phase_type: 'base',
                    nodes: [
                        {
                            node_name: '选应用',
                            node_params: [
                                {
                                    param_key: 'iter_id',
                                    param_value: '',
                                    param_cn_name: '迭代',
                                    param_show_type: 'text',
                                    param_show_flag: true,
                                    param_update_disable: true
                                },
                                {
                                    param_key: 'module_name',
                                    param_value: null,
                                    param_show_flag: true,
                                    param_cn_name: '应用名',
                                    param_show_type: 'select',
                                    param_options_type: 'iter_app'
                                }
                            ]
                        }
                    ]
                }
            ],
            publish_plan_basic_phases: [],
            publish_plan_phases: [],
            publishPlanModalWidth: 900,
            publish_plan_types: [],
            curr_publish_plan_id: 1,
            pipeline_id: '',
            publish_plan_columns: [
                {
                    title: '批次',
                    key: 'batch_no'
                },
                {
                    title: '发起时间',
                    key: 'create_time'
                },
                {
                    title: '发起人',
                    key: 'create_user',
                    align: 'center'
                },
                {
                    title: '发布应用',
                    key: 'module_name',
                    align: 'center'
                },

                {
                    title: '发布时间',
                    key: 'publish_time',
                    align: 'center'
                },
                {
                    title: '执行状态',
                    align: 'center',
                    render: (h, params) => {
                        let color = ''
                        if (params.row.plan_status === 'SUCCESS') {
                            color = 'green'
                        } else if (params.row.plan_status === 'FAIL' || params.row.plan_status === 'STOP') {
                            color = 'red'
                        } else if (params.row.plan_status === 'RUNNING') {
                            color = '#ff9900'
                        }
                        return h(
                            'div',
                            {
                                style: {
                                    color: color
                                },
                                domProps: {
                                    innerHTML: params.row.plan_status
                                }
                            },
                            ''
                        )
                    }
                },
                {
                    title: '结束时间',
                    key: 'end_time',
                    align: 'center'
                },
                {
                    title: '操作',
                    align: 'center',
                    render: (h, params) => {
                        return h('div', [
                            h(
                                'Button',
                                {
                                    props: {
                                        size: 'small',
                                        type: 'success'
                                    },
                                    style: {
                                        marginTop: '0.5em',
                                        marginRight: '0.5em'
                                    },
                                    on: {
                                        click: () => {
                                            this.publish_plan_detail_show = true
                                            this.detail_title = '发布计划详情-' + '批次【' + params.row.batch_no + '】'
                                            this.$refs.publish_plan_detail.initThisVue(params.row.batch_no)
                                        }
                                    }
                                },
                                '详情'
                            ),
                            h(
                                'Button',
                                {
                                    props: {
                                        size: 'small',
                                        type: 'error'
                                    },
                                    style: {
                                        marginTop: '0.5em',
                                        display: params.row.plan_status === 'RUNNING' ? '' : 'None'
                                    },
                                    on: {
                                        click: () => {
                                            this.$Modal.confirm({
                                                title: '提示',
                                                content: '<p>确认终止此发布计划【' + params.row.batch_no + '】？</p>',
                                                onOk: () => {
                                                    this.stop_publish_plan(params.row)
                                                },
                                                onCancel: () => {}
                                            })
                                        }
                                    }
                                },
                                '终止'
                            )
                        ])
                    }
                }
            ],
            publish_plan_data: [],
            create_publish_plan_show: false,
            publish_plan_detail_show: false,
            // rapid
            create_publish_rapid_plan_show: false,
            rapidLoading: false,
            rapidBtnStauts: true,
            // normal
            canShowNormalModel: false,
            create_publish_normal_plan_show: false,
            normalLoading: false,
            // 发布[全流程]发布计划
            create_publish_express_plan_show: false,
            expressLoading: false,
            expressBtnStauts: true,
            publishErr: false,
            publishErrColumns: [
                {
                    title: '应用/迭代',
                    key: 'biz_key'
                },
                {
                    title: '失败原因',
                    key: 'msg'
                }
            ],
            publishErrData: [],
            test_data: [],
            person_columns: [
                {
                    title: '人工测试报告',
                    render: (h, params) => {
                        return h('div', [
                            h(
                                'a',
                                {
                                    style: {
                                        marginRight: '5px'
                                    },
                                    on: {
                                        click: () => {
                                            // 处理业务编排点击事件
                                            window.open(params.row.report_url, '_blank')
                                        }
                                    }
                                },
                                '查看报告'
                            )
                        ])
                    }
                },
                {
                    title: '状态',
                    key: 'report_status'
                }
            ],
            person_data: []
        }
    },
    methods: {
        // 关闭弹窗 - 清空发布弹框数据
        closeModal(planType) {
            if (planType === this.enumPlanType.Normal) {
                this.create_publish_normal_plan_show = false
                this.$refs.normalPlanRef.clearData()
            } else if (planType === this.enumPlanType.Rapid) {
                this.create_publish_rapid_plan_show = false
                this.$refs.rapidPlanRef.clearData()
            } else if (planType === this.enumPlanType.Express) {
                this.create_publish_express_plan_show = false
                this.$refs.expressPlanRef.clearData()
            }
        },
        // 应用校验 - 发布按钮是否可点击
        commonBtnStatus({ disabled }, planType) {
            if (planType === this.enumPlanType.Normal) {
                // this.normalBtnStauts = disabled
            } else if (planType === this.enumPlanType.Rapid) {
                this.rapidBtnStauts = disabled
            } else if (planType === this.enumPlanType.Express) {
                this.expressBtnStauts = disabled
            }
        },
        // 点击发布按钮
        submitCommon(planType) {
            if (planType === this.enumPlanType.Normal) {
                const result = this.$refs.normalPlanRef.validate()
                this.normalLoading = true
                if (!result) {
                    this.normalLoading = false
                } else {
                    const data = this.$refs.normalPlanRef.getPublishData()
                    // 调用发布接口
                    submitPublishPlan(data)
                        .then(res => {
                            if (res.data.status === 'success') {
                                this.create_publish_normal_plan_show = false
                                this.$Message.success(res.data.msg)
                                this.search_publish_plan()
                            } else {
                                // this.$Message.error(res.data.msg)
                                this.publishErr = true
                                this.publishErrData = res.data.data
                            }
                        })
                        .finally(() => {
                            this.normalLoading = false
                        })
                }
            } else if (planType === this.enumPlanType.Rapid) {
                this.rapidLoading = true
                const data = this.$refs.rapidPlanRef.getPublishData()
                const result = this.$refs.rapidPlanRef.validate()
                if (!result) {
                    this.rapidLoading = false
                } else {
                    // 调用发布接口
                    submitPublishPlan(data)
                        .then(res => {
                            if (res.data.status === 'success') {
                                this.create_publish_rapid_plan_show = false
                                this.$Message.success(res.data.msg)
                                this.search_publish_plan()
                            } else {
                                this.$Message.error(res.data.msg)
                            }
                        })
                        .finally(() => {
                            this.rapidLoading = false
                        })
                }
            } else if (planType === this.enumPlanType.Express) {
                this.expressLoading = true
                const data = this.$refs.expressPlanRef.getPublishData()
                const result = this.$refs.expressPlanRef.validate()
                if (!result) {
                    this.expressLoading = false
                } else {
                    // 调用发布接口
                    submitPublishPlan(data)
                        .then(res => {
                            if (res.data.status === 'success') {
                                this.create_publish_express_plan_show = false
                                this.$Message.success(res.data.msg)
                                this.search_publish_plan()
                            } else {
                                this.$Message.error(res.data.msg)
                            }
                        })
                        .finally(() => {
                            this.expressLoading = false
                        })
                }
            }
        },
        clear_publish_plan_phases() {
            this.publish_plan_phases = []
        },
        create_publish_plan_modal() {
            this.create_publish_plan_show = false
        },
        close_publish_plan_detail() {
            this.$refs.publish_plan_detail.cancel_interval()
            this.publish_plan_detail_show = false
        },
        show_publish_plan_modal(publish_plan_type) {
            if (publish_plan_type === 'rapid') {
                this.create_publish_plan_modal_title = `迭代: ${this.pipeline_id} 【${publish_plan_type}】发布计划`
                this.publish_plan_types = [
                    {
                        publish_plan_type: publish_plan_type,
                        publish_plan_type_desc: '快速发布',
                        status: 'open'
                    }
                ]

                this.$refs.iterPublishPlanNode.initThisVue('initThisVue')
                this.create_publish_plan_show = true
            } else if (publish_plan_type === 'express') {
                // this.$Message.warning({ content: '等待开放', duration: 2 })
                this.create_publish_express_plan_show = true
            } else if (publish_plan_type === 'normal') {
                this.create_publish_normal_plan_show = true
                this.$refs.normalPlanRef.initData()
            } else if (publish_plan_type === 'rapid_new') {
                this.create_publish_plan_modal_title = `迭代: ${this.pipeline_id} 【${publish_plan_type}】发布计划`
                this.create_publish_rapid_plan_show = true
            }
        },
        add_publish_app() {
            this.$refs.iterPublishPlanNode.add_publish_app()
        },
        changePlanTab(value) {
            this.publish_plan_type = value
        },
        submit_publish_plan() {
            this.$refs.iterPublishPlanNode.submit_publish_plan()
        },
        clearPublishPlanForm() {
            this.publish_plan_phases = []
            this.create_publish_plan_show = false
            this.publish_plan_basic_phases = deepCopy(this.publish_plan_basic_phases_init)
        },
        close_show_publish_plan() {
            this.publish_plan_basic_phases = []
            this.create_publish_plan_show = false
            this.clearPublishPlanForm()
            this.$refs.iterPublishPlanNode.clear()
        },
        initThisVue() {
            console.log('initThisVue-----')

            this.pipeline_id = store.state.iterationID
            if (!this.pipeline_id) {
                return false
            }
            this.publishPlanModalWidth = window.innerWidth * 0.9
            this.clearPublishPlanForm()
            this.search_publish_plan()
            getTestSuiteCode().then(res => {
                if (res.data.status === 'success') {
                    let env_options = []
                    res.data.data.forEach(item => {
                        env_options.push({
                            name: item,
                            value: item
                        })
                    })
                    console.log('env_options:%o', env_options)
                    this.env_options = env_options
                    // this.$refs.iterPublishPlanNode.env_options= env_options
                }
            })
            getEmailAddresses().then(res => {
                if (res.data.status === 'success') {
                    let user_options = []
                    console.log('user_options:%o', res.data.data)
                    res.data.data.forEach(item => {
                        user_options.push({
                            name: item,
                            value: item
                        })
                    })
                    this.user_options = user_options
                    // this.$refs.iterPublishPlanNode.env_options= user_options
                }
            })
            get_test_iter_list().then(res => {
                if (res.data.status === 'success') {
                    let biz_iter_branch_options = []
                    res.data.data.forEach(item => {
                        // biz_iter_branch_options.push({
                        //   'name': item.biz_test_iter_br,
                        //   'value': item.biz_test_iter_br
                        // })
                        biz_iter_branch_options.push({
                            name: item.biz_test_iter_id,
                            value: item.biz_test_iter_id
                        })
                    })
                    this.biz_iter_branch_options = biz_iter_branch_options
                }
            })

            getIterGitRopesApi({ iterationID: this.pipeline_id }).then(res => {
                // 获取应用信息
                if (res.data.status === 'success') {
                    let iter_app_options = []
                    res.data.data['git_repo_list'].forEach(repo_item => {
                        res.data.data['appname_list'].forEach(app_item => {
                            if (app_item.appName === repo_item.app_name && app_item.need_ops === 1) {
                                iter_app_options.push({
                                    name: app_item.appName,
                                    value: app_item.appName
                                })
                            }
                        })
                    })
                    this.iter_app_options = iter_app_options
                }
            })

            // 查询按钮状态
            getNormalBtnStatus({ iteration_id: this.pipeline_id }).then(res => {
                if (res.data.status === 'success') {
                    this.canShowNormalModel = true
                } else {
                    this.canShowNormalModel = false
                }
            })
        },
        initRequest() {
            // 查询迭代的自动化信息
            getAutoTestReport({ iteration_id: this.pipeline_id }).then(res => {
                if (res.data.code === '0000') {
                    this.test_data = res.data.data
                }
            })
            get_mantis_request({
                business_name: 'get_iteration_quality_report',
                params: { iteration_id: this.pipeline_id }
            }).then(res => {
                if (res.data.status === 'success') {
                    this.person_data = res.data.data
                }
            })
        },
        search_publish_plan(data) {
            console.log('data:%o', data)
            if (!data) {
                data = {
                    iter_id: this.pipeline_id,
                    page_size: this.page_size,
                    page: 1
                }
            }
            if (this.filter.plan_status) {
                data.plan_status = this.filter.plan_status
            }
            if (this.filter.module_name) {
                data.module_name = this.filter.module_name
            }
            getIterPublishPlanByPage(data)
                .then(res => {
                    if (res.data.status === 'success') {
                        this.publish_plan_data = res.data.data.publish_plans
                        this.total_num = res.data.data.total
                    } else {
                        this.$Notice.error({
                            title: 'Error',
                            desc: res.data.msg,
                            duration: 0
                        })
                    }
                })
                .catch(err => {
                    this.$Notice.error({
                        title: 'Error',
                        desc: err.message,
                        duration: 0
                    })
                })
        },
        changePage(index) {
            this.current_page = index
            this.search_publish_plan({
                iter_id: this.pipeline_id,
                page_size: this.page_size,
                page: index
            })
        },
        stop_publish_plan(publish_plan) {
            stopIterPublishPlan(publish_plan)
                .then(res => {
                    if (res.data.status === 'success') {
                        this.$Message.success({
                            content: '发布计划终止成功',
                            duration: 2
                        })
                        this.search_publish_plan()
                    } else {
                        this.$Notice.error({
                            title: 'Error',
                            desc: res.data.msg,
                            duration: 0
                        })
                    }
                })
                .catch(err => {
                    this.$Notice.error({
                        title: 'Error',
                        desc: err.message,
                        duration: 0
                    })
                })
        },
        getPublishPlanHistory(iter_app_name) {
            getCurrentIterAppPublishPlanLatestHistory({
                app_name: iter_app_name,
                plan_type: this.publish_plan_type
            })
                .then(res => {
                    // 获取应用信息
                    if (res.data.status === 'success') {
                        this.publish_plan_phases = res.data.data
                        this.publish_plan_phases.forEach(phase => {
                            phase.nodes.forEach(node => {
                                node.node_params.forEach(param => {
                                    if (param.param_key === 'module_name') {
                                        param.param_value = iter_app_name
                                    }
                                    if (param.param_key === 'iter_id') {
                                        param.param_value = this.pipeline_id
                                    }
                                })
                            })
                        })
                    } else {
                        this.$Notice.error({
                            title: 'Error',
                            desc: res.data.message,
                            duration: 0
                        })
                    }
                })
                .catch(err => {
                    this.$Notice.error({
                        title: 'Error',
                        desc: err.message,
                        duration: 0
                    })
                })
                .finally(() => {
                    // this.publish_plan_phases = [{
                    //   phase_name: '验证',
                    //   nodes: [{
                    //     node_name: '构建/部署',
                    //     node_params: [{
                    //       "param_key": "suite_code",
                    //       "param_value": null,
                    //       "param_cn_name": "验证环境",
                    //       "param_show_type": "select",
                    //       "param_show_flag": true,
                    //       "param_options_type": "env"
                    //     }]
                    //   }]
                    // },
                    //   {
                    //     phase_name: '产线申请',
                    //     nodes: [{
                    //       node_name: '产线申请',
                    //       node_params: [{
                    //         "param_key": "iter_id",
                    //         "param_value": "",
                    //         "param_cn_name": "迭代",
                    //         "param_show_type": "text",
                    //         "param_show_flag": true,
                    //         "param_update_disable": true
                    //       }, {
                    //         "param_key": "module_name",
                    //         "param_value": "",
                    //         "param_cn_name": "应用名",
                    //         "param_show_type": "text",
                    //         "param_show_flag": true,
                    //         "param_update_disable": true
                    //       }]
                    //     }, {
                    //       node_name: '邮件确认',
                    //       node_params: [{
                    //         "param_key": "approver",
                    //         "param_value": null,
                    //         "param_cn_name": "审批人",
                    //         "param_options_type": "user",
                    //         "param_show_type": "select",
                    //         "param_show_flag": true
                    //       }]
                    //     }]
                    //   },
                    //   {
                    //     phase_name: '上线',
                    //     nodes: [{
                    //       node_name: '发布',
                    //       node_params: [{
                    //         "param_key": "publish_time",
                    //         "param_value": null,
                    //         "param_cn_name": "发布时间",
                    //         "param_show_type": "time_select",
                    //         "param_show_flag": true,
                    //       }, {
                    //         "param_key": "publish_envs",
                    //         "param_value": [{
                    //           "name": "唐镇-灾备",
                    //           "env": "tz-zb"
                    //         }, {
                    //           "name": "唐镇-生产",
                    //           "env": "tz-prod"
                    //         }, {
                    //           "name": "外高桥",
                    //           "env": "prod"
                    //         }],
                    //         "param_cn_name": "环境发布",
                    //         "param_show_type": "draggable_order_list",
                    //         "param_show_flag": true,
                    //       }]
                    //     }]
                    //   },
                    //   {
                    //     phase_name: '上线后',
                    //     nodes: [{
                    //       node_name: '归档',
                    //       node_params: [{
                    //         "param_key": "archive_strategy",
                    //         "param_value": null,
                    //         "param_cn_name": "归档策略",
                    //         "param_show_type": "select",
                    //         "param_show_flag": true,
                    //         "param_options": [{
                    //           "name": "六小时后",
                    //           "value": "六小时后"
                    //         }, {
                    //           "name": "第二个工作日开始",
                    //           "value": "第二个工作日开始"
                    //         }]
                    //       }]
                    //     }]
                    //   }]
                })
        }
    },
    mounted() {
        console.log('mounted')

        this.initThisVue()
    },
    destroyed() {}
}
</script>

<style>
.publish_plan_modal_class .ivu-modal-body {
    padding: 0 !important;
}

.publish_plan_tab_class {
    /*padding: 8px;*/
}

.ivu-modal-body {
    padding: 0 16px 0 16px;
}

.filter_param {
    display: flex;
    flex-direction: row;
}

.filter_param_item {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-top: 10px;
    margin-right: 10px;
}

.filter_param_item_name {
    margin-right: 4px;
}

.ivu-modal-header p,
.ivu-modal-header-inner {
    font-size: 16px;
}

.ivu-modal-close .ivu-icon-ios-close {
    color: #000 !important;
    font-weight: bold;
}
</style>
<style lang="less" scoped>
.custom_card {
    display: flex;
    margin-bottom: 10px;
    .btn_test {
        flex: 1 0 300px;
        margin-left: 10px;
    }
}
.custom_tables {
    /deep/ .cutom_culumn {
        .ivu-table-cell {
            padding: 0;
        }
    }
}
</style>
