<template>
    <div ref="publishPlanDetail" style="
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    height: 100%;
    width: 100%;
">
        <vue-pipeline ref="iterPublishPlanPipelineSelf" :x="parseInt(form.x)" :y="parseInt(form.y)" :data="plan_nodes"
                      :showArrow="form.showArrow"
                      :ystep="parseInt(form.ystep)" :xstep="parseInt(form.xstep)" :lineStyle="form.lineStyle"
                      @select="handleSelect"/>
        <Modal
            :title="curren_node.node_name"
            v-model="node_result_show"
            :closable="true"
            :width="300"
            :mask-closable="false"
            class="publish_plan_modal_class"
        >
            <div>
                <div> 详情: {{ curren_node.node_run_result.detail }}</div>
                <div class="button-container" v-if="show_action_type">
                    <button class="red-button" @click="handleButtonClick">{{ action_type_tittle }}</button>
                </div>
            </div>
            <div slot="footer"/>
        </Modal>

    </div>
</template>

<script>
import {get_publish_plan_nodes} from "@/spider-api/iter-plan";
import {publishPlanExecuteImmediately} from "@/spider-api/get-iter-info";
import VuePipeline from 'vue-pipeline'

export default {
    name: 'IterPublishPlanPipeline',
    data() {
        return {
            node_result_show: false,
            curren_node: {node_run_result: {}},
            current: 1,
            node_show: true,
            publishPlanDetailHeight: 800,
            action_type: '',
            action_type_tittle: '',
            show_action_type: false,
            batch_no: "",
            form: {
                x: 50,
                y: 55,
                xstep: 120,
                ystep: 70,
                data: 0,
                showArrow: true,
                lineStyle: "default",
                from: 0,
                to: 0
            },
            plan_nodes: [],
            base_plan_nodes: [
                {
                    name: "Start",
                    hint: "1m23s",
                    status: "start",
                    next: [{index: 1, weight: 0}]
                },
                {name: "End ", hint: "2m23s", status: "end", next: []}
            ],
            interval: 0
        }
    },
    props: {
        pipeline_id: {
            type: String,
            default: ''
        }
    },
    methods: {
        remove_paused_class: function () {
            setTimeout(function () {
                let polygons = document.querySelectorAll('polygon[points="-4,-4.65 -4,4.65 -4,4.65 -1.5,4.65 -1.5,-4.65"], polygon[points="4,-4.65 1.5,-4.65 1.5,-4.65 1.5,4.65 4,4.65"]');
                if (polygons) {
                    polygons.forEach(function (polygon) {
                        polygon.parentNode.removeChild(polygon);
                    });
                }
            }, 20)
        },
        get_publish_plan_nodes_new_status(batch_no) {
            this.batch_no = batch_no
            get_publish_plan_nodes({"iter_id": this.pipeline_id, "batch_no": batch_no}).then(res => {
                //获取应用信息
                if (res.data.status === "success") {

                    const nodes = res.data.data

                    const statusEnum = {
                        start: 'start',
                        SUCCESS: 'success',
                        FAIL: 'failure',
                        RUNNING: 'running',
                        READY: 'paused',
                        STOP: 'failure',
                        end: 'end',
                    };


                    // 前面追加开始节点
                    let minOrderNo = Math.min(...nodes.map(node => node.order_no));
                    nodes.unshift({
                        "phase": "开始",
                        "node_name": "开始",
                        "order_no": 0,
                        "next_node_order_no": minOrderNo,
                        "node_status": 'start',
                    });

                    let end_node_order_no = 0
                    // 所有节点的 order_no 加 1
                    nodes.forEach(node => {
                        if (node.next_node_order_no < 0) {
                            end_node_order_no = node.order_no + 10;
                            node.next_node_order_no = end_node_order_no;
                        }
                    });

                    // 追加结束节点
                    nodes.push({
                        "phase": "结束",
                        "node_name": "结束",
                        "order_no": end_node_order_no,
                        "next_node_order_no": -1,
                        "node_status": "end"
                    });

                    this.plan_nodes = nodes.map((node, index) => {
                        const next = [];
                        if (index < nodes.length - 1) {
                            nodes.map((next_node, next_index) => {
                                if (next_node.order_no === node.next_node_order_no) {
                                    next.push({
                                        index: next_index,
                                        weight: (next_node.node_status === 'SUCCESS' || next_node.node_status === 'start') ? 2 : 0
                                    });
                                }
                            })
                        }

                        let status = statusEnum[node.node_status]
                        if (index == 1 && node.node_status == 'READY') {
                            status = statusEnum['RUNNING']
                        }
                        return {
                            originNode: node,
                            name: node.node_name,
                            hint: node.phase, // 这里需要根据实际情况填写正确的值
                            status: status,
                            next: next
                        };
                    });
                    console.log(" this.plan_nodes：%o", this.plan_nodes)
                    this.$refs["iterPublishPlanPipelineSelf"].render();
                    this.remove_paused_class();
                } else {
                    this.$Notice.error({
                        title: 'Error',
                        desc: res.data.message,
                        duration: 0
                    });
                }
                this.publishPlanDetailHeight = window.innerHeight * 0.7
                this.$refs.publishPlanDetail.style.height = this.publishPlanDetailHeight + 'px'
            }).catch(err => {
                this.$Notice.error({
                    title: 'Error',
                    desc: err.message,
                    duration: 0
                });
            })
        },
        handleButtonClick() {

            publishPlanExecuteImmediately(this.curren_node.batch_no, this.pipeline_id).then(res => {
                console.log("res:%o", res)
                if (res.data.status == 'success') {
                    this.$Notice.info({
                        desc: '立即执行生效',
                        duration: 1000,
                    });
                } else {
                    this.$Notice.error({
                        title: 'Error',
                        desc: res.data.msg,
                        duration: 1000
                    })
                }
            })
        },
        handleSelect(node) {
            console.log(" this.$refs[\"iterPublishPlanPipelineSelf\"].selectedList：%o", this.$refs["iterPublishPlanPipelineSelf"].selectedList)
            this.$refs["iterPublishPlanPipelineSelf"].selectedList.fill(false, 0, this.plan_nodes.length);
            if (node.originNode.action_type == 'execute_immediately') {
                this.show_action_type = true
                this.action_type_tittle = '立即执行'
            } else {
                this.show_action_type = false
            }
            // this.action_type = "立即执行"
            console.log("node:%o", node)
            let _this = this
            if (node.status == 'start' || node.status == 'end') {
                return false
            }
            _this.curren_node = node.originNode
            if (_this.curren_node.node_run_result) {
                if (_this.curren_node.node_run_result.show_type === 'external_link') {
                    window.open(_this.curren_node.node_run_result.external_link_url)
                }
                if (_this.curren_node.node_run_result.show_type === "text") {
                    _this.node_result_show = true
                }
            } else {
                let detail = "無"
                if (node.status === 'running') {
                    detail = "运行中"
                } else if (node.status === 'paused') {
                    detail = "未运行"
                } else if (node.status === 'failure') {
                    detail = "运行出錯"
                }
                _this.curren_node.node_run_result = {"detail": detail}
                _this.node_result_show = true

            }
        },
        initThisVue(batch_no) {
            this.plan_nodes = [...this.base_plan_nodes];
            this.get_publish_plan_nodes_new_status(batch_no)
            this.interval = setInterval(() => this.get_publish_plan_nodes_new_status(batch_no), 5000);
        },
        cancel_interval() {
            this.plan_nodes = [...this.base_plan_nodes];
            clearInterval(this.interval)
        }
    }
    ,
    mounted() {
        clearInterval(this.interval)
    }
    ,
    destroyed() {
        this.plan_nodes = this.base_plan_nodes
        clearInterval(this.interval)
    }
}
</script>

<style>
.publish_plan {
    height: 100%;
}

.running {
    animation: rotation 2s linear infinite
}

.button-container {
    display: flex;
    justify-content: center;
    margin-top: 20px;
}

.red-button {
    background-color: red;
    color: white; /* 设置按钮文本为白色，以便在红色背景上看清楚 */
}

@keyframes rotation {
    0% {
        transform: rotate(0);
    }
    100% {
        transform: rotate(360deg);
    }
}
</style>
