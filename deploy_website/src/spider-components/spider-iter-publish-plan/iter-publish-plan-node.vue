<template>
    <div>
        <Tabs size="small" v-if="tabs.length > 0" style="height: 100vh">
            <TabPane v-for="item in tabs" :name="item.value" :label="item.label">
                <div ref="publishPlanNode" class="plan-node" style="height: 100vh">
                    <div class="plan-node-left">
                        <div class="ivu-steps ivu-steps-vertical">
                            <div
                                :class="publish_plan_phase.class"
                                style="width: 100%;"
                                v-for="(publish_plan_phase, phase_index) in publish_plan_phases"
                            >
                                <div class="ivu-steps-tail"><i></i></div>
                                <div
                                    class="ivu-steps-head"
                                    style="cursor: pointer;"
                                    @click="handlePhaseClick(publish_plan_phase, phase_index)"
                                >
                                    <div
                                        v-if="
                                            publish_plan_phase.checkStatus === 'finish' && !publish_plan_phase.current
                                        "
                                        class="ivu-steps-head-inner"
                                    >
                                        <span class="ivu-steps-icon ivu-icon ivu-icon-ios-checkmark"></span>
                                    </div>
                                    <div
                                        v-if="publish_plan_phase.checkStatus === 'wait' || publish_plan_phase.current"
                                        class="ivu-steps-head-inner"
                                    >
                                        <span>{{ phase_index + 1 }}</span>
                                    </div>
                                </div>
                                <div
                                    class="ivu-steps-main"
                                    style="cursor: pointer;"
                                    @click="handlePhaseClick(publish_plan_phase, phase_index)"
                                >
                                    <div class="ivu-steps-title">{{ publish_plan_phase.phase_name }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- 内容区域 -->
                    <div class="plan-node-right">
                        <Card
                            v-if="
                                publish_plan_phases[current_phase_index] &&
                                    publish_plan_phases[current_phase_index].nodes
                            "
                            style="width:100%;margin-bottom: 1em;border: 1px solid #2d8cf0;"
                            v-for="(phase, phase_index) in publish_plan_phases"
                        >
                            <p slot="title" class="plan-node-card-title">
                                <Icon type="ios-film-outline"></Icon>
                                {{ phase.phase_name }}
                            </p>
                            <div v-for="node in phase.nodes">
                                <Card
                                    v-if="node.node_params && node.node_params.length > 0"
                                    style="margin-bottom: 1em;"
                                >
                                    <div v-for="param in node.node_params">
                                        <div
                                            v-if="param.param_show_type === 'text' && param.param_show_flag"
                                            class="plan-node-card-param"
                                        >
                                            <Tooltip :content="param.param_desc">
                                                <div class="plan-node-card-param-desc">{{ param.param_cn_name }}:</div>
                                            </Tooltip>
                                            <Input
                                                size="small"
                                                :disabled="param.param_update_disable"
                                                style="width:200px;"
                                                type="text"
                                                v-model="param.param_value"
                                            />
                                        </div>
                                        <div
                                            v-if="param.param_show_type === 'time_select' && param.param_show_flag"
                                            class="plan-node-card-param"
                                        >
                                            <Tooltip :content="param.param_desc">
                                                <div class="plan-node-card-param-desc">{{ param.param_cn_name }}:</div>
                                            </Tooltip>
                                            <DatePicker
                                                type="datetime"
                                                size="small"
                                                v-model="param.param_value"
                                                format="yyyy-MM-dd HH:mm"
                                                placeholder="Select date and time"
                                                style="width: 200px"
                                                @on-change="change_time(param, node)"
                                                @on-clear="clear_time(param, node)"
                                            ></DatePicker>
                                            <div v-if="!param.param_value" class="plan-node-card-param-desc">必填</div>
                                        </div>
                                        <div
                                            v-else-if="param.param_show_type === 'select' && param.param_show_flag"
                                            class="plan-node-card-param"
                                        >
                                            <Tooltip :content="param.param_desc">
                                                <div class="plan-node-card-param-desc">{{ param.param_cn_name }}:</div>
                                            </Tooltip>
                                            <Select
                                                filterable
                                                size="small"
                                                clearable
                                                v-model="param.param_value"
                                                style="width:200px; "
                                                @on-change="
                                                    bizBrNameChangeSelect(param.param_value, param.param_options_type)
                                                "
                                            >
                                                <Option
                                                    v-if="param.param_options_type === 'iter_app'"
                                                    v-for="option in iter_app_options"
                                                    :value="option.value"
                                                    >{{ option.name }}
                                                </Option>
                                                <Option
                                                    v-if="param.param_options_type === 'env'"
                                                    v-for="option in env_options"
                                                    :value="option.value"
                                                    >{{ option.name }}
                                                </Option>
                                                <Option
                                                    v-if="param.param_options_type === 'user'"
                                                    v-for="option in user_options"
                                                    :value="option.value"
                                                    >{{ option.name }}
                                                </Option>
                                                <Option
                                                    v-if="param.param_options_type === 'biz_iter_branch'"
                                                    v-for="option in biz_iter_branch_options"
                                                    :value="option.value"
                                                    >{{ option.name }}
                                                </Option>
                                                <Option
                                                    v-if="param.param_options_type === 'biz_flow_name'"
                                                    v-for="option in biz_flow_name_options"
                                                    :value="option.value"
                                                    >{{ option.name }}
                                                </Option>
                                                <Option
                                                    v-if="!param.param_options_type"
                                                    v-for="option in param.param_options"
                                                    :value="option.value"
                                                    >{{ option.name }}
                                                </Option>
                                            </Select>
                                            <div
                                                v-if="!param.param_value && publish_plan_phases[phase_index].required"
                                                class="plan-node-card-param-desc"
                                            >
                                                必填
                                            </div>
                                        </div>
                                        <div
                                            v-else-if="
                                                param.param_show_type === 'draggable_order_list' &&
                                                    param.param_show_flag &&
                                                    param.draggable_order_list_type === 'prod-suites'
                                            "
                                            class="plan-node-card-param"
                                        >
                                            <Tooltip :content="param.param_desc">
                                                <div class="plan-node-card-param-desc">{{ param.param_cn_name }}:</div>
                                            </Tooltip>
                                            <draggable
                                                style="width: 200px;height: 100px; display: flex;justify-content: center;align-items: center;flex-direction: column"
                                                v-model="param.param_value"
                                            >
                                                <div
                                                    v-for="option in param.param_value"
                                                    style=" cursor: pointer;margin: 8px -800px 8px 0;border: 1px solid #ccc;border-radius: 8px; width: 1000px;justify-content: left;align-items: center;display: flex;"
                                                >
                                                    <div
                                                        style=" cursor: pointer;margin: 4px 4px 4px 4px;border: 1px solid #ccc;border-radius: 4px; width: 200px;justify-content: center;align-items: center;display: flex;"
                                                    >
                                                        {{ option.name.suite_name }}
                                                    </div>
                                                    <div>
                                                        {{ option.name.node_list }}
                                                    </div>
                                                </div>
                                            </draggable>
                                        </div>
                                    </div>
                                </Card>
                            </div>
                        </Card>
                        <div style="height: 1em; width: 100%; height: 50px;"></div>
                    </div>
                </div>
            </TabPane>
        </Tabs>
        <!-- 选择应用modal -->
        <Modal
            v-model="show_add_app_modal"
            @on-cancel="cancel_add_app"
            footer-hide
            title="选择上线的应用"
            style="padding-bottom: 1em;"
            class="show_add_app_modal"
        >
            <Card
                v-if="show_add_app_modal"
                style="width:100%;margin-top: 1em;margin-bottom: 1em;border: 2px solid rgb(203 193 194);"
                v-for="node in publish_plan_basic_phases[0].nodes"
            >
                <p slot="title" class="plan-node-card-title">
                    <Icon type="ios-film-outline"></Icon>
                    {{ node.node_name }}
                </p>
                <div v-if="node.node_params && node.node_params.length > 0" v-for="param in node.node_params">
                    <div v-if="param.param_show_type === 'text' && param.param_show_flag" class="plan-node-card-param">
                        <div class="plan-node-card-param-desc">{{ param.param_cn_name }}:</div>
                        <Input
                            size="small"
                            :disabled="param.param_update_disable"
                            style="width:200px;"
                            type="text"
                            v-model="param.param_value"
                        />
                    </div>
                    <div
                        v-if="param.param_show_type === 'select' && param.param_show_flag"
                        class="plan-node-card-param"
                    >
                        <div class="plan-node-card-param-desc">{{ param.param_cn_name }}:</div>
                        <Select
                            ref="select_app"
                            filterable
                            size="small"
                            clearable
                            v-model="param.param_value"
                            style="width:200px;"
                            @on-change="change_iter_app_name"
                        >
                            <Option v-for="option in iter_app_options" :value="option.value">{{ option.name }}</Option>
                        </Select>
                    </div>
                </div>
            </Card>
        </Modal>
    </div>
</template>
<script>
import draggable from 'vuedraggable'
import { createIterPublishPlan, getCurrentIterAppPublishPlanLatestHistory } from '@/spider-api/iter-plan'
import { get_app_prod_suites, getAppProdSuites } from '@/spider-api/mgt-env'
import { get_test_flow_name_list } from '@/spider-api/biz-mgt'

export default {
    name: 'IterPublishPlanNode',
    components: { draggable },
    data() {
        return {
            watchPlanPhaseStatusInterval: 0,
            show_add_app_modal: false,
            tabs: [],
            current_phase_index: 0,
            current_phase: {},
            publish_plan_phases: [],
            app_prod_suites: [],
            biz_flow_name_options: []
        }
    },
    watch: {
        publish_plan_phases: function(new_plan_phases, old_plan_phases) {
            console.log('new_plan_phases:%o', new_plan_phases)
        }
    },
    props: {
        pipeline_id: {
            type: String,
            default: ''
        },
        plan_type: {
            type: String,
            default: ''
        },
        iter_app_options: {
            type: Array,
            default: []
        },
        env_options: {
            type: Array,
            default: []
        },
        user_options: {
            type: Array,
            default: []
        },
        biz_iter_branch_options: {
            type: Array,
            default: []
        },
        // biz_flow_name_options: {
        //   type: Array,
        //   default: []
        // },
        publish_plan_basic_phases: {
            type: Array,
            default: []
        }
    },
    methods: {
        handlePhaseClick(current_phase, phase_index) {
            // this.current_phase = current_phase
            // this.current_phase_index = phase_index
            // this.publish_plan_phases.forEach((phase, index) => {
            //   if (index !== phase_index) {
            //     phase.current = false
            //     if (phase.checkStatus === "finish") {
            //       phase.checkStatusClass = 'ivu-steps-item'
            //       phase.extClass = 'ivu-steps-status-finish'
            //       phase.class = `${phase.checkStatusClass} ${phase.extClass} `
            //     } else {
            //       phase.extClass = ""
            //       phase.class = `${phase.checkStatusClass} ${phase.extClass} `
            //     }
            //   }
            // })
            // current_phase.current = true
            // current_phase.checkStatusClass = "ivu-steps-item"
            // current_phase.extClass = "ivu-steps-status-process"
            // current_phase.class = `${current_phase.checkStatusClass} ${current_phase.extClass} `
        },
        add_publish_app() {
            this.show_add_app_modal = true
        },
        // select组件的change回调函数
        bizBrNameChangeSelect(value, param_options_type) {
            console.log(`${value}:${param_options_type}`)
            if (param_options_type === 'biz_iter_branch') {
                if (typeof value === 'undefined') {
                    value = 0
                }
                // let query_params = {"biz_test_iter_br": value}
                let query_params = { biz_test_iter_id: value }
                this.biz_flow_name_options = []
                get_test_flow_name_list(query_params).then(res => {
                    if (res.data.status === 'success') {
                        this.biz_flow_name_options = res.data.data.map(item => {
                            return {
                                value: item.biz_flow_name,
                                name: item.biz_flow_name
                            }
                        })
                    }
                })
            }
        },
        // 初始化tabs
        initTabs(app_name) {
            this.tabs.push({
                module_name: app_name,
                name: app_name,
                label: h => {
                    return h('div', [
                        h(
                            'span',
                            {
                                on: {
                                    contextmenu: event => {
                                        event.preventDefault() // 阻止默认右键菜单
                                        this.$contextmenu({
                                            items: [
                                                {
                                                    label: '复制',
                                                    onClick: () => {},
                                                    icon: 'el-icon-document-copy'
                                                },
                                                {
                                                    label: '删除',
                                                    onClick: () => {},
                                                    icon: 'el-icon-delete'
                                                }
                                            ],
                                            event,
                                            customClass: 'custom-class', // 自定义菜单 class
                                            zIndex: 99999, // 菜单样式 z-index，浮层设置，越大值显示越上层
                                            minWidth: 80 // 主菜单最小宽度
                                        })
                                    }
                                }
                            },
                            app_name
                        )
                    ])
                }
            })
        },
        // 初始化状态
        initThisVue(update) {
            this.clear()
            if (!update) {
                return false
            }
            if (this.pipeline_id) {
                this.publish_plan_basic_phases[0].nodes[0].node_params[0].param_value = this.pipeline_id
            }
            if (this.publish_plan_phases.length === 0) {
                this.show_add_app_modal = true
            }
        },
        // 选择应用回调 - 选择应用后获取应用的发布计划历史
        change_iter_app_name(iter_app_name) {
            if (iter_app_name) {
                this.publish_plan_basic_phases[0].nodes[0].node_params[1].param_value = iter_app_name
            } else {
                return false
            }
            this.initTabs(iter_app_name)
            this.getPublishPlanHistory(iter_app_name)
        },
        // 查询默认配置
        getPublishPlanHistory(iter_app_name) {
            getCurrentIterAppPublishPlanLatestHistory({
                module_name: iter_app_name,
                plan_type: this.plan_type
            })
                .then(res => {
                    // 获取应用信息
                    if (res.data.status === 'success') {
                        // 配置数据
                        this.publish_plan_phases = res.data.data
                        this.publish_plan_phases.forEach(phase => {
                            phase.nodes.forEach(node => {
                                node.node_params.forEach(param => {
                                    if (param.param_key === 'module_name') {
                                        param.param_value = iter_app_name
                                    }
                                    if (param.param_key === 'iter_id') {
                                        param.param_value = this.pipeline_id
                                    }
                                })
                            })
                            phase.checkStatusClass = 'ivu-steps-item ivu-steps-status-wait'
                            phase.extClass = ''
                            phase.class = `${phase.checkStatusClass} ${phase.extClass} `
                            phase.checkStatus = 'wait'
                        })
                        this.get_app_prod_suites(iter_app_name)
                        this.publish_plan_phases[0].extClass = 'ivu-steps-status-process'
                        this.publish_plan_phases[0].class = `${this.publish_plan_phases[0].checkStatusClass} ${this.publish_plan_phases[0].extClass} `
                        this.current_phase = this.publish_plan_phases[0]
                        console.log('current_phase:%o', this.current_phase)
                        this.watchPlanPhaseStatus()
                        this.show_add_app_modal = false
                    } else {
                        this.$Notice.error({
                            title: 'Error',
                            desc: res.data.message,
                            duration: 0
                        })
                    }
                })
                .catch(err => {
                    this.$Notice.error({
                        title: 'Error',
                        desc: err.message,
                        duration: 0
                    })
                })
        },
        // 选择时间回调 - 时间格式处理
        change_time(param, node) {
            if (param.param_key == 'schedule_time') {
                // 将date时间类型转成字符串格式 2019-01-01 00:00:00
                var dateObject = new Date(param.param_value)
                // 从 Date 对象中获取年、月、日、小时和分钟
                var year = dateObject.getFullYear()
                var month = dateObject.getMonth() + 1 // 返回的月份从 0 开始，所以需要加 1
                var day = dateObject.getDate()
                var hours = dateObject.getHours()
                var minutes = dateObject.getMinutes()
                // 格式化为指定格式的字符串
                var formattedDate =
                    year +
                    '-' +
                    (month < 10 ? '0' + month : month) +
                    '-' +
                    (day < 10 ? '0' + day : day) +
                    ' ' +
                    (hours < 10 ? '0' + hours : hours) +
                    ':' +
                    (minutes < 10 ? '0' + minutes : minutes)
                node.schedule_time = formattedDate
                param.param_value = formattedDate
            }
        },
        clear_time(param, node) {
            param.param_value = undefined
        },
        // 清空数据
        clear() {
            this.tabs = []
            this.app_prod_suites = []
            this.current_phase_index = 0
            this.publish_plan_phases = []
            this.$emit('clear_publish_plan_phases')
            clearInterval(this.watchPlanPhaseStatusInterval)
        },
        // 选择应用弹框的取消按钮
        cancel_add_app() {
            if (this.publish_plan_phases.length === 0) {
                this.$emit('create_publish_plan_modal')
            }
        },
        // 发布事件 - 父组件调用 - 数据校验和保存接口调用
        submit_publish_plan() {
            let can_submit = true
            let block_phase = {}
            this.publish_plan_phases.forEach((phase, index) => {
                if (phase.checkStatus !== 'finish' && phase.required) {
                    can_submit = false
                    block_phase = phase
                }
            })
            if (!can_submit) {
                this.$Message.warning({ content: `${block_phase.phase_name} 阶段参数配置不完整！！！`, duration: 2 })
                return false
            }
            let warn = false
            let publish_plan_phases = this.publish_plan_phases.filter(phase => {
                let canSubmit = true
                phase.nodes.map(node => {
                    node.node_params.map(param => {
                        if (!param.param_value) {
                            canSubmit = false
                        }
                    })
                })
                if (phase.required && !canSubmit) {
                    warn = true
                }
                return canSubmit
            })
            if (warn) {
                this.$Message.warning({ content: `有必填阶段的必填参数未填写！！！`, duration: 2 })
                return false
            }
            createIterPublishPlan({
                iter_id: this.pipeline_id,
                plan_type: this.plan_type,
                module_name: this.publish_plan_basic_phases[0].nodes[0].node_params[1].param_value,
                branch_name: this.pipeline_id.split('_')[1],
                phases: publish_plan_phases
            })
                .then(res => {
                    console.log('res:%o', res)
                    if (res.data.status === 'success') {
                        this.$Message.success({ content: '发布计划创建成功', duration: 2 })
                        this.$emit('clearPublishPlanForm')
                        this.$emit('search_publish_plan')
                    } else {
                        this.$Notice.error({
                            title: 'Error',
                            desc: res.data.msg,
                            duration: 0
                        })
                    }
                })
                .catch(err => {
                    this.$Notice.error({
                        title: 'Error',
                        desc: err.message,
                        duration: 0
                    })
                })
            this.$emit('on-click')
        },
        // 用于左侧步骤样式控制
        watchPlanPhaseStatus() {
            let _this = this
            _this.watchPlanPhaseStatusInterval = setInterval(() => {
                _this.publish_plan_phases.forEach((phase, index) => {
                    let status = 'finish'
                    phase.nodes.forEach(node => {
                        node.node_params.forEach(param => {
                            if (!param.param_value) {
                                status = 'wait'
                            }
                        })
                    })
                    if (status === 'finish') {
                        phase.checkStatus = 'finish'
                        if (index === this.current_phase_index) {
                            phase.checkStatusClass = 'ivu-steps-item ivu-steps-status-wait'
                            phase.extClass = 'ivu-steps-status-process'
                        } else {
                            phase.checkStatusClass = 'ivu-steps-item ivu-steps-status-finish'
                            phase.extClass = ''
                        }
                        phase.class = `${phase.checkStatusClass} ${phase.extClass} `
                    } else {
                        phase.checkStatus = 'wait'
                        phase.checkStatusClass = 'ivu-steps-item ivu-steps-status-wait'
                        if (index === this.current_phase_index) {
                            phase.extClass = 'ivu-steps-status-process'
                        } else {
                            phase.extClass = ''
                        }
                        phase.class = `${phase.checkStatusClass} ${phase.extClass} `
                    }
                })
            }, 1000)
        },
        // 拖拽数据查询
        get_app_prod_suites(module_name) {
            getAppProdSuites(module_name).then(res => {
                if (res.data.status === 'success') {
                    this.app_prod_suites = res.data.data.map(item => {
                        if (item.node_ip_list !== '' && item.node_ip_list !== null) {
                            return {
                                env: item.suite_code,
                                name: {
                                    suite_name: item.suite_name,
                                    node_list: item.node_ip_list
                                }
                            }
                        }
                    })
                    this.publish_plan_phases.forEach(phase => {
                        phase.nodes.forEach(node => {
                            node.node_params.forEach(param => {
                                if (
                                    !param.param_value &&
                                    param.param_show_type === 'draggable_order_list' &&
                                    param.param_show_flag &&
                                    param.draggable_order_list_type === 'prod-suites'
                                ) {
                                    param.param_value = this.app_prod_suites
                                }
                            })
                        })
                    })
                }
            })
        }
    },
    mounted() {
        this.initThisVue()
    },
    destroyed() {
        this.tabs = []
        clearInterval(this.watchPlanPhaseStatusInterval)
    }
}
</script>

<style>
.ivu-card-head {
    padding: 0;
}

.ivu-tabs-mini .ivu-tabs-tab {
    font-weight: bold !important;
}

.plan-phase-card-title {
    font-size: 14px !important;
}

.plan-node-card-title {
    font-size: 12px !important;
}

.ivu-tabs-mini .ivu-tabs-tab {
    font-size: 14px !important;
}

.plan-node-card-param {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-bottom: 5px;
}

.plan-node-card-param-desc {
    min-width: 60px;
    max-width: 30%;
    margin-right: 4px;
    justify-content: flex-end;
    display: flex;
    font-size: 12px;
}

.ivu-card-head {
    border-bottom: 1px solid #2d8cf0;
}

.plan-node-left {
    display: flex;
    justify-content: center;
    height: 80%;
    width: 10%;
}

.plan-node-right {
    height: 100%;
    border-left: 1px solid rgb(203 193 194);
    width: 88%;
    padding-left: 2%;
}

.plan-node {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-direction: row;
    overflow: auto;
    height: 80%;
    width: 100%;
    position: relative;
}

.ivu-steps-vertical .ivu-steps-main {
    min-height: 100px !important;
}

.ivu-steps {
    height: 100%;
    width: 80%;
}

.show_add_app_modal .ivu-modal-body {
    padding: 0 16px 1px 16px;
}
</style>
