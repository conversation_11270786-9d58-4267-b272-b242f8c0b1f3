<!--
 * @Author: chao.bao <EMAIL>
 * @Date: 2024-08-23 15:56:14
 * @LastEditors: chao.bao <EMAIL>
 * @LastEditTime: 2025-05-20 17:00:45
 * @FilePath: /website_web/deploy_website/src/view/personal/quality_detail/index.vue
 * @Description: 质量报告邮件模版
-->
<template>
    <div class="contain">
        <div class="warpper">
            <!--自定义table -->
            <div class="custom_table">
                <div class="table_header">
                    <div v-for="item in columns" :key="item.key" class="table_header_key cell_width">
                        {{ item.label }}
                    </div>
                </div>
                <div class="table_body">
                    <div v-for="item in tableData" :key="item.id" class="table_body_row">
                        <!-- 业务_编排 -->
                        <div class="row_id flex_center cell_width">
                            <a @click="goDetail(item)">{{ item.biz_flow_name }}</a>
                        </div>
                        <!-- 最新执行时间 -->
                        <div class="row_id flex_center cell_width">{{ item.run_time }}</div>
                        <!-- 执行状态 -->
                        <div
                            class="row_id flex_center cell_width"
                            :class="{ green_font: item.testset_status === 'success', red_font: item.testset_status !== 'success' }"
                        >
                            {{ item.testset_status || '-' }}
                        </div>
                        <!-- 最新结果 -->
                        <div
                            class="row_id flex_center cell_width green_font"
                            :class="{ red_font: item.avg_pass_rate !== 100 }"
                        >
                            <a v-if="item.report_url" @click="goReport(item)" class="report-link">{{ item.avg_pass_rate }}</a>
                            <span v-else>{{ item.avg_pass_rate }}</span>
                        </div>
                        <div class="row_item">
                            <div
                                v-for="(child, childIndex) in item.app_deploy_info"
                                :key="childIndex"
                                class="row_item_app"
                            >
                                <!-- 可覆盖应用 -->
                                <div class="row_item_app_name flex_center cell_width">{{ child.app_name }}</div>
                                <!-- 分支信息 -->
                                <div
                                    class="row_item_app_name flex_center cell_width"
                                    :class="{ yellow_font: checkId(child.app_deploy_branch) }"
                                >
                                    {{ child.app_deploy_branch }}
                                </div>
                                <!-- 应用最新构建 -->
                                <div
                                    class="row_item_app_name flex_center cell_width"
                                    :class="{ yellow_font: child.is_out_of_run_time }"
                                >
                                    {{ child.build_time }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
export default {
    data() {
        return {
            columns: [
                {
                    label: '业务_编排'
                },
                {
                    label: '最新执行时间'
                },
                {
                    label: '执行状态'
                },
                {
                    label: '最新结果(%)'
                },
                {
                    label: '可覆盖应用'
                },
                {
                    label: '分支信息'
                },
                {
                    label: '应用最新构建'
                }
            ]
        }
    },
    props: {
        tableData: {
            type: Array,
            default: () => []
        },
        pipeline_id: {
            type: String,
            default: ''
        }
    },
    computed: {
        biz_flow_name() {
            return this.$route.query.biz_flow_name
        }
    },
    methods: {
        checkId(id) {
            return id !== this.pipeline_id.split('_')[1]
        },
        goDetail(item) {
            window.open(
                `/biz-iter-mgt/test_data_exec_new?biz_code=${item.biz_code}&biz_br_name=${item.biz_iter_br}&flow=${item.biz_flow_name}&iteration_id=${this.pipeline_id}&version_type=REMOTE`,
                `_blank`
            )
        },
        goReport(item) {
            if (item.report_url) {
                window.open(item.report_url, '_blank')
            }
        }
    }
}
</script>
<style lang="less" scoped>
.contain {
    height: 100%;
    overflow: auto;
}
.warpper {
    // min-width: 1400px;
    width: 100%;
    .title {
        font-weight: bold;
        font-size: 20px;
        margin-bottom: 20px;
    }
    .flex_center {
        // height: 100px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .cell_width {
        // width: 130px;
        // width: 160px;
        width: 14.285714%; // 7列，每列约14.29%
        white-space: normal;
        word-break: break-all;
        overflow: hidden;
        text-align: center;
        // padding: 0 5px;
        padding: 0 4px 0 5px;
        min-height: 40px;
    }
    .green_font {
        color: green;
    }
    .red_font {
        color: red;
    }
    .yellow_font {
        color: #ff9900;
    }
    .report-link {
        cursor: pointer;
        text-decoration: underline;
        color: inherit;
    }
    .report-link:hover {
        opacity: 0.8;
    }
    .custom_table {
        display: inline-block;
        // width: auto;
        width: 100%;
        border-left: 1px solid #e8eaec;
        border-top: 1px solid #e8eaec;
        .table_header {
            display: flex;
            align-items: center;
            // background-color: #cfcfe3;
            background-color: #f8f8f9;

            height: 40px;
            .table_header_key {
                border-right: 1px solid #e8eaec;
                border-bottom: 1px solid #e8eaec;
                line-height: 40px;
                text-align: center;
                color: #515a6e;
                font-weight: 700;
            }
        }
        .table_body {
            .table_body_row {
                // background: #eef5f2 !important;
                display: flex;
                .row_id {
                    border-right: 1px solid #e8eaec;
                    border-bottom: 1px solid #e8eaec;
                }
                .row_item {
                    flex: 1;
                    .row_item_app {
                        display: flex;
                        .row_item_app_name {
                            width: 100%;
                            box-sizing: border-box;
                            border-right: 1px solid #e8eaec;
                            border-bottom: 1px solid #e8eaec;
                            text-align: center;
                        }
                        .row_item_app_wrapper {
                            display: flex;
                            flex-direction: column;
                            .row_item_app_item {
                                display: flex;
                                height: 100%;
                                .cell_width {
                                    min-height: 40px;
                                    text-align: center;
                                    min-height: 40px;
                                    border-right: 1px solid #e8eaec;
                                    border-bottom: 1px solid #e8eaec;
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
</style>
