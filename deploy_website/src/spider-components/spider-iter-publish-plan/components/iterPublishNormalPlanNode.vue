<!--
 * @Author: chao.bao <EMAIL>
 * @Date: 2024-09-10 15:30:47
 * @LastEditors: chao.bao <EMAIL>
 * @LastEditTime: 2025-01-03 16:03:15
 * @FilePath: /website_web/deploy_website/src/spider-components/spider-iter-publish-plan/iterPublishAllPlanNode.vue
 * @Description: 发布normal人工测试model
-->
<template>
    <div class="wrapper">
        <Spin fix v-if="loadding"></Spin>
        <Form ref="formValidate" :label-width="labelWidth">
            <!-- 应用 -->
            <Card class="card_contain">
                <div slot="title" class="card_header_title">
                    <Icon type="ios-film-outline" size="16"></Icon>
                    <span>基础</span>
                </div>
                <FormItem class="form_item_width" label="应用" prop="iter_app_name">
                    <div>
                        <div v-for="item in applyList" :key="item">{{ item }}</div>
                    </div>
                </FormItem>
            </Card>
            <!-- 多应用配置 -->
            <div v-for="item in publish_before_config" :key="item.phase_name">
                <div v-for="(node_param, nodeIndex) in item.nodes[0].node_params" :key="node_param.module_name">
                    <Card v-if="node_param.execute_auto_test" class="card_contain">
                        <div slot="title" class="card_header_title">
                            <Icon type="ios-film-outline" size="16"></Icon>
                            <span>{{ node_param.module_name }} - 自动化</span>
                        </div>
                        <div v-for="moduleItem in node_param.module_params" :key="moduleItem.param_key">
                            <FormItem
                                v-if="moduleItem.param_show_flag"
                                :class="{
                                    form_item_width: moduleItem.param_show_type !== enumType.Table
                                }"
                                :label="moduleItem.param_cn_name"
                                :prop="moduleItem.param_key"
                            >
                                <Input
                                    v-if="moduleItem.param_show_type === enumType.Input"
                                    v-model="moduleItem.param_value"
                                    :disabled="moduleItem.param_update_disable"
                                    placeholder="请输入"
                                ></Input>
                                <Select
                                    v-else-if="moduleItem.param_show_type === enumType.Select"
                                    v-model="moduleItem.param_value"
                                    @on-change="val => selectApplyHandler(val, moduleItem.param_key, nodeIndex)"
                                    :disabled="moduleItem.param_update_disable"
                                    clearable
                                    filterable
                                >
                                    <!-- 业务分支 -->
                                    <template v-if="moduleItem.param_key === 'biz_iter_branch'">
                                        <Option
                                            v-for="item in applyData[nodeIndex].iter_list_options"
                                            :key="item.value"
                                            :value="item.value"
                                            >{{ item.name }}</Option
                                        >
                                    </template>
                                    <!-- 环境名称（自动化） -->
                                    <template v-if="moduleItem.param_key === 'suite_code'">
                                        <Option
                                            v-for="item in applyData[nodeIndex].fdevelopment_evn_options"
                                            :key="item.value"
                                            :value="item.value"
                                            >{{ item.name }}</Option
                                        >
                                    </template>
                                    <!-- 自动化编排 -->
                                    <template v-if="moduleItem.param_key === 'biz_flow_name'">
                                        <Option
                                            v-for="item in applyData[nodeIndex].flow_list_options"
                                            :key="item.value"
                                            :value="item.value"
                                            >{{ item.name }}</Option
                                        >
                                    </template>
                                </Select>
                                <div v-else-if="moduleItem.param_show_type === enumType.Table">
                                    <!-- 自动化编排 - 列表 - 服务端需要单独给一个item控制展示 -->
                                    <div v-if="moduleItem.param_key === 'test_set'" class="btn_custom">
                                        <Button type="primary" ghost size="small" @click="branchReset(nodeIndex)"
                                            >一键指定归档版本</Button
                                        >
                                        <Button
                                            style="margin-left: 10px;"
                                            type="primary"
                                            ghost
                                            size="small"
                                            @click="setShowFlow(nodeIndex)"
                                            >{{ applyData[nodeIndex].showFlow ? '收起' : '展开' }}</Button
                                        >
                                    </div>
                                    <Table
                                        v-if="moduleItem.param_key === 'test_set'"
                                        class="praphy_table"
                                        :columns="graphyColumns"
                                        :data="applyData[nodeIndex].flowTableData"
                                        :border="false"
                                        :height="applyData[nodeIndex].showFlow ? 300 : 40"
                                        @on-row-click="rowClick(nodeIndex)"
                                    />
                                </div>
                                <div
                                    v-if="
                                        item.required &&
                                            ![enumType.Table, enumType.Dragger].includes(moduleItem.param_show_type)
                                    "
                                    class="plan-node-card-param-desc"
                                >
                                    必填
                                </div>
                            </FormItem>
                        </div>
                    </Card>
                </div>
            </div>
            <!-- 其他配置 -->
            <Card class="card_contain" v-for="item in publish_plan_phases" :key="item.phase_name">
                <div slot="title" class="card_header_title">
                    <Icon type="ios-film-outline" size="16"></Icon>
                    <span>{{ item.phase_name }}</span>
                </div>
                <div v-for="(phaseItem, index) in item.nodes" :key="index">
                    <div v-for="paramsItem in phaseItem.node_params" :key="paramsItem.param_key">
                        <FormItem
                            v-if="paramsItem.param_show_flag"
                            :class="{
                                form_item_width: paramsItem.param_show_type !== enumType.Table
                            }"
                            :label="paramsItem.param_cn_name"
                            :prop="paramsItem.param_key"
                        >
                            <Input
                                v-if="paramsItem.param_show_type === enumType.Input"
                                v-model="paramsItem.param_value"
                                :disabled="paramsItem.param_update_disable"
                                placeholder="请输入"
                            ></Input>
                            <Select
                                v-else-if="paramsItem.param_show_type === enumType.Select"
                                v-model="paramsItem.param_value"
                                :disabled="paramsItem.param_update_disable"
                                clearable
                                filterable
                            >
                                <!-- 审核人 -->
                                <template v-if="paramsItem.param_key === 'receiver'">
                                    <Option
                                        v-for="item in optionsList.email_address_options"
                                        :key="item.value"
                                        :value="item.value"
                                        >{{ item.name }}</Option
                                    >
                                </template>
                                <!-- 归档策略 -->
                                <template v-if="paramsItem.param_key === 'archive_strategy'">
                                    <Option
                                        v-for="item in optionsList.tactics_options"
                                        :key="item.value"
                                        :value="item.value"
                                        >{{ item.name }}</Option
                                    >
                                </template>
                            </Select>
                            <DatePicker
                                v-else-if="paramsItem.param_show_type === enumType.DatePicker"
                                class="date_picker_width"
                                v-model="paramsItem.param_value"
                                type="datetime"
                                format="yyyy-MM-dd HH:mm"
                                :disabled="paramsItem.param_update_disable"
                                placeholder="选择时间"
                            ></DatePicker>
                            <div class="dragger_warraper" v-else-if="paramsItem.param_show_type === enumType.Dragger">
                                <ServicePublish :iterationId="pipeline_id"></ServicePublish>
                                <!-- <div class="dragger_search">
                                    <Select
                                        class="dragger_select"
                                        v-model="paramsItem.param_value"
                                        :disabled="paramsItem.param_update_disable"
                                        clearable
                                        filterable
                                        @on-change="changeDrageSuite"
                                    >
                                        <Option
                                            v-for="item in optionsList.suite_by_iter_id_options"
                                            :key="item.value"
                                            :value="item.value"
                                            >{{ item.name }}</Option
                                        >
                                    </Select>
                                    <Button type="primary" @click="saveGroup">保存策略</Button>
                                </div>
                                <draggable
                                    style="margin-top: 10px;"
                                    v-model="dragUpData"
                                    :group="group"
                                    animation="200"
                                    dragClass="dragClass"
                                    ghostClass="ghostClass"
                                    chosenClass="chosenClass"
                                    @add="add"
                                >
                                    <transition-group style="min-height:120px;display: block;">
                                        <div class="item" v-for="item in dragUpData" :key="item.app_name">
                                            {{ item.app_name }}
                                        </div>
                                    </transition-group>
                                </draggable>
                                <div class="itxst">
                                    <div v-for="(drag, dragIndex) in dragData" :key="dragIndex" class="col">
                                        <div class="title">顺序{{ dragIndex + 1 }}</div>
                                        <draggable
                                            v-model="dragData[dragIndex]"
                                            dragClass="dragClass"
                                            ghostClass="ghostClass"
                                            chosenClass="chosenClass"
                                            animation="200"
                                            :group="group"
                                            @add="add"
                                        >
                                            <transition-group :key="dragIndex" style="min-height:120px;display: block;">
                                                <div
                                                    class="item"
                                                    v-for="item in dragData[dragIndex]"
                                                    :key="item.app_name"
                                                >
                                                    {{ item.app_name }}
                                                </div>
                                            </transition-group>
                                        </draggable>
                                    </div>
                                </div> -->
                            </div>
                            <div v-else-if="paramsItem.param_show_type === enumType.Table">
                                <!-- 自动化编排 - 列表 - 服务端需要单独给一个item控制展示 -->
                                <!-- 选择提交SQL -->
                                <Table
                                    v-if="paramsItem.param_key === 'commit_sql'"
                                    :columns="columns"
                                    :data="publishData.sqlData"
                                    :border="false"
                                    @on-selection-change="selectTableHandler"
                                />
                            </div>
                            <div
                                v-if="
                                    item.required &&
                                        ![enumType.Table, enumType.Dragger].includes(paramsItem.param_show_type)
                                "
                                class="plan-node-card-param-desc"
                            >
                                必填
                            </div>
                        </FormItem>
                    </div>
                </div>
            </Card>
        </Form>
    </div>
</template>

<script>
import draggable from 'vuedraggable'
import normal from '../mixins/normal'
import ServicePublish from './service-publish'
export default {
    name: 'iterPublishAllPlanNode',
    components: { draggable, ServicePublish },
    mixins: [normal]
}
</script>

<style lang="less" scoped>
.wrapper {
    position: relative;
    padding-bottom: 10px;
    .card_contain {
        padding: 16px;
        margin-top: 10px;
        .card_header_title {
            display: flex;
            align-items: center;
            padding-bottom: 5px;
        }
        .praphy_table {
            margin-bottom: 24px;
        }
        .form_item_width {
            width: 400px;
            .date_picker_width {
                width: 300px;
            }
            /deep/ .ivu-form-item-content {
                display: flex;
            }
        }
        .tips {
            margin-left: 96px;
            margin-top: -15px;
            color: red;
        }
    }
    .dragger_contain {
        // width: 200px;
        height: 100px;
        display: flex;
        align-items: center;
        flex-direction: column;
    }
    .drag_item {
        cursor: move;
        background-color: antiquewhite;
        margin-bottom: 10px;
        width: 400px;
        text-align: center;
    }
}
.btn_custom {
    display: flex;
    justify-content: end;
    margin-bottom: 10px;
}

.dragger_warraper {
    min-width: 800px;
    overflow: auto;
    .dragger_search {
        display: flex;
    }
    .dragger_select {
        width: 236px;
        margin-right: 10px;
    }
}
.ghostClass {
    background-color: blue !important;
}

.chosenClass {
    background-color: red !important;
    opacity: 1 !important;
}

.dragClass {
    background-color: blueviolet !important;
    opacity: 1 !important;
    box-shadow: none !important;
    outline: none !important;
    background-image: none !important;
}

.itxst {
    margin: 10px;
    display: flex;
}

.title {
    padding: 6px 12px;
}

.col {
    width: 300px;
    padding: 10px;
    border: solid 1px #eee;
    border-radius: 5px;
}

.col + .col {
    margin-left: 10px;
}
.item {
    padding: 6px 12px;
    margin: 0px 10px 0px 10px;
    border: solid 1px #eee;
    background-color: #f1f1f1;
}

.item:hover {
    background-color: #fdfdfd;
    cursor: move;
}

.item + .item {
    border-top: none;
    margin-top: 6px;
}
</style>
<style lang="less">
.my-select {
    .ivu-select-dropdown {
        max-height: 100px; /* 设置下拉选项区域的最大高度为200px */
        overflow-y: auto; /* 当内容超过最大高度时，出现垂直滚动条 */
    }
}
</style>
