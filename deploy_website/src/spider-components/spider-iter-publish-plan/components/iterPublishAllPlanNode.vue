<!--
 * @Author: chao.bao <EMAIL>
 * @Date: 2024-09-10 15:30:47
 * @LastEditors: chao.bao <EMAIL>
 * @LastEditTime: 2024-11-28 09:54:49
 * @FilePath: /website_web/deploy_website/src/spider-components/spider-iter-publish-plan/iterPublishAllPlanNode.vue
 * @Description: 发布全流程model
-->
<template>
    <div class="wrapper">
        <Spin fix v-if="loadding"></Spin>
        <!-- :model="publishData" :rules="ruleValidate" -->
        <Form ref="formValidate" :label-width="labelWidth">
            <Card class="card_contain">
                <div slot="title" class="card_header_title">
                    <Icon type="ios-film-outline" size="16"></Icon>
                    <span>基础</span>
                </div>
                <FormItem class="form_item_width" label="选应用" prop="iter_app_name">
                    <Select v-model="publishData.iter_app_name" @on-change="change_iter_app_name" filterable clearable>
                        <!-- v-for="item in iter_app_options" -->
                        <Option v-for="item in iter_app_options" :key="item.value" :value="item.value">{{
                            item.name
                        }}</Option>
                    </Select>
                    <div class="plan-node-card-param-desc">必填</div>
                </FormItem>
                <div class="tips">{{ submitStatus.msg }}</div>
            </Card>

            <Card class="card_contain" v-for="item in publish_plan_phases" :key="item.phase_name">
                <div slot="title" class="card_header_title">
                    <Icon type="ios-film-outline" size="16"></Icon>
                    <span>{{ item.phase_name }}</span>
                </div>
                <div v-for="(phaseItem, index) in item.nodes" :key="index">
                    <div v-for="paramsItem in phaseItem.node_params" :key="paramsItem.param_key">
                        <FormItem
                            v-if="paramsItem.param_show_flag"
                            :class="{
                                form_item_width: paramsItem.param_show_type !== enumType.Table
                            }"
                            :label="paramsItem.param_cn_name"
                            :prop="paramsItem.param_key"
                        >
                            <Input
                                v-if="paramsItem.param_show_type === enumType.Input"
                                v-model="paramsItem.param_value"
                                :disabled="paramsItem.param_update_disable"
                                placeholder="请输入"
                            ></Input>
                            <Select
                                v-else-if="paramsItem.param_show_type === enumType.Select"
                                v-model="paramsItem.param_value"
                                @on-change="val => selectHandler(val, paramsItem.param_key)"
                                :disabled="paramsItem.param_update_disable"
                                clearable
                                filterable
                            >
                                <!-- 业务分支 -->
                                <template v-if="paramsItem.param_key === 'biz_iter_branch'">
                                    <Option
                                        v-for="item in optionsList.iter_list_options"
                                        :key="item.value"
                                        :value="item.value"
                                        >{{ item.name }}</Option
                                    >
                                </template>
                                <!-- 自动化编排 -->
                                <template v-if="paramsItem.param_key === 'biz_flow_name'">
                                    <Option
                                        v-for="item in optionsList.flow_list_options"
                                        :key="item.value"
                                        :value="item.value"
                                        >{{ item.name }}</Option
                                    >
                                </template>
                                <!-- 选择环境（自动化） -->
                                <template v-if="paramsItem.param_key === 'suite_code'">
                                    <Option
                                        v-for="item in optionsList.fdevelopment_evn_options"
                                        :key="item.value"
                                        :value="item.value"
                                        >{{ item.name }}</Option
                                    >
                                </template>
                                <!-- 审核人 -->
                                <template v-if="paramsItem.param_key === 'receiver'">
                                    <Option
                                        v-for="item in optionsList.email_address_options"
                                        :key="item.value"
                                        :value="item.value"
                                        >{{ item.name }}</Option
                                    >
                                </template>
                                <!-- 归档策略 -->
                                <template v-if="paramsItem.param_key === 'archive_strategy'">
                                    <Option
                                        v-for="item in optionsList.tactics_options"
                                        :key="item.value"
                                        :value="item.value"
                                        >{{ item.name }}</Option
                                    >
                                </template>
                                <!-- 选择环境 - build_suite_code -->
                                <template v-if="paramsItem.param_key === 'build_suite_code'">
                                    <Option
                                        v-for="item in optionsList.build_suite_code_options"
                                        :key="item.value"
                                        :value="item.value"
                                        >{{ item.name }}</Option
                                    >
                                </template>
                            </Select>
                            <DatePicker
                                v-else-if="paramsItem.param_show_type === enumType.DatePicker"
                                class="date_picker_width"
                                v-model="paramsItem.param_value"
                                type="datetime"
                                format="yyyy-MM-dd HH:mm"
                                :disabled="paramsItem.param_update_disable"
                                placeholder="选择时间"
                            ></DatePicker>
                            <draggable
                                v-else-if="paramsItem.param_show_type === enumType.Dragger"
                                class="dragger_contain"
                                v-model="paramsItem.param_value"
                            >
                                <div v-for="item in paramsItem.param_value" :key="item.env" class="drag_item">
                                    {{ item.name.suite_name }}-{{ item.name.node_list }}
                                </div>
                            </draggable>
                            <div v-else-if="paramsItem.param_show_type === enumType.Table">
                                <!-- 自动化编排 - 列表 - 服务端需要单独给一个item控制展示 -->
                                <div v-if="paramsItem.param_key === 'test_set'" class="btn_custom">
                                    <Button type="primary" ghost size="small" @click="branchReset"
                                        >一键指定归档版本</Button
                                    >
                                    <Button
                                        style="margin-left: 10px;"
                                        type="primary"
                                        ghost
                                        size="small"
                                        @click="showFlow = !showFlow"
                                        >{{ showFlow ? '收起' : '展开' }}</Button
                                    >
                                </div>
                                <Table
                                    v-if="paramsItem.param_key === 'test_set'"
                                    class="praphy_table"
                                    :columns="graphyColumns"
                                    :data="publishData.flowTableData"
                                    :border="false"
                                    :height="showFlow ? 300 : 40"
                                />
                                <!-- 选择提交SQL -->
                                <Table
                                    v-if="paramsItem.param_key === 'commit_sql'"
                                    :columns="columns"
                                    :data="publishData.sqlData"
                                    :border="false"
                                    @on-selection-change="selectTableHandler"
                                />
                            </div>
                            <div
                                v-if="
                                    item.required &&
                                        ![enumType.Table, enumType.Dragger].includes(paramsItem.param_show_type)
                                "
                                class="plan-node-card-param-desc"
                            >
                                必填
                            </div>
                        </FormItem>
                    </div>
                </div>
            </Card>
        </Form>
    </div>
</template>

<script>
import draggable from 'vuedraggable'
import express from '../mixins/express'
export default {
    name: 'iterPublishAllPlanNode',
    components: { draggable },
    mixins: [express]
}
</script>

<style lang="less" scoped>
.wrapper {
    position: relative;
    padding-bottom: 10px;
    .card_contain {
        padding: 16px;
        margin-top: 10px;
        .card_header_title {
            display: flex;
            align-items: center;
            padding-bottom: 5px;
        }
        .praphy_table {
            margin-bottom: 24px;
        }
        .form_item_width {
            width: 400px;
            .date_picker_width {
                width: 300px;
            }
            /deep/ .ivu-form-item-content {
                display: flex;
            }
        }
        .tips {
            margin-left: 96px;
            margin-top: -15px;
            color: red;
        }
    }
    .dragger_contain {
        // width: 200px;
        height: 100px;
        display: flex;
        align-items: center;
        flex-direction: column;
    }
    .drag_item {
        cursor: move;
        background-color: antiquewhite;
        margin-bottom: 10px;
        width: 400px;
        text-align: center;
    }
}
.btn_custom {
    display: flex;
    justify-content: end;
    margin-bottom: 10px;
}
</style>
<style lang="less">
.my-select {
    .ivu-select-dropdown {
        max-height: 100px; /* 设置下拉选项区域的最大高度为200px */
        overflow-y: auto; /* 当内容超过最大高度时，出现垂直滚动条 */
    }
}
</style>
