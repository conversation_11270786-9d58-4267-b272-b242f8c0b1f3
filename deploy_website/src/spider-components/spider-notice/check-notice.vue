<template>
  <div >
    <Modal
      :title="check_notice_title"

      width="800"
      v-model="visible"
      :mask-closable="true"
      @on-cancel="Cancel"
    >
      <div>
        <Row >
          <Table
            :no-data-text="check_notice_msg"
            :columns="check_table_columns"
            :data="check_table_data"
          >
          </Table>
        </Row>
      </div>
      <div slot="footer">
        <!--<Button @click="$emit('continut')">继续申请</Button>-->
        <Button @click="Cancel">关闭</Button>
      </div>
    </Modal>
  </div>
</template>


<script>

    export default {
        name: "CheckNotice",
        props: {
            value:{
              type:Boolean,
              defaults:false
            },

            check_notice_title: {
              type: String,
              defaults: ''
            },
            check_notice_msg: {
                type: String,
                defaults: ''
            },
            check_table_data: {
              type: Array,
              defaults: []
            },

    },
        data() {
          return {
              visible: this.value,
              check_table_columns: [
          {
            title: '检查项',
            key: 'msg',
            align: 'center',
          },
          {
            title: '检查是否通过',
            key: 'status',
            align: 'center',
            render: (h, params) => {

              if (params.row.status.indexOf("success") >= 0) {
                var color = "green";
              } else if (params.row.status.indexOf("failure") >= 0) {
                var color = "red";
              }
              return h("div", [
                h("p",
                  {props: {}, style: {color: color}},
                  params.row.status)
              ]);
            }
          }
        ],
          }
        },
      methods: {
        Cancel(){
                this.visible = false
                this.$emit('input', this.visible)

            },
      },
       watch: {
            value (val) {
                this.visible = val;
            },
       }
    }

</script>

<style scoped>

</style>
