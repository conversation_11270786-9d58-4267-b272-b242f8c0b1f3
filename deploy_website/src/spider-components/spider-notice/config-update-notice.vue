<template>
  <div>
    <Modal
        v-model="model_notice"
        title="配置更新提醒"
        @on-ok="Ok"
        @on-cancel="Cancel">
        <p style="color: crimson;font-size: 16px"  >1、宙斯应用外移文件必须更新！请确认已经点过【配置更新】按钮！</p>
        <p style="color: crimson;font-size: 16px"  >2、请确认SQL审核通过，并已产线库执行！</p>
        <div slot="footer">
        <Button @click="Cancel">关闭</Button>
        <Button @click="Ok">确定发布，已经点过【配置更新】按钮！</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
    export default {
        name: "ConfigUpdateNotice",
        // props: {model_notice: {type: Boolean,
        //                 defaults: false}},
        data(){
          return{
            model_notice:false,
            deploy_type: "",
            node_list:[],
            suite_code:"",
          }
        },
        methods:{
               open(deploy_type, node_list, suite_code){
                 this.model_notice = true
                 this.deploy_type = deploy_type
                 this.node_list = node_list
                 this.suite_code = suite_code

               },
               Ok(){
                  this.$emit('prod_node_publish', this.deploy_type, this.node_list, this.suite_code)
                  this.model_notice = false;
                },
            Cancel(){
                this.$emit('reset_loading_btn', this.node_list)
                this.model_notice = false;
            },
        }
    }
</script>

<style scoped>

</style>
