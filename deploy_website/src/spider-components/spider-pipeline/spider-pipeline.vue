<template>
    <Card shadow>
        <Row style="margin-top: 1em">
            <i-col span="2">迭代版本</i-col>
            <i-col span="4">{{ this.pipeline_id }}</i-col>
            <i-col span="2" offset="6">
                <Button
                    ghost
                    type="primary"
                    style="text-align: left; display: inline-block; width:100px;margin-top:-1px"
                    @click="modal_archive_app_info = true"
                    >上线完成版本
                </Button>
            </i-col>
            <i-col span="2" offset="1">
                <Button
                    ghost
                    type="primary"
                    style="text-align: left; display: inline-block; width:100px;margin-top:-1px"
                    @click="modal_onlineing_app_info = true"
                    >正在上线版本
                </Button>
            </i-col>
        </Row>

        <Row style="margin-top: 2em">
            <i-col span="3">变更提交流水线列表</i-col>
            <Button ghost type="success" style="float: left" @click="show_img_modal = true"
                >流水线概念图
                <Icon type="ios-alert-outline" size="15" />
            </Button>
            <!--<Button ghost type="success" style="float: left" @click="batchCompile">批量全流程构建</Button>-->
            <!--<Button  @click="batchCompile">批量全流程mock构建</Button>-->
            <Button style="margin-left: 2ex" type="dashed" @click="show_img_ccn = true"
                >ccn报告字段提示
                <Icon type="ios-alert-outline" size="18" />
            </Button>
            <Button ghost type="success" style="margin-left: 2ex" @click="batchBindEnv">批量绑定环境 </Button>
            <Button
                :disabled="batchPushSelectData.length === 0"
                ghost
                type="primary"
                style="margin-left: 2ex"
                @click="batchPushBag"
                >批量-变更-推包-CI
            </Button>
            <!-- 新增测试左移按钮 暂时先不上线，没测试验证 20250806 by fwm-->
            <!-- <Button
                ghost
                type="warning"
                style="margin-left: 2ex"
                @click="showTestShiftLeftModal"
                >测试左移
            </Button> -->
        </Row>
        <Table
            style="margin-top: 1em"
            border
            width="100%"
            :columns="pipeline_columns"
            :data="pipeline_data"
            @on-selection-change="batchPushSelect"
        ></Table>

        <Row style="margin-top: 2em">
            <i-col span="3">持续集成流水线列表</i-col>
        </Row>
        <Table
            style="margin-top: 1em"
            border
            width="100%"
            :columns="continuous_integration_columns"
            :data="continuous_integration_data"
            @on-selection-change="selectList"
        ></Table>

        <Modal
            title="应用未绑定以下环境套，请到“基础信息管理->节点管理->新增”页绑定"
            v-model="bind_env_fail_modal_show"
            width="560"
            :mask-closable="true"
        >
            <Table :columns="bindEnvFaiColumns" :data="bindEnvFailData" />
            <div slot="footer">
                <Button @click="close_fail_bind_env">关闭</Button>
            </div>
        </Modal>

        <Modal title="绑定环境套" v-model="bind_env_modal_show" width="580" :mask-closable="true">
            <Row style="margin-top: 2em">
                <i-col span="5">测试环境套列表</i-col>
                <input hidden v-model="select_pipeline_name" />
            </Row>
            <Input v-model="table_data_filter" @on-change="table_filters" size="large" placeholder="查询" />
            <Table
                :columns="envtableColumns"
                :data="envtablePageData"
                no-data-text="环境套信息为空"
                ref="bind_env_table"
                @on-select="selectChange"
                @on-select-cancel="selectChangeCancel"
                @on-select-all="selectChangeAll"
                @on-select-all-cancel="selectChangeAllCancel"
            >
            </Table>
            <Page
                class="pag"
                :total="pagination.total"
                :current="pagination.page"
                :page-size="pagination.size"
                show-total
                show-elevator
                show-sizer
                @on-change="pageChange"
                @on-page-size-change="pageSizeChange"
            />
            <div slot="footer">
                <Button @click="save_bind_env">保存</Button>
                <Button @click="cancel_bind_env">关闭</Button>
            </div>
        </Modal>

        <Modal
            title="绑定测试集"
            v-model="bind_testsuit_show"
            width="580"
            :mask-closable="true"
            @on-ok="save_testsuit_id"
        >
            <Row style="margin-top: 2em">
                <i-col span="5">测试集ID</i-col>
                <Input v-model="testsuit_id" size="large" placeholder="请输入测试集ID" />
            </Row>
        </Modal>

        <Modal
            title="绑定sql验证环境"
            v-model="bind_sql_check_suite_show"
            width="580"
            :mask-closable="true"
            @on-ok="save_sql_check_suite"
        >
            <Select v-model="app_sql_check_suite_code" style="width: 100px" clearable filterable>
                <Option v-for="item in suite_code_list" :value="item" :key="item">{{ item }}</Option>
            </Select>
            <i-col>
                说明：mysql数据库的应用需要设置一次sql验证环境，流水线对提交的sql在设置的测试环境对应的数据库上进行验证，默认选择it999环境！
            </i-col>
        </Modal>

        <Modal
            title="增强CI测试"
            v-model="strengthen_CI_show"
            width="580"
            :mask-closable="true"
            @on-ok="beginStrengthenContinuousIntegration"
        >
            <!-- <Row style="margin-top: 2em">
                <Checkbox v-model="jacoco_type">Jacoco</Checkbox>
                <Checkbox v-model="mock_type" @on-change="show_mock_agent_version">Mock</Checkbox>
                <Checkbox v-model="arex_type">Arex</Checkbox>
                <Checkbox v-model="pinpoint_type">Pinpoint</Checkbox>
            </Row> -->
            <div style="height: 10px;"></div>
            <AgentComponent ref="agentRefThree" />
            <!-- <Row style="margin-top: 2em">
                <RadioGroup v-model="select_mock_agent_version" v-show="mock_agent_version_modal_show">
                    <Radio v-for="item in mock_agent_version_list" :key="item.message" :label="item.version">
                        {{ item.version }}({{ item.message }})
                    </Radio>
                </RadioGroup> -->
            </Row>
        </Modal>

        <Modal width="560" v-model="modal_compile" title="流水线执行概况">
            <Table border :columns="compile_columns" :data="compile_data"></Table>
        </Modal>

        <Modal
            width="675"
            v-model="modal_archive_app_info"
            title="最近一次上线完成的迭代信息"
            @on-visible-change="get_app_latest_archive_info"
        >
            <Table border :columns="archive_app_columns" :data="archive_app_data"></Table>
        </Modal>

        <Modal
            width="515"
            v-model="modal_onlineing_app_info"
            title="正在上线中的迭代信息"
            @on-visible-change="get_app_onlineing_app_info"
        >
            <Table border :columns="onlineing_app_columns" :data="onlineing_app_data"></Table>
        </Modal>
        <Modal
            :title="'发布风险[' + current_entrance_guard_check_app + ']'"
            v-model="entrance_guard_show"
            :closable="false"
            :mask-closable="true"
            width="700"
            class="entrance_guard_show_class"
            :styles="{ top: '20px' }"
        >
            <Tabs size="small" class="risk_tab_class" @on-click="changeRiskTab" v-if="entrance_guard_show">
                <TabPane
                    v-for="item in current_risk_detail"
                    style="margin-bottom: 10px;"
                    :label="item.check_type_show_desc"
                >
                    <Table
                        v-if="item.result_show_type === 'table'"
                        border
                        width="100%"
                        :columns="risk_columns"
                        :data="current_page_risk_detail"
                    ></Table>
                    <div v-if="item.result_show_type === 'table'" style="margin: 6px 2px 2px 2px;overflow: hidden">
                        <div style="float: right;" class="pagination_class">
                            <Page
                                size="small"
                                :total="item.risk_detail.length"
                                :current="current_risk_page"
                                @on-change="changePage"
                            ></Page>
                        </div>
                    </div>
                    <span v-if="item.result_show_type === 'text'"> {{ item.risk_detail }}</span>
                </TabPane>
            </Tabs>
            <div slot="footer">
                <Button
                    @click="
                        () => {
                            this.entrance_guard_show = false
                        }
                    "
                    >关闭
                </Button>
            </div>
        </Modal>
        <Modal title="变更推包配置面板" v-model="select_env_modal_show" width="580" :mask-closable="true">
            CI流水线暂时只支持推单个环境， 请选择一个环境套进行部署！！！
            <Divider size="small" v-if="is_qa">测试配置</Divider>
            <Row v-if="is_qa" style="margin-left: 5px">
                <i-col style="margin: 5px" span="5">
                    <i-col style="margin: 5px" span="1">
                        <Icon type="ios-list-box" style="margin-right: 8px" />
                    </i-col>
                    <i-col style="margin: 5px" span="4">
                        <span style="text-align: left; display: inline-block; width:100px;">测试结束时间:</span>
                    </i-col>
                </i-col>
                <i-col style="margin: 5px" span="5">
                    <DatePicker
                        type="date"
                        placeholder="Select date"
                        format="yyyy-MM-dd"
                        v-model="test_end_date"
                    ></DatePicker>
                </i-col>
            </Row>
            <Divider size="small">部署环境</Divider>
            <Row style="margin-top: 2em">
                <RadioGroup v-model="select_env" @on-change="handleRadioChange">
                    <Radio v-for="item in select_env_list" :label="item"> </Radio>
                </RadioGroup>
            </Row>
            <Divider size="small">应用agent挂载</Divider>
            <!-- <Row style="margin-top: 2em"
                >sharding_agent开关：
                <RadioGroup v-model="is_sharding_agent">
                    <Radio :label="1">是 </Radio>
                    <Radio :label="0">否 </Radio>
                </RadioGroup>
            </Row> -->
            <!-- <Row style="margin-top: 2em"
                >mock-agent开关：
                <RadioGroup v-model="is_mock_agent">
                    <Radio :label="1">是 </Radio>
                    <Radio :label="0">否 </Radio>
                </RadioGroup>
            </Row>
            <Row style="margin-top: 2em"
                >jacoco-agent开关：
                <RadioGroup v-model="is_jacoco_agent">
                    <Radio :label="1">是 </Radio>
                    <Radio :label="0">否 </Radio>
                </RadioGroup>
            </Row>
            <Row style="margin-top: 2em"
                >arex-agent开关：
                <RadioGroup v-model="is_arex_agent">
                    <Radio :label="1">是 </Radio>
                    <Radio :label="0">否 </Radio>
                </RadioGroup>
            </Row>
            <Row style="margin-top: 2em"
                >pinpoint-agent开关：
                <RadioGroup v-model="is_pinpoint_agent">
                    <Radio :label="1">是 </Radio>
                    <Radio :label="0">否 </Radio>
                </RadioGroup>
            </Row> -->
            <AgentComponent ref="agentRefOne" />
            <Divider size="small">应用数据库部署</Divider>
            <Row style="margin-top: 2em"
                >数据库：
                <span style="text-align: left; display: inline-block;">{{ db_info_list_str }}</span>
            </Row>
            <Row style="margin-top: 2em"
                >初始化数据库(谨慎操作，此选项会恢复数据库！)【feature_sql】：
                <RadioGroup v-model="is_init">
                    <Radio :label="1">是 </Radio>
                    <Radio :label="0">否 </Radio>
                </RadioGroup>
            </Row>
            <Row style="margin-top: 2em"
                >增量执行最新归档sql&当前迭代sql【feature_sql】：
                <RadioGroup v-model="is_increment">
                    <Radio :label="1">是 </Radio>
                    <Radio :label="0">否 </Radio>
                </RadioGroup>
            </Row>
            <Row style="margin-top: 2em"
                >业务类型：
                <Select v-model="biz_code" style="width: 200px" clearable @on-change="getBaseDbInfo">
                    <Option
                        v-for="item in biz_name_list"
                        :value="item.biz_code"
                        :key="item.biz_code"
                        :label="item.biz_name"
                    >
                        {{ item.biz_name }}
                    </Option>
                </Select>
            </Row>
            <Row style="margin-top: 2em"
                >业务基础库集【testing_sql】：
                <span style="text-align: left; display: inline-block;">{{ biz_base_db }}</span>
            </Row>
            <div slot="footer">
                <Button @click="submitChangeSubmitAndContinuousIntegration">确定</Button>
                <Button @click="cancelChangeSubmitAndContinuousIntegration">关闭</Button>
            </div>
        </Modal>
        <Modal title="" v-model="mock_select_env_modal_show" width="580" :mask-closable="true" @on-ok="submitMockBuild">
            <Row style="margin-top: 2em">
                <RadioGroup v-model="mock_select_env">
                    <Radio v-for="item in mock_select_env_list" :label="item"> </Radio>
                </RadioGroup>
            </Row>
            <Row style="margin-top: 2em">
                <i-col span="15">说明：CI流水线暂时只支持推单个环境， 请选择一个环境套进行部署</i-col>
            </Row>
        </Modal>
        <Modal title=" " width="1000" v-model="show_img_modal" :mask-closable="true">
            <div style="width: 120%">
                <img :src="imgUrl" style="width: 900px" />
            </div>
        </Modal>
        <Modal title=" " width="1000" v-model="show_img_ccn" :mask-closable="true">
            <div style="width: 120%">
                <img :src="imgUrl1" style="width: 900px" />
            </div>
        </Modal>
        <Row style="margin-top: 2em">
            <i-col span="15">备注：</i-col>
            <i-col span="15">变更提交：完整构建并推送制品库，详情请查看流水线概念图。</i-col>
            <i-col span="15">
                变更-推包-CI：完整构建推送制品库，并自动推送到测试环境（需要提前给流水线做测试环境绑定），详情请查看流水线概念图。
            </i-col>
            <i-col span="15">推包-CI：自动推送制品到测试环境并启动（需要提前给流水线做测试环境绑定）。</i-col>
            <i-col span="15"
                >推包-CI-增强：自动推送制品到测试环境并可选择带测试增强工具启动（需要提前给流水线做测试环境绑定）。
            </i-col>
            <i-col span="15">详情：跳转Jenkins Blueocean详情页</i-col>
        </Row>
        <Drawer
            title="编译列表"
            :mask-closable="false"
            v-model="drawer_compile"
            :closable="false"
            placement="right"
            width="460px"
        >
            <slot>
                <Tree ref="compile_list" :data="compile_list" show-checkbox multiple></Tree>
                <Button type="success" @click="startCustomCompile" style="margin-top: 2em; margin-right: 1em"
                    >开始构建
                </Button>
                <Button type="error" @click="closeDrawer" style="margin-top: 2em;">取消 </Button>
            </slot>
        </Drawer>
        <!-- 批量变更-推包-CI -->
        <Modal title="批量变更-推包-CI" v-model="batchPushModal" :width="800" :mask-closable="false">
            <div v-if="batchPushModal">
                <Table border :columns="batchPushColumns" :height="300" :data="batchPushData"></Table>
                <div style="height: 10px;"></div>
                <AgentComponent ref="agentRefTwo" />
            </div>
            <div slot="footer">
                <Button type="text" @click="batchPushModal = false">取消</Button>
                <Button type="primary" @click="pushConfirm" :loading="loadingBtn">确定</Button>
            </div>
        </Modal>
        
        <!-- 测试左移Modal组件 -->
        <TestShiftLeftModal 
            ref="testShiftLeftModal" 
            @success="handleTestShiftLeftSuccess"
        />
    </Card>
</template>

<script>
import { getGitDiff } from '@/api/iterative-plan'
import { getLibUrlApi } from '@/spider-api/lib-repo-mgt'
import store from '@/spider-store'
import {
    checkAppSqlCheckSuiteApi,
    getAgentVersionInfo,
    getAppSqlCheckSuiteCode,
    getAppVersionInfo,
    getTestSuiteCode,
    saveSqlCheckSuite
} from '@/api/data'
import {
    doCustomCompile,
    doEnvBind,
    doJenkinsCompileJob,
    get_app_db_info,
    getAppLatestArchiveInfo,
    getAppOnlineingAppInfo,
    getEnvBindInfo,
    getPipelineInfo,
    parseCompileApp,
    pushTestEnv,
    saveOrUpdateBatchEnvBind,
    saveOrUpdateEnvAppAgentBind,
    saveOrUpdateEnvBind,
    saveOrUpdateEnvTestsuiteBind,
    getAppSuiteList,
    saveBatchComPile,
    getAgentUseInfoRequest
} from '@/spider-api/pipeline'

import { getIterLockInfoApi, getIterTestEndDateApi, updateIterTestEndDateApi } from '@/spider-api/mgt-iter'
import { createPipeLineLog } from '@/spider-api/pipeline-log'
import { getIsQaApi } from '@/spider-api/user-info'
import { getAgentUseInfo } from '@/spider-api/mgt-env'
import { get_base_db_bind, getBisNameLIstInfo } from '@/spider-api/biz-mgt'
import Vue from 'vue'
import AgentComponent from '@/spider-components/AgentComponent/index.vue'
import TestShiftLeftModal from '@/spider-components/test-shift-left-modal/TestShiftLeftModal.vue'

Vue.component('button-with-dot', {
    render: function(createElement) {
        return createElement(
            'button',
            {
                style: {
                    position: 'relative'
                },
                on: {
                    click: event => {
                        this.$emit('buttonClicked', event.target.title)
                    }
                }
            },
            ['发布风险', createElement('span', {})]
        )
    }
})

export default {
    name: 'SpiderPipeline',
    components: {
        AgentComponent,
        TestShiftLeftModal
    },
    data() {
        return {
            pagination: {
                page: 1,
                size: 10,
                total: 0
            },
            is_junit_dict: {},
            tempArr: [],
            test_end_date: '',
            db_info_list_str: '',
            table_data_filter: '',
            suite_code_filters: [],
            container_filters: [],
            is_qa: false,
            pipeline_id: '',
            show_img_modal: false,
            show_img_ccn: false,
            modal_archive_app_info: false,
            modal_onlineing_app_info: false,
            imgUrl: require('../../img/流水线概念图.png'),
            imgUrl1: require('../../img/ccn字段提示图.png'),
            select_env: '',
            is_mock_agent: '',
            is_jacoco_agent: '',
            is_sharding_agent: '',
            is_arex_agent: '',
            is_pinpoint_agent: '',
            is_init: '',
            is_increment: '',
            biz_base_db: 0,
            biz_code: '',
            biz_name_list: '',
            sharding_agent_show: false,
            collect_agent_show: false,
            pre_entrance_guard_show: false,
            entrance_guard_show: false,
            current_entrance_guard_check_app: '',
            current_no_api_count: 1000,
            current_risk_page: 1,
            is_collect_agent: 1,
            select_param: '',
            select_env_list: [],
            select_env_modal_show: false,
            mock_select_env: '',
            mock_select_param: '',
            mock_select_env_list: [],
            mock_select_env_modal_show: false,
            select_pipeline_name: '',
            bind_env_modal_show: false,
            bind_env_fail_modal_show: false,
            bind_testsuit_show: false,
            strengthen_CI_show: false,
            bind_sql_check_suite_show: false,
            app_sql_check_suite_code: '',
            suite_code_list: [],
            jacoco_type: 0,
            // sharding_type: 0,
            mock_type: 0,
            arex_type: 0,
            pinpoint_type: 0,
            increment_type: 0,
            init_type: 0,
            mock_agent_version_modal_show: false,
            select_mock_agent_version: '',
            mock_agent_version_list: [],
            testsuit_id: '',
            rowdata: '',
            modal_compile: false,
            drawer_compile: false,
            compile_lock_status: false,
            custom_job_name: '',
            pipeline_data: [],
            radio_app_name: '',
            continuous_integration_data: [],
            switch_pipeline_info: '',
            selectApps: [],
            compile_list: [],
            compile_data: [],
            envtableData: [],
            envtablePageData: [],
            bindEnvFailData: [],
            no_agent_api: false,
            entrance_guard_warn: false,
            current_risk_detail: [],
            current_page_risk_detail: [],
            current_risk_tab: 0,
            risk_columns: [
                {
                    title: '风险项',
                    key: 'risk_item'
                },
                {
                    title: '警告内容',
                    key: 'warn_content'
                }
            ],
            archive_app_columns: [
                {
                    title: '迭代名称',
                    key: 'pipeline_id',
                    width: 160
                },
                {
                    title: '分支名称',
                    key: 'br_name',
                    width: 160
                },
                {
                    title: '应用名称',
                    key: 'appName',
                    width: 160
                },
                {
                    title: '最近上线完成时间',
                    key: 'br_end_date',
                    width: 160
                }
            ],
            archive_app_data: [],
            onlineing_app_columns: [
                {
                    title: '迭代名称',
                    key: 'pipeline_id',
                    width: 160
                },
                {
                    title: '分支名称',
                    key: 'br_name',
                    width: 160
                },
                {
                    title: '应用名称',
                    key: 'appName',
                    width: 160
                }
            ],
            onlineing_app_data: [],
            bindEnvFaiColumns: [
                {
                    title: '应用',
                    key: 'app_name',
                    width: 240
                },
                {
                    title: '环境',
                    key: 'env',
                    width: 300
                }
            ],
            envtableColumns: [
                {
                    title: '环境套名',
                    key: 'suite_code',
                    width: 160
                },
                {
                    title: '内含虚机列表',
                    key: 'node',
                    width: 160
                },
                {
                    title: '对应的K8S环境',
                    key: 'container',
                    width: 160
                },
                {
                    type: 'selection',
                    width: 50,
                    align: 'center'
                }
            ],
            compile_columns: [
                {
                    title: '流水线名称',
                    key: 'job_name',
                    width: 240
                },
                {
                    title: '详情',
                    key: 'msg',
                    width: 260,
                    render: (h, params) => {
                        if (params.row.result === 'success') {
                            var color = 'green'
                        } else {
                            var color = 'red'
                        }
                        return h('div', [
                            h(
                                'p',
                                {
                                    props: {},
                                    style: {
                                        color: color
                                    }
                                },
                                params.row.msg
                            )
                        ])
                    }
                }
            ],
            pipeline_columns: [
                {
                    type: 'selection',
                    width: 60,
                    align: 'center'
                },
                {
                    title: '流水线名称',
                    key: 'job_name',
                    width: 200
                },
                {
                    title: '最新执行信息',
                    key: 'last_time',
                    width: 150,
                    align: 'center'
                },
                {
                    title: '执行人',
                    key: 'operator',
                    width: 100,
                    align: 'center'
                },

                {
                    title: '状态',
                    width: 100,
                    align: 'center',
                    render: (h, params) => {
                        let color = ''
                        if (params.row.status === 'SUCCESS') {
                            color = 'green'
                        } else if (params.row.status === 'FALIED') {
                            color = 'red'
                        } else if (params.row.status === 'RUNNING') {
                            color = '#ff9900'
                        }
                        return h(
                            'div',
                            {
                                style: {
                                    color: color
                                },
                                domProps: {
                                    innerHTML: params.row.status
                                }
                            },
                            ''
                        )
                    }
                },
                {
                    title: '环境套',
                    key: 'env_list',
                    width: 130,
                    align: 'center'
                },
                {
                    title: '操作',
                    align: 'center',
                    className: 'ciOperation',
                    render: (h, params) => {
                        return h('div', [
                            h(
                                'Button',
                                {
                                    props: {
                                        size: 'small',
                                        disabled: this.compile_lock_status
                                    },
                                    style: { marginTop: '0.5em' },

                                    on: {
                                        click: () => {
                                            this.rowdata = params.row
                                            checkAppSqlCheckSuiteApi(params.row.app_name).then(res => {
                                                if (res.data.status === 'success') {
                                                    if (res.data.data) {
                                                        this.beginChangeSubmit(params.row)
                                                        let job_name = params.row.job_name
                                                        let opt_desc = job_name + "'变更提交'流水线"
                                                        let pipeline_id = params.row.pipeline_id
                                                        createPipeLineLog(opt_desc, pipeline_id, job_name)
                                                            .then(res => {})
                                                            .catch(err => {
                                                                this.$Message.error(err.response.data.msg)
                                                            })
                                                    } else {
                                                        this.showSqlCheckSuiteModal(params.row.app_name)
                                                    }
                                                } else {
                                                    alert('系统异常！')
                                                }
                                            })
                                        }
                                    }
                                },
                                '变更提交'
                            ),
                            h(
                                'Button',
                                {
                                    props: {
                                        size: 'small',
                                        disabled: this.compile_lock_status
                                    },
                                    style: { marginTop: '0.5em' },
                                    on: {
                                        click: () => {
                                            this.radio_app_name = params.row.app_name
                                            checkAppSqlCheckSuiteApi(params.row.app_name).then(res => {
                                                if (res.data.status === 'success') {
                                                    if (res.data.data) {
                                                        this.beginChangeSubmitAndContinuousIntegration(params.row)
                                                        this.getBisNameInfo()
                                                        this.getAppDbInfo(params.row.app_name)
                                                    } else {
                                                        this.showSqlCheckSuiteModal(params.row.app_name)
                                                    }
                                                } else {
                                                    alert('系统异常！')
                                                }
                                            })
                                        }
                                    }
                                },
                                '变更-推包-CI'
                            ),
                            h(
                                'Button',
                                {
                                    attrs: {
                                        class: 'ivu-btn ivu-btn-success ivu-btn-ghost ivu-btn-small',
                                        title: '指定测试节点'
                                    },
                                    props: { size: 'small' },
                                    style: { marginTop: '0.5em' },
                                    on: {
                                        click: () => {
                                            this.$refs.bind_env_table.selectAll(false)
                                            this.select_pipeline_name = params.row.job_name
                                            if (params.row.env_list != null && params.row.env_list.length > 0) {
                                                this.tempArr = params.row.env_list.split(',')
                                            }
                                            this.envtableData.sort(function(a, b) {
                                                return b.suite_id - a.suite_id
                                            })
                                            this.sortToSelectToUnshift()
                                            this.getEnvData()
                                            this.init_select_env()
                                            this.bind_env_modal_show = true
                                        }
                                    }
                                },
                                '绑定'
                            ),
                            h(
                                'Button',
                                {
                                    attrs: {
                                        class: 'ivu-btn  ivu-btn-primary ivu-btn-ghost ivu-btn-small',
                                        title: '构建详情BlueOcean'
                                    },
                                    props: { size: 'small' },
                                    style: { marginTop: '0.5em' },
                                    on: {
                                        click: () => {
                                            window.open(this.pipeline_data[params.index].jenkins_job_url)
                                        }
                                    }
                                },
                                '详情'
                            ),
                            h(
                                'Button',
                                {
                                    attrs: {
                                        class: 'ivu-btn  ivu-btn-primary ivu-btn-ghost ivu-btn-small',
                                        title: 'sonar'
                                    },
                                    props: { size: 'small' },
                                    style: { marginTop: '0.5em' },
                                    on: {
                                        click: () => {
                                            this.openLibUrl(params.row.app_name, 'sonar', '')
                                        }
                                    }
                                },
                                'sonar'
                            ),
                            h(
                                'Poptip',
                                {
                                    attrs: {
                                        class: 'ivu-btn  ivu-btn-primary ivu-btn-ghost ivu-btn-small',
                                        title: 'ccn'
                                    },
                                    props: {
                                        confirm: true,
                                        transfer: true,
                                        title: '下载吗?',
                                        type: 'error',
                                        size: 'small'
                                    },
                                    style: { marginTop: '0.5em' },
                                    on: {
                                        'on-ok': () => {
                                            this.openLibUrl(params.row.app_name, 'ccn', '')
                                        }
                                    }
                                },
                                'ccn'
                            ),
                            h(
                                'Poptip',
                                {
                                    attrs: {
                                        class: 'ivu-btn  ivu-btn-primary ivu-btn-ghost ivu-btn-small',
                                        title: 'p3c'
                                    },
                                    props: {
                                        confirm: true,
                                        transfer: true,
                                        title: '下载吗?',
                                        type: 'error',
                                        size: 'small'
                                    },
                                    style: { marginTop: '0.5em' },
                                    on: {
                                        'on-ok': () => {
                                            this.openLibUrl(params.row.app_name, 'p3c', '')
                                        }
                                    }
                                },
                                'p3c'
                            ),
                            h(
                                'Button',
                                {
                                    attrs: {
                                        class: 'ivu-btn  ivu-btn-primary ivu-btn-ghost ivu-btn-small',
                                        title: 'apidoc'
                                    },
                                    props: { size: 'small' },
                                    style: {
                                        marginTop: '0.5em'
                                    },
                                    on: {
                                        click: () => {
                                            this.openApiDocUrl(
                                                params.row.app_name,
                                                params.row.pipeline_id.split('_')[1]
                                            )
                                        }
                                    }
                                },
                                'apidoc'
                            ),
                            h(
                                'Button',
                                {
                                    attrs: {
                                        class: 'ivu-btn  ivu-btn-primary ivu-btn-ghost ivu-btn-small',
                                        title: 'sqlcheck'
                                    },
                                    props: { size: 'small' },
                                    style: {
                                        marginTop: '0.5em'
                                    },
                                    on: {
                                        click: () => {
                                            this.rowdata = params.row
                                            this.showSqlCheckSuiteModal(params.row.app_name)
                                        }
                                    }
                                },
                                'sql验证'
                            ),
                            h('button-with-dot', {
                                attrs: {
                                    class:
                                        'ivu-btn ivu-btn-ghost ivu-btn-small ' +
                                        (params.row.risk_detail.risk_level == 'red'
                                            ? 'entrance_guard_blocked ivu-btn-error'
                                            : params.row.risk_detail.risk_level == 'yellow'
                                            ? 'entrance_guard_warn ivu-btn-warning'
                                            : 'entrance_guard_normal ivu-btn-success'),
                                    title: params.row.app_name
                                },
                                props: { size: 'small', app_name: params.row.app_name },
                                style: {
                                    marginTop: '0.5em'
                                },
                                on: {
                                    buttonClicked: app_name => {
                                        this.current_entrance_guard_check_app = app_name
                                        this.current_risk_detail = params.row.risk_detail.risk_infos

                                        this.current_risk_detail.forEach(function(item) {
                                            item.check_type_show_desc = h => {
                                                return h('div', [
                                                    h(
                                                        'span',
                                                        {
                                                            style: {
                                                                position: 'relative',
                                                                display: 'inline-block'
                                                            }
                                                        },
                                                        [
                                                            item.check_type_desc,
                                                            h('span', {
                                                                style:
                                                                    item.risk_level != 'green'
                                                                        ? {
                                                                              position: 'absolute',
                                                                              top: '-2px',
                                                                              right: '-10px',
                                                                              width: '8px',
                                                                              height: '8px',
                                                                              backgroundColor: item.risk_level,
                                                                              borderRadius: '50%',
                                                                              animation: 'pulse 2s infinite'
                                                                          }
                                                                        : {
                                                                              position: 'absolute',
                                                                              top: '-2px',
                                                                              right: '-10px',
                                                                              width: '8px',
                                                                              height: '8px',
                                                                              backgroundColor: 'white',
                                                                              borderRadius: '50%'
                                                                          }
                                                            })
                                                        ]
                                                    )
                                                ])
                                            }
                                        })
                                        console.log('row,%o', params.row)
                                        console.log('current_risk_detail,%o', this.current_risk_detail)
                                        this.entrance_guard_show = true
                                        this.entrance_guard_warn = !!params.row.entrance_guard_warn
                                        let _this = this
                                        setTimeout(() => {
                                            _this.changeRiskTab(0)
                                        }, 100)
                                    }
                                }
                            })
                        ])
                    }
                }
            ],
            continuous_integration_columns: [
                {
                    title: '应用名',
                    key: 'app_name',
                    align: 'center',
                    width: 200
                },
                {
                    title: '环境套',
                    key: 'env',
                    width: 150,
                    align: 'center'
                },
                {
                    title: '节点/容器',
                    key: 'node',
                    width: 200,
                    align: 'center'
                },
                {
                    title: '测试集ID',
                    key: 'test_set_id',
                    width: 100,
                    align: 'center'
                },
                {
                    title: '操作',
                    align: 'center',
                    render: (h, params) => {
                        return h('div', [
                            h(
                                'Button',
                                {
                                    props: { size: 'small' },
                                    style: {
                                        marginRight: '1em'
                                    },
                                    on: {
                                        click: () => {
                                            this.rowdata = params.row
                                            this.testsuit_id = params.row.test_set_id
                                            this.bind_testsuit_show = true
                                        }
                                    }
                                },
                                '绑定'
                            ),
                            // h(
                            //   'Button',
                            //   {
                            //     props: {size: 'small',},
                            //     style: {
                            //       marginRight: '1em'
                            //     },
                            //     on: {
                            //       click: () => {
                            //         let jacoco_type = 0
                            //         let mock_type = 0
                            //         this.beginContinuousIntegration(params.row, jacoco_type, mock_type)
                            //         /* this.jenkinsCompileExec(
                            //              this.pipeline_data[params.index]["job_name"], this.pipeline_data[params.index]["need_mock"], 1, 0
                            //            ); */
                            //         let job_name = this.pipeline_id + '_' + params.row.app_name
                            //         let opt_desc = job_name + "'推包-CI'流水线" + '(' + params.row.env + ')'
                            //         let pipeline_id = this.pipeline_id
                            //         createPipeLineLog(opt_desc, pipeline_id, job_name).then(res => {
                            //         }).catch(err => {
                            //           this.$Message.error(err.response.data.msg)
                            //         })
                            //       }
                            //     }
                            //   },
                            //   '推包-CI'
                            // ),
                            h(
                                'Button',
                                {
                                    props: { size: 'small' },
                                    style: {
                                        marginRight: '1em'
                                    },
                                    on: {
                                        click: () => {
                                            getAgentUseInfo(params.row.env, params.row.app_name).then(res => {
                                                let agent_name = 'howbuy-mock-agent'
                                                let agent_use_info_list = res.data.data
                                                for (let i = 0; i < agent_use_info_list.length; i++) {
                                                    if (
                                                        agent_use_info_list[i].agent_name ===
                                                        'howbuy-interface-scan-agent'
                                                    ) {
                                                        // this.collect_agent_show = true
                                                        this.is_collect_agent = parseInt(agent_use_info_list[i].is_use)
                                                    }
                                                    // else if (
                                                    //     agent_use_info_list[i].agent_name === 'shardingsphere-agent'
                                                    // ) {
                                                    //     // this.sharding_agent_show = true
                                                    //     this.is_sharding_agent = parseInt(agent_use_info_list[i].is_use)
                                                    //     this.sharding_type = Boolean(this.is_sharding_agent)
                                                    // }
                                                    else if (agent_use_info_list[i].agent_name === 'JaCoCo') {
                                                        this.is_jacoco_agent = parseInt(agent_use_info_list[i].is_use)
                                                        this.jacoco_type = Boolean(this.is_jacoco_agent)
                                                    } else if (
                                                        agent_use_info_list[i].agent_name === 'howbuy-mock-agent'
                                                    ) {
                                                        this.is_mock_agent = parseInt(agent_use_info_list[i].is_use)
                                                        this.mock_type = Boolean(this.is_mock_agent)
                                                    } else if (agent_use_info_list[i].agent_name === 'arex-agent') {
                                                        this.is_arex_agent = parseInt(agent_use_info_list[i].is_use)
                                                        this.arex_type = Boolean(this.is_arex_agent)
                                                    } else if (agent_use_info_list[i].agent_name === 'pinpoint-agent') {
                                                        this.is_pinpoint_agent = parseInt(agent_use_info_list[i].is_use)
                                                        this.pinpoint_type = Boolean(this.is_pinpoint_agent)
                                                    }
                                                }
                                                if (this.mock_type === true) {
                                                    this.mock_agent_version_modal_show = true
                                                } else {
                                                    this.mock_agent_version_modal_show = false
                                                }
                                                getAgentVersionInfo(agent_name).then(res => {
                                                    console.log('res.data======' + res.data.data[0].message)
                                                    this.mock_agent_version_list = res.data.data
                                                    // 默认选中已归档的版本
                                                    this.select_mock_agent_version = res.data.data[0].version
                                                })
                                            })
                                            this.rowdata = params.row
                                            this.strengthen_CI_show = true
                                            this.$refs.agentRefThree.queryList([params.row.app_name], [params.row.env])
                                        }
                                    }
                                },
                                '推包-CI-增强'
                            ),
                            h(
                                'Button',
                                {
                                    attrs: {
                                        class: 'ivu-btn ivu-btn-primary ivu-btn-ghost ivu-btn-small'
                                    },
                                    props: { size: 'small' },
                                    style: {
                                        marginRight: ''
                                    },
                                    on: {
                                        click: () => {
                                            window.open(params.row.job_url)
                                        }
                                    }
                                },
                                '详情'
                            )
                        ])
                    }
                }
            ],
            index: '',
            email_modal: false,
            finish_modal: false,
            mail_content: '',
            stage: '',
            sql_content: '',
            sql_status: '',
            date: '',
            time: '',
            modal_git: false,
            modal_config: false,
            m_app_config: '',
            m_git_diff: '',
            app_detail: [],
            release_notice: '',
            release_description: '',
            allMails: [],
            allSelectedMails: [],
            allFilterMails: [],
            finish_table_columns: [
                {
                    title: '应用名',
                    key: 'app_name'
                },
                {
                    title: 'IP地址',
                    key: 'ip'
                },
                {
                    title: '已发布版本',
                    key: 'pro_version',
                    width: 300
                },
                {
                    title: '制品库版本',
                    key: 'git_version',
                    width: 300
                },
                {
                    title: '版本比较',
                    key: 'status',
                    render: (h, params) => {
                        let tag_color = ''
                        let tag_text = ''

                        switch (params.row.status) {
                            case 0:
                                tag_color = 'success'
                                tag_text = '一致'
                                break
                            case 1:
                                tag_color = 'error'
                                tag_text = '不一致'
                                break
                            case 2:
                                tag_color = 'warning'
                                tag_text = '未发布'
                                break
                        }

                        return h(
                            'Tag',
                            {
                                props: {
                                    color: tag_color
                                }
                            },
                            tag_text
                        )
                    }
                }
            ],
            finish_table_data: [],
            app_columns: [
                {
                    title: '上线应用',
                    key: 'app_name',
                    width: 220,
                    render: (h, params) => {
                        if (params.row.app_stat === 'UAT') {
                            var app_color = '#515a6e'
                        } else {
                            var app_color = 'green'
                        }
                        return h('div', [
                            h(
                                'p',
                                {
                                    style: {
                                        color: app_color
                                    }
                                },
                                params.row.app_name
                            )
                        ])
                    }
                },
                {
                    title: '配置变更',
                    key: 'app_config',
                    align: 'left',
                    width: 110,
                    render: (h, params) => {
                        if (params.row.app_config) {
                            var show_color = 'orange'
                            var show_logo = 'ivu-icon ivu-icon-ios-create'
                        } else {
                            var show_color = '#515a6e'
                            var show_logo = 'ivu-icon ivu-icon-ios-create-outline'
                        }
                        return h('div', [
                            h(
                                'a',
                                {
                                    props: {
                                        size: 'small'
                                    },
                                    style: {
                                        color: show_color
                                    },
                                    on: {
                                        click: () => {
                                            this.showAppConfig(params.index)
                                        }
                                    }
                                },
                                '配置信息'
                            ),
                            h(
                                'a',
                                {
                                    attrs: {
                                        class: show_logo
                                    },
                                    style: {
                                        'font-size': '20px',
                                        'margin-left': '5px',
                                        'margin-top': '-4px'
                                    },
                                    on: {
                                        click: () => {
                                            this.showAppConfig(params.index)
                                        }
                                    }
                                },
                                ''
                            )
                        ])
                    }
                },
                {
                    title: 'git版本',
                    key: 'app_git',
                    align: 'left',
                    width: 320
                },
                {
                    title: '版本差异',
                    align: 'left',
                    render: (h, params) => {
                        return h('div', [
                            h(
                                'Button',
                                {
                                    props: {
                                        size: 'small'
                                    },
                                    style: {
                                        // marginRight: '5px'
                                    },
                                    on: {
                                        click: () => {
                                            this.showGitDiff(params.index)
                                        }
                                    }
                                },
                                '详细信息'
                            )
                        ])
                    }
                }
            ],
            // ---------------批量变更-推包-CI
            batchPushModal: false,
            batchPushColumns: [
                {
                    title: '应用',
                    key: 'app_name'
                },
                {
                    title: '环境',
                    key: 'ip',
                    width: 120,
                    render: (h, params) => {
                        const options = []
                        this.batchPushData[params.index].env_list.map(item => {
                            options.push(
                                h(
                                    'Option',
                                    {
                                        props: {
                                            value: item
                                        }
                                    },
                                    item
                                )
                            )
                        })
                        return h(
                            'Select',
                            {
                                props: {
                                    transfer: true,
                                    value: params.row.suite_code,
                                    filterable: true
                                },
                                on: {
                                    'on-change': val => {
                                        params.row.suite_code = val
                                        this.batchPushData[params.index].suite_code = val
                                        this.queryDefaultRadio(params.row, params.index)
                                        const app_list = this.batchPushData.map(item => item.app_name)
                                        const suite_list = []
                                        // 如果suite_lsit中没有suite_code，就把suite_code加入到suite_list中
                                        this.batchPushData.map(item => {
                                            if (!suite_list.includes(item.suite_code) && item.suite_code) {
                                                suite_list.push(item.suite_code)
                                            }
                                        })
                                        console.log('this.batchPushData', this.batchPushData, suite_list)
                                        this.$refs.agentRefTwo.queryList(app_list, suite_list)
                                    }
                                }
                            },
                            options
                        )
                    }
                }
                // {
                //     title: 'mock-agent开关',
                //     key: 'pro_version',
                //     width: 120,
                //     render: (h, params) => {
                //         return h(
                //             'RadioGroup',
                //             {
                //                 props: {
                //                     value: this.batchPushData[params.index].agent_use_info[0].is_use == 1 ? '是' : '否'
                //                 },
                //                 on: {
                //                     'on-change': val => {
                //                         val == '是'
                //                             ? (this.batchPushData[params.index].agent_use_info[0].is_use = 1)
                //                             : (this.batchPushData[params.index].agent_use_info[0].is_use = 0)
                //                     }
                //                 }
                //             },
                //             [
                //                 h('Radio', {
                //                     props: {
                //                         label: '是'
                //                     }
                //                 }),
                //                 h('Radio', {
                //                     props: {
                //                         label: '否'
                //                     }
                //                 })
                //             ]
                //         )
                //     }
                // },
                // {
                //     title: 'jacoco-agent开关',
                //     key: 'git_version',
                //     width: 120,
                //     render: (h, params) => {
                //         return h(
                //             'RadioGroup',
                //             {
                //                 props: {
                //                     value: this.batchPushData[params.index].agent_use_info[1].is_use == 1 ? '是' : '否'
                //                 },
                //                 on: {
                //                     'on-change': val => {
                //                         val == '是'
                //                             ? (this.batchPushData[params.index].agent_use_info[1].is_use = 1)
                //                             : (this.batchPushData[params.index].agent_use_info[1].is_use = 0)
                //                     }
                //                 }
                //             },
                //             [
                //                 h('Radio', {
                //                     props: {
                //                         label: '是'
                //                     }
                //                 }),
                //                 h('Radio', {
                //                     props: {
                //                         label: '否'
                //                     }
                //                 })
                //             ]
                //         )
                //     }
                // },
                // {
                //     title: 'arex-agent开关',
                //     key: 'git_version1',
                //     width: 120,
                //     render: (h, params) => {
                //         return h(
                //             'RadioGroup',
                //             {
                //                 props: {
                //                     value: this.batchPushData[params.index].agent_use_info[2].is_use == 1 ? '是' : '否'
                //                 },
                //                 on: {
                //                     'on-change': val => {
                //                         val == '是'
                //                             ? (this.batchPushData[params.index].agent_use_info[2].is_use = 1)
                //                             : (this.batchPushData[params.index].agent_use_info[2].is_use = 0)
                //                     }
                //                 }
                //             },
                //             [
                //                 h('Radio', {
                //                     props: {
                //                         label: '是'
                //                     }
                //                 }),
                //                 h('Radio', {
                //                     props: {
                //                         label: '否'
                //                     }
                //                 })
                //             ]
                //         )
                //     }
                // },
                // {
                //     title: 'pinpint-agent开关',
                //     key: 'git_version2',
                //     width: 120,
                //     render: (h, params) => {
                //         return h(
                //             'RadioGroup',
                //             {
                //                 props: {
                //                     value: this.batchPushData[params.index].agent_use_info[2].is_use == 1 ? '是' : '否'
                //                 },
                //                 on: {
                //                     'on-change': val => {
                //                         val == '是'
                //                             ? (this.batchPushData[params.index].agent_use_info[2].is_use = 1)
                //                             : (this.batchPushData[params.index].agent_use_info[2].is_use = 0)
                //                     }
                //                 }
                //             },
                //             [
                //                 h('Radio', {
                //                     props: {
                //                         label: '是'
                //                     }
                //                 }),
                //                 h('Radio', {
                //                     props: {
                //                         label: '否'
                //                     }
                //                 })
                //             ]
                //         )
                //     }
                // }
            ],
            batchPushData: [],
            batchPushSelectData: [],
            is_arex_agent: null,
            is_pinpoint_agent: null,
            loadingBtn: false
        }
    },
    methods: {
        // 显示测试左移Modal
        showTestShiftLeftModal() {
            this.$refs.testShiftLeftModal.show(this.pipeline_id)
        },
        
        // 测试左移成功回调
        handleTestShiftLeftSuccess(result) {
            this.$Message.success('测试左移流水线已启动，请查看Jenkins执行状态')
            // 可以添加其他后续处理逻辑
        },
        
        batchPushSelect(arr) {
            this.batchPushSelectData = arr
            this.batchPushData = []
            arr.map(item => {
                this.batchPushData.push({
                    app_name: item.app_name,
                    suite_code: '',
                    env_list: [],
                    agent_use_info: [
                        {
                            // agent_name: 'mock-agent开关',
                            agent_name: 'howbuy-mock-agent',
                            is_use: 0
                        },
                        {
                            // agent_name: 'jacoco-agent开关',
                            agent_name: 'JaCoCo',
                            is_use: 0
                        },
                        {
                            // agent_name: 'arex-agent开关',
                            agent_name: 'arex-agent',
                            is_use: 0
                        },
                        {
                            // agent_name: 'pinpoint-agent开关',
                            agent_name: 'pinpoint-agent',
                            is_use: 0
                        }
                    ]
                })
            })
        },
        pushConfirm() {
            // 某一个数据的环境没有选择则拦截后续行为
            const check = this.batchPushData.some(item => {
                return item.suite_code === ''
            })
            if (check) {
                this.$Message.error('请选择环境!')
                return
            }

            this.batchPushData
            // 弹框确定按钮
            this.loadingBtn = true
            saveBatchComPile({
                iteration_id: this.pipeline_id,
                app_suite_list: this.batchPushData
            })
                .then(res => {
                    if (res.data.code === '0000') {
                        this.$Message.success(res.data.message)
                        this.batchPushModal = false
                    } else {
                        this.$Message.error(res.data.message)
                    }
                })
                .finally(() => {
                    this.loadingBtn = false
                })
        },
        batchPushBag() {
            if (this.batchPushData.length > 5) {
                this.$Message.error('批量编译发布数量不能超过5个!')
                return
            }
            // 弹框，弹框中展示列表
            this.batchPushModal = true
            // 批量获取应用的环境列表
            getAppSuiteList({
                iteration_id: this.pipeline_id,
                app_list: this.batchPushData.map(item => item.app_name)
            })
                .then(res => {
                    if (res.data.code === '0000') {
                        res.data.data.map(item => {
                            this.batchPushData.map(item1 => {
                                if (item1.app_name === item.app_name) {
                                    item1.env_list = item.env_list
                                }
                            })
                        })
                    } else {
                        this.$Message.error(res.data.message)
                    }
                })
                .catch(() => {
                    this.$Message.error('批量编译发布失败!')
                })
                .finally(() => {
                    console.log('finally')
                    const app_list = this.batchPushData.map(item => item.app_name)
                    const suite_list = []
                    this.batchPushData.map(item => {
                        item.env_list.map(item1 => {
                            if (suite_list.indexOf(item1) === -1) {
                                suite_list.push(item1)
                            }
                        })
                    })
                    this.$refs.agentRefTwo.queryList(app_list, [])
                })
        },
        queryDefaultRadio(row, index) {
            // 查询默认选项
            getAgentUseInfoRequest({
                suite_code: row.suite_code,
                app_name: row.app_name
            }).then(res => {
                if (res.data.status === 'success') {
                    res.data.data.map(item => {
                        if (item.agent_name === 'howbuy-mock-agent') {
                            this.batchPushData[index].agent_use_info[0].is_use = item.is_use * 1
                        } else if (item.agent_name === 'JaCoCo') {
                            this.batchPushData[index].agent_use_info[1].is_use = item.is_use * 1
                        } else if (item.agent_name === 'arex-agent') {
                            this.batchPushData[index].agent_use_info[2].is_use = item.is_use * 1
                        } else if (item.agent_name === 'pinpoint-agent') {
                            this.batchPushData[index].agent_use_info[3].is_use = item.is_use * 1
                        }
                    })
                } else {
                    this.$Message.error(res.data.msg)
                }
            })
        },
        getIterLockInfo() {
            getIterLockInfoApi({
                iteration_id: this.pipeline_id,
                lock_type: 'compile'
            }).then(res => {
                console.log(res.data.data)
                if (Object.keys(res.data.data).length > 0) {
                    console.log(res.data.data.lock_status)

                    if (res.data.data.lock_status === 1) {
                        this.compile_lock_status = true
                    } else {
                        this.compile_lock_status = false
                    }
                } else {
                    this.compile_lock_status = false
                }
            })
        },
        handleRadioChange() {
            console.log('handleRadioChange============================')
            this.$refs.agentRefOne.queryList([this.select_param.app_name], [this.select_env])
            getAgentUseInfo(this.select_env, this.radio_app_name).then(res => {
                let agent_use_info_list = res.data.data
                for (let i = 0; i < agent_use_info_list.length; i++) {
                    if (agent_use_info_list[i].agent_name === 'howbuy-interface-scan-agent') {
                        // this.collect_agent_show = true
                        this.is_collect_agent = parseInt(agent_use_info_list[i].is_use)
                    }
                    // else if (agent_use_info_list[i].agent_name === 'shardingsphere-agent') {
                    //     // this.sharding_agent_show = true
                    //     this.is_sharding_agent = parseInt(agent_use_info_list[i].is_use)
                    //     this.sharding_type = Boolean(this.is_sharding_agent)
                    // }
                    else if (agent_use_info_list[i].agent_name === 'JaCoCo') {
                        this.is_jacoco_agent = parseInt(agent_use_info_list[i].is_use)
                        this.jacoco_type = Boolean(this.is_jacoco_agent)
                    } else if (agent_use_info_list[i].agent_name === 'howbuy-mock-agent') {
                        this.is_mock_agent = parseInt(agent_use_info_list[i].is_use)
                        this.mock_type = Boolean(this.is_mock_agent)
                    } else if (agent_use_info_list[i].agent_name === 'arex-agent') {
                        this.is_arex_agent = parseInt(agent_use_info_list[i].is_use)
                        this.arex_type = Boolean(this.is_arex_agent)
                    } else if (agent_use_info_list[i].agent_name === 'pinpoint-agent') {
                        this.is_pinpoint_agent = parseInt(agent_use_info_list[i].is_use)
                        this.pinpoint_type = Boolean(this.is_pinpoint_agent)
                    }
                }
            })
        },
        changePage(index) {
            var pageSize = 10
            this.current_risk_page = index
            var startIndex = (this.current_risk_page - 1) * pageSize
            var endIndex = startIndex + pageSize
            var pageArr = this.current_risk_detail[this.current_risk_tab]['risk_detail'].slice(startIndex, endIndex)
            this.current_page_risk_detail = pageArr
        },
        getBaseDbInfo() {
            console.log('this.biz_code====' + this.biz_code)
            get_base_db_bind(this.biz_code).then(res => {
                if (res.data.status === 'success') {
                    if (res.data.data) {
                        this.biz_base_db = res.data.data['biz_base_db_code']
                    } else {
                        this.biz_base_db = 0
                    }
                }
            })
        },
        getAppDbInfo(module_name) {
            get_app_db_info(module_name).then(res => {
                this.db_info_list_str = ''
                if (res.data.status === 'success') {
                    if (res.data.data) {
                        this.db_info_list_str = res.data.data['db_info_list'].join(',')
                    } else {
                        this.db_info_list_str = ''
                    }
                }
            })
        },
        getBisNameInfo() {
            console.log('这里开始。。。')
            getBisNameLIstInfo().then(res => {
                console.log('res.data.data===' + JSON.stringify(res.data.data))
                this.biz_name_list = res.data.data
            })
        },
        closeCompileModal() {
            this.compile_data = []
            this.modal_compile = false
        },
        pageChange(page) {
            /**
             * 分页中改变size触发的函数
             */
            this.setPagParams(page, '', '')
            let data = {
                page: this.pagination.page,
                size: this.pagination.size
            }
            this.getEnvData(data)
        },
        pageSizeChange(size) {
            this.setPagParams('', size, '')
            let data = {
                page: 1,
                size: this.pagination.size
            }
            this.getEnvData(data)
        },
        setPagParams(page, size, total) {
            /**
             * 设置分页参数
             */
            if (page !== '') {
                this.pagination.page = page
            }
            if (size !== '') {
                this.pagination.size = size
            }
            if (total !== '') {
                this.pagination.total = total
            }
        },
        getEnvData() {
            this.setPagParams('', '', this.envtableData.length)
            if (this.pagination.page * this.pagination.size >= this.envtableData.length) {
                this.envtablePageData = this.envtableData.slice((this.pagination.page - 1) * this.pagination.size)
            } else {
                this.envtablePageData = this.envtableData.slice(
                    (this.pagination.page - 1) * this.pagination.size,
                    this.pagination.page * this.pagination.size
                )
            }
            let vm = this
            let t1 = window.setTimeout(function() {
                vm.init_select_env()
            }, 100)
        },
        openApiDocUrl(app_name, br_name) {
            window.open('http://apidoc.howbuy.pa/apidoc.html?module_name=' + app_name + '&branch_name=' + br_name)
        },
        openLibUrl(app_name, lib_type, suite_code) {
            getLibUrlApi(this.pipeline_id, app_name, lib_type, suite_code)
                .then(res => {
                    if (res.data.status === 'success') {
                        this.$Message.success(res.data.data.lib_url)
                        window.open(res.data.data.lib_url)
                    } else {
                        this.$Message.error(res.data.msg)
                    }
                })
                .catch(err => {
                    this.$Message.error(err.response.data.msg)
                })
        },
        beginChangeSubmit(param) {
            /* 开始变更提交 */
            doEnvBind(param.job_name, param.app_name, '')
                .then(res => {
                    if (res.data.status === 'success') {
                        // this.$Message.success(res.data.msg);
                        this.jenkinsCompileExec(param.job_name, param.need_mock, 1, 0)
                    } else {
                        this.$Message.error(res.data.msg)
                    }
                })
                .catch(err => {
                    this.$Message.error(err.response.data.msg)
                })
        },
        beginContinuousIntegration(param, jacoco_type, mock_type, arex_type, pinpoint_type) {
            if (param.git_repos_time === null || param.git_repos_time === '') {
                this.$Message.error('该应用无可用制品,请触发变更提交流水线构建可用制品！')
                return
            }
            /* 开始持续集成 */
            this.$Spin.show({
                render: h => {
                    return h('div', [
                        h('Icon', {
                            class: 'demo-spin-icon-load',
                            props: {
                                type: 'ios-loading',
                                size: 32,
                                color: '#2d8cf0'
                            }
                        })
                    ])
                }
            })
            pushTestEnv(
                param.pipeline_id,
                param.node,
                param.app_name,
                param.env,
                param.test_set_id,
                jacoco_type,
                mock_type,
                arex_type,
                pinpoint_type,
                param.agent_info
            )
                .then(res => {
                    this.$Spin.hide()
                    if (res.data.status === 'success') {
                        this.$Message.success(res.data.msg)
                    } else {
                        this.$Message.error(res.data.msg)
                    }
                })
                .catch(err => {
                    this.$Spin.hide()
                    this.$Message.error('发生异常')
                })
        },
        getIterTestEndDate() {
            // todo 新增获取测试结束时间接口
            getIterTestEndDateApi(this.pipeline_id)
                .then(res => {
                    this.test_end_date = res.data.data.test_end_date
                })
                .catch(err => {
                    this.$Spin.hide()
                    this.$Message.error('发生异常')
                })
        },
        beginChangeSubmitAndContinuousIntegration(param) {
            // 测试环境发布时候获取测试结束时间 by 帅 20220322
            console.log('param.app_name===' + param.app_name)
            this.getIterTestEndDate()
            /* 开始变更提交+持续集成 */
            this.pipeline_id = param.pipeline_id
            this.select_param = param
            let envs = param.env_list
            getIsQaApi()
                .then(res => {
                    console.log(res.data.msg)
                    console.log(res.data.data['is_qa'])
                    this.is_qa = res.data.data['is_qa']
                })
                .catch(err => {
                    this.$Message.error(err)
                })

            if (envs != null && envs.trim() !== '') {
                let env_list = envs.split(',')
                if (env_list.length > 0) {
                    this.select_env_list = env_list
                    this.select_env = env_list[0]
                    this.select_env_modal_show = true
                    this.$refs.agentRefOne.queryList([param.app_name], [this.select_env])

                    getAgentUseInfo(this.select_env, param.app_name).then(res => {
                        let agent_use_info_list = res.data.data
                        for (let i = 0; i < agent_use_info_list.length; i++) {
                            if (agent_use_info_list[i].agent_name === 'howbuy-interface-scan-agent') {
                                // this.collect_agent_show = true
                                this.is_collect_agent = parseInt(agent_use_info_list[i].is_use)
                            }
                            // else if (agent_use_info_list[i].agent_name === 'shardingsphere-agent') {
                            //     // this.sharding_agent_show = true
                            //     this.is_sharding_agent = parseInt(agent_use_info_list[i].is_use)
                            //     this.sharding_type = this.is_sharding_agent
                            // }
                            else if (agent_use_info_list[i].agent_name === 'JaCoCo') {
                                this.is_jacoco_agent = parseInt(agent_use_info_list[i].is_use)
                                this.jacoco_type = this.is_jacoco_agent
                            } else if (agent_use_info_list[i].agent_name === 'howbuy-mock-agent') {
                                this.is_mock_agent = parseInt(agent_use_info_list[i].is_use)
                                this.mock_type = this.is_mock_agent
                            } else if (agent_use_info_list[i].agent_name === 'arex-agent') {
                                this.is_arex_agent = parseInt(agent_use_info_list[i].is_use)
                                this.arex_type = this.is_arex_agent
                            } else if (agent_use_info_list[i].agent_name === 'pinpoint-agent') {
                                this.is_pinpoint_agent = parseInt(agent_use_info_list[i].is_use)
                                this.pinpoint_type = this.is_pinpoint_agent
                            }
                        }
                    })
                }
            } else {
                this.$Message.error('请先绑定环境套！')
            }
        },
        cancelChangeSubmitAndContinuousIntegration() {
            this.select_env_modal_show = false
        },
        submitChangeSubmitAndContinuousIntegration() {
            // todo 如果是测试人员需要填写测试结束日期
            if (this.is_qa && this.test_end_date == '') {
                alert('测试结束日期不可以为空')
            } else {
                this.select_env_modal_show = false
                if (this.is_qa) {
                    let test_end_date = this.test_end_date
                    console.log(typeof test_end_date)
                    if (typeof test_end_date === 'object') {
                        test_end_date = test_end_date.toLocaleDateString()
                    }

                    console.info({ iter_id: this.pipeline_id, test_end_date: test_end_date })
                    updateIterTestEndDateApi({ iter_id: this.pipeline_id, test_end_date: test_end_date })
                        .then(res => {
                            console.log(res.data.msg)
                        })
                        .catch(err => {
                            this.$Message.error(err)
                        })
                }
                console.log('this.is_mock_agent ====' + this.is_mock_agent)
                if (this.is_mock_agent === '') {
                    alert('mock-agent开关不能为空')
                } else if (this.is_jacoco_agent === '') {
                    alert('jacoco-agent开关不能为空')
                } else if (this.is_arex_agent === '') {
                    alert('arex-agent开关不能为空')
                } else if (this.is_pinpoint_agent === '') {
                    alert('pinpoint-agent开关不能为空')
                } else {
                    let agent_use_info = []
                    agent_use_info.push(
                        {
                            agent_name: 'howbuy-mock-agent',
                            is_use: this.is_mock_agent
                        },
                        { agent_name: 'JaCoCo', is_use: this.is_jacoco_agent },
                        { agent_name: 'arex-agent', is_use: this.is_arex_agent },
                        { agent_name: 'pinpoint-agent', is_use: this.is_pinpoint_agent }
                    )
                    if (this.sharding_agent_show === true) {
                        agent_use_info.push({ agent_name: 'shardingsphere-agent', is_use: this.is_sharding_agent })
                    }
                    if (this.collect_agent_show === true) {
                        // agent_use_info.push({ 'agent_name': 'howbuy-interface-scan-agent', 'is_use': this.is_collect_agent })
                        // 强制打开扫描开关 20230322 by fwm
                        agent_use_info.push({ agent_name: 'howbuy-interface-scan-agent', is_use: 1 })
                    }
                    saveOrUpdateEnvAppAgentBind(
                        this.select_env,
                        this.select_param.app_name,
                        JSON.stringify(agent_use_info)
                    )
                        .then(res => {
                            /* 选择环境后执行变更-推包-CI */
                            doEnvBind(this.select_param.job_name, this.select_param.app_name, this.select_env)
                                .then(res => {
                                    if (res.data.status === 'success') {
                                        // this.$Message.success(res.data.msg);
                                        this.jenkinsCompileExec(
                                            this.select_param.job_name,
                                            this.select_param.need_mock,
                                            0,
                                            0
                                        )
                                    } else {
                                        this.$Message.error(res.data.msg)
                                    }
                                })
                                .catch(err => {
                                    this.$Message.error(err.response.data.msg)
                                })
                        })
                        .catch(err => {
                            this.$Message.error(err)
                        })
                }

                let job_name = this.select_param.job_name
                let opt_desc = job_name + "'变更-推包-CI'流水线" + '(' + this.select_env + ')'
                let pipeline_id = this.pipeline_id
                createPipeLineLog(opt_desc, pipeline_id, job_name)
                    .then(res => {})
                    .catch(err => {
                        this.$Message.error(err.response.data.msg)
                    })
            }
        },
        beginMockBuild(param) {
            /* 开始变更提交+持续集成 */
            this.mock_select_param = param
            let envs = param.env_list
            if (envs != null && envs.trim() !== '') {
                let env_list = envs.split(',')
                if (env_list.length > 1) {
                    this.mock_select_env_list = env_list
                    this.mock_select_env = env_list[0]
                    this.mock_select_env_modal_show = true
                } else {
                    this.mock_select_env = env_list[0]
                    this.submitMockBuild()
                }
            } else {
                this.$Message.error('请先绑定环境套！')
            }
        },
        submitMockBuild() {
            /* 选择环境后执行变更提交+持续集成 */
            doEnvBind(this.mock_select_param.job_name, this.mock_select_param.app_name, this.mock_select_env)
                .then(res => {
                    if (res.data.status === 'success') {
                        // this.$Message.success(res.data.msg);
                        this.jenkinsCompileExec(this.mock_select_param.job_name, this.mock_select_param.need_mock, 0, 1)
                    } else {
                        this.$Message.error(res.data.msg)
                    }
                })
                .catch(err => {
                    this.$Message.error(err.response.data.msg)
                })
        },
        selectChange(selection, row) {
            this.tempArr = Array.from(new Set(this.tempArr)) // 去重

            // let newArr=[]
            // this.tempArr.forEach(item=>{
            //   if(undefined!=item&&item.trim().length>0){
            //     newArr.push(item)
            //   }
            // })
            // this.tempArr = newArr

            console.log('this.tempArr.length===' + this.tempArr.length)
            console.log('待推送的环境数据row.suite_code===' + row.suite_code)
            this.tempArr.push(row.suite_code) // 将该条数据添加到tempArr中
            console.log('把环境数据推送到this.tempArr===' + this.tempArr)
            // this.init_select_env();
        },
        selectChangeCancel(selection, row) {
            this.tempArr = Array.from(new Set(this.tempArr)) // 去重
            this.tempArr.forEach((item, index) => {
                // tempArr中找到该条数据并删除
                if (row.suite_code === item) {
                    this.tempArr.splice(index, 1)
                }
            })
        },
        selectChangeAll(selection) {
            selection.forEach(item => {
                // 将本页全部勾选添加到tempArr中
                this.tempArr.push(item.suite_code)
            })
            this.tempArr = Array.from(new Set(this.tempArr)) // 去重
        },
        selectChangeAllCancel(selection) {
            this.tempArr = Array.from(new Set(this.tempArr)) // 去重
            selection.forEach(item => {
                // tempArr中找到该条数据并删除
                this.tempArr.forEach((e, index) => {
                    if (item.suite_code === e) {
                        this.tempArr.splice(index, 1)
                    }
                })
            })
        },
        init_select_env() {
            this.$refs.bind_env_table.selectAll(false)
            // console.info('初始化当前页勾选');
            // console.info(this.tempArr);
            // console.info(this.envtablePageData);
            if (this.tempArr != null && this.tempArr.length > 0) {
                let objData = this.$refs.bind_env_table.objData
                // console.info(objData);
                for (let env of this.tempArr) {
                    // console.info(env)
                    for (let index in objData) {
                        if (objData[index].suite_code === env) {
                            objData[index]._isChecked = true
                        }
                    }
                }
            }
        },
        cancel_bind_env() {
            this.bind_env_modal_show = false
        },
        batchBindEnv() {
            this.tempArr = []
            this.table_data_filter = ''
            this.select_pipeline_name = ''
            this.$refs.bind_env_table.selectAll(false)
            this.getEnvData()
            this.bind_env_modal_show = true
        },
        close_fail_bind_env() {
            this.bind_env_fail_modal_show = false
            this.this.tempArr = []
            this.table_data_filter = ''
        },

        save_bind_env() {
            /* 保存环境绑定 */
            this.tempArr = Array.from(new Set(this.tempArr)) // 去重
            console.info('绑定环境后的this.tempArr====' + this.tempArr)
            let envList = this.tempArr.join(',')
            console.info('转换成环境列表====' + envList)
            console.info('转换成环境列表this.select_pipeline_name====' + this.select_pipeline_name)
            if (this.select_pipeline_name != null && this.select_pipeline_name != '') {
                for (let pipelineDatum of this.pipeline_data) {
                    if (pipelineDatum.job_name === this.select_pipeline_name) {
                        pipelineDatum.env_list = envList
                        saveOrUpdateEnvBind(pipelineDatum.pipeline_id, pipelineDatum.app_name, pipelineDatum.env_list)
                            .then(res => {
                                if (res.data.status === 'success') {
                                    this.$Message.success(res.data.msg)
                                } else {
                                    this.$Message.error(res.data.msg)
                                }
                                this.getEnvInfo()
                                this.get_pipeline_info()
                            })
                            .catch(err => {
                                this.$Message.error('更新服务异常！')
                            })
                    }
                }
            } else {
                const pipeline_data = []
                for (let pipelineDatum of this.pipeline_data) {
                    pipeline_data.push({ pipeline_id: pipelineDatum.pipeline_id, app_name: pipelineDatum.app_name })
                }
                console.info('bind_env--------------' + JSON.stringify(pipeline_data))
                this.bindEnvFailData = []
                saveOrUpdateBatchEnvBind({ pipeline_data: pipeline_data, env_list: envList })
                    .then(res => {
                        if (res.data.status === 'success') {
                            this.$Message.success(res.data.msg)
                        } else {
                            this.bindEnvFailData = res.data.data
                            this.bind_env_fail_modal_show = true
                        }
                        this.getEnvInfo()
                        this.get_pipeline_info()
                    })
                    .catch(err => {
                        this.$Message.error('更新服务异常！')
                    })
            }

            // console.info(this.pipeline_data)
            this.bind_env_modal_show = false
        },
        save_testsuit_id() {
            /* 保存测试集id */
            let rowdata = this.rowdata
            let pipeline_id = this.pipeline_id
            let testsuite_id = this.testsuit_id
            saveOrUpdateEnvTestsuiteBind(pipeline_id, rowdata.app_name, rowdata.env, testsuite_id)
                .then(res => {
                    if (res.data.status === 'success') {
                        this.$Message.success(res.data.msg)
                        this.getEnvInfo()
                    }
                })
                .catch(err => {
                    this.$Message.error('更新测试集ID异常！')
                })
        },

        beginStrengthenContinuousIntegration() {
            let rowdata = this.rowdata
            // let sharding_type = this.sharding_type ? 1 : 0
            let jacoco_type = this.jacoco_type ? 1 : 0
            let mock_type = this.mock_type ? 1 : 0
            let arex_type = this.arex_type ? 1 : 0
            let pinpoint_type = this.pinpoint_type ? 1 : 0
            let mock_agent_version = this.select_mock_agent_version
            console.log('jacoco_type======' + jacoco_type)
            console.log('mock_type======' + mock_type)
            let agent_info = { agent_name: 'howbuy-mock-agent', agent_version: mock_agent_version }
            if (mock_type == 1) {
                rowdata['agent_info'] = JSON.stringify(agent_info)
            }
            if (rowdata.deploy_type === 1) {
                alert('暂不支持虚机部署方式的增强CI，请切换为容器部署后尝试！')
            } else {
                this.strengthen_CI_show = false
                this.beginContinuousIntegration(rowdata, jacoco_type, mock_type, arex_type, pinpoint_type)
                let job_name = this.pipeline_id + '_' + rowdata.app_name
                let opt_desc = job_name + "'推包-CI-增强'流水线" + '(' + rowdata.env + ')'
                let pipeline_id = this.pipeline_id
                createPipeLineLog(opt_desc, pipeline_id, job_name)
                    .then(res => {})
                    .catch(err => {
                        this.$Message.error(err.response.data.msg)
                    })
            }
        },
        // savePlan() {
        //   let data = {};
        //   data["businessID"] = store.state.businessID;
        //   data["date"] = this.date;
        //   data["time"] = this.time;
        //   data["sql_content"] = this.sql_content;
        //   data["release_notice"] = this.release_notice;
        //   data["release_description"] = this.release_description;
        //   data["receivers"] = this.allSelectedMails.toString();
        //   editIterativePlan(data).then(res => {
        //     if (res.data.code === 0) {
        //       this.$Message.success("保存成功");
        //     } else {
        //       this.$Message.error("保存失败");
        //     }
        //   });
        // },
        sortToSelectToUnshift() {
            // console.info('已选择数据置前排序');
            if (this.tempArr != null && this.tempArr.length > 0) {
                for (let tempArgument of this.tempArr) {
                    this.envtableData.forEach((e, index) => {
                        if (e.suite_code === tempArgument) {
                            this.envtableData.splice(index, 1)
                            this.envtableData.unshift(e)
                        }
                    })
                }
            }
        },
        table_filters() {
            // console.log(this.table_data_filter)
            let tmp_arry = []
            for (let i of this.envtableData) {
                // console.log(i.suite_code)
                if (i.suite_code && i.suite_code.indexOf(this.table_data_filter) >= 0) {
                    tmp_arry.push(i)
                } else if (i.node && i.node.indexOf(this.table_data_filter) >= 0) {
                    tmp_arry.push(i)
                }
            }
            this.envtablePageData = tmp_arry
            // this.envtablePageData = []
            // let tmp_arry = []
            // for (var i = 0; i < data.length; i++) {
            // console.log(data[i]["suite_code"])
            //   tmp_arry.push({"label": data[i].suite_code, "value": i})
            // }
            // this.suite_code_filters = tmp_arry
            // this.$set(this.envtableColumns, 0, {
            //   title: "环境套名",
            //   key: "suite_code",
            //   width: 160,
            //   filters: tmp_arry,
            //   filterMultiple: false,
            //   filterMethod (value, row) {
            //     for (var i = 0; i < data.length; i++) {
            //       if (value === i) {
            //         return row.suite_code === data[i]['label']
            //       }
            //     }
            //   }
            // })

            // console.log(tmp_arry)
            // this.$set(this.suite_code_filters, tmp_arry)
        },

        getEnvInfo() {
            /* 获取环境信息详情 */
            getEnvBindInfo(this.pipeline_id)
                .then(res => {
                    if (res.data.status === 'success') {
                        this.envtableData = res.data.data['suite_info_list']
                        this.getEnvData()
                        // this.suite_code_selection = res.data.data["bind_env"];
                        this.continuous_integration_data = res.data.data['push_list']
                    } else {
                        this.$Message.error(res.data.msg)
                    }
                })
                .catch(err => {
                    this.$Message.error(err.response.data.msg)
                })
        },

        get_app_latest_archive_info() {
            console.log('jinlaile')
            /* 获取应用最近一次完成（归档信息） */
            getAppLatestArchiveInfo(this.pipeline_id)
                .then(res => {
                    if (res.data.status === 'success') {
                        this.archive_app_data = res.data.data['app_latest_archive_info_list']
                    } else {
                        this.$Message.error(res.data.msg)
                    }
                })
                .catch(err => {
                    this.$Message.error(err.response.data.msg)
                })
        },

        get_app_onlineing_app_info() {
            /* 获取正在上线的应用信息 */
            getAppOnlineingAppInfo(this.pipeline_id)
                .then(res => {
                    if (res.data.status === 'success') {
                        this.onlineing_app_data = res.data.data['app_online_info_list']
                    } else {
                        this.$Message.error(res.data.msg)
                    }
                })
                .catch(err => {
                    this.$Message.error(err.response.data.msg)
                })
        },

        get_pipeline_info(get_pipeline_info_type) {
            let getPipelineInfoType = get_pipeline_info_type || 'update'
            getPipelineInfo(store.state.iterationID, getPipelineInfoType)
                .then(res => {
                    if (res.data.status === 'success') {
                        // this.pipeline_data = []
                        for (let row of res.data.data) {
                            // row["is_junit"] = true
                            // 没有被赋值过的时候给一个 默认值，如果有责不改，防止覆盖用户的修改 by帅 20220419
                            if (!this.is_junit_dict.hasOwnProperty(row['job_name'])) {
                                this.is_junit_dict[row['job_name']] = false
                            }
                        }
                        this.pipeline_data = res.data.data
                        this.batchPushSelectData = []
                        console.log(this.is_junit_dict)
                    } else {
                        this.$Message.error(res.data.msg)
                    }
                })
                .catch(err => {
                    this.$Message.error(err.response.data.msg)
                })
        },
        initThisVue() {
            this.pipeline_id = store.state.iterationID

            if (this.pipeline_id) {
                this.getIterLockInfo()
                // if (this.pipeline_data.length > 0) {
                //     this.get_pipeline_info()
                // } else {
                //     this.get_pipeline_info('init')
                //     this.get_pipeline_info()
                // }
                // 每次初始化都走这个逻辑，先渲染table表数据，再查风险信息 20250328 by fwm
                this.get_pipeline_info('init')
                setTimeout(() => {
                    // 等待10秒后执行的代码
                    this.get_pipeline_info()
                }, 10000)
                this.getEnvInfo()
                if (!this.switch_pipeline_info) {
                    // this.switch_pipeline_info = setInterval(this.get_pipeline_info, 15000)
                    this.switch_pipeline_info = setInterval(this.get_pipeline_info, 60000)
                } else {
                    clearInterval(this.switch_pipeline_info)
                    // this.switch_pipeline_info = setInterval(this.get_pipeline_info, 15000)
                    this.switch_pipeline_info = setInterval(this.get_pipeline_info, 60000)
                }
            } else {
                this.pipeline_data = []
                if (this.switch_pipeline_info) {
                    clearInterval(this.switch_pipeline_info)
                }
            }
        },
        closeJenkinsStatus() {
            if (this.switch_pipeline_info) {
                clearInterval(this.switch_pipeline_info)
            }
        },
        showAppConfig(val) {
            this.index = val
            this.m_app_config = this.app_detail[this.index].app_config
            this.modal_config = true
        },
        showGitDiff(val) {
            this.m_git_diff = ''
            this.modal_git = true
            let data = {}
            data['business_id'] = store.state.businessID
            data['app_name'] = this.app_detail[val].app_name
            data['app_git'] = this.app_detail[val].app_git
            this.m_git_diff = '正在获取信息 ...'
            getGitDiff(data).then(res => {
                if (res.data.code === 0) {
                    this.m_git_diff = res.data.data
                } else {
                    this.m_git_diff = res.data.msg
                }
            })
        },
        cancel() {
            this.modal_git = false
            this.modal_config = false
        },
        customCompileExec(job_name) {
            this.custom_job_name = job_name
            this.$Spin.show({
                render: h => {
                    return h('div', [
                        h('Icon', {
                            class: 'demo-spin-icon-load',
                            props: {
                                type: 'ios-loading',
                                size: 18
                            }
                        }),
                        h('div', '解析应用的构建列表...')
                    ])
                }
            })
            parseCompileApp(job_name)
                .then(res => {
                    if (res.data.status === 'success') {
                        this.drawer_compile = true
                        this.compile_list = res.data.data
                        // this.$Message.success(res.data.msg);
                    } else {
                        this.$Message.error(res.data.msg)
                    }
                    this.$Spin.hide()
                })
                .catch(err => {
                    this.$Spin.hide()
                    this.$Message.error(err.response.data.msg)
                })
        },
        jenkinsCompileExec(job_name, need_mock, skip, build_mock) {
            if (this.is_init === 1 && this.is_increment === 1) {
                alert('mock-agent开关不能同时选中')
                return false
            }
            let db_exec_type = 'increment'
            if (this.is_init === 1) {
                db_exec_type = 'init'
            }
            if (this.is_increment === 1) {
                db_exec_type = 'increment'
            }

            this.$Spin.show({
                render: h => {
                    return h('div', [
                        h('Icon', {
                            class: 'demo-spin-icon-load',
                            props: {
                                type: 'ios-loading',
                                size: 18
                            }
                        }),
                        h('div', '调用构建job...')
                    ])
                }
            })
            if (this.biz_base_db === undefined) {
                this.biz_base_db = 0
            }
            doJenkinsCompileJob(
                job_name,
                skip,
                this.is_junit_dict[job_name],
                this.is_mock_agent,
                this.biz_base_db,
                db_exec_type
            )
                .then(res => {
                    if (res.data.status === 'success') {
                        this.compile_data = res.data.data
                        this.modal_compile = true
                    } else {
                        this.$Message.error(res.data.msg)
                    }
                    this.$Spin.hide()
                })
                .catch(err => {
                    this.$Spin.hide()
                    this.$Message.error(err.response.data.msg)
                })
        },
        mockJenkinsCompileExec(job_name, need_mock, skip) {
            if (need_mock == 1 || need_mock == 0) {
                this.jenkinsCompileExec(job_name, need_mock, skip, 1)
            } else {
                alert(job_name + '不支持mock编译')
            }
        },
        selectList(selection) {
            this.selectApps = []
            for (let i = 0; i < selection.length; i++) {
                this.selectApps.push(selection[i].job_name)
            }
        },
        startCustomCompile() {
            let all_choice = this.$refs.compile_list.getCheckedAndIndeterminateNodes()
            let compile_list = []
            all_choice.forEach(item => {
                compile_list.push(item.title)
            })
            this.$Spin.show({
                render: h => {
                    return h('div', [
                        h('Icon', {
                            class: 'demo-spin-icon-load',
                            props: {
                                type: 'ios-loading',
                                size: 18
                            }
                        }),
                        h('div', '调用自定义构建job...')
                    ])
                }
            })
            doCustomCompile(this.custom_job_name, compile_list)
                .then(res => {
                    if (res.data.status === 'success') {
                        this.$Message.success(res.data.msg)
                    } else {
                        this.$Message.error(res.data.msg)
                    }
                    this.$Spin.hide()
                    this.closeDrawer()
                })
                .catch(err => {
                    this.$Spin.hide()
                    this.closeDrawer()
                    this.$Message.error(err.response.data.msg)
                })
        },
        batchCompile() {
            this.$Spin.show({
                render: h => {
                    return h('div', [
                        h('Icon', {
                            class: 'demo-spin-icon-load',
                            props: {
                                type: 'ios-loading',
                                size: 18
                            }
                        }),
                        h('div', '调用构建job...')
                    ])
                }
            })
            doJenkinsCompileJob(this.selectApps.join(','), 0)
                .then(res => {
                    if (res.data.status === 'success') {
                        this.compile_data = res.data.data
                        this.modal_compile = true
                    } else {
                        this.$Message.error(res.data.msg)
                    }
                    this.$Spin.hide()
                })
                .catch(err => {
                    this.$Spin.hide()
                    this.$Message.error(err.response.data.msg)
                })
        },
        // sendEmail() {
        //   let vm = this;
        //   let data = {};
        //   data["addresses"] = this.allSelectedMails.join(",");
        //   data["business_id"] = store.state.businessID;
        //   data["stage"] = this.stage;

        //   //归档时不可操作
        //   if (this.stage == "finish") {
        //     this.$Spin.show({
        //       render: h => {
        //         return h("div", [
        //           h("Icon", {
        //             class: "demo-spin-icon-load",
        //             props: {
        //               type: "ios-loading",
        //               size: 18
        //             }
        //           }),
        //           h("div", "归档进行中。。。")
        //         ]);
        //       }
        //     });
        //   }
        //   sendEmailNotify(data)
        //     .then(res => {
        //       if (res.data.code === 0) {
        //         this.$Message.success(res.data.msg);
        //       } else if (res.data.code === 2) {
        //         alert(res.data.msg);
        //         store.commit("setBusinessID", "");
        //       } else {
        //         this.$Message.error(res.data.msg);
        //       }
        //     })
        //     .catch(function (err) {
        //       vm.$Message.error("邮件发送失败");
        //     });
        //   if (this.stage == "finish") {
        //     //归档成功时置空
        //     this.$Spin.hide();
        //   }
        //   this.email_modal = false;
        //   this.mail_content = "";
        // },
        closeDrawer() {
            this.drawer_compile = false
        },
        cancelEmail() {
            this.email_modal = false
            this.mail_content = ''
        },
        // sendUatEmail() {
        //   this.email_modal = true;
        //   this.stage = "uat";
        //   let data = {};
        //   data["business_id"] = store.state.businessID;
        //   data["stage"] = this.stage;
        //   getEmailPreview(data).then(res => {
        //     if (res.data.code === 0) {
        //       this.mail_content = res.data.data;
        //     } else {
        //       this.mail_content = "";
        //     }
        //   });
        // },
        // sendProdEmail() {
        //   this.email_modal = true;
        //   this.stage = "prod";
        //   let data = {};
        //   data["business_id"] = store.state.businessID;
        //   data["stage"] = this.stage;
        //   getEmailPreview(data).then(res => {
        //     if (res.data.code === 0) {
        //       this.mail_content = res.data.data;
        //     } else {
        //       this.mail_content = "";
        //     }
        //   });
        // },
        sendFinishEmail() {
            // this.email_modal = true
            // this.stage = 'finish'
            // this.mail_content = '<p style="font-size: 1rem; margin-left: 8rem; margin-right: 1rem;"><span style="color: blue">' +  store.state.businessID + '</span> 上线完成?</p>'
            this.stage = 'finish'
            let type = 'git'
            let business_id = store.state.businessID
            getAppVersionInfo((type = type), (business_id = business_id)).then(res => {
                this.finish_table_data = res.data
            })
            this.finish_modal = true
        },
        show_mock_agent_version() {
            let agent_name = 'howbuy-mock-agent'
            if (this.mock_agent_version_modal_show === true) {
                this.mock_agent_version_modal_show = false
            } else {
                this.mock_agent_version_modal_show = true
            }
            getAgentVersionInfo(agent_name).then(res => {
                console.log('res.data======' + res.data.data[0].message)
                this.mock_agent_version_list = res.data.data
                // 默认选中已归档的版本
                this.select_mock_agent_version = res.data.data[0].version
            })
        },
        showSqlCheckSuiteModal(app_name) {
            this.bind_sql_check_suite_show = true
            getAppSqlCheckSuiteCode(app_name).then(res => {
                if (res.data.status === 'success') {
                    console.log('res.data.check_suite_code====' + res.data.data.check_suite_code)
                    this.app_sql_check_suite_code = res.data.data.check_suite_code
                } else {
                    this.app_sql_check_suite_code = ''
                }
            })
            getTestSuiteCode().then(res => {
                if (res.data.status === 'success') {
                    console.log('this.suite_code_list====' + res.data.data)
                    this.suite_code_list = res.data.data
                }
            })
        },
        save_sql_check_suite() {
            console.log('this.rowdata ====' + JSON.stringify(this.rowdata))
            let app_name = this.rowdata.app_name
            console.log('app_name ====' + app_name)

            if (typeof app_name === 'undefined') {
                app_name = this.radio_app_name
            }
            let check_suite_code = this.app_sql_check_suite_code
            if (check_suite_code === '') {
                this.$Message.error('环境不能为空')
            } else {
                saveSqlCheckSuite(app_name, check_suite_code).then(res => {
                    this.bind_sql_check_suite_show = false
                    if (res.data.status === 'success') {
                        this.$Message.info('保存成功')
                    } else {
                        this.$Message.error('保存失败')
                    }
                })
            }
        },
        changeRiskTab(riskTabIndex) {
            this.current_risk_tab = riskTabIndex
            console.log('current_risk_detail:%o', this.current_risk_detail)
            if (this.current_risk_detail[riskTabIndex]['result_show_type'] == 'table') {
                this.current_risk_page = 1
                var pageSize = 10
                var startIndex = (this.current_risk_page - 1) * pageSize
                var endIndex = startIndex + pageSize
                var pageArr = this.current_risk_detail[riskTabIndex]['risk_detail'].slice(startIndex, endIndex)
                this.current_page_risk_detail = pageArr
            }
        }
    },
    mounted() {
        // this.initThisVue();
    },
    destroyed() {
        if (this.switch_pipeline_info) {
            clearInterval(this.switch_pipeline_info)
        }
    }
}
</script>

<style>
.ciOperation .ivu-table-cell {
    padding: 0px;
}

.ciOperation .ivu-table-cell div {
    display: flex;
    justify-content: space-evenly;
    flex-wrap: wrap;
}

@keyframes pulse {
    0% {
        transform: scale(0);
    }
    50% {
        transform: scale(1);
    }
    100% {
        transform: scale(0);
    }
}

.entrance_guard_normal > span {
    position: absolute;
    top: 0;
    right: 0;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: white;
}

.entrance_guard_blocked > span {
    position: absolute;
    top: 0;
    right: 0;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: red;
    animation: pulse 2s infinite;
}

.entrance_guard_warn > span {
    position: absolute;
    top: 0;
    right: 0;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: yellow;
    animation: pulse 2s infinite;
}

.entrance_guard_show_class .ivu-modal-header {
    padding: 10px 4px;
}

.entrance_guard_show_class .ivu-modal-body {
    padding: 4px;
}

.pagination_class {
}
</style>
