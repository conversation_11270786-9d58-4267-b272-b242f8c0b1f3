<template>
  <Card shadow style="height: 100%">
    <p slot="title" style="width: 100%;text-align: center;">
      业务数据执行计划管理
    </p>
    <Form


      :label-width="180"
      style="text-align: left"
    >
      <FormItem v-show="br_show" label="测试数据开发业务类型：" prop="bis_type" :rules="ruleValidate">
        <Row>
          <Col span="7">
            <Select placeholder="填写业务类型" v-model="formValidate.bis_type" filterable clearable style="width:200px"
                    @on-change="select_bis_type">
              <Option v-for="item in bis_type_list"
                      :value="item"
                      :key="item"
                      :label="item">
                {{ item }}
              </Option>
            </Select>
          </Col>
          <i-col style="margin: 10px;text-align: right" span="2">
            <span style="text-align: right; display: inline-block;">分支版本：</span>
          </i-col>
          <Col span="8">
            <span style="margin: 10px;text-align: left; display: inline-block;">{{
                archive_bis_pipeline_id
              }}</span>
          </Col>
        </Row>
      </FormItem>
      <FormItem v-show="br_show" label="分支版本号：" prop="sql_branch_name">
        <Row>
          <Col span="7">
            <Select placeholder="选择业务分支号" v-model="formValidate.sql_branch_name" filterable clearable
                    style="width:200px" @on-change="select_bis_branch">
              <Option v-for="item in test_data_branch_list"
                      :value="item.bis_branch"
                      :key="item.bis_branch"
                      :label="item.bis_branch">
                {{ item.bis_branch }}
              </Option>
            </Select>
          </Col>
        </Row>
      </FormItem>
      <FormItem label="被测应用与分支选择：" label-position="top">
        <Tables stripe v-model="test_data_info" :columns="columns" @on-selection-change="selectChange" ref="my_table">
        </Tables>
      </FormItem>
      <FormItem label="时间设置" label-position="top" prop="setting_time">
        <Row>
          <Col span="3">
            <DatePicker v-model="setting_time" type="datetime" placeholder="请选择开始时间" style="width: 200px"
                        :transfer="true"/>
            <!-- <DatePicker v-model="start_datetime" type="datetime" placeholder="请选择开始时间" style="width:100%"></DatePicker> -->
          </Col>
        </Row>
      </FormItem>
      <FormItem v-show="br_show" label="测试集ID：" prop="test_version">
        <Row>
          <Col span="7">
            <Input v-model="test_id" placeholder="填写测试集ID" style="width:200px"/>
          </Col>
        </Row>
      </FormItem>
      <FormItem prop="pipeline_list" label="是否初始化CCMS">
        <RadioGroup v-model="form.init_ccms">
          <Radio label="1">是</Radio>
          <Radio label="0">否</Radio>
        </RadioGroup>
      </FormItem>
      <FormItem label="清理缓存" class="formItem">
        <RadioGroup v-model="form.clean_cache">
          <Radio label="1">是</Radio>
          <Radio label="0">否</Radio>
        </RadioGroup>
      </FormItem>
      <FormItem>
        <Col span="3">
        <Button type="primary" @click="handleSubmit('formValidate','start_datetime','test_id','form')"
                :loading="btnDisabled">执行
        </Button>
        </Col>
        <Col span="2">
        <Button type="primary" @click="goToDetail()"
                :loading="btnDisabled">流水线详情
        </Button>
        </Col>
      </FormItem>
      <Modal
        title=""
        v-model="select_env_modal_show"
        width="580"
        :mask-closable="true"
      >
        <Row style="margin-top: 2em"> 选择执行环境 上一次执行环境：{{ last_suite_code }}
          <Select v-model="suite_info" filterable clearable placeholder="请选择执行环境" @on-change="select_suite_code">
            <Option v-for="item in suite_item_list"
                    :value="item.suite_code"
                    :label="item.suite_code"
                    :key="item.suite_code">
              {{ item.suite_code }}
            </Option>
          </Select>
          <!-- <Select v-model="dump_bis_code" style="width: 100px" clearable>
            <Option v-for="item in dump_type_list" :value="item" :key="item">{{ item }}</Option>
          </Select> -->
        </Row>
        <div slot="footer">
          <Button @click="submitTestPlanExec">确定</Button>
          <Button @click="cancelSubmitTestPlanExec">关闭</Button>
        </div>
      </Modal>
    </Form>

  </Card>
</template>

<script>
import TestDataMgtPlanTable from "@/spider-components/test-data-mgt-plan-table";
import Tables from "@/components/tables";
import store from "@/spider-store";
import {
  getBisInfo, getBisVersionInfo, getBisBranchInfo, testDataSaveExecPlan, getBisBranchAppInfo, getTestExecPlanRecord,
  getTestExecPlanBisRecord, dbTestInitMultiPush, getTestExecPlanPipelineRecord
} from '@/spider-api/test-env-mgt'
import {
  getSuiteInfo,
} from '@/spider-api/mgt-env'

export default {
  name: "TestDataMgtPlan",
  components: {
    TestDataMgtPlanTable,
    Tables
  },
  data() {
    return {
      br_show: true,
      bis_type_list: [],
      setting_time: '1970-1-1 8:0:0',
      btnDisabled: false,
      test_data_branch_list: [],
      test_data_info: [],
      test_id_list: ['408', '409'],
      select_row_test_plan: [],
      test_branch_dict: [],
      test_data_branch_dict: {},
      suite_item_list: [],
      suite_info: '',
      suite_info_dict: {},
      select_env_modal_show: false,
      tmp_app_info: [],
      test_id: '',
      archive_bis_pipeline_id:'暂无归档版本',
      last_suite_code: '',
      history_title_info:'历史执行列表',
      history_modal:false,
      historytableData:[],
      jenkins_url:'',
      form: {
        clean_cache: '1',
        init_ccms: '1',
      },
      formValidate: {
        sql_branch_name: '',
        bis_type: '',
      },
      first_change_flag: false,
      columns: [
        {
          type: 'selection',
          width: 50,
        },
        {title: "业务关联的应用", key: "app_name"},
        /*{title: "仓库", key: "git_path"},*/
        // {title: "业务类型", key: "bis_code"},
        // {
        //   title: "业务归档版本",
        //   key: "archive_bis_br_name",

        // },
        {
          title: "应用归档版本",
          key: "archive_br_name",
        },
        {
          title: "应用线上版本",
          key: "online_br_name",
        },
        {
          title: "应用在途分支",
          key: "pipeline_id",
          render: (h, params) => {
            let branch_list = [];
            console.log(params.row.pipeline_id)
            params.row.pipeline_id.forEach(item => {
              let branch = h('Option', {
                props: {
                  lable: Object.keys(item)[0],
                  value: Object.values(item)[0]
                }
              }, Object.keys(item)[0]);
              branch_list.push(branch)
            });
            return h('Select', {
              props: {
                value: params.row.br_name,
                transfer: true
              },
              style: {
                width: '100px',
              },
              on: {
                'on-change': (val) => {
                  let tmp_row = params.row
                  let select_app_name = params.row.app_name
                  console.log('llllllllllllllllllllll')
                  this.test_branch_dict[params.row.app_name] = {
                    app_name: params.row.app_name,
                    archive_br_name: params.row.archive_br_name,
                    br_name: val,
                    branch_name: val,
                    online_br_name: params.row.online_br_name
                  }
                  // this.test_branch_dict.push({app_name:params.row.app_name,archive_br_name:params.row.archive_br_name,br_name:val,branch_name:val,online_br_name:params.row.online_br_name})
                  console.log(this.test_branch_dict)
                  // params.row['select_app_list'] = val
                  // this.appChangeGroup(params.row.ip, val)
                }
              }
            }, branch_list);
          },
        },
      ],
    };
  },

  computed: {

    ruleValidate() {
      return {
        sql_branch_name: [
          {required: true, message: '请选择分支源', trigger: 'blur'}
        ],
        bis_type: [{required: true, message: '请选择分支源', trigger: 'blur'}]
      }
    }
  },

  methods: {
    select_bis_branch() {
      this.$Spin.show({
        render: h => {
          return h("div", [
            h("Icon", {
              class: "demo-spin-icon-load",
              props: {
                type: "ios-loading",
                size: 18
              }
            }),
            h("div", "查询数据中......")
          ]);
        }
      });
      getBisBranchAppInfo(this.formValidate).then(res => {
        this.test_data_app_info = res.data.data
        this.test_data_info = []
        for (let item in this.test_data_app_info) {
          console.log('vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv')
          console.log(this.test_data_app_info[item].module_name)
          this.test_data_info.push(this.test_data_branch_dict[this.test_data_app_info[item].module_name])
        }
        console.log('this.test_data_info', this.test_data_info)

        this.$Spin.hide();
      })
      //   getBisVersionInfo(this.formValidate).then(res =>{
      //     this.test_data_info = res.data.data
      //     console.log('this.test_data_info', this.test_data_info)
      //     this.$Spin.hide();
      // })
      console.log(',,,,,,,,,,,,,,,,,,,,,,,,')
      console.log(this.formValidate)
      if (this.formValidate.sql_branch_name != undefined) {
        getTestExecPlanPipelineRecord(this.formValidate).then(res => {
          this.test_data_info = res.data.data.app_info

          this.test_id = res.data.data.test_id
          console.log('jjjjjjjjjjjjjjjjjjjjjjjjjjjjjjjjj')
          this.form = res.data.data.form
          this.last_suite_code = res.data.data.last_suite_code
          this.jenkins_url = res.data.data.jenkins_url
        })
      }

      this.$Spin.hide();
      
    },
    select_bis_type() {
      this.$Spin.show({
        render: h => {
          return h("div", [
            h("Icon", {
              class: "demo-spin-icon-load",
              props: {
                type: "ios-loading",
                size: 18
              }
            }),
            h("div", "查询数据中......")
          ]);
        }
      });
      
      console.log('-----------------------------------')
      // console.log(this.bis_type)
      console.log(this.bis_type_list)
      console.log(this.formValidate)
      // if (this.formValidate.bis_type ==='')
      this.formValidate.sql_branch_name = ''
      getTestExecPlanBisRecord(this.formValidate).then(res => {
        console.log('获取bis记录')
        console.log(res.data.data)
        this.test_data_info = res.data.data.app_info
        this.formValidate.sql_branch_name = res.data.data.formValidate['sql_branch_name'];
        // if(this.first_change_flag === false){
        //   this.formValidate = res.data.data.formValidate;
        //   this.first_change_flag = true;
        // }
        // this.formValidate = res.data.data.formValidate
        // this.getBisBranch()
        this.test_id = res.data.data.test_id
        console.log('jjjjjjjjjjjjjjjjjjjjjjjjjjjjjjjjj')
        this.form = res.data.data.form
        this.last_suite_code = res.data.data.last_suite_code
        this.jenkins_url = res.data.data.jenkins_url
        console.log(this.form)
        console.log(this.formValidate)
      })
      this.getBisBranch()
      this.$Spin.hide();
    },
    selectChange(selectRow) {

      this.select_row_test_plan = selectRow
    },
    getBisTypeInfo() {
      getBisInfo().then(res => {
        this.bis_type_list = res.data.data

      })
    },
    getBisBranch() {

      getBisBranchInfo(this.formValidate).then(res => {
        this.test_data_branch_list = res.data.data.bis_iteration_info_list
        console.log('rrrrrrrrrrrrrrrrrrrrrrrrrrrrr')
        console.log(res.data.data)
        console.log(this.archive_bis_pipeline_id = res.data.data.close_bis_iter_list['bis_pipeline_id'])
        if (res.data.data.close_bis_iter_list.length >0){
          this.archive_bis_pipeline_id = res.data.data.close_bis_iter_list['bis_pipeline_id']
        }
      })

    },
    getTestExecRecord() {
      console.log('调用记录')
      console.log(this.formValidate)
      getTestExecPlanRecord(this.formValidate).then(res => {
        console.log('获取记录')
        console.log(res.data.data.app_info.length)
        if (res.data.data.app_info.length > 0) {
          this.test_data_info = res.data.data.app_info
          // this.bis_type = res.data.data.formValidate.bis_type;
          //   this.sql_branch_name =res.data.data.formValidate.bis_type
          if (this.first_change_flag === false) {
            this.formValidate = res.data.data.formValidate;
            this.first_change_flag = true;
          }
          // this.getBisBranch()
          this.test_id = res.data.data.test_id
          this.form = res.data.data.form
          this.last_suite_code = res.data.data.last_suite_code
          this.jenkins_url = res.data.data.jenkins_url
        }
        
        this.getBisBranch()

      })
      console.log(this.form)
      console.log(this.formValidate)
    },
    getTestExecBisRecord() {
      getTestExecPlanBisRecord(this.formValidate).then(res => {
        console.log('获取bis记录')
        console.log(res.data.data)
        this.test_data_info = res.data.data.app_info
        this.formValidate = res.data.data.formValidate
        // if(this.first_change_flag === false){
        //   this.formValidate = res.data.data.formValidate;
        //   this.first_change_flag = true;
        // }
        // this.formValidate = res.data.data.formValidate
        // this.getBisBranch()
        this.test_id = res.data.data.test_id
        this.form = res.data.data.form
        this.jenkins_url = res.data.data.jenkins_url
        console.log(this.form)
        console.log(this.formValidate)
        getBisBranchInfo(this.formValidate).then(res => {
          console.log('999999999999999999999999999999')
          this.test_data_branch_list = res.data.data
          console.log(this.test_data_branch_list)
        })
      })
    },
    init() {
      this.getBisTypeInfo()
      this.getTestExecRecord()
      this.getBisBranch()
    },
    checkBranchNameLenth() {
    },
    query_recommend_version() {
    },
    handleSubmit(formValidate, setting_time, test_id, form) {
      console.log(this.$refs.my_table)
      this.getSuite()
      const dayjs = require('dayjs');
      let date = this.setting_time
      this.setting_time = dayjs(date).format('YYYY-MM-DD HH:mm:ss')
      if (this.test_id == '') {
        this.test_id = 0
      }
      console.log(this.form)
      if (this.select_row_test_plan.length > 0) {
        for (let it in this.select_row_test_plan) {
          if (Object.keys(this.test_branch_dict).indexOf(this.select_row_test_plan[it].app_name) > -1) {
            console.log('rrrrrrrrrrrrrrrrrrrrrrrrrrr')
            console.log(this.test_branch_dict[this.select_row_test_plan[it].app_name])
            this.select_row_test_plan[it].br_name = this.test_branch_dict[this.select_row_test_plan[it].app_name].br_name
          }
          // if (this.test_branch_dict[this.select_row_test_plan[it].app_name] === undefined){
          //   alert(this.select_row_test_plan[it].app_name + '没有选择分支')
          //   return true
          // }

          this.select_row_test_plan[it].branch_name = this.select_row_test_plan[it].br_name
        }

      } else {
        for (let item in this.test_data_info) {
          console.log('nnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnn')
          if (this.test_data_info[Number(item)]._checked == true) {
            this.test_data_info[Number(item)].branch_name = this.test_data_info[Number(item)].br_name
            this.select_row_test_plan.push(this.test_data_info[Number(item)])
          }
        }
      }
      for (let item in this.select_row_test_plan) {
        if (this.select_row_test_plan[item].br_name === undefined || this.select_row_test_plan[item].br_name === '' || this.select_row_test_plan[item].br_name === null) {
          console.log('没有选择分支')
          console.log(this.select_row_test_plan[item].app_name)
          alert(this.select_row_test_plan[item].app_name + '没有选择分支')
          return true
        }
      }

      this.select_env_modal_show = true
      // testDataSaveExecPlan(this.select_row_test_plan, this.formValidate, this.setting_time, this.test_id, this.form).then(res =>{

      // })
      console.log(this.select_row_test_plan)
      console.log(this.form.init_ccms)
      console.log(this.form.clean_cache)
      console.log(this.init_db)
      console.log(this.test_id)
    },
    submitTestPlanExec() {
      this.db_test_init_multi_info = {
        "env_id": this.suite_info_dict.env_id,
        "env_name": this.suite_info_dict.suite_code,
        "init_db": "None",
        "ccms_type": this.form.init_ccms,
        "use_env_time": this.setting_time,
        "clean_cache": this.form.clean_cache,
        "test_set_id": this.test_id,
        "app_dict_list": this.select_row_test_plan,
        "bis_pipeline_id": this.formValidate.bis_type + '_' + this.formValidate.sql_branch_name
      }
      this.select_env_modal_show = false
      console.log('00000000000000000000000000000')
      console.log(this.db_test_init_multi_info)
      dbTestInitMultiPush(this.db_test_init_multi_info).then(res => {
        console.log(res.data.status)
        if (res.data.status == 'success') {
          this.$Notice.success({
            title: 'success',
            desc: '多推流水线调用成功'
          });

        } else {
          this.$Notice.error({
            title: 'error',
            desc: '多推流水线调用失败',
          });
        }
      })
    },
    cancelSubmitTestPlanExec() {
      this.select_env_modal_show = false
    },
    select_suite_code() {
      for (let item in this.suite_item_list) {
        console.log(this.suite_item_list[item]['suite_code'])
        console.log(this.suite_info)
        if (this.suite_item_list[item]['suite_code'] == this.suite_info) {
          this.suite_info_dict = this.suite_item_list[item]
          console.log(this.suite_info_dict)
        }
      }
    },
    getSuite() {
      getSuiteInfo().then(res => {
        this.suite_item_list = res.data.data["data_list"]
      })
    },
    historyList(){
      this.history_modal = true
    },
    goToDetail(){
      window.open(this.jenkins_url)
    }
  }

};
</script>

<style scoped>
.demo-spin-icon-load {
  animation: ani-demo-spin 1s linear infinite;
}
</style>
