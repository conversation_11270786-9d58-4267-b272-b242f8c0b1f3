<template>
    <Card shadow>
        <Row style="margin: 5px">
            <i-col style="margin: 1em" span="2">
                <span style="text-align: left; display: inline-block;">迭代版本</span>
            </i-col>
            <i-col style="margin: 1em" span="12">
                <span style="text-align: left; display: inline-block;">{{ this.pipeline_id }}</span>
            </i-col>
        </Row>
        <Row>
            <i-col style="margin: 5px">
                <!--        <Button ghost icon="md-mail" style="margin: 0.5em;" @click="do_publish_apply('uat')" type="primary">仿真申请-->
                <!--        </Button>-->
                <!--        <Button ghost icon="md-mail" style="margin: 0.5em;" @click="do_publish_apply('hd')" type="primary">灰度申请-->
                <!--        </Button>-->
                <Button ghost icon="md-mail" style="margin: 0.5em;" @click="online_apply" type="primary"
                >产线申请
                </Button
                >
                <!--<Button ghost icon="md-mail" style="margin: 0.5em;" @click="do_publish_apply('prod')" type="primary">产线申请</Button>-->

                <!-- <Button ghost icon="md-mail" style="margin: 0.5em;"  type="primary">发布确认申请</Button>-->
                <Button ghost @click="showCancelProApplyModal" style="margin: 0.5em;" type="error">取消产线申请</Button>
                <Button ghost style="margin: 0.5em;" @click="showArchiveAckModal" type="success"
                >上线完成(含归档)
                </Button
                >
                <Button type="text" @click="show_process_modal = true">同应用不同迭代版本，上线互斥流程</Button>
            </i-col>
        </Row>
        <Row style="margin: 5px">
            <i-col style="margin: 1em" span="2">
                <span style="text-align: left; display: inline-block;"><span style="color: red">*</span>邮件列表</span>
            </i-col>
            <i-col style="margin: 5px" span="18">
                <Select placeholder="邮箱地址" style="margin-top: 5px" v-model="allSelectedMails" filterable multiple>
                    <Option v-for="item in allFilterMails" :value="item" :key="item">{{ item }}</Option>
                </Select>
            </i-col>
        </Row>
        <Row style="margin: 5px">
            <i-col style="margin: 1em" span="2">
                <span style="text-align: left; display: inline-block;">自助确认申请</span>
            </i-col>
            <i-col style="margin: 5px" span="18">
                <i-button @click="showConfirmModal">选择确认人</i-button>
            </i-col>
        </Row>
        <Row style="margin: 5px">
            <i-col style="margin: 1em" span="2">
                <span style="text-align: left; display: inline-block;">预计上线时间</span>
            </i-col>
            <i-col style="margin: 5px" span="18">
                <DatePicker type="date" v-model="date" placeholder="Select date" style="width: 8em"></DatePicker>
                <TimePicker
                    format="HH:mm"
                    v-model="time"
                    placeholder="Select time"
                    style="margin: 1em;width: 6em;"
                ></TimePicker>
            </i-col>
        </Row>
        <Row style="margin: 5px">
            <i-col style="margin: 1em" span="2">
                <span style="text-align: left; display: inline-block;">功能描述</span>
            </i-col>
            <i-col style="margin: 5px" span="18">
                <Input type="textarea" placeholder="功能描述" v-model="feature"/>
            </i-col>
        </Row>
        <Row style="margin: 5px">
            <i-col style="margin: 1em" span="20">
                <Table :columns="app_columns" :data="app_detail"></Table>
            </i-col>
        </Row>
        <Row style="margin: 5px">
            <i-col style="margin: 1em" span="2">
                <span style="text-align: left; display: inline-block;">SQL</span>
            </i-col>
            <i-col style="margin: 5px" span="18">
                <Input type="textarea" placeholder="SQL内容" v-model="sql_content"/>
            </i-col>
        </Row>
        <Row style="margin: 5px">
            <i-col style="margin: 1em" span="2">
                <span style="text-align: left; display: inline-block;">调度</span>
            </i-col>
            <i-col style="margin: 5px" span="18">
                <Input type="textarea" placeholder="调度内容" v-model="schedule"/>
            </i-col>
        </Row>
        <Row style="margin: 5px">
            <i-col style="margin: 1em" span="2">
                <span style="text-align: left; display: inline-block;">配置</span>
            </i-col>
            <i-col style="margin: 5px" span="18">
                <Input type="textarea" placeholder="文件配置或ccms配置" v-model="file_ccms_config"/>
            </i-col>
        </Row>
        <Row style="margin: 5px">
            <i-col style="margin: 1em" span="2">
                <span style="text-align: left; display: inline-block;">注意事项</span>
            </i-col>
            <i-col style="margin: 5px" span="18">
                <Input type="textarea" :autosize="{ minRows: 2, maxRows: 5 }" placeholder="注意事项" v-model="notice"/>
            </i-col>
        </Row>
        <Row style="margin: 5px">
            <i-col>
                <Button @click="savePlan" style="margin: 1px" type="primary">保存</Button>
            </i-col>
        </Row>
        <Modal v-model="modal_cancel_pro_apply">
            <p slot="header" style="color:gray;">
                <Icon type="md-clipboard"></Icon>
                <span>取消产线申请</span>
            </p>
            <span>{{ modal_cancel_prod_apply_title }}</span>
            <div slot="footer">
                <Button @click="closeProApplyModal">关闭</Button>
                <Button ghost type="error" @click="cancelProApplyAck">确定</Button>
            </div>
        </Modal>
        <Modal v-model="modal_archive_ack" width="1000">
            <p slot="header" style="color:gray;">
                <Icon type="md-clipboard"></Icon>
                <span>迭代归档</span>
            </p>
            <span>确定将 {{ this.pipeline_id }} 归档?</span>
            <Row>
                <span v-if="depend_relation">这些不同库下的依赖应用也会归档：{{ this.depend_relation }}</span>
            </Row>
            <div style="margin: 20px 0;" v-if="reasonValidate.length > 0">
                <Table :columns="reasonColumns" :data="reasonValidate" height="500"></Table>
            </div>
            <div slot="footer">
                <Button @click="closeArchiveAckModal">关闭</Button>
                <Button ghost type="success" @click="do_archive_apply">确定</Button>
            </div>
        </Modal>
        <Modal v-model="modal_repo">
            <p slot="header" style="color:gray;">
                <Icon type="md-clipboard"></Icon>
                <span>制品差异</span>
            </p>
            <span v-html="m_git_diff"></span>
            <div slot="footer">
                <Button @click="closeRepoModal">关闭</Button>
            </div>
        </Modal>
        <Modal width="560" v-model="modal_confirm" @on-ok="confirmOk" @on-cancel="cancelConfirm">
            <p slot="header" style="color:gray;">
                <Icon type="md-clipboard"></Icon>
                <span>产线发布确认人</span>
            </p>
            <Select placeholder="邮箱地址" style="margin-top: 5px" v-model="allNotifyMails" filterable multiple>
                <Option v-for="item in allFilterMails" :value="item" :key="item">{{ item }}</Option>
            </Select>
        </Modal>
        <Modal :mask-closable="false" :closable="false" v-model="apply_modal" width="60em" title="申请反馈">
            <div v-scrolltop style="height:300px; overflow-y: auto;">
                <p v-for="(item, index) in apply_log" :key="index">{{ item }}</p>
            </div>
            <div slot="footer">
                <Button @click="closeApplyModal">关闭</Button>
            </div>
        </Modal>
        <Modal :mask-closable="false" :closable="false" v-model="archive_modal" width="60em" title="归档日志">
            <div v-scrolltop style="height:300px; overflow-y: auto;">
                <p v-for="(item, index) in archive_log" :key="index">{{ item }}</p>
            </div>
            <div slot="footer">
                <Button @click="closeArchiveModal">关闭</Button>
            </div>
        </Modal>
        <Modal :mask-closable="false" :closable="false" v-model="apply_fail_modal" width="40em" title="调用结果">
            <div style="height:200px; overflow-y: auto;">
                <span>{{ apply_fail_content }}</span>
            </div>
            <div slot="footer">
                <Button @click="closeApplyModal">关闭</Button>
            </div>
        </Modal>
        <Modal
            :title="title_info"
            width="800"
            v-model="online_content_confirm"
            :mask-closable="true"
            ok-text="确认"
            @on-ok="onlineContentConfirm"
        >
            <div>
                <Row>
                    <Table
                        :loading="tableLoading"
                        :columns="tableColumns"
                        :data="tableData"
                        :no-data-text="noDataText"
                        ref="online_content_table"
                    >
                    </Table>
                </Row>
                <Row v-show="showSql">
                    <Table
                        :loading="tableLoading"
                        :columns="sqlInfotableColumns"
                        :data="sqlInfoData"
                        :no-data-text="noDataText"
                        ref="online_sql_content_table"
                    >
                    </Table>
                </Row>
            </div>
        </Modal>
        <Modal
            title="同应用不同迭代版本，上线互斥流程说明图"
            width="930"
            v-model="show_process_modal"
            :mask-closable="true"
        >
            <div style="width: 120%">
                <img :src="imgUrl" style="width: 900px"/>
            </div>
        </Modal>
        <!--<input type="text" :value="message" @input="message = $event.target.value" >-->
        <CheckNotice
            v-model="check_notice"
            :check_notice_title="check_notice_title"
            :check_table_data="check_notice_data"
            :check_notice_msg="check_notice_msg"
        ></CheckNotice>
    </Card>
</template>

<script>
import store from '@/spider-store'

import {
    getRepoDiff,
    publishApply,
    getProdApplyInfo,
    getApplyNotice,
    getArchiveNotice,
    proApplyNotice,
    getPlanInfo,
    setPlanInfo,
    cancelProApply,
    getServiceResult,
    archiveApi,
    archiveCheckApi,
    applyCheckEmail,
    getOnlineApplyTableInfo,
    getEmailAddresses,
    getDependRelation,
    getOnlineSqlApplyApi,
    getNeedReasonNodeInfoApi,
    createPublishReasonApi,
    getUserArcheryTokenIsTimeout,
    loginArchery,
    ServersPublishApplyApi, cancel_prod_apply
} from '@/spider-api/iter-plan'
import {checkConfigConsistentApi, fileConfigBranch} from '@/spider-api/zues'
import {recordProdApply} from '@/spider-api/mgt-env'
import {GetMobileCiInfo} from '@/spider-api/ci/mobile-ci/mobile-ci-info'

import CheckNotice from '@/spider-components/spider-notice/check-notice'

const OP_TYPE_CN = {
    deploy: '发布',
    update_and_deploy: '配置更新+发布',
    restart: '重启',
    stop: '停止',
    rollback: '回滚',
    update: '配置更新',
    code_update: '代码更新',
    update_and_deploy_and_verify: 'jenkins批量发布'
}

export default {
    name: 'SpiderGitIterativePlan',
    components: {CheckNotice},
    data() {
        return {
            passwd: '',
            check_notice: false,
            check_notice_data: [],
            check_notice_title: '校验失败',
            check_notice_msg: '',
            show_process_modal: false,
            imgUrl: require('../../img/上线互斥流程图.png'),
            title_info: '上线内容确认',
            online_content_confirm: false,
            is_close_spin: false,
            env_name: '',
            env_cn_name: {uat: '仿真', hd: '灰度', prod: '产线'},
            switch_history: '',
            switch_service_result: '',
            switch_archive_history: '',
            pipeline_id: '',
            apply_modal: false,
            apply_fail_modal: false,
            apply_fail_content: '',
            modal_cancel_pro_apply: false,
            modal_cancel_prod_apply_title: '',
            modal_archive_ack: false,
            apply_log: '',
            modal_confirm: false,
            allNotifyMails: [],
            index: '',
            archive_modal: false,
            archive_log: '',
            sql_content: '',
            schedule: '',
            file_ccms_config: '',
            date: '',
            time: '',
            modal_repo: false,
            modal_config: false,
            m_app_config: '',
            m_git_diff: '',
            app_detail: [],
            app_name_list: [],
            send_email: false,
            notice: '',
            feature: '',
            allMails: [],
            allSelectedMails: ['<EMAIL>'],
            allFilterMails: [],
            depend_relation: '',
            showSql: false,
            finish_table_columns: [
                {
                    title: '应用名',
                    key: 'app_name'
                },
                {
                    title: 'IP地址',
                    key: 'ip'
                },
                {
                    title: '已发布版本',
                    key: 'pro_version',
                    width: 300
                },
                {
                    title: '制品库版本',
                    key: 'git_version',
                    width: 300
                },
                {
                    title: '版本比较',
                    key: 'status',
                    render: (h, params) => {
                        let tag_color = ''
                        let tag_text = ''

                        switch (params.row.status) {
                            case 0:
                                tag_color = 'success'
                                tag_text = '一致'
                                break
                            case 1:
                                tag_color = 'error'
                                tag_text = '不一致'
                                break
                            case 2:
                                tag_color = 'warning'
                                tag_text = '未发布'
                                break
                        }

                        return h(
                            'Tag',
                            {
                                props: {
                                    color: tag_color
                                }
                            },
                            tag_text
                        )
                    }
                }
            ],
            app_columns: [
                {
                    title: '上线应用',
                    key: 'app_name',
                    width: 220,
                    render: (h, params) => {
                        return h('div', [h('p', {style: {}}, params.row.app_name)])
                    }
                },
                {
                    title: '宙斯配置信息',
                    align: 'left',
                    width: 110,
                    render: (h, params) => {
                        return h('div', [
                            h(
                                'Button',
                                {
                                    props: {
                                        size: 'small'
                                    },
                                    style: {},
                                    on: {
                                        click: () => {
                                            this.showAppConfig(params.row.app_name)
                                        }
                                    }
                                },
                                '查看配置'
                            )
                        ])
                    }
                },
                {
                    title: '产线运行版本',
                    key: 'online_branch',
                    align: 'left',
                    width: 320
                },
                {
                    title: '版本差异',
                    align: 'left',
                    render: (h, params) => {
                        return h('div', [
                            h(
                                'Button',
                                {
                                    props: {
                                        size: 'small'
                                    },
                                    style: {},
                                    on: {
                                        click: () => {
                                            this.showGitDiff(this.pipeline_id, params.row.app_name)
                                        }
                                    }
                                },
                                '详细信息'
                            )
                        ])
                    }
                }
            ],
            noDataText: '相关应用信息查询为空',
            tableLoading: false,
            tableData: [],
            sqlInfoData: [],
            sqlInfotableColumns: [
                {
                    title: '选择',
                    type: 'selection',
                    align: 'center',
                    width: 50
                },
                {
                    title: '数据库',
                    key: 'sql_ver_db',
                    align: 'center'
                },
                {
                    title: 'SQL文件',
                    key: 'sql_ver_name',
                    align: 'center'
                }
            ],
            tableColumns: [
                {
                    title: '选择',
                    type: 'selection',
                    align: 'center',
                    width: 50
                },
                {
                    title: '内容',
                    key: 'app_name',
                    align: 'center'
                },
                {
                    title: '制品/邮件表单更新时间',
                    key: 'update_time',
                    align: 'center'
                },
                {
                    title: '上一次申请时间',
                    key: 'last_push_time',
                    align: 'center'
                }
            ],
            reasonColumns: [
                // {
                //     title: '应用名',
                //     key: 'app_name',
                //     width: 150
                // },
                // {
                //     title: '环境',
                //     key: 'suite_name',
                //     width: 100
                // },
                // {
                //     title: '节点',
                //     key: 'node_ip',
                //     width: 140
                // },
                // {
                //     title: '操作人',
                //     key: 'op_user',
                //     width: 100
                // },
                // {
                //     title: '操作时间',
                //     key: 'op_time',
                //     render: (h, params) => {
                //         return h(
                //             'span',
                //             params.row.op_time
                //                 ? this.dateFormat('YYYY-mm-dd HH:MM:SS', new Date(params.row.op_time))
                //                 : ''
                //         )
                //     }
                // },
                // {
                //     title: '操作类型',
                //     key: 'op_type',
                //     render: (h, params) => {
                //         return h('span', OP_TYPE_CN[params.row.op_type])
                //     }
                // },
                {
                    title: '重复产线申请时间',
                    key: 'apply_at'
                },
                {
                    title: '申请人',
                    key: 'applicant'
                },
                {
                    title: '发布统计',
                    key: 'publish_detail'
                },
                {
                    title: '原因',
                    key: 'opt_reason',
                    width: 220,
                    render: (h, params) => {
                        return h('Input', {
                            props: {
                                value: params.row.opt_reason,
                                type: 'textarea',
                                autosize: {minRows: 1, maxRows: 3},
                                placeholder: '请输入原因',
                                maxlength: 100
                            },
                            on: {
                                input: value => {
                                    params.row.opt_reason = value
                                    this.reasonValidate.map(item => {
                                        if (item.id === params.row.id) {
                                            item.opt_reason = value
                                        }
                                        return item
                                    })
                                },
                                'on-blur': event => {
                                    if (event.target.value.length < 10) {
                                        this.$Message.error('原因至少包含10个字符，请重新输入!')
                                    }
                                }
                            }
                        })
                    }
                }
            ],
            reasonValidate: []
        }
    },
    watch: {},
    methods: {
        dateFormat(fmt, date) {
            let ret
            const opt = {
                'Y+': date.getFullYear().toString(), // 年
                'm+': (date.getMonth() + 1).toString(), // 月
                'd+': date.getDate().toString(), // 日
                'H+': date.getHours().toString(), // 时
                'M+': date.getMinutes().toString(), // 分
                'S+': date.getSeconds().toString() // 秒
                // 有其他格式化字符需求可以继续添加，必须转化成字符串
            }
            for (let k in opt) {
                ret = new RegExp('(' + k + ')').exec(fmt)
                if (ret) {
                    fmt = fmt.replace(ret[1], ret[1].length == 1 ? opt[k] : opt[k].padStart(ret[1].length, '0'))
                }
            }
            return fmt
        },
        online_apply() {
            this.title_info = '上线内容确认'
            getOnlineApplyTableInfo(store.state.iterationID).then(res => {
                if (res.data.status === 'success') {
                    this.tableData = res.data.data.table_info
                    if (this.tableData == null || this.tableData.length === 0) {
                    } else {
                        this.tableData.forEach(element => {
                            if (element.update_time) {
                                element.update_time = this.dateFormat(
                                    'YYYY-mm-dd HH:MM:SS',
                                    new Date(element.update_time)
                                )
                            }
                            if (element.last_push_time) {
                                element.last_push_time = this.dateFormat(
                                    'YYYY-mm-dd HH:MM:SS',
                                    new Date(element.last_push_time)
                                )
                            }
                        })
                    }
                    if (res.data.data.warn_info !== null && res.data.data.warn_info.length !== 0) {
                        this.title_info = '上线内容确认' + '(注意：代码仓库下有拉取后删除应用,若有代码改动会归档到主干)'
                    }
                } else {
                    this.$Message.error(res.data.msg)
                }
            })
            this.online_content_confirm = true
            getOnlineSqlApplyApi(store.state.iterationID).then(res => {
                if (res.data.status === 'success') {
                    this.showSql = true
                    this.sqlInfoData = res.data.data
                    console.log(res.data.data)
                } else {
                    this.showSql = false
                }
            })
        },

        onlineContentConfirm() {
            let selectList = this.$refs.online_content_table.getSelection()
            let sqlSelectList = this.$refs.online_sql_content_table.getSelection()
            console.info(selectList)
            console.info(sqlSelectList)
            if (selectList.length === 0) {
                this.$Message.error('选择申请应用为空！！')
                return
            }

            // 判断是否需要输入密码
            if (sqlSelectList.length > 0) {
                getUserArcheryTokenIsTimeout()
                    .then(res => {
                        if (res.data.status === 'success') {
                            // token过期了
                            this.$Modal.confirm({
                                title: '输入密码，登录产线archery!',
                                render: h => {
                                    return h('Input', {
                                        props: {
                                            type: 'password',
                                            value: this.passwd,
                                            autofocus: true,
                                            placeholder: 'Please enter your password...'
                                        },
                                        on: {
                                            input: val => {
                                                this.passwd = val
                                            }
                                        }
                                    })
                                },
                                onOk: () => {
                                    // 用户点击确定后的逻辑
                                    let passwordValue = this.passwd

                                    loginArchery({passwd: passwordValue}).then(res => {
                                        if (res.data.status === 'success') {
                                            this.handlePasswordConfirmation(selectList)
                                        } else {
                                            this.$Message.error(res.data.msg)
                                        }
                                    })
                                },
                                onCancel: () => {
                                    // 用户点击取消后的逻辑
                                    // 清空密码输入框
                                    this.passwd = ''
                                }
                            })
                        } else {
                            this.handlePasswordConfirmation(selectList)
                        }
                    })
                    .catch(e => {
                        console.log(e)
                    })
            } else {
                // 没有需要输入密码的情况下直接继续后面的逻辑
                this.handlePasswordConfirmation(selectList)
            }
        },
        handlePasswordConfirmation(selectList) {
            let app_name_list = []
            let email = false
            for (let selectListElement of selectList) {
                if (selectListElement.app_name === '申请结果邮件') {
                    email = true
                } else {
                    app_name_list.push(selectListElement.app_name)
                }
            }
            if (app_name_list.length === 0 && email) {
                applyCheckEmail(store.state.iterationID)
                    .then(res => {
                        if (!res.data.data) {
                            this.showConfirmModal()
                        }
                    })
                    .catch(err => {
                    })
            }

            console.info(app_name_list)
            console.info(email)
            this.app_name_list = app_name_list
            this.send_email = email
            this.do_publish_apply('prod')
            let app_name_param = []
            for (let item in this.app_name_list) {
                app_name_param.push({app_name: this.app_name_list[item], iterationID: store.state.iterationID})
            }
            recordProdApply('publish_apply', app_name_param).then(res => {
                console.log(res.data.data)
            })
        },

        savePlanQuiet() {
            let data = {}
            data['receivers'] = Array.from(new Set(this.allSelectedMails)).join(',')
            data['pipeline_id'] = store.state.iterationID
            data['date'] = this.date
            data['time'] = this.time
            data['feature'] = this.feature
            data['sql_content'] = this.sql_content
            data['schedule'] = this.schedule
            data['file_ccms_config'] = this.file_ccms_config
            data['notice'] = this.notice
            setPlanInfo(data)
                .then(res => {
                    if (res.data.status !== 'success') {
                        this.$Message.error(res.data.msg)
                    }
                })
                .catch(err => {
                    this.$Message.error(err.response.data.msg)
                })
        },
        savePlan() {
            let data = {}
            data['receivers'] = Array.from(new Set(this.allSelectedMails)).join(',')
            data['pipeline_id'] = store.state.iterationID
            data['date'] = this.date
            data['time'] = this.time
            data['feature'] = this.feature
            data['sql_content'] = this.sql_content
            data['schedule'] = this.schedule
            data['file_ccms_config'] = this.file_ccms_config
            data['notice'] = this.notice
            setPlanInfo(data)
                .then(res => {
                    if (res.data.status === 'success') {
                        this.$Message.success(res.data.msg)
                    } else {
                        this.$Message.error(res.data.msg)
                        this.initThisVue()
                    }
                })
                .catch(err => {
                    this.$Message.error(err.response.data.msg)
                })
        },
        initThisVue() {
            this.pipeline_id = store.state.iterationID
            getEmailAddresses().then(res => {
                if (res.data.status === 'success') {
                    this.allMails = res.data.data
                    this.allFilterMails = this.allMails
                }
            })
            getPlanInfo(store.state.iterationID)
                .then(res => {
                    this.app_name_list = []
                    if (res.data.status === 'success') {
                        this.sql_content = res.data.data['sql_content']
                        this.schedule = res.data.data['schedule']
                        this.file_ccms_config = res.data.data['file_ccms_config']
                        this.notice = res.data.data['notice']
                        this.feature = res.data.data['feature']
                        this.date = res.data.data['date']
                        this.time = res.data.data['time']
                        this.allSelectedMails = res.data.data['receivers']
                        if (this.allSelectedMails.length === 0) {
                            this.allSelectedMails.push('<EMAIL>')
                        }
                        this.app_detail = res.data.data['app_detail']
                        for (let i = 0; i < this.app_detail.length; i++) {
                            this.app_name_list.push(this.app_detail[i]['app_name'])
                        }
                    } else {
                        this.$Message.error(res.data.msg)
                    }
                })
                .catch(err => {
                    this.$Message.error(err.response.data.msg)
                })
        },
        showGitDiff(pipeline_id, app_name) {
            getRepoDiff(pipeline_id, app_name)
                .then(res => {
                    if (res.data.status === 'success') {
                        this.modal_repo = true
                        this.m_git_diff = res.data.data['info']
                    } else {
                        this.$Message.error(res.data.msg)
                    }
                })
                .catch(err => {
                    this.$Message.error(err.response.data.msg)
                })
        },
        closeRepoModal() {
            this.modal_repo = false
            this.m_git_diff = ''
        },
        closeArchiveModal() {
            this.archive_modal = false
            this.archive_log = ''
            if (this.switch_archive_history) {
                clearInterval(this.switch_archive_history)
            }
        },
        closeApplyModal() {
            this.apply_modal = false
            this.apply_fail_modal = false
            if (this.switch_history) {
                clearInterval(this.switch_history)
            }
        },

        // 提取 失败告警
        alertErrorNotice(publish_apply_message) {
            this.$Spin.hide()
            console.log(publish_apply_message)
            console.log(typeof publish_apply_message)
            // 返回的错误类型可能不是json格式 20220329 by 帅
            try {
                this.check_notice_data = JSON.parse(publish_apply_message.replace(/'/g, '"'))
            } catch (e) {
                // 无法解析 需要清除 table数据 防止无法显示msg提示
                this.check_notice_data = []
                this.check_notice_msg = publish_apply_message
                console.log(e)
            }
            this.check_notice_title = '！！！！！！！！失败告警！！！！！！！！！'
            this.check_notice = true
        },
        alertNotice(publish_apply_message) {
            //  this.$Spin.hide();
            console.log(publish_apply_message)
            console.log(typeof publish_apply_message)
            // 返回的错误类型可能不是json格式 20220329 by 帅
            try {
                this.check_notice_data = publish_apply_message
            } catch (e) {
                // 无法解析 需要清除 table数据 防止无法显示msg提示
                this.check_notice_data = []
                this.check_notice_msg = publish_apply_message
                console.log(e)
            }
            this.check_notice_title = '！！！！！！！！归档详情！！！！！！！！！'
            this.check_notice = true
        },
        getServersPublishApplyInfo(iter_id, action_item_list, app_type_list) {
            GetMobileCiInfo({
                iteration_id: iter_id,
                package_type: app_type_list,
                action_item: action_item_list,
                app_name_list: this.app_name_list
            }).then(res => {
                let publish_status = 'success'
                let publish_apply_message = ''
                console.log(res.data.data.publish_info)
                // 刷新产线申请数据 by 帅
                for (let row of res.data.data.publish_info) {
                    //console.log(res.data.data.publish_info)
                    if (row['publish_apply_status'].indexOf('running') > -1) {
                        publish_status = 'running'
                        break
                    } else if (row['publish_apply_status'].indexOf('failure') > -1) {
                        publish_status = 'publish_failure'
                        publish_apply_message = row['publish_apply_message']
                        break
                    }
                }
                console.log(publish_status)
                if (publish_status == 'running') {
                    let vm = this
                    setTimeout(function () {
                        vm.getServersPublishApplyInfo(iter_id, action_item_list, app_type_list)
                    }, 3000)
                } else if (publish_status == 'publish_failure') {
                    this.alertErrorNotice(publish_apply_message)
                } else if (publish_status == 'success') {
                    //this.$Spin.hide();
                    console.log('校验通过')
                    // 校验成功后执行发布动作
                    publishApply(store.state.iterationID, this.env_name, this.app_name_list, this.send_email)
                        .then(result => {
                            if (result.data.status === 'success') {
                                this.$Message.success(result.data.msg)
                                let sid = result.data.data.sid
                                this.get_check_service_result(sid, 'publish_apply')
                            } else {
                                this.alertErrorNotice(result.data.msg)
                                // alert(result.data.msg);
                                // this.$Spin.hide();
                            }
                        })
                        .catch(err => {
                            this.$Spin.hide()
                        })
                }
            })
        },
        doApply() {
            //第一期先将 宙斯 和 校验逻辑改到后端执行 by帅 20230117
            var sqlSelectList = this.$refs.online_sql_content_table.getSelection()
            if (!sqlSelectList) {
                sqlSelectList = []
            }
            console.log('sqlSelectList:%o', sqlSelectList)
            let apply_list = []
            for (let app_name of this.app_name_list) {
                apply_list.push({
                    suite_code: this.env_name,
                    iteration_id: store.state.iterationID,
                    action_item: 'servers_publish_apply',
                    app_name: app_name,
                    sql_check_list: [...sqlSelectList]
                })
            }
            ServersPublishApplyApi(apply_list).then(result => {
                if (result.data.status === 'success') {
                    console.log(result.data.msg)
                    this.getServersPublishApplyInfo(
                        store.state.iterationID,
                        ['servers_publish_apply'],
                        ['war', 'jar', 'py', 'tar']
                    )
                } else {
                    alert(result.data.msg)
                    this.$Spin.hide()
                }
            })
        },

        doArchiveCheck() {
            archiveCheckApi(store.state.iterationID)
                .then(result => {
                    if (result.data.status === 'success') {
                        let sid = result.data.data.sid
                        this.get_check_service_result(sid, 'archive_check')
                    } else {
                        this.alertErrorNotice(result.data.msg)
                        this.$Spin.hide()
                    }
                })
                .catch(err => {
                    this.$Spin.hide()
                })
        },
        doArchive() {
            console.log('this.app_name_list' + this.app_name_list)
            checkConfigConsistentApi(store.state.iterationID, this.app_name_list, 'prod').then(result => {
                if (result.data.status === 'success') {
                    // 执行配置归档
                    fileConfigBranch(store.state.iterationID, this.app_name_list, 'prod').then(result => {
                        if (result.data.status === 'success') {
                            archiveApi(store.state.iterationID)
                                .then(result => {
                                    if (result.data.status === 'success') {
                                        //this.$Message.success(result.data.msg);
                                        let sid = result.data.data.sid
                                        this.get_check_service_result(sid, 'archive')
                                    } else {
                                        alert(result.data.msg)
                                        this.$Spin.hide()
                                    }
                                })
                                .catch(err => {
                                    this.$Spin.hide()
                                })
                        } else {
                            alert(result.data.msg)
                            this.$Spin.hide()
                        }
                    })
                } else {
                    alert(result.data.msg)
                    this.$Spin.hide()
                }
            })
        },

        do_publish_apply(env) {
            if (this.send_email) {
                if (this.allSelectedMails === '' || this.allSelectedMails.length <= 0) {
                    this.$Message.error('邮件列表不能为空，请选择邮件列表！！')
                    return
                }
            }
            this.savePlanQuiet()
            this.env_name = env
            this.$Spin.show({
                render: h => {
                    return h('div', [
                        h('Icon', {
                            class: 'demo-spin-icon-load',
                            props: {
                                type: 'ios-loading',
                                size: 18
                            }
                        }),
                        h('div', this.env_cn_name[this.env_name] + '申请中...')
                    ])
                }
            })
            if (env === 'prod') {
                if (this.send_email) {
                    applyCheckEmail(store.state.iterationID)
                        .then(res => {
                            if (!res.data.data) {
                                this.showConfirmModal()
                            }
                        })
                        .catch(err => {
                        })
                }
                //this.doApplyCheck()
                this.doApply()
            } else {
                this.doApply()
            }
        },
        //执行归档
        do_archive_apply() {
            const check = this.reasonValidate.every(item => {
                return item.opt_reason.length >= 10
            })
            // 校验是否有数据的“原因”字段小于10个字符
            if (!check) {
                this.$Message.error('归档原因字数不能少于10个字符，请检查后重新提交。')
                return
            }

            const hasNoneReason = this.reasonValidate.filter(item => !item.opt_reason)

            if (hasNoneReason.length > 0) {
                this.$Message.error('部分节点原因未填写，请检查并填写后再次提交。')
                return
            }

            createPublishReasonApi({
                publish_reason_list: this.reasonValidate.map(item => {
                    return {
                        // module_name: item.app_name,
                        // suite_code: item.suite_name,
                        // node_ip: item.node_ip,
                        // opt_type: item.op_type,
                        // opt_time: item.op_time,
                        // iteration_id: item.iteration_id,
                        // opt_reason: item.opt_reason
                        apply_at: item.apply_at,
                        applicant: item.applicant,
                        publish_detail: item.publish_detail,
                        iteration_id: item.iteration_id,
                        opt_reason: item.opt_reason
                    }
                })
            })
                .then(res => {
                    if (res.data && res.data.status === 'success') {
                        this.modal_archive_ack = false
                        this.$Spin.show({
                            render: h => {
                                return h('div', [
                                    h('Icon', {
                                        class: 'demo-spin-icon-load',
                                        props: {
                                            type: 'ios-loading',
                                            size: 18
                                        }
                                    }),
                                    h('div', '归档中...')
                                ])
                            }
                        })
                        this.doArchiveCheck()
                    } else {
                        this.$Message.error(res.data.msg)
                    }
                })
                .catch(ex => console.error(ex))
        },

        get_history_data() {
            getApplyNotice(store.state.iterationID, this.env_name)
                .then(res => {
                    this.apply_log = res.data.data
                })
                .catch(err => {
                })
        },

        confirmOk() {
            this.modal_confirm = false
            proApplyNotice(
                store.state.iterationID,
                this.allNotifyMails.join(','),
                'http://' + window.location.host,
                this.title_info,
                'prod'
            )
                .then(result => {
                    if (result.data.status === 'success') {
                        this.$Message.success(result.data.msg)
                    } else {
                        this.$Message.error(result.data.msg)
                    }
                })
                .catch(err => {
                    this.$Message.error(err.response.data.msg)
                })
        },
        cancelConfirm() {
            this.modal_confirm = false
        },
        showConfirmModal() {
            this.savePlanQuiet()
            getProdApplyInfo(store.state.iterationID)
                .then(result => {
                    if (result.data.status === 'success') {
                        this.modal_confirm = true
                    } else {
                        this.$Message.error(result.data.msg)
                    }
                })
                .catch(err => {
                    this.$Message.error(err.response.data.msg)
                })
        },
        showCancelProApplyModal() {
            this.modal_cancel_pro_apply = false
            this.modal_cancel_prod_apply_title = ''
            cancel_prod_apply(store.state.iterationID).then(res => {
                if (res.data.status === 'success') {
                    this.modal_cancel_prod_apply_title = res.data.msg
                    this.modal_cancel_pro_apply = true
                } else {
                    this.$Message.error(res.data.msg)
                }
            })
        },
        closeProApplyModal() {
            this.modal_cancel_pro_apply = false
        },
        closeArchiveAckModal() {
            this.modal_archive_ack = false
        },
        showArchiveAckModal() {
            this.reasonValidate = []
            getDependRelation(store.state.iterationID).then(res => {
                this.depend_relation = res.data.data
            })

            // 获取需要填写原因的节点列表
            getNeedReasonNodeInfoApi({
                iteration_id: store.state.iterationID
            })
                .then(res => {
                    if (res.data && res.data.status === 'success') {
                        const {data} = res.data
                        if (data.length > 0) {
                            data.forEach((item, index) => {
                                // this.reasonValidate.push({
                                //     id: index,
                                //     opt_reason: '',
                                //     app_name: item.app_name,
                                //     node_ip: item.node_ip,
                                //     op_time: item.op_time,
                                //     op_type: item.op_type,
                                //     op_user: item.op_user,
                                //     suite_name: item.suite_name,
                                //     iteration_id: item.iteration_id
                                // })
                                this.reasonValidate.push({
                                    id: index,
                                    opt_reason: item.opt_reason || '',
                                    apply_at: item.apply_at,
                                    applicant: item.applicant,
                                    publish_detail: item.publish_detail,
                                    iteration_id: store.state.iterationID
                                })
                            })
                        }
                    }
                })
                .catch(ex => console.error(ex))
            this.modal_archive_ack = true
        },

        get_check_service_result(sid, business_name) {
            getServiceResult(sid)
                .then(res => {
                    if (res.data.status === 'success') {
                        var status = res.data.data.status
                        var detail = res.data.data.detail
                        console.log(status, business_name, sid)
                        if (status === 'success') {
                            if (business_name === 'publish_check') {
                                this.doApply()
                            } else if (business_name === 'archive_check') {
                                this.doArchive()
                            } else {
                                if (detail) {
                                    let detail_new = []
                                    console.log('====================================================')
                                    console.log(detail)
                                    console.log(typeof detail)
                                    if (typeof detail == 'string') {
                                        alert(detail)
                                    } else {
                                        for (let i in detail[0]) {
                                            let cc = detail[0][i]
                                            detail_new.push(cc)
                                        }
                                        detail_new.push({msg: detail[1], status: '无'})
                                        // let detail_new = JSON.parse(detail.replace(/'/g, '"'))

                                        console.log(detail_new)
                                        this.alertNotice(detail_new)
                                    }

                                    // alert(detail);
                                } else {
                                    alert('执行成功')
                                }
                                this.$Spin.hide()
                            }
                        } else if (status === 'failure') {
                            if (detail) {
                                this.alertErrorNotice(detail)
                            } else {
                                this.alertErrorNotice('执行失败')
                            }
                            //this.$Spin.hide();
                        } else {
                            let vm = this
                            setTimeout(function () {
                                vm.get_check_service_result(sid, business_name)
                            }, 2000)
                        }
                    } else {
                        this.$Message.error(res.data.msg)
                        this.alertErrorNotice(res.data.msg)
                        //this.$Spin.hide();
                    }
                })
                .catch(err => {
                    alert(接口调用异常)
                    this.$Spin.hide()
                })
        },

        cancelProApplyAck() {
            this.modal_cancel_pro_apply = false
            cancelProApply(store.state.iterationID)
                .then(result => {
                    if (result.data.status === 'success') {
                        this.$Message.success(result.data.msg)
                    } else {
                        this.$Message.error(result.data.msg)
                    }
                })
                .catch(err => {
                    this.$Message.error(err.response.data.msg)
                })
            recordProdApply('cancle_publish_apply', store.state.iterationID).then(res => {
                console.log(res.data.data)
            })
        }
    },
    mounted() {
    }
}
</script>
