<!--
 * @Author: chao.bao <EMAIL>
 * @Date: 2025-05-28 09:32:01
 * @LastEditors: chao.bao <EMAIL>
 * @LastEditTime: 2025-08-11 17:10:47
 * @FilePath: /website_web/deploy_website/src/spider-components/AgentComponent/index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div>
        <div class="edit_btn">
            <Button type="primary" @click="editHandler">设置</Button>
        </div>
        <Table :columns="listColumns" :data="tableDataView" :height="300"></Table>
        <Modal v-model="showModal" :closable="false" :mask-closable="false" :width="1000">
            <div class="title_contain" slot="header">
                <div class="title">agent开关设置</div>
                <div class="tips">说明：应用与环境存在绑定关系才显示数据</div>
            </div>
            <div class="query_aera">
                <Select
                    v-model="app_list"
                    placeholder="应用名称(可多选)"
                    multiple
                    filterable
                    style="width:300px"
                    @on-change="changeApp"
                >
                    <Option v-for="item in appList" :value="item.value" :key="item.value" :label="item.label">{{
                        item.label
                    }}</Option>
                </Select>
                <Select
                    class="item_com"
                    v-model="suite_list"
                    placeholder="环境(可多选)"
                    multiple
                    filterable
                    style="width:300px"
                    @on-change="changeSuite"
                >
                    <Option v-for="item in suiteList" :value="item.value" :key="item.value" :label="item.label">{{
                        item.label
                    }}</Option>
                </Select>
                <Button class="item_com" type="primary" @click="openHandler">批量开</Button>
                <Button class="item_com" @click="closeHandler">批量关</Button>
            </div>
            <Table
                ref="tableRef"
                :columns="editColumns"
                :data="tableDataEdit"
                :height="300"
                @on-selection-change="changeSelect"
            ></Table>
            <div slot="footer">
                <Button @click="cancel">取消</Button>
                <Button type="primary" :loading="loading" @click="ok">保存</Button>
            </div>
        </Modal>
    </div>
</template>

<script>
import { getAgentUseInfo, saveAgentUserInfo, getPublishAppList, getAllTestingEnv } from './api'
import { deepClone } from '@/utils'
export default {
    data() {
        return {
            listColumns: [
                {
                    title: '应用名称',
                    key: 'app_name'
                },
                {
                    title: 'agent名称',
                    key: 'agent_name'
                },
                {
                    title: '环境',
                    key: 'suite_code',
                    width: 120
                },
                {
                    title: '开关',
                    key: 'is_use',
                    width: 100,
                    render: (h, params) => {
                        return h('div', params.row.is_use === 1 ? '开' : '关')
                    }
                }
            ],
            editColumns: [
                {
                    type: 'selection',
                    width: 60,
                    align: 'center'
                },
                {
                    title: '应用名称',
                    key: 'app_name'
                },
                {
                    title: 'agent名称',
                    key: 'agent_name',
                    sortable: true
                },
                {
                    title: '环境',
                    key: 'suite_code',
                    width: 100,
                    sortable: true
                },
                {
                    title: '开关',
                    key: 'is_use',
                    width: 100,
                    align: 'center',
                    render: (h, params) => {
                        return h('i-switch', {
                            props: {
                                value: params.row.is_use === 1 ? true : false,
                                disabled: params.row.forced_mount === '1' ? true : false
                            },
                            on: {
                                'on-change': status => {
                                    // 更新数据
                                    this.tableDataEdit[params.index].is_use = status ? 1 : '0'
                                    // 手动重置勾选框状态
                                    this.selectionData = this.$refs.tableRef.getSelection()
                                    this.$nextTick(() => {
                                        // 获取当前选中元素selectionData在tableDataEdit中的索引
                                        this.selectionData.map(item => {
                                            const index = this.tableDataEdit.findIndex(item2 => {
                                                return (
                                                    item2.app_name === item.app_name &&
                                                    item2.agent_name === item.agent_name &&
                                                    item2.suite_code === item.suite_code
                                                )
                                            })
                                            this.$refs.tableRef.toggleSelect(index)
                                        })
                                    })
                                }
                            },
                            scopedSlots: {
                                open: () => h('span', '开'),
                                close: () => h('span', '关')
                            }
                        })
                    }
                }
            ],
            tableDataView: [],
            tableDataEdit: [],
            showModal: false,
            appList: [],
            suiteList: [],
            app_list: [], // 应用名称选择
            suite_list: [], // 环境选择
            view_app_list: [], // 应用名称选择
            view_suite_list: [], // 环境选择
            selectionData: [],
            loading: false
        }
    },
    methods: {
        changeApp(val) {
            this.queryList(val, this.suite_list)
        },
        changeSuite(val) {
            this.queryList(this.app_list, val)
        },
        openHandler() {
            this.tableDataEdit.map(item => {
                if (item.forced_mount === '1') return
                // 如果item不再this.selectionData中，则return
                if (
                    !this.selectionData.find(
                        item2 => item2.app_name === item.app_name && item2.agent_name === item.agent_name
                    )
                )
                    return
                item.is_use = 1
            })
            // 手动重置勾选框状态
            this.selectionData = this.$refs.tableRef.getSelection()
            setTimeout(() => {
                // 获取当前选中元素selectionData在tableDataEdit中的索引
                this.selectionData.map(item => {
                    const index = this.tableDataEdit.findIndex(item2 => {
                        return (
                            item2.app_name === item.app_name &&
                            item2.agent_name === item.agent_name &&
                            item2.suite_code === item.suite_code
                        )
                    })
                    console.log('index', index)
                    // this.$refs.tableRef.toggleSelect(index)
                    this.$refs.tableRef.objData[index]._isChecked = true
                })
            }, 0)
            // this.$nextTick(() => {
            //     // 获取当前选中元素selectionData在tableDataEdit中的索引
            //     this.selectionData.map(item => {
            //         const index = this.tableDataEdit.findIndex(item2 => {
            //             return (
            //                 item2.app_name === item.app_name &&
            //                 item2.agent_name === item.agent_name &&
            //                 item2.suite_code === item.suite_code
            //             )
            //         })
            //         console.log('index', index)
            //         this.$refs.tableRef.toggleSelect(index)
            //     })
            // })
        },
        closeHandler() {
            this.tableDataEdit.map(item => {
                if (item.forced_mount === '1') return
                // 如果item不再this.selectionData中，则return
                if (
                    !this.selectionData.find(
                        item2 => item2.app_name === item.app_name && item2.agent_name === item.agent_name
                    )
                )
                    return
                item.is_use = 0
            })
            // 手动重置勾选框状态
            this.selectionData = this.$refs.tableRef.getSelection()
            setTimeout(() => {
                // 获取当前选中元素selectionData在tableDataEdit中的索引
                this.selectionData.map(item => {
                    const index = this.tableDataEdit.findIndex(item2 => {
                        return (
                            item2.app_name === item.app_name &&
                            item2.agent_name === item.agent_name &&
                            item2.suite_code === item.suite_code
                        )
                    })
                    console.log('index', index)
                    // this.$refs.tableRef.toggleSelect(index)
                    this.$refs.tableRef.objData[index]._isChecked = true
                })
            }, 0)
            // this.$nextTick(() => {
            //     // 获取当前选中元素selectionData在tableDataEdit中的索引
            //     this.selectionData.map(item => {
            //         const index = this.tableDataEdit.findIndex(item2 => {
            //             return (
            //                 item2.app_name === item.app_name &&
            //                 item2.agent_name === item.agent_name &&
            //                 item2.suite_code === item.suite_code
            //             )
            //         })
            //         console.log('index', index)
            //         this.$refs.tableRef.toggleSelect(index)
            //     })
            // })
        },
        changeSelect(selection) {
            this.selectionData = deepClone(selection)
        },
        editHandler() {
            // this.$router.push({
            //     path: '/base_management/agent_bing',
            //     query: {
            //         app_list: this.app_list.join(','),
            //         suite_list: this.suite_list.join(',')
            //     }
            // })

            // 构建查询参数
            const queryParams = new URLSearchParams({
                app_list: this.app_list.join(','),
                suite_list: this.suite_list.join(',')
            })

            // 构建完整的URL
            const targetUrl = `${window.location.origin}/base_management/agent_bing?${queryParams.toString()}`
            console.log('targetUrl', targetUrl)

            // 在新标签页中打开
            window.open(targetUrl, '_blank')
        },
        ok() {
            this.loading = true
            const arr = this.selectionData.length > 0 ? this.selectionData : this.tableDataEdit
            saveAgentUserInfo({ app_agent_list: arr }).then(res => {
                if (res.data.status === 'success') {
                    this.$Message.success('保存成功')
                    // 刷新列表
                    this.queryList(this.view_app_list, this.view_suite_list)
                    this.showModal = false
                    this.loading = false
                } else {
                    this.$Message.error(res.data.message)
                }
            })
        },
        cancel() {
            this.showModal = false
        },
        queryList(app_list = [], suite_list = []) {
            this.app_list = [...app_list]
            this.suite_list = [...suite_list]
            this.view_app_list = [...app_list]
            this.view_suite_list = [...suite_list]
            getAgentUseInfo({ app_list, suite_list }).then(res => {
                if (res.data.status === 'success') {
                    this.tableDataView = deepClone(res.data.data.data)
                    this.tableDataEdit = deepClone(res.data.data.data)
                    this.tableDataEdit = this.tableDataEdit.map(item => {
                        item._disabled = item.forced_mount === '1' ? true : false
                        return item
                    })
                }
            })
        }
    },
    mounted() {
        getPublishAppList({}).then(res => {
            if (res.data.status === 'success') {
                this.appList = res.data.data.app_list.map(item => {
                    return {
                        value: item,
                        label: item
                    }
                })
            }
        })
        getAllTestingEnv({ page: 1, size: 100 }).then(res => {
            if (res.data.status === 'success') {
                const envList = res.data.data && res.data.data.results ? res.data.data.results : []
                this.suiteList = envList
                    .filter(item => item.suite_code.indexOf('it') !== -1)
                    .map(item => ({
                        value: item.suite_code,
                        label: item.suite_code
                    }))
            }
        })
    }
}
</script>

<style lang="less" scoped>
.edit_btn {
    display: flex;
    justify-content: end;
    margin-bottom: 10px;
}
.query_aera {
    display: flex;
    margin-bottom: 10px;
    .item_com {
        margin-left: 10px;
        height: 31px;
    }
}
.title_contain {
    display: flex;
    align-items: center;
    .title {
        height: 20px;
        line-height: 20px;
        font-size: 14px;
        color: #17233d;
        font-weight: bold;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    .tips {
        color: red;
        font-size: 16px;
        margin-left: 20px;
    }
}
</style>
