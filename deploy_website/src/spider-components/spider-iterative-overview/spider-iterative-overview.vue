<template>
    <Card shadow style="height: 100%">
        <Row style="margin-left: 5px">
            <i-col style="margin: 5px" span="2">
                <Icon type="md-crop" />&nbsp;
                <span style="text-align: left; display: inline-block; width:60px;">发布类型</span>
            </i-col>
            <i-col style="margin: 5px" span="5">
                <Select v-model="iterative_type" @on-change="changeSelect" style="width: 300px">
                    <Option v-for="item in iterative_type_list" :value="item.value" :key="item.value"
                        >{{ item.label }}
                    </Option>
                </Select>
            </i-col>
            <!--      功能迁移至mantis tapd gateway 20240524 by fwm-->
            <!--      <i-col style="margin-left:100px;display: flex;justify-content: center;align-items: center;" span="5">-->
            <!--        <div >-->
            <!--          <Icon type="md-list" ></Icon>&nbsp;-->
            <!--          <Button-->
            <!--            ghost-->
            <!--            type="primary"-->
            <!--            @click="syncIterToTapd"-->
            <!--          >同步迭代至TAPD-->
            <!--          </Button>-->
            <!--        </div>-->
            <!--      </i-col>-->
        </Row>
        <Row style="margin-left: 5px">
            <i-col style="margin: 5px" span="2">
                <Icon type="ios-list-box" style="margin-right: 8px" />
                <span style="text-align: left; display: inline-block; width:60px;">迭代版本</span>
            </i-col>
            <i-col style="margin: 5px" span="5">
                <Select v-model="iteration_id" style="width: 300px" @on-change="getGitrepos" filterable>
                    <Option v-for="(item, index) in iterativeList" :value="item" :label="item" :key="item + index">
                        {{ item }}</Option
                    >
                </Select>
            </i-col>
        </Row>
        <Row style="margin-left: 5px">
            <i-col style="margin: 5px" span="2">
                <Icon type="ios-list-box" style="margin-right: 8px" />
                <span style="text-align: left; display: inline-block; width:60px;">申请时间</span>
            </i-col>
            <i-col style="margin: 5px" span="5">
                <p style="width: 300px">{{ iterativeInfo.br_start_date }}</p>
            </i-col>
        </Row>

        <Row style="margin-left: 5px">
            <i-col style="margin: 5px" span="2">
                <Icon type="ios-list-box" style="margin-right: 8px" />
                <span style="text-align: left; display: inline-block; width:60px;">截止日期</span>
            </i-col>
            <i-col style="margin: 5px" span="5">
                <DatePicker
                    type="date"
                    placeholder="Select date"
                    format="yyyy-MM-dd"
                    v-model="iterativeInfo.deadline"
                ></DatePicker>
            </i-col>
        </Row>
        <Row style="margin-left: 5px">
            <i-col style="margin: 5px" span="2">
                <Icon type="md-text"></Icon>&nbsp;
                <span style="text-align: left; display: inline-block; width:60px;">版本描述&nbsp;</span>
            </i-col>
            <i-col style="margin: 5px" span="8">
                <Input
                    type="textarea"
                    placeholder="版本描述"
                    v-model="iterativeInfo.description"
                    style="width: 300px"
                />
            </i-col>
        </Row>
        <Row style="margin-left: 5px">
            <i-col style="margin: 10px">
                <Icon type="md-list"></Icon>&nbsp;
                <Button
                    ghost
                    type="primary"
                    style="text-align: left; display: inline-block; width:80px;"
                    @click="modfiyInfo"
                    >保存修改
                </Button>
                &nbsp;
                <Icon v-show="is_lock" type="md-unlock"></Icon>&nbsp;
                <Button
                    v-show="is_lock"
                    style="text-align: left; display: inline-block; width:80px;"
                    @click="unlockIterCompile"
                    >解锁编译
                </Button>
                <Icon v-show="!is_lock" type="md-lock"></Icon>&nbsp;
                <Button
                    v-show="!is_lock"
                    ghost
                    type="primary"
                    style="text-align: left; display: inline-block; width:80px;"
                    @click="lockIterCompile"
                    >锁定编译
                </Button>
            </i-col>
        </Row>

        <Row style="margin-left: 5px">
            <i-col style="margin: 5px" span="2">
                <Icon type="md-text"></Icon>&nbsp;
                <span style="text-align: left; display: inline-block; width:60px;">仓库列表</span>
            </i-col>
            <i-col style="margin: 5px" span="18">
                <tables v-model="repoTableData" :columns="columns2" @on-delete="handleDelete" />
            </i-col>
        </Row>
        <Row style="margin-left: 5px">
            <i-col style="margin: 10px">
                <Icon type="md-list"></Icon>&nbsp;
                <Button
                    ghost
                    type="primary"
                    style="text-align: left; display: inline-block; width:80px;"
                    @click="showAddRepo"
                    >追加仓库
                </Button>
            </i-col>
        </Row>
        <Row>
            <Divider orientation="left">模块列表：</Divider>
            <tables v-model="projecttableData" :columns="columns" />
        </Row>
        <Drawer title="应用列表" placement="left" :closable="false" v-model="appListShow" width="30%">
            <Row v-show="iterative_type === 'bugfix'">
                <Col span="7">是否升级依赖版本</Col>
                <Col span="7">
                    <RadioGroup v-model="is_update_out_dep">
                        <Radio label="0">否</Radio>
                        <Radio label="1">是</Radio>
                    </RadioGroup>
                </Col>
            </Row>
            <Tree
                class="custom_tree"
                :data="group_repos"
                ref="group_repos_validate"
                show-checkbox
                v-model="add_project_list"
            ></Tree>
            <!--<CheckboxGroup ref="chekboxList" v-model="appList">-->
            <!--<Row v-for="item in allApps" :key="item.title">-->
            <!--<Checkbox :label="item.value">-->
            <!--<span>{{item.title}}</span>-->
            <!--</Checkbox>-->
            <!--</Row>-->
            <div class="apply-footer">
                <Button style="margin-right: 8px" @click="appListShow = false">Cancel</Button>
                <Button type="primary" @click="addSyss">Submit</Button>
            </div>
            <!--</CheckboxGroup>-->
        </Drawer>
    </Card>
</template>

<script>
import Tables from '@/components/tables'
import { createIter, getCreateIterStatusApi, syncAppBranchInfo } from '@/spider-api/create-iter'
import { getIterListApi, getIterGitRopesApi, iterGroupOtherReposInfoApi } from '@/spider-api/get-iter-info'
import { addRepos, delRepos, modIterInfo, iterLockApi, iterUnlockApi, getIterLockInfoApi } from '@/spider-api/mgt-iter'
import { setIterAppJdk } from '@/spider-api/jdk-version'
import { confirmProdApply, publishConfigBranch, syncIterationToTapd } from '@/spider-api/iter-plan'
import { pipelineArchive } from '@/spider-api/pipeline'
import store from '@/spider-store'
import { getUserName } from '@/libs/util'
export default {
    name: 'SpiderIterativeOverview',
    components: {
        Tables
    },
    data() {
        return {
            is_update_out_dep: '1',
            is_lock: false,
            env_name: '',
            pipeline_id: '',
            add_project_list: [],
            group_repos: [],
            lock_type: 'compile',
            reposList: [],
            btnDisabled: false,
            appListShow: false,
            iteration_id: '',
            iterative_type: '',
            iterative_list: [],
            appList: [],
            allApps: [],
            iterative_type_list: [
                {
                    value: 'release',
                    label: 'release'
                },
                {
                    value: 'bugfix',
                    label: 'bugfix'
                },
                {
                    value: 'feature',
                    label: 'feature'
                }
            ],
            projecttableData: [],
            columns: [
                { title: '模块名称', key: 'appName' },
                { title: '应用状态', key: 'sys_status' },
                { title: '包类型', key: 'appType' },
                {
                    title: '编译JDK版本',
                    render: (h, params) => {
                        let op_list = [
                            h('Option', { props: { value: '1.8' } }),
                            h('Option', { props: { value: '1.7' } }),
                            h('Option', { props: { value: '1.6' } }),
                            h('Option', { props: { value: 'openjdk@11' } }),
                            h('Option', { props: { value: 'openjdk@17' } }),
                            h('Option', { props: { value: 'openjdk@21' } })
                        ]

                        return h(
                            'Select',
                            {
                                props: {
                                    placeholder: params.row.jdkVersion,
                                    value: params.row.jdkVersion,
                                    transfer: true
                                },
                                style: {
                                    width: '100px'
                                },
                                on: {
                                    'on-change': val => {
                                        this.specify_iter_app_jdk(this.iteration_id, params.row.appName, val)
                                    }
                                }
                            },
                            op_list
                        )
                    }
                }
            ],

            repoTableData: [],
            columns2: [
                { title: '仓库名', key: 'gitRepo' },
                { title: '应用名', key: 'app_name' },
                { title: '申请人', key: 'proposer' },
                { title: '中文名', key: 'cname' },
                {
                    title: '操作',
                    key: 'action',
                    width: 150,
                    align: 'center',
                    options: ['delete'],

                    render: (h, params) => {
                        return h('div', [
                            h(
                                'Button',
                                {
                                    props: { type: 'error', size: 'small' }
                                },
                                [
                                    h(
                                        'Poptip',
                                        {
                                            props: {
                                                confirm: true,
                                                transfer: true,
                                                title: '你确定要删除吗?',
                                                type: 'error',
                                                size: 'small'
                                            },
                                            on: {
                                                'on-ok': () => {
                                                    console.log(params, params.row.proposer)
                                                    if (params.row.proposer == getUserName()) {
                                                        this.handleDelete(params)
                                                    } else {
                                                        this.$Message.error(
                                                            '没有权限删除' + params.row.proposer + '用户申请的分支'
                                                        )
                                                    }
                                                },
                                                'on-cancel': function() {}
                                            }
                                        },
                                        '删除'
                                    )
                                ]
                            )
                            //return-end
                        ])
                    }
                }
                // {
                //     title: '删除仓库',
                //     key: 'handle',
                //     options: ['delete'],
                //     button: [
                //         (h, params, vm) => {
                //             return h(
                //                 'Poptip',
                //                 {
                //                     props: {
                //                         confirm: true,
                //                         title: '你确定要删除吗?'
                //                     },
                //                     on: {
                //                         'on-ok': () => {
                //                             if (params.row.proposer == getUserName()) {
                //                                 vm.$emit('on-delete', params)
                //                                 vm.$emit(
                //                                     'input',
                //                                     params.repoTableData.filter(
                //                                         (item, index) => index !== params.row.initRowIndex
                //                                     )
                //                                 )
                //                             } else {
                //                                 this.$Message.error(
                //                                     '没有权限删除' + params.row.proposer + '用户申请的分支'
                //                                 )
                //                             }
                //                         }
                //                     }
                //                 }
                //                 // [h("Button", "自定义删除")]
                //             )
                //         }
                //     ]
                // }
            ]
        }
    },

    computed: {
        iterativeList() {
            let iterativeList = []
            for (let i = 0; i < this.iterative_list.length; i++) {
                // console.log(this.iterative_list[i].br_style)
                if (this.iterative_type === this.iterative_list[i].br_style) {
                    // console.log(this.iterative_list[i].pipeline_id);
                    iterativeList.push(this.iterative_list[i].pipeline_id)
                }
            }
            // console.log(iterativeList);
            return iterativeList
        },
        iterativeInfo() {
            let iterativeInfo = { br_start_date: '', deadline: '', description: '' }
            for (let i = 0; i < this.iterative_list.length; i++) {
                if (this.iteration_id === this.iterative_list[i].pipeline_id) {
                    iterativeInfo.br_start_date = this.iterative_list[i].br_start_date
                    iterativeInfo.deadline = this.iterative_list[i].duedate
                    iterativeInfo.description = this.iterative_list[i].description
                    iterativeInfo.tapd_id = this.iterative_list[i].tapd_id
                    iterativeInfo.branch_name = this.iterative_list[i].branch_name
                }
            }
            return iterativeInfo
        }
    },

    methods: {
        getIterLockInfo() {
            getIterLockInfoApi({
                iteration_id: this.iteration_id,
                lock_type: this.lock_type
            }).then(res => {
                console.log(res.data.data)
                if (Object.keys(res.data.data).length > 0) {
                    console.log(res.data.data.lock_status)
                    this.is_lock = res.data.data.lock_status
                    //   if (res.data.data.lock_status === 1) { this.is_lock = true }
                    // } else {
                    //   this.is_lock = false
                    // }
                } else {
                    this.is_lock = false
                }
            })
        },
        lockIterCompile() {
            console.log(this.iteration_id)
            if (this.iteration_id === '' || this.iteration_id === undefined) {
                this.$Message.error('请先选择迭代')
                return
            }
            iterLockApi({
                iteration_id: this.iteration_id,
                lock_type: this.lock_type,
                lock_status: true
            }).then(res => {
                console.log(res)
                if (res.data.status === 'failed') {
                    this.$Message.error(res.data.msg)
                } else {
                    this.$Message.success(res.data.msg)
                    this.is_lock = true
                }
            })
        },
        unlockIterCompile() {
            if (this.iteration_id === '' || this.iteration_id === undefined) {
                this.$message({
                    message: '请先选择迭代',
                    type: 'error'
                })
                return
            }
            iterUnlockApi({
                iteration_id: this.iteration_id,
                lock_type: this.lock_type,
                lock_status: false
            }).then(res => {
                if (res.data.status === 'failed') {
                    this.$Message.error(res.data.msg)
                } else {
                    this.$Message.success(res.data.msg)
                    this.is_lock = false
                }
            })
        },
        changeSelect(params) {
            if (params === 'bugfix') {
                this.is_update_out_dep = '0'
            } else {
                this.is_update_out_dep = '1'
            }
            this.iteration_id = ''
            store.commit('setIterationID', '')
        },
        getStatus(sid) {
            getCreateIterStatusApi({ sid: sid })
                .then(res => {
                    if (res.data.msg == 'running') {
                        let vm = this
                        setTimeout(function() {
                            vm.getStatus(sid)
                        }, 2000)
                    } else {
                        this.$Message.success(res.data['msg'])
                        alert(res.data['msg'])
                        syncAppBranchInfo().then(res => {
                            if (res.data.msg === 'success') {
                                this.$Message.success('"触发同步应用分支信息任务成功！"')
                            } else {
                                alert('触发同步应用分支信息任务失败！可尝试手动执行jenkins job。')
                            }
                        })
                        getIterGitRopesApi({ iterationID: this.iteration_id }).then(res => {
                            // 获取git仓库信息
                            this.repoTableData = res.data.data['git_repo_list']
                            // 获取应用信息
                            this.projecttableData = res.data.data['appname_list']
                            // alert(JSON.stringify(this.projecttableData))
                        })
                        this.$Spin.hide()
                        this.btnDisabled = false
                    }
                })
                .catch(err => {
                    this.$Message.error(err.response.data.msg)
                    alert(err.response.data.msg)
                    this.$Spin.hide()
                    this.btnDisabled = false
                })
        },
        // 左侧框
        showAddRepo() {
            this.appListShow = true
            if (this.iteration_id != '') {
                iterGroupOtherReposInfoApi({ iterationID: this.iteration_id }).then(res => {
                    this.group_repos = res.data.data['repos_list']
                    // 获取相同组下的git库信息
                    // let midRepos = [];
                    // let flag = 0;
                    // //如果迭代中已经存在对应git库，则不展示
                    // for (let i = 0; i < res.data.data.repos_list.length; i++) {
                    //   flag = 0;
                    //   for (let j = 0; j < this.repoTableData.length; j++) {
                    //     if (
                    //       res.data.data.repos_list[i].value ==
                    //       this.repoTableData[j].gitRepo
                    //     ) {
                    //       flag = 1;
                    //     }
                    //   }
                    //   if (flag == 0) {
                    //     midRepos.push(res.data.data.repos_list[i]);
                    //   }
                    // }
                    // this.allApps = midRepos;
                    // alert(JSON.stringify(res.data.repos_list))
                })
            }
        },

        // onchange 迭代列表事件
        getGitrepos(params) {
            store.commit('setIterationID', this.iteration_id)
            this.getIterLockInfo()
            // this.$emit("changeBusinessID", this.iteration_id)
            // alert(store.state.businessID)
            // alert(params);
            getIterGitRopesApi({ iterationID: this.iteration_id }).then(res => {
                // 获取git仓库信息
                this.repoTableData = res.data.data['git_repo_list']
                // 获取应用信息
                this.projecttableData = res.data.data['appname_list']
                // alert(JSON.stringify(this.projecttableData))
            })
        },
        handleDelete(params) {
            // 分支所有人 和当前用户相同 可以删除
            console.log('登入用户为' + store.state.uname)
            this.btnDisabled = true
            this.$Spin.show({
                render: h => {
                    return h('div', [
                        h('Icon', {
                            class: 'demo-spin-icon-load',
                            props: {
                                type: 'ios-loading',
                                size: 18
                            }
                        }),
                        h('div', '分支删除中请稍等。。。')
                    ])
                }
            })
            delRepos({
                repos_str: [{ repos_path: params.row.gitRepo, module_name: params.row.app_name }],
                iteration_id: this.iteration_id
            }).then(result => {
                // console.log(JSON.stringify(result));
                let sid = result.data.data['sid']
                this.getStatus(sid)
            })
            // if (params.row.proposer == store.state.uname) {
            //   delRepos({
            //     repos_str: params.row.gitRepo,
            //     iteration_id: this.iteration_id,
            //   }).then(result => {
            //     if (result.data.code === 0) {
            //       this.$Message.success(result.data.msg);
            //     } else this.$Message.error(result.data.error);
            //   });
            // }
            // else {
            //   this.$Message.error(
            //     "没有权限删除" + params.row.proposer + "用户申请的分支"
            //   );
            // }

            // params.row.proposer
            //  alert(JSON.stringify(params.row.gitRepo));
        },
        // exportExcel() {
        //   this.$refs.tables.exportCsv({
        //     filename: `table-${new Date().valueOf()}.csv`
        //   });
        // },

        // 信息修改
        modfiyInfo() {
            // console.log(this.iterativeInfo.deadline);
            modIterInfo({
                deadline: this.iterativeInfo.deadline,
                description: this.iterativeInfo.description,
                iteration_id: this.iteration_id
            })
                .then(result => {
                    // console.log(JSON.stringify(result.data));
                    if (result.data['status'] == 'success') {
                        this.$Message.success(JSON.stringify(result.data['msg']))
                    } else {
                        this.$Message.error(JSON.stringify(result.data['msg']))
                    }
                    alert(JSON.stringify(result.data['msg']))
                })
                .catch(err => {
                    this.$Message.error(err.response.data.msg)
                })
            getIterListApi('server').then(res => {
                this.iterative_list = res.data.data['iterative_list']
            })
        },
        getReposList(choicesAll) {
            // let choicesAll = vm.$refs["treeValidate"].getCheckedAndIndeterminateNodes();
            // console.log(JSON.stringify(choicesAll))
            let project_list = []

            let group_cout = 0
            choicesAll.forEach(item => {
                console.log(JSON.stringify(item))
                if ('group_name' in item) {
                    this.groupName = item.title
                    group_cout = group_cout + 1
                } else if ('children' in item) {
                } else {
                    if ('module_name' in item) {
                        project_list.push({ repos_path: item.repos_path, module_name: item.module_name })
                    } else {
                        project_list.push({ repos_path: item.repos_path })
                    }
                }
            })
            if (group_cout > 1) {
                this.$Message.error('不能同时申请两个以上gitlab组的分支!!!')
                this.$Spin.hide()
                this.btnDisabled = false
            }
            this.add_project_list = project_list
            console.log(this.groupName)
            console.log(this.add_project_list)
        },
        addSyss() {
            this.btnDisabled = true
            this.$Spin.show({
                render: h => {
                    return h('div', [
                        h('Icon', {
                            class: 'demo-spin-icon-load',
                            props: {
                                type: 'ios-loading',
                                size: 18
                            }
                        }),
                        h('div', '分支拉取中请稍等。。。')
                    ])
                }
            })

            this.appListShow = false
            var vm = this
            var choicesAll = vm.$refs['group_repos_validate'].getCheckedAndIndeterminateNodes()
            this.getReposList(choicesAll)
            let branch_info = {
                is_update_out_dep: this.is_update_out_dep,
                branch_name: vm.iterativeInfo.branch_name,
                branch_type: vm.iterative_type,
                deadline: vm.iterativeInfo.deadline,
                desc: vm.iterativeInfo.description,
                tapd_id: vm.iterativeInfo.branch_name,
                gitlab_group: vm.groupName,
                repos_str: vm.add_project_list
            }
            createIter(branch_info).then(result => {
                if (result.data['status'] == 'failed') {
                    alert(JSON.stringify(result.data['msg']))
                    this.$Spin.hide()
                    this.btnDisabled = false
                } else {
                    let sid = result.data.data['sid']
                    // alert(result.data)
                    this.getStatus(sid)
                }
            })
        },
        specify_iter_app_jdk(iteration_id, app_name, jdk_version) {
            if (jdk_version != null && jdk_version !== '') {
                let data = {
                    iterative_id: iteration_id,
                    app_name: app_name,
                    jdk_version: jdk_version
                }
                // console.log(data);
                setIterAppJdk(data).then(res => {
                    if (res.data.status === 'success') {
                        this.$Message.success(res.data.msg)
                    } else {
                        this.$Message.error(res.data.msg)
                    }
                    this.getGitrepos(this.iteration_id)
                })
            }
        },
        initThisVue() {
            this.iterative_type = store.state.iterationID.split('_')[1]
            if (this.iterative_type === 'bugfix') {
                this.is_update_out_dep = '0'
            } else {
                this.is_update_out_dep = '1'
            }
            this.iteration_id = store.state.iterationID
            this.getGitrepos(this.iterationID)
        },
        confirmProdPublishApply(query) {
            let md5_str = query['md5Str']
            this.$Spin.show({
                render: h => {
                    return h('div', [
                        h('Icon', {
                            class: 'demo-spin-icon-load',
                            props: {
                                type: 'ios-loading',
                                size: 18
                            }
                        }),
                        h('div', '确认产线发布申请中...')
                    ])
                }
            })
            confirmProdApply(md5_str)
                .then(result => {
                    if (result.data.status === 'success') {
                        this.$Message.success(result.data.msg)
                    } else {
                        this.$Message.error(result.data.msg)
                    }
                    this.$Spin.hide()
                })
                .catch(err => {
                    this.$Spin.hide()
                    this.$Message.error(err.response.data.msg)
                })
        },
        syncIterToTapd() {
            syncIterationToTapd(this.iteration_id).then(res => {
                if (res.data.status == 'success') {
                    this.$Notice.success({
                        desc: '同步成功'
                    })
                } else {
                    this.$Notice.error({
                        desc: '同步失败'
                    })
                }
            })
        }
    },
    created() {
        //    // var param = this.$route.params;
        // alert (JSON.stringify(this.$route.params))
        //    this.iterative_type=this.$route.params["iterative_type"]
        //   alert (this.iterative_type)
        //    this.iteration_id = this.$route.params["iteration_id"]
        // this.iterative_type=this.$route.iterative_type
        // 如果使用query方式传入的参数使用this.$route.query 接收
        // 如果使用params方式传入的参数使用this.$router.params接收
    },

    mounted() {
        getIterListApi('server').then(res => {
            // console.log(JSON.stringify(res))
            this.iterative_list = res.data.data['iterative_list']
            // alert(JSON.stringify(this.iterative_list))
            // this.columns[2].filters = res.data.group_list
            // this.groupList=res.data.group_list;
            // alert(this.groupList)
        })
        // alert (JSON.stringify(this.$route.params))
        this.iterative_type = this.$route.params['iterative_type']
        if (this.iterative_type === 'bugfix') {
            this.is_update_out_dep = '0'
        } else {
            this.is_update_out_dep = '1'
        }
        // alert (this.iterative_type)
        //  alert (this.$route.params["br_style"])
        this.iteration_id = this.$route.params['iteration_id']

        if (this.iteration_id !== '') {
            this.getGitrepos(this.iteration_id)
        }

        if (JSON.stringify(this.$route.query) !== '{}') {
            this.confirmProdPublishApply(this.$route.query)
        }
    }
}
</script>

<style scoped>
.demo-spin-icon-load {
    animation: ani-demo-spin 1s linear infinite;
}
.apply-footer {
    width: calc(30% - 16px);
    position: fixed;
    bottom: 0;
    border-top: 1px solid #e8e8e8;
    padding: 10px 16px;
    text-align: right;
    background: #fff;
}
.custom_tree {
    padding-bottom: 50px;
}
</style>
