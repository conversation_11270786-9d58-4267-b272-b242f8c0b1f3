<template>
  <Card shadow style="height: 100%">
    <p slot="title" style="width: 100%;text-align: center;">
          业务数据计划执行
        </p>
    
    
    <Row>
        <Col span="7">
        <Table
      style="margin-top: 1em"
      border
      width="1150"
      :columns="bis_pipeline_columns"
      :data="test_exec_plan_data"
      
    ></Table>
        </Col>
    </Row>
  <Modal
      :title="title_info"
      width="400"
      v-model="online_content_confirm"
      :mask-closable="true"
      >
      <div>
        <Row>
          <Table 
                 :columns="tableColumns"
                 :data="tableData"
                 :no-data-text="noDataText"
                 ref="online_content_table"
          >
          </Table>
        </Row>
      </div>
    </Modal>
    <Modal
      title=""
      v-model="select_env_modal_show"
      width="580"
      :mask-closable="true"
    >
      <Row style="margin-top: 2em">部署环境
        <Select v-model="suite_info" filterable clearable placeholder="Select suite code" @on-change="select_suite_code">
            <Option v-for="item in suite_item_list"
                    :value="item.suite_code"
                    :label="item.suite_code"
                    :key="item.suite_code">
              {{ item.suite_code }}
            </Option>
            </Select>
        <!-- <Select v-model="dump_bis_code" style="width: 100px" clearable>
          <Option v-for="item in dump_type_list" :value="item" :key="item">{{ item }}</Option>
        </Select> -->
      </Row>
      <div slot="footer">
        <Button @click="submitTestPlanExec">确定</Button>
        <Button @click="cancelSubmitTestPlanExec">关闭</Button>
      </div>
    </Modal>
  </Card>
</template>

<script>
  import Tables from "@/components/tables";
  import store from "@/spider-store";
  import {
  getBisInfo, getBisVersionInfo, getBisBranchInfo, getBisExecPlanInfo, dbTestInitMultiPush
} from '@/spider-api/test-env-mgt'
  import {
  getRecoverDumpInfo
} from '@/spider-api/pipeline'
import {
    getSuiteInfo,
  } from '@/spider-api/mgt-env'
  export default {
    name: "TestDataMgtPlanExec",
    components: {
      Tables
    },
    data() {
      return {
        br_show:true,
        bis_type:'',
        dump_type_list:'',
        btnDisabled:false,
        online_content_confirm:false,
        select_env_modal_show:false,
        suite_item_list:[],
        suite_info:'',
        suite_info_dict:{},
        noDataText:"暂无应用信息",
        title_info:"应用信息",
        db_test_init_multi_info:'',
        test_exec_plan_data:[],
        tableData:[],
        init_ccms:'',
        clean_cache:'',
        tmp_app_info:'',
        test_set_id:'',
        setting_time:'',
        formValidate: {
        sql_branch_name: '',
        bis_type: '',
      },
      tableColumns: [{
          title: '应用名',
          key: 'app_name',
          width: 160
        },{
          title: '应用迭代',
          key: 'bis_pipeline_id',
          width: 200
        }],
      bis_pipeline_columns: [
        {
          title: '业务迭代',
          key: 'bis_pipeline_id',
          width: 80
        },
        {
          title: '应用信息',
          align: 'center',
          render: (h, params) => {
            return h('div', [
              h(
                'Button',
                {
                  attrs: {
                    class: 'ivu-btn  ivu-btn-primary ivu-btn-ghost',
                    title: '应用信息展示'
                  },
                  props: {},
                  style: {
                    marginRight: '1em'
                  },
                  on: {
                    click: () => {
                      this.online_content_confirm = true
                      this.tableData = []
                      console.log(JSON.parse(params.row.app_info_list))
                      let APP_DICT_LIST = JSON.parse(params.row.app_info_list)
                      for (let it in APP_DICT_LIST['APP_DICT_LIST']){
                        this.tableData.push({'app_name':Object.keys(APP_DICT_LIST['APP_DICT_LIST'][it])[0],'bis_pipeline_id':Object.values(APP_DICT_LIST['APP_DICT_LIST'][it])[0]})
                      }
                      this.tmp_app_info = APP_DICT_LIST['APP_DICT_LIST']
                    }
                  }
                },
                '查看'
              ),
            ])
          }
        },
        {
          title: '测试集ID',
          key: 'test_id',
          width: 150,
          align: 'center'
        },
        {
          title: '时间设置',
          key: 'setting_time',
          width: 150,
          align: 'center'
        },
        {
          title: '初始化CCMS',
          key: 'init_ccms',
          width: 110,
          align: 'center'
        },
        {
          title: '清理缓存',
          key: 'clean_cache',
          width: 110,
          align: 'center'
        },
        {
          title: '创建人',
          key: 'create_user',
          width: 150,
          align: 'center'
        },
        {
          title: '状态',
          width: 130,
          align: 'center',
        },
        {
          title: '操作',
          align: 'center',

          render: (h, params) => {
            return h('div', [
              h(
                'Button',
                {
                  attrs: {
                    class: 'ivu-btn  ivu-btn-primary ivu-btn-ghost',
                    title: '触发部署流水线'
                  },
                  props: {},
                  style: {
                    marginRight: '1em'
                  },
                  on: {
                    click: () => {
                      this.getSuite()
                      this.select_env_modal_show = true
                      let APP_DICT_LIST = JSON.parse(params.row.app_info_list)
                      this.tmp_app_info = APP_DICT_LIST['APP_DICT_LIST']
                      this.init_ccms = params.row.init_ccms
                      this.clean_cache = params.row.clean_cache
                      this.test_set_id = params.row.test_id
                      this.bis_pipeline_id = params.row.bis_pipeline_id
                      this.setting_time = params.row.setting_time
                    }
                  }
                },
                '部署'
              ),
            ])
          }
        }
      ],
      };
    },

    computed: {
      
    ruleValidate () {
        return {
          sql_branch_name: [
            { required: true, message: '请选择分支源', trigger: 'blur' }
          ],
          bis_type:[{ required: true, message: '请选择分支源', trigger: 'blur' }]
    }
    }
    },

    methods: {
      getRecoverDumpTypeInfo(){
      getRecoverDumpInfo().then(res =>{
        
      })
      },
      select_suite_code(){
        for (let item in this.suite_item_list){
          console.log(this.suite_item_list[item]['suite_code'])
          console.log(this.suite_info)
          if (this.suite_item_list[item]['suite_code'] == this.suite_info ){
                          this.suite_info_dict =  this.suite_item_list[item]
                          console.log(this.suite_info_dict)
                        }
                      }
      },
      submitTestPlanExec(){
        this.db_test_init_multi_info= {
        "env_id": this.suite_info_dict.env_id,
        "env_name": this.suite_info_dict.suite_code,
        "init_db": "None",
        "ccms_type": this.init_ccms,
        "use_env_time": this.setting_time,
        "clean_cache": this.clean_cache,
      "test_set_id": this.test_set_id,
      "app_dict_list": this.tmp_app_info,
      "bis_pipeline_id": this.bis_pipeline_id
      }
this.select_env_modal_show = false
 dbTestInitMultiPush(this.db_test_init_multi_info).then(res =>{
  console.log(res)
 })
},
      cancelSubmitTestPlanExec(){

      },
      getSuite(){
        getSuiteInfo().then(res => {
          this.suite_item_list = res.data.data["data_list"]
        })
        },
      init(){
        this.getRecoverDumpTypeInfo()
        this.getBisExecPlan()
      },
    checkBranchNameLenth(){},
    select_bis_type(){
      
    },
    query_recommend_version(){},
    handleSubmit(formValidate){
         
    },
    getBisExecPlan(){
      getBisExecPlanInfo().then(res =>{
        this.test_exec_plan_data = res.data.data
        let app_info_list = this.test_exec_plan_data['app_info_list']
        console.log(app_info_list)
      })
    }
      
    }
    
  };
</script>

<style scoped>
  .demo-spin-icon-load {
    animation: ani-demo-spin 1s linear infinite;
  }
</style>
