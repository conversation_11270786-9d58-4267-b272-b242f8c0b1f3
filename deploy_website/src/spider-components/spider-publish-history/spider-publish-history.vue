<template>
    <Card shadow>
        <div style="display: flex;">
            <i-col v-if="app_name_c" style="flex: 1;">
                <Button style="margin-right: 1em;" size="small" ghost type="success" @click="history">执行历史</Button>
                <span v-if="showChange && curDataInfo.branch_name" style="color: #ed3f14;"
                    >当前已选回滚版本： {{ curDataInfo.branch_name }}</span
                >
                <Button
                    v-if="showChange && curDataInfo.branch_name"
                    style="margin-left: 10px;"
                    size="small"
                    ghost
                    type="error"
                    @click="changeHandler"
                    >变更</Button
                >
            </i-col>
            <Poptip confirm title="是否确认结束回滚?" @on-ok="endHandler" @on-cancel="cancel">
                <Button
                    v-if="showChange && curDataInfo.branch_name"
                    style="margin-left: 10px;"
                    size="small"
                    ghost
                    type="error"
                    >结束回滚</Button
                >
            </Poptip>
        </div>
        <Modal
            v-model="ops_operate_modal"
            :app_name="app_name_c"
            :pipeline_id="pipeline_id_p"
            width="680"
            :mask-closable="false"
            id="pid"
            @on-cancel="cancelHistory"
        >
            <p slot="header" style="color:gray;">
                <Icon type="md-clipboard"></Icon>
                <span>{{ app_name }} 执行历史</span>
            </p>
            <div style="height:300px; overflow-y: auto;">
                <table style="margin: 10px;" v-for="cont in historyCont" :key="cont.index">
                    <tr>
                        <td width="15px">
                            <Icon type="md-arrow-round-forward"></Icon>
                        </td>
                        <td width="100px" style="color: darkblue;">{{ cont.op_user }}</td>
                        <td width="80px" style="color: black">{{ cont.op_type }}</td>
                        <td width="50px">{{ cont.suite_code }}</td>
                        <td width="400px">{{ cont.op_time }}</td>
                    </tr>
                    <tr>
                        <td width="15px"></td>
                        <td width="100px" style="border-bottom: #DDDDDD solid 2px; color: black;">{{ cont.ip }}</td>
                        <td
                            width="450px"
                            colspan="3"
                            style="border-bottom: #DDDDDD solid 2px;"
                            v-if="cont.message !== 'error'"
                        >
                            <span v-html="cont.message"></span>
                        </td>
                        <td width="450px" colspan="3" style="border-bottom: #DDDDDD solid 2px; color: red;" v-else>
                            <span v-html="cont.message"></span>
                        </td>
                    </tr>
                </table>
            </div>
            <div slot="footer">
                <Button @click="cancelHistory">关闭</Button>
            </div>
        </Modal>
    </Card>
</template>

<script>
import { getPublishHistory } from '@/spider-api/publish'
export default {
    name: 'SpiderPublishHistory',
    props: {
        app_name_c: {
            type: String,
            defaults: ''
        },
        pipeline_id_p: {
            type: String,
            defaults: ''
        },
        pid: {
            type: 'string'
        },
        showChange: {
            type: Boolean,
            defaults: false
        },
        curDataInfo: {
            type: Object,
            defaults: () => ({})
        }
    },

    data() {
        return {
            pipeline_id: '',
            ops_operate_modal: false,
            switch_history: '',
            app_name: '',
            historyCont: []
        }
    },
    methods: {
        cancel() {
            this.$Message.info('已取消')
        },
        changeHandler() {
            this.$emit('changeHandler')
        },
        endHandler() {
            this.$emit('endHandler')
        },
        get_history_data(app_name) {
            getPublishHistory(app_name, this.pipeline_id, 'prod')
                .then(res => {
                    if (res.data.status === 'success') {
                        this.historyCont = res.data.data
                    } else {
                        this.historyCont = []
                    }
                })
                .catch(err => {
                    this.$Message.error(err.response.data.msg)
                })
        },
        history() {
            console.log(this.app_name_c)
            console.log(this.pipeline_id_p)
            this.app_name = this.app_name_c
            this.pipeline_id = this.pipeline_id_p
            this.ops_operate_modal = true
            this.showHistory(this.app_name)
        },
        showHistory(app_name) {
            this.app_name = app_name
            this.ops_operate_modal = true
            this.get_history_data(app_name)
            // this.switch_history = setInterval(this.get_history_data(app_name), 5000);
        },

        cancelHistory() {
            this.app_name = ''
            this.ops_operate_modal = false
            this.historyCont = []
            if (this.switch_history) {
                clearInterval(this.switch_history)
            }
        }
    },
    mounted() {}
}
</script>
