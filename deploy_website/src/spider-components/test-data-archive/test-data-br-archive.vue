<template>
  <Card shadow style="height: 60rem;">
    <Row style="margin-left: 5px;display: flex;align-items: center;position: relative;">
      <i-col style="margin: 5px" span="2">
        <Icon type="ios-trending-up" style="margin-right: 5px"/>
        <span style="text-align: left; display: inline-block; width:60px;">业务分支</span>
      </i-col>
      <i-col style="margin: 5px" span="8">
        {{ biz_param.biz_br_name }}
      </i-col>
      <div style="position: absolute;right: 0px; margin-right: 10px;">
        <Icon type="ios-bookmarks-outline" style="margin-right: 5px"/>
        <span style="margin-right: 25px ;width:60px;">执行详情</span>

        <Select v-model="exec_history" filterable clearable size="default" @on-change="execHistoryChangeSelect"
                style="width: 150px;">
          <Option
            v-for="item in exec_history_list"
            :value="item.value"
            :key="item.value"
          >{{ item.label }}
          </Option>
        </Select>
        <Tooltip content="仅跳转到你最后一次执行的流水线的详情" placement="right-start">
          <Button type="primary" icon="ios-search" ghost size="default" @click="goExecHistory">跳转详情</Button>
        </Tooltip>
        <Button type="primary" ghost size="default" @click="redirectToDumpUrl">跳转dump详情</Button>
      </div>
    </Row>
    <Row style="display: flex; align-items: center; margin: 5px;">
      <i-col style="margin-right: 5px;">
        <Icon type="ios-trending-up" style="margin-right: 5px" />
        <span style="text-align: left; display: inline-block; width: 80px;">在途分支</span>
      </i-col>
      <i-col style="color: red">
        {{ using_test_iter_list.join('; ') }}
      </i-col>
    </Row>
    <Row style="margin-left: 5px;display: flex;align-items: center;">
      <i-col style="margin: 5px" span="2">
        <Icon type="logo-youtube" style="margin-right: 5px"/>
        <span style="text-align: left; display: inline-block; width:60px;">操作&nbsp;</span>
      </i-col>
      <div style="margin: 5px;width: 200px;display: flex;justify-content: space-between">
        <Tooltip content="归档当前分支，并将变更数据归档" placement="top-end">
        <Button size="default" type="warning" @click="execute_confirm_archive_data('formValidate')"
                :loading="btnDisabled">数据归档
        </Button>
        </Tooltip>
      </div>
    </Row>
    <Modal
      v-model="showModel"
      title="归档详情"
      @on-ok="revertShowModel"
    >
      <div style="padding-bottom:6px;margin-bottom:6px;" v-if="archiveStatus">
        {{ archiveStatus }}
      </div>
      <div style="padding-bottom:6px;margin-bottom:6px;" v-if="archiveStatus">
        {{ tag_url }}
      </div>
      <div style="padding-bottom:6px;margin-bottom:6px;" v-else>
        归档中。。。
      </div>
    </Modal>
    <Modal
      v-model="makeDumpModel"
      title="制作dump"
      @on-ok="makeDumpSubmit">
      <div>
        <i-col style="margin: 5px; align-content: center;">
          业务分支：{{ biz_param.biz_br_name }}
        </i-col>
      </div>
      <div slot="footer">
        <Button  type="primary" size="default" :disabled="this.isSubmitting" @click="makeDumpSubmit">提交</Button>
      </div>
    </Modal>
    <Modal
      v-model="showDumpUrlModel"
      title="dump详情"
      @on-ok="closeDumpUrl">
        <span @click="redirectToDumpUrl" style="align-content: center; font-size: 20px;">
          点击进入详情：dump创建详情
        </span>
      <div slot="footer">
        <Button type="primary" size="default" @click="closeDumpUrl">关闭</Button>
      </div>
    </Modal>
    <Modal v-model="showCheckResult">
      <p slot="header" style="color:gray;">
        <span> 以下应用分支非归档版本，无法归档 </span>
      </p>
      <div class="body">
        <table style="width: 100%;margin-top: 20px;margin-bottom: 20px;">
          <thead>
            <th class="row">
              <td>应用</td>
              <td>归档版本</td>
              <td>当前版本</td>
            </th>
          </thead>
          <tbody>
          <tr v-for="item, index in this.showCheckData" v-bind:key="index" class="row">
            <td>{{ item.app_name }}</td>
            <td>{{ item.archive_br }}</td>
            <td>{{ item.archive_br_name }}</td>
          </tr>
          </tbody>
        </table>
      </div>
      <div slot="footer">
        <Button size="default" @click="closeCheckResult()">关闭</Button>
      </div>
    </Modal>

    <Modal
      v-model="showArchiveAlarm"
      title="数据归档">
        <span style="align-content: center; font-size: 20px; color: red">
          {{archiveAlarmMessage}}
        </span>
      <div slot="footer">
        <Button type="primary" size="middle" @click="closeArchiveAlarm">取消</Button>
        <Button type="primary" size="middle" @click="archiveData">确认</Button>
      </div>
    </Modal>
  </Card>
</template>

<script>
import Tables from '@/components/tables'
import store from '@/spider-store'
import {
  getRecoverDumpInfo
} from '@/spider-api/pipeline'
import {
  formatDate
} from '@/spider-api/h5-common'
import {
  check_iter_app_had_archive,
  getCanMakeDump,
  testDataDevMgtBranchArchive, testDataDevMgtBranchCommit, testDataMakeDump
} from '@/spider-api/create-iter'
import {
  getTestDataDevBranchInfo, getTestDataDevBranchDetailInfo
} from '@/spider-api/create-iter'
import {
  execute_history_retrieve,
  get_dump_jenkins_url,
  get_test_iter_list,
  get_using_test_iter_list
} from '@/spider-api/biz-mgt'
import { getTestDataDevStatusApi, testSqlArchiveCheckApi } from '@/spider-api/test-data-dev'
export default {
  name: 'TestdataBrArchive',
  components: {
  },
  props: {
    biz_param: Object
  },
  data () {
    return {
      showModel: false,
      makeDumpModel: false,
      showDumpUrlModel: false,
      archiveStatus: undefined,
      tag_url: undefined,
      spinShow: false,
      br_show: true,
      dump_make_url: '',
      using_test_iter_list: [],
      bis_type: '',
      dump_type_list: [],
      bis_pipeline_id: '',
      biz_br_name: '',
      env_name: undefined,
      commitBtnDisabled: false,
      btnDisabled: false,
      test_data_dev_info_list: [],
      showArchiveAlarm: false,
      archiveAlarmMessage:'',
      job_name: 'commit_diff_sql',
      start_datetime: undefined,
      exec_history: "",
      isSubmitting: false, // 添加isSubmitting变量
      showCheckResult: false,
      showCheckData: [],
      exec_history_list: [ {
        value: 'archive_sql',
        label: '数据归档'
      }]
    }
  },

  computed: {},

  methods: {
    closeCheckResult() {
      console.info('关闭')
      this.showCheckResult = false
    },
    revertShowModel () {
      this.showModel = !this.showModel
    },
    execHistoryChangeSelect (params) {
      this.exec_history = params
    },
    goExecHistory () {
      if (!this.exec_history || this.exec_history.trim().length == 0) {
        this.$Message.warning('请先选择执行类型')
        return false
      }
      if (!this.biz_param.biz_test_iter_id || this.biz_param.biz_test_iter_id.trim().length == 0) {
        this.$Message.warning('请先选择分支')
        return false
      }
      execute_history_retrieve({
        'exec_action_type': this.exec_history,
        'biz_test_iter_id': this.biz_param.biz_test_iter_id,
        'env_name': this.biz_param.env_name
      }).then(res => {
        if (res.data.status != 'success') {
          this.$Message.warning(res.data.msg)
          return false
        }
        if (res.data.data) {
          if (this.exec_history == 'archive_sql') {
            this.showModel = !this.showModel
            this.archiveStatus = res.data.data.split(':************************:')[0]
            this.tag_url = 'http://gitlab-lib.howbuy.pa/' + res.data.data.split(':************************:')[1]
            return false
          }
          window.open(res.data.data, '_blank')
          return false
        } else {
          this.$Message.warning('没有查询到你的执行记录')
        }
      })
    },
    get_test_iters () {
      get_test_iter_list().then(res => {
        let data_list = res.data.data
        if (data_list) {
          this.biz_br_name_obj_list = data_list
          this.biz_br_name_list = data_list.map((item) => {
            return {
              value: item.biz_test_iter_br,
              label: item.biz_test_iter_br
            }
          })
        } else {
          this.$Message.error('业务分支获取失败！')
        }
      }).catch(err => {
        console.log(err)
      })
    },
    execute_confirm_archive_data () {
      this.$Modal.confirm({
        title: '提示',
        content: '<p>确认执行数据归档？</p>',
        onOk: () => {
          if (!this.biz_param.biz_test_iter_id || this.biz_param.biz_test_iter_id.trim().length == 0) {
            this.$Message.warning('请先选择分支')
            return false
          }
          this.showCheckResult = false
          this.showCheckData = []
          check_iter_app_had_archive(this.biz_param.biz_test_iter_id).then(res => {
            if (res.data.status == 'failed') {
              this.showCheckResult = true
              this.showCheckData = res.data.data
              return false
            }
          })

          this.btnDisabled = true
          getTestDataDevStatusApi({ 'biz_test_iter_id': this.biz_param.biz_test_iter_id }).then(res => {
            if (res.data.status !== 'success') {
              this.$Message.warning(res.data.msg)
              this.btnDisabled = false
              return false
            }
            const is_pause_recording = res.data.msg
            if (is_pause_recording) {
              this.showArchiveAlarm = false
              testSqlArchiveCheckApi({ 'biz_test_iter_id': this.biz_param.biz_test_iter_id }).then((res) => {
                if (res.data.status === 'success') {
                  this.archiveData()
                } else {
                  this.showArchiveAlarm = true
                  this.archiveAlarmMessage = res.data.msg
                  this.btnDisabled = false
                  //   this.$Modal.confirm({
                  //     title: '警告',
                  //     content: res.data.msg,
                  //     onOk: () => {
                  //       this.archiveData()
                  //     }
                  // })
                }
              })
            } else {
              this.archiveData()
            }
          })
        }
      })
    },
    archiveData () {
      this.btnDisabled = true
      this.showArchiveAlarm = false
      testDataDevMgtBranchArchive(this.biz_param.biz_test_iter_id).then(res => {
        console.log(res.data.data)
        if (res.data.status === 'success') {
          this.$Notice.success({
            title: 'success',
            desc: '数据归档命令已发出,稍后点击跳转详情'
          })
          this.exec_history = 'archive_sql'
          getCanMakeDump(this.biz_param.biz_test_iter_id).then(res => {
            this.makeDumpModel = false
            if (res.data.status === 'success') {
              this.makeDumpModel = true
            }
            this.btnDisabled = false
          })
        } else {
          this.$Notice.error({
            title: 'error',
            desc: '数据归档启动失败:' + res.data.msg,
            duration: 15
          })
        }
        this.btnDisabled = false
      })
    },
    closeDumpUrl() {
      this.showDumpUrlModel = false
    },
    closeArchiveAlarm() {
      this.showArchiveAlarm = false
    },
    makeDumpSubmit() {
      if (!this.biz_param.biz_test_iter_id || this.biz_param.biz_test_iter_id.trim().length == 0) {
        this.$Message.warning('请先选择分支')
        return false
      }
      if (!this.biz_param.env_name || this.biz_param.env_name.trim().length == 0) {
        this.$Message.warning('请先选择环境')
        return false
      }
      this.showDumpUrlModel = false
      if (this.isSubmitting) {
        return; // 如果正在提交中，则不处理
      }
      this.isSubmitting = true; // 设置为提交中状态
      testDataMakeDump(this.biz_param.biz_test_iter_id, this.biz_param.env_name).then(res => {
        console.log(res.data.data)
        this.dump_make_url = ''
        this.makeDumpModel = false
        if (res.data.status === 'success') {
          this.showDumpUrlModel = true
          this.dump_make_url = res.data.data
        } else {
          this.$Notice.error({
            title: 'error',
            desc: res.data.msg,
            duration: 15
          })
        }
        this.btnDisabled = false
        this.isSubmitting = false; // 设置为提交完成状态
      }).catch(ex => {
        console.log(ex)
        this.isSubmitting = false; // 设置为提交完成状态
        this.$Message.error(ex.message);
      });
    },
    redirectToDumpUrl() {
      // 使用window.open()在新页面打开
      if (this.dump_make_url !== '' && this.dump_make_url !== null) {
        window.open(this.dump_make_url)
      } else {
        this.$Message.warning('当前业务未制作dump')
      }
    },
    get_using_iter_list () {
      get_using_test_iter_list({'biz_test_iter_id':this.biz_param.biz_test_iter_id}).then(res => {
        let data_list = res.data.data
        this.using_test_iter_list = []
        if (data_list) {
          this.using_test_iter_list = data_list
        } else {
          this.$Message.error('在途业务分支获取失败！')
        }
      }).catch(err => {
        console.log(err)
        this.using_test_iter_list = ['A','B']
        this.$Message.error('在途业务分支获取失败！')
      })
    },
    get_jenkins_job_url() {
      get_dump_jenkins_url(this.biz_param.biz_test_iter_id).then(res => {
        this.dump_make_url = ''
        if (res.data.status === 'success') {
          this.dump_make_url = res.data.data.job_url
        }
      })
    },
    init () {
      this.showModel = false
      this.archiveStatus = undefined
      this.tag_url = undefined
      this.make_dump = false
      if (!this.biz_param.biz_test_iter_id || this.biz_param.biz_test_iter_id.trim().length == 0) {
        this.$Message.warning('请先选择分支')
        return false
      }
      this.get_using_iter_list()
      this.get_jenkins_job_url()
    }
  }

}

</script>

<style lang="less">
.row {
  display: flex;
  justify-content: space-around;
  align-items: center;
  min-height: 30px;
  border-bottom: 1px solid #e8e8e8e8;
  &:hover {
    background-color: #e8e8e8e8;
  }
  td {
    width: 100%;
    text-align: center;
  }
}
tbody {
  tr.row:not(:last-child) {
    border-bottom: 1px solid transparent;
  }
}
</style>
