<template xmlns="http://www.w3.org/1999/html">
  <Card style="height: auto;min-height: 100%;overflow-y: hidden;width: 100%">
    <div  class="iter-quality-report">
      <div class="iter-quality-report-header">
        迭代分支:
        <Select size="small" v-model="iteration_id" style="width: 350px" @on-change="change_iteration" filterable
                clearable>
          <Option v-for="(item) in iter_list "
                  :value="item"
                  :label="item"
                  :key="item">
            {{ item }}
          </Option>
        </Select>
        <Button style="margin-left: 10px" v-if="iteration_id"  type="primary" size="small"
                @click="change_iteration"> 刷新
        </Button>
        <div class="biz_report">

          <Button v-if="report_info.biz_report.biz_report_url && report_info.biz_report.biz_report_url.length>0"
                  type="warning" size="small" ghost
                  @click="jump_biz_report"> 跳转业务报告详情
          </Button>
          <Tooltip v-else placement="bottom-start" :content="report_info.biz_report.msg" :max-width="200">
            <Button type="warning" disabled
                    size="small" ghost
                    @click="jump_biz_report"> 跳转业务报告详情
            </Button>
          </Tooltip>

        </div>
      </div>
      <div class="iter-quality-report-body">
        <div class="app--quality-report">
          <Table
            style="margin-top: 1em"
            border
            width="100%"
            :columns="iter_app_quality_report_columns"
            :data="report_info.app_report"
            :span-method="handleSpan"
          ></Table>
          <Spin fix v-if="loading">
            <Icon type="ios-loading" size=18 class="demo-spin-icon-load"></Icon>
            <div>Loading</div>
          </Spin>
        </div>

      </div>
    </div>
  </Card>
</template>
<script>

import {iterListAllApi} from "@/spider-api/get-iter-info";
import {getIterQualityReportApi} from "@/spider-api/get-iter-info";

export default {
  name: 'IterQualityReport',
  components: {},
  data() {
    return {
      loading: false,
      iter_list: [],
      iteration_id: '',
      report_info: {
        "biz_report": {msg: "请选择迭代后查询"},
        "app_report": [],
      },
      iter_app_quality_report_columns: [
        {
          title: '应用',
          key: 'module_name',
        }, {
          title: '报告名称',
          key: 'report_title',
        },
        {
          title: '通过率',
          key: 'pass_rate',
        },
        {
          title: '状态',
          align: 'center',
          render: (h, params) => {
            let color = 'green'
            if (params.row.status === '不通过') {
              color = 'red'
            }
            return h('div', {
              style: {
                color: color
              },
              domProps: {
                innerHTML: params.row.status
              }
            }, '')
          }
        },
        {
          title: '报告描述',
          key: 'report_ext_desc',
          align: 'center'
        },
        {
          title: '跳转报告详情',
          align: 'center',
          render: (h, params) => {
            return h('div', [
              h(
                'Button',
                {
                  props: {
                    size: "small",
                    type: 'success'
                  },
                  style: {marginTop: '0.5em', marginRight: '0.5em'},
                  on: {
                    click: () => {
                      window.open(params.row.report_url)
                    }
                  }
                },
                '跳转'
              )
            ])
          }
        }
      ],
    }
  },
  props: {},
  methods: {
    jump_biz_report() {
      window.open(this.report_info.biz_report.biz_report_url)
    },
    handleSpan({row, column, rowIndex, columnIndex}) {
      alert(1)
      if (columnIndex === 0) {
        return {
          rowspan: 0,
          colspan: 0
        };
      }
    },
    change_iteration(iteration_id) {
      if (this.iteration_id) {
        this.loading = true
        getIterQualityReportApi({"iteration_id": this.iteration_id}).then(res => {
          if (res.data.status === "success") {
            this.$Message.success(`获取${this.iteration_id}报告成功`);
            this.report_info = res.data.data
          } else {
            this.$Notice.error({
              title: 'Error',
              desc: res.data.message,
              duration: 0
            });
          }
        }).catch(err => {
          this.$Notice.error({
            title: 'Error',
            desc: err.message,
            duration: 0
          });
        }).finally(() => {
            this.loading = false
        })
      }
    },
    initThisVue() {
      let params = {"days": 180}
      iterListAllApi(params).then(res => {
        this.iter_list = res.data.data["iterative_list"].map(item => {
            return item.pipeline_id
          }
        );
      });
    },
  },
  mounted() {
    this.initThisVue()
  }
  ,
  destroyed() {
  }
}
</script>

<style>
.app--quality-report {
  height: 100%;
  width: 100%;
}

.iter-quality-report-body {
  height: 100%;
  width: 100%;
}

.biz_report {
  float: right;
}
</style>
