<style scoped>
.env_application {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;
}

.card {
  padding-top: 10px;
  padding-left: 10px
}

.formItem {
  width: 50%;
}

.formButton {
  width: 50%;
  display: flex;
  justify-content: flex-end
}

.pag {
  display: flex;
  justify-content: center;
  /* margin-top: 24px; */
}

/* .env_application /deep/ .ivu-input-group-prepend, .ivu-input-group-append{
    background-color: #2d8cf0;
    color: #ffffff;
}
.env_application /deep/ .ivu-input-group-prepend, .ivu-input-group-append .ivu-select, .ivu-select-arrow{
    color: #ffffff;
}
.env_application /deep/ .ivu-input-group-prepend .ivu-select{
    color: #ffffff;
} */
.demo-spin-icon-load{
  animation: ani-demo-spin 1s linear infinite;
}
@keyframes ani-demo-spin {
  from { transform: rotate(0deg);}
  50%  { transform: rotate(180deg);}
  to   { transform: rotate(360deg);}
}
</style>
<style lang='less'>
.ivu-modal-header {
  text-align: center;
}
</style>
<template>
  <div class="env_application">
    <Tabs v-model="tab_name" ref="tabs" @on-click="changeTab">
      <TabPane label="数据开发初始化" name="test_data_pipeline" :disabled="tab_disable">
        <TestdataPipeline ref="TestdataPipeline" @send-data="handleData"
                          @handle-loading="handLoading"></TestdataPipeline>
      </TabPane>
      <TabPane label="数据提交" name="test_data_commit" :disabled="tab_disable">
        <TestdataCommit ref="TestdataCommit" :biz_param="biz_param" @handle-loading="handLoading"></TestdataCommit>
      </TabPane>

      <TabPane label="数据归档" name="test_data_archive" :disabled="tab_disable">
        <TestdataBrArchive ref="TestdataBrArchive" :biz_param="biz_param" @handle-loading="handLoading"></TestdataBrArchive>
      </TabPane>
    </Tabs>
    <Spin size="large" fix v-if="spinShow">
      <Icon type="ios-loading" size=32 class="demo-spin-icon-load"></Icon>
      <div>请求中...</div>
    </Spin>
  </div>
</template>
<script>

import TestdataPipeline from "@/spider-components/test-data-dev-pipeline";
import TestdataCommit from "@/spider-components/test-data-commit";
import TestDataBranchManager from "@/spider-components/test-data-mgt-branch";
import TestdataBrArchive from "@/spider-components/test-data-archive/test-data-br-archive.vue";
import TestDataRollBack from "@/spider-components/test-data-roll-back";
import time from '@/mixin/time'
import isEmpty from '@/mixin/isEmpty'


export default {
  mixins: [time, isEmpty],
  data() {
    return {
      biz_param: {
        bis_pipeline_id: undefined,
        biz_br_name: undefined,
        env_name: undefined
      },
      tab_disable: false,
      tab_name: 'test_data_pipeline',
      spinShow: false
    }
  },
  components: {
    TestdataPipeline,
    TestdataCommit,
    TestdataBrArchive,
    TestDataBranchManager,
    TestDataRollBack,
  },
  mounted() {
  },
  methods: {
    handleData(data) {
      this.biz_param = data
    }, handLoading(data) {
      this.spinShow = !this.spinShow
    },
    changeTab(tab_name) {
      if (tab_name === 'test_data_pipeline') {
        this.$refs.TestdataPipeline.init()
      } else if (tab_name === 'test_data_commit') {
        this.$refs.TestdataCommit.init()
      }else if (tab_name === 'test_data_archive') {
        this.$refs.TestdataBrArchive.init()
      }
    }
  }
}
</script>
