<style scoped>
.card {
  padding-top: 10px;
  padding-left: 10px
}

.formItem {
  width: 50%;
}

.formButton {
  width: 50%;
  display: flex;
  justify-content: flex-end
}

.pag {
  display: flex;
  justify-content: center;
  /* margin-top: 24px; */
}

</style>
<style lang='less'>
.ivu-modal-header {
  text-align: center;
}
</style>
<template>
  <div class="env_application">
    <Tabs v-model="tab_name" ref="tabs" @on-click="changeTab">
      <TabPane label="迭代自动化管理" name="test_data_mgt_plan">
        <TestDataMgtPlan ref="TestDataMgtPlan"></TestDataMgtPlan>
      </TabPane>
      <TabPane label="自动化执行配置" name="test_data_exec_config_tab">
        <TestDataExecConfig></TestDataExecConfig>
      </TabPane>
    </Tabs>
  </div>
</template>
<script>
import TestDataMgtPlan from "@/spider-components/test-data-mgt-plan";
import TestDataExecConfig from "./test_data_exec_config";
import time from '@/mixin/time'
import isEmpty from '@/mixin/isEmpty'

export default {
  mixins: [time, isEmpty],
  data() {
    return {
      tab_disable: false,
      tab_name: 'test_data_exec_config_tab'
    }
  },
  components: {
    TestDataMgtPlan,
    TestDataExecConfig
  },
  mounted() {
    this.changeTab(this.tab_name)

  },
  methods: {
    changeTab(tab_name) {
      if (tab_name === 'test_data_mgt_plan') {
        this.$refs.TestDataMgtPlan.init()
      }
    }

  }

}
</script>
