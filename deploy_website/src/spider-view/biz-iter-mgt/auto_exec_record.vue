<template>
    <div class="auto-exec-record">
        <Card>
            <Form
                ref="searchForm"
                :model="searchForm"
                :label-width="50"
                inline
                style="display: flex;justify-content: space-between;"
            >
                <div>
                    <div class="search-row">
                        <div class="form-item-wrapper">
                            <FormItem label="业务" prop="biz_name">
                                <Select
                                    v-model="searchForm.biz_name"
                                    clearable
                                    filterable
                                    style="width: 200px"
                                    @on-change="handleBizNameChange"
                                >
                                    <Option v-for="item in bizList" :key="item.label" :value="item.value">{{
                                        item.label
                                    }}</Option>
                                </Select>
                            </FormItem>
                        </div>
                        <div class="form-item-wrapper">
                            <FormItem label="分支" prop="biz_test_iter_br">
                                <Select v-model="searchForm.biz_test_iter_br" clearable filterable style="width: 200px">
                                    <Option v-for="item in branchList" :key="item.value" :value="item.value">{{
                                        item.label
                                    }}</Option>
                                </Select>
                            </FormItem>
                        </div>
                        <div class="form-item-wrapper">
                            <FormItem label="环境" prop="suite_code">
                                <Select v-model="searchForm.suite_code" clearable filterable style="width: 200px">
                                    <Option v-for="item in envList" :key="item.value" :value="item.value">{{
                                        item.label
                                    }}</Option>
                                </Select>
                            </FormItem>
                        </div>
                        <div class="form-item-wrapper" style="flex: 0 0 616px;">
                            <FormItem label="日期" prop="dateRange">
                                <DatePicker
                                    v-model="searchForm.dateRange"
                                    type="datetimerange"
                                    format="yyyy-MM-dd HH:mm:ss"
                                    placeholder="选择日期范围"
                                    style="width: 516px"
                                />
                            </FormItem>
                        </div>
                        <div class="form-item-wrapper">
                            <FormItem label="执行人" prop="opt_user">
                                <Input
                                    v-model="searchForm.opt_user"
                                    placeholder="请输入执行人名"
                                    clearable
                                    style="width: 200px"
                                />
                            </FormItem>
                        </div>
                        <div class="form-item-wrapper">
                            <FormItem label="批次号" prop="batch_no">
                                <Input
                                    v-model="searchForm.batch_no"
                                    placeholder="请输入执行批次"
                                    clearable
                                    style="width: 200px"
                                />
                            </FormItem>
                        </div>
                    </div>
                </div>
                <div
                    style="flex: 0 1 200px; display: flex;align-items: center;justify-content: center; margin-right: 50px;"
                >
                    <Button type="primary" @click="handleSearch">查询</Button>
                    <Button style="margin-left: 8px" @click="handleReset">重置</Button>
                </div>
            </Form>

            <Table :columns="columns" :data="tableData" :loading="loading" />

            <div class="pagination">
                <Page
                    :total="total"
                    :current="current"
                    :page-size="pageSize"
                    show-total
                    show-elevator
                    @on-change="handlePageChange"
                />
            </div>
        </Card>
    </div>
</template>

<script>
import { getAutoTestRecordList, getAllTestingEnv } from '@/spider-api/test_mgt/auto_exec_record'
import { getBisNameLIstInfo, get_test_iter_list } from '@/spider-api/biz-mgt'

export default {
    name: 'AutoExecRecord',
    data() {
        return {
            searchForm: {
                biz_name: '',
                biz_test_iter_br: '',
                suite_code: '',
                dateRange: [],
                opt_user: '',
                batch_no: '',
                status_0: '',
                status_1: ''
            },
            bizList: [], // 业务列表
            branchList: [], // 分支列表
            branchObjList: [], // 分支对象列表，保存完整的分支信息
            envList: [], // 环境列表
            status1List: [
                { value: 'success', label: '成功' },
                { value: 'failed', label: '失败' },
                { value: 'running', label: '执行中' }
            ],
            status2List: [
                { value: 'normal', label: '正常' },
                { value: 'abnormal', label: '异常' }
            ],
            columns: [
                { title: '业务名称', width: 200, fixed: 'left', key: 'biz_name', sortable: true },
                { title: '分支', width: 100, key: 'biz_test_iter_br', sortable: true },
                { title: '环境', width: 80, key: 'suite_code', sortable: true },
                { title: '执行时间', width: 160, key: 'execute_time', sortable: true },
                { title: '执行人', width: 150, key: 'opt_user', sortable: true },
                { title: '批次号', minWidth: 100, key: 'batch_no', sortable: true },
                { title: '案例平均通过率', minWidth: 160, key: 'avg_case_pass_rate', sortable: true },
                {
                    title: '环境初始化线状态',
                    width: 160,
                    key: 'status_0',
                    sortable: true,
                    render: (h, params) => {
                        let color = '';
                        if (params.row.status_0 === 'success') {
                            color = '#19be6b'; // 绿色
                        } else if (params.row.status_0 === 'failure') {
                            color = '#ed4014'; // 红色
                        } else {
                            color = '#ff9900'; // 黄色
                        }
                        return h('span', {
                            style: {
                                color: color,
                                fontWeight: 'bold'
                            }
                        }, params.row.status_0);
                    }
                },
                {
                    title: '编排线执行状态',
                    width: 160,
                    key: 'status_1',
                    sortable: true,
                    render: (h, params) => {
                        let color = '';
                        if (params.row.status_1 === 'success') {
                            color = '#19be6b'; // 绿色
                        } else if (params.row.status_1 === 'failure') {
                            color = '#ed4014'; // 红色
                        } else {
                            color = '#ff9900'; // 黄色
                        }
                        return h('span', {
                            style: {
                                color: color,
                                fontWeight: 'bold'
                            }
                        }, params.row.status_1);
                    }
                },
                {
                    title: '总耗时(分钟)',
                    width: 120,
                    key: 'total_duration',
                    sortable: true,
                    render: (h, params) => {
                        const duration = params.row.total_duration;
                        if (duration === null || duration === undefined) {
                            return h('span', '-');
                        }
                        return h('span', Math.floor(duration).toString());
                    }
                },
                {
                    title: '环境初始化线',
                    width: 120,
                    align: 'center',
                    key: 'pipeline_type_0',
                    render: (h, params) => {
                        if (!params.row.job_url_0) {
                            return h('span', '')
                        }
                        return h(
                            'Button',
                            {
                                props: {
                                    ghost: true,
                                    type: 'primary',
                                    size: 'small'
                                },
                                on: {
                                    click: () => {
                                        window.open(params.row.job_url_0 || '#')
                                    }
                                }
                            },
                            '详情'
                        )
                    }
                },
                {
                    title: '编排线',
                    width: 100,
                    align: 'center',
                    key: 'pipeline_type_1',
                    render: (h, params) => {
                        if (!params.row.job_url_1) {
                            return h('span', '')
                        }
                        return h(
                            'Button',
                            {
                                props: {
                                    ghost: true,
                                    type: 'primary',
                                    size: 'small'
                                },
                                on: {
                                    click: () => {
                                        window.open(params.row.job_url_1 || '#')
                                    }
                                }
                            },
                            '详情'
                        )
                    }
                },
                {
                    title: '静态报告',
                    width: 100,
                    align: 'center',
                    key: 'test_flow_lib_url',
                    render: (h, params) => {
                        if (!params.row.test_flow_lib_url) {
                            return h('span', '')
                        }
                        return h(
                            'Button',
                            {
                                props: {
                                    ghost: true,
                                    type: 'primary',
                                    size: 'small'
                                },
                                on: {
                                    click: () => {
                                        window.open(params.row.test_flow_lib_url || '#')
                                    }
                                }
                            },
                            '详情'
                        )
                    }
                }
            ],
            tableData: [],
            loading: false,
            total: 0,
            current: 1,
            pageSize: 10
        }
    },
    mounted() {
        this.loadBizList()
        this.loadEnvList()
        this.loadData()
    },
    methods: {
        // 加载环境列表
        async loadEnvList() {
            try {
                const { data } = await getAllTestingEnv({
                    page: 1,
                    size: 200,
                    type_name: 1
                })
                if (data.status === 'success') {
                    // 处理分页数据结构
                    const envList = data.data && data.data.results ? data.data.results : []
                    this.envList = envList
                        .filter(item => item.suite_code.indexOf('it') !== -1)
                        .map(item => ({
                            value: item.suite_code,
                            label: item.suite_code
                        }))
                } else {
                    this.$Message.error(data.msg || '获取环境列表失败')
                }
            } catch (error) {
                console.error('获取环境列表失败:', error)
                this.$Message.error('获取环境列表失败：' + (error.message || '未知错误'))
            }
        },

        // 加载业务列表
        async loadBizList() {
            try {
                const { data } = await getBisNameLIstInfo()
                if (data.status === 'success') {
                    this.bizList = (data.data || []).map(item => ({
                        value: item.biz_code,
                        label: item.biz_name
                    }))
                } else {
                    this.$Message.error(data.msg || '获取业务列表失败')
                }
            } catch (error) {
                console.error('获取业务列表失败:', error)
                this.$Message.error('获取业务列表失败：' + (error.message || '未知错误'))
            }
        },

        // 加载分支列表
        async loadBranchList(bizCode) {
            if (!bizCode) {
                this.branchList = []
                this.branchObjList = []
                return
            }
            try {
                const { data } = await get_test_iter_list({ biz_code: bizCode })
                if (data.status === 'success') {
                    this.branchObjList = data.data || []
                    this.branchList = this.branchObjList.map(item => ({
                        value: item.biz_test_iter_br,
                        label: item.biz_test_iter_br
                    }))
                } else {
                    this.$Message.error(data.msg || '获取分支列表失败')
                }
            } catch (error) {
                console.error('获取分支列表失败:', error)
                this.$Message.error('获取分支列表失败：' + (error.message || '未知错误'))
            }
        },

        // 业务选择变更
        async handleBizNameChange(value) {
            this.searchForm.biz_test_iter_br = '' // 清空分支选择
            if (value) {
                await this.loadBranchList(value)
            } else {
                this.branchList = []
                this.branchObjList = []
            }
        },
        moment(date, formatStr) {
            const d = date ? new Date(date) : new Date()
            const year = d.getFullYear()
            const month = String(d.getMonth() + 1).padStart(2, '0')
            const day = String(d.getDate()).padStart(2, '0')
            const hours = String(d.getHours()).padStart(2, '0')
            const minutes = String(d.getMinutes()).padStart(2, '0')
            const seconds = String(d.getSeconds()).padStart(2, '0')

            return formatStr
                .replace(/YYYY/g, year)
                .replace(/MM/g, month)
                .replace(/DD/g, day)
                .replace(/HH/g, hours)
                .replace(/mm/g, minutes)
                .replace(/ss/g, seconds)
        },
        // 处理查询参数
        getQueryParams() {
            const {
                biz_name,
                biz_test_iter_br,
                suite_code,
                dateRange,
                opt_user,
                batch_no,
                status_0,
                status_1
            } = this.searchForm
            const obj = this.bizList.find(item => item.value === biz_name) || {}
            return {
                biz_name: obj.label || '',
                biz_test_iter_br,
                suite_code,
                opt_user,
                batch_no,
                status_0,
                status_1,
                start_date: dateRange[0] ? this.moment(dateRange[0], 'YYYY-MM-DD HH:mm:ss') : '',
                end_date: dateRange[1] ? this.moment(dateRange[1], 'YYYY-MM-DD HH:mm:ss') : '',
                page_num: this.current,
                page_size: this.pageSize
            }
        },

        // 加载数据
        async loadData() {
            this.loading = true
            try {
                const params = this.getQueryParams()
                const { data } = await getAutoTestRecordList(params)
                console.log('查询结果：', data)
                if (data.code === '0000') {
                    this.tableData = data.data.records || []
                    this.total = data.data.total || 0
                } else {
                    this.$Message.error(data.msg || '查询失败')
                }
            } catch (error) {
                console.error('查询失败:', error)
                this.$Message.error('查询失败：' + (error.message || '未知错误'))
            } finally {
                this.loading = false
            }
        },

        handleSearch() {
            this.current = 1
            this.loadData()
        },
        handleReset() {
            this.$refs.searchForm.resetFields()
            this.handleSearch()
        },
        handlePageChange(page) {
            this.current = page
            this.loadData()
        }
    }
}
</script>

<style lang="less" scoped>
.auto-exec-record {
    .search-row {
        display: flex;
        flex-wrap: wrap;
        margin-bottom: 16px;
        gap: 16px;
        justify-content: flex-start;

        .form-item-wrapper {
            // flex: 0 0 calc(25% - 12px);
            flex: 0 0 300px;
            min-width: 280px;
            display: flex;
            justify-content: flex-start;

            /deep/ .ivu-form-item {
                margin-bottom: 0;
                display: flex;
                align-items: center;

                .ivu-form-item-label {
                    width: 40px;
                    text-align: right;
                    padding-right: 2px;
                    flex-shrink: 0;
                }

                .ivu-form-item-content {
                    flex: 1;
                    min-width: 200px;
                }
            }

            &.search-buttons {
                flex: 0 0 auto;
                min-width: auto;
                justify-content: flex-end;
                margin-left: auto;
            }
        }
    }

    .pagination {
        margin-top: 16px;
        text-align: right;
        padding: 0 16px;
    }

    /deep/ .ivu-card-body {
        padding: 16px;
    }
}
</style>
