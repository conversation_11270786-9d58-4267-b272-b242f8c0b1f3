<template>
    <Card shadow style="height: 100%;overflow-y: auto;position: relative;">
        <Row style="margin-left: 5px;display: flex;align-items: center;">
            <i-col style="margin: 5px" span="2">
                <Icon type="ios-trending-up" style="margin-right: 5px" />
                <Tooltip content="选择你的业务分支" placement="right-start">
                    <span style="text-align: left; display: inline-block; width:60px;">选择业务</span>
                </Tooltip>
            </i-col>
            <i-col style="margin: 5px" span="8">
                <Select
                    v-model="biz_code"
                    filterable
                    clearable
                    size="small"
                    @on-change="bizCodeChangeSelect"
                    style="width: 300px"
                >
                    <Option
                        v-for="item in bis_name_list"
                        :value="item.biz_code"
                        :key="item.biz_code"
                        :label="item.biz_name"
                    >
                        {{ item.biz_name }}
                    </Option>
                </Select>
            </i-col>
        </Row>
        <Row style="margin-left: 5px;display: flex;align-items: center;">
            <i-col style="margin: 5px" span="2">
                <Icon type="ios-trending-up" style="margin-right: 5px" />
                <Tooltip content="选择你的业务分支" placement="right-start">
                    <span style="text-align: left; display: inline-block; width:60px;">选择分支</span>
                </Tooltip>
            </i-col>
            <i-col style="margin: 5px" span="8">
                <Select
                    v-model="biz_br_name"
                    filterable
                    clearable
                    size="small"
                    @on-change="bizBrNameChangeSelect"
                    style="width: 300px"
                >
                    <Option v-for="item in biz_br_name_list" :value="item.value" :key="item.value" :label="item.value"
                        >{{ item.label }}
                    </Option>
                </Select>
            </i-col>

            <div style="margin:0 25px 0 50px;white-space: nowrap;">
                <Icon type="ios-boat-outline" style="margin-right: 5px" />
                <span>历史配置</span>
            </div>
            <Tooltip :content="flow_schedule_tips" placement="bottom">
                <Select
                    v-model="flow_schedule_id"
                    filterable
                    clearable
                    size="small"
                    @on-change="flowConfigChangeSelect"
                    style="width: 340px;"
                >
                    <Option v-for="item in flow_schedule_list" :value="item.value" :key="item.value"
                        >{{ item.label }}
                    </Option>
                </Select>
            </Tooltip>
            <Button type="warning" icon="ios-trash-outline" ghost size="small" @click="delFlowSchedule"
                >删除配置</Button
            >
        </Row>
        <Row style="margin-left: 5px;display: flex;align-items: center;">
            <i-col style="margin: 5px" span="2">
                <Icon type="ios-nutrition-outline" style="margin-right: 5px" />
                <Tooltip content="选择业务关联的测试集编排" placement="right-start">
                    <span style="text-align: left; display: inline-block; width:60px;">选择编排</span>
                </Tooltip>
            </i-col>
            <i-col style="margin: 5px" span="8">
                <Select
                    v-model="flow"
                    filterable
                    clearable
                    size="small"
                    @on-change="flowChangeSelect"
                    style="width: 300px"
                >
                    <Option v-for="item in flowList" :value="item.value" :key="item.value" :label="item.value"
                        >{{ item.label }}
                    </Option>
                </Select>
            </i-col>
            <div style="margin:0 25px 0 50px;white-space: nowrap;">
                <Icon type="ios-bookmarks-outline" style="margin-right: 5px" />
                <span>执行详情</span>
            </div>
            <Tooltip max-width="200" :content="exec_history_tips" placement="bottom">
                <Select
                    v-model="exec_history"
                    filterable
                    clearable
                    size="small"
                    @on-change="execHistoryChangeSelect"
                    style="width: 341px;"
                >
                    <Option v-for="item in exec_history_list" :value="item.value" :key="item.value"
                        >{{ item.label }}
                    </Option>
                </Select>
            </Tooltip>
            <Button type="primary" icon="ios-search" ghost size="small" @click="goExecHistory">查看详情</Button>
        </Row>

        <Row style="margin-left: 5px;display: flex;align-items: center;">
            <i-col style="margin: 5px" span="4.5">
                {{ biz_base_info.biz_name }}
            </i-col>
            <i-col style="margin: 5px" span="4">
                <Button type="dashed" size="small" @click="goFlow">跳转到编排</Button>
            </i-col>
        </Row>

        <Row style="margin-left: 5px;display: flex;align-items: center;">
            <i-col style="margin: 5px" span="2">
                <Icon type="ios-aperture-outline" style="margin-right: 5px" />
                <Tooltip content="在哪个环境执行测试集编排" placement="right-start">
                    <span style="text-align: left; display: inline-block; width:60px;">执行环境</span>
                </Tooltip>
            </i-col>
            <i-col style="margin: 5px" span="5">
                <Select
                    v-model="env_name"
                    filterable
                    clearable
                    size="small"
                    @on-change="envChangeSelect"
                    style="width: 300px"
                >
                    <Option v-for="item in env_name_list" :value="item.value" :key="item.value"
                        >{{ item.label }}
                    </Option>
                </Select>
            </i-col>
        </Row>

        <Row style="margin-left: 5px;display: flex;align-items: center;">
            <i-col style="margin: 5px" span="2">
                <Icon type="ios-apps-outline" style="margin-right: 5px" />
                <span style="text-align: left; display: inline-block; width:60px;">脚本分支</span>
            </i-col>
            <i-col style="margin: 5px" span="18">
                <div class="btn_custom">
                    <RadioGroup v-model="version_type">
                        <Radio label="LOCAL">
                            <span>本地</span>
                        </Radio>
                        <Radio label="REMOTE">
                            <span>远端</span>
                        </Radio>
                    </RadioGroup>
                    <Button type="primary" ghost size="small" @click="branchReset">一键指定归档版本</Button>
                    <Button
                        style="margin-left: 10px;"
                        type="primary"
                        ghost
                        size="small"
                        @click="showFlow = !showFlow"
                        >{{ showFlow ? '收起' : '展开' }}</Button
                    >
                </div>
                <Table
                    class="praphy_table"
                    :columns="graphyColumns"
                    :data="publishData.flowTableData"
                    :border="false"
                    :height="showFlow ? 300 : 40"
                />
            </i-col>
        </Row>
        <Row style="margin-left: 5px;display: flex;align-items: center; margin-bottom: 20px;">
            <i-col style="margin: 5px" span="2">
                <Icon type="ios-apps-outline" style="margin-right: 5px" />
                <Tooltip content="选择业务关联的应用的分支部署到环境" placement="right-start">
                    <span style="text-align: left; display: inline-block; width:60px;">应用分支</span>
                </Tooltip>
            </i-col>
            <i-col style="margin: 5px" span="18">
                <div class="btn_custom">
                    <Select
                        v-model="iteration_id"
                        filterable
                        clearable
                        size="small"
                        placeholder="请选择在途版本"
                        @on-change="brNameChangeSelect"
                        style="width: 200px;margin-right: 10px;"
                    >
                        <Option v-for="item in iteration_id_options" :value="item" :key="item" :label="item">
                            {{ item }}
                        </Option>
                    </Select>
                    <Button type="primary" style="margin-right: 10px;" ghost size="small" @click="gdAll"
                        >一键指定归档版本</Button
                    >
                    <Button type="primary" ghost size="small" @click="showApply = !showApply">{{
                        showApply ? '收起' : '展开'
                    }}</Button>
                </div>
                <tables
                    @on-selection-change="selectApp"
                    ref="selection"
                    v-model="appList"
                    :columns="columns"
                    :height="showApply ? 300 : 40"
                />
            </i-col>
        </Row>

        <Row style="margin-left: 5px;display: flex;align-items: center;">
            <i-col style="margin: 5px" span="2">
                <Icon type="ios-clock-outline" style="margin-right: 5px" />
                <Tooltip content="配置每日几点执行测试集编排" placement="right-start">
                    <span style="text-align: left; display: inline-block; width:60px;">计划任务&nbsp;</span>
                </Tooltip>
            </i-col>
            <i-col style="margin: 5px" span="8">
                <Select
                    v-model="cron"
                    filterable
                    clearable
                    size="small"
                    @on-change="cronChangeSelect"
                    style="width: 300px;margin-right: 10px;  "
                >
                    <Option v-for="item in cronList" :value="item.value" :key="item.value">{{ item.label }} </Option>
                </Select>
            </i-col>
            <i-col style="margin: 5px" span="4">
                <Checkbox v-model="enable_schedule" style="font-weight: bold">是否启动定时</Checkbox>
            </i-col>

            <!-- <i-col style="margin: 5px" span="4">
                <Checkbox v-model="biz_db_init_flag" style="font-weight: bold">仅初始化数据库</Checkbox>
            </i-col> -->
            <i-col style="margin: 5px" span="4">
                <Button type="primary" size="small" @click="saveConfig">保存定时执行配置</Button>
            </i-col>
        </Row>
        <Row style="margin-left: 5px;display: flex;align-items: center;">
            <i-col style="margin: 5px" span="2">
                <Icon type="logo-youtube" style="margin-right: 5px" />
                <span style="text-align: left; display: inline-block; width:60px;">操作&nbsp;</span>
            </i-col>
            <i-col span="6">
                <div class="check_border border_flex">
                    <Radio-group v-model="initStatus">
                        <Radio label="1">全部初始化</Radio>
                        <Radio label="2">部分初始化</Radio>
                    </Radio-group>
                    <Button type="text" size="small" @click="reset">重置</Button>
                </div>
                <div class="check_border">
                    <Checkbox v-model="biz_db_init_flag_copy">仅初始化数据库</Checkbox>
                </div>
                <div class="check_border">
                    <Checkbox v-model="biz_flow_name_box">执行测试集编排</Checkbox>
                </div>
            </i-col>
            <i-col style="margin: 5px" span="4">
                <Button
                    :disabled="publishData.flowTableData.length === 0"
                    type="primary"
                    size="small"
                    @click="pubulishHandler"
                    >执行</Button
                >
            </i-col>
            <!-- <i-col style="margin: 5px" span="4">
                <Button type="primary" size="small" @click="saveConfig">保存定时执行配置</Button>
            </i-col>
            <i-col style="margin: 5px" span="4">
                <Tooltip content="仅执行测试集编排,不初始化环境" placement="top-end">
                    <Button type="info" size="small" @click="execute_biz_pipeline_confirm">仅执行测试集编排</Button>
                </Tooltip>
            </i-col>
            <i-col style="margin: 5px" span="5">
                <Tooltip content="初始化业务绑定的所有应用&执行测试集编排" placement="top-end">
                    <Button type="error" size="small" @click="execute_jenkins_composition_confirm">
                        环境初始化+执行测试集编排
                    </Button>
                </Tooltip>
            </i-col>
            <i-col style="margin: 5px" span="4">
                <Tooltip content="仅初始化勾选的应用&执行测试集编排" placement="top-end">
                    <Button type="success" size="small" @click="execute_jenkins_composition_seg_confirm">
                        部分初始化+执行测试集编排
                    </Button>
                </Tooltip>
            </i-col> -->
        </Row>

        <Modal v-model="showModel" title="部分初始化+执行测试集" @on-ok="execute_jenkins_composition_seg">
            <div style="padding-bottom:6px;margin-bottom:6px;">
                已选择【应用及版本】
            </div>
            <div style="padding-bottom:6px;margin-bottom:6px;">
                <tables v-model="app_selection" :columns="app_selection_columns" />
            </div>

            <div style="padding-bottom:6px;margin-bottom:6px;">
                请选择是否【数据库初始化】
            </div>
            <div style="padding-bottom:6px;margin-bottom:6px;">
                <Checkbox v-model="biz_db_init_flag_copy">初始化</Checkbox>
            </div>
            <div style="padding-bottom:6px;margin-bottom:6px;">
                请选择【中间件及其他无需选择版本的应用】
            </div>
            <CheckboxGroup v-model="no_version_app_selection" @on-change="selectNoVersionApp">
                <Checkbox v-for="app in select_version_apps" :key="app.app_name" :label="app.app_name"></Checkbox>
            </CheckboxGroup>
        </Modal>

        <Modal v-model="showCompositionListModel" title="流水线列表[点击流水线名跳转]">
            <div v-for="item in compositionList" :key="item.job_name">
                <Button :to="item.job_url" target="_blank" type="dashed" style="margin: 5px;">
                    {{ item.job_name + '【运行状态:' + item.job_status + '】' }}
                </Button>
                {{ item.job_url ? '' : '流水线没有运行' }}
            </div>
        </Modal>

        <!-- 脚本分支合并状态警告弹窗 -->
        <Modal
            v-model="showMergeStatusModal"
            title="测试脚本(案例)分支合并状态警告"
            :closable="false"
            :mask-closable="false"
            width="520"
            class-name="merge-status-modal"
        >
            <div class="modal-content">
                <div class="warning-icon">
                    <Icon type="ios-warning-outline" size="48"></Icon>
                </div>
                <div class="warning-message">
                    <div class="message-title">以下测试集存在未合并最新主干的脚本分支：</div>
                    <div class="message-items">
                        <pre>{{ mergeStatusMessage }}</pre>
                    </div>
                </div>
                <div class="warning-tip">
                    请确认是否继续执行操作？
                </div>
            </div>
            <div slot="footer" class="modal-footer">
                <Button @click="cancelExecute" size="large" class="cancel-btn">取消</Button>
                <Button type="warning" @click="confirmContinueExecute" size="large" class="confirm-btn"
                    >确定继续</Button
                >
            </div>
        </Modal>

        <Spin size="large" fix v-if="spinShow">
            <Icon type="ios-loading" size="32" class="demo-spin-icon-load"></Icon>
            <div>请求中...</div>
        </Spin>
    </Card>
</template>

<script>
import Tables from '@/components/tables'
import {
    del_flow_schedule_config,
    execute_biz_pipeline,
    execute_history,
    execute_history_list,
    execute_history_retrieve,
    execute_jenkins_composition,
    get_biz_app_list,
    get_flow_schedule_config,
    get_test_flow_list,
    get_test_iter_list,
    save_flow_schedule_config,
    getBisNameLIstInfo,
    get_app_list_by_iteration_id,
    get_iteration_id_list
} from '@/spider-api/biz-mgt'
import { checkScriptBranchMergeStatus, getFlowTableList } from '@/spider-api/iter-plan'
import { getAutoTestingEnv } from '@/api/test-env'
import { dbTestInitMultiPushNew } from '@/spider-api/test-env-mgt'

export default {
    name: 'TestDataExecConfigNew',
    components: {
        Tables
    },
    // biz_code
    async created() {
        // 获取路由参数
        const { query } = this.$route
        if (query.biz_code) {
            this.biz_code = query.biz_code
            await this.getBizCodeList()
            await this.bizCodeChangeSelect()
        }
        if (query.biz_br_name) {
            this.biz_br_name = query.biz_br_name
            await this.bizBrNameChangeSelect(query.biz_br_name, '', true)
        }
        if (query.flow) {
            this.flow = query.flow
            this.flowChangeSelect(query.flow)
        }
    },
    data() {
        return {
            version_type: 'REMOTE',
            // 选择业务
            biz_code: '',
            bis_name_list: [],
            // 选择分支
            biz_br_name: '',
            biz_br_name_list: [],
            // 流水线列表[点击流水线名跳转]
            compositionList: [],
            showCompositionListModel: false,
            enable_schedule: false,
            spinShow: false,
            biz_db_init_flag_copy: false,
            // 部分初始化
            showModel: false,
            exec_history_tips: '',
            exec_history: '',
            exec_history_list: [],
            env_name: '',
            env_name_list: [],
            appList: [],
            bizApps: [],
            cron: '',
            cronList: [],
            flow: '',
            flowList: [],
            publishData: {
                flowTableData: [] // 查自动化编排-table数据
            },
            graphyColumns: [
                {
                    title: '测试集',
                    key: 'testSetName'
                },
                {
                    title: '应用',
                    key: 'appName'
                },
                {
                    title: '测试脚本(案例)分支',
                    key: 'script_branch',
                    render: (h, params) => {
                        const optionsArr = params.row.select_branch_list.map(item => {
                            return h(
                                'Option',
                                {
                                    props: {
                                        value: item
                                    }
                                },
                                item
                            )
                        })
                        return h(
                            'Select',
                            {
                                props: {
                                    transfer: true,
                                    clearable: true,
                                    value: params.row.script_branch,
                                    filterable: true
                                },
                                on: {
                                    'on-change': value => {
                                        // 当选择项改变时更新数据
                                        this.publishData.flowTableData[params.index].script_branch = value
                                        this.transferScriptValue(params.row, value)
                                    }
                                }
                            },
                            optionsArr
                        )
                    }
                }
            ],
            columns: [
                {
                    type: 'selection',
                    align: 'center'
                },
                { title: '应用名', key: 'app_module_name' },
                { title: '数据库', key: 'db_names', tooltip: true, tooltipMaxWidth: 900 },
                { title: '线上版本', key: 'online_br' },
                { title: '归档版本', key: 'archive_br' },
                {
                    title: '选择版本',
                    key: 'br_name',
                    render: (h, params) => {
                        let op_list = params.row.br_names.map(item => {
                            return h('Option', {
                                // 下拉框的值
                                props: {
                                    value: item,
                                    label: item
                                }
                            })
                        })

                        return h(
                            'Select',
                            {
                                props: {
                                    placeholder: params.row.br_name,
                                    value: params.row.br_name,
                                    transfer: true,
                                    clearable: true,
                                    filterable: true
                                },
                                style: {
                                    width: '100px'
                                },
                                on: {
                                    'on-change': val => {
                                        params.row.br_name = val // 改变下拉框赋值
                                        this.appList[params.index].br_name = val // 改变下拉框赋值
                                        this.updateAppList[params.row.app_module_name] = params.row.br_name
                                        this.selectApp(this.app_selection)
                                    }
                                }
                            },
                            op_list
                        )
                    }
                }
            ],
            app_selection_columns: [
                { title: '应用名', key: 'app_module_name' },
                { title: '部署版本', key: 'br_name' }
            ],
            biz_br_name_obj_list: [],
            flowObjList: [],
            biz_base_info: {
                biz_base_db_code: undefined,
                biz_name: undefined
            },
            updateAppList: {},
            flow_schedule: {},
            flow_schedule_list_his: [],
            flow_schedule_list: [],
            flow_schedule_id: undefined,
            flow_schedule_tips: undefined,
            app_selection: [],
            select_version_apps: [],
            no_version_app_selection: [],
            exec_history_list_objs: {},
            initStatus: '1',
            biz_flow_name_box: true,
            showFlow: true,
            showApply: true,
            app_name_list: [],
            iteration_id_options: [],
            iteration_id: '',
            // 脚本分支合并状态检查相关
            showMergeStatusModal: false,
            mergeStatusMessage: '',
            pendingExecuteMethod: null
        }
    },
    computed: {},
    methods: {
        gdAll() {
            this.appList = this.appList.map(item => {
                item.br_name = '归档版本' // 改变下拉框赋值
                this.updateAppList[item.app_module_name] = '归档版本'
                return item
            })
        },
        brNameChangeSelect(val) {
            if (!val) {
                return
            }
            // this.appList[params.index].br_name = val // 改变下拉框赋值
            // this.updateAppList[params.row.app_module_name] = params.row.br_name
            get_app_list_by_iteration_id({ app_name_list: this.app_name_list, iteration_id: this.iteration_id }).then(
                res => {
                    if (res.data.code === '0000') {
                        const arr = res.data.data
                        // 将this.appList中的app_module_name包含在arr中的元素的br_names赋值为this.br_name
                        const br_name = this.iteration_id.split('_')[1]
                        this.appList = this.appList.map(item => {
                            if (arr.includes(item.app_module_name)) {
                                item.br_name = br_name // 改变下拉框赋值
                                this.updateAppList[item.app_module_name] = br_name
                            }
                            return item
                        })
                    }
                }
            )
        },
        transferScriptValue(row, val) {
            this.publishData.publishData = this.publishData.flowTableData.map(item => {
                if (item.appName === row.appName) {
                    item.script_branch = val
                }
                return item
            })
        },
        reset() {
            this.initStatus = ''
        },
        branchReset() {
            this.publishData.flowTableData = this.publishData.flowTableData.map(item => {
                item.script_branch = 'lastArchived'
                return item
            })
        },
        dbTestInitMultiPushHandler() {
            if (!this.common_check()) {
                return false
            }
            let app_dict_list = []
            if (this.initStatus === '1') {
                app_dict_list = this.appList.map(item => {
                    item.app_name = item.app_module_name
                    item.branch_name =
                        item.br_name === '归档版本'
                            ? 'archive_br'
                            : item.br_name === '线上版本'
                            ? 'online_br'
                            : item.br_name
                    return item
                })
            } else if (this.initStatus === '2') {
                if (this.app_selection.length === 0) {
                    this.$Message.warning('请选择应用')
                    return false
                }
                app_dict_list = this.app_selection.map(item => {
                    item.app_name = item.app_module_name
                    item.branch_name =
                        item.br_name === '归档版本'
                            ? 'archive_br'
                            : item.br_name === '线上版本'
                            ? 'online_br'
                            : item.br_name
                    return item
                })
            }
            dbTestInitMultiPushNew({
                env_name: this.env_name,
                just_init_db: this.biz_db_init_flag_copy ? 1 : 0,
                app_dict_list, // 勾选的应用
                bis_pipeline_id: `${this.biz_code}_${this.biz_br_name}`,
                biz_db_init_flag: true
            }).then(res => {
                if (res.data.status == 'success') {
                    this.$Notice.success({
                        title: 'success',
                        desc: '多推流水线调用成功'
                    })

                    // 添加历史记录
                    let biz_test_iter_id = ''
                    for (let bizObj in this.biz_br_name_obj_list) {
                        if (this.biz_br_name_obj_list[bizObj].biz_test_iter_br == this.biz_br_name) {
                            biz_test_iter_id = this.biz_br_name_obj_list[bizObj].biz_test_iter_id
                        }
                    }
                    const params = {
                        exec_suite_code: this.env_name,
                        exec_action_type: this.initStatus === '1' ? 'init' : 'seg_init',
                        exec_detail_param: res.data.data,
                        biz_type: biz_test_iter_id + '_' + this.biz_base_info.biz_pipeline_name
                    }
                    execute_history(params)
                } else {
                    this.$Notice.error({
                        title: 'error',
                        desc: '多推流水线调用失败'
                    })
                }
            })
        },
        pubulishHandler() {
            if (this.initStatus === '1') {
                // 环境初始化
                const ischeck = this.appList.every(item => {
                    return !!item.br_name
                })
                if (!ischeck) {
                    this.$Message.warning('存在应用版本未选择')
                    return
                }
            } else if (this.initStatus === '2') {
                // 部分初始化
                const ischeck = this.app_selection.every(item => {
                    return !!item.br_name
                })
                if (!ischeck) {
                    this.$Message.warning('存在应用版本未选择')
                    return
                }
            }
            if (this.biz_flow_name_box) {
                // 如果勾选执行测试集校验空
                const result = this.publishData.flowTableData.every(item => {
                    return !!item.script_branch
                })
                if (!result) {
                    this.$Message.warning('存在脚本版本未选择')
                    return
                }
                // 检查脚本分支合并状态
                this.checkScriptBranchMergeStatusBeforeExecute()
            } else if (this.initStatus) {
                this.dbTestInitMultiPushHandler()
            }
        },
        // 查询业务下拉列表数据
        async getBizCodeList() {
            const res = await getBisNameLIstInfo()
            this.bis_name_list = res.data.data
        },
        async bizCodeChangeSelect() {
            this.clearBizInfo()
            this.biz_br_name = ''
            await this.getBranchList(this.biz_code)
        },
        // 查询分支下拉列表数据
        async getBranchList(biz_code) {
            const res = await get_test_iter_list({ biz_code })
            const data_list = res.data.data
            if (data_list) {
                this.biz_br_name_obj_list = data_list
                this.biz_br_name_list = data_list.map(item => {
                    return {
                        value: item.biz_test_iter_br,
                        label: item.biz_test_iter_br
                    }
                })
            } else {
                this.$Message.error('业务分支获取失败！')
            }
        },
        async bizBrNameChangeSelect(params, flow_change, is_init = false) {
            this.clearBizInfo()
            this.biz_br_name = params
            for (let bizObj in this.biz_br_name_obj_list) {
                if (this.biz_br_name_obj_list[bizObj].biz_test_iter_br == params) {
                    await this.get_test_flow_lists(
                        { biz_code: this.biz_br_name_obj_list[bizObj].biz_code },
                        flow_change
                    )
                    let biz_test_iter_id = ''
                    for (let bizObj in this.biz_br_name_obj_list) {
                        if (this.biz_br_name_obj_list[bizObj].biz_test_iter_br == this.biz_br_name) {
                            biz_test_iter_id = this.biz_br_name_obj_list[bizObj].biz_test_iter_id
                        }
                    }
                    this.get_biz_app_lists(
                        {
                            biz_code: this.biz_br_name_obj_list[bizObj].biz_code,
                            biz_test_iter_id: biz_test_iter_id
                        },
                        is_init
                    )
                }
            }
            this.get_execute_history_list()
        },
        // 选择历史配置
        flowConfigChangeSelect(params) {
            this.flow_schedule = {}
            this.flow_schedule_tips = undefined
            this.flow_schedule_id = params
            this.flow_schedule_list_his
                .filter(item => {
                    return item.id === this.flow_schedule_id
                })
                .map(async item => {
                    // 选中配置后 - 回显业务 - 根据业务查询分支 - 原有逻辑
                    this.biz_code = item.biz_code
                    await this.getBranchList(this.biz_code)

                    this.flow_schedule = item
                    this.bizBrNameChangeSelect(item.biz_iter_branch, item.biz_flow_name)
                    this.envChangeSelect(item.suite_code)
                    this.cronChangeSelect(item.cron)
                    this.enable_schedule = !!item.enable_schedule
                    this.flow_schedule_tips = item.biz_iter_branch + '/' + item.biz_flow_name + '/' + item.suite_code
                    this.biz_db_init_flag_copy = false
                    if (item.ext_config) {
                        if (
                            item.ext_config.param &&
                            item.ext_config.param.just_init_db &&
                            item.ext_config.param.just_init_db == '1'
                        ) {
                            this.biz_db_init_flag_copy = true
                        }
                    }
                    this.flow_schedule_tips = this.flow_schedule_tips + '/' + item.cron
                })
        },

        selectApp(selection) {
            this.app_selection = selection.map(item => {
                if (this.updateAppList[item.app_module_name]) {
                    item.br_name = this.updateAppList[item.app_module_name]
                }
                return item
            })
        },
        selectNoVersionApp(selection) {
            this.no_version_app_selection = selection
        },
        goFlow() {
            this.$router.push({
                name: 'biz_auto_test_define',
                query: { biz_code: this.biz_base_info.biz_code, biz_flow_name: this.flow }
            })
        },
        clearBizInfo() {
            this.biz_base_info = {
                biz_base_db_code: undefined,
                biz_name: undefined
            }
            this.flow = ''
            this.env_name = ''
            this.cron = ''
            this.appList = []
            this.app_name_list = []
            this.updateAppList = {}
            this.iteration_id = ''
        },
        async flowChangeSelect(params) {
            this.flow = params
            for (let flowObj in this.flowObjList) {
                if (this.flowObjList[flowObj].biz_flow_name == params) {
                    this.biz_base_info = this.flowObjList[flowObj]
                }
            }
            this.get_execute_history_list()
            const obj = this.bis_name_list.filter(item => item.biz_code === this.biz_code)
            const biz_pipeline_name = `${obj[0].biz_name}_${params}`
            getFlowTableList({
                // biz_pipeline_name: params,
                biz_pipeline_name
            }).then(res => {
                if (res.data.status === 'success') {
                    this.publishData.flowTableData = res.data.data
                    console.log(
                        'this.publishData.flowTableData=====',
                        JSON.stringify(this.publishData.flowTableData, null, 2)
                    )
                    if (this.$route.query.version_type) {
                        this.version_type = 'REMOTE'
                    } else {
                        this.version_type = res.data.data[0].version_type || 'REMOTE'
                    }
                }
            })
        },
        envChangeSelect(params) {
            this.env_name = params
            this.get_execute_history_list()
        },
        cronChangeSelect(params) {
            this.cron = params
        },
        execHistoryChangeSelect(params) {
            this.exec_history = params
            this.exec_history_list.map(item => {
                if (item.value === this.exec_history) {
                    this.exec_history_tips = item.label
                }
            })
        },
        goExecHistory() {
            let param = this.exec_history_list_objs[this.exec_history]
            if (param && this.exec_history) {
                execute_history_retrieve(param).then(res => {
                    if (res.data.status != 'success') {
                        this.$Message.warning(res.data.msg)
                        return false
                    }
                    if (res.data.data) {
                        this.showCompositionListModel = !this.showCompositionListModel
                        this.compositionList = res.data.data
                        return false
                    } else {
                        this.$Message.warning('没有查询到你的执行记录')
                    }
                })
            } else {
                this.$Message.warning('请选择执行记录！')
            }
            this.get_execute_history_list()
        },
        init_suite_info() {
            let data = {
                page: 1,
                size: 200,
                type_name: 1
            }
            getAutoTestingEnv(data).then(res => {
                let env_list = res.data.data['results']
                if (env_list) {
                    this.env_name_list = env_list
                        .filter(item => {
                            return item.suite_code.indexOf('it') !== -1
                        })
                        .map(item => {
                            return {
                                value: item.suite_code,
                                label: item.suite_code
                            }
                        })
                } else {
                    this.$Message.error('环境获取失败！')
                }
            })
        },
        async get_test_flow_lists(param, flow_change) {
            const res = await get_test_flow_list(param)
            let data_list = res.data.data
            if (data_list) {
                this.flowObjList = data_list
                this.flowList = data_list.map(item => {
                    return {
                        value: item.biz_flow_name,
                        label: item.biz_flow_name
                    }
                })
                if (flow_change) {
                    this.flowChangeSelect(flow_change)
                }
            } else {
                this.$Message.error('业务分支获取失败！')
            }
        },
        get_biz_app_lists(param, is_init) {
            get_biz_app_list(param)
                .then(async res => {
                    let data_list = res.data.data
                    if (data_list) {
                        this.bizApps = data_list
                        this.appList = data_list
                            .filter(item => {
                                let br_name_list = []
                                if (item.archive_br) {
                                    br_name_list = [item.archive_br]
                                }
                                let temp_br_name_list = item.br_name_fifteen ? item.br_name_fifteen.split(',') : []
                                br_name_list.push(...temp_br_name_list)
                                item.br_names = br_name_list
                                return item.br_names.length > 0
                            })
                            .map(item => {
                                let br_name_list = []
                                if (item.archive_br && item.online_br) {
                                    br_name_list = ['归档版本', '线上版本', item.archive_br, item.online_br]
                                    item.br_name = '归档版本'
                                } else if (item.archive_br) {
                                    br_name_list = ['归档版本', item.archive_br]
                                    item.br_name = '归档版本'
                                } else if (item.online_br) {
                                    br_name_list = ['线上版本', item.online_br]
                                    item.br_name = '线上版本'
                                }
                                let temp_br_name_list = item.br_name_fifteen ? item.br_name_fifteen.split(',') : []
                                br_name_list.push(...temp_br_name_list)
                                item.br_names = [...new Set(br_name_list)]
                                if (!item.br_name) {
                                    item.br_name = item.br_names[0]
                                }
                                if (
                                    this.flow_schedule.global_param &&
                                    this.flow_schedule.global_param.param_type === 'test_apps' &&
                                    this.flow_schedule.global_param.param
                                ) {
                                    this.flow_schedule.global_param.param.map(global_param_app_item => {
                                        if (
                                            global_param_app_item.app_module_name === item.app_module_name &&
                                            global_param_app_item.br_name
                                        ) {
                                            item.br_name =
                                                global_param_app_item.br_name === 'archive_br'
                                                    ? '归档版本'
                                                    : global_param_app_item.br_name === 'online_br'
                                                    ? '线上版本'
                                                    : global_param_app_item.br_name
                                        }
                                    })
                                }
                                this.updateAppList[item.app_module_name] = item.br_name
                                return item
                            })
                        this.app_name_list = this.appList.map(item => {
                            return item.app_module_name
                        })
                        const id_res = await get_iteration_id_list({ app_name_list: this.app_name_list })
                        // .then(res => {
                        //     if (res.data.code === '0000') {
                        //         this.iteration_id_options = res.data.data
                        //     }
                        // })
                        if (id_res.data.code === '0000') {
                            this.iteration_id_options = id_res.data.data
                        }
                        if (is_init) {
                            this.gdAll()
                            this.iteration_id = this.$route.query.iteration_id
                            this.brNameChangeSelect(this.$route.query.iteration_id)
                        }
                    } else {
                        this.$Message.error('业务被测应用获取失败！')
                    }
                })
                .catch(err => {
                    console.log(err)
                })
        },
        common_check() {
            if (!this.biz_code || this.biz_code.trim().length === 0) {
                this.$Message.warning('请选择业务业务')
                return false
            }
            if (!this.biz_br_name || this.biz_br_name.trim().length === 0) {
                this.$Message.warning('请选择业务分支')
                return false
            }
            if (!this.flow || this.flow.trim().length == 0) {
                this.$Message.warning('请选择编排')
                return false
            }
            let not_check_apps = []
            this.appList.forEach(app => {
                let exist = false
                Object.keys(this.updateAppList).forEach(update_app => {
                    if (app.app_module_name == update_app) {
                        exist = true
                    }
                })
                if (!exist) {
                    not_check_apps.push(app.app_module_name)
                }
            })
            if (not_check_apps.length > 0) {
                this.$Message.error('这些应用:' + not_check_apps + ':没有选择版本@！')
                return false
            }
            if (!this.env_name || this.env_name.trim().length == 0) {
                this.$Message.warning('请选择执行环境')
                return false
            }
            return true
        },
        saveConfig() {
            if (!this.cron || this.cron.trim().length === 0) {
                this.$Message.error('请选择计划任务')
                return false
            }

            if (!this.common_check()) {
                return false
            }
            let config_params = {
                testset_detail_list: this.publishData.flowTableData,
                suite_code: this.env_name,
                biz_iter_branch: this.biz_br_name,
                cron: this.cron,
                biz_flow_name: this.flow,
                biz_code: this.biz_base_info.biz_code,
                version_type: this.version_type
            }
            let app_list = []
            Object.keys(this.updateAppList).forEach(update_app => {
                let br_name = this.updateAppList[update_app]
                if (br_name == '归档版本') {
                    br_name = 'archive_br'
                } else if (br_name == '线上版本') {
                    br_name = 'online_br'
                }
                app_list.push({ app_module_name: update_app, br_name: br_name })
            })
            config_params['params'] = [
                {
                    params: app_list,
                    param_type: 'test_apps'
                },
                { params: { just_init_db: this.biz_db_init_flag_copy ? '1' : '0' }, param_type: 'ext_config' }
            ]
            config_params['enable_schedule'] = this.enable_schedule ? 1 : 0
            this.spinShow = true
            save_flow_schedule_config(config_params)
                .then(res => {
                    if (res.data.status === 'success') {
                        this.spinShow = false
                        this.$Message.success('保存成功')
                        this.initHistory()
                    } else {
                        this.$Message.success('保存失败，失败原因：' + res.data.msg)
                    }
                })
                .catch(err => {
                    this.$Message.error(err)
                })
                .finally(() => {
                    this.spinShow = false
                })
        },
        initHistory() {
            get_flow_schedule_config().then(res => {
                let data_list = res.data.data
                if (data_list) {
                    this.flow_schedule_list_his = data_list
                    this.flow_schedule_list = data_list.map(item => {
                        const text = item.enable_schedule === 1 ? '/有定时' : ''
                        return {
                            value: item.id,
                            label: item.biz_iter_branch + '/' + item.biz_flow_name + '/' + item.suite_code + text
                        }
                    })
                }
            })
        },
        delFlowSchedule() {
            this.$Modal.confirm({
                title: '提示',
                content: this.flow_schedule_tips
                    ? '<p>是否删除配置：' + this.flow_schedule_tips + '</p>'
                    : '<p>请先选择配置' + '</p>',
                onOk: () => {
                    if (this.flow_schedule_id) {
                        this.spinShow = true
                        del_flow_schedule_config({ id: this.flow_schedule_id })
                            .then(res => {
                                if (res.data.status != 'success') {
                                    this.spinShow = false
                                    this.$Message.error(res.data.msg)
                                    return false
                                }
                                this.spinShow = false
                                this.$Message.success('删除成功')
                                this.initHistory()
                            })
                            .finally(() => {
                                this.spinShow = false
                            })
                    }
                }
            })
        },
        execute_biz_pipeline_confirm() {
            this.$Modal.confirm({
                title: '提示',
                content: '<p>是否启动执行测试集流水线</p>',
                onOk: () => {
                    if (!this.common_check()) {
                        return false
                    }
                    this.execute_biz_pipeline_fc()
                }
            })
        },
        execute_biz_pipeline_fc() {
            let biz_test_iter_id = ''
            for (let bizObj in this.biz_br_name_obj_list) {
                if (this.biz_br_name_obj_list[bizObj].biz_test_iter_br == this.biz_br_name) {
                    biz_test_iter_id = this.biz_br_name_obj_list[bizObj].biz_test_iter_id
                }
            }
            this.spinShow = true
            execute_biz_pipeline({
                testset_detail_list: this.publishData.flowTableData,
                suite_code: this.env_name,
                biz_test_iter_id: biz_test_iter_id,
                biz_pipeline_name: this.biz_base_info.biz_pipeline_name,
                version_type: this.version_type
            })
                .then(res => {
                    if (res.data.status != 'success') {
                        this.spinShow = false
                        this.$Message.error(res.data.msg)
                        return false
                    }
                    this.spinShow = false
                    this.$Message.success('启动命令已发出')
                    execute_history({
                        exec_suite_code: this.env_name,
                        exec_action_type: 'biz_job',
                        exec_detail_param: res.data.data,
                        biz_type: biz_test_iter_id + '_' + this.biz_base_info.biz_pipeline_name
                    }).then(res => {
                        console.log(res.data)
                    })
                    this.get_execute_history_list()
                })
                .catch(err => {
                    console.log(err)
                })
                .finally(() => {
                    this.spinShow = false
                })
        },
        execute_jenkins_composition_confirm() {
            this.$Modal.confirm({
                title: '提示',
                content: '<p>是否启动环境初始化+执行测试集流水线</p>',
                onOk: () => {
                    if (!this.common_check()) {
                        return false
                    }
                    this.execute_jenkins_composition_fc()
                }
            })
        },
        execute_jenkins_composition_seg_confirm() {
            if (!this.common_check()) {
                return false
            }
            if (this.app_selection.length === 0) {
                this.$Message.warning('请选择应用')
                return false
            }
            this.execute_jenkins_composition_seg()
            // if (!this.biz_br_name || this.biz_br_name.trim().length == 0) {
            //     this.$Message.warning('请选择业务分支')
            //     return false
            // }
            // if (!this.flow || this.flow.trim().length == 0) {
            //     this.$Message.warning('请选择编排')
            //     return false
            // }
            // if (!this.env_name || this.env_name.trim().length == 0) {
            //     this.$Message.warning('请选择执行环境')
            //     return false
            // }
            // this.showModel = !this.showModel
            // this.select_version_apps = this.bizApps
            //     .filter(item => {
            //         let br_name_list = []
            //         if (item.archive_br) {
            //             br_name_list = [item.archive_br]
            //         }
            //         let temp_br_name_list = item.br_name_fifteen ? item.br_name_fifteen.split(',') : []
            //         br_name_list.push(...temp_br_name_list)
            //         item.br_names = br_name_list
            //         return item.br_names.length === 0
            //     })
            //     .map(item => {
            //         return { app_name: item.app_module_name, branch_name: '' }
            //     })
        },
        execute_jenkins_composition_seg() {
            let app_list = []
            this.no_version_app_selection.forEach(app => {
                app_list.push({ app_name: app, branch_name: '' })
            })
            this.app_selection.map(item => {
                if (item.br_name == '归档版本') {
                    item.br_name = 'archive_br'
                } else if (item.br_name == '线上版本') {
                    item.br_name = 'online_br'
                }
                app_list.push({ app_name: item.app_module_name, branch_name: item.br_name })
            })
            // let just_init_db = 0
            // 经过沟通，这段代码永远不会执行，先注释
            // if (app_list.length === 0 && this.biz_db_init_flag_copy) {
            //     Object.keys(this.updateAppList).forEach(update_app => {
            //         let br_name = this.updateAppList[update_app]
            //         if (br_name == '归档版本') {
            //             br_name = 'archive_br'
            //         } else if (br_name == '线上版本') {
            //             br_name = 'online_br'
            //         }
            //         app_list.push({ app_name: update_app, branch_name: br_name })
            //     })
            //     console.log('biz_db_init_flag_copy======================', this.biz_db_init_flag_copy)
            //     // just_init_db = 1
            // } else if (app_list.length === 0 && !this.biz_db_init_flag_copy) {
            //     this.$Message.warning('请至少选择一个应用或者仅初始化数据库！')
            //     return false
            // }
            let mult_params = {
                app_dict_list: app_list,
                env_name: this.env_name,
                db_env: this.biz_db_init_flag_copy ? this.biz_base_info.biz_base_db_code : undefined,
                // biz_db_init_flag: this.biz_db_init_flag_copy,
                biz_db_init_flag: true,
                just_init_db: this.biz_db_init_flag_copy ? 1 : 0
            }
            let biz_test_iter_id = ''
            for (let bizObj in this.biz_br_name_obj_list) {
                if (this.biz_br_name_obj_list[bizObj].biz_test_iter_br == this.biz_br_name) {
                    biz_test_iter_id = this.biz_br_name_obj_list[bizObj].biz_test_iter_id
                }
            }
            // if (this.biz_db_init_flag_copy) {
            //     mult_params['bis_pipeline_id'] = biz_test_iter_id
            // }
            mult_params['bis_pipeline_id'] = biz_test_iter_id
            let param = {
                exec_action_type: 'seg_init&biz_job_jenkins_composition',
                suite_code: this.env_name,
                jobs_info: [
                    { job_name: 'mult_init_job_type_环境初始化', param: mult_params },
                    {
                        job_name: 'biz_job_job_type_' + this.biz_base_info.biz_pipeline_name,

                        param: {
                            testset_detail_list: this.publishData.flowTableData,
                            suite_code: this.env_name,
                            biz_test_iter_id: biz_test_iter_id,
                            biz_pipeline_name: this.biz_base_info.biz_pipeline_name,
                            version_type: this.version_type
                        }
                    }
                ]
            }
            this.spinShow = true
            execute_jenkins_composition(param)
                .then(res => {
                    this.spinShow = false
                    if (res.data.status != 'success') {
                        this.$Message.error(res.data.msg)
                    } else {
                        this.$Message.success('启动命令已发出')
                        execute_history({
                            exec_suite_code: this.env_name,
                            exec_action_type: 'seg_init&biz_job_jenkins_composition',
                            exec_detail_param: res.data.data,
                            biz_type: biz_test_iter_id + '_' + this.biz_base_info.biz_pipeline_name
                        }).then(res => {
                            console.log(res.data)
                        })
                    }

                    this.get_execute_history_list()
                })
                .catch(err => {
                    this.$Message.error(err)
                })
                .finally(() => {
                    this.spinShow = false
                })
        },
        execute_jenkins_composition_fc() {
            let app_list = []
            Object.keys(this.updateAppList).forEach(update_app => {
                let br_name = this.updateAppList[update_app]
                if (br_name == '归档版本') {
                    br_name = 'archive_br'
                } else if (br_name == '线上版本') {
                    br_name = 'online_br'
                }
                app_list.push({ app_name: update_app, branch_name: br_name })
            })
            this.bizApps
                .filter(item => {
                    let br_name_list = []
                    if (item.archive_br) {
                        br_name_list = [item.archive_br]
                    }
                    let temp_br_name_list = item.br_name_fifteen ? item.br_name_fifteen.split(',') : []
                    br_name_list.push(...temp_br_name_list)
                    item.br_names = br_name_list
                    return item.br_names.length == 0
                })
                .map(item => {
                    app_list.push({ app_name: item.app_module_name, branch_name: '' })
                })
            let mult_params = {
                app_dict_list: app_list,
                env_name: this.env_name,
                db_env: this.biz_base_info.biz_base_db_code,
                biz_db_init_flag: true,
                just_init_db: this.biz_db_init_flag_copy ? 1 : 0
            }

            let biz_test_iter_id = ''
            for (let bizObj in this.biz_br_name_obj_list) {
                if (this.biz_br_name_obj_list[bizObj].biz_test_iter_br == this.biz_br_name) {
                    biz_test_iter_id = this.biz_br_name_obj_list[bizObj].biz_test_iter_id
                }
            }
            mult_params['bis_pipeline_id'] = biz_test_iter_id
            let param = {
                exec_action_type: 'init&biz_job_jenkins_composition',
                suite_code: this.env_name,
                jobs_info: [
                    { job_name: 'mult_init_job_type_环境初始化', param: mult_params },
                    {
                        job_name: 'biz_job_job_type_' + this.biz_base_info.biz_pipeline_name,
                        param: {
                            testset_detail_list: this.publishData.flowTableData,
                            suite_code: this.env_name,
                            biz_test_iter_id: biz_test_iter_id,
                            biz_pipeline_name: this.biz_base_info.biz_pipeline_name,
                            version_type: this.version_type
                        }
                    }
                ]
            }
            this.spinShow = true
            execute_jenkins_composition(param)
                .then(res => {
                    this.spinShow = false
                    if (res.data.status != 'success') {
                        this.$Message.error(res.data.msg)
                    } else {
                        this.$Message.success('启动命令已发出')
                        execute_history({
                            exec_action_type: 'init&biz_job_jenkins_composition',
                            exec_detail_param: res.data.data,
                            exec_suite_code: this.env_name,
                            biz_type: biz_test_iter_id + '_' + this.biz_base_info.biz_pipeline_name
                        }).then(res => {
                            console.log(res.data)
                        })
                    }
                    this.get_execute_history_list()
                })
                .catch(err => {
                    console.log(err)
                })
                .finally(() => {
                    this.spinShow = false
                })
        },
        get_execute_history_list() {
            this.exec_history = undefined
            this.exec_history_list = []
            if (!this.biz_br_name || this.biz_br_name.trim().length == 0) {
                return false
            }
            let biz_test_iter_id = ''
            for (let bizObj in this.biz_br_name_obj_list) {
                if (this.biz_br_name_obj_list[bizObj].biz_test_iter_br == this.biz_br_name) {
                    biz_test_iter_id = this.biz_br_name_obj_list[bizObj].biz_test_iter_id
                }
            }
            let job_type = {
                init: '环境初始化',
                seg_init: '部分初始化',
                biz_job: '仅执行测试集',
                'init&biz_job_jenkins_composition': '环境初始化+执行测试集',
                'seg_init&biz_job_jenkins_composition': '部分初始化+执行测试集',
                flow_schedule: '定时任务'
            }
            execute_history_list({
                exec_suite_code: this.env_name,
                biz_type: biz_test_iter_id + '_' + this.biz_base_info.biz_pipeline_name
            }).then(res => {
                let execute_historys = res.data.data
                if (execute_historys) {
                    this.exec_history_list = execute_historys.map(item => {
                        return {
                            label:
                                '[' +
                                job_type[item.exec_action_type] +
                                ']-[' +
                                item.exec_suite_code +
                                ']-[' +
                                item.create_user +
                                ']' +
                                '-[' +
                                item.create_time.replace('T', ' ') +
                                ']',
                            value: item.id
                        }
                    })
                    this.exec_history_list_objs = {}
                    if (execute_historys && execute_historys.length > 0) {
                        this.execHistoryChangeSelect(execute_historys[0].id)
                    }

                    execute_historys.map(item => {
                        this.exec_history_list_objs[item.id] = item
                    })
                }
            })
        },
        init() {
            this.initHistory()
            this.get_execute_history_list()
            this.cronList = Array.from(Array(24).keys())
                .filter(num => num !== 2)
                .flatMap(num => {
                    if (num === 1) {
                        return [
                            {
                                value: `0 ${num} * * *`,
                                label: `每日${num}点执行`
                            }
                        ]
                    } else {
                        return [
                            {
                                value: `0 ${num} * * *`,
                                label: `每日${num}点执行`
                            },
                            {
                                value: `10 ${num} * * *`,
                                label: `每日${num}点10执行`
                            },
                            {
                                value: `20 ${num} * * *`,
                                label: `每日${num}点20执行`
                            },
                            {
                                value: `30 ${num} * * *`,
                                label: `每日${num}点30执行`
                            },
                            {
                                value: `40 ${num} * * *`,
                                label: `每日${num}点40执行`
                            },
                            {
                                value: `50 ${num} * * *`,
                                label: `每日${num}点50执行`
                            }
                            // {
                            //     value: `30 ${num} * * *`,
                            //     label: `每日${num}点30执行`
                            // }
                        ]
                    }
                })
            this.init_suite_info()
            this.getBizCodeList()
        },
        // 检查脚本分支合并状态
        async checkScriptBranchMergeStatusBeforeExecute() {
            try {
                // 构建请求参数
                const testSetList = this.publishData.flowTableData.map(flow => {
                    const appBranchList = [
                        {
                            appName: flow.appName,
                            version: flow.script_branch || 'lastArchived'
                        }
                    ]
                    return {
                        testSetId: flow.testSetId,
                        appBranchList
                    }
                })

                const params = { testSetList }

                const response = await checkScriptBranchMergeStatus(params)

                if (response.data.code === '9998') {
                    // 显示警告弹窗
                    const errorMessages = response.data.data.error_messages
                    if (Array.isArray(errorMessages) && errorMessages.length > 0) {
                        this.mergeStatusMessage = errorMessages.join('\n')
                    } else {
                        this.mergeStatusMessage = errorMessages || '存在脚本分支未合并最新master，是否继续执行？'
                    }
                    this.showMergeStatusModal = true
                    // 保存待执行的方法
                    this.setPendingExecuteMethod()
                } else if (response.data.code === '0000') {
                    // 直接执行
                    this.executeTargetMethod()
                } else {
                    this.$Message.error(response.data.message || '检查脚本分支合并状态失败')
                }
            } catch (error) {
                console.error('检查脚本分支合并状态异常:', error)
                this.$Message.error('检查脚本分支合并状态异常')
            }
        },
        // 设置待执行的方法
        setPendingExecuteMethod() {
            if (this.initStatus === '1') {
                this.pendingExecuteMethod = 'execute_jenkins_composition_confirm'
            } else if (this.initStatus === '2') {
                this.pendingExecuteMethod = 'execute_jenkins_composition_seg_confirm'
            } else {
                this.pendingExecuteMethod = 'execute_biz_pipeline_confirm'
            }
        },
        // 执行目标方法
        executeTargetMethod() {
            if (this.initStatus === '1') {
                this.execute_jenkins_composition_confirm()
            } else if (this.initStatus === '2') {
                this.execute_jenkins_composition_seg_confirm()
            } else {
                this.execute_biz_pipeline_confirm()
            }
        },
        // 确认继续执行
        confirmContinueExecute() {
            this.showMergeStatusModal = false
            this.executeTargetMethod()
        },
        // 取消执行
        cancelExecute() {
            this.showMergeStatusModal = false
            this.pendingExecuteMethod = null
        }
    },
    mounted() {
        this.init()
    }
}
</script>
<style lang="less" scoped>
.praphy_table {
    margin-bottom: 24px;
}
.btn_custom {
    display: flex;
    justify-content: end;
    margin-bottom: 10px;
}
.check_border {
    border: 1px dashed;
    margin-top: 10px;
    padding: 5px;
    border-radius: 4px;
    margin-right: 20px;
}
.border_flex {
    display: flex;
    justify-content: space-between;
}

// 警告弹窗样式
.modal-content {
    text-align: center;
    padding: 24px 20px 20px;

    .warning-icon {
        margin-bottom: 16px;

        .ivu-icon {
            color: #ff9900;
            background: rgba(255, 153, 0, 0.15);
            border-radius: 50%;
            padding: 12px;
            box-shadow: 0 4px 12px rgba(255, 153, 0, 0.25);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 4px 12px rgba(255, 153, 0, 0.25);
            }
            50% {
                box-shadow: 0 4px 20px rgba(255, 153, 0, 0.4);
            }
            100% {
                box-shadow: 0 4px 12px rgba(255, 153, 0, 0.25);
            }
        }
    }

    .warning-message {
        margin-bottom: 16px;
        text-align: left;

        .message-title {
            font-size: 16px;
            color: #333;
            font-weight: 600;
            margin-bottom: 12px;
            text-align: center;
        }

        .message-items {
            background-color: #fff7e6;
            border-radius: 6px;
            padding: 12px 16px;
            border-left: 4px solid #ff9900;
            max-height: 200px;
            overflow-y: auto;

            pre {
                font-family: inherit;
                font-size: 14px;
                color: #333;
                line-height: 1.8;
                font-weight: 400;
                margin: 0;
                padding: 0;
                background: none;
                border: none;
                white-space: pre-wrap;
                word-wrap: break-word;
            }
        }
    }

    .warning-tip {
        font-size: 15px;
        color: #d48806;
        margin-bottom: 10px;
        text-align: center;
        font-weight: 500;
        background-color: #fffbe6;
        padding: 8px 0;
        border-radius: 4px;
    }
}

.modal-footer {
    text-align: center;
    padding: 16px 0 8px;

    .cancel-btn {
        margin-right: 12px;
        min-width: 80px;
        border-radius: 6px;
    }

    .confirm-btn {
        min-width: 80px;
        border-radius: 6px;
        font-weight: 500;
    }
}

// 全局弹窗样式覆盖
:global(.merge-status-modal) {
    .ivu-modal-header {
        background: linear-gradient(135deg, #fff7e6 0%, #fff2d9 100%);
        border-bottom: 1px solid #ffe7ba;

        .ivu-modal-header-inner {
            color: #d48806;
            font-weight: 600;
            font-size: 16px;
        }
    }

    .ivu-modal-body {
        padding: 0;
    }

    .ivu-modal-footer {
        border-top: 1px solid #f0f0f0;
        background: #fafafa;
    }
}
</style>
<style lang="less">
.my-select {
    .ivu-select-dropdown {
        max-height: 100px; /* 设置下拉选项区域的最大高度为200px */
        overflow-y: auto; /* 当内容超过最大高度时，出现垂直滚动条 */
    }
}
</style>
