<template>
    <Card shadow style="height: 100%;overflow-y: auto">
        <Row style="margin-left: 5px;display: flex;align-items: center;">
            <i-col style="margin: 5px" span="2">
                <Icon type="ios-trending-up" style="margin-right: 5px" />
                <span style="text-align: left; display: inline-block; width:60px;">选择业务</span>
            </i-col>
            <i-col style="margin: 5px" span="12">
                <Select v-model="biz" size="small" @on-change="bizChangeSelect" style="width: 430px" filterable>
                    <Option v-for="item in biz_list" :value="item.value" :key="item.value">{{ item.label }} </Option>
                </Select>
            </i-col>
        </Row>

        <Row style="margin-left: 5px;display: flex;align-items: center;">
            <i-col style="margin: 5px" span="2">
                <Icon type="ios-apps" style="margin-right: 5px" />
                <span style="text-align: left; display: inline-block; width:60px;">被测应用</span>
            </i-col>
            <i-col style="margin: 5px" span="22">
                <Transfer
                    :titles="['可选应用', '已选应用']"
                    :operations="['To left', 'To right']"
                    :data="appList"
                    :list-style="listStyle"
                    :target-keys="targetKeys"
                    :render-format="customRender"
                    filterable
                    :filter-method="filterMethod"
                    @on-change="handleAppChange"
                ></Transfer>
            </i-col>
        </Row>

        <Row style="margin-left: 5px;display: flex;align-items: center;">
            <i-col style="margin: 5px" span="2">
                <Icon type="ios-apps" style="margin-right: 5px" />
                <span style="text-align: left; display: inline-block; width:60px;">依赖应用</span>
            </i-col>
            <i-col style="margin: 5px" span="22">
                <Transfer
                    :titles="['可选依赖应用', '已选依赖应用']"
                    :operations="['To left', 'To right']"
                    :data="depAppList"
                    :list-style="listStyle"
                    :target-keys="depTargetKeys"
                    :render-format="customRender"
                    filterable
                    :filter-method="filterMethod"
                    @on-change="handleDepChange"
                ></Transfer>
            </i-col>
        </Row>

        <Row style="margin-left: 5px;display: flex;align-items: center;">
            <i-col style="margin: 5px" span="2">
                <Icon type="ios-apps" style="margin-right: 5px" />
                <span style="text-align: left; display: inline-block; width:90px;">自研中间件</span>
            </i-col>
            <i-col style="margin: 5px" span="22">
                <Transfer
                    :titles="['可选自研中间件', '已选自研中间件']"
                    :operations="['To left', 'To right']"
                    :data="paAppList"
                    :list-style="listStyle"
                    :target-keys="paTargetKeys"
                    :render-format="customRender"
                    filterable
                    :filter-method="filterMethod"
                    @on-change="handlePaChange"
                ></Transfer>
            </i-col>
        </Row>

        <Row style="margin-left: 5px;display: flex;align-items: center;">
            <i-col style="margin: 5px" span="2">
                <Icon type="ios-apps" style="margin-right: 5px" />
                <span style="text-align: left; display: inline-block; width:90px;">第三方中间件</span>
            </i-col>
            <i-col style="margin: 5px" span="22">
                <Transfer
                    :titles="['可选第三方中间件', '已选第三方中间件']"
                    :operations="['To left', 'To right']"
                    :data="d3AppList"
                    :list-style="listStyle"
                    :target-keys="d3TargetKeys"
                    :render-format="customRender"
                    filterable
                    :filter-method="filterMethod"
                    @on-change="handle3dChange"
                ></Transfer>
            </i-col>
        </Row>

        <Row style="margin-left: 5px;display: flex;align-items: center;">
            <i-col style="margin: 5px" span="2">
                <Icon type="md-send" style="margin-right: 5px" />
                <span style="text-align: left; display: inline-block; width:60px;">操作</span>
            </i-col>
            <i-col style="margin: 5px" span="16">
                <Button type="primary" size="small" @click="save">保存</Button>
            </i-col>
        </Row>
    </Card>
</template>

<script>
import Tables from '@/components/tables'
import {
    get_biz_app_list,
    get_biz_relative_schedule,
    getBisNameLIstInfo,
    save_biz_rel_apps
} from '@/spider-api/biz-mgt'
import { getAppList } from '@/spider-api/env-init'

export default {
    name: 'BizAppsRelation',
    components: {
        Tables
    },
    data() {
        return {
            biz: '',
            biz_list: [],
            listStyle: {
                // width: '300px',
                width: '430px',
                height: '280px'
            },
            appList: [],
            depAppList: [],
            paAppList: [],
            d3AppList: [],
            targetKeys: [],
            depTargetKeys: [],
            paTargetKeys: [],
            d3TargetKeys: []
        }
    },
    computed: {},
    methods: {
        handlePlayBack(biz) {
            get_biz_app_list({ biz_code: biz })
                .then(res => {
                    let data_list = res.data.data
                    if (data_list) {
                        this.targetKeys = data_list
                            .filter(item => {
                                return item.biz_app_bind_type == 1
                            })
                            .map(item => {
                                return item.app_module_name
                            })

                        this.depTargetKeys = data_list
                            .filter(item => {
                                return item.biz_app_bind_type == 2
                            })
                            .map(item => {
                                return item.app_module_name
                            })

                        this.paTargetKeys = data_list
                            .filter(item => {
                                return item.biz_app_bind_type == 3
                            })
                            .map(item => {
                                return item.app_module_name
                            })

                        this.d3TargetKeys = data_list
                            .filter(item => {
                                return item.biz_app_bind_type == 4
                            })
                            .map(item => {
                                return item.app_module_name
                            })
                    } else {
                        this.$Message.error('业务被测应用获取失败！')
                    }
                })
                .catch(err => {
                    console.log(err)
                })
        },
        bizChangeSelect(params) {
            this.biz = params
            this.targetKeys = []
            this.depTargetKeys = []
            this.handlePlayBack(this.biz)
        },
        handleAppChange(newTargetKeys) {
            this.targetKeys = newTargetKeys
        },
        handleDepChange(newTargetKeys) {
            this.depTargetKeys = newTargetKeys
        },
        handlePaChange(newTargetKeys) {
            this.paTargetKeys = newTargetKeys
        },
        handle3dChange(newTargetKeys) {
            this.d3TargetKeys = newTargetKeys
        },
        filterMethod(data, query) {
            console.log(data)
            console.log(query)
            return data.label.indexOf(query) > -1
        },
        initBizList() {
            getBisNameLIstInfo().then(res => {
                let data_list = res.data.data
                if (data_list) {
                    this.biz_list = data_list.map(item => {
                        return {
                            value: item.biz_code,
                            label: item.biz_name
                        }
                    })
                } else {
                    this.$Message.error('业务查询失败')
                }
            })
        },
        initAppList() {
            getAppList({ app_type: 1 }).then(res => {
                let data_list = res.data.data
                console.log(data_list)
                if (data_list) {
                    this.appList = data_list.map(item => {
                        return { key: item[0], label: item[3] + '-' + item[0], disabled: false, team: item[3] }
                    })
                } else {
                    this.$Message.error('业务查询失败')
                }
            })
        },
        initDepAppList() {
            getAppList({ app_type: 2 }).then(res => {
                let data_list = res.data.data
                console.log(data_list)
                if (data_list) {
                    this.depAppList = data_list.map(item => {
                        return { key: item[0], label: item[3] + '-' + item[0], disabled: false, team: item[3] }
                    })
                } else {
                    this.$Message.error('业务查询失败')
                }
            })
        },
        initPaAppList() {
            getAppList({ app_type: 3 }).then(res => {
                let data_list = res.data.data
                console.log(data_list)
                if (data_list) {
                    this.paAppList = data_list.map(item => {
                        return { key: item[0], label: item[3] + '-' + item[0], disabled: false, team: item[3] }
                    })
                } else {
                    this.$Message.error('业务查询失败')
                }
            })
        },
        init3dAppList() {
            getAppList({ app_type: 4 }).then(res => {
                let data_list = res.data.data
                console.log(data_list)
                if (data_list) {
                    this.d3AppList = data_list.map(item => {
                        return { key: item[0], label: item[3] + '-' + item[0], disabled: false, team: item[3] }
                    })
                } else {
                    this.$Message.error('业务查询失败')
                }
            })
        },
        customRender(item) {
            return item.label
        },
        save() {
            if (this.biz == '') {
                this.$Message.error('请选择业务')
                return
            }
            get_biz_relative_schedule({ biz_code: this.biz }).then(res => {
                if (res.data.status === 'success') {
                    this.$Modal.confirm({
                        title: '提示',
                        content: '<p>改业务绑定的应用可能会影响以下迭代自动化执行：</p>' + res.data.msg,
                        onOk: () => {
                            save_biz_rel_apps({
                                biz_code: this.biz,
                                test_apps: this.targetKeys,
                                dep_apps: this.depTargetKeys,
                                pa_apps: this.paTargetKeys,
                                d3_apps: this.d3TargetKeys
                            }).then(res => {
                                this.$Message.warning(res.data.msg)
                            })
                        }
                    })
                } else {
                    save_biz_rel_apps({
                        biz_code: this.biz,
                        test_apps: this.targetKeys,
                        dep_apps: this.depTargetKeys,
                        pa_apps: this.paTargetKeys,
                        d3_apps: this.d3TargetKeys
                    }).then(res => {
                        this.$Message.warning(res.data.msg)
                    })
                }
            })
        }
    },
    created() {},
    mounted() {
        this.initBizList()
        this.initAppList()
        this.initDepAppList()
        this.initPaAppList()
        this.init3dAppList()
    }
}
</script>

<style lang="less" scoped></style>
