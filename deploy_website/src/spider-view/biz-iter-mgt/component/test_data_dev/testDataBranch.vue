<!--
 * @Author: chao.bao <EMAIL>
 * @Date: 2024-09-19 10:11:53
 * @LastEditors: chao.bao <EMAIL>
 * @LastEditTime: 2024-09-19 14:33:20
 * @FilePath: /website_web/deploy_website/src/spider-view/biz-iter-mgt/component/test_data_dev/testDataBranch.vue
 * @Description: 分支管理
-->
<template>
    <Card shadow style="height: 100%;overflow-y: auto;position: relative;">
        <!-- 筛选条件 -->
        <searchForm v-model="searchData" size="small" :serachConfig="serachConfig" @on-change="changeHnadle" />
    </Card>
</template>

<script>
import { get_test_iter_list } from '@/spider-api/biz-mgt'
import { getDataDevelopmentEnv } from '@/api/test-env'
import searchForm from '@/components/searchForm'
export default {
  name: 'testDataBranch',
  components: {
    searchForm
  },
  props: ['bis_name_list'],
  data () {
    return {
      searchData: {
        biz_code: '',
        biz_br_name: '',
        env_name: ''
      },
      biz_br_name_list: [],
      biz_br_name_obj_list: [],
      env_name_list: []
    }
  },
  computed: {
    serachConfig () {
      return [
        {
          type: 'select',
          key: 'biz_code',
          label: '选择业务',
          icon: 'ios-albums',
          options: this.bis_name_list.map(item => {
            return {
              label: item.biz_name,
              value: item.biz_code
            }
          })
        },
        {
          type: 'select',
          key: 'biz_br_name',
          label: '开发分支',
          icon: 'ios-trending-up',
          options: this.biz_br_name_list
        },
        {
          type: 'select',
          key: 'env_name',
          label: '执行环境',
          icon: 'ios-aperture-outline',
          options: this.env_name_list
        }
      ]
    }
  },
  methods: {
    sendData (biz_test_iter_id) {
      this.$emit('send-data', {
        biz_code: this.searchData.biz_code,
        biz_br_name: this.searchData.biz_br_name,
        env_name: this.searchData.env_name,
        biz_br_name_list: this.biz_br_name_list || [],
        biz_br_name_obj_list: this.biz_br_name_obj_list || [],
        env_name_list: this.env_name_list || []
      })
    },
    // 查询分支下拉列表数据
    async getBranchList (biz_code) {
      const res = await get_test_iter_list({ biz_code })
      const data_list = res.data.data
      if (data_list) {
        this.biz_br_name_obj_list = data_list
        this.biz_br_name_list = data_list.map((item) => {
          return {
            value: item.biz_test_iter_br,
            label: item.biz_test_iter_br
          }
        })
        // 不展示master
        this.biz_br_name_list = this.biz_br_name_list.filter((item) => {
          return item.value !== 'master'
        })
        this.sendData()
      } else {
        this.$Message.error('业务分支获取失败！')
      }
    },
    changeHnadle (key, val) {
      this.sendData()
      if (key === 'biz_code') {
        this.getBranchList(val)
      }
    },
    // 查询环境下拉数据
    init_suite_info () {
      let data = {
        page: 1,
        size: 200,
        type_name: 'data_development'
      }
      getDataDevelopmentEnv(data).then(res => {
        let env_list = res.data.data['results']
        console.log('env_list----', env_list)

        if (env_list) {
          this.env_name_list = env_list.filter((item) => {
            return item.suite_code.indexOf('it') !== -1
          }).map((item) => {
            return {
              value: item.suite_code,
              label: item.suite_code
            }
          })
          this.sendData()
        } else {
          this.$Message.error('环境获取失败！')
        }
      })
    }
  },
  created () {
    this.init_suite_info()
  }
}
</script>

<style scoped>

</style>
