<template>
    <Card shadow style="height: 100%;overflow: auto">
        <!-- 筛选条件 -->
        <searchForm v-model="searchData" size="small" :serachConfig="serachConfig" />
        <Row style="margin-top: 10px;display: flex;align-items: center;">
            <i-col style="margin: 5px" span="2">
                <Icon type="ios-apps-outline" style="margin-right: 5px" />
                <span style="text-align: left; display: inline-block; width:60px;">应用分支</span>
            </i-col>
            <i-col span="18">
                <div class="btn_custom">
                    <Button type="primary" ghost size="small" @click="branchReset">一键指定归档版本</Button>
                </div>
                <tables
                    v-if="reLoadTable"
                    class="cutom-table"
                    @on-selection-change="selectApp"
                    ref="selection"
                    v-model="appList"
                    :columns="columns"
                    :rowClassName="rowClassName"
                />
            </i-col>
        </Row>

        <Row style="margin-top: 10px;display: flex;align-items: center;">
            <i-col style="margin: 5px" span="2">
                <Icon type="logo-youtube" style="margin-right: 5px" />
                <span style="text-align: left; display: inline-block; width:60px;">操作&nbsp;</span>
            </i-col>
            <i-col span="4">
                <Tooltip :content="content" placement="top" :max-width="200">
                    <Button type="primary" :loading="loading" size="small" @click="execute_confirm"
                        >开发测试数据初始化</Button
                    >
                </Tooltip>
            </i-col>

            <Tooltip content="仅跳转到你最后一次初始化的详情" placement="top-end">
                <Button type="primary" icon="ios-search" ghost size="small" @click="goExecHistory"
                    >跳转初始化详情</Button
                >
            </Tooltip>
        </Row>
    </Card>
</template>

<script>
import { doJenkinsTestDataDeploy } from '@/spider-api/pipeline'
import { deepClone } from '@/utils'

import Tables from '@/components/tables'
import { getLastUser, execute_history_retrieve, get_biz_app_list, get_test_iter_list } from '@/spider-api/biz-mgt'
import { getDataDevelopmentEnv } from '@/api/test-env'

import searchForm from '@/components/searchForm'

export default {
    name: 'testDataPipelineNew',
    components: { Tables, searchForm },
    props: ['bis_name_list', 'biz_param'],
    data() {
        return {
            content: '',
            loading: false,
            searchData: {
                biz_code: '',
                biz_br_master: '',
                biz_br_name: '',
                env_name: ''
            },
            // 选择业务
            reLoadTable: true,
            appList: [],
            biz_br_name_obj_list: [],
            columns: [
                { title: '应用名', key: 'app_module_name' },
                { title: '数据库', key: 'db_names', tooltip: true, tooltipMaxWidth: 900 },
                { title: '线上版本', key: 'online_br' },
                { title: '归档版本', key: 'archive_br' },
                {
                    title: '选择版本',
                    key: 'br_name',
                    render: (h, params) => {
                        let op_list = params.row.br_names.map(item => {
                            return h('Option', {
                                // 下拉框的值
                                props: {
                                    value: item,
                                    label: item
                                }
                            })
                        })
                        return h(
                            'Select',
                            {
                                props: {
                                    placeholder: params.row.br_name,
                                    value: params.row.br_name,
                                    transfer: true,
                                    clearable: true,
                                    filterable: true
                                },
                                style: {
                                    width: '100px'
                                },
                                on: {
                                    'on-change': val => {
                                        params.row.br_name = val // 改变下拉框赋值
                                        this.updateAppList[params.row.app_module_name] = params.row.br_name
                                        this.appList[params.index].cur_br_name = val
                                        this.appList[params.index].br_name = val
                                        console.log('params---', val, params, this.appList)

                                        // this.reLoadTable = false
                                        // this.$nextTick(() => {
                                        //     this.reLoadTable = true
                                        // })
                                    }
                                }
                            },
                            op_list
                        )
                    }
                }
            ],
            updateAppList: {},
            app_selection: []
        }
    },
    computed: {
        serachConfig() {
            return [
                {
                    type: 'select',
                    key: 'biz_code',
                    label: '选择业务',
                    icon: 'ios-albums',
                    disabled: true,
                    options: this.bis_name_list.map(item => {
                        return {
                            label: item.biz_name,
                            value: item.biz_code
                        }
                    })
                },
                {
                    type: 'select',
                    key: 'biz_br_master',
                    label: '目标分支',
                    icon: 'ios-trending-up',
                    disabled: true,
                    options: this.biz_param.biz_br_name_list
                },
                {
                    type: 'select',
                    key: 'biz_br_name',
                    label: 'dump 源',
                    icon: 'ios-trending-up',
                    disabled: false,
                    options: [
                        {
                            label: 'master',
                            value: 'master'
                        },
                        {
                            label: `${this.biz_param.biz_br_name}`,
                            value: `${this.biz_param.biz_br_name}`
                        }
                    ]
                },
                {
                    type: 'select',
                    key: 'env_name',
                    label: '执行环境',
                    icon: 'ios-aperture-outline',
                    disabled: true,
                    options: this.biz_param.env_name_list
                }
            ]
        }
    },
    methods: {
        branchReset() {
            this.appList = this.appList.map(item => {
                item.br_name = item.archive_br
                this.updateAppList[item.app_module_name] = item.archive_br
                return item
            })
        },
        bizBrNameChangeSelect(params, flow_change) {
            this.searchData.biz_br_name = params
            for (let bizObj in this.biz_br_name_obj_list) {
                if (this.biz_br_name_obj_list[bizObj].biz_test_iter_br == params) {
                    let biz_test_iter_id = ''
                    for (let bizObj in this.biz_br_name_obj_list) {
                        if (this.biz_br_name_obj_list[bizObj].biz_test_iter_br == this.searchData.biz_br_name) {
                            biz_test_iter_id = this.biz_br_name_obj_list[bizObj].biz_test_iter_id
                        }
                    }
                    this.get_biz_app_lists({
                        biz_code: this.biz_br_name_obj_list[bizObj].biz_code,
                        biz_test_iter_id: biz_test_iter_id
                    })
                    // this.sendData(biz_test_iter_id)
                }
            }
        },

        // 控制颜色
        rowClassName(row, index) {
            if (row.br_name && row.br_name !== row.archive_br) {
                return 'custom-table-tr-style'
            }
            // if (row.cur_br_name && row.cur_br_name !== row.archive_br) {
            //     return 'custom-table-tr-style'
            // }

            if (this.updateAppList[row.app_module_name] && row.archive_br !== this.updateAppList[row.app_module_name]) {
                return 'custom-table-tr-style'
            }
            return ''
        },
        selectApp(selection) {
            this.app_selection = selection
        },
        get_biz_app_lists(param) {
            get_biz_app_list(param)
                .then(res => {
                    let data_list = res.data.data
                    if (data_list) {
                        this.appList = data_list
                            .filter(item => {
                                let br_name_list = []
                                if (item.archive_br) {
                                    br_name_list = [item.archive_br]
                                }
                                let temp_br_name_list = item.br_name_fifteen ? item.br_name_fifteen.split(',') : []
                                br_name_list.push(...temp_br_name_list)
                                item.br_names = br_name_list
                                return (
                                    item.br_names.length > 0 &&
                                    (item.biz_app_bind_type === 1 || item.biz_app_bind_type === 2 || item.biz_app_bind_type === 3)
                                )
                            })
                            .map(item => {
                                // let br_name_list = [item.biz_iter_app_archive_br_name]
                                // item.br_name = item.biz_iter_app_archive_br_name
                                // if (item.archive_br) {
                                //     br_name_list.push(item.archive_br)
                                // }
                                // if (item.online_br) {
                                //     br_name_list.push(item.online_br)
                                // }
                                let br_name_list = []
                                let temp_br_name_list = item.br_name_fifteen ? item.br_name_fifteen.split(',') : []
                                br_name_list.push(...temp_br_name_list)
                                item.br_names = br_name_list
                                if (!item.br_name) {
                                    item.br_name = item.br_names[0]
                                }
                                this.updateAppList[item.app_module_name] = item.br_name
                                return item
                            })
                        // 过滤掉null的数据？
                        this.appList.map(item => {
                            item.br_names = item.br_names.filter(item => {
                                return item
                            })
                        })
                        // 默认选中所有数据
                        this.app_selection = deepClone(this.appList)
                    } else {
                        this.$Message.error('业务被测应用获取失败！')
                    }
                })
                .catch(err => {
                    console.log(err)
                })
        },
        execute_confirm() {
            this.$Modal.confirm({
                title: '提示',
                content: '<p>应用分支请选择归档版本，否则影响归档</p>',
                onOk: () => {
                    this.saveConfig()
                }
            })
        },
        saveConfig() {
            let app_list = []
            this.app_selection.map(item => {
                app_list.push({
                    module_name: item.app_module_name,
                    branch_name: this.updateAppList[item.app_module_name]
                })
            })
            let biz_test_iter_id = ''
            for (let bizObj in this.biz_br_name_obj_list) {
                if (this.biz_br_name_obj_list[bizObj].biz_test_iter_br === this.searchData.biz_br_name) {
                    biz_test_iter_id = this.biz_br_name_obj_list[bizObj].biz_test_iter_id
                }
            }
            if (!this.searchData.biz_code) {
                this.$Message.warning('请选择业务')
                return false
            }
            if (!this.searchData.biz_br_name) {
                this.$Message.warning('请选择分支')
                return false
            }
            if (!this.searchData.env_name) {
                this.$Message.warning('请选择执行环境')
                return false
            }
            if (app_list.length == 0) {
                this.$Message.warning('请至少选择一个应用及其版本')
                return false
            }
            this.$emit('handle-loading', true)
            doJenkinsTestDataDeploy(
                'test_data_init',
                biz_test_iter_id,
                this.searchData.env_name,
                app_list,
                this.searchData.biz_br_master
            )
                .then(res => {
                    if (res.data.status === 'success') {
                        this.$Notice.success({
                            title: 'success',
                            desc: '初始化命令已发出，稍后点击跳转详情'
                        })
                    } else {
                        this.$Message.error(res.data.msg)
                    }
                })
                .catch(err => {
                    this.$Message.error(err.response.data.msg)
                })
                .finally(() => {
                    this.$emit('handle-loading', false)
                })
        },
        goExecHistory() {
            let biz_test_iter_id = ''
            for (let bizObj in this.biz_br_name_obj_list) {
                if (this.biz_br_name_obj_list[bizObj].biz_test_iter_br === this.searchData.biz_br_name) {
                    biz_test_iter_id = this.biz_br_name_obj_list[bizObj].biz_test_iter_id
                }
            }
            if (biz_test_iter_id.trim().length === 0) {
                this.$Message.warning('请先选择分支')
                return false
            }
            execute_history_retrieve({
                exec_action_type: 'test_data_init',
                biz_test_iter_id: biz_test_iter_id
            }).then(res => {
                if (res.data.status != 'success') {
                    this.$Message.warning(res.data.msg)
                    return false
                }
                if (res.data.data) {
                    window.open(res.data.data, '_blank')
                    return false
                } else {
                    this.$Message.warning('没有查询到你的执行记录')
                }
            })
        },

        init() {
            // this.init_suite_info()
            this.searchData = {
                biz_code: this.biz_param.biz_code,
                biz_br_master: this.biz_param.biz_br_name,
                biz_br_name: this.biz_param.biz_br_name,
                env_name: this.biz_param.env_name
            }
            this.biz_br_name_obj_list = this.biz_param.biz_br_name_obj_list
            this.bizBrNameChangeSelect(this.biz_param.biz_br_name)
            let biz_test_iter_id = ''
            for (let bizObj in this.biz_br_name_obj_list) {
                if (this.biz_br_name_obj_list[bizObj].biz_test_iter_br === this.searchData.biz_br_name) {
                    biz_test_iter_id = this.biz_br_name_obj_list[bizObj].biz_test_iter_id
                }
            }
            getLastUser({ bis_pipeline_id: biz_test_iter_id, suite_code: this.biz_param.env_name }).then(res => {
                console.log('res------', res)
                if (res.data.status !== 'success') {
                    this.content = res.data.msg
                } else {
                    this.content = ''
                }
            })
        }
    },
    mounted() {
        // this.init()
    }
}
</script>
<style lang="less" scoped>
.cutom-table {
    /deep/ .ivu-table {
        /* 自定义表格样式 */
        .custom-table-tr-style td {
            background-color: #f79f9fff;
        }
    }
}
.btn_custom {
    display: flex;
    justify-content: end;
    margin-bottom: 10px;
}
</style>
