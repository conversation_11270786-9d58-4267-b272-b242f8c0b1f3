<!--
 * @Author: chao.bao <EMAIL>
 * @Date: 2024-08-26 14:34:46
 * @LastEditors: chao.bao <EMAIL>
 * @LastEditTime: 2024-09-13 16:52:22
 * @FilePath: /website_web/deploy_website/src/spider-view/biz-iter-mgt/component/test_data_dev/testDataDump.vue
 * @Description: dump tab页面
-->
<template>
    <Card shadow style="height: 100%;overflow-y: auto;position: relative;">
        <!-- 筛选条件 -->
        <searchForm v-model="searchData" size="small" :serachConfig="serachConfig" @on-change="changeHnadle" />
        <!-- 列表 -->
         <!-- @on-selection-change="selectApp" -->
        <Tables ref="selection" v-model="appList" :columns="columns"/>
        <div class="btn_flex">
          <Button class="btn" size="small" type="info" :loading="loading" @click="dumpHandler">提交DUMP</Button>
          <Button type="primary" icon="ios-search" ghost size="small" @click="goExecHistory">跳转dump制作详情</Button>
        </div>
    </Card>
</template>

<script>
import { get_biz_app_list, submitDump, getDumpDetail } from '@/spider-api/biz-mgt'
import Tables from '@/components/tables'
import searchForm from '@/components/searchForm'
export default {
  name: 'TestDataDump',
  components: {
    searchForm,
    Tables
  },
  props: ['bis_name_list', 'biz_param'],
  data () {
    return {
      loading: false,
      searchData: {
        biz_code: '',
        biz_br_name: '',
        env_name: ''
      },
      biz_test_iter_id: '', // 当前选中的分支对应的id
      appList: [],
      columns: [
        // {
        //   type: 'selection',
        //   align: 'center',
        //   width: 60
        // },
        { title: '应用', key: 'app_module_name' },
        { title: '数据库', key: 'db_names', tooltip: true, tooltipMaxWidth: 900 },
        { title: '归档分支', key: 'archive_br' },
        { title: '选择版本', key: 'br_name' }
        // {
        //   title: '选择版本',
        //   key: 'br_name',
        //   render: (h, params) => {
        //     let op_list = params.row.br_names.map((item) => {
        //       return h('Option', { // 下拉框的值
        //         props: {
        //           value: item.trim(),
        //           label: item.trim()
        //         }
        //       })
        //     })
        //     return h(
        //       'Select',
        //       {
        //         props: {
        //           // placeholder: params.row.br_name,
        //           value: params.row.br_name,
        //           transfer: true,
        //           clearable: true
        //         },
        //         style: {
        //           width: '100px'
        //         },
        //         on: {
        //           'on-change': val => {
        //             // 更新数据源
        //             console.log('params---', params)

        //             this.appList[params.index].br_name = val
        //           }
        //         }
        //       },
        //       op_list
        //     )
        //   }
        // }
      ]
    }
  },
  computed: {
    serachConfig () {
      return [
        {
          type: 'select',
          key: 'biz_code',
          label: '选择业务',
          icon: 'ios-albums',
          disabled: true,
          options: this.bis_name_list.map(item => {
            return {
              label: item.biz_name,
              value: item.biz_code
            }
          })
        },
        {
          type: 'select',
          key: 'biz_br_name',
          label: '目标分支',
          icon: 'ios-trending-up',
          disabled: true,
          options: this.biz_param.biz_br_name_list
        },
        {
          type: 'select',
          key: 'env_name',
          label: '选择环境',
          icon: 'ios-aperture-outline',
          disabled: true,
          options: this.biz_param.env_name_list
        }
      ]
    }
  },
  methods: {
    changeHnadle (key, val) {
      console.log('changeHnadle-----', key, val, this.biz_param.biz_br_name_obj_list)
      if (key === 'biz_br_name') {
        const obj = this.biz_param.biz_br_name_obj_list.filter(item => {
          return item.biz_test_iter_br === val
        })[0]
        console.log('obj----------------', obj)

        const biz_code = obj.biz_code || ''
        this.biz_test_iter_id = obj.biz_test_iter_id || ''
        get_biz_app_list({
          biz_code,
          biz_test_iter_id: this.biz_test_iter_id
        }).then(res => {
          console.log(res)
          let data_list = res.data.data
          if (data_list) {
            let arr = []
            arr = data_list.filter((item) => {
              let br_name_list = []
              if (item.archive_br) {
                br_name_list = [item.archive_br]
              }
              let temp_br_name_list = item.br_name_fifteen ? item.br_name_fifteen.split(',') : []
              br_name_list.push(...temp_br_name_list)
              item.br_names = br_name_list
              return item.br_names.length > 0 && (item.biz_app_bind_type === 1 || item.biz_app_bind_type === 2)
            })
            arr = arr.map((item) => {
              item.br_name = item.biz_iter_app_archive_br_name || item.br_names[0]
              let br_name_list = [item.biz_iter_app_archive_br_name]
              if (item.archive_br) {
                br_name_list.push(item.archive_br)
              }
              if (item.online_br) {
                br_name_list.push(item.online_br)
              }
              let temp_br_name_list = item.br_name_fifteen ? item.br_name_fifteen.split(',') : []
              br_name_list.push(...temp_br_name_list)
              item.br_names = br_name_list
              return item
            })
            arr.map((item) => {
              item.br_names = item.br_names.filter((item) => {
                return item
              })
            })
            arr.forEach(item => {
              this.appList.push(item)
            })
          } else {
            this.$Message.error('业务被测应用获取失败！')
          }
        })
      }
    },
    dumpHandler () {
      this.loading = true
      submitDump({
        bis_pipeline_id: this.biz_test_iter_id,
        suite_code: this.biz_param.env_name
      }).then(res => {
        console.log(res)
        const { status, msg, data } = res.data
        if (status === 'success') {
          this.$Message.success('命令触发成功，请点击跳转详情查看结果')
        } else {
          this.$Message.error(msg)
        }
      }).finally(() => {
        this.loading = false
      })
    },
    goExecHistory () {
      getDumpDetail({
        bis_pipeline_id: this.biz_test_iter_id
      }).then(res => {
        if (res.data.status !== 'success') {
          this.$Message.warning(res.data.msg)
          return false
        }
        if (res.data.data.job_url) {
          window.open(res.data.data.job_url, '_blank')
          return false
        } else {
          this.$Message.warning('没有查询到你的执行记录')
        }
      })
    },
    init () {
      console.log('init')
      this.searchData.biz_code = this.biz_param.biz_code
      this.searchData.env_name = this.biz_param.env_name
      this.searchData.biz_br_name = this.biz_param.biz_br_name
      this.appList = []
      this.changeHnadle('biz_br_name', this.biz_param.biz_br_name)
    }
  }
}
</script>

<style lang="less" scoped>
.btn {
    margin-right: 20px;
}
.btn_flex {
  margin-top: 20px;
}
</style>
