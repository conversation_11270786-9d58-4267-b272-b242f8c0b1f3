<!--
 * @Author: chao.bao <EMAIL>
 * @Date: 2024-08-26 14:34:46
 * @LastEditors: chao.bao <EMAIL>
 * @LastEditTime: 2024-09-13 17:02:32
 * @FilePath: /website_web/deploy_website/src/spider-view/biz-iter-mgt/component/test_data_dev/testDataDump.vue
 * @Description: 归档 tab页面
-->
<template>
  <Card shadow style="height: 100%;overflow-y: auto;position: relative;">
      <!-- 筛选条件 -->
      <searchForm v-model="searchData" size="small" :serachConfig="serachConfig" @on-change="changeHnadle" />
      <!-- 列表 -->
      <Tables ref="selection" v-model="appList" :columns="columns"/>
      <div class="btn_flex">
        <Button class="btn" size="small" type="info" :loading="loading" @click="dumpHandler">归档</Button>
        <Button type="primary" icon="ios-search" ghost size="small" @click="goExecHistory">跳转归档详情</Button>
      </div>
      <Modal
        v-model="modleFlag"
        title="失败拦截（原因）：部分研发迭代未归档"
        @on-ok="modleFlag = false"
        @on-cancel="modleFlag = false"
      >
        <Tables v-model="modalData" :columns="modalColumns"/>
      </Modal>
  </Card>
</template>

<script>
import { get_biz_app_list, submitArchDump, getArchDumpDetail } from '@/spider-api/biz-mgt'
import Tables from '@/components/tables'
import searchForm from '@/components/searchForm'
export default {
  name: 'TestDataDump',
  components: {
    searchForm,
    Tables
  },
  props: ['bis_name_list', 'biz_param'],
  data () {
    return {
      loading: false,
      searchData: {
        biz_code: '',
        biz_br_name: '',
        biz_br_master: 'master',
        env_name: ''
      },
      biz_test_iter_id: '',
      appList: [],
      columns: [
        { title: '应用', key: 'app_module_name' },
        { title: '数据库', key: 'db_names', tooltip: true, tooltipMaxWidth: 900 }
      ],
      // 弹框
      modleFlag: false,
      modalData: [],
      modalColumns: [
        { title: '应用名', key: 'app_name' },
        { title: '线上版本', key: 'archive_br' },
        { title: '初始化版本', key: 'archive_br_name' }
      ]
    }
  },
  computed: {
    serachConfig () {
      return [
        {
          type: 'select',
          key: 'biz_code',
          label: '选择业务',
          icon: 'ios-albums',
          disabled: true,
          options: this.bis_name_list.map(item => {
            return {
              label: item.biz_name,
              value: item.biz_code
            }
          })
        },
        {
          type: 'select',
          key: 'biz_br_name',
          label: '来源分支',
          icon: 'ios-trending-up',
          disabled: true,
          options: this.biz_param.biz_br_name_list
        },
        {
          type: 'select',
          key: 'biz_br_master',
          label: '目标分支',
          icon: 'ios-trending-up',
          disabled: true,
          options: [{
            label: 'master',
            value: 'master'
          }]
        },
        {
          type: 'select',
          key: 'env_name',
          label: '选择环境',
          icon: 'ios-aperture-outline',
          disabled: true,
          options: this.biz_param.env_name_list
        }
      ]
    }
  },
  methods: {
    changeHnadle (key, val) {
      console.log('changeHnadle-----', key, val)
      if (key === 'biz_br_name') {
        const obj = this.biz_param.biz_br_name_obj_list.filter(item => {
          return item.biz_test_iter_br === val
        })[0]
        const biz_code = obj.biz_code || ''
        this.biz_test_iter_id = obj.biz_test_iter_id || ''
        get_biz_app_list({
          biz_code,
          biz_test_iter_id: this.biz_test_iter_id
        }).then(res => {
          console.log(res)
          let data_list = res.data.data
          if (data_list) {
            let arr = []
            arr = data_list.filter((item) => {
              let br_name_list = []
              if (item.archive_br) {
                br_name_list = [item.archive_br]
              }
              let temp_br_name_list = item.br_name_fifteen ? item.br_name_fifteen.split(',') : []
              br_name_list.push(...temp_br_name_list)
              item.br_names = br_name_list
              return item.br_names.length > 0 && (item.biz_app_bind_type === 1 || item.biz_app_bind_type === 2)
            })
            arr.forEach(item => {
              this.appList.push(item)
            })
          } else {
            this.$Message.error('业务被测应用获取失败！')
          }
        })
      }
    },
    dumpHandler () {
      this.loading = true
      submitArchDump({
        bis_pipeline_id: this.biz_test_iter_id,
        suite_code: this.biz_param.env_name
      }).then(res => {
        const { status, msg, data } = res.data
        if (status === 'success') {
          this.$Message.success('命令触发成功，请点击跳转详情查看结果')
        } else {
          // 失败
          if (data.length === 0) {
            this.$Message.error(msg)
          } else {
            this.modalData = data
            this.modleFlag = true
          }
        }
      }).finally(() => {
        this.loading = false
      })
    },
    goExecHistory () {
      getArchDumpDetail({
        bis_pipeline_id: this.biz_test_iter_id
      }).then(res => {
        if (res.data.status !== 'success') {
          this.$Message.warning(res.data.msg)
          return false
        }
        if (res.data.data.job_url) {
          window.open(res.data.data.job_url, '_blank')
          return false
        } else {
          this.$Message.warning('没有查询到你的执行记录')
        }
      })
    },
    init () {
      this.searchData.biz_code = this.biz_param.biz_code
      this.searchData.env_name = this.biz_param.env_name
      this.searchData.biz_br_name = this.biz_param.biz_br_name
      this.appList = []
      this.changeHnadle('biz_br_name', this.biz_param.biz_br_name)
    }
  }
}
</script>

<style lang="less" scoped>
.btn {
    margin-right: 20px;
}
.btn_flex {
  margin-top: 20px;
}
</style>
