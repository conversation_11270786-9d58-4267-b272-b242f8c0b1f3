<!--
 * @Author: chao.bao <EMAIL>
 * @Date: 2024-08-26 10:23:35
 * @LastEditors: chao.bao <EMAIL>
 * @LastEditTime: 2024-09-11 10:25:39
 * @FilePath: /website_web/deploy_website/src/spider-view/biz-iter-mgt/test_data_dump.vue
 * @Description: dump还原页面
-->
<template>
  <Card shadow style="height: 100%;overflow-y: auto;position: relative;">
      <!-- 筛选条件 -->
      <searchForm v-model="searchData" size="small" :serachConfig="serachConfig" @on-change="changeHnadle" />
      <!-- 列表 -->
      <Tables ref="selection" v-model="appList" :columns="columns"/>
      <Button class="btn" size="small" type="info" :loading="loading" @click="dumpHandler">dump还原</Button>
      <Modal
      v-model="modleFlag"
      title="失败拦截"
      @on-ok="modleFlag = false"
      @on-cancel="modleFlag = false"
    >
      <Tables v-model="modalData" :columns="modalColumns"/>
    </Modal>
  </Card>
</template>

<script>
import { getBisNameLIstInfo, get_test_iter_list, get_biz_app_list, submitDumpRestore } from '@/spider-api/biz-mgt'
import { getDataDevelopmentEnv } from '@/api/test-env'
import Tables from '@/components/tables'
import searchForm from '@/components/searchForm'
export default {
  name: 'TestDataDump',
  components: {
    searchForm,
    Tables
  },
  data () {
    return {
      loading: false,
      bis_name_list: [],
      biz_br_name_obj_list: [],
      biz_br_name_list: [],
      env_name_list: [],
      biz_test_iter_id: '',
      searchData: {
        biz_code: '',
        biz_br_name: '',
        env_name: ''
      },
      appList: [],
      columns: [
        { title: '应用名', key: 'app_module_name' },
        { title: '数据库', key: 'db_names', tooltip: true, tooltipMaxWidth: 900 },
        { title: '归档分支', key: 'archive_br' },
        { title: '选择版本', key: 'br_name' }
      ],
      // 弹框
      modleFlag: false,
      modalData: [],
      modalColumns: [
        { title: '应用名', key: 'app_name' },
        { title: '线上版本', key: 'archive_br' },
        { title: '初始化版本', key: 'archive_br_name' }
      ]
    }
  },
  computed: {
    serachConfig () {
      return [
        {
          type: 'select',
          key: 'biz_code',
          label: '选择业务',
          icon: 'ios-albums',
          options: this.bis_name_list.map(item => {
            return {
              label: item.biz_name,
              value: item.biz_code
            }
          })
        },
        {
          type: 'select',
          key: 'biz_br_name',
          label: '选择分支',
          icon: 'ios-trending-up',
          options: this.biz_br_name_list
        },
        {
          type: 'select',
          key: 'env_name',
          label: '选择环境',
          icon: 'ios-aperture-outline',
          options: this.env_name_list
        }
      ]
    }
  },
  methods: {
    /**
     * @description: 查询业务下拉列表数据
     * @return {*}
     */
    getBizCodeList () {
      getBisNameLIstInfo().then(res => {
        this.bis_name_list = res.data.data
      })
    },
    /**
     * @description: 查询分支列表数据
     * @param {*} biz_code
     * @return {*}
     */
    async getBranchList (biz_code) {
      const res = await get_test_iter_list({ biz_code })
      const data_list = res.data.data
      if (data_list) {
        this.biz_br_name_obj_list = data_list
        this.biz_br_name_list = data_list.map((item) => {
          return {
            value: item.biz_test_iter_br,
            label: item.biz_test_iter_br
          }
        })
      } else {
        this.$Message.error('业务分支获取失败！')
      }
    },
    /**
     * @description: 查询环境
     * @return {*}
     */
    init_suite_info () {
      let data = {
        page: 1,
        size: 200,
        type_name: 'dump_restore'
      }
      getDataDevelopmentEnv(data).then(res => {
        let env_list = res.data.data['results']
        if (env_list) {
          this.env_name_list = env_list.filter((item) => {
            return item.suite_code.indexOf('it') !== -1
          }).map((item) => {
            return {
              value: item.suite_code,
              label: item.suite_code
            }
          })
        } else {
          this.$Message.error('环境获取失败！')
        }
      })
    },
    queryTableList (val) {
      this.appList = []
      const obj = this.biz_br_name_obj_list.filter(item => {
        return item.biz_test_iter_br === val
      })[0]
      console.log('obj----------------', obj)
      const biz_code = obj.biz_code || ''
      this.biz_test_iter_id = obj.biz_test_iter_id || ''
      get_biz_app_list({
        biz_code,
        biz_test_iter_id: this.biz_test_iter_id
      }).then(res => {
        let data_list = res.data.data
        if (data_list) {
          let arr = []
          arr = data_list.filter((item) => {
            let br_name_list = []
            if (item.archive_br) {
              br_name_list = [item.archive_br]
            }
            let temp_br_name_list = item.br_name_fifteen ? item.br_name_fifteen.split(',') : []
            br_name_list.push(...temp_br_name_list)
            item.br_names = br_name_list
            return item.br_names.length > 0 && (item.biz_app_bind_type === 1 || item.biz_app_bind_type === 2)
          })
          arr = arr.map((item) => {
            item.br_name = item.biz_iter_app_archive_br_name || item.br_names[0]
            let br_name_list = [item.biz_iter_app_archive_br_name]
            if (item.archive_br) {
              br_name_list.push(item.archive_br)
            }
            if (item.online_br) {
              br_name_list.push(item.online_br)
            }
            let temp_br_name_list = item.br_name_fifteen ? item.br_name_fifteen.split(',') : []
            br_name_list.push(...temp_br_name_list)
            item.br_names = br_name_list
            return item
          })
          arr.map((item) => {
            item.br_names = item.br_names.filter((item) => {
              return item
            })
          })
          arr.forEach(item => {
            this.appList.push(item)
          })
        } else {
          this.$Message.error('业务被测应用获取失败！')
        }
      })
    },
    /**
     * @description: 下拉框值改变
     * @param {*} key
     * @param {*} val
     * @return {*}
     */
    changeHnadle (key, val) {
      console.log('changeHnadle-----', key, val)
      if (key === 'biz_code') {
        this.getBranchList(val)
      } else if (key === 'biz_br_name') {
        this.queryTableList(val)
      }
    },
    /**
     * @description: dump回调
     * @return {*}
     */
    dumpHandler () {
      this.loading = true
      submitDumpRestore({
        bis_pipeline_id: this.biz_test_iter_id,
        suite_code: this.searchData.env_name
      }).then(res => {
        const { status, msg, data } = res.data
        if (status === 'success') {
          this.$Message.success(msg)
        } else {
          // 失败
          if (data.length === 0) {
            this.$Message.error(msg)
          } else {
            this.modalData = data
            this.modleFlag = true
          }
        }
      }).finally(() => {
        this.loading = false
      })
    }
  },
  created () {
    this.getBizCodeList()
    this.init_suite_info()
  }
}
</script>

<style lang="less" scoped>
.btn {
    margin-top: 20px;
}
</style>
