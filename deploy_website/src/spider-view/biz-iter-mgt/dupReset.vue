<template>
    <div class="env_application">
        <Tabs v-model="tab_name" ref="tabs">
            <TabPane label="分支管理" name="test_data_branch" :disabled="tab_disable">
                <Card shadow style="height: 500px;overflow-y: auto;position: relative;">
                    <!-- 筛选条件 -->
                    <searchForm
                        v-model="searchData"
                        size="small"
                        :serachConfig="serachConfig"
                        :labelWidth="110"
                        @on-change="changeHnadle"
                    />
                    <div class="btn_flex">
                        <Button class="btn" size="small" type="info" :loading="loading" @click="dumpHandler"
                            >DUMP还原</Button
                        >
                        <Button type="primary" ghost size="small" @click="goExecHistory">跳转Jenkins</Button>
                    </div>
                </Card>
            </TabPane>
        </Tabs>
        <Spin size="large" fix v-if="spinShow">
            <Icon type="ios-loading" size="32" class="demo-spin-icon-load"></Icon>
            <div>请求中...</div>
        </Spin>
    </div>
</template>
<script>
import {
    get_test_iter_list,
    getBisNameLIstInfo,
    get_dump_file_list,
    get_dump_restore,
    post_dump_restore
} from '@/spider-api/biz-mgt'
import searchForm from '@/components/searchForm'

export default {
    components: {
        searchForm
    },
    data() {
        return {
            tab_disable: false,
            tab_name: 'test_data_branch',
            bis_name_list: [],
            biz_br_name_list: [],
            dump_file_list: [],
            suite_code_list: [],
            spinShow: false,
            searchData: {
                biz_code: '',
                biz_br_name: '',
                dump_file: '',
                suite_code: ''
            },
            loading: false,
            url: ''
        }
    },
    computed: {
        serachConfig() {
            return [
                {
                    type: 'select',
                    key: 'biz_code',
                    label: '选择业务',
                    icon: 'ios-albums',
                    options: this.bis_name_list.map(item => {
                        return {
                            label: item.biz_name,
                            value: item.biz_code
                        }
                    })
                },
                {
                    type: 'select',
                    key: 'biz_br_name',
                    label: '开发分支',
                    icon: 'ios-trending-up',
                    options: this.biz_br_name_list
                },
                {
                    type: 'select',
                    key: 'dump_file',
                    label: 'dump文件',
                    icon: 'ios-aperture-outline',
                    options: this.dump_file_list
                },
                {
                    type: 'select',
                    key: 'suite_code',
                    label: '环境',
                    icon: 'ios-aperture-outline',
                    options: this.suite_code_list
                }
            ]
        }
    },
    methods: {
        dumpHandler() {
            this.loading = true
            post_dump_restore({
                db_file_id: this.searchData.dump_file.split('-')[0],
                db_logic_id: this.searchData.dump_file.split('-')[1],
                suite_code: this.searchData.suite_code
            })
                .then(res => {
                    this.loading = false
                    if (res.data.status === 'success') {
                        this.$Message.success(res.data.msg)
                    } else {
                        this.$Message.error(res.data.msg)
                    }
                })
                .catch(err => {
                    this.loading = false
                })
        },
        goExecHistory() {
            window.open(this.url, '_blank')
        },
        async getBranchList() {
            const brRes = await get_test_iter_list({ biz_code: this.searchData.biz_code })
            const data_list = brRes.data.data
            this.biz_br_name_list = data_list.map(item => {
                return {
                    value: item.biz_test_iter_br,
                    label: item.biz_test_iter_br
                }
            })
        },
        getDumpFile() {
            get_dump_file_list({ biz_test_iter_id: `${this.searchData.biz_code}_${this.searchData.biz_br_name}` }).then(
                res => {
                    this.suite_code_list = res.data.data.suite_code_list.map(item => {
                        return {
                            value: item,
                            label: item
                        }
                    })
                    this.dump_file_list = res.data.data.dump_list.map(item => {
                        return {
                            value: `${item.dump_file_id}-${item.db_logic_id}`,
                            label: item.dump_file_name
                        }
                    })
                }
            )
        },
        // 获取业务下拉列表数据
        async getBisNameInfo() {
            this.spinShow = true
            try {
                const res = await getBisNameLIstInfo()
                this.bis_name_list = res.data.data
                this.spinShow = false
            } catch (error) {
                this.spinShow = false
            }
        },
        changeHnadle(key, val) {
            if (key === 'biz_code') {
                this.searchData.biz_br_name = ''
                this.getBranchList(val)
            } else if (key === 'biz_br_name') {
                // 查询dump文件和环境的列表数据
                this.getDumpFile()
            }
        }
    },
    mounted() {
        this.getBisNameInfo()
        get_dump_restore({}).then(res => {
            this.url = res.data.data.job_url
        })
    }
}
</script>
<style lang="less" scoped>
.btn_flex {
    margin-top: 20px;
    .btn {
        margin-right: 20px;
    }
}
</style>
