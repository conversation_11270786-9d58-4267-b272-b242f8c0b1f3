<style scoped>
.env_application {
    width: 100%;
    height: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    position: relative;
}

.card {
    padding-top: 10px;
    padding-left: 10px;
}

.formItem {
    width: 50%;
}

.formButton {
    width: 50%;
    display: flex;
    justify-content: flex-end;
}

.pag {
    display: flex;
    justify-content: center;
}
.demo-spin-icon-load {
    animation: ani-demo-spin 1s linear infinite;
}
@keyframes ani-demo-spin {
    from {
        transform: rotate(0deg);
    }
    50% {
        transform: rotate(180deg);
    }
    to {
        transform: rotate(360deg);
    }
}
</style>
<style lang="less">
.ivu-modal-header {
    text-align: center;
}
</style>
<template>
    <div class="env_application">
        <Tabs v-model="tab_name" ref="tabs" @on-click="changeTab">
            <TabPane label="分支管理" name="test_data_branch" :disabled="tab_disable">
                <testDataBranch
                    ref="testDataBranch"
                    @send-data="handleData"
                    :bis_name_list="bis_name_list"
                ></testDataBranch>
            </TabPane>
            <TabPane label="数据开发初始化" name="test_data_pipeline" :disabled="tab_disable">
                <TestdataPipeline
                    ref="TestdataPipeline"
                    :bis_name_list="bis_name_list"
                    :biz_param="biz_param"
                    @handle-loading="handLoading"
                ></TestdataPipeline>
            </TabPane>
            <TabPane label="数据提交" name="test_data_commit" :disabled="tab_disable">
                <div v-show="biz_param.biz_br_name !== 'master'">
                    <testDataDump
                        ref="TestdataCommit"
                        :bis_name_list="bis_name_list"
                        :biz_param="biz_param"
                        @handle-loading="handLoading"
                    />
                </div>
            </TabPane>

            <TabPane label="归档" name="test_data_archive" :disabled="tab_disable">
                <div v-show="biz_param.biz_br_name !== 'master'">
                    <testDataFiled
                        ref="TestdataBrArchive"
                        :bis_name_list="bis_name_list"
                        :biz_param="biz_param"
                        @handle-loading="handLoading"
                    />
                </div>
            </TabPane>
        </Tabs>
        <Spin size="large" fix v-if="spinShow">
            <Icon type="ios-loading" size="32" class="demo-spin-icon-load"></Icon>
            <div>请求中...</div>
        </Spin>
    </div>
</template>
<script>
// import TestdataPipeline from '@/spider-components/test-data-dev-pipeline'
// import TestdataCommit from '@/spider-components/test-data-commit'
// import TestdataBrArchive from '@/spider-components/test-data-archive/test-data-br-archive.vue'
import TestdataPipeline from './component/test_data_dev/testDataPipeline.vue'
import testDataDump from './component/test_data_dev/testDataDump.vue'
import testDataFiled from './component/test_data_dev/testDataFiled.vue'
import testDataBranch from './component/test_data_dev/testDataBranch.vue'
import time from '@/mixin/time'
import isEmpty from '@/mixin/isEmpty'

import { getBisNameLIstInfo } from '@/spider-api/biz-mgt'

export default {
    mixins: [time, isEmpty],
    data() {
        return {
            bis_name_list: [],
            biz_param: {
                biz_code: '',
                biz_br_name: '',
                env_name: '',
                biz_br_name_list: [],
                biz_br_name_obj_list: [],
                env_name_list: []
            },
            tab_disable: false,
            tab_name: 'test_data_branch',
            spinShow: false
        }
    },
    components: {
        TestdataPipeline,
        // TestdataCommit,
        // TestdataBrArchive,
        testDataDump,
        testDataFiled,
        testDataBranch
    },
    methods: {
        handleData(data) {
            this.biz_param = data
        },
        handLoading(flag = false) {
            this.spinShow = flag
        },
        changeTab(tab_name) {
            if (tab_name === 'test_data_pipeline') {
                this.$refs.TestdataPipeline.init()
            } else if (tab_name === 'test_data_commit') {
                this.$refs.TestdataCommit.init()
            } else if (tab_name === 'test_data_archive') {
                this.$refs.TestdataBrArchive.init()
            }
        },
        // 获取业务下拉列表数据
        getBisNameInfo() {
            getBisNameLIstInfo().then(res => {
                this.bis_name_list = res.data.data
            })
        }
    },
    mounted() {
        this.getBisNameInfo()
    }
}
</script>
