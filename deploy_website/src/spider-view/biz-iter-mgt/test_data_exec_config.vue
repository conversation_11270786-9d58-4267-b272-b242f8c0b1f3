<template>
  <Card shadow style="height: 100%;overflow-y: auto;position: relative;">
    <Row style="margin-left: 5px;display: flex;align-items: center;">
      <i-col style="margin: 5px" span="2">
        <Icon type="ios-trending-up" style="margin-right: 5px"/>
        <Tooltip content="选择你的业务分支" placement="right-start">
          <span style="text-align: left; display: inline-block; width:60px;">选择分支</span>
        </Tooltip>
      </i-col>
      <i-col style="margin: 5px" span="8">
        <Select v-model="biz_br_name" filterable clearable size="small" @on-change="bizBrNameChangeSelect"
                style="width: 300px">
          <Option
            v-for="item in biz_br_name_list"
            :value="item.value"
            :key="item.value"
          >{{ item.label }}
          </Option>
        </Select>
      </i-col>


      <div style="margin:0 25px 0 50px;white-space: nowrap;">
        <Icon type="ios-boat-outline" style="margin-right: 5px"/>
        <span>历史配置</span>
      </div>
      <Tooltip :content="flow_schedule_tips" placement="bottom">
        <Select v-model="flow_schedule_id" filterable clearable size="small" @on-change="flowConfigChangeSelect"
                style="width: 340px;">
          <Option
            v-for="item in flow_schedule_list"
            :value="item.value"
            :key="item.value"
          >{{ item.label }}
          </Option>
        </Select>
      </Tooltip>
      <Button type="warning" icon="ios-trash-outline" ghost size="small" @click="delFlowSchedule">删除配置</Button>

    </Row>
    <Row style="margin-left: 5px;display: flex;align-items: center;">
      <i-col style="margin: 5px" span="2">
        <Icon type="ios-nutrition-outline" style="margin-right: 5px"/>
        <Tooltip content="选择业务关联的测试集编排" placement="right-start">
          <span style="text-align: left; display: inline-block; width:60px;">选择编排</span>
        </Tooltip>
      </i-col>
      <i-col style="margin: 5px" span="8">
        <Select v-model="flow" filterable clearable size="small" @on-change="flowChangeSelect" style="width: 300px">
          <Option
            v-for="item in flowList"
            :value="item.value"
            :key="item.biz_flow_name"
          >{{ item.label }}
          </Option>
        </Select>
      </i-col>
      <div style="margin:0 25px 0 50px;white-space: nowrap;">
        <Icon type="ios-bookmarks-outline" style="margin-right: 5px"/>
        <span>执行详情</span>
      </div>
      <Tooltip max-width="200" :content="exec_history_tips" placement="bottom">
        <Select v-model="exec_history" filterable clearable size="small" @on-change="execHistoryChangeSelect"
                style="width: 341px;">
          <Option
            v-for="item in exec_history_list"
            :value="item.value"
            :key="item.value"
          >{{ item.label }}
          </Option>
        </Select>
      </Tooltip>
      <Button type="primary" icon="ios-search" ghost size="small" @click="goExecHistory">查看详情</Button>

    </Row>

    <Row style="margin-left: 5px;display: flex;align-items: center;">
      <i-col style="margin: 5px" span="2">
        <Icon type="ios-link-outline" style="margin-right: 5px"/>
        <Tooltip content="分支关联的业务" placement="right-start">
          <span style="text-align: left; display: inline-block; width:60px;">关联业务&nbsp;</span>
        </Tooltip>
      </i-col>
      <i-col style="margin: 5px" span="4.5">
        {{ biz_base_info.biz_name }}
      </i-col>
      <i-col style="margin: 5px" span="4">
        <Button type="dashed" size="small" @click="goFlow">跳转到编排</Button>
      </i-col>
    </Row>
    <Row style="margin-left: 5px;display: flex;align-items: center;">
      <i-col style="margin: 5px" span="2">
        <Icon type="ios-color-filter-outline" style="margin-right: 5px"/>
        <Tooltip content="业务关联的基础库" placement="right-start">
          <span style="text-align: left; display: inline-block; width:60px;">基础库</span>
        </Tooltip>
      </i-col>
      <i-col style="margin: 5px" span="5">
        <p style="width: 300px;color: #2d8cf0"> {{ biz_base_info.biz_base_db_code }}</p>
      </i-col>
    </Row>

    <Row style="margin-left: 5px;display: flex;align-items: center;">
      <i-col style="margin: 5px" span="2">
        <Icon type="ios-aperture-outline" style="margin-right: 5px"/>
        <Tooltip content="在哪个环境执行测试集编排" placement="right-start">
          <span style="text-align: left; display: inline-block; width:60px;">执行环境</span>
        </Tooltip>
      </i-col>
      <i-col style="margin: 5px" span="5">
        <Select v-model="env_name" filterable clearable size="small" @on-change="envChangeSelect" style="width: 300px">
          <Option
            v-for="item in env_name_list"
            :value="item.value"
            :key="item.value"
          >{{ item.label }}
          </Option>
        </Select>
      </i-col>
    </Row>

    <Row style="margin-left: 5px;display: flex;align-items: center;">
      <i-col style="margin: 5px" span="2">
        <Icon type="ios-apps-outline" style="margin-right: 5px"/>
        <Tooltip content="选择业务关联的应用的分支部署到环境" placement="right-start">
          <span style="text-align: left; display: inline-block; width:60px;">应用分支</span>
        </Tooltip>
      </i-col>
      <i-col style="margin: 5px" span="18">
        <tables @on-selection-change="selectApp" ref="selection" v-model="appList" :columns="columns"/>
      </i-col>
    </Row>

    <Row style="margin-left: 5px;display: flex;align-items: center;">
      <i-col style="margin: 5px" span="2">
        <Icon type="ios-clock-outline" style="margin-right: 5px"/>
        <Tooltip content="配置每日几点执行测试集编排" placement="right-start">
          <span style="text-align: left; display: inline-block; width:60px;">计划任务&nbsp;</span>
        </Tooltip>
      </i-col>
      <i-col style="margin: 5px" span="8">
        <Select v-model="cron" filterable clearable size="small" @on-change="cronChangeSelect"
                style="width: 300px;margin-right: 10px;  ">
          <Option
            v-for="item in cronList"
            :value="item.value"
            :key="item.value"
          >{{ item.label }}
          </Option>
        </Select>
      </i-col>
      <i-col style="margin: 5px" span="4">
         <Checkbox v-model="enable_schedule" style="font-weight: bold">是否启动定时</Checkbox>
      </i-col>

      <i-col style="margin: 5px" span="4">
        <Checkbox v-model="biz_db_init_flag" style="font-weight: bold">仅初始化数据库</Checkbox>
      </i-col>

    </Row>

    <Row style="margin-left: 5px;display: flex;align-items: center;">
      <i-col style="margin: 5px" span="2">
        <Icon type="logo-youtube" style="margin-right: 5px"/>
        <span style="text-align: left; display: inline-block; width:60px;">操作&nbsp;</span>
      </i-col>
      <i-col style="margin: 5px" span="4">
        <Button type="primary" size="small" @click="saveConfig">保存定时执行配置</Button>
      </i-col>
      <i-col style="margin: 5px" span="4">
        <Tooltip content="仅执行测试集编排,不初始化环境" placement="top-end">
          <Button type="info" size="small" @click="execute_biz_pipeline_confirm">仅执行测试集编排</Button>
        </Tooltip>
      </i-col>
      <i-col style="margin: 5px" span="5">
        <Tooltip content="初始化业务绑定的所有应用&执行测试集编排" placement="top-end">
          <Button type="error" size="small" @click="execute_jenkins_composition_confirm"> 环境初始化+执行测试集编排
          </Button>
        </Tooltip>
      </i-col>
      <i-col style="margin: 5px" span="4">
        <Tooltip content="仅初始化勾选的应用&执行测试集编排" placement="top-end">
          <Button type="success" size="small" @click="execute_jenkins_composition_seg_confirm">
            部分初始化+执行测试集编排
          </Button>
        </Tooltip>
      </i-col>
    </Row>


    <Modal
      v-model="showModel"
      title="部分初始化+执行测试集"
      @on-ok="execute_jenkins_composition_seg"
      @on-cancle="execute_jenkins_composition_seg_cancle"
    >
      <div style="padding-bottom:6px;margin-bottom:6px;">
        已选择【应用及版本】
      </div>
      <div style="padding-bottom:6px;margin-bottom:6px;">
        <tables v-model="app_selection" :columns="app_selection_columns"/>
      </div>

      <div style="padding-bottom:6px;margin-bottom:6px;">
        请选择是否【数据库初始化】
      </div>
      <div style="padding-bottom:6px;margin-bottom:6px;">
        <Checkbox v-model="biz_db_init_flag">初始化:{{ biz_base_info.biz_base_db_code }}</Checkbox>
      </div>
      <div style="padding-bottom:6px;margin-bottom:6px;">
        请选择【中间件及其他无需选择版本的应用】
        <!--        <Checkbox-->
        <!--          :indeterminate="indeterminate"-->
        <!--          :value="checkAll"-->
        <!--          @click.prevent.native="handleCheckAllNoVersionApp">全选[中间件及其他无需选择版本的应用]-->
        <!--        </Checkbox>-->
      </div>
      <CheckboxGroup v-model="no_version_app_selection" @on-change="selectNoVersionApp">
        <Checkbox v-for="app in select_version_apps" :key="app.app_name" :label="app.app_name"></Checkbox>
      </CheckboxGroup>
    </Modal>


    <Modal
      v-model="showCompositionListModel"
      title="流水线列表[点击流水线名跳转]"
    >
      <div v-for="item in compositionList"
           :key="item.job_name">
        <Button :to="item.job_url" target="_blank" type="dashed" style="margin: 5px;">
          {{ item.job_name + "【运行状态:" + item.job_status + "】" }}
        </Button>
        {{ item.job_url ? "" : "流水线没有运行" }}
      </div>
    </Modal>

    <Spin size="large" fix v-if="spinShow">
      <Icon type="ios-loading" size=32 class="demo-spin-icon-load"></Icon>
      <div>请求中...</div>
    </Spin>
  </Card>
</template>

<script>
import Tables from '@/components/tables'
import { getEnvInfo } from '@/spider-api/mgt-env'
import {
    del_flow_schedule_config,
    execute_biz_pipeline,
    execute_history,
    execute_history_list,
    execute_history_retrieve,
    execute_jenkins_composition,
    get_biz_app_list,
    get_flow_schedule_config,
    get_test_flow_list,
    get_test_iter_list,
    save_flow_schedule_config
} from '@/spider-api/biz-mgt'
import params from '@/view/argu-page/params.vue'
import { getAllEnvInfo, getAutoTestingEnv } from '@/api/test-env'

export default {
    name: 'TestDataExecConfig',
    components: {
        Tables
    },
    data() {
        return {
            compositionList: [],
            showCompositionListModel: false,
            enable_schedule: false,
            spinShow: false,
            biz_db_init_flag: false,
            indeterminate: true,
            checkAll: false,
            showModel: false,
            exec_history_tips: '',
            exec_history: '',
            exec_history_list: [],
            env_name: '',
            env_name_list: [],
            appList: [],
            bizApps: [],
            cron: '',
            cronList: [],
            biz_br_name: '',
            biz_br_name_list: [],
            flow: '',
            flowList: [],
            columns: [
                {
                    type: 'selection',
                    align: 'center'
                },
                { title: '应用名', key: 'app_module_name' },
                { title: '数据库', key: 'db_names', tooltip: true, tooltipMaxWidth: 900 },
                { title: '线上版本', key: 'online_br' },
                { title: '归档版本', key: 'archive_br' },
                {
                    title: '选择版本',
                    key: 'br_name',
                    render: (h, params) => {
                        let op_list = params.row.br_names.map(item => {
                            return h('Option', {
                                // 下拉框的值
                                props: {
                                    value: item,
                                    label: item
                                }
                            })
                        })
                        return h(
                            'Select',
                            {
                                props: {
                                    placeholder: params.row.br_name,
                                    value: params.row.br_name,
                                    transfer: true,
                                    clearable: true
                                },
                                style: {
                                    width: '100px'
                                },
                                on: {
                                    'on-change': val => {
                                        params.row.br_name = val // 改变下拉框赋值
                                        this.updateAppList[params.row.app_module_name] = params.row.br_name
                                        this.selectApp(this.app_selection)
                                    }
                                }
                            },
                            op_list
                        )
                    }
                }
            ],
            app_selection_columns: [{ title: '应用名', key: 'app_module_name' }, { title: '部署版本', key: 'br_name' }],
            biz_br_name_obj_list: [],
            flowObjList: [],
            biz_base_info: {
                biz_base_db_code: undefined,
                biz_name: undefined
            },
            updateAppList: {},
            flow_schedule: {},
            flow_schedule_list_his: [],
            flow_schedule_list: [],
            flow_schedule_id: undefined,
            flow_schedule_tips: undefined,
            app_selection: [],
            select_version_apps: [],
            no_version_app_selection: [],
            exec_history_list_objs: {}
        }
    },
    computed: {},
    methods: {
        selectApp(selection) {
            this.app_selection = selection.map(item => {
                if (this.updateAppList[item.app_module_name]) {
                    item.br_name = this.updateAppList[item.app_module_name]
                }
                return item
            })
            console.log(this.app_selection)
        },
        selectNoVersionApp(selection) {
            console.log('selectNoVersionApp:%o', selection)
            this.no_version_app_selection = selection
        },
        handleCheckAllNoVersionApp(selection) {
            this.checkAll = !this.checkAll
        },
        flowConfigChangeSelect(params) {
            this.flow_schedule = {}
            this.flow_schedule_tips = undefined
            this.flow_schedule_id = params
            this.flow_schedule_list_his
                .filter(item => {
                    return item.id == this.flow_schedule_id
                })
                .map(item => {
                    this.flow_schedule = item
                    this.bizBrNameChangeSelect(item.biz_iter_branch, item.biz_flow_name)
                    this.envChangeSelect(item.suite_code)
                    this.cronChangeSelect(item.cron)
                    this.enable_schedule = item.enable_schedule ? true : false
                    this.flow_schedule_tips = item.biz_iter_branch + '/' + item.biz_flow_name + '/' + item.suite_code
                    console.log('item.ext_config:%o', item.ext_config)
                    this.biz_db_init_flag = false
                    if (item.ext_config) {
                        if (
                            item.ext_config.param &&
                            item.ext_config.param.just_init_db &&
                            item.ext_config.param.just_init_db == '1'
                        ) {
                            this.biz_db_init_flag = true
                        }
                    }
                    this.flow_schedule_tips = this.flow_schedule_tips + '/' + item.cron
                })
        },
        goFlow() {
            this.$router.push({
                name: 'biz_auto_test_define',
                query: { biz_code: this.biz_base_info.biz_code, biz_flow_name: this.flow }
            })
        },
        clearBizInfo() {
            this.biz_base_info = {
                biz_base_db_code: undefined,
                biz_name: undefined
            }
            this.flow = ''
            this.env_name = ''
            this.cron = ''
            this.appList = []
            this.updateAppList = {}
        },
        bizBrNameChangeSelect(params, flow_change) {
            this.clearBizInfo()
            this.biz_br_name = params
            for (let bizObj in this.biz_br_name_obj_list) {
                if (this.biz_br_name_obj_list[bizObj].biz_test_iter_br == params) {
                    this.get_test_flow_lists({ biz_code: this.biz_br_name_obj_list[bizObj].biz_code }, flow_change)
                    let biz_test_iter_id = ''
                    for (let bizObj in this.biz_br_name_obj_list) {
                        if (this.biz_br_name_obj_list[bizObj].biz_test_iter_br == this.biz_br_name) {
                            biz_test_iter_id = this.biz_br_name_obj_list[bizObj].biz_test_iter_id
                        }
                    }
                    this.get_biz_app_lists({
                        biz_code: this.biz_br_name_obj_list[bizObj].biz_code,
                        biz_test_iter_id: biz_test_iter_id
                    })
                }
            }
            this.get_execute_history_list()
        },
        flowChangeSelect(params) {
            this.flow = params
            console.log('this.flow:%o', this.flow)
            console.log('this.flowObjList:%o', this.flowObjList)
            for (let flowObj in this.flowObjList) {
                if (this.flowObjList[flowObj].biz_flow_name == params) {
                    this.biz_base_info = this.flowObjList[flowObj]
                }
            }
            console.log('this.biz_base_info:%o', this.biz_base_info)
            this.get_execute_history_list()
        },
        envChangeSelect(params) {
            this.env_name = params
            this.get_execute_history_list()
        },
        cronChangeSelect(params) {
            this.cron = params
        },
        execHistoryChangeSelect(params) {
            this.exec_history = params
            this.exec_history_list.map(item => {
                if (item.value == this.exec_history) {
                    this.exec_history_tips = item.label
                }
            })
        },
        goExecHistory() {
            let param = this.exec_history_list_objs[this.exec_history]
            if (param && this.exec_history) {
                execute_history_retrieve(param).then(res => {
                    if (res.data.status != 'success') {
                        this.$Message.warning(res.data.msg)
                        return false
                    }
                    if (res.data.data) {
                        this.showCompositionListModel = !this.showCompositionListModel
                        this.compositionList = res.data.data
                        return false
                    } else {
                        this.$Message.warning('没有查询到你的执行记录')
                    }
                })
            } else {
                this.$Message.warning('请选择执行记录！')
            }
            this.get_execute_history_list()
        },
        init_suite_info() {
            let data = {
                page: 1,
                size: 200,
                type_name: 1
            }
            // getAllEnvInfo(data).then(res => {
            getAutoTestingEnv(data).then(res => {
                let env_list = res.data.data['results']
                if (env_list) {
                    this.env_name_list = env_list
                        .filter(item => {
                            return item.suite_code.indexOf('it') !== -1
                        })
                        .map(item => {
                            return {
                                value: item.suite_code,
                                label: item.suite_code
                            }
                        })
                } else {
                    this.$Message.error('环境获取失败！')
                }
            })
        },
        get_test_iters() {
            get_test_iter_list()
                .then(res => {
                    let data_list = res.data.data
                    if (data_list) {
                        this.biz_br_name_obj_list = data_list
                        this.biz_br_name_list = data_list.map(item => {
                            return {
                                value: item.biz_test_iter_br,
                                label: item.biz_test_iter_br
                            }
                        })
                    } else {
                        this.$Message.error('业务分支获取失败！')
                    }
                })
                .catch(err => {
                    console.log(err)
                })
        },
        get_test_flow_lists(param, flow_change) {
            get_test_flow_list(param)
                .then(res => {
                    let data_list = res.data.data
                    if (data_list) {
                        this.flowObjList = data_list
                        this.flowList = data_list.map(item => {
                            return {
                                value: item.biz_flow_name,
                                label: item.biz_flow_name
                            }
                        })
                        if (flow_change) {
                            this.flowChangeSelect(flow_change)
                        }
                    } else {
                        this.$Message.error('业务分支获取失败！')
                    }
                })
                .catch(err => {
                    console.log(err)
                })
        },
        get_biz_app_lists(param) {
            get_biz_app_list(param)
                .then(res => {
                    let data_list = res.data.data
                    if (data_list) {
                        this.bizApps = data_list
                        this.appList = data_list
                            .filter(item => {
                                let br_name_list = []
                                if (item.archive_br) {
                                    br_name_list = [item.archive_br]
                                }
                                let temp_br_name_list = item.br_name_fifteen ? item.br_name_fifteen.split(',') : []
                                br_name_list.push(...temp_br_name_list)
                                item.br_names = br_name_list
                                return item.br_names.length > 0
                            })
                            .map(item => {
                                let br_name_list = []
                                if (item.archive_br && item.online_br) {
                                    br_name_list = ['归档版本', '线上版本', item.archive_br, item.online_br]
                                    item.br_name = '归档版本'
                                } else if (item.archive_br) {
                                    br_name_list = ['归档版本', item.archive_br]
                                    item.br_name = '归档版本'
                                } else if (item.online_br) {
                                    br_name_list = ['线上版本', item.online_br]
                                    item.br_name = '线上版本'
                                }
                                let temp_br_name_list = item.br_name_fifteen ? item.br_name_fifteen.split(',') : []
                                br_name_list.push(...temp_br_name_list)
                                item.br_names = [...new Set(br_name_list)]
                                console.log(item.br_names)
                                if (!item.br_name) {
                                    item.br_name = item.br_names[0]
                                }
                                if (
                                    this.flow_schedule.global_param &&
                                    this.flow_schedule.global_param.param_type == 'test_apps' &&
                                    this.flow_schedule.global_param.param
                                ) {
                                    this.flow_schedule.global_param.param.map(global_param_app_item => {
                                        if (
                                            global_param_app_item.app_module_name == item.app_module_name &&
                                            global_param_app_item.br_name
                                        ) {
                                            item.br_name =
                                                global_param_app_item.br_name === 'archive_br'
                                                    ? '归档版本'
                                                    : global_param_app_item.br_name === 'online_br'
                                                        ? '线上版本'
                                                        : global_param_app_item.br_name
                                        }
                                    })
                                }
                                this.updateAppList[item.app_module_name] = item.br_name
                                return item
                            })
                        console.log(this.appList)
                    } else {
                        this.$Message.error('业务被测应用获取失败！')
                    }
                })
                .catch(err => {
                    console.log(err)
                })
        },
        common_check() {
            if (!this.biz_br_name || this.biz_br_name.trim().length == 0) {
                this.$Message.warning('请选择业务分支')
                return false
            }
            if (!this.flow || this.flow.trim().length == 0) {
                this.$Message.warning('请选择编排')
                return false
            }
            let not_check_apps = []
            this.appList.forEach(app => {
                let exist = false
                Object.keys(this.updateAppList).forEach(update_app => {
                    if (app.app_module_name == update_app) {
                        exist = true
                    }
                })
                if (!exist) {
                    not_check_apps.push(app.app_module_name)
                }
            })
            if (not_check_apps.length > 0) {
                this.$Message.error('这些应用:' + not_check_apps + ':没有选择版本@！')
                return false
            }
            if (!this.env_name || this.env_name.trim().length == 0) {
                this.$Message.warning('请选择执行环境')
                return false
            }
            return true
        },
        saveConfig() {
            if (!this.cron || this.cron.trim().length == 0) {
                this.$Message.error('请选择计划任务')
                return false
            }

            if (!this.common_check()) {
                return false
            }
            let config_params = {
                suite_code: this.env_name,
                biz_iter_branch: this.biz_br_name,
                cron: this.cron,
                biz_flow_name: this.flow,
                biz_code: this.biz_base_info.biz_code
            }
            let app_list = []
            Object.keys(this.updateAppList).forEach(update_app => {
                let br_name = this.updateAppList[update_app]
                if (br_name == '归档版本') {
                    br_name = 'archive_br'
                } else if (br_name == '线上版本') {
                    br_name = 'online_br'
                }
                app_list.push({ app_module_name: update_app, br_name: br_name })
            })
            console.log('config_params:%o', config_params)
            console.log('action_params:%o', { params: JSON.stringify(app_list), param_type: 'test_apps' })
            config_params['params'] = [
                {
                    params: app_list,
                    param_type: 'test_apps'
                },
                { params: { just_init_db: this.biz_db_init_flag ? '1' : '0' }, param_type: 'ext_config' }
            ]
            config_params['enable_schedule'] = this.enable_schedule ? 1 : 0
            this.spinShow = true
            save_flow_schedule_config(config_params)
                .then(res => {
                    if (res.data.status === 'success') {
                        this.spinShow = false
                        this.$Message.success('保存成功')
                        this.initHistory()
                    } else {
                        this.$Message.success('保存失败，失败原因：' + res.data.msg)
                    }
                })
                .catch(err => {
                    this.$Message.error(err)
                })
                .finally(() => {
                    this.spinShow = false
                })
        },
        initHistory() {
            get_flow_schedule_config().then(res => {
                let data_list = res.data.data
                if (data_list) {
                    this.flow_schedule_list_his = data_list
                    this.flow_schedule_list = data_list.map(item => {
                        return {
                            value: item.id,
                            label: item.biz_iter_branch + '/' + item.biz_flow_name + '/' + item.suite_code
                        }
                    })
                }
            })
        },
        delFlowSchedule() {
            this.$Modal.confirm({
                title: '提示',
                content: this.flow_schedule_tips
                    ? '<p>是否删除配置：' + this.flow_schedule_tips + '</p>'
                    : '<p>请先选择配置' + '</p>',
                onOk: () => {
                    if (this.flow_schedule_id) {
                        console.log('flow_schedule_id:%o', this.flow_schedule_id)
                        this.spinShow = true
                        del_flow_schedule_config({ id: this.flow_schedule_id })
                            .then(res => {
                                if (res.data.status != 'success') {
                                    this.spinShow = false
                                    this.$Message.error(res.data.msg)
                                    return false
                                }
                                this.spinShow = false
                                this.$Message.success('删除成功')
                                this.initHistory()
                            })
                            .finally(() => {
                                this.spinShow = false
                            })
                    }
                }
            })
        },
        flowScheduleHis() {
            if (!this.flow_schedule_id) {
                this.$Message.warning('请选择配置')
                return false
            }
            let biz_test_iter_id = ''
            for (let bizObj in this.biz_br_name_obj_list) {
                if (this.biz_br_name_obj_list[bizObj].biz_test_iter_br == this.biz_br_name) {
                    biz_test_iter_id = this.biz_br_name_obj_list[bizObj].biz_test_iter_id
                }
            }
            execute_history_retrieve({
                exec_suite_code: this.env_name,
                exec_action_type: 'flow_schedule',
                flow_schedule_id: this.flow_schedule_id,
                biz_type: biz_test_iter_id + '_' + this.biz_base_info.biz_pipeline_name
            }).then(res => {
                if (res.data.status != 'success') {
                    this.$Message.warning(res.data.msg)
                    return false
                }
                if (res.data.data) {
                    window.open(res.data.data, '_blank')
                    return false
                } else {
                    this.$Message.warning('没有查询到执行记录')
                }
            })
        },
        execute_biz_pipeline_confirm() {
            this.$Modal.confirm({
                title: '提示',
                content: '<p>是否启动执行测试集流水线</p>',
                onOk: () => {
                    if (!this.common_check()) {
                        return false
                    }
                    this.execute_biz_pipeline_fc()
                }
            })
        },
        execute_biz_pipeline_fc() {
            let biz_test_iter_id = ''
            for (let bizObj in this.biz_br_name_obj_list) {
                if (this.biz_br_name_obj_list[bizObj].biz_test_iter_br == this.biz_br_name) {
                    biz_test_iter_id = this.biz_br_name_obj_list[bizObj].biz_test_iter_id
                }
            }
            this.spinShow = true
            execute_biz_pipeline({
                suite_code: this.env_name,
                biz_test_iter_id: biz_test_iter_id,
                biz_pipeline_name: this.biz_base_info.biz_pipeline_name
            })
                .then(res => {
                    if (res.data.status != 'success') {
                        this.spinShow = false
                        this.$Message.error(res.data.msg)
                        return false
                    }
                    this.spinShow = false
                    this.$Message.success('启动命令已发出')
                    execute_history({
                        exec_suite_code: this.env_name,
                        exec_action_type: 'biz_job',
                        exec_detail_param: res.data.data,
                        biz_type: biz_test_iter_id + '_' + this.biz_base_info.biz_pipeline_name
                    }).then(res => {
                        console.log(res.data)
                    })
                    this.get_execute_history_list()
                })
                .catch(err => {})
                .finally(() => {
                    this.spinShow = false
                })
        },
        execute_jenkins_composition_confirm() {
            this.$Modal.confirm({
                title: '提示',
                content: '<p>是否启动环境初始化+执行测试集流水线</p>',
                onOk: () => {
                    if (!this.common_check()) {
                        return false
                    }
                    this.execute_jenkins_composition_fc()
                }
            })
        },
        execute_jenkins_composition_seg_confirm() {
            if (!this.biz_br_name || this.biz_br_name.trim().length == 0) {
                this.$Message.warning('请选择业务分支')
                return false
            }
            if (!this.flow || this.flow.trim().length == 0) {
                this.$Message.warning('请选择编排')
                return false
            }
            if (!this.env_name || this.env_name.trim().length == 0) {
                this.$Message.warning('请选择执行环境')
                return false
            }
            this.showModel = !this.showModel
            let app_list = []
            this.select_version_apps = this.bizApps
                .filter(item => {
                    let br_name_list = []
                    if (item.archive_br) {
                        br_name_list = [item.archive_br]
                    }
                    let temp_br_name_list = item.br_name_fifteen ? item.br_name_fifteen.split(',') : []
                    br_name_list.push(...temp_br_name_list)
                    item.br_names = br_name_list
                    return item.br_names.length === 0
                })
                .map(item => {
                    return { app_name: item.app_module_name, branch_name: '' }
                })
        },
        execute_jenkins_composition_seg() {
            let app_list = []
            this.no_version_app_selection.forEach(app => {
                app_list.push({ app_name: app, branch_name: '' })
            })
            this.app_selection.map(item => {
                if (item.br_name == '归档版本') {
                    item.br_name = 'archive_br'
                } else if (item.br_name == '线上版本') {
                    item.br_name = 'online_br'
                }
                app_list.push({ app_name: item.app_module_name, branch_name: item.br_name })
            })
            let just_init_db = 0
            if (app_list.length === 0 && this.biz_db_init_flag) {
                Object.keys(this.updateAppList).forEach(update_app => {
                    let br_name = this.updateAppList[update_app]
                    if (br_name == '归档版本') {
                        br_name = 'archive_br'
                    } else if (br_name == '线上版本') {
                        br_name = 'online_br'
                    }
                    app_list.push({ app_name: update_app, branch_name: br_name })
                })
                just_init_db = 1
            } else if (app_list.length === 0 && !this.biz_db_init_flag) {
                this.$Message.warning('请至少选择一个应用或者仅初始化数据库！')
                return false
            }
            let mult_params = {
                app_dict_list: app_list,
                env_name: this.env_name,
                db_env: this.biz_db_init_flag ? this.biz_base_info.biz_base_db_code : undefined,
                biz_db_init_flag: this.biz_db_init_flag,
                just_init_db: just_init_db
            }
            console.log('mult_params:%o', mult_params)
            let biz_test_iter_id = ''
            for (let bizObj in this.biz_br_name_obj_list) {
                if (this.biz_br_name_obj_list[bizObj].biz_test_iter_br == this.biz_br_name) {
                    biz_test_iter_id = this.biz_br_name_obj_list[bizObj].biz_test_iter_id
                }
            }
            if (this.biz_db_init_flag) {
                mult_params['bis_pipeline_id'] = biz_test_iter_id
            }
            let param = {
                exec_action_type: 'seg_init&biz_job_jenkins_composition',
                suite_code: this.env_name,
                jobs_info: [
                    { job_name: 'mult_init_job_type_环境初始化', param: mult_params },
                    {
                        job_name: 'biz_job_job_type_' + this.biz_base_info.biz_pipeline_name,
                        param: {
                            suite_code: this.env_name,
                            biz_test_iter_id: biz_test_iter_id,
                            biz_pipeline_name: this.biz_base_info.biz_pipeline_name
                        }
                    }
                ]
            }
            this.spinShow = true
            execute_jenkins_composition(param)
                .then(res => {
                    this.spinShow = false
                    if (res.data.status != 'success') {
                        this.$Message.error(res.data.msg)
                    } else {
                        this.$Message.success('启动命令已发出')
                    }

                    execute_history({
                        exec_suite_code: this.env_name,
                        exec_action_type: 'seg_init&biz_job_jenkins_composition',
                        exec_detail_param: res.data.data,
                        biz_type: biz_test_iter_id + '_' + this.biz_base_info.biz_pipeline_name
                    }).then(res => {
                        console.log(res.data)
                    })
                    this.get_execute_history_list()
                })
                .catch(err => {
                    this.$Message.error(err)
                })
                .finally(() => {
                    this.spinShow = false
                })
        },
        execute_jenkins_composition_seg_cancle() {},
        execute_jenkins_composition_fc() {
            let app_list = []
            Object.keys(this.updateAppList).forEach(update_app => {
                let br_name = this.updateAppList[update_app]
                if (br_name == '归档版本') {
                    br_name = 'archive_br'
                } else if (br_name == '线上版本') {
                    br_name = 'online_br'
                }
                app_list.push({ app_name: update_app, branch_name: br_name })
            })
            this.bizApps
                .filter(item => {
                    let br_name_list = []
                    if (item.archive_br) {
                        br_name_list = [item.archive_br]
                    }
                    let temp_br_name_list = item.br_name_fifteen ? item.br_name_fifteen.split(',') : []
                    br_name_list.push(...temp_br_name_list)
                    item.br_names = br_name_list
                    return item.br_names.length == 0
                })
                .map(item => {
                    app_list.push({ app_name: item.app_module_name, branch_name: '' })
                })
            let mult_params = {
                app_dict_list: app_list,
                env_name: this.env_name,
                db_env: this.biz_base_info.biz_base_db_code,
                biz_db_init_flag: true,
                just_init_db: this.biz_db_init_flag ? 1 : 0
            }
            console.log('mult_params:%o', mult_params)

            let biz_test_iter_id = ''
            for (let bizObj in this.biz_br_name_obj_list) {
                if (this.biz_br_name_obj_list[bizObj].biz_test_iter_br == this.biz_br_name) {
                    biz_test_iter_id = this.biz_br_name_obj_list[bizObj].biz_test_iter_id
                }
            }
            mult_params['bis_pipeline_id'] = biz_test_iter_id
            let param = {
                exec_action_type: 'init&biz_job_jenkins_composition',
                suite_code: this.env_name,
                jobs_info: [
                    { job_name: 'mult_init_job_type_环境初始化', param: mult_params },
                    {
                        job_name: 'biz_job_job_type_' + this.biz_base_info.biz_pipeline_name,
                        param: {
                            suite_code: this.env_name,
                            biz_test_iter_id: biz_test_iter_id,
                            biz_pipeline_name: this.biz_base_info.biz_pipeline_name
                        }
                    }
                ]
            }
            this.spinShow = true
            execute_jenkins_composition(param)
                .then(res => {
                    this.spinShow = false
                    if (res.data.status != 'success') {
                        this.$Message.error(res.data.msg)
                    } else {
                        this.$Message.success('启动命令已发出')
                    }
                    execute_history({
                        exec_action_type: 'init&biz_job_jenkins_composition',
                        exec_detail_param: res.data.data,
                        exec_suite_code: this.env_name,
                        biz_type: biz_test_iter_id + '_' + this.biz_base_info.biz_pipeline_name
                    }).then(res => {
                        console.log(res.data)
                    })
                    this.get_execute_history_list()
                })
                .catch(err => {})
                .finally(() => {
                    this.spinShow = false
                })
        },
        get_execute_history_list() {
            this.exec_history = undefined
            this.exec_history_list = []
            if (!this.biz_br_name || this.biz_br_name.trim().length == 0) {
                return false
            }
            let biz_test_iter_id = ''
            for (let bizObj in this.biz_br_name_obj_list) {
                if (this.biz_br_name_obj_list[bizObj].biz_test_iter_br == this.biz_br_name) {
                    biz_test_iter_id = this.biz_br_name_obj_list[bizObj].biz_test_iter_id
                }
            }
            let job_type = {
                biz_job: '仅执行测试集',
                'init&biz_job_jenkins_composition': '环境初始化+执行测试集',
                'seg_init&biz_job_jenkins_composition': '部分初始化+执行测试集',
                flow_schedule: '定时任务'
            }
            execute_history_list({
                exec_suite_code: this.env_name,
                biz_type: biz_test_iter_id + '_' + this.biz_base_info.biz_pipeline_name
            }).then(res => {
                let execute_historys = res.data.data
                if (execute_historys) {
                    this.exec_history_list = execute_historys.map(item => {
                        return {
                            label:
                                '[' +
                                job_type[item.exec_action_type] +
                                ']-[' +
                                item.exec_suite_code +
                                ']-[' +
                                item.create_user +
                                ']' +
                                '-[' +
                                item.create_time.replace('T', ' ') +
                                ']',
                            value: item.id
                        }
                    })
                    this.exec_history_list_objs = {}
                    if (execute_historys && execute_historys.length > 0) {
                        this.execHistoryChangeSelect(execute_historys[0].id)
                    }

                    execute_historys.map(item => {
                        this.exec_history_list_objs[item.id] = item
                    })
                }
            })
        },

        init() {
            this.initHistory()
            this.get_execute_history_list()
            this.cronList = Array.from(Array(24).keys())
                .filter(num => num !== 2)
                .flatMap(num => {
                    if (num === 1) {
                        return [
                            {
                                value: `0 ${num} * * *`,
                                label: `每日${num}点执行`
                            }
                        ]
                    } else {
                        return [
                            {
                                value: `0 ${num} * * *`,
                                label: `每日${num}点执行`
                            },
                            {
                                value: `30 ${num} * * *`,
                                label: `每日${num}点30执行`
                            }
                        ]
                    }
                })
            this.init_suite_info()
            this.get_test_iters()
        }
    },
    created() {},
    mounted() {
        this.init()
    }
}
</script>

<style scoped>
</style>
