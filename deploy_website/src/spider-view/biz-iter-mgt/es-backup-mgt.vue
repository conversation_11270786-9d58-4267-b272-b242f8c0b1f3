<style scoped>
.env_application {
    width: 100%;
    height: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    position: relative;
}

.card {
    padding-top: 10px;
    padding-left: 10px;
}

.formItem {
    width: 50%;
}

.formButton {
    width: 50%;
    display: flex;
    justify-content: flex-end;
}

.pag {
    display: flex;
    justify-content: center;
}

.demo-spin-icon-load {
    animation: ani-demo-spin 1s linear infinite;
}

@keyframes ani-demo-spin {
    from {
        transform: rotate(0deg);
    }
    50% {
        transform: rotate(180deg);
    }
    to {
        transform: rotate(360deg);
    }
}
</style>

<style lang="less">
.ivu-modal-header {
    text-align: center;
}
</style>

<template>
    <div class="env_application">
        <Tabs v-model="tab_name" ref="tabs" @on-click="changeTab">
            <TabPane label="ES备份创建" name="create_backup" :disabled="tab_disable">
                <CreateBackup ref="CreateBackup" />
            </TabPane>
            <TabPane label="ES备份绑定" name="bind_backup" :disabled="tab_disable">
                <BindBackup ref="BindBackup" />
            </TabPane>
        </Tabs>
        
        <Spin size="large" fix v-if="spinShow">
            <Icon type="ios-loading" size="32" class="demo-spin-icon-load"></Icon>
            <div>请求中...</div>
        </Spin>
    </div>
</template>

<script>
import CreateBackup from './es-backup/create-backup'
import BindBackup from './es-backup/bind-backup'

export default {
    name: 'ESBackupMgt',
    components: {
        CreateBackup,
        BindBackup
    },
    data() {
        return {
            tab_disable: false,
            tab_name: 'create_backup',
            spinShow: false
        }
    },
    mounted() {
        this.changeTab(this.tab_name)
    },
    methods: {
        changeTab(tab_name) {
            if (tab_name === 'create_backup') {
                this.$refs.CreateBackup && this.$refs.CreateBackup.init && this.$refs.CreateBackup.init()
            } else if (tab_name === 'bind_backup') {
                this.$refs.BindBackup && this.$refs.BindBackup.init && this.$refs.BindBackup.init()
            }
        }
    }
}
</script>