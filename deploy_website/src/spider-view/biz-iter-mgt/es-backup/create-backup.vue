<template>
    <Card shadow style="height: 100%;overflow-y: auto;position: relative;">
        <p slot="title" style="width: 100%;text-align: center;">
            ES备份创建
        </p>

        <Form ref="formValidate" :model="formData" :rules="ruleValidate" :label-width="120" style="text-align: left">
            <FormItem label="环境：" prop="suite_code" required>
                <Row>
                    <Col span="7">
                        <Select
                            v-model="formData.suite_code"
                            filterable
                            clearable
                            placeholder="请选择环境"
                            style="width:200px"
                            @on-change="onEnvChange"
                        >
                            <Option
                                v-for="item in envList"
                                :value="item.value"
                                :key="item.value"
                                :label="item.label"
                            >
                                {{ item.label }}
                            </Option>
                        </Select>
                    </Col>
                </Row>
            </FormItem>

            <FormItem label="ES模块：" prop="source_es_module_name" required>
                <Row>
                    <Col span="7">
                        <Select
                            v-model="formData.source_es_module_name"
                            filterable
                            clearable
                            placeholder="请选择ES模块"
                            style="width:200px"
                        >
                            <Option
                                v-for="item in esModuleList"
                                :value="item.value"
                                :key="item.value"
                                :label="item.label"
                            >
                                {{ item.label }}
                            </Option>
                        </Select>
                    </Col>
                </Row>
            </FormItem>

            <FormItem label="业务：" prop="biz_code">
                <Row>
                    <Col span="7">
                        <Select
                            v-model="formData.biz_code"
                            filterable
                            clearable
                            placeholder="请选择业务"
                            style="width:200px"
                            @on-change="onBizChange"
                        >
                            <Option
                                v-for="item in bizList"
                                :value="item.biz_code"
                                :key="item.biz_code"
                                :label="item.biz_name"
                            >
                                {{ item.biz_name }}
                            </Option>
                        </Select>
                    </Col>
                </Row>
            </FormItem>

            <FormItem label="分支：" prop="branch">
                <Row>
                    <Col span="7">
                        <Select
                            v-model="formData.branch"
                            filterable
                            clearable
                            placeholder="请选择分支"
                            style="width:200px"
                        >
                            <Option
                                v-for="item in branchList"
                                :value="item.value"
                                :key="item.value"
                                :label="item.label"
                            >
                                {{ item.label }}
                            </Option>
                        </Select>
                    </Col>
                </Row>
            </FormItem>

            <FormItem label="备份描述：" prop="remark" required>
                <Row>
                    <Col span="12">
                        <Input
                            v-model="formData.remark"
                            type="textarea"
                            :autosize="{minRows: 3, maxRows: 5}"
                            placeholder="请输入备份描述（必填）"
                        />
                    </Col>
                </Row>
            </FormItem>

            <FormItem>
                <Button type="primary" @click="createBackup" :loading="btnLoading">创建备份</Button>
                <Button @click="resetForm" style="margin-left: 8px">重置</Button>
            </FormItem>
        </Form>

        <Spin size="large" fix v-if="spinShow">
            <Icon type="ios-loading" size="32" class="demo-spin-icon-load"></Icon>
            <div>创建中...</div>
        </Spin>
    </Card>
</template>

<script>
import { createESBackup, getEnvModules } from '@/spider-api/es-backup'
import { getBisNameLIstInfo, get_test_iter_list } from '@/spider-api/biz-mgt'

export default {
    name: 'CreateBackup',
    data() {
        return {
            spinShow: false,
            btnLoading: false,
            formData: {
                suite_code: '',
                source_es_module_name: '',
                biz_code: '',
                branch: '',
                remark: '',
                creator: this.$store.state.user.userName
            },
            envList: [],
            esModuleList: [],
            bizList: [],
            branchList: [],
            envModuleMap: {}, // 环境与模块的映射关系
            ruleValidate: {
                suite_code: [
                    { required: true, message: '请选择环境', trigger: 'change' }
                ],
                source_es_module_name: [
                    { required: true, message: '请选择ES模块', trigger: 'change' }
                ],
                biz_code: [
                    { required: false, message: '请选择业务', trigger: 'change' }
                ],
                branch: [
                    { required: false, message: '请选择分支', trigger: 'change' }
                ],
                remark: [
                    { required: true, message: '请输入备份描述', trigger: 'blur' }
                ]
            }
        }
    },
    methods: {
        // 获取环境与模块关联关系
        async getEnvModuleList() {
            try {
                const res = await getEnvModules()
                if (res.data.code === '0000' || res.data.code === 200) {
                    const data = res.data.data
                    // 构建环境列表（去重）
                    const envSet = new Set()
                    data.forEach(item => {
                        envSet.add(item.node_docker)
                        // 构建环境与模块的映射关系
                        if (!this.envModuleMap[item.node_docker]) {
                            this.envModuleMap[item.node_docker] = []
                        }
                        // 检查模块是否已存在，避免重复
                        const moduleExists = this.envModuleMap[item.node_docker].some(
                            module => module.value === item.module_name
                        )
                        if (!moduleExists) {
                            this.envModuleMap[item.node_docker].push({
                                value: item.module_name,
                                label: item.module_name
                            })
                        }
                    })

                    this.envList = Array.from(envSet).map(env => ({
                        value: env,
                        label: env
                    }))
                } else {
                    // 当code不为0000时，显示message错误信息
                    const errorMessage = res.data.message || res.data.msg || '获取环境列表失败'
                    this.$Message.error(errorMessage)
                }
            } catch (error) {
                this.$Message.error('获取环境列表失败')
                console.error(error)
            }
        },

        // 环境变化时更新ES模块列表
        onEnvChange(env) {
            this.formData.source_es_module_name = ''
            this.esModuleList = this.envModuleMap[env] || []
        },

        // 获取业务列表
        async getBizList() {
            try {
                const res = await getBisNameLIstInfo()
                if (res.data.data) {
                    this.bizList = res.data.data
                } else {
                    this.$Message.error('获取业务列表失败')
                }
            } catch (error) {
                this.$Message.error('获取业务列表失败')
                console.error(error)
            }
        },

        // 业务变化时获取分支列表
        async onBizChange(bizCode) {
            this.formData.branch = ''
            this.branchList = []

            if (!bizCode) return

            try {
                const res = await get_test_iter_list({ biz_code: bizCode })
                if (res.data.data) {
                    this.branchList = res.data.data.map(item => ({
                        value: item.biz_test_iter_br,
                        label: item.biz_test_iter_br
                    }))
                } else {
                    this.$Message.error('获取分支列表失败')
                }
            } catch (error) {
                this.$Message.error('获取分支列表失败')
                console.error(error)
            }
        },

        // 创建备份
        createBackup() {
            // 表单验证 - 校验环境、备份文件（ES模块）等必填项的完整性
            this.$refs.formValidate.validate((valid) => {
                if (!valid) {
                    this.$Message.warning('请完善表单信息')
                    return
                }

                this.btnLoading = true
                this.spinShow = true

                this.submitBackup()
            })
        },

        // 提交备份创建请求
        submitBackup() {
            createESBackup(this.formData).then((res) => {
                if (res.data.code === '0000' || res.data.code === 200) {
                    this.$Message.success('ES备份创建成功')
                    if (res.data.data && res.data.data.es_dump_name) {
                        this.$Notice.success({
                            title: '备份创建成功',
                            desc: `备份名称：${res.data.data.es_dump_name}，请到备份绑定页签下验证备份文件的状态`
                        })
                    }
                    this.resetForm()
                } else {
                    // 当code不为0000时，显示message错误信息
                    console.log('接口返回错误:', res.data)
                    this.$Message.error(res.data.message || '创建失败')
                }
            }).catch((error) => {
                console.log(error,1111)
                this.$Message.error('创建失败')
                console.error(error)
            }).finally(() => {
                this.btnLoading = false
                this.spinShow = false
            })
        },

        // 重置表单
        resetForm() {
            this.formData = {
                suite_code: '',
                source_es_module_name: '',
                biz_code: '',
                branch: '',
                remark: '',
                creator: this.$store.state.user.userName
            }
            this.esModuleList = []
            this.branchList = []
            // 重置表单验证状态，避免显示必输项提示
            this.$nextTick(() => {
                this.$refs.formValidate.resetFields()
            })
        },

        // 初始化
        async init() {
            await this.getEnvModuleList()
            await this.getBizList()
        }
    },

    mounted() {
        this.init()
    }
}
</script>

<style scoped>
.demo-spin-icon-load {
    animation: ani-demo-spin 1s linear infinite;
}

@keyframes ani-demo-spin {
    from { transform: rotate(0deg); }
    50%  { transform: rotate(180deg); }
    to   { transform: rotate(360deg); }
}
</style>
