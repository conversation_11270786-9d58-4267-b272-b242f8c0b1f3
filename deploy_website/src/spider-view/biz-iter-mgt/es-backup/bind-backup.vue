<template>
    <Card shadow style="height: 100%;overflow-y: auto;position: relative;">
        <p slot="title" style="width: 100%;text-align: center;">
            ES备份绑定与验证
        </p>
        
        <Form ref="formValidate" :model="formData" :label-width="120" style="text-align: left">
            <FormItem label="业务：" prop="biz_code">
                <Row>
                    <Col span="7">
                        <Select 
                            v-model="formData.biz_code" 
                            filterable 
                            clearable 
                            placeholder="请选择业务"
                            style="width:200px"
                            @on-change="onBizChange"
                        >
                            <Option 
                                v-for="item in bizList" 
                                :value="item.biz_code" 
                                :key="item.biz_code" 
                                :label="item.biz_name"
                            >
                                {{ item.biz_name }}
                            </Option>
                        </Select>
                    </Col>
                </Row>
            </FormItem>
            
            <FormItem label="分支：" prop="branch">
                <Row>
                    <Col span="7">
                        <Select 
                            v-model="formData.branch" 
                            filterable 
                            clearable 
                            placeholder="请选择分支"
                            style="width:200px"
                        >
                            <Option 
                                v-for="item in branchList" 
                                :value="item.value" 
                                :key="item.value" 
                                :label="item.label"
                            >
                                {{ item.label }}
                            </Option>
                        </Select>
                    </Col>
                </Row>
            </FormItem>
            
            <FormItem label="环境：" prop="suite_code">
                <Row>
                    <Col span="7">
                        <Select 
                            v-model="formData.suite_code" 
                            filterable 
                            clearable 
                            placeholder="请选择环境"
                            style="width:200px"
                            @on-change="onEnvChange"
                        >
                            <Option 
                                v-for="item in envList" 
                                :value="item.value" 
                                :key="item.value" 
                                :label="item.label"
                            >
                                {{ item.label }}
                            </Option>
                        </Select>
                    </Col>
                </Row>
            </FormItem>
            
            <FormItem label="备份文件：" prop="es_dump_name">
                <Row>
                    <Col span="12">
                        <Select 
                            v-model="formData.es_dump_name" 
                            filterable 
                            clearable 
                            placeholder="请选择备份文件"
                            style="width:400px"
                        >
                            <Option 
                                v-for="item in backupList" 
                                :value="item" 
                                :key="item" 
                                :label="item"
                            >
                                {{ item }}
                            </Option>
                        </Select>
                    </Col>
                </Row>
            </FormItem>
            
            <FormItem v-if="backupStatus" label="备份状态：">
                <Row>
                    <Col span="12">
                        <Tag :color="getStatusColor(backupStatus)">{{ backupStatus }}</Tag>
                    </Col>
                </Row>
            </FormItem>
            
            <FormItem>
                <Button 
                    type="info" 
                    @click="validateBackup" 
                    :loading="validateLoading"
                    :disabled="!formData.es_dump_name"
                >
                    验证备份状态
                </Button>
                <Button 
                    type="primary" 
                    @click="bindBackup" 
                    :loading="bindLoading"
                    style="margin-left: 8px"
                >
                    保存绑定
                </Button>
                <Button @click="resetForm" style="margin-left: 8px">重置</Button>
            </FormItem>
        </Form>
        
        <Spin size="large" fix v-if="spinShow">
            <Icon type="ios-loading" size="32" class="demo-spin-icon-load"></Icon>
            <div>处理中...</div>
        </Spin>
    </Card>
</template>

<script>
import { getBackupList, validateBackupStatus, bindBusinessBackup, getEnvModules } from '@/spider-api/es-backup'
import { getBisNameLIstInfo, get_test_iter_list } from '@/spider-api/biz-mgt'

export default {
    name: 'BindBackup',
    data() {
        return {
            spinShow: false,
            validateLoading: false,
            bindLoading: false,
            formData: {
                biz_code: '',
                branch: '',
                suite_code: '',
                es_dump_name: '',
                operator: this.$store.state.user.userName
            },
            bizList: [],
            branchList: [],
            envList: [],
            backupList: [],
            backupStatus: ''
        }
    },
    methods: {
        // 获取环境列表
        async getEnvList() {
            try {
                const res = await getEnvModules()
                if (res.data.code === '0000' || res.data.code === 200) {
                    const data = res.data.data
                    // 构建环境列表（去重）
                    const envSet = new Set()
                    data.forEach(item => {
                        envSet.add(item.node_docker)
                    })
                    
                    this.envList = Array.from(envSet).map(env => ({
                        value: env,
                        label: env
                    }))
                } else {
                    // 当code不为0000时，显示message错误信息
                    const errorMessage = res.data.message || res.data.msg || '获取环境列表失败'
                    this.$Message.error(errorMessage)
                }
            } catch (error) {
                this.$Message.error('获取环境列表失败')
                console.error(error)
            }
        },
        
        // 获取业务列表
        async getBizList() {
            try {
                const res = await getBisNameLIstInfo()
                if (res.data.data) {
                    this.bizList = res.data.data
                } else {
                    this.$Message.error('获取业务列表失败')
                }
            } catch (error) {
                this.$Message.error('获取业务列表失败')
                console.error(error)
            }
        },
        
        // 业务变化时获取分支列表
        async onBizChange(bizCode) {
            this.formData.branch = ''
            this.branchList = []
            
            if (!bizCode) return
            
            try {
                const res = await get_test_iter_list({ biz_code: bizCode })
                if (res.data.data) {
                    this.branchList = res.data.data.map(item => ({
                        value: item.biz_test_iter_br,
                        label: item.biz_test_iter_br
                    }))
                } else {
                    this.$Message.error('获取分支列表失败')
                }
            } catch (error) {
                this.$Message.error('获取分支列表失败')
                console.error(error)
            }
        },
        
        // 环境变化时获取备份文件列表
        async onEnvChange(env) {
            this.formData.es_dump_name = ''
            this.backupList = []
            this.backupStatus = ''
            
            if (!env) return
            
            try {
                const res = await getBackupList({ suite_code: env })
                if (res.data.code === '0000' || res.data.code === 200) {
                    this.backupList = res.data.data.backups || []
                } else {
                    // 当code不为0000时，显示message错误信息
                    const errorMessage = res.data.message || res.data.msg || '获取备份列表失败'
                    this.$Message.error(errorMessage)
                }
            } catch (error) {
                this.$Message.error('获取备份列表失败')
                console.error(error)
            }
        },
        
        // 验证备份状态
        async validateBackup() {
            if (!this.formData.es_dump_name) {
                this.$Message.warning('请选择备份文件')
                return
            }
            
            this.validateLoading = true
            
            try {
                const res = await validateBackupStatus({ 
                    es_dump_name: this.formData.es_dump_name 
                })
                if (res.data.code === '0000' || res.data.code === 200) {
                    this.backupStatus = res.data.data.current_status
                    this.$Message.success('验证完成')
                } else {
                    // 当code不为0000时，显示message错误信息
                    const errorMessage = res.data.message || res.data.msg || '验证失败'
                    this.$Message.error(errorMessage)
                }
            } catch (error) {
                this.$Message.error('验证失败')
                console.error(error)
            } finally {
                this.validateLoading = false
            }
        },
        
        // 绑定备份
        async bindBackup() {
            // 表单验证
            if (!this.formData.biz_code) {
                this.$Message.warning('请选择业务')
                return
            }
            if (!this.formData.branch) {
                this.$Message.warning('请选择分支')
                return
            }
            if (!this.formData.es_dump_name) {
                this.$Message.warning('请选择备份文件')
                return
            }
            
            this.bindLoading = true
            this.spinShow = true
            
            try {
                const res = await bindBusinessBackup(this.formData)
                if (res.data.code === '0000' || res.data.code === 200) {
                    this.$Message.success('绑定成功')
                    this.resetForm()
                } else {
                    // 当code不为0000时，显示message错误信息
                    console.log('绑定接口返回错误:', res.data)
                    const errorMessage = res.data.message || res.data.msg || '绑定失败'
                    this.$Message.error(errorMessage)
                }
            } catch (error) {
                this.$Message.error('绑定失败')
                console.error(error)
            } finally {
                this.bindLoading = false
                this.spinShow = false
            }
        },
        
        // 获取状态颜色
        getStatusColor(status) {
            switch (status) {
                case 'SUCCESS':
                    return 'success'
                case 'FAILED':
                    return 'error'
                case 'RUNNING':
                    return 'processing'
                default:
                    return 'default'
            }
        },
        
        // 重置表单
        resetForm() {
            this.formData = {
                biz_code: '',
                branch: '',
                suite_code: '',
                es_dump_name: '',
                operator: this.$store.state.user.userName
            }
            this.branchList = []
            this.backupList = []
            this.backupStatus = ''
        },
        
        // 初始化
        async init() {
            await this.getEnvList()
            await this.getBizList()
        }
    },
    
    mounted() {
        this.init()
    }
}
</script>

<style scoped>
.demo-spin-icon-load {
    animation: ani-demo-spin 1s linear infinite;
}

@keyframes ani-demo-spin {
    from { transform: rotate(0deg); }
    50%  { transform: rotate(180deg); }
    to   { transform: rotate(360deg); }
}
</style>