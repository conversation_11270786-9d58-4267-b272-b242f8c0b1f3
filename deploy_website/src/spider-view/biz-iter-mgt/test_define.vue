<template>
    <Card shadow style="height: 100%;position: relative;">
        <Row style="margin-left: 5px;display: flex;align-items: center;">
            <i-col style="margin: 5px" span="2">
                <Icon type="ios-link-outline" style="margin-right: 5px" />
                <span style="text-align: left; display: inline-block; width:60px;">业务类型&nbsp;</span>
            </i-col>
            <i-col style="margin: 5px  5px  5px 0px;" span="4">
                <Select
                    placeholder="填写业务类型"
                    v-model="formValidate.biz_code"
                    size="small"
                    filterable
                    clearable
                    style="width:200px"
                    @on-change="handleBizCodeSelectChange"
                >
                    <Option
                        v-for="item in bis_name_list"
                        :value="item.biz_code"
                        :key="item.biz_code"
                        :label="item.biz_name"
                    >
                        {{ item.biz_name }}
                    </Option>
                </Select>
            </i-col>
        </Row>

        <Row style="margin-left: 5px;display: flex;align-items: center;">
            <i-col style="margin: 5px" span="2">
                <Icon type="ios-brush-outline" style="margin-right: 5px" />
                <span style="text-align: left; display: inline-block; width:60px;">命名编排</span>
            </i-col>
            <i-col style="margin: 5px  5px  5px 0px;" span="4">
                <Input
                    v-model="formValidate.flow_name"
                    size="small"
                    placeholder="填写执行流水线名称"
                    style="width:200px"
                />
            </i-col>
            <i-col>
                <Button
                    type="primary"
                    size="small"
                    @click="showHistoryModal"
                    :loading="btnDisabled"
                    style="margin-left: 10px;"
                >
                    查看编排列表
                </Button>
            </i-col>
        </Row>
        <Row style="margin-left: 5px;display: flex;align-items: center;">
            <i-col style="margin: 5px" span="2">
                <Icon type="ios-nutrition-outline" style="margin-right: 5px" />
                <span style="text-align: left; display: inline-block; width:60px;">编排：&nbsp;</span>
            </i-col>
            <i-col style="margin: 5px  5px  5px 0px; width: 80%;max-height: 500px; border: 1px #2d8cf0 solid" span="4">
                <draggable v-model="modalData" :animation="200" class="flow-layout">
                    <!-- <div class="flow-layout"> -->
                    <div
                        class="flow-item"
                        v-for="(item, index) in modalData"
                        :key="index"
                        style="max-height: 400px; overflow: auto"
                    >
                        <span class="index">{{ index + 1 }}</span>
                        <div
                            style="width: 100%;display: flex;justify-content: flex-start;align-items: center; margin-bottom: 5px;"
                        >
                            <Checkbox v-model="item.deleteFile">删文件</Checkbox>
                            <Input
                                size="small"
                                v-model="item.filePath"
                                :disabled="!item.deleteFile"
                                style="width: 340px;"
                                placeholder="/data/files/..."
                            />
                        </div>
                        <div
                            style="width: 100%;display: flex;justify-content: flex-start;align-items: center; margin-bottom: 5px;"
                        >
                            <Checkbox v-model="item.changeTime">改时间</Checkbox>
                            <DatePicker
                                size="small"
                                v-model="item.time"
                                type="datetime"
                                style="width: 340px;"
                                :disabled="!item.changeTime"
                                placeholder="选择时间"
                            />
                        </div>
                        <div
                            style="width: 100%;display: flex;justify-content: flex-start;align-items: center; margin-bottom: 5px; height: 24px;"
                        >
                            <Checkbox v-model="item.restartApp">重启</Checkbox>
                        </div>
                        <div
                            v-if="item.restartApp"
                            style="width: 100%;display: flex;justify-content: flex-start;align-items: center;margin-bottom: 5px;min-height: 24px"
                        >
                            <label style="margin-right: 20px;padding-left: 18px;font-size: 12px;">指定</label>
                            <Select
                                size="small"
                                v-model="item.appLists"
                                style="width: 340px; border-radius: 3px; max-height: 100px; overflow: auto;"
                                multiple
                                :disabled="appOptions.length <= 0"
                            >
                                <Option v-for="item in appOptions" :value="item.value" :key="item.value">
                                    {{ item.name }}
                                </Option>
                            </Select>
                        </div>
                        <div
                            style="width: 100%;display: flex;justify-content: flex-start;align-items: center; margin-bottom: 5px; height: 24px;"
                        >
                            <Checkbox v-model="item.isChecked">检测</Checkbox>
                        </div>
                        <div
                            style="width: 100%;display: flex;justify-content: flex-start;align-items: center; margin-bottom: 5px;"
                        >
                            <Checkbox v-model="item.wait_check">等一会</Checkbox>
                            <Input
                                size="small"
                                type="number"
                                v-model="item.wait_minutes"
                                min="1"
                                :disabled="!item.wait_check"
                                style="width: 340px;"
                                placeholder="等几秒执行后面的点"
                            />
                        </div>
                        <div
                            style="width: 100%;display: flex;justify-content: flex-start;align-items: center; margin-bottom: 5px;"
                        >
                            <Checkbox v-model="item.runTestSet">测试集</Checkbox>
                            <Select
                                size="small"
                                v-model="item.testSetId"
                                :disabled="!item.runTestSet"
                                style="width: 340px; border-radius: 3px; max-height: 100px; overflow: auto;"
                                filterable
                                clearable
                                multiple
                            >
                                <Option
                                    v-for="option in testSetOptions"
                                    :value="option.value.toString()"
                                    style="width: 120px;"
                                    :key="option.value.toString()"
                                    >{{ option.name }}</Option
                                >
                            </Select>
                            <Button type="text" size="small" @click="showSort(item, index)">调序</Button>
                        </div>
                        <div
                            style="width: 100%;display: flex;justify-content: flex-start;align-items: center; margin-bottom: 5px;"
                        >
                            <Checkbox v-model="item.makeDump">DUMP</Checkbox>
                            <Input
                                size="small"
                                v-model="item.dumpPrefix"
                                :disabled="!item.makeDump"
                                style="width: 340px;"
                                placeholder="dump文件前缀名"
                            />
                        </div>
                        <div style="width: 100%;margin-top: 20px">
                            <Button type="primary" size="small" @click="removeModal(index)" :loading="btnDisabled"
                                >删除阶段
                            </Button>
                        </div>
                    </div>
                    <!-- </div> -->
                </draggable>
            </i-col>
        </Row>
        <Row style="margin-left: 5px;display: flex;align-items: center;">
            <i-col style="margin: 5px" span="2">
                <Icon type="logo-youtube" style="margin-right: 5px" />
                <span style="text-align: left; display: inline-block; width:60px;">操作：&nbsp;</span>
            </i-col>
            <i-col style="margin: 5px  5px  5px 0px;" span="2">
                <Button type="primary" size="small" @click="addModal" :loading="btnDisabled" :disabled="addDisabled"
                    >添加阶段
                </Button>
            </i-col>
            <i-col style="margin: 5px  5px  5px 0px;" span="2">
                <Button type="primary" size="small" @click="handleSubmit" :loading="btnDisabled"
                    >保存 (共{{ modalData.length }}个阶段)
                </Button>
            </i-col>
        </Row>
        <Spin size="large" fix v-if="spinShow">
            <Icon type="ios-loading" size="32" class="demo-spin-icon-load"></Icon>
            <div>请求中...</div>
        </Spin>

        <!-- 添加删除确认模态框 -->
        <Modal v-model="modalVisible" title="确认删除" ok-text="确认" cancel-text="取消" @on-ok="deleteModalData">
            <p><span style="color:red;">是否确认删除？</span></p>
        </Modal>

        <!-- 查看历史编排 -->
        <Modal
            v-model="historyModalVisible"
            :title="historyModalTitle"
            :mask-closable="false"
            :footer-hide="false"
            width="1000px"
        >
            <div class="history-form-box">
                <Input
                    v-model="historyBizPipelineName"
                    clearable
                    placeholder="请输入编排名称"
                    class="form-item-input"
                />
                <Input v-model="creator" clearable placeholder="请输入创建人" class="form-item-input" />
                <Select
                    placeholder="业务"
                    v-model="biz_code"
                    filterable
                    clearable
                    style="width:200px;margin-right: 10px;"
                >
                    <Option
                        v-for="item in bis_name_list"
                        :value="item.biz_code"
                        :key="item.biz_code"
                        :label="item.biz_name"
                    >
                        {{ item.biz_name }}
                    </Option>
                </Select>
                <Button type="primary" @click="handleSearchButtonClick">搜索</Button>
            </div>
            <Table :data="historyTableData" :columns="historyColumns" :pagination="true">
                <template slot-scope="{ row, index }" slot="action">
                    <div style="display: flex; gap: 4px;">
                        <Button type="primary" size="small" @click="handleRefererButtonClick(row)">编辑</Button>
                        <Button type="default" size="small" @click="handleGoToJenkinsButtonClick(row)">Jenkins</Button>
                        <Button 
                            v-if="canDelete(row)" 
                            type="error" 
                            size="small" 
                            @click="handleDeleteButtonClick(row)"
                        >删除</Button>
                    </div>
                </template>
            </Table>
            <div slot="footer">
                <Page
                    :total="pagination.total"
                    :page-size="pagination.size"
                    :current="pagination.page"
                    show-total
                    show-elevator
                    show-sizer
                    @on-change="pageChange"
                    @on-page-size-change="pageSizeChange"
                />
            </div>
        </Modal>
        <Modal v-model="showTestSet" title="调整测试集执行顺序" @on-ok="sortConfirm" @on-cancel="showTestSet = false">
            <draggable
                class="draggable-container"
                v-model="testSortArr"
                chosen-class="chosen"
                force-fallback="true"
                group="people"
                animation="500"
            >
                <transition-group>
                    <div class="item no-select" v-for="element in testSortArr" :key="element.id">
                        {{ element.name }}
                    </div>
                </transition-group>
            </draggable>
        </Modal>
    </Card>
</template>

<script>
import {
    get_biz_pipeline,
    get_biz_pipeline_detail,
    get_test_set_list,
    getBisNameLIstInfo,
    save_biz_pipeline,
    get_biz_app_list,
    delete_biz_pipeline_flow
} from '@/spider-api/biz-mgt'
import isEmpty from '@/mixin/isEmpty'
import time from '@/mixin/time'
import draggable from 'vuedraggable'

export default {
    mixins: [time, isEmpty],
    name: 'test_define',
    components: { draggable },
    data() {
        return {
            testSortArr: [],
            curSortIndex: 0,
            showTestSet: false,
            spinShow: false,
            br_show: true,
            bis_name_list: [],

            btnDisabled: false, // 按钮是否禁用状态
            addDisabled: false,
            modalVisible: false, // 模态框是否可见
            modalData: [], // 编排内容数据
            testSetOptions: [], // 测试集id下拉框数据
            appListsOptions: [], // 排除应用下拉框数据
            test_id: '',
            formValidate: {
                biz_code: '',
                flow_name: '',
                flow_content: {}
            },
            curIndex: 0, // 删除阶段的下标

            // 历史编排数据相关
            pagination: {
                page: 1,
                total: 0,
                size: 10
            },
            historyModalVisible: false, // 历史编排模态框是否可见
            historyColumns: [
                {
                    title: '序号',
                    key: 'no',
                    width: 60,
                    render: (h, params) => {
                        return h('span', params.index + 1)
                    }
                },
                {
                    title: '编排命名',
                    key: 'biz_pipeline_name'
                },
                {
                    title: '创建人',
                    key: 'create_user',
                    width: 130
                },
                {
                    title: '创建时间',
                    key: 'create_time',
                    width: 160
                },
                {
                    title: '操作',
                    key: 'action',
                    slot: 'action',
                    width: 200
                    // render: (h, params) => {
                    //   return h('Button', {
                    //     props: {
                    //       type: 'primary',
                    //       size: 'small'
                    //     },
                    //     on: {
                    //       click: () => {
                    //         this.historyModalVisible = false
                    //         this.edit_biz_pipeline(params)
                    //       }
                    //     }
                    //   }, '引用')
                    // }
                }
            ],
            historyTableData: [],
            historyModalTitle: '',
            historyBizPipelineName: '', // 历史编排查询条件编排名称
            biz_code: '',
            creator: ''
        }
    },

    computed: {
        appOptions() {
            return this.appListsOptions
        }
    },
    methods: {
        showSort(item, index) {
            this.showTestSet = true
            this.curSortIndex = index
            this.testSortArr = item.testSetId.map(item => {
                return {
                    id: item,
                    name: this.testSetOptions.filter(option => option.value == item)[0].name
                }
            })
        },
        sortConfirm() {
            this.modalData[this.curSortIndex].testSetId = this.testSortArr.map(item => item.id)
        },
        // 根据业务类型查询应用列表
        getBizApplist(bizCode) {
            get_biz_app_list({
                biz_code: bizCode
            }).then(res => {
                console.log('getBizAppList result: ', res)

                this.appListsOptions =
                    res.data.data.map(item => {
                        return {
                            name: item.app_module_name,
                            value: item.app_module_name
                        }
                    }) || []
            })
        },
        // 业务类型变更事件
        handleBizCodeSelectChange(value) {
            if (!value) return

            let hasAppLists = false
            this.modalData.map(item => {
                if (item.appLists.length > 0) {
                    hasAppLists = true
                    item.appLists = []
                }
                return item
            })

            if (hasAppLists) {
                this.$Message.info('业务类型变更，已清空指定重启应用, 请重新配置！')
            }

            this.getBizApplist(value)
        },
        // 引用按钮点击事件
        handleRefererButtonClick(row) {
            this.historyModalVisible = false
            console.log(row, 'row')
            this.edit_biz_pipeline({ row: row })
        },
        // 打开Jenkins链接按钮点击事件
        handleGoToJenkinsButtonClick(row) {
            if (!row.jenkins_url) return

            window.open(row.jenkins_url)
        },
        // 判断当前用户是否可以删除
        canDelete(row) {
            const currentUser = this.$store.state.user.userName
            return currentUser === row.create_user
        },
        // 删除按钮点击事件
        handleDeleteButtonClick(row) {
            this.$Modal.confirm({
                title: '确认删除',
                content: `<p style="color: red; font-weight: bold;">警告：此操作不可逆！</p><p>确定要删除编排"${row.biz_pipeline_name}"吗？</p><p>删除后将无法恢复，请谨慎操作。</p>`,
                okText: '确认删除',
                cancelText: '取消',
                onOk: () => {
                    // 二次确认
                    this.$Modal.confirm({
                        title: '最终确认',
                        content: `<p style="color: red;">这是最后一次确认！</p><p>您真的要删除编排"${row.biz_pipeline_name}"吗？</p>`,
                        okText: '确认删除',
                        cancelText: '取消',
                        onOk: () => {
                            this.deleteBizPipelineFlow(row)
                        }
                    })
                }
            })
        },
        // 删除业务流水线编排
        deleteBizPipelineFlow(row) {
            const param = {
                biz_code: row.biz_code,
                biz_flow_name: row.biz_pipeline_name.split('_').slice(1).join('_')
            }
            
            delete_biz_pipeline_flow(param)
                .then(res => {
                    if (res.data.status === 'success') {
                        this.$Message.success('删除成功')
                        this.getHistoryTableData() // 刷新列表
                    } else {
                        this.$Message.error('删除失败：' + res.data.msg)
                    }
                })
                .catch(err => {
                    const errorMsg = err.response && err.response.data && err.response.data.msg ? err.response.data.msg : '网络错误'
                    this.$Message.error('删除失败：' + errorMsg)
                })
        },
        // 历史编排搜索按钮点击事件
        handleSearchButtonClick() {
            this.pagination.page = 1
            this.getHistoryTableData()
        },
        // 显示历史编排模态框
        showHistoryModal() {
            this.historyModalTitle = '编排列表'
            this.historyBizPipelineName = ''
            this.biz_code = ''
            this.creator = ''
            this.getHistoryTableData()
            this.historyModalVisible = true
        },
        // 加载历史编排的数据
        setPagParams(page, size, total) {
            /**
             * 设置分页参数
             */
            if (!this.isEmpty(page)) {
                this.pagination.page = page
            }
            if (!this.isEmpty(size)) {
                this.pagination.size = size
            }
            if (!this.isEmpty(total)) {
                this.pagination.total = total
            }
        },
        getHistoryTableData() {
            // 模拟异步加载
            let data = {
                page: this.pagination.page,
                size: this.pagination.size,
                biz_pipeline_name: this.historyBizPipelineName || undefined,
                biz_code: this.biz_code || undefined,
                creator: this.creator || undefined
            }
            this.get_biz_pipeline_data(data)
        },
        get_biz_pipeline_data(data) {
            get_biz_pipeline(data).then(res => {
                if (res.data.status === 'success') {
                    this.setPagParams('', '', res.data.data.count)
                    this.historyTableData = res.data.data.results
                }
            })
        },
        pageChange(page) {
            /**
             * 分页中改变size触发的函数
             */
            this.setPagParams(page, '', '')
            let data = {
                page: this.pagination.page,
                size: this.pagination.size,
                biz_pipeline_name: this.historyBizPipelineName || undefined
            }
            this.get_biz_pipeline_data(data)
        },
        pageSizeChange(size) {
            this.setPagParams('', size, '')
            let data = {
                page: 1,
                size: this.pagination.size,
                biz_pipeline_name: this.historyBizPipelineName || undefined
            }
            this.get_biz_pipeline_data(data)
        },
        // 点击添加阶段按钮
        addModal() {
            this.modalData.push({
                // clearCache: false, // 清除缓存
                changeTime: false,
                time: '',
                restartApp: false, // 重启
                appLists: [],
                isChecked: true, // 检测步骤 new
                runTestSet: true,
                testSetId: '',
                deleteFile: false,
                filePath: '',
                makeDump: false,
                dumpPrefix: '',
                wait_minutes: 0,
                wait_check: false
            })
            console.info('addModal')
            console.info(this.modalData.length)
            if (this.modalData.length >= 20) {
                this.addDisabled = true
            }
        },
        // 点击删除阶段按钮
        removeModal(index) {
            this.curIndex = index
            this.modalVisible = true // 打开删除确认模态框
        },
        // 确认删除模态框的处理
        deleteModalData() {
            this.modalVisible = false // 关闭删除确认模态框
            this.modalData.splice(this.curIndex, 1) // 删除当前阶段
            this.curIndex = 0
        },
        getBisNameInfo() {
            console.log('这里开始getBisNameInfo。。。')
            this.addDisabled = false
            getBisNameLIstInfo().then(res => {
                this.bis_name_list = res.data.data
                console.log('this.$route.params.biz_code11===' + this.$route.query.biz_code)
                console.log('this.$route.params.biz_flow_name11===' + this.$route.query.biz_flow_name)
                if (!this.isEmpty(this.$route.query.biz_code) && !this.isEmpty(this.$route.query.biz_flow_name)) {
                    let biz_name = ''
                    for (let i = 0; i < this.bis_name_list.length; i++) {
                        if (this.$route.query.biz_code == this.bis_name_list[i].biz_code) {
                            biz_name = this.bis_name_list[i].biz_name
                            break
                        }
                    }
                    let data = { biz_pipeline_name: biz_name + '_' + this.$route.query.biz_flow_name }
                    get_biz_pipeline_detail(data).then(res => {
                        this.modalData = res.data.data
                        this.formValidate.biz_code = this.$route.query.biz_code
                        this.formValidate.flow_name = this.$route.query.biz_flow_name
                        if (this.modalData.length >= 20) {
                            this.addDisabled = true
                        }
                    })
                }
            })
        },
        testSetOptionsSelect() {
            get_test_set_list().then(res => {
                if (res.data.status === 'success') {
                    this.testSetOptions = res.data.data.map(item => {
                        return {
                            value: item.id,
                            name: item.id + '【' + item.title + '】'
                        }
                    })
                }
            })
        },
        formatDate(sourceDateObj) {
            let dateObj
            if (typeof sourceDateObj === 'string') {
                dateObj = new Date(sourceDateObj)
            } else {
                dateObj = sourceDateObj
            }
            let year = dateObj.getFullYear()
            let month = dateObj.getMonth() + 1
            month = month < 10 ? '0' + month : month
            let day = dateObj.getDate()
            day = day < 10 ? '0' + day : day
            let hour = dateObj.getHours()
            hour = hour < 10 ? '0' + hour : hour
            let minute = dateObj.getMinutes()
            minute = minute < 10 ? '0' + minute : minute
            let second = dateObj.getSeconds()
            second = second < 10 ? '0' + second : second
            let dateString = year + '-' + month + '-' + day + ' ' + hour + ':' + minute + ':' + second
            return dateString
        },
        edit_biz_pipeline(param) {
            let biz_pipeline_name = param.row.biz_pipeline_name
            let data = { biz_pipeline_name: biz_pipeline_name }
            this.getBizApplist(param.row.biz_code)
            this.addDisabled = false
            get_biz_pipeline_detail(data).then(res => {
                this.modalData = res.data.data
                if (this.modalData.length >= 20) {
                    this.addDisabled = true
                }
            })
            let index = biz_pipeline_name.indexOf('_')
            this.formValidate.biz_code = param.row.biz_code
            this.formValidate.flow_name = biz_pipeline_name.slice(index + 1)
        },
        init() {
            this.addModal()
        },
        handleSubmit() {
            console.log('modalData的长度：' + this.modalData.length)
            if (this.modalData.length > 20) {
                this.$Modal.error({
                    title: '提示',
                    content: '编排数量不能为超过20个'
                })
                return false
            }
            let vm_data = this.modalData
            let flow_content = {}
            let set_time_check = true
            let test_set_check = true
            let flow_content_check = true
            let file_path_check = true
            let app_list_check = true
            let dump_prefix_check = true
            for (let i = 0; i < vm_data.length; i++) {
                let j = 1
                flow_content[i + 1] = {}
                // if (vm_data[i].clearCache) {
                //   flow_content[i + 1][j] = {"action": "clear_cache", "action_name": "清缓存", "param": ""}
                //   j += 1
                // }
                if (vm_data[i].deleteFile) {
                    if (vm_data[i].filePath === '') {
                        file_path_check = false
                        break
                    } else {
                        let param = {}
                        param['file_path'] = vm_data[i].filePath
                        flow_content[i + 1][j] = { action: 'delete_file', action_name: '删文件', param: param }
                        j += 1
                    }
                }
                if (vm_data[i].changeTime) {
                    if (vm_data[i].time === '') {
                        set_time_check = false
                        break
                    } else {
                        let param = {}
                        param['sys_datetime'] = this.formatDate(vm_data[i].time)
                        console.log('vm_data[i].time===' + param['sys_datetime'])
                        flow_content[i + 1][j] = { action: 'set_time', action_name: '改时间', param: param }
                        j += 1
                    }
                }
                if (vm_data[i].restartApp) {
                    let param = {}
                    if (vm_data[i].appLists.length === 0) {
                        app_list_check = false
                        break
                    }
                    param['app_list'] = vm_data[i].appLists
                    flow_content[i + 1][j] = { action: 'restart_app', action_name: '重启', param: param }
                    j += 1
                }
                if (vm_data[i].isChecked) {
                    let param = {}
                    param['is_checked'] = ''
                    flow_content[i + 1][j] = { action: 'is_checked', action_name: '检测', param: param }
                    j += 1
                }
                if (vm_data[i].wait_check) {
                    let param = {}
                    param['wait_minutes'] = vm_data[i].wait_minutes
                    console.log('vm_data[i].wait_minutes===' + param['wait_minutes'])
                    flow_content[i + 1][j] = { action: 'wait_check', action_name: '等一会', param: param }
                    j += 1
                }
                if (vm_data[i].runTestSet) {
                    if (vm_data[i].testSetId === '') {
                        test_set_check = false
                        break
                    } else {
                        let param = {}
                        param['test_set'] = vm_data[i].testSetId
                        console.log('vm_data[i].testSetId===' + vm_data[i].testSetId)
                        flow_content[i + 1][j] = { action: 'run_testset', action_name: '测试集', param: param }
                        j += 1
                    }
                }
                if (vm_data[i].makeDump) {
                    if (vm_data[i].dumpPrefix === '') {
                        dump_prefix_check = false
                        break
                    } else {
                        let param = {}
                        param['dump_file_prefix_name'] = vm_data[i].dumpPrefix
                        flow_content[i + 1][j] = { action: 'make_dump', action_name: 'DUMP', param: param }
                        j += 1
                    }
                }
                if (JSON.stringify(flow_content[i + 1]) === '{}') {
                    flow_content_check = false
                }
            }
            console.log('this.formValidate.flow_name===' + this.formValidate.flow_name)
            console.log('this.formValidate.flow_name.indexOf===' + this.formValidate.flow_name.indexOf('_'))
            if (!this.formValidate.biz_code) {
                this.$Modal.info({
                    title: '提示',
                    content: '业务类型不能为空！'
                })
                // alert("业务类型不能为空！")
            } else if (!this.formValidate.flow_name) {
                this.$Modal.info({
                    title: '提示',
                    content: '命名编排不能为空！'
                })
            } else if (!file_path_check) {
                this.$Modal.info({
                    title: '提示',
                    content: '请设置文件路径！'
                })
            } else if (!set_time_check) {
                this.$Modal.info({
                    title: '提示',
                    content: '请设置时间！'
                })
            } else if (!app_list_check) {
                this.$Modal.info({
                    title: '提示',
                    content: '请指定需要重启的应用！'
                })
            } else if (!test_set_check) {
                this.$Modal.info({
                    title: '提示',
                    content: '请设置测试集id！'
                })
            } else if (!dump_prefix_check) {
                this.$Modal.info({
                    title: '提示',
                    content:
                        '请设置dump文件前缀名，例如，abc，后台打出来的dump文件名为：abc_docker_it12_deal_0_20240706121212.sql！'
                })
            } else if (this.formValidate.flow_name.indexOf('_') !== -1) {
                this.$Modal.info({
                    title: '提示',
                    content: "编排名称不可包含下划线'_'！"
                })
                // alert("编排名称不可包含下划线'_'！")
            } else if (!flow_content_check || JSON.stringify(flow_content) === '{}') {
                this.$Modal.info({
                    title: '提示',
                    content: '编排内容不能为空！'
                })
                // alert("编排内容不能为空！")
            } else {
                this.formValidate.flow_content = flow_content

                this.spinShow = !this.spinShow
                save_biz_pipeline(this.formValidate)
                    .then(res => {
                        if (res.data.status === 'success') {
                            // alert(res.data.msg)
                            this.$Modal.success({
                                title: '提示',
                                content: res.data.msg
                            })
                        } else {
                            this.$Modal.error({
                                title: '提示',
                                content: '业务执行流水线保存失败！原因：' + res.data.msg
                            })
                            // alert("业务执行流水线保存失败！原因：" + res.data.msg)
                        }
                    })
                    .finally(() => {
                        this.spinShow = !this.spinShow
                    })
            }
        }
    },

    created() {
        this.getBisNameInfo()
        this.testSetOptionsSelect()
    },
    mounted() {
        console.log('this.$route.params.biz_code===' + this.$route.query.biz_code)
        console.log('this.$route.params.biz_flow_name===' + this.$route.query.biz_flow_name)
        this.init()
        if (this.$route.query.biz_code) this.getBizApplist(this.$route.query.biz_code)
    }
}
</script>

<style scoped>
.draggable-container {
    cursor: move;
}

.no-select {
    -webkit-user-select: none; /* Safari */
    -moz-user-select: none; /* Firefox */
    -ms-user-select: none; /* IE10+/Edge */
    user-select: none; /* 标准语法 */
}
.item {
    padding: 6px;
    background-color: #fdfdfd;
    border: solid 1px #eee;
    margin-bottom: 10px;
    cursor: move;
}

.item:hover {
    background-color: #f1f1f1;
    cursor: move;
}

.chosen {
    border: solid 2px #3089dc !important;
}
.demo-spin-icon-load {
    animation: ani-demo-spin 1s linear infinite;
}

.flow-layout {
    display: -webkit-box;
    width: auto;
    overflow: auto;
}

.flow-item {
    position: relative;
    width: 480px;
    border: 1px solid black;
    padding: 16px;
    margin: 16px;
}

.index {
    position: absolute;
    top: 5px;
    right: 10px;
    font-size: 20px;
    color: #409eff;
}
</style>

<style>
.history-form-box {
    display: flex;
    justify-content: start;
    align-items: center;
    margin-bottom: 20px;
    .form-item-input {
        width: 220px;
        margin-right: 20px;
    }
}
</style>
