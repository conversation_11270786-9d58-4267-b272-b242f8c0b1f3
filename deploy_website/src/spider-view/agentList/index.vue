<!--
 * @Author: chao.bao <EMAIL>
 * @Date: 2025-05-28 09:32:01
 * @LastEditors: chao.bao <EMAIL>
 * @LastEditTime: 2025-08-04 15:52:25
 * @FilePath: /website_web/deploy_website/src/spider-components/AgentComponent/index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div class="agent_wrapper">
        <Spin v-if="loading" fix></Spin>
        <div class="title_contain" slot="header">
            <!-- <div class="title">agent开关设置</div> -->
            <div class="tips">说明：应用与环境存在绑定关系才显示数据</div>
        </div>
        <div class="query_aera">
            <div class="custom_label">应用名称：</div>
            <Select v-model="app_list" placeholder="应用名称(可多选)" multiple filterable style="width:200px">
                <Option v-for="item in appList" :value="item.value" :key="item.value" :label="item.label">{{
                    item.label
                }}</Option>
            </Select>
            <div class="custom_label">环境：</div>
            <Select
                class="item_com"
                v-model="suite_list"
                placeholder="环境(可多选)"
                multiple
                filterable
                style="width:200px"
            >
                <Option v-for="item in suiteList" :value="item.value" :key="item.value" :label="item.label">{{
                    item.label
                }}</Option>
            </Select>
            <div class="custom_label">agent：</div>
            <Select
                class="item_com"
                v-model="agent_module_name"
                placeholder="agent"
                filterable
                clearable
                style="width:200px"
            >
                <Option v-for="item in agentList" :value="item.value" :key="item.value" :label="item.label">{{
                    item.label
                }}</Option>
            </Select>
            <Button class="item_com" type="primary" @click="queryList">查询</Button>
            <Button class="item_com" type="primary" @click="openHandler">批量开</Button>
            <Button class="item_com" @click="closeHandler">批量关</Button>
        </div>
        <Table ref="tableRef" :columns="editColumns" :data="tableDataEdit" @on-selection-change="changeSelect"></Table>
        <div style="display: flex; justify-content: space-between;margin-top: 20px;">
            <Page :total="total" :current="page_num" :page-size="page_size" @on-change="changePage" show-total></Page>
            <!-- <Button type="primary" :loading="loading" @click="ok">保存</Button> -->
        </div>
    </div>
</template>

<script>
import { getAgentUseInfo, saveAgentUserInfo, getPublishAppList, getAllTestingEnv, get_agent_list } from './api'
import { deepClone } from '@/utils'
export default {
    data() {
        return {
            editColumns: [
                {
                    type: 'selection',
                    width: 60,
                    align: 'center'
                },
                {
                    title: '应用名称',
                    key: 'app_name'
                },
                {
                    title: 'agent名称',
                    key: 'agent_name',
                    sortable: true
                },
                {
                    title: '环境',
                    key: 'suite_code',
                    width: 100,
                    sortable: true
                },
                {
                    title: '开关',
                    key: 'is_use',
                    width: 100,
                    align: 'center',
                    render: (h, params) => {
                        return h('i-switch', {
                            props: {
                                value: params.row.is_use === 1 ? true : false,
                                disabled: params.row.forced_mount === '1' ? true : false
                            },
                            on: {
                                'on-change': status => {
                                    // 更新数据
                                    this.tableDataEdit[params.index].is_use = status ? 1 : '0'
                                    // 手动重置勾选框状态
                                    this.selectionData = this.$refs.tableRef.getSelection()
                                    this.$nextTick(() => {
                                        // 获取当前选中元素selectionData在tableDataEdit中的索引
                                        this.selectionData.map(item => {
                                            const index = this.tableDataEdit.findIndex(item2 => {
                                                return (
                                                    item2.app_name === item.app_name &&
                                                    item2.agent_name === item.agent_name &&
                                                    item2.suite_code === item.suite_code
                                                )
                                            })
                                            this.$refs.tableRef.toggleSelect(index)
                                        })
                                        console.log('change--------', this.selectionData)
                                        this.ok()
                                    })
                                }
                            },
                            scopedSlots: {
                                open: () => h('span', '开'),
                                close: () => h('span', '关')
                            }
                        })
                    }
                }
            ],
            tableDataEdit: [],
            appList: [],
            suiteList: [],
            agentList: [], // agent选择
            app_list: [], // 应用名称选择
            suite_list: [], // 环境选择
            // app_list: ['howbuy-wechat-message-console'], // 应用名称选择
            // suite_list: ['it10'], // 环境选择
            agent_module_name: '',
            selectionData: [],
            loading: false,
            total: 0,
            page_num: 1,
            page_size: 10
        }
    },
    methods: {
        openHandler() {
            this.tableDataEdit.map(item => {
                if (item.forced_mount === '1') return
                // 如果item不再this.selectionData中，则return
                if (
                    !this.selectionData.find(
                        item2 => item2.app_name === item.app_name && item2.agent_name === item.agent_name
                    )
                )
                    return
                item.is_use = 1
            })
            // 手动重置勾选框状态
            this.selectionData = this.$refs.tableRef.getSelection()
            setTimeout(() => {
                // 获取当前选中元素selectionData在tableDataEdit中的索引
                this.selectionData.map(item => {
                    const index = this.tableDataEdit.findIndex(item2 => {
                        return (
                            item2.app_name === item.app_name &&
                            item2.agent_name === item.agent_name &&
                            item2.suite_code === item.suite_code
                        )
                    })
                    this.$refs.tableRef.objData[index]._isChecked = true
                })
            }, 0)
            this.ok()
        },
        closeHandler() {
            this.tableDataEdit.map(item => {
                if (item.forced_mount === '1') return
                // 如果item不再this.selectionData中，则return
                if (
                    !this.selectionData.find(
                        item2 => item2.app_name === item.app_name && item2.agent_name === item.agent_name
                    )
                )
                    return
                item.is_use = 0
            })
            // 手动重置勾选框状态
            this.selectionData = this.$refs.tableRef.getSelection()
            setTimeout(() => {
                // 获取当前选中元素selectionData在tableDataEdit中的索引
                this.selectionData.map(item => {
                    const index = this.tableDataEdit.findIndex(item2 => {
                        return (
                            item2.app_name === item.app_name &&
                            item2.agent_name === item.agent_name &&
                            item2.suite_code === item.suite_code
                        )
                    })
                    this.$refs.tableRef.objData[index]._isChecked = true
                })
            }, 0)
            this.ok()
        },
        changeSelect(selection) {
            this.selectionData = deepClone(selection)
        },
        ok() {
            this.loading = true
            const arr = this.selectionData.length > 0 ? this.selectionData : this.tableDataEdit
            saveAgentUserInfo({ app_agent_list: arr })
                .then(res => {
                    if (res.data.status === 'success') {
                        this.$Message.success('保存成功')
                    } else {
                        this.$Message.error(res.data.message)
                    }
                })
                .finally(() => {
                    this.loading = false
                })
        },
        queryList() {
            getAgentUseInfo({
                app_list: this.app_list,
                suite_list: this.suite_list,
                agent_module_name: this.agent_module_name,
                page_num: this.page_num,
                page_size: this.page_size
            })
                .then(res => {
                    if (res.data.status === 'success') {
                        this.tableDataEdit = deepClone(res.data.data.data)
                        this.total = res.data.data.total
                        this.tableDataEdit = this.tableDataEdit.map(item => {
                            item._disabled = item.forced_mount === '1' ? true : false
                            return item
                        })
                    }
                })
                .finally(() => {
                    this.$Spin.hide()
                })
        },
        changePage(page) {
            this.page_num = page
            this.queryList()
        }
    },
    async mounted() {
        getPublishAppList({}).then(resapp => {
            if (resapp.data.status === 'success') {
                this.appList = resapp.data.data.app_list.map(item => {
                    return {
                        value: item,
                        label: item
                    }
                })
            }
        })
        getAllTestingEnv({ page: 1, size: 100 }).then(resenv => {
            if (resenv.data.status === 'success') {
                const envList = resenv.data.data && resenv.data.data.results ? resenv.data.data.results : []
                this.suiteList = envList
                    .filter(item => item.suite_code.indexOf('it') !== -1)
                    .map(item => ({
                        value: item.suite_code,
                        label: item.suite_code
                    }))
            }
        })
        get_agent_list({}).then(resagent => {
            if (resagent.data.status === 'success') {
                this.agentList = resagent.data.data.map(item => {
                    return {
                        value: item.agent_module_name,
                        label: item.agent_module_name
                    }
                })
            }
        })
        console.log(this.$route.query, this.$route.query.app_list.split())
        this.app_list = this.$route.query.app_list.split(',') || []
        this.suite_list = this.$route.query.suite_list.split(',') || []
        if (this.app_list.length > 0 && this.suite_list.length > 0) {
            this.queryList()
        }
    }
}
</script>

<style lang="less" scoped>
.edit_btn {
    display: flex;
    justify-content: end;
    margin-bottom: 10px;
}
.agent_wrapper {
    position: relative;
}
.query_aera {
    display: flex;
    margin-bottom: 10px;
    .item_com {
        margin-left: 10px;
        height: 31px;
    }
}
.title_contain {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    .title {
        height: 20px;
        line-height: 20px;
        font-size: 14px;
        color: #17233d;
        font-weight: bold;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    .tips {
        color: red;
        font-size: 16px;
        margin-left: 28px;
    }
}
.custom_label {
    margin-top: 8px;
    margin-left: 30px;
    font-weight: bold;
}
</style>
