/*
 * @Author: chao.bao <EMAIL>
 * @Date: 2025-05-28 10:18:46
 * @LastEditors: chao.bao <EMAIL>
 * @LastEditTime: 2025-07-29 15:28:43
 * @FilePath: /website_web/deploy_website/src/spider-components/AgentComponent/api.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import spider_axios from '@/libs/spider_api.request'

// post查询应用在环境下的默认agent配置
export const getAgentUseInfo = data => {
    return spider_axios.request({
        url: '/spider/pipeline/get_agent_use_info',
        method: 'post',
        data
    })
}
// 保存应用在环境下的agent状态
export const saveAgentUserInfo = data => {
    return spider_axios.request({
        url: '/spider/pipeline/save_agent_use_info',
        method: 'post',
        data
    })
}
// 查询应用
export const getPublishAppList = params => {
    return spider_axios.request({
        url: '/spider/publish_mgt/get_publish_app_list/',
        params,
        method: 'get'
    })
}
// 查询环境
export const getAllTestingEnv = params => {
    return spider_axios.request({
        url: '/spider/test_env_mgt/get_all_testing_env',
        params,
        method: 'get'
    })
}
export const get_agent_list = params => {
    return spider_axios.request({
        url: '/spider/pipeline/get_agent_list',
        params,
        method: 'get'
    })
}
