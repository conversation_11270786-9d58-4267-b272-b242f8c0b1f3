<template>
    <div>
        <div style="margin-bottom: 15px">
            <h2>节点管理</h2>
        </div>
        <Card>
            <Col>
                <Select v-model="module_name_search" filterable clearable style="width:200px">
                    <Option v-for="item in module_item_list"
                            :value="item.module_name"
                            :label="item.module_name"
                            :key="item.module_name">
                        {{ item.module_name }}
                    </Option>
                </Select>

                <Input
                    placeholder="IP地址"
                    v-model="node_ip_text"
                    clearable
                    style="margin:5px; width:240px"
                />

                <Select v-model="node_status_search" filterable clearable style="width:200px">
                    <Option v-for="item in node_status_list"
                            :value="item.value"
                            :label="item.label"
                            :key="item.key">
                        {{ item.label }}
                    </Option>
                </Select>

                <Select v-model="suite_code_search" filterable clearable style="width:200px">
                    <Option v-for="item in suite_item_list"
                            :value="item.suite_code"
                            :label="item.suite_code"
                            :key="item.suite_code">
                        {{ item.suite_code }}
                    </Option>
                </Select>

                <Button @click="getTable(1)" type="info">查询</Button>

                <Button type="success" ghost @click="showcreatenoedbindModal()" align="right">新增</Button>

            </Col>

            <i-col>
                <Table :columns="node_column" :data="node_data"></Table>
            </i-col>
            <i-col>
                <Page style="margin: 5px;" :total="pageTotal" :current="pageNum" @page-size="pageSize"
                      @on-change="changePage" show-total></Page>
            </i-col>
        </Card>
        <Modal v-model="edit_modal" width="30em">
            <p slot="header">编辑回收单 </p>
            <div style="margin: 1em">
                <span align="right" style="font-size: 1rem; width: 5em; display:inline-block;">应用名</span>
                <Input
                    style="width: 14em; font-size: 1rem; padding-left: 1em; color: #2d8cf0;"
                    v-model="edit_module_name" disabled/>
            </div>
            <div style="margin: 1em">
                <span align="right" style="font-size: 1rem; width: 5em; display:inline-block;">主机名</span>
                <Input
                    style="width: 14em; font-size: 1rem; padding-left: 1em; color: #2d8cf0;"
                    v-model="edit_node_name" disabled/>
            </div>
            <div style="margin: 1em">
                <span align="right" style="font-size: 1rem; width: 5em; display:inline-block;">IP</span>
                <Input
                    style="width: 14em; font-size: 1rem; padding-left: 1em; color: #2d8cf0;"
                    v-model="edit_node_ip" disabled/>
            </div>
            <div style="margin: 1em">
                <span align="right" style="font-size: 1rem; width: 5em; display:inline-block;">回收时间</span>
                <DatePicker style="padding-left: 1em;" format="yyyy-MM-dd" :clearable="false" type="datetime"
                            v-model="edit_valid_at" placeholder="生效日期"></DatePicker>
            </div>
            <div style="margin: 1em">
                <span align="right" style="font-size: 1rem; width: 5em; display:inline-block;">回收原因</span>
                <Input
                    type="textarea"
                    style="width: 14em; font-size: 1rem; padding-left: 1em; color: #2d8cf0;"
                    v-model="edit_recycle_order_reason"/>
            </div>
            <div style="margin: 1em">
                <span align="right" style="font-size: 1rem; width: 5em; display:inline-block;">回收说明</span>
                <Input
                    type="textarea"
                    style="width: 14em; font-size: 1rem; padding-left: 1em; color: #2d8cf0;"
                    v-model="edit_recycle_order_desc"/>
            </div>
            <div slot="footer">
                <Button @click="order_cancel">关闭</Button>
                <Button type="primary" @click="order_confirm">提交</Button>
            </div>
        </Modal>

        <Modal
            v-model="edit_nodebind_modal" width="580px">
            <p slot="header" style="color:gray;">
                <Icon type="md-clipboard"></Icon>
                <span>编辑节点应用绑定关系</span>
            </p>
            <Form ref="form_node_bind" :rules="formRules" :model="form_node_bind" :label-width="80">
                <FormItem prop="module_name" label="应用名">
                    <Select v-model="form_node_bind.module_name" filterable clearable>
                        <Option v-for="item in module_item_list"
                                :value="item.module_name"
                                :label="item.module_name"
                                :key="item.module_name">
                            {{ item.module_name }}
                        </Option>
                    </Select>
                </FormItem>
                <FormItem prop="suite_code" label="环境套编码">
                    <Select v-model="form_node_bind.suite_code" filterable clearable placeholder="Select suite code">
                        <Option v-for="item in suite_item_list"
                                :value="item.suite_code"
                                :label="item.suite_code"
                                :key="item.suite_code">
                            {{ item.suite_code }}
                        </Option>
                    </Select>

                </FormItem>
                <FormItem prop="deploy_type" label="节点类型">
                    <RadioGroup v-model="form_node_bind.deploy_type"
                                style="width: 19em; font-size: 1rem; padding-left: 1.0em; color: #2d8cf0;margin-bottom：0em">
                        <Radio align="right" :label="1">
                            <span align="right">虚拟机</span>
                        </Radio>
                        <Radio :label="2">
                            <span align="left">容器</span>
                        </Radio>
                    </RadioGroup>
                </FormItem>

                <FormItem v-if="form_node_bind.deploy_type=='1'" prop="node_name_create" label="主机名">
                    <Input
                        v-model="form_node_bind.node_name" disabled/>
                </FormItem>
                <!-- <FormItem v-if="form_node_bind_create.createnodebind=='VM'" prop="node_ip_create" label="IP">
                        <Input
                                v-model="form_node_bind_create.node_ip_create"   />
                      </FormItem> -->

                <FormItem v-if="form_node_bind.deploy_type=='1'" prop="node_ip_create" label="IP">
                    <Select v-model="form_node_bind.node_ip" filterable clearable
                            @on-change="node_name_edit_select_change">
                        <Option v-for="item in node_list"
                                :value="item.node_ip"
                                :label="item.node_ip"
                                :key="item.node_ip">
                            {{ item.node_ip }}
                        </Option>
                    </Select>
                </FormItem>


                <!-- <FormItem v-if="form_node_bind.deploy_type=='1'" prop="node_name_create" label="主机名">
                        <Input
                                v-model="form_node_bind.node_name"  disabled/>
                      </FormItem>
                <FormItem v-if="form_node_bind.deploy_type=='1'" prop="node_ip_create" label="IP">
                        <Input
                                v-model="form_node_bind.node_ip"   disabled/>
                      </FormItem> -->

                <!-- <FormItem v-if="form_node_bind_create.createnodebind=='docker'" prop="node_name_create" label="容器名">
                        <Input
                                v-model="form_node_bind_create.node_name_create"  />
                      </FormItem> -->
                <FormItem v-if="form_node_bind.deploy_type=='2'" prop="node_docker_create" label="容器标签">
                    <Select v-model="form_node_bind.node_docker" filterable clearable>
                        <Option v-for="item in node_docker_list"
                                :value="item.container_code"
                                :label="item.container_code"
                                :key="item.container_code">
                            {{ item.container_code }}
                        </Option>
                    </Select>
                </FormItem>
                <FormItem v-if="form_node_bind.deploy_type=='1'" prop="node_port_edit" label="端口号">
                    <Input
                        v-model="form_node_bind.node_port_edit"/>
                </FormItem>
                <FormItem v-if="false" prop="node_minion_id" label="minion_id">
                    <Input

                        v-model="form_node_bind.node_minion_id"/>

                </FormItem>
            </Form>

            <div slot="footer">
                <Button @click="edit_cancel">关闭</Button>
                <Button type="primary" @click="order_edit_nodebind_confirm(form_node_bind)" :disabled="false">确定
                </Button>
            </div>
        </Modal>

        <Modal
            v-model="create_nodebind_modal" width="580px">
            <p slot="header" style="color:gray;">
                <Icon type="md-clipboard"></Icon>
                <span>新增节点应用绑定关系</span>
            </p>
            <Form ref="form_node_bind_create" :rules="formRulesCreate" :model="form_node_bind_create" :label-width="80">
                <FormItem prop="module_name_create" label="应用名">
                    <Select v-model="form_node_bind_create.module_name_create" filterable clearable>
                        <Option v-for="item in module_item_list"
                                :value="item.module_name"
                                :label="item.module_name"
                                :key="item.module_name">
                            {{ item.module_name }}
                        </Option>
                    </Select>
                </FormItem>

                <FormItem prop="suite_code_create" label="环境套编码">
                    <Select v-model="form_node_bind_create.suite_code_create" filterable clearable
                            placeholder="Select suite code">
                        <Option v-for="item in suite_item_list"
                                :value="item.suite_code"
                                :label="item.suite_code"
                                :key="item.suite_code">
                            {{ item.suite_code }}
                        </Option>
                    </Select>
                </FormItem>

                <FormItem prop="createnodebind" label="节点类型">
                    <RadioGroup v-model="form_node_bind_create.createnodebind"
                                style="width: 19em; font-size: 1rem; padding-left: 1.0em; color: #2d8cf0;margin-bottom：0em">
                        <Radio align="right" label="VM">
                            <span align="right">虚拟机</span>
                        </Radio>
                        <Radio label="docker">
                            <span align="left">容器</span>
                        </Radio>
                    </RadioGroup>
                </FormItem>
                <FormItem v-if="form_node_bind_create.createnodebind=='VM'" prop="node_name_create" label="主机名">
                    <Input
                        v-model="form_node_bind_create.node_name_create" disabled/>
                </FormItem>
                <!-- <FormItem v-if="form_node_bind_create.createnodebind=='VM'" prop="node_ip_create" label="IP">
                        <Input
                                v-model="form_node_bind_create.node_ip_create"   />
                      </FormItem> -->

                <FormItem v-if="form_node_bind_create.createnodebind=='VM'" prop="node_ip_create" label="IP">
                    <Select v-model="form_node_bind_create.node_ip_create" filterable clearable multiple
                            @on-change="node_name_select_change">
                        <Option v-for="item in node_list"
                                :value="item.node_ip"
                                :label="item.node_ip"
                                :key="item.node_ip">
                            {{ item.node_ip }}
                        </Option>
                    </Select>
                </FormItem>
                <!-- <FormItem v-if="form_node_bind_create.createnodebind=='docker'" prop="node_name_create" label="容器名">
                        <Input
                                v-model="form_node_bind_create.node_name_create"  />
                      </FormItem> -->
                <FormItem v-if="form_node_bind_create.createnodebind=='docker'" prop="node_docker_create"
                          label="容器标签">
                    <Select v-model="form_node_bind_create.node_docker_create" filterable clearable>
                        <Option v-for="item in node_docker_list"
                                :value="item.container_code"
                                :label="item.container_code"
                                :key="item.container_code">
                            {{ item.container_code }}
                        </Option>
                    </Select>
                </FormItem>
                <FormItem v-if="form_node_bind_create.createnodebind=='VM'" prop="node_port_create" label="端口号">
                    <Input
                        v-model="form_node_bind_create.node_port_create"/>
                </FormItem>
            </Form>
            <div slot="footer">
                <Button @click="order_create_nodebind_cancel">关闭</Button>
                <Button type="primary" @click="order_create_nodebind_confirm(form_node_bind_create)" :disabled="false">
                    确定
                </Button>
            </div>
        </Modal>
    </div>
</template>
<script>

import {
    getAppModule,
} from '@/spider-api/mgt-app'

import {
    getSuiteInfo,
} from '@/spider-api/mgt-env'

import {
    getDockerInfo,
    getNodelist
} from '@/spider-api/mgt-env'

import {
    getNodeInfo,
    createRecycleNodeApply,
    editNodeBind,
    getaccessstatus,
    createNodeBind,
} from '@/spider-api/mgt-node'

export default {
    data() {
        return {
            edit_modal: false,
            edit_nodebind_modal: false,
            create_nodebind_modal: false,
            form_order: {
                edit_module_name: '',
                node_name: '',
                node_ip: '',
                node_env_code: '',
                edit_valid_at: '',
                edit_recycle_order_reason: '',
                edit_recycle_order_desc: '',
            },
            form_node_bind: {
                module_name: '',
                node_name: '',
                node_ip: '',
                suite_code: '',
                node_minion_id: '',
                editnodebind: '',
                node_port_edit: '',
                bind_id: '',
                node_docker: '',
                deploy_type: '',
            },
            form_node_bind_create: {
                module_name_create: '',
                node_name_create: '',
                node_ip_create: [],
                suite_code_create: '',
                minion_id_create: '',
                createnodebind: '',
                node_port_create: '',
            },
            editnodebind: '',
            access_status: '',
            module_name: '',
            node_name: '',
            module_name_create: '',
            node_name_create: '',
            node_ip_create: [],
            node_port_edit: '',
            node_port_create: '',
            node_port: '',
            suite_code_create: '',
            minion_id_create: '',
            createnodebind: '1',
            suite_code: '',
            minion_id: '',
            node_ip: '',
            edit_node_minion_id: '',
            edit_module_name: '',
            edit_node_name: '',
            edit_node_ip: '',
            edit_node_env_code: '',
            edit_recycle_order_reason: '',
            edit_recycle_order_desc: '',
            edit_valid_at: '',
            module_name_search: '',
            module_item_list: [],
            node_ip_text: '',
            suite_code_search: '',
            suite_item_list: [],
            node_docker_list: [],
            node_status_search: '',
            node_status_list: [
                {key: 0, value: 0, name: 'USING', label: '使用中'},
                {key: 1, value: 1, name: 'TO_BE_RECYCLED', label: '待回收'},
                {key: 2, value: 2, name: 'RECYCLING', label: '回收中'},
                {key: 3, value: 3, name: 'RECYCLED', label: '已回收'},
            ],
            row: null,
            pageNum: 1,
            pageSize: 10,
            pageTotal: 0,
            formRules: {
                module_name: [{required: true, message: 'The module_name cannot be empty', trigger: 'blur'}],
                suite_code: [
                    {required: true, message: 'The suite_code cannot be empty', trigger: 'blur'}
                ],
                // editnodebind:[
                //   { required: true, message: '节点类型不能为空', trigger: 'change'}
                //   ]
            },
            formRulesCreate: {
                module_name_create: [
                    {required: true, message: 'The module_name cannot be empty', trigger: 'blur'}
                ],
                suite_code_create: [
                    {required: true, message: 'The suite_code cannot be empty', trigger: 'blur'}
                ],
                node_docker_create: [
                    {required: true, message: 'The container_code cannot be empty', trigger: 'blur'}
                ],
                // node_name_create: [
                //     { required: true, message: 'The node_name cannot be empty', trigger: 'blur' }
                //     ],
                // node_ip_create: [
                //   {required: true, message: 'The node_ip cannot be empty', trigger: 'change'}
                // ],
                createnodebind: [
                    {required: true, message: '节点类型不能为空', trigger: 'change'}
                ],
            },
            node_column: [
                {title: '应用名', key: 'module_name'},
                {
                    title: '节点IP/容器标签', width: 120, key: 'node_ip'
                },
                {
                    title: '节点名称', width: 120, key: 'node_name'
                },

                {
                    title: '节点状态',
                    key: 'node_status',
                    width: 90,
                    align: "center",
                    render: (h, params) => {
                        console.log(this.access_status)
                        switch (params.row.node_status) {
                            case 0:
                                return h('span', '使用中');
                                break;
                            case 1:
                                return h('span', '待回收');
                                break;
                            case 2:
                                return h('span', '回收中');
                                break;
                            case 3:
                                return h('span', '已回收');
                                break;
                        }
                        return h('span', params.row.node_status == 1 || params.row.container_is_active == 1 ? '可用' : '不可用')
                    }
                },
                {title: '环境套编码', width: 70, key: 'suite_code'},
                {title: '环境套名', width: 90, key: 'suite_name'},
                {title: '可用域', width: 80, key: 'region_name'},
                {title: 'minion_id', key: 'minion_id'},
                {title: '分组编码', width: 120, key: 'deploy_group_code'},
                {title: '分组名', width: 80, key: 'deploy_group_name'},
                {
                    title: '操作', key: 'recycle', width: 130,
                    align: 'center', render: (h, params) => {
                        return h('div', [h('Button', {
                            attrs: {
                                class: 'ivu-btn ivu-btn-primary ivu-btn-small'
                            }, props: {
                                disabled: this.access_status,
                                type: 'primary',
                                size: 'small',
                            }, style: {
                                marginRight: '6px',
                            }, on: {
                                click: () => {
                                    this.showEditModal(params)
                                    // this.rewireOrderRequest(params)
                                }
                            },
                        }, '回收'), h('Button', {
                            attrs: {
                                class: 'ivu-btn ivu-btn-primary ivu-btn-small'
                            }, props: {
                                disabled: this.access_status,
                                type: 'primary',
                                size: 'small',
                            }, style: {
                                marginRight: '6px',
                            }, on: {
                                click: () => {
                                    this.showEditnoedbindModal(params)
                                    // this.rewireOrderRequest(params)
                                }
                            },
                        }, '编辑'),
                        ]);
                    }
                },
            ],
            node_data: [],
        }
    },//data

    methods: {
        showEditModal(params) {
            this.edit_modal = true
            this.edit_recycle_order_reason = params.row.recycle_order_reason
            this.edit_recycle_order_desc = params.row.recycle_order_desc
            this.edit_module_name = params.row.module_name
            this.edit_node_name = params.row.node_name
            this.edit_node_ip = params.row.node_ip
            this.edit_valid_at = params.row.valid_at
        },
        showEditnoedbindModal(params) {
            this.row = params.row
            this.edit_nodebind_modal = true
            this.form_node_bind.node_name = params.row.node_name
            this.form_node_bind.node_ip = params.row.node_ip
            this.form_node_bind.module_name = params.row.module_name
            this.form_node_bind.suite_code = params.row.suite_code
            this.form_node_bind.node_port_edit = params.row.node_port
            this.form_node_bind.node_minion_id = params.row.minion_id
            this.form_node_bind.bind_id = params.row.bind_id
            this.form_node_bind.deploy_type = params.row.deploy_type
            this.form_node_bind.node_docker = params.row.node_docker
        },
        showcreatenoedbindModal(params) {
            this.create_nodebind_modal = true
            this.form_node_bind_create.node_name_create = ''
            this.form_node_bind_create.node_ip_create = ''
            this.form_node_bind_create.module_name_create = ''
            this.form_node_bind_create.suite_code_create = ''
            this.form_node_bind_create.minion_id_create = ''
            this.form_node_bind_create.node_port_create = ''
        },
        changePage(idx) {
            this.pageNum = idx;
            this.getTable(idx)
        },
        order_cancel() {
            this.edit_modal = false
        },
        order_confirm() {
            this.edit_modal = false
            this.form_order.node_ip = this.edit_node_ip
            this.form_order.node_name = this.edit_node_name
            this.form_order.module_name = this.edit_module_name
            this.form_order.valid_at = this.edit_valid_at
            this.form_order.recycle_order_desc = this.edit_recycle_order_desc
            this.form_order.recycle_order_reason = this.edit_recycle_order_reason
            createRecycleNodeApply(this.form_order).then(res => {
            })

        },
        init_node_name_select(node_ip_create) {
            this.node_name_select_change(node_ip_create)
        },
        node_name_select_change(node_ip_create) {
            console.log('node_ip_create')
            console.log(node_ip_create)
            if (node_ip_create != undefined && node_ip_create != null && node_ip_create != '') {
                this.form_node_bind_create.node_ip_create = node_ip_create
                if (this.node_list != null && this.node_list.length > 0) {
                    for (let i = 0; i < this.node_list.length; i++) {
                        let node_obj = this.node_list[i]
                        if (node_obj.node_ip == node_ip_create) {
                            this.form_node_bind_create.node_ip_create = node_obj.node_ip
                            this.form_node_bind_create.node_name_create = node_obj.node_name
                            break
                        }
                    }
                }
            }
        },
        node_name_edit_select_change(node_ip) {
            if (node_ip != undefined && node_ip != null && node_ip != '') {
                this.form_node_bind.node_ip = node_ip
                if (this.node_list != null && this.node_list.length > 0) {
                    for (let i = 0; i < this.node_list.length; i++) {
                        let node_obj = this.node_list[i]
                        if (node_obj.node_ip == node_ip) {
                            this.form_node_bind.node_ip = node_obj.node_ip
                            this.form_node_bind.node_name = node_obj.node_name
                            break
                        }
                    }
                }
            }
        },
        order_edit_nodebind_confirm(name) {


            // this.edit_nodebind_modal = false
            // this.form_node_bind.node_ip = this.edit_node_ip
            // this.form_node_bind.node_name = this.edit_node_name
            // this.form_node_bind.module_name = this.module_name
            // this.form_node_bind.node_minion_id = this.edit_node_minion_id
            // this.form_node_bind.suite_code = this.suite_code
            // this.form_node_bind.editnodebind = this.editnodebind
            // this.form_node_bind.node_port_edit = this.node_port_edit

            this.$refs['form_node_bind'].validate((valid) => {
                if (valid) {
                    // console.log(this.row)
                    console.log(this.form_node_bind)
                    this.edit_nodebind_modal = false
                    editNodeBind(this.form_node_bind).then(res => {
                        if (res.data["status"] == "success") {
                            this.$Message.success(res.data['msg'])
                            this.row.suite_code = this.form_node_bind.suite_code
                            this.row.module_name = this.form_node_bind.module_name
                            this.row.minion_id = this.form_node_bind.node_minion_id
                            this.row.node_port = this.form_node_bind.node_port_edit
                            this.row.node_ip = this.form_node_bind.node_ip
                            this.row.node_name = this.form_node_bind.node_name
                        } else if (res.data["status"] == "failed") {
                            alert(res.data['msg'])
                        } else {
                            this.$Message.error(res.data.msg)
                        }
                    })
                }
            })

        },
        edit_cancel() {
            this.edit_nodebind_modal = false
        },
        order_create_nodebind_confirm() {
            console.log('+++++++++++++++++++------------------')
            console.log(this.$refs['form_node_bind_create'])
            this.$refs.form_node_bind_create.validate((valid) => {
                if (valid) {
                    // console.log(this.form_node_bind_create)
                    this.create_nodebind_modal = false
                    createNodeBind(this.form_node_bind_create).then(res => {
                        if (res.data["status"] == "success") {
                            this.$Message.success(res.data['msg'])
                        } else if (res.data["status"] == "failed") {
                            alert(res.data['msg'])
                        } else {
                            this.$Message.error(res.data.msg)
                        }
                    })
                }
            })
        },
        create_cancel() {
            this.create_nodebind_modal = false
        },
        // this.create_nodebind_modal = false
        //   this.form_node_bind_create.node_ip = this.node_ip_create
        //   this.form_node_bind_create.node_name = this.node_name_create
        //   this.form_node_bind_create.module_name = this.module_name_create
        //   this.form_node_bind_create.createnodebind = this.createnodebind
        //   this.form_node_bind_create.suite_code = this.suite_code_create
        //   this.form_node_bind_create.node_port =this.node_port_create
        //   this.form_node_bind_create.minion_id=this.minion_id
        //   console.log(this.suite_code_create)
        //   if (this.suite_code_create){
        //     this.create_nodebind_modal = false
        //   }
        //   else{this.$Message.error('请输入环境套编码！');}
        //   createNodeBind(this.form_node_bind_create).then(res => {if (res.data["status"] == "success") {
        //       this.$Message.success(res.data['msg'])
        //     }
        //     else if (res.data["status"] == "failed") {
        //       alert(res.data['msg'])
        //       // this.$Message.error(res.data['msg'])
        //     }
        //     else {
        //       this.$Message.error(res.data.msg)
        //     }})
        // },
        order_create_nodebind_cancel() {
            this.create_nodebind_modal = false
        },
        order_edit_nodebind_cancel() {
            this.edit_nodebind_modal = false
        },

        getTable(idx) {

            let data = {
                "module_name": this.module_name_search,
                "node_ip": this.node_ip_text,
                "suite_code": this.suite_code_search,
                "node_status": this.node_status_search,
                "pageNum": idx,
                "pageSize": this.pageSize,
                "pageTotal": this.pageTotal,
                "access_status": this.access_status,
            };
            getaccessstatus(data).then(res => {
                this.access_status = res.data.data["access_status"]
                // console.log(this.access_status)
            });
            getNodeInfo(data).then(res => {
                let page = res.data.data["page"]
                this.pageNum = page["num"]
                this.pageSize = page["size"]
                this.pageTotal = page["total"]

                this.node_data = res.data.data["data_list"]
            })
        },
    },//methods
    mounted() {
        getAppModule().then(res => {
            this.module_item_list = res.data.data["data_list"]
        }),
            getSuiteInfo().then(res => {
                this.suite_item_list = res.data.data["data_list"]
            }),
            getDockerInfo().then(res => {
                this.node_docker_list = res.data.data["docker_dict_list"]
                this.node_list = res.data.data["node_dict_list"]
            })
    }//mounted
}//default
</script>

<style>

</style>
