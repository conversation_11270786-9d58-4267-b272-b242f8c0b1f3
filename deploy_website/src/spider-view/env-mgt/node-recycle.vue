<!-- /* eslint-disable */ -->
<template>
    <div>
        <div style="margin-bottom: 15px">
            <h2>节点回收</h2>
        </div>
        <Card>
            <i-col>
                <Input
                    placeholder="回收批次号"
                    v-model="recycle_batch_code"
                    clearable
                    style="margin:5px; width:240px"
                />

                <Button @click="getTable(1)" type="info">查询</Button>
                <Button type="success" ghost @click="addRecycle()" align="right">创建回收单</Button>
            </i-col>
            <i-col>
                <Table :columns="recycle_column" :data="recycle_data"></Table>
            </i-col>
            <i-col>
                <Page
                    style="margin: 5px;"
                    :total="pageTotal"
                    :current="pageNum"
                    @page-size="pageSize"
                    @on-change="changePage"
                    show-total
                ></Page>
            </i-col>
        </Card>
        <Drawer :title="drawertittle" v-model="drawerShow" :width="drawerWidth" :mask-closable="false">
            <Form ref="recycleOrderCreateForm" :model="recycleOrderCreateForm" :rules="createFormValidation">
                <Row :gutter="32">
                    <Col span="24">
                        <FormItem label="服务器" label-position="top" prop="node_list">
                            <Select
                                v-model="recycleOrderCreateForm.node_list"
                                :filterable="true"
                                :clearable="true"
                                multiple
                                placeholder="选择要回收的服务器"
                            >
                                <Option
                                    v-for="(item, index) in recycleServer"
                                    :key="item.node_name"
                                    :value="JSON.stringify(item)"
                                >
                                    <span>{{ item.node_name + '(' + item.node_ip + ')' }}</span>
                                </Option>
                            </Select>
                        </FormItem>
                    </Col>
                </Row>
                <Row :gutter="32">
                    <Col span="24">
                        <FormItem label="回收开始时间" label-position="top" prop="recycle_start_time">
                            <DatePicker
                                v-model="recycleOrderCreateForm.recycle_start_time"
                                type="datetime"
                                placeholder="请选择回收开始时间"
                                style="width:100%"
                            ></DatePicker>
                        </FormItem>
                    </Col>
                </Row>
                <div class="drawer__footer">
                    <Button style="margin-right: 8px" @click="cancelDrawer">取消</Button>
                    <!-- <Button style="margin-right: 8px" type="warning" @click="resetForm('recycleOrderCreateForm')">重置</Button> -->
                    <Button
                        type="primary"
                        :loading="recycleBtnLoading"
                        @click="createRecycleSpiderOrder('recycleOrderCreateForm')"
                        >提交</Button
                    >
                </div>
            </Form>
        </Drawer>

        <Modal v-model="reject_modal" :title="reject_title" @on-ok="reject_order" @on-cancel="cancel_reject">
            <Form :label-width="100">
                <FormItem prop="recycle_reject_reason" label="驳回原因说明">
                    <Input
                        type="textarea"
                        placeholder="请输入驳回原因"
                        v-model="recycle_reject_reason"
                        style="width: 385px; padding-left: 1em; "
                    />
                </FormItem>
            </Form>
        </Modal>
        <Modal
            v-model="verify_modal"
            title="节点回收提示"
            @on-ok="verify_reject_order"
            @on-cancel="verify_cancel_reject"
        >
            <p style="color: red;font-size: large;">点击确定后，节点会在一分钟之后关机，请知悉！</p>
        </Modal>
    </div>
</template>
<script>
import { getAppModule } from '@/spider-api/mgt-app'

import { getRegionInfo, getSuiteList } from '@/spider-api/mgt-env'

import {
    getNodeApply,
    getNodeZone,
    getNodeVm,
    getZoneVm,
    createNodeApply,
    getRecycleOrderApi,
    getRecycleNodeInfo,
    saveNodeRecycleOrder,
    GetNodeRecycleOrderList,
    NodeRecycle,
    CancelNodeRecycleBeforeApprove,
    RejectNodeRecycleByAppOps,
    CancelNodeRecycleAfterApprove
} from '@/spider-api/mgt-node'
import commonIconVue from '../../components/common-icon/common-icon.vue'

export default {
    data() {
        return {
            verify_modal: false,
            reject_modal: false,
            drawerShow: false,
            recycleBtnLoading: false,
            recycleServer: '',
            drawertittle: '创建主机回收',
            drawerWidth: 460,
            recycle_code_text: '',
            recycle_order_code: '',
            recycle_order_user: '',
            module_name: '',
            node_name: '',
            node_ip: '',
            create_user: '',
            order_status: '',
            recycle_order_status: '',
            operate_status: '',
            pageNum: 1,
            pageSize: 10,
            pageTotal: 0,
            recycle_batch_code: '',
            verify_permission_disable: '',
            cancel_disabal: '',
            recycle_reject_reason: '',
            reject_title: '',
            reject_recycle_batch_code: '',
            verify_recycle_batch_code: '',
            // recycleOrderTableData: [],
            // recycleOrderTableColumn: [],
            // recycleOrderTableColumnCheck: ['code','updated_at','created_at','recycled_at','apply_user','order_status'],
            /* eslint-disable */
            recycle_column: [
                {
                    type: 'expand',
                    width: 60,
                    render: (h, params) => {
                        return h('Table', {
                            //  console.log(params.row)
                            props: {
                                columns: this.subtableColumn,
                                data: params.row.recycle_order_list,
                                border: true,
                                stripe: true
                            },
                            style: {
                                padding: '0px'
                            }
                        })
                    }
                },
                { title: '订单批次号', key: 'recycle_batch_code', width: 200 },
                { title: '创建人', key: 'recycle_batch_user', width: 200 },
                { title: '审核时间', key: 'update_time' },
                { title: '创建时间', key: 'create_time' },
                //{title: '数量', key: 'vm_count', },
                {
                    title: '批次单状态',
                    key: 'recycle_batch_status',

                    align: 'center',
                    render: (h, params) => {
                        switch (params.row.recycle_batch_status) {
                            case 0:
                                return h('span', '已取消')
                            case 1:
                                return h('span', 'app_ops驳回')
                            case 2:
                                return h('span', '待审核')
                            case 3:
                                return h('span', 'app-ops审核通过')
                            case 4:
                                return h('span', '已关机待回收')
                            case 10:
                                return h('span', '部分取消')
                            case 11:
                                return h('span', '全部取消')
                            case 5:
                                return h(
                                    'span',
                                    {
                                        style: {
                                            color: '#187'
                                        }
                                    },
                                    '已回收'
                                )
                        }
                    }
                },
                {
                    title: '操作',
                    key: 'handle',

                    align: 'center',
                    render: (h, params) => {
                        if (params.row.recycle_batch_status) {
                            this.verify_permission_disable = true
                        }
                        return h('div', [
                            h(
                                'Button',
                                {
                                    attrs: {
                                        class: 'ivu-btn ivu-btn-primary ivu-btn-small'
                                    }, //button-查看-attrs
                                    props: {
                                        type: 'primary',
                                        size: 'small',
                                        disabled: params.row.verify_permission
                                    }, //button-查看-props
                                    style: {
                                        marginRight: '6px'
                                    }, //button-查看-style
                                    on: {
                                        click: () => {
                                            this.reject_modal = true
                                            this.reject_recycle_batch_code = params.row.recycle_batch_code
                                            this.reject_title = '批量回收单:' + this.reject_recycle_batch_code
                                        }
                                    } //button-查看-on
                                },
                                '驳回'
                            ),
                            h(
                                'Button',
                                {
                                    attrs: {
                                        class: 'ivu-btn ivu-btn-primary ivu-btn-small'
                                    }, //button-查看-attrs
                                    props: {
                                        type: 'primary',
                                        size: 'small',
                                        disabled: params.row.verify_permission
                                    }, //button-查看-props
                                    style: {
                                        marginRight: '6px'
                                    }, //button-查看-style
                                    on: {
                                        click: () => {
                                            this.verify_modal = true
                                            this.verify_recycle_batch_code = params.row.recycle_batch_code
                                        }
                                    } //button-查看-on
                                },
                                '审核通过'
                            ) //button-查看
                        ]) //return
                    } //render
                }
            ],
            recycle_data: [],
            recycleOrderCreateForm: {
                node_list: [],
                recycle_start_time: ''
            },
            createFormValidation: {
                node_list: [{ type: 'array', required: true, message: '选择要回收的服务器', trigger: 'change' }]
                // recycle_start_time: [
                //     {type: 'date',required: true,message: '选择回收开始的时间',trigger: 'change'}
                // ]
            },
            subtableColumn: [
                {
                    title: '回收订单号',
                    key: 'recycle_order_code',
                    sortable: true,
                    tooltip: true
                },
                {
                    title: '服务器名称',
                    key: 'recycle_node_name',
                    sortable: true,
                    tooltip: true
                },
                {
                    title: 'IP',
                    key: 'recycle_node_ip',
                    sortable: true,
                    tooltip: true
                },
                {
                    title: '订单状态',
                    width: 150,
                    align: 'center',
                    render: (h, params) => {
                        switch (params.row.recycle_order_status) {
                            case 0:
                                return h('span', '已取消')
                            case -1:
                                return h('span', '申请失败')
                            case 1:
                                return h('span', 'app_ops驳回')
                            case 2:
                                return h('span', '待审核')
                            case 3:
                                return h('span', 'app-ops审核通过')
                            case 4:
                                return h('span', '已关机待回收')
                            case 5:
                                return h(
                                    'span',
                                    {
                                        style: {
                                            color: '#187'
                                        }
                                    },
                                    '已回收'
                                )
                        }
                    }
                },
                {
                    title: '操作',
                    key: 'handle',

                    align: 'center',
                    render: (h, params) => {
                        console.log('33333333333333333333')
                        console.log(params.row)
                        if (params.row.cancel_permission == false) {
                            return h('div', [
                                h(
                                    'Button',
                                    {
                                        attrs: {
                                            class: 'ivu-btn ivu-btn-primary ivu-btn-small'
                                        }, //button-查看-attrs
                                        props: {
                                            type: 'primary',
                                            size: 'small',
                                            disabled: params.row.cancel_disabel,
                                            loading: params.row.cancel_loading
                                        }, //button-查看-props
                                        style: {
                                            marginRight: '6px'
                                        }, //button-查看-style
                                        on: {
                                            click: () => {
                                                params.row.cancel_loading = true
                                                CancelNodeRecycleAfterApprove(params.row.recycle_order_code).then(
                                                    res => {
                                                        if (res.data.status === 'failed') {
                                                            alert(res.data.msg)
                                                        }
                                                        params.row.cancel_loading = false
                                                    }
                                                )
                                            }
                                        } //button-查看-on
                                    },
                                    '审核后取消'
                                )
                            ])
                        } else {
                            return h('div', [
                                h(
                                    'Button',
                                    {
                                        attrs: {
                                            class: 'ivu-btn ivu-btn-primary ivu-btn-small'
                                        }, //button-查看-attrs
                                        props: {
                                            type: 'primary',
                                            size: 'small',
                                            disabled: params.row.cancel_disabel,
                                            loading: params.row.cancel_loading
                                        }, //button-查看-props
                                        style: {
                                            marginRight: '6px'
                                        }, //button-查看-style
                                        on: {
                                            click: () => {
                                                params.row.cancel_loading = true
                                                CancelNodeRecycleBeforeApprove(params.row.recycle_batch_code).then(
                                                    res => {
                                                        if (res.data.status === 'failed') {
                                                            alert(res.data.msg)
                                                        }
                                                        params.row.cancel_loading = false
                                                    }
                                                )
                                            }
                                        } //button-查看-on
                                    },
                                    '审核前取消'
                                )
                            ])
                        }
                        //return
                    } //render
                }
            ]
        }
    }, //data

    methods: {
        verify_cancel_reject() {
            this.verify_modal = false
        },
        verify_reject_order() {
            this.verify_modal = false
            NodeRecycle(this.verify_recycle_batch_code).then(res => {
                if (res.data.status === 'failed') {
                    alert(res.data.msg)
                }
            })
        },
        cancel_reject() {
            this.reject_modal = false
        },
        reject_order() {
            RejectNodeRecycleByAppOps(this.reject_recycle_batch_code, this.recycle_reject_reason).then(res => {
                if (res.data.status === 'failed') {
                    alert(res.data.msg)
                }
            })
        },
        addRecycle() {
            this.drawerShow = true
            this.modal = true
        },
        resetForm() {},

        createRecycleSpiderOrder() {
            this.drawerShow = false
            saveNodeRecycleOrder(this.recycleOrderCreateForm).then(res => {
                if (res.data.status === 'failed') {
                    alert(res.data.msg)
                }
            })
        },
        cancelDrawer() {
            this.drawerShow = false
        },
        changePage(idx) {
            this.pageNum = idx
            this.getTable(idx)
        },

        getTable(idx) {
            let data = {
                recycle_batch_code: this.recycle_batch_code,
                pageNum: idx,
                pageSize: this.pageSize,
                pageTotal: this.pageTotal
            }
            GetNodeRecycleOrderList(data).then(res => {
                var page = res.data.data['page']
                if (page != null) {
                    this.pageNum = page['num']
                    this.pageSize = page['size']
                    this.pageTotal = page['total']
                }
                this.recycle_data = res.data.data['node_recycle_order_list']
                for (let item in this.recycle_data) {
                    for (let i in this.recycle_data[item]['recycle_order_list']) {
                        this.recycle_data[item]['recycle_order_list'][i]['cancel_loading'] = false
                    }
                }
                console.log('qqqqqqqqqqqqqqqqqqqqq')
                console.log(typeof this.recycle_data)
                console.log(this.recycle_data)
            })
        }
    }, //methods

    mounted() {
        getRecycleNodeInfo().then(res => {
            console.log('----------------------------')
            console.log(res.data.data)
            this.recycleServer = res.data.data
        })
    } //mounted
} //default
</script>

<style>
/*对table自定义的operation做样式设置*/
td.ivu-table-expanded-cell {
    padding: 0px;
    padding-right: 0px;
}
/*对expand的table做样式设置*/
</style>
