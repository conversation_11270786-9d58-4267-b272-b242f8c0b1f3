<template>
  <div>
    <Modal
        v-model="modal_interface_edit">
        <p slot="header" style="color:gray;">
          <Icon type="md-clipboard"></Icon>
          <span>接口参数编辑</span>
        </p>
        <Form>
          <FormItem prop="apply_reason" label="参数编辑">
            <Input
              type="textarea"
              placeholder="例如[{'filedName':'**','fieldType':'**','required':'**','enumVaules':'**'},{'filedName':'**','fieldType':'**','required':'**','enumVaules':'**'}]"
              v-model="edit_params"
              autosize style="width: 100%; padding-left: 1em;height:100% "/>
          </FormItem>
        </Form>
        <div slot="footer">
          <Button @click="edit_cancel">关闭</Button>
          <Button type="primary" @click="edit_confirm">保存</Button>
        </div>
      </Modal>
  </div>

</template>

<script>
//   import maintenanceCmd  from "./maintenance-cmd";
  import {
    getPublishAppBindApi, getPublishAppBindApiByRegionGroupList,
    getPublishStatusApi,getInterfaceParams, updateOrCreateInterfaceParams
  } from '@/spider-api/prod-publish/publish-info'
import store from '../../../store';
  export default {
    name: 'editArguments',
    components: {
    //   maintenanceCmd,
    },
    props: {
      // modal_interface_edit:{
      //   type:Boolean,
      //   defaults:'false'
      // }
    },
        data() {
      return {
        modal_interface_edit:false,
        edit_params:'',
        app_name:'',
        git_version:'',
        last_deploy:'',
        app_data:[],
        data: [

],
params_data:{},
      }},
      methods: {
        edit_cancel(){
          this.modal_interface_edit = false
        },
        edit_confirm(){
          this.params_data['param_list'] = JSON.parse(this.edit_params)
          console.log(this.params_data)
          updateOrCreateInterfaceParams(this.params_data).then(res => {
          console.log(res.data.data)
          })
          this.modal_interface_edit = false
        },
        getInterfaceParamsInfo(app_name,branch_name,interface_path,interface_method,interface_type){
          getInterfaceParams(app_name,branch_name,interface_path,interface_method,interface_type).then(res => {
            this.edit_params = JSON.stringify(res.data.data.params_list)  
          })
        },
    
      },

  }

</script>
