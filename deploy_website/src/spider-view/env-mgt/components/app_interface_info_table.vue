<template>
  <div>
    <!-- <i-col v-if="app_name" style="margin-top: 1em; width:552px;"> -->
      <Table :columns="table_columns" :data="app_interface_list" width="840" border></Table>
    <!-- </i-col> -->
    <i-col>
        <Page style="margin: 5px;" :total="pageTotal" :current="pageNum" @page-size="pageSize"
              @on-change="changePage" show-total></Page>
      </i-col>
    <editArguments ref = 'edit_arg'></editArguments>
    <editInterfaceName ref = 'edit_interface_name'></editInterfaceName>
    <!-- <maintenanceCmd ref="maintenance_cmd_data"></maintenanceCmd> -->
  </div>

</template>

<script>
//   import maintenanceCmd  from "./maintenance-cmd";
  import {
    getPublishAppBindApi, getPublishAppBindApiByRegionGroupList,
    getPublishStatusApi, getAppInterface<PERSON><PERSON>, getInterfaceParams,
  } from '@/spider-api/prod-publish/publish-info'
  import editArguments from './edit_arguments'
  import editInterfaceName from './edit_interface_name'
  export default {
    name: 'appInterfaceInfoTable',
    components: {
    //   maintenanceCmd,
    editArguments,
    editInterfaceName
    },
    props: {

    },
        data() {
      return {
        app_name:'',
        git_version:'',
        last_deploy:'',
        app_data:[],
        app_interface_info:[],
        app_interface_list:[],
        pageTotal:0,
        pageNum:1,
        pageSize:10,
        data: [

],
        table_columns: [
          {
            title: '应用',
            key: 'app_name',
            width: 130,
            tooltip: true,
            // fixed: 'left'
          },
          {
            title: '接口名-开发',
            width: 150,
            key: 'interface_name_dev',
            tooltip: true
          },
          {
            title: '接口名-测试',
            width: 150,
            key: 'interface_name',
            tooltip: true
          },
          {
            title: '接口路径',
            width: 200,
            key: 'interface_path',
            tooltip: false
          },
          {
            title: '接口参数',
            width: 250,
            // fixed: 'right',
            render: (h, params) => {
              return h('div', [
                h('Button', {
                  attrs: {
                    title: '参数维护',
                    type : 'dashed'
                  },

                  style: {
                    color: 'black',
                    "font-size": "12px",
                  },
                  on: {
                    click: () => {
                      console.log(this.$store.state.user.userName)
                      this.$refs.edit_arg.getInterfaceParamsInfo(params.row.app_name,params.row.branch_name,params.row.interface_path,
                      params.row.interface_method,params.row.interface_type)
                      this.$refs.edit_arg.modal_interface_edit = true
                      if (this.$store.state.user.userName){
                        this.$refs.edit_arg.params_data = {
                      "app_name": params.row.app_name,
                      "branch_name": params.row.branch_name,
                      "interface_path": params.row.interface_path,
                      "interface_method": params.row.interface_method,
                      "interface_type": params.row.interface_type,
                      "user": this.$store.state.user.userName,
                      
                  }
                  this.$refs.edit_arg.modal_interface_edit = true
                      }
                      else{
                        alert('请先登录再编辑')
                      }
                    }
                  },
                }, '接口参数编辑'),
                h('Button', {
                  attrs: {
                    title: '接口名维护',
                    type : 'dashed'
                  },

                  style: {
                    color: 'black',
                    "font-size": "12px",
                  },
                  on: {
                    click: () => {
                      console.log(this.$store.state.user.userName)
                      if (this.$store.state.user.userName){
                        this.$refs.edit_interface_name.params_data = {
                      "app_name": params.row.app_name,
                      "branch_name": params.row.branch_name,
                      "interface_path": params.row.interface_path,
                      "interface_method": params.row.interface_method,
                      "interface_type": params.row.interface_type,
                      "user": this.$store.state.user.userName, 
                  }
                  this.$refs.edit_interface_name.modal_interface_name_edit = true
                      }
                      else{
                        alert('请先登录再编辑')
                      }  
                    }
                  },
                }, '接口名编辑'),

              ]);
            }
          },
        ],
      }},
      methods: {
        getAppInterfaceinfo(app_name,branch_name,interface_path,interface_method){
          console.log(this.pageTotal)
          getAppInterfaceApi(app_name,branch_name,interface_path,interface_method).then(res => {
          this.app_interface_info = res.data.data
          this.pageTotal = this.app_interface_info.length
          if(this.pageTotal <= 10){
            this.app_interface_list = this.app_interface_info
          }
          else{
            this.app_interface_list = this.app_interface_info.slice((this.pageNum-1)*10,(this.pageNum-1)*10 + 10)
          }
          // this.app_interface_list = this.app_interface_info.slice((this.pageNum-1)*10,(this.pageNum-1)*10 + 10)
          console.log(this.pageTotal)
          console.log(this.pageNum)
          console.log(this.app_interface_list)
          console.log(this.app_interface_info)
        })
        },
        changePage(idx){
          this.app_interface_list = []
          this.pageNum = idx
          this.app_interface_list = this.app_interface_info.slice((this.pageNum-1)*10,(this.pageNum-1)*10 + 10)
        },
        
      },

  }

</script>
