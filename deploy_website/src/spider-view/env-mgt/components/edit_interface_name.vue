<template>
  <div>
    <Modal
        v-model="modal_interface_name_edit">
        <p slot="header" style="color:gray;">
          <Icon type="md-clipboard"></Icon>
          <span>接口名编辑</span>
        </p>
        <Form>
          <FormItem prop="edit_interface_name" label="接口名编辑">
            <RadioGroup v-model="edit_type">
            <Radio label="test"></Radio>
            <Radio label="dev"></Radio>
        </RadioGroup>
            <Input
              type="text"
              placeholder="编辑接口名"
              v-model="edit_interface_name_params"
              autosize style="width: 100%; padding-left: 1em;height:100% "/>
          </FormItem>
        </Form>
        <div slot="footer">
          <Button @click="edit_cancel">关闭</Button>
          <Button type="primary" @click="edit_confirm">保存</Button>
        </div>
      </Modal>
  </div>

</template>

<script>
//   import maintenanceCmd  from "./maintenance-cmd";
  import {
    getPublishAppBind<PERSON><PERSON>, getPublishAppBindApiByRegionGroupList,
    getPublishStatusApi,getInterfaceParams, updateInterfaceName
  } from '@/spider-api/prod-publish/publish-info'
import store from '../../../store';
  export default {
    name: 'editInterfaceName',
    components: {
    //   maintenanceCmd,
    },
    props: {
      // modal_interface_edit:{
      //   type:Boolean,
      //   defaults:'false'
      // }
    },
        data() {
      return {
        modal_interface_name_edit:false,
        edit_interface_name_params:'',
        edit_type:'',
        params_data:{},
      }},
      methods: {
        edit_cancel(){
          this.modal_interface_name_edit = false
        },
        edit_confirm(){
          console.log(this.params_data)
          this.params_data['edit_type'] = this.edit_type
          this.params_data['edit_interface_name_params'] = this.edit_interface_name_params
          console.log(this.params_data)
          this.modal_interface_name_edit = false
          updateInterfaceName(this.params_data).then(res => {
          if(res.data.status == 'failed'){
            alert('请编辑接口类型')
          }
          })
        },
        // getInterfaceParamsInfo(app_name,branch_name,interface_path,interface_method,interface_type){
        //   getInterfaceParams(app_name,branch_name,interface_path,interface_method,interface_type).then(res => {
        //     this.edit_params = JSON.stringify(res.data.data.params_list)  
        //   })
        // },
    
      },

  }

</script>
