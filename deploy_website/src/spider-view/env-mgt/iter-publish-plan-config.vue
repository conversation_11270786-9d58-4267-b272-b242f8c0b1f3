<template>
    <Card style="height: auto;min-height: 100%;overflow-y: hidden;width: 100%">
        <div style="width: 400px;">
            <Form
                ref="formDynamic"
                :model="formDynamic"
                :rules="ruleValidate"
                :label-width="80"
                style="width: 400px;margin-top: 20px;"
            >
                <FormItem label="应用名" prop="module_name">
                    <Select
                        size="small"
                        v-model="formDynamic.module_name"
                        placeholder="选择应用"
                        filterable
                        @on-change="changeModuleName"
                    >
                        <Option v-for="item in app_name_list" :value="item" :label="item" :key="item">
                            {{ item }}
                        </Option>
                    </Select>
                    <div v-if="!formDynamic.module_name" class="error-text">请选择应用再编辑配置项</div>
                </FormItem>
            </Form>
        </div>

        <div style="width: 400px;">
            <Form
                ref="formDynamic"
                :model="formDynamic"
                :rules="ruleValidate"
                :label-width="80"
                style="width: 400px;margin-top: 20px;"
            >
                <FormItem label="计划阶段" prop="phase">
                    <Select
                        size="small"
                        v-model="formDynamic.currentPhase"
                        placeholder="选择计划阶段"
                        filterable
                        @on-change="changePhase"
                    >
                        <Option v-for="item in formDynamic.phase_list" :value="item" :label="item" :key="item">
                            {{ item }}
                        </Option>
                    </Select>
                </FormItem>
            </Form>
        </div>
        <div style="width: 400px;">
            <Form
                ref="formDynamic"
                :model="formDynamic"
                :rules="ruleValidate"
                :label-width="80"
                style="width: 400px;margin-top: 20px;"
            >
                <FormItem label="计划节点" prop="phase">
                    <Select
                        size="small"
                        v-model="formDynamic.currentNode"
                        placeholder="选择计划节点"
                        filterable
                        @on-change="changeNodes"
                    >
                        <Option v-for="item in formDynamic.nodes" :value="item" :label="item" :key="item">
                            {{ item }}
                        </Option>
                    </Select>
                </FormItem>
            </Form>
        </div>
        <Divider>配置项</Divider>
        <div style="display: grid; grid-template-columns: repeat(2, 1fr); grid-gap: 10px;">
            <div v-for="param in formDynamic.current_params" style="width: 400px;">
                <div v-if="param.param_ext.param_show_flag">
                    <div v-if="param.param_ext.param_show_type === 'text'" class="plan-node-card-param">
                        <Tooltip :content="param.param_ext.param_desc">
                            <div class="plan-node-card-param-desc">{{ param.param_cn_name }}:</div>
                        </Tooltip>
                        <Input size="small" style="width:200px;" type="text" v-model="param.param_ext.param_value" />
                        <Button
                            type="primary"
                            size="small"
                            :disabled="!param.param_ext.param_value"
                            @click="saveParam(param)"
                            style="margin-left: 20px;"
                            >保存
                        </Button>
                    </div>
                    <div v-if="param.param_ext.param_show_type === 'time_select'" class="plan-node-card-param">
                        <Tooltip :content="param.param_ext.param_desc">
                            <div class="plan-node-card-param-desc">{{ param.param_cn_name }}:</div>
                        </Tooltip>
                        <DatePicker
                            type="datetime"
                            size="small"
                            v-model="param.param_ext.param_value"
                            format="yyyy-MM-dd HH:mm"
                            placeholder="Select date and time"
                            style="width: 200px"
                            @on-change="change_time(param, node)"
                            @on-clear="clear_time(param, node)"
                        ></DatePicker>
                        <div v-if="!param.param_ext.param_value" class="plan-node-card-param-desc">必填</div>
                        <Button
                            type="primary"
                            size="small"
                            :disabled="!param.param_ext.param_value"
                            @click="saveParam(param)"
                            style="margin-left: 20px;"
                            >保存
                        </Button>
                    </div>
                    <div v-else-if="param.param_ext.param_show_type === 'select'" class="plan-node-card-param">
                        <Tooltip :content="param.param_ext.param_desc">
                            <div class="plan-node-card-param-desc">{{ param.param_cn_name }}:</div>
                        </Tooltip>
                        <Select
                            filterable
                            size="small"
                            clearable
                            v-model="param.param_ext.param_value"
                            style="width:200px; "
                            @on-change="
                                bizBrNameChangeSelect(param.param_ext.param_value, param.param_ext.param_options_type)
                            "
                        >
                            <Option
                                v-if="param.param_ext.param_options_type === 'env'"
                                v-for="option in env_options"
                                :value="option.value"
                                >{{ option.name }}
                            </Option>
                            <Option
                                v-if="param.param_ext.param_options_type === 'user'"
                                v-for="option in user_options"
                                :value="option.value"
                                >{{ option.name }}
                            </Option>
                            <Option
                                v-if="param.param_ext.param_options_type === 'biz_iter_branch'"
                                v-for="option in biz_iter_branch_options"
                                :value="option.value"
                                >{{ option.name }}
                            </Option>
                            <Option
                                v-if="param.param_ext.param_options_type === 'biz_flow_name'"
                                v-for="option in biz_flow_name_options"
                                :value="option.value"
                                >{{ option.name }}
                            </Option>
                            <Option
                                v-if="!param.param_ext.param_options_type"
                                v-for="option in param.param_options"
                                :value="option.value"
                                >{{ option.name }}
                            </Option>
                        </Select>
                        <div v-if="!param.param_ext.param_value" class="plan-node-card-param-desc error-text">必填</div>
                        <Button
                            type="primary"
                            size="small"
                            :disabled="!param.param_ext.param_value"
                            @click="saveParam(param)"
                            style="margin-left:20px;"
                            >保存
                        </Button>
                    </div>
                    <div
                        v-else-if="
                            param.param_ext.param_show_type === 'draggable_order_list' &&
                                param.param_ext.draggable_order_list_type === 'prod-suites'
                        "
                        class="plan-node-card-param"
                    >
                        <Tooltip :content="param.param_ext.param_desc">
                            <div class="plan-node-card-param-desc">{{ param.param_cn_name }}:</div>
                        </Tooltip>
                        <draggable
                            style="width: 1000px; display: flex;justify-content: center;align-items: center;flex-direction: column;"
                            v-model="param.param_ext.param_value"
                        >
                            <div
                                v-for="option in param.param_ext.param_value"
                                style=" cursor: pointer;margin: 8px -800px 8px 0;border: 1px solid #ccc;border-radius: 8px; width: 1000px;justify-content: left;align-items: center;display: flex;"
                            >
                                <div
                                    style=" cursor: pointer;margin: 4px 4px 4px 4px;border: 1px solid #ccc;border-radius: 4px; width: 200px;justify-content: center;align-items: center;display: flex;"
                                >
                                    {{ option.name.suite_name }}
                                </div>
                                <div>
                                    {{ option.name.node_list }}
                                </div>
                            </div>
                        </draggable>
                        <Button
                            type="primary"
                            size="small"
                            :disabled="!param.param_ext.param_value"
                            @click="saveParam(param)"
                            style="margin-left: 820px; "
                            >保存
                        </Button>
                    </div>
                </div>
            </div>
        </div>
    </Card>
</template>
<script>
import draggable from 'vuedraggable'
import {
    get_publish_plan_node_default_param,
    getEmailAddresses,
    update_publish_plan_node_default_param,
    getFlowList,
    getBusinessBranch
} from '@/spider-api/iter-plan'
import { getAppModule } from '@/spider-api/mgt-app'
import { getTestSuiteCode } from '@/api/data'
import { getAppProdSuites } from '@/spider-api/mgt-env'
import { get_test_flow_name_list, get_test_iter_list } from '@/spider-api/biz-mgt'

export default {
    name: 'IterPublishPlanConfig',
    components: { draggable },
    data() {
        return {
            env_options: [],
            user_options: [],
            biz_iter_branch_options: [],
            biz_flow_name_options: [],
            app_name_list: [],
            formDynamic: {
                param_dict: {},
                module_name: '',
                currentPhase: '',
                currentNode: '',
                phase_list: [],
                params: [],
                nodes: [],
                current_params: {}
            },
            ruleValidate: {
                module_name: [{ required: true, message: '请选择应用', trigger: 'blur' }],
                currentPhase: [{ required: true, message: '请选择发布计划阶段', trigger: 'blur' }]
            }
        }
    },
    props: {},
    methods: {
        changePhase(phase) {
            this.formDynamic.nodes = Object.keys(this.formDynamic.param_dict[this.formDynamic.currentPhase])
            this.formDynamic.currentNode = this.formDynamic.nodes[0]
            this.formDynamic.current_params = this.formDynamic.param_dict[this.formDynamic.currentPhase][
                this.formDynamic.currentNode
            ]
        },
        changeNodes(node_name) {
            this.formDynamic.current_params = this.formDynamic.param_dict[this.formDynamic.currentPhase][
                this.formDynamic.currentNode
            ]
        },
        change_time(param, node) {},
        saveParam(param) {
            update_publish_plan_node_default_param(param)
                .then(res => {
                    if (res.data.status === 'success') {
                        this.$Message.success('保存成功')
                    } else {
                        this.$Notice.error({
                            title: 'Error',
                            desc: res.data.message,
                            duration: 0
                        })
                    }
                })
                .catch(err => {
                    this.$Notice.error({
                        title: 'Error',
                        desc: err.message,
                        duration: 0
                    })
                })
        },
        getParams(module_name) {
            let _this = this
            get_publish_plan_node_default_param({ module_name: module_name })
                .then(res => {
                    if (res.data.status === 'success') {
                        this.$Message.success(`查询${module_name}发布配置成功`)
                        this.formDynamic.params = res.data.data
                        getAppProdSuites(module_name).then(res2 => {
                            if (res2.data.status === 'success') {
                                _this.app_prod_suites = res2.data.data.map(item => {
                                    return {
                                        env: item.suite_code,
                                        name: { suite_name: item.suite_name, node_list: item.node_ip_list }
                                    }
                                })
                                _this.formDynamic.params.forEach(param => {
                                    if (
                                        !param.param_ext.param_value &&
                                        param.param_ext.param_show_type === 'draggable_order_list' &&
                                        param.param_ext.draggable_order_list_type === 'prod-suites'
                                    ) {
                                        param.param_ext.param_value = this.app_prod_suites
                                    }
                                })
                                _this.formDynamic.param_dict = this.formDynamic.params.reduce(
                                    (accumulator, currentValue) => {
                                        const phase = currentValue.phase
                                        const node = currentValue.node_name
                                        if (!accumulator[phase]) {
                                            accumulator[phase] = {}
                                        }
                                        if (!accumulator[phase][node]) {
                                            accumulator[phase][node] = []
                                        }
                                        accumulator[phase][node].push(currentValue)
                                        return accumulator
                                    },
                                    {}
                                )
                                console.log(this.formDynamic.param_dict)
                                _this.formDynamic.phase_list = Object.keys(this.formDynamic.param_dict)
                                _this.formDynamic.currentPhase = this.formDynamic.phase_list[0]
                                _this.formDynamic.nodes = Object.keys(
                                    this.formDynamic.param_dict[this.formDynamic.currentPhase]
                                )
                                _this.formDynamic.currentNode = this.formDynamic.nodes[0]
                                _this.formDynamic.current_params = this.formDynamic.param_dict[
                                    this.formDynamic.currentPhase
                                ][this.formDynamic.currentNode]
                            }
                        })
                        // 初始化时，查询编排下拉数据
                        const curBranch = this.formDynamic.params.filter(item => {
                            return item.param_key === 'biz_iter_branch'
                        })[0].param_ext.param_value
                        let query_params = { biz_code: curBranch.split('_')[0] }
                        this.biz_flow_name_options = []
                        getFlowList(query_params).then(res => {
                            if (res.data.status === 'success') {
                                this.biz_flow_name_options = res.data.data.map(item => {
                                    return {
                                        value: item.biz_pipeline_name,
                                        name: item.biz_pipeline_name
                                    }
                                })
                            }
                        })
                    } else {
                        this.$Notice.error({
                            title: 'Error',
                            desc: res.data.message,
                            duration: 0
                        })
                    }
                })
                .catch(err => {
                    this.$Notice.error({
                        title: 'Error',
                        desc: err.message,
                        duration: 0
                    })
                })
        },
        changeModuleName(module_name) {
            this.formDynamic.module_name = module_name
            this.formDynamic.params = []
            this.getParams(module_name)

            // 查询业务分支
            getBusinessBranch(this.formDynamic.module_name).then(res => {
                if (res.data.status === 'success') {
                    this.biz_iter_branch_options = res.data.data.map(item => {
                        return {
                            name: item.biz_test_iter_id,
                            value: item.biz_test_iter_id
                        }
                    })
                }
            })
        },
        bizBrNameChangeSelect(value, param_options_type) {
            console.log(`${value}:${param_options_type}`)
            if (param_options_type === 'biz_iter_branch') {
                if (typeof value === 'undefined') {
                    value = 0
                }
                // let query_params = { 'biz_test_iter_br': value}
                let query_params = { biz_code: value.split('_')[0] }
                this.biz_flow_name_options = []
                // get_test_flow_name_list(query_params).then(res => {
                getFlowList(query_params).then(res => {
                    if (res.data.status === 'success') {
                        this.biz_flow_name_options = res.data.data.map(item => {
                            return {
                                value: item.biz_pipeline_name,
                                name: item.biz_pipeline_name
                            }
                        })
                    }
                })
            }
        },
        initThisVue() {
            getAppModule().then(res => {
                let module_info_list = res.data.data['data_list']
                module_info_list.forEach(item => {
                    this.app_name_list.push(item['module_name'])
                })
            })
            getTestSuiteCode().then(res => {
                if (res.data.status === 'success') {
                    let env_options = []
                    res.data.data.forEach(item => {
                        env_options.push({
                            name: item,
                            value: item
                        })
                    })
                    console.log('env_options:%o', env_options)
                    this.env_options = env_options
                }
            })
            getEmailAddresses().then(res => {
                if (res.data.status === 'success') {
                    let user_options = []
                    console.log('user_options:%o', res.data.data)
                    res.data.data.forEach(item => {
                        user_options.push({
                            name: item,
                            value: item
                        })
                    })
                    this.user_options = user_options
                }
            })
            // get_test_iter_list().then(res => {
            //     if (res.data.status === 'success') {
            //         let biz_iter_branch_options = []
            //         res.data.data.forEach(item => {
            //             // biz_iter_branch_options.push({
            //             //   'name': item.biz_test_iter_br,
            //             //   'value': item.biz_test_iter_br
            //             // })
            //             biz_iter_branch_options.push({
            //                 name: item.biz_test_iter_id,
            //                 value: item.biz_test_iter_id
            //             })
            //         })
            //         this.biz_iter_branch_options = biz_iter_branch_options
            //     }
            // })
        }
    },
    mounted() {
        this.initThisVue()
    },
    destroyed() {}
}
</script>

<style>
.error-text {
    color: red;
}

.plan-phase-card-title {
    font-size: 14px !important;
}

.plan-node-card-title {
    font-size: 12px !important;
}

.ivu-tabs-mini .ivu-tabs-tab {
    font-size: 14px !important;
}

.plan-node-card-param {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-bottom: 5px;
}

.plan-node-card-param-desc {
    min-width: 60px;
    max-width: 30%;
    margin-right: 20px;
    justify-content: flex-end;
    display: flex;
    font-size: 12px;
}

.ivu-card-head {
    border-bottom: 1px solid #2d8cf0;
}

.plan-node-left {
    display: flex;
    justify-content: center;
    height: 80%;
    width: 10%;
}

.plan-node-right {
    height: 100%;
    width: 88%;
    border-left: 1px solid rgb(203 193 194);
    padding-left: 2%;
}

.plan-node {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-direction: row;
    overflow: auto;
    height: 80%;
    width: 100%;
    position: relative;
}
</style>
