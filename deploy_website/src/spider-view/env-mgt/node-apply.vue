<template>
    <div>
        <div style="margin-bottom: 15px">
            <h2>节点申请</h2>
        </div>
        <Card>
            <i-col>
                <Input placeholder="订单编号" v-model="order_code_text" clearable style="margin:5px; width:240px" />

                <Select placeholder="应用名" v-model="module_name_search" filterable clearable style="width:200px">
                    <Option
                        v-for="item in module_item_list"
                        :value="item.module_name"
                        :key="item.module_name"
                        :label="item.module_name"
                    >
                        {{ item.module_name }}
                    </Option>
                </Select>

                <Button @click="getTable(1)" type="info">查询</Button>

                <Button type="success" ghost @click="addOrder()" align="right">新增</Button>
                <Button style="margin-left: 2ex" type="dashed" @click="show_img">查询</Button>
                <span style=" text-align: left; display: inline-block;">（了解快速扩容方案详情请点击查询）</span>
                <Modal title=" " width="1000" v-model="show_img_val" :mask-closable="true">
                    <div style="width: 120%">
                        <img :src="imgUrl0" style="width: 900px" />
                    </div>
                </Modal>
            </i-col>

            <i-col>
                <Table :columns="apply_column" :data="apply_data"></Table>
            </i-col>
            <i-col>
                <Page
                    style="margin: 5px;"
                    :total="pageTotal"
                    :current="pageNum"
                    @page-size="pageSize"
                    @on-change="changePage"
                    show-total
                ></Page>
            </i-col>

            <!--编译页面-->
            <Modal v-model="modal_order" width="580px">
                <p slot="header" style="color:gray;">
                    <Icon type="md-clipboard"></Icon>
                    <span>节点申请订单</span>
                </p>
                <Form ref="form_order" :model="form_order" :rules="rule_order" label-position="right" :label-width="85">
                    <FormItem prop="order_code" label="订单编号" v-show="order_code_show">
                        <Input
                            style="width: 24em; font-size: 1rem; padding-left: 1em; color: #2d8cf0;"
                            v-model="form_order.order_code"
                            placeholder="订单编号"
                            disabled
                        />
                    </FormItem>
                    <FormItem prop="module_name" label="应用名">
                        <Select
                            placeholder="应用名"
                            v-model="form_order.module_name"
                            filterable
                            clearable
                            style="width: 12em; font-size: 1rem; padding-left: 1em; color: #2d8cf0;"
                            @on-change="module_name_select_change"
                        >
                            <Option
                                v-for="item in module_item_list"
                                :value="item.module_name"
                                :key="item.module_name"
                                :label="item.module_name"
                            >
                                {{ item.module_name }}
                            </Option>
                        </Select>
                        <Input
                            style="width: 6em; font-size: 1rem; padding-left: 1em; color: #2d8cf0;"
                            v-model="form_order.module_code"
                            placeholder="res_code"
                            disabled
                        />
                        <Input
                            style="width: 6em; font-size: 1rem; padding-left: 1em; color: #2d8cf0;"
                            v-model="form_order.team_name"
                            placeholder="所属团队"
                            disabled
                        />
                        <Input
                            style="width: 6em; font-size: 1rem; padding-left: 1em; color: #2d8cf0;"
                            v-model="form_order.team_short_name"
                            v-show="false"
                            placeholder="所属团队简称"
                            disabled
                        />
                        <Input
                            style="width: 6em; font-size: 1rem; padding-left: 1em; color: #2d8cf0;"
                            v-model="form_order.cmdb_team_name"
                            v-show="false"
                            placeholder="cmdb团队"
                            disabled
                        />
                    </FormItem>
                    <FormItem prop="region_name" label="可用域">
                        <Select
                            placeholder="可用域"
                            v-model="form_order.region_name"
                            filterable
                            style="width: 12em; font-size: 1rem; padding-left: 1em; color: #2d8cf0;"
                            @on-change="region_name_select_change"
                        >
                            <Option
                                v-for="item in region_item_list"
                                :value="item.region_name"
                                :key="item.region_name"
                                :label="item.region_name"
                            >
                                {{ item.region_name }}
                            </Option>
                        </Select>
                        <Input
                            style="width: 6em; font-size: 1rem; padding-left: 1em; color: #2d8cf0;"
                            v-model="region_id_model"
                            placeholder="ID"
                            v-show="false"
                            disabled
                        />
                        <Input
                            style="width: 6em; font-size: 1rem; padding-left: 1em; color: #2d8cf0;"
                            v-model="addr_name_model"
                            placeholder="机房"
                            disabled
                        />
                        <Input
                            style="width: 6em; font-size: 1rem; padding-left: 1em; color: #2d8cf0;"
                            v-model="type_name_model"
                            placeholder="用途"
                            disabled
                        />
                    </FormItem>
                    <FormItem prop="suite_code" label="环境套">
                        <Select
                            placeholder="请选择环境套"
                            v-model="form_order.suite_code"
                            style="width: 14em; font-size: 1rem; padding-left: 1em; color: #2d8cf0;"
                            @on-change="suite_code_select_change"
                        >
                            <Option v-for="item in suite_item_list" :value="item.suite_code" :key="item.suite_name">
                                {{ item.suite_name }}
                            </Option>
                        </Select>
                        <Input
                            style="width: 10em; font-size: 1rem; padding-left: 1em; color: #2d8cf0;"
                            v-model="suite_code_model"
                            placeholder="环境套编码"
                            disabled
                        />
                    </FormItem>
                    <FormItem prop="zone_code" label="可用区">
                        <Select
                            placeholder="请选择可用区"
                            v-model="form_order.zone_code"
                            clearable
                            style="width: 14em; font-size: 1rem; padding-left: 1em; color: #2d8cf0;"
                            @on-change="zone_code_select_change"
                        >
                            <Option v-for="item in zone_item_list" :value="item.zone_code" :key="item.vm_name">
                                {{ item.zone_code }}
                            </Option>
                        </Select>
                        <Input
                            style="width: 6em; font-size: 1rem; padding-left: 1em; color: #2d8cf0;"
                            v-model="zone_id_model"
                            placeholder="可用区ID"
                            v-show="false"
                            disabled
                        />
                        <Input
                            style="width: 10em; font-size: 1rem; padding-left: 1em; color: #2d8cf0;"
                            v-model="zone_code_model"
                            placeholder="可用区编码"
                            disabled
                        />
                        <!-- <Input style="width: 10em; font-size: 1rem; padding-left: 1em; color: #2d8cf0;"
                   v-model="cmdb_provider_code_model"
                   placeholder="cmdb供应商编码"
                   v-show="false"
                   disabled/>
            <Input style="width: 10em; font-size: 1rem; padding-left: 1em; color: #2d8cf0;"
                   v-model="cmdb_region_code_model"
                   placeholder="cmdb可用域编码"
                   v-show="false"
                   disabled/>
            <Input style="width: 10em; font-size: 1rem; padding-left: 1em; color: #2d8cf0;"
                   v-model="cmdb_zone_code_model"
                   placeholder="cmdb可用区编码"
                   v-show="false"
                   disabled/>
            <Input style="width: 10em; font-size: 1rem; padding-left: 1em; color: #2d8cf0;"
                   v-model="cmdb_env_code_model"
                   placeholder="cmdb环境编码"
                   v-show="false"
                   disabled/>
            <Input style="width: 10em; font-size: 1rem; padding-left: 1em; color: #2d8cf0;"
                   v-model="bread_domain_code_model"
                   placeholder="cmdb资源域英文名"
                   v-show="false"
                   disabled/> -->
                    </FormItem>
                    <FormItem prop="vm_name" label="机型模板">
                        <Select
                            placeholder="请选择模板"
                            v-model="form_order.vm_name"
                            style="width: 14em; font-size: 1rem; padding-left: 1em; color: #2d8cf0;"
                            @on-change="vm_code_select_change"
                        >
                            <Option v-for="item in vm_template_info_list" :value="item.vm_name" :key="item.vm_code">
                                {{ item.vm_name }}
                            </Option>
                        </Select>
                        <Input
                            style="width: 10em; font-size: 1rem; padding-left: 1em; color: #2d8cf0;"
                            v-model="vm_conf_name_model"
                            placeholder="机器信息"
                            disabled
                        />
                        <br />
                        <div style="padding-left: 1em">
                            <Tag color="volcano" v-show="false">{{ vm_info.vm_code }}</Tag>
                            <Tag color="red">{{ vm_info.vm_cpu }}</Tag>
                            <Tag color="green">{{ vm_info.vm_memory }}</Tag>
                            <Tag color="cyan">{{ vm_info.vm_disk }}</Tag>
                        </div>
                    </FormItem>
                    <!-- <FormItem prop="vm_code" label="机型">
            <RadioGroup v-model="form_order.vm_code" type="button" size="small"
                        style="font-size: 1rem; padding-left: 1em; color: #2d8cf0;"
                        @on-change="vm_code_radio_change">
              <Radio v-for="item in vm_item_list" :label="item.vm_name" :key="item.vm_name"
                     :disabled="!item.vm_is_active"/>
            </RadioGroup>
            <br/>
            <div style="padding-left: 1em">
              <Tag color="volcano" v-show="false">{{ vm_info.vm_code }}</Tag>
              <Tag color="red">{{ vm_info.vm_cpu }}</Tag>
              <Tag color="green">{{ vm_info.vm_memory }}</Tag>
              <Tag color="cyan">{{ vm_info.vm_disk }}</Tag>
              <Tag color="blue">{{ vm_info.vm_network }}</Tag>
              <Tag color="purple">{{ vm_info.vm_os }}</Tag>
              <Tag color="default">{{ vm_info.vm_desc }}</Tag>
            </div>
            <Input style="width: 6em; font-size: 1rem; padding-left: 1em; color: #2d8cf0;"
                   v-model="vm_id_model"
                   placeholder="机型ID"
                   v-show="false"
                   disabled/>
          </FormItem> -->
                    <Form :label-width="100">
                        <Row>
                            <Col span="12">
                                <FormItem prop="vm_count" label="数量">
                                    <Select
                                        placeholder="数量"
                                        v-model="form_order.vm_count"
                                        style="width: 6em; font-size: 1rem; padding-left: 1em; color: #2d8cf0;"
                                        @on-change="vm_count_select_change"
                                    >
                                        <Option
                                            v-for="item in vm_count_item_list"
                                            :value="item.code_value"
                                            :key="item.key"
                                        >
                                            {{ item.code_name }}
                                        </Option>
                                    </Select>
                                </FormItem>
                            </Col>
                            <Col span="12">
                                <FormItem prop="vm_disk" label="容量">
                                    <Select
                                        placeholder="容量"
                                        v-model="form_order.vm_disk"
                                        style="width: 6em; font-size: 1rem; padding-left: 1em; color: #2d8cf0;"
                                        @on-change="vm_disk_select_change"
                                    >
                                        <Option
                                            v-for="item in vm_disk_item_list"
                                            :value="item.code_value"
                                            :key="item.key"
                                        >
                                            {{ item.code_name }}
                                        </Option>
                                    </Select>
                                </FormItem>
                            </Col>
                        </Row>
                    </Form>
                    <FormItem prop="apply_reason" label="特殊说明">
                        <Input
                            type="textarea"
                            placeholder="JDK版本、Tomcat版本、配置参考等，如需提醒运维，请在此说明。"
                            v-model="form_order.apply_reason"
                            style="width: 385px; padding-left: 1em; "
                        />
                    </FormItem>
                </Form>

                <div slot="footer">
                    <Button @click="order_cancel">关闭</Button>
                    <Button v-show="apply_confirm_button_show" type="primary" @click="order_confirm">确定</Button>
                    <Button v-show="apply_button_show" type="primary" @click="order_apply_confirm">审核</Button>
                    <!-- <Button v-show = apply_button_show type="primary" @click="ops_operate_order(0)" >取消</Button> -->
                    <Button v-show="apply_button_show" type="primary" @click="ops_operate_order(5)">驳回</Button>
                </div>
            </Modal>
        </Card>
    </div>
</template>
<script>
import { getAppModule } from '@/spider-api/mgt-app'

import { getRegionInfo, getSuiteList } from '@/spider-api/mgt-env'

import {
    NodeApply,
    getNodeApply,
    getNodeZone,
    getNodeVm,
    getZoneVm,
    createNodeApply,
    getRegionZone,
    getVmCountAndDisk,
    envNodeMgtRegionZoneApi,
    zoneVmApi,
    saveNodeApplyOrder,
    GetOpsPermission,
    opsOperateNodeApplyOrder
} from '@/spider-api/mgt-node'
import commonIconVue from '../../components/common-icon/common-icon.vue'
import nodeMgtVue from './node-mgt.vue'

export default {
    data() {
        return {
            imgUrl0: require('../../img/节点扩容详情.png'),
            show_img_val: false,
            modal_order: false,
            order_code_show: false,
            vm_conf_name_model: '',
            apply_button_show: true,
            apply_confirm_button_show: '',
            vmInfo: '',
            reject_id: '',
            zone_obj: '',
            form_order: {
                order_code: '',
                module_name: '',
                module_code: '',

                team_name: '',
                team_short_name: '',
                cmdb_team_name: '',

                region_name: '',
                suite_code: '',
                zone_code: '',
                zone_id: '',

                vm_code: '',
                vm_count: '',
                vm_disk: '',
                vm_name: '',
                apply_reason: ''
            },
            rule_order: {
                module_name: [{ required: true, message: '应用名不能为空', trigger: 'change' }],
                region_name: [{ required: true, message: '可用域不能为空', trigger: 'change' }],
                suite_code: [{ required: true, message: '环境套不能为空', trigger: 'change' }],
                vm_count: [{ required: true, message: '数量不能为空', trigger: 'change' }],
                apply_reason: [{ required: true, message: '特殊说明不能为空', trigger: 'blur' }]
            },

            order_code_text: '',
            module_name_search: '',
            module_item_list: [],

            region_item_list: [],
            region_id_model: '',
            addr_name_model: '',
            type_name_model: '',

            suite_item_list: [],
            suite_code_model: '',
            vm_template_info_list: '',

            zone_item_list: [],
            zone_id_model: '',
            zone_code_model: '',
            cmdb_provider_code_model: '',
            cmdb_region_code_model: '',
            cmdb_zone_code_model: '',
            cmdb_env_code_model: '',
            bread_domain_code_model: '',

            vm_item_list: [],
            zone_vm_item_list: [],
            vm_id_model: '',
            permission_disable: '',
            vm_info: {
                vm_code: '',
                vm_cpu: '',
                vm_memory: '',
                vm_disk: '',
                vm_network: '',
                vm_os: '',
                vm_desc: ''
            },

            vm_count_item_list: [],
            vm_disk_item_list: [],
            pageNum: 1,
            pageSize: 10,
            pageTotal: 0,

            apply_column: [
                {
                    type: 'expand',
                    width: 60,
                    render: (h, params) => {
                        if (params.row.order_status !== 1) {
                            var show_expand = 'none'
                        }
                        return h('Table', {
                            //  console.log(params.row)
                            props: {
                                columns: this.subtableColumn,
                                data: params.row.servers,
                                border: true,
                                stripe: true
                            },
                            style: {
                                display: show_expand,
                                padding: '0px'
                            }
                        })
                    }
                },
                { title: '订单编号', key: 'order_code', width: 180 },
                { title: '应用名', key: 'module_name' },
                { title: 'res_code', key: 'module_code' },
                { title: '可用域', key: 'region_name' },
                { title: '环境套', key: 'suite_code' },
                { title: '可用区', key: 'zone_code' },
                { title: '机型', key: 'vm_name' },
                { title: '数量', key: 'vm_count', width: 50 },
                {
                    title: '订单状态',
                    key: 'order_status',

                    align: 'center',
                    render: (h, params) => {
                        switch (params.row.order_status) {
                            case 0:
                                return h('span', '已取消')
                            case 1:
                                return h('span', '已开通')
                            case 2:
                                return h('span', '待审核')
                            case 3:
                                return h('span', 'app-ops审核通过')
                            case 4:
                                return h('span', 'infra审核通过')
                            case 5:
                                return h('span', 'app-ops驳回')
                            case 6:
                                return h('span', 'infra驳回')
                            case 7:
                                return h('span', '超时')
                            case 8:
                                return h('span', '异常')
                            case 9:
                                return h(
                                    'span',
                                    {
                                        style: {
                                            color: '#187'
                                        }
                                    },
                                    '待确认'
                                )
                        }
                    }
                },
                // {
                //   title: '申请状态',
                //   key: 'apply_status',

                //   align: "center",
                //   render: (h, params) => {
                //     switch(params.row.apply_status){
                //       case 1:
                //         return h('span', '申请中');
                //         break;
                //       case 2:
                //         return h('span', '已申请');
                //         break;
                //       case 3:
                //         return h('span', '交付中');
                //         break;
                //       case 4:
                //         return h('span', '已驳回');
                //         break;
                //       case 5:
                //         return h('span', '已取消');
                //         break;
                //       case 6:
                //         return h('span', '已交付');
                //         break;
                //     }
                //   }
                // },
                {
                    title: '操作',
                    key: 'handle',

                    align: 'center',
                    render: (h, params) => {
                        return h('div', [
                            h(
                                'Button',
                                {
                                    attrs: {
                                        class: 'ivu-btn ivu-btn-primary ivu-btn-small'
                                    }, //button-查看-attrs
                                    props: {
                                        type: 'primary',
                                        size: 'small',
                                        disabled: params.row.permission_disable
                                    }, //button-查看-props
                                    style: {
                                        marginRight: '6px'
                                    }, //button-查看-style
                                    on: {
                                        click: () => {
                                            console.log(params.row)
                                            this.modal_order = true
                                            this.order_code_show = true
                                            this.apply_button_show = true
                                            this.apply_confirm_button_show = false
                                            this.form_order.order_code = params.row.order_code
                                            this.form_order.apply_user = params.row.apply_user
                                            this.init_module_name_select(params.row.module_name)
                                            this.init_region_name_select(params.row.region_name)
                                            this.init_suite_code_select(this.region_id_model, params.row.suite_code)
                                            // this.init_zone_code_select(params.row.region_name, params.row.zone_code)
                                            this.init_vm_code_radio(params.row.zone_code, params.row.vm_code)
                                            this.zone_item_list = []
                                            envNodeMgtRegionZoneApi(params.row.region_name).then(res => {
                                                let ret = res.data.data
                                                for (let item of res.data.data.data_list) {
                                                    this.zone_item_list.push(item)
                                                }
                                            })
                                            getVmCountAndDisk().then(res => {
                                                this.vm_count_item_list = res.data.data.vm_count
                                                this.vm_disk_item_list = res.data.data.vm_disk
                                            })
                                            zoneVmApi(params.row.zone_code).then(res => {
                                                this.vm_template_info_list = res.data.data.data_list
                                            })
                                            this.form_order.vm_count = params.row.vm_count
                                            this.form_order.vm_disk = params.row.vm_disk
                                            this.form_order.vm_name = params.row.vm_name
                                            this.form_order.zone_code = params.row.zone_code
                                            this.form_order.zone_id = params.row.zone_id
                                            this.form_order.apply_reason = params.row.apply_reason
                                            this.reject_id = params.row.id
                                            if (params.row.order_status === 2) {
                                            }
                                        }
                                    } //button-查看-on
                                },
                                '审核'
                            ),
                            h(
                                'Button',
                                {
                                    attrs: {
                                        class: 'ivu-btn ivu-btn-primary ivu-btn-small'
                                    }, //button-查看-attrs
                                    props: {
                                        type: 'primary',
                                        size: 'small',
                                        disabled: params.row.cancel_permission_disable
                                    }, //button-查看-props
                                    style: {
                                        marginRight: '6px'
                                    }, //button-查看-style
                                    on: {
                                        click: () => {
                                            this.modal_order = false
                                            opsOperateNodeApplyOrder(0, params.row).then(res => {
                                                if (res.data.status === 'failed') {
                                                    alert(res.data.msg)
                                                } else {
                                                    this.getTable(this.pageNum)
                                                }
                                            })
                                        }
                                    } //button-查看-on
                                },
                                '取消'
                            ) //button-查看
                        ]) //return
                    } //render
                } //操作
            ],
            subtableColumn: [
                {
                    title: '服务器名称',
                    key: 'node_name',
                    sortable: true,
                    tooltip: true
                },
                {
                    title: '机器IP',
                    key: 'node_ip',
                    sortable: true,
                    tooltip: true
                }
            ],
            apply_data: []
        }
    }, //data

    methods: {
        addOrder() {
            this.modal_order = true
            this.apply_confirm_button_show = true
            this.apply_button_show = false
            this.order_code_show = false
            this.form_order.order_code = ''
            this.init_module_name_select()
            this.init_region_name_select()
            // this.init_zone_code_select()
            this.form_order.suite_name = ''
            this.form_order.zone_code = ''
            // this.form_order.vm_name = ''
            this.form_order.vm_count = 1
            this.form_order.vm_disk = 0
            this.form_order.apply_reason = ''
            this.form_order.region_name = ''
            this.form_order.vm_name = ''
            getVmCountAndDisk().then(res => {
                console.log(res.data.data.vm_count)
                this.vm_count_item_list = res.data.data.vm_count
                this.vm_disk_item_list = res.data.data.vm_disk
                console.log(this.vm_count_item_list)
                console.log(this.vm_disk_item_list)
            })
        },
        show_img() {
            this.show_img_val = true
        },
        changePage(idx) {
            this.pageNum = idx
            this.getTable(idx)
        },
        ops_operate_order(operate_type) {
            this.modal_order = false
            opsOperateNodeApplyOrder(operate_type, this.form_order).then(res => {
                console.log(res.data)
                if (res.data.status === 'failed') {
                    alert(res.data.msg)
                } else {
                    this.getTable(this.pageNum)
                }
            })
        },
        getTable(idx) {
            let data = {
                module_name: this.module_name_search,
                order_code: this.order_code_text,

                pageNum: idx,
                pageSize: this.pageSize,
                pageTotal: this.pageTotal
            }
            GetOpsPermission().then(res => {
                this.permission_disable = res.data.data['permission_disable']
            })
            getNodeApply(data).then(res => {
                let page = res.data.data['page']
                if (page != null) {
                    this.pageNum = page['num']
                    this.pageSize = page['size']
                    this.pageTotal = page['total']
                }

                this.apply_data = res.data.data['data_list']
            })
        },
        // 确定
        order_confirm() {
            if (this.form_order.vm_disk == '' && this.form_order.vm_disk != 0) {
                this.form_order.vm_disk = this.vm_info.vm_disk
            }
            this.modal_order = false
            saveNodeApplyOrder(this.form_order).then(res => {
                if (res.data.status === 'failed') {
                    alert(res.data.msg)
                } else {
                    this.getTable(this.pageNum)
                }
            })
        },
        order_apply_confirm() {
            console.log('=======审核参数=======')
            console.log(this.form_order)
            saveNodeApplyOrder(this.form_order).then(res => {
                if (res.data.status === 'failed') {
                    alert(res.data.msg)
                } else {
                    NodeApply(this.form_order.order_code).then(res => {
                        if (res.data.status === 'failed') {
                            alert(res.data.msg)
                        } else {
                            this.getTable(this.pageNum)
                        }
                    })
                    this.getTable(this.pageNum)
                }
            })
            this.modal_order = false
        },
        // order_confirm () {
        //   this.modal_order = false
        //   this.form_order.region_id = this.region_id_model
        //   this.form_order.addr_name = this.addr_name_model
        //   this.form_order.type_name = this.type_name_model

        //   this.form_order.zone_id = this.zone_id_model
        //   this.form_order.cmdb_provider_code = this.cmdb_provider_code_model
        //   this.form_order.cmdb_region_code = this.cmdb_region_code_model
        //   this.form_order.cmdb_zone_code = this.cmdb_zone_code_model
        //   this.form_order.cmdb_env_code = this.cmdb_env_code_model
        //   this.form_order.bread_domain_code = this.bread_domain_code_model

        //   this.form_order.vm_id = this.vm_id_model

        //   this.form_order.vm_cpu = this.vm_info.vm_cpu
        //   this.form_order.vm_memory = this.vm_info.vm_memory
        //   this.form_order.vm_disk = this.vm_info.vm_disk
        //   this.form_order.vm_network = this.vm_info.vm_network
        //   this.form_order.vm_os = this.vm_info.vm_os
        //   this.form_order.vm_desc = this.vm_info.vm_desc
        //   console.log(">>>> createNodeApply():req = ", JSON.stringify(this.form_order))
        //   createNodeApply(this.form_order).then(res => {
        //     console.log(">>>> createNodeApply():res = ", JSON.stringify(res))
        //     if (res.data["status"] == "success") {
        //       this.changePage(this.pageNum)
        //       this.$Message.success('录入成功')
        //     }
        //     else if (res.data["status"] == "failed") {
        //       this.changePage(this.pageNum)
        //       this.$Message.error(res.data['msg'])
        //     }
        //     else {
        //       this.$Message.error(res.data.msg)
        //     }
        //     // 因为会反向填充res_code，所以需要刷新一下
        //     this.init_app_module_list()
        //   })
        // },
        // 取消
        order_cancel() {
            this.modal_order = false
        },
        // 「应用名」初始化：直接调用change
        init_module_name_select(module_name) {
            this.module_name_select_change(module_name)
        },
        // 「应用名」改变：联动res_code
        module_name_select_change(module_name) {
            if (module_name != undefined && module_name != null && module_name != '') {
                this.form_order.module_name = module_name
                if (this.module_item_list != null && this.module_item_list.length > 0) {
                    for (let i = 0; i < this.module_item_list.length; i++) {
                        let module_obj = this.module_item_list[i]
                        if (module_obj.module_name == module_name) {
                            this.form_order.module_code = module_obj.module_code
                            this.form_order.team_name = module_obj.team_name
                            this.form_order.team_short_name = module_obj.team_short_name
                            this.form_order.cmdb_team_name = module_obj.cmdb_team_name
                            break
                        }
                    }
                } else {
                    this.form_order.module_code = null
                    this.form_order.team_name = null
                    this.form_order.team_short_name = null
                    this.form_order.cmdb_team_name = null
                }
            } else {
                this.form_order.module_name = null
                this.form_order.module_code = null
                this.form_order.team_name = null
                this.form_order.team_short_name = null
                this.form_order.cmdb_team_name = null
            }
        },
        // 「可用域」初始化：初始化「环境套」和「可用区」
        init_region_name_select(region_name) {
            if (region_name != undefined && region_name != null && region_name != '') {
                this.form_order.region_name = region_name
                if (this.region_item_list != null && this.region_item_list.length > 0) {
                    for (let i = 0; i < this.region_item_list.length; i++) {
                        let region_item = this.region_item_list[i]
                        if (region_item.region_name == region_name) {
                            this.form_order.region_name = region_item.region_name
                            this.region_id_model = region_item.id
                            this.addr_name_model = region_item.addr_name
                            this.type_name_model = region_item.type_name
                            break
                        }
                    }
                } else {
                    this.region_id_model = null
                    this.addr_name_model = null
                    this.type_name_model = null
                }
            } else {
                this.form_order.region_name = null
                this.region_id_model = null
                this.addr_name_model = null
                this.type_name_model = null
            }
        },
        // 「可用域」改变：联动「环境套」和「可用区」
        region_name_select_change(region_name) {
            this.init_region_name_select(region_name)
            // 联动「环境套」
            this.suite_code_for_region(this.region_id_model)
            // 联动「可用区」
            // this.zone_code_for_region(region_name)
            envNodeMgtRegionZoneApi(region_name).then(res => {
                let ret = res.data.data
                console.log(res.data.data.data_list)
                this.zone_item_list = []
                for (let item of res.data.data.data_list) {
                    this.zone_item_list.push(item)
                }
            })
        },
        // 「环境套」初始化：初始化环境套编码
        init_suite_code_select(region_id, suite_code) {
            if (region_id != undefined && region_id != null && region_id != '') {
                let data = {
                    suite_is_active: 1,
                    region_id: region_id
                }
                getSuiteList(data).then(res => {
                    this.suite_item_list = res.data.data['data_list']
                    if (
                        this.suite_item_list != null &&
                        this.suite_item_list.length > 0 &&
                        suite_code != undefined &&
                        suite_code != null &&
                        suite_code != ''
                    ) {
                        // suite_code 赋值
                        this.form_order.suite_code = suite_code
                        // 联运suite_code_model
                        for (let i = 0; i < this.suite_item_list.length; i++) {
                            let suite_obj = this.suite_item_list[i]
                            if (suite_obj.suite_code == suite_code) {
                                this.suite_code_model = suite_obj.suite_code
                                break
                            }
                        }
                    } else {
                        this.form_order.suite_code = null
                        this.suite_code_model = null
                    }
                })
            } else {
                this.form_order.suite_code = null
                this.suite_code_model = null
            }
        },
        // 「环境套」动态改变：由可用域联动过来
        suite_code_for_region(region_id) {
            if (region_id != undefined && region_id != null && region_id != '') {
                let data = {
                    suite_is_active: 1,
                    region_id: region_id
                }
                getSuiteList(data).then(res => {
                    this.suite_item_list = res.data.data['data_list']
                    if (this.suite_item_list != null && this.suite_item_list.length == 1) {
                        let suite_item = this.suite_item_list[0]
                        this.form_order.suite_code = suite_item.suite_code
                        this.suite_code_model = suite_item.suite_code
                    } else {
                        this.form_order.suite_code = null
                        this.suite_code_model = null
                    }
                })
            } else {
                this.form_order.suite_code = null
                this.suite_code_model = null
            }
        },
        // 「环境套」自身改变：联动环境套code
        suite_code_select_change(suite_code) {
            if (suite_code != undefined && suite_code != null && suite_code != '') {
                this.form_order.suite_code = suite_code
                // 联运suite_code_model
                for (let i = 0; i < this.suite_item_list.length; i++) {
                    let suite_obj = this.suite_item_list[i]
                    if (suite_obj.suite_code == suite_code) {
                        this.suite_code_model = suite_obj.suite_code
                        break
                    }
                }
            } else {
                this.form_order.suite_code = null
                this.suite_code_model = null
            }
        },
        zone_code_select_change() {
            if (
                this.form_order.zone_code != undefined &&
                this.form_order.zone_code != null &&
                this.form_order.zone_code != ''
            ) {
                for (let item in this.zone_item_list) {
                    console.log(this.zone_item_list[item])
                    if (this.zone_item_list[item]['zone_code'] == this.form_order.zone_code) {
                        this.form_order.zone_id = this.zone_item_list[item]['id']
                        this.zone_code_model = this.zone_item_list[item]['zone_name']
                    }
                }
            } else {
                this.form_order.zone_code = null
            }
            console.log('======切换可用区的参数=======')
            console.log(this.form_order)
            // if(zone_code != undefined && zone_code != null && zone_code != ''){

            //   let new_zone_obj = JSON.parse(this.zone_obj)
            //   this.form_order.zone_id = new_zone_obj.id
            //   this.form_order.zone_code = new_zone_obj.zone_code
            //   this.zone_code_model = new_zone_obj.zone_name
            // }else{
            //   this.form_order.zone_code = null
            // this.zone_id_model = null
            // this.zone_code_model = null
            // this.cmdb_provider_code_model = null
            // this.cmdb_region_code_model = null
            // this.cmdb_zone_code_model = null
            // this.cmdb_env_code_model = null
            // this.bread_domain_code_model = null
            // }
            // 联动「机型」可用性初始化
            // this.zone_code_model = this.zone_item_list[this.form_order.zone_code]
            // this.vm_code_for_zone(zone_code)
            zoneVmApi(this.form_order.zone_code).then(res => {
                this.vm_template_info_list = res.data.data.data_list
            })
        },
        // 「机型」初始化：虚拟机配置联动
        init_vm_code_radio(zone_code, vm_code) {
            if (zone_code != undefined && zone_code != null && zone_code != '') {
                let data = {
                    zone_code: zone_code
                }
                getZoneVm(data).then(res => {
                    this.zone_vm_item_list = res.data.data['data_list']
                    if (
                        this.zone_vm_item_list != null &&
                        this.zone_vm_item_list.length > 0 &&
                        vm_code != undefined &&
                        vm_code != null &&
                        vm_code != ''
                    ) {
                        // vm_code赋值
                        for (let i = 0; i < this.zone_vm_item_list.length; i++) {
                            let zone_vm_obj = this.zone_vm_item_list[i]
                            if (zone_vm_obj.vm_code == vm_code) {
                                this.form_order.vm_code = zone_vm_obj.vm_code
                                this.vm_id_model = zone_vm_obj.vm_id
                                break
                            }
                        }
                    } else {
                        this.form_order.vm_code = null
                        this.vm_id_model = null
                    }
                    // vm_code可用性初始化（异步）
                    this.init_vm_code_enabled(this.form_order.vm_code)
                })
            } else {
                this.form_order.vm_code = null
                this.vm_id_model = null
                // vm_code可用性初始化（同步）
                this.init_vm_code_enabled(this.form_order.vm_code)
            }
        },
        // 「机型」改变：可用区改变引发「机型」变化
        // vm_code_for_zone(zone_code){
        //   if(zone_code != undefined && zone_code != null && zone_code != ''){
        //     let data = {
        //       "zone_code": zone_code,
        //     }
        //     getZoneVm(data).then(res => {
        //       this.zone_vm_item_list = res.data.data["data_list"]
        //       if(this.zone_vm_item_list != null && this.zone_vm_item_list.length > 0){
        //         if(this.zone_vm_item_list.length == 1){
        //           let zone_vm_obj = this.zone_vm_item_list[0]
        //           // vm_code赋值
        //           this.form_order.vm_code = zone_vm_obj.vm_code
        //           this.vm_id_model = zone_vm_obj.vm_id
        //         }else{
        //           this.form_order.vm_code = null
        //           this.vm_id_model = null
        //         }
        //         // vm_code可用性初始化并选中
        //         this.init_vm_code_enabled(this.form_order.vm_code)

        //       }else{
        //         this.form_order.vm_code = null
        //         this.vm_id_model = null
        //         // vm_code全部不可用
        //         this.init_vm_code_disabled()
        //       }
        //     })
        //   }else{
        //     this.form_order.vm_code = null
        //     this.vm_id_model = null
        //     // vm_code全部不可用
        //     this.init_vm_code_disabled()
        //   }
        // },
        // 「机型」自身改变：联动彩色标签变化
        vm_code_radio_change(vm_code) {
            if (
                this.zone_vm_item_list != null &&
                this.zone_vm_item_list.length > 0 &&
                vm_code != undefined &&
                vm_code != null &&
                vm_code != ''
            ) {
                // vm_code赋值
                for (let i = 0; i < this.zone_vm_item_list.length; i++) {
                    let zone_vm_obj = this.zone_vm_item_list[i]
                    if (zone_vm_obj.vm_code == vm_code) {
                        this.form_order.vm_code = zone_vm_obj.vm_code
                        this.vm_id_model = zone_vm_obj.vm_id
                        break
                    }
                }
            } else {
                this.form_order.vm_code = null
                this.vm_id_model = null
            }
            // 联动彩色标签
            this.vm_tag_for_code(vm_code)
        },
        // 「机型标签」可用性初始化并选中
        init_vm_code_enabled(vm_code) {
            for (let i = 0; i < this.vm_item_list.length; i++) {
                let vm_item = this.vm_item_list[i]
                let vm_is_active = false
                for (let j = 0; j < this.zone_vm_item_list.length; j++) {
                    if (vm_item.vm_name === this.zone_vm_item_list[j].vm_code) {
                        vm_is_active = true
                        break
                    }
                }
                vm_item['vm_is_active'] = vm_is_active
                this.vm_item_list.splice(i, 1, vm_item)
            }
            // 联动彩色标签
            this.vm_tag_for_code(vm_code)
        },
        // 「机型标签」：全部不可用
        init_vm_code_disabled() {
            this.vm_item_list.forEach(vm_item => {
                vm_item['vm_is_active'] = false
            })
            // 联动彩色标签
            this.vm_tag_for_code()
        },
        // 「机型标签」改变：彩色标签跟随变化
        vm_tag_for_code(vm_code) {
            if (vm_code != undefined && vm_code != null && vm_code != '') {
                this.vm_info.vm_code = vm_code + '型：'
                for (let i = 0; i < this.vm_item_list.length; i++) {
                    let vm_item = this.vm_item_list[i]
                    if (vm_item.vm_name === vm_code) {
                        this.vm_info.vm_cpu = vm_item.vm_cpu + ' CPU'
                        this.vm_info.vm_memory = Math.round(vm_item.vm_memory / 1024) + ' GB'
                        this.vm_info.vm_disk = vm_item.vm_disk + ' GB'
                        this.vm_info.vm_network = vm_item.vm_network + ' 网卡'
                        this.vm_info.vm_os = vm_item.vm_os
                        this.vm_info.vm_desc = vm_item.vm_desc
                        break
                    }
                }
            } else {
                // 切换后清空选择的机型
                this.vm_info.vm_code = null
                this.vm_info.vm_cpu = null
                this.vm_info.vm_memory = null
                this.vm_info.vm_disk = null
                this.vm_info.vm_network = null
                this.vm_info.vm_os = null
                this.vm_info.vm_desc = null
            }
        },
        vm_count_select_change(val) {
            console.log(this.form_order.vm_count)
        },
        vm_disk_select_change() {
            // console.log(this.form_order.vm_disk)
            for (let item of this.vm_disk_item_list) {
                console.log(item)
                if (item['code_value'] === this.form_order.vm_disk) {
                    console.log(item['code_name'])
                    this.vm_info.vm_disk = item['code_name']
                }
            }
        },
        vm_code_select_change() {
            for (let item of this.vm_template_info_list) {
                if (this.form_order.vm_name === item['vm_name']) {
                    this.vm_conf_name_model = item['vm_conf_name']
                    this.vm_info.vm_cpu = item['vm_cpu']
                    this.vm_info.vm_memory = item['vm_memory']
                    this.vm_info.vm_disk = item['vm_disk_name']
                    this.form_order.vm_id = item['vm_id']
                    this.form_order.vm_disk = item['vm_disk']
                }
            }
        },
        init_app_module_list() {
            getAppModule().then(res => {
                this.module_item_list = res.data.data['data_list']
            })
        }
    }, //methods
    mounted() {
        this.init_app_module_list()
        getRegionInfo().then(res => {
            this.region_item_list = res.data.data['data_list']
        })
        getNodeVm().then(res => {
            this.vm_item_list = res.data.data['data_list']
            this.vm_item_list.forEach(vm_item => {
                vm_item['vm_is_active'] = false
            })
        })
    } //mounted
} //default
</script>

<style></style>
