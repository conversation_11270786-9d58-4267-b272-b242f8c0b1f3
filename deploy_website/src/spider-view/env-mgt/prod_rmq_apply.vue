<template>
  <div>
    <div style="margin-bottom: 15px">
      <h2>产线组RMQ申请</h2>
    </div>
    <Card>
      <Input
          placeholder="订单号"
          v-model="applyFormItem.rmq_order_code"
          clearable
          style="margin:5px; width:240px"
        />
        <Button @click="getTable(1)" type="info">查询</Button>

        <Button type="success" ghost @click="showcreatermqapplyModal()" align="right">申请单</Button>
        <i-col >
        <Table :columns="apply_column" :data="apply_data"></Table>
      </i-col>
      <i-col>
        <Page style="margin: 5px;" :total="pageTotal" :current="pageNum" @page-size="pageSize"
              @on-change="changePage" show-total></Page>
      </i-col>
    <Modal
        v-model="rmq_apply_modal"
        title="产线组RMQ申请"
        width="800"
        @on-ok="create_rmq_order"
        @on-cancel="apply_cancel">

    <Form :model="applyFormItem" :label-width="80">
        <Row>
            <Col span="12">
        <FormItem label="应用名">
            <Select placeholder="应用名" v-model="applyFormItem.app_name" :filterable="true"
                                :clearable="true" @on-change="get_app_version">
                <Option v-for="item in module_item_list"
                      :value="item.module_name"
                      :key="item.module_name"
                      :label="item.module_name">
                {{ item.module_name }}</Option>
            </Select>
        </FormItem>
        </Col>
        <Col span="10">
        <FormItem label="应用版本">
            <Select placeholder="应用版本" v-model="applyFormItem.version" filterable clearable>
                <Option v-for="item in version_list"
                      :value="item.pipeline_id"
                      :key="item.pipeline_id"
                      :label="item.pipeline_id">
                {{ item.pipeline_id }}</Option>
            </Select>
        </FormItem>

        </Col>
    </Row>
    <Row>
        <Col span="10">
            <FormItem label="环境">
                <Select placeholder="环境" v-model="suite_obj" @on-change="get_nameserver_info">
                    <Option v-for="item in env_info_list"
                      :value="JSON.stringify(item)"
                      :label="item.suite_code">
                    {{ item.suite_code }}</Option>
                </Select>
            </FormItem>
        </Col>
        <Col span="11">
            <FormItem label="nameserver">
                <Select placeholder="nameserver" v-model="nameserver_obj" @on-change="change_name_server">
                    <Option v-for="item in name_server_list"
                            :value="JSON.stringify(item)"
                            :key="item.code_name"
                            :label="item.code_name">
                    {{ item.code_name }}</Option>
                </Select>
            </FormItem>

        </Col>
    </Row>
    <Row>
        <Col span="10">
            <!-- <FormItem label="消息名称"> -->
            <FormItem label="主题名称">
                <Input style=" font-size: 1rem; color: #2d8cf0;"
                   v-model="applyFormItem.message_name"
                   placeholder="主题名称"
                   />
            </FormItem>
        </Col>
        <Col span="9">
        <FormItem label="使用类型">
            <i-switch v-model="applyFormItem.usetype" size="large" @on-change="switch_use_type">
                <span slot="open">消</span>
                <span slot="close">生</span>
            </i-switch>
        </FormItem>
        </Col>
    </Row>
    <Row v-show="group_and_tag">
        <Col span="10">
            <!-- <FormItem label="msgConfig的组"> -->
            <FormItem label="宙斯中配置的组名">
                <Input style=" font-size: 1rem; color: #2d8cf0;"
                   v-model="applyFormItem.msgconfiggroup"
                   placeholder="宙斯msgConfig中的groupName属性"
                   />
            </FormItem>
        </Col>
        <Col span="10">
            <FormItem label="消费的tag">
                <Input style=" font-size: 1rem; color: #2d8cf0;"
                   v-model="applyFormItem.consumer_tag"
                   placeholder="消费的tag"
                   />
            </FormItem>
        </Col>
    </Row>
    <Row v-show="group_and_tag">
        <Col span="9" >
            <FormItem label="QueueGroup">
            <i-switch v-model="applyFormItem.queuegroupmode" size="large" @on-change="switch_queue_group">
                <span slot="open">是</span>
                <span slot="close">否</span>
            </i-switch>
        </FormItem>
        </Col>
        <Col span="11" v-show="show_message_properties">
            <FormItem label="消费者名称"  >
                <Input style="font-size: 1rem; color: #2d8cf0;"
                   v-model="applyFormItem.message_properties"
                   placeholder="msg.properties应用名称"
                   />
            </FormItem>
        </Col>
    </Row>
    </Form>

    </Modal>

    </Card>
  </div>
</template>
<script>

import {
  getAppModule
} from '@/spider-api/mgt-app'

import {
  getRegionInfo,
  getSuiteList,
  getProdRegin,
  getNameServer,
  getAppVersion,
  createRmqOrder,
  getRmqOrderInfo
} from '@/spider-api/mgt-env'

export default {
  data () {
    return {
      pageTotal: '',
      pageNum: '',
      pageSize: '',
      rmq_order: '',
      pageNum: 1,
      pageSize: 10,
      pageTotal: 0,
      rmq_apply_modal: false,
      module_item_list: '',
      env_info_list: '',
      name_server_list: '',
      group_and_tag: false,
      show_message_properties: false,
      suite_obj: '',
      nameserver_obj: '',
      version_list: '',
      applyFormItem: {
        app_name: '',
        version: '',
        env: '',
        rmq_order_code: '',
        nameserver: '',
        rmq_name_srv_id: '',
        rmq_prod_env_id: '',
        message_name: '',
        usetype: false,
        msgconfiggroup: '',
        consumer_tag: '',
        queuegroupmode: false,
        message_properties: ''
      },
      apply_column: [
        {
          title: '订单号',
          key: 'rmq_order_code',
          align: 'center'
        },
        {
          title: '应用名称',
          key: 'rmq_module_name',
          align: 'center'
        },
        {
          title: '版本',
          key: 'rmq_iteration_ver',
          align: 'center'
        },
        {
          title: 'message_config_group',
          key: 'msg_conf_group',
          align: 'center'
        },
        {
          title: '消息名称(topic)',
          key: 'rmq_name',
          align: 'center'
        },
        {
          title: '使用类型',
          key: 'is_consumer',
          align: 'center'
        },
        {
          title: '消费的tag',
          key: 'consumer_tag',
          align: 'center'
        },
        {
          title: 'QueueGroup模式',
          key: 'is_queue_group',
          align: 'center'
        },
        {
          title: 'msg.properties应用名称',
          key: 'msg_prop_proj_name',
          align: 'center'
        },
        {
          title: 'nameServer',
          key: 'code_name',
          align: 'center'
        },
        {
          title: '申请人',
          key: 'apply_user',
          align: 'center'
        },
        {
          title: '申请时间',
          key: 'create_time',
          align: 'center'
        }
      ],
      apply_data: []
    }
  },

  methods: {
    getTable (idx) {
      let search_info = {
        'order_code': this.applyFormItem.rmq_order_code,

        'pageNum': idx,
        'pageSize': this.pageSize,
        'pageTotal': this.pageTotal
      }
      getRmqOrderInfo(search_info).then(res => {
        if (res.data.status === 'failed') {
          alert(res.data.msg)
        }
        this.apply_data = res.data.data.rmq_list
        let page_info = res.data.data.page_info
        this.pageNum = page_info['num']
        this.pageSize = page_info['size']
        this.pageTotal = page_info['total']
      })
    },
    showcreatermqapplyModal () {
      this.rmq_apply_modal = true
      getAppModule().then(res => {
        this.module_item_list = res.data.data['data_list']
      })
      getProdRegin().then(res => {
        this.env_info_list = res.data.data['env_info']
        console.log(this.env_info_list)
      })
    },
    changePage (idx) {
      this.pageNum = idx
      this.getTable(idx)
    },

    apply_comfirm () {

    },

    apply_cancel () {

    },
    switch_use_type () {
      this.group_and_tag = this.applyFormItem.usetype
    },
    switch_queue_group () {
      this.show_message_properties = this.applyFormItem.queuegroupmode
    },
    get_nameserver_info () {
      let env_obj = JSON.parse(this.suite_obj)
      this.applyFormItem.env = env_obj.suite_code
      this.applyFormItem.rmq_prod_env_id = env_obj.env_id
      getNameServer(env_obj.env_id).then(res => {
        this.name_server_list = res.data.data['name_server']
      })
    },
    get_app_version () {
      getAppVersion(this.applyFormItem.app_name).then(res => {
        if (res.data.status === 'failed') {
          alert(res.data.msg)
        } else {
          this.version_list = res.data.data['version_list']
        }
      })
    },
    change_name_server () {
      let n = JSON.parse(this.nameserver_obj)
      this.applyFormItem.rmq_name_srv_id = n.id
      this.applyFormItem.nameserver = n.code_name
    },
    create_rmq_order () {
      createRmqOrder(this.applyFormItem).then(res => {
        if (res.data.status === 'failed') {
          alert(res.data.msg)
        }
      })
    },
    apply_cancel () {
      this.rmq_apply_modal = false
    }

  }, // methods
  mounted () {
    // getAppModule()

  }// mounted
}// default
</script>

<style>

</style>
