<template>
  <div>
    <div style="margin-bottom: 15px">
      <h2>环境管理</h2>
    </div>
    <Card>
      <Table stripe :columns="env_column" :data="env_data"/>

      <Modal
        v-model="modal_env">
        <p slot="header" style="color:gray;">
          <Icon type="md-clipboard"></Icon>
          <span>环境信息编辑</span>
        </p>
        <Form ref="form_env" :model="form_env" :rules="rule_env" label-position="right" :label-width="70">
          <FormItem prop="region_name" label="可用域">
            <Select v-model="region_model" style="width: 10em; font-size: 1rem; padding-left: 1em; color: #2d8cf0;"
            @on-change="region_model_change">
              <Option v-for="item in region_list" :value="item.region_name" :key="item.region_name">
                {{ item.region_name }}
              </Option>
            </Select>
            <Input style="width: 6em; font-size: 1rem; padding-left: 1em; color: #2d8cf0;"
                   v-model="region_id_model"
                   placeholder="ID"
                   v-show="false"
                   disabled/>
            <Input style="width: 6em; font-size: 1rem; padding-left: 1em; color: #2d8cf0;"
                   v-model="addr_name_model"
                   placeholder="机房"
                   disabled/>
            <Input style="width: 6em; font-size: 1rem; padding-left: 1em; color: #2d8cf0;"
                   v-model="type_name_model"
                   placeholder="用途"
                   disabled/>
          </FormItem>
          <FormItem prop="suite_code" label="编码">
            <Input style="width: 22em; font-size: 1rem; padding-left: 1em; color: #2d8cf0;"
                   v-model="form_env.suite_code"
                   placeholder="编码"
                   disabled/>
          </FormItem>
          <FormItem prop="suite_name" label="名称">
            <Input style="width: 22em; font-size: 1rem; padding-left: 1em; color: #2d8cf0;"
                   v-model="form_env.suite_name"
                   placeholder="名称"/>
          </FormItem>
          <FormItem prop="suite_desc" label="说明">
            <Input style="width: 22em; font-size: 1rem; padding-left: 1em; color: #2d8cf0;"
                   type="textarea" :autosize="{minRows: 2,maxRows: 5}"
                   v-model="form_env.suite_desc"
                   placeholder="说明"/>
          </FormItem>
          <FormItem prop="suite_is_active" label="可用性">
            <i-switch v-model="suite_is_active_model"/>
          </FormItem>
        </Form>
        <div slot="footer">
          <Button @click="env_cancel">关闭</Button>
          <Button type="primary" @click="env_confirm">确定</Button>
        </div>
      </Modal>
    </Card>
  </div>
</template>
<script>
  import {
    getEnvInfo,
    createEnvMgt,
    getRegionInfo,
  } from '@/spider-api/mgt-env'
  export default {
    data () {
      return {
        modal_env: false,
        form_env: {
          region_id: '',
          region_name: '',
          addr_name: '',
          type_name: '',
          suite_code: '',
          suite_name: '',
          suite_desc: '',
          suite_is_active: '',
        },
        rule_env: {
          suite_code: [
            { required: true, message: '编码不能为空', trigger: 'blur'}
          ],
          suite_name: [
            { required: true, message: '名称不能为空', trigger: 'blur'}
          ],
          suite_desc: [

          ]
        },
        env_column: [
          {title: '环境编码', key: 'suite_code' },
          {title: '环境名称', key: 'suite_name' },
          {title: '机房', key: 'addr_name' },
          {title: '用途', key: 'type_name' },
          {title: '可用域', key: 'region_name' },
          {title: '更新时间', key: 'update_time' },
          // {title: '可用性', key: 'suite_is_active' },
          {
            title: '可用性',
            key: 'suite_is_active',
            width: 100,
            align: "center",
            render: (h, params) => {
              return h('span', params.row.suite_is_active == 1 ? '可用':'不可用')
            }
          },
          {
            title: '操作',
            key: 'handle',
            width: 70,
            align: 'center',
            render: (h, params) => {
              return h('div', [
                h('Button', {
                  attrs: {
                    class: 'ivu-btn ivu-btn-primary ivu-btn-small'
                  },//button-编辑-attrs
                  props: {
                    type: 'primary',
                    size: 'small',
                  },//button-编辑-props
                  style: {
                    marginRight: '6px',
                  },//button-编辑-style
                  on: {
                    click: () => {
                      this.modal_env = true
                      this.form_env = params.row
                      this.region_model = this.form_env.region_name
                      this.region_id_model = this.form_env.region_id
                      this.addr_name_model = this.form_env.addr_name
                      this.type_name_model = this.form_env.type_name
                      this.suite_is_active_model = this.form_env.suite_is_active == 1 ? true : false
                    }
                  }//button-编辑-on
                }, '编辑'),//button-编辑
              ]);//return
            }//render
          }//操作
        ],
        env_data: [],
        region_list: [],
        region_model: '',
        region_id_model: '',
        addr_name_model: '',
        type_name_model: '',
        suite_is_active_model: '',
      }
    },//data

    methods: {
      env_confirm () {
        this.modal_env = false
        this.form_env.region_id = this.region_id_model
        this.form_env.suite_is_active = this.suite_is_active_model
        createEnvMgt(this.form_env).then(res => {
          console.log(JSON.stringify(res))
          if (res.status === 200) {
            this.init_suite_info()
            this.$Message.success('录入成功')
          } else {
            this.$Message.error(res.data.msg)
          }
        })
      },
      env_cancel () {
        this.modal_env = false
      },
      region_model_change(val){
        for(let i = 0; i < this.region_list.length; i++){
          let region_obj = this.region_list[i]
          if(region_obj.region_name == val){
            this.region_id_model = region_obj.id
            this.addr_name_model = region_obj.addr_name
            this.type_name_model = region_obj.type_name
          }
        }
      },
      init_suite_info(){
        getEnvInfo().then(res => {
          this.env_data = res.data.data["data_list"]
        })
      }
    },//methods
    mounted () {
      this.init_suite_info()

      getRegionInfo().then(res => {
        this.region_list = res.data.data["data_list"]
      })
    }//mounted
  }//default
</script>

<style>

</style>
