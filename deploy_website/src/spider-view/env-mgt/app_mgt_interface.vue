<template>
  <Card shadow>
    <div>
      <div class="message-page-con message-category-con">
        <Input @keyup.enter.native="search_apps" prefix="ios-search" placeholder="应用名称或IP" v-model="appSearch"
               style="width:12em; margin-right: 1em"/>
        <Button size="small" type="primary" @click="search_apps">搜索</Button>
        <Scroll style="margin-top: 1em" :height="window_height">
          <ul class="ivu-menu ivu-menu-light ivu-menu-vertical" v-for="item in app_list" :app_list="app_list"
              :key="item">
            <li class="ivu-menu-item" @click="clickPanel(item)">{{item}}</li>
          </ul>
        </Scroll>
      </div>
    </div>
    <div class="message-page-con message-view-con">
        <div>
          <i-col>
            <Tag>应用接口管理: <span style="color: #2d8cf0">{{this.$store.state.user.role_project}}</span></Tag>
          </i-col>
          <Divider>{{app_name}}</Divider>
    
          <i-col v-if="app_name" style="margin-top: 1em;">
            <Select
              placeholder="分支"
              v-model="branch_name"
              filterable clearable style="width: 12em; font-size: 1rem; margin-right: 1em; color: #2d8cf0;"
              @on-change="branch_name_select_change">
              <Option v-for="item in branch_info_list" :value="item.branch_name" :key="item.branch_name">
                {{ item.branch_name }}
              </Option>
            </Select>
            <Select
              placeholder="接口路径"
              v-model="interface_path"
              filterable clearable style="width: 12em; font-size: 1rem; margin-right: 1em; color: #2d8cf0;"
              @on-change="interface_path_select_change">
              <Option v-for="(item,index) in app_interface_info" :value="item.interface_path" :key="index">
                {{ item.interface_path }}
              </Option>
            </Select>
            <Select
              placeholder="方法名(重复的可任选)"
              v-model="interface_method"
              filterable clearable style="width: 12em; font-size: 1rem; margin-right: 1em; color: #2d8cf0;"
              @on-change="method_select_change">
              <Option v-for="(item,index) in app_interface_info" :value="item.interface_method" :key="index">
                {{ item.interface_method }}
              </Option>
            </Select>
            <!-- <Input @keyup.enter.native="search_apps" prefix="ios-search" placeholder="接口名" v-model="appSearch"
               style="width:12em; margin-right: 1em"/> -->
            <Button size="small" type="primary" @click="search_interface">搜索</Button> 
          </i-col>
        </div>
        <appInterfaceInfoTable ref="app_interface_info_table"></appInterfaceInfoTable>
      </div>
  </Card>
</template>

<script>
  // import maintenanceCmd  from "./components/maintenance-cmd";
  import appInterfaceInfoTable from "./components/app_interface_info_table";
  import {
    getModuleName,getAppInterfaceApi,getAppBranchExistInterfaceApi,getAppBranchInfoApi,getNoPermissionModuleName
  } from '@/spider-api/prod-publish/publish-info'
  import store from '../../store'

  export default {
    name: 'app_mgt_interface',
    components: {
      // PublishStat
      // maintenanceCmd,
      appInterfaceInfoTable
    },
    data() {
      return {
        window_height: 500,
        appSearch: '',
        app_name: '',
        all_app_list: [],
        app_list: [],
        app_info_list: [],
        branch_info_list:[],
        branch_name:'',
        data: [],
        app_data: [],
        app_interface_info:[],
        interface_path:'',
        interface_method:''    
      }
    },
    watch: {
      
      
    },
    methods: {
      //打开日志查看tab
      
      //刷新节点是否一致状态
      search_apps() {
        let data = {'search': this.appSearch}
        getNoPermissionModuleName(this.appSearch).then(res => {  
        this.app_list = res.data.data 
        })
        // getProdAppData(data).then(res => {
        //   if (res.data.code === 0) {
        //     this.all_app_list = res.data.data
        //     this.app_list = this.all_app_list
        //   } else {
        //     this.$Message.error('获取应用列表失败');
        //   }
        // })
      },
      clickPanel(val) {
        // appBranchInfoApi(val).then(res => {
        //   console.log("9999999999999999999999999999999999")
        //   console.log(res.data.data)
        // })
        this.branch_info_list = []
        if (val.length) {
          this.app_name = val
          // this.$refs.app_interface_info_table.getAppPublishInfo(this.app_name)
          getAppBranchInfoApi(this.app_name).then(res => {
          this.branch_info_list = res.data.data
        })
        }
      },
      interface_path_select_change(){
        this.$refs.app_interface_info_table.getAppInterfaceinfo(this.app_name,this.branch_name,this.interface_path,this.interface_method)
      },
      method_select_change(){
        this.$refs.app_interface_info_table.getAppInterfaceinfo(this.app_name,this.branch_name,this.interface_path,this.interface_method)
      },
      branch_name_select_change(){
        this.interface_path = ''
        this.$refs.app_interface_info_table.getAppInterfaceinfo(this.app_name,this.branch_name,this.interface_path)
        getAppInterfaceApi(this.app_name,this.branch_name).then(res => {
          this.app_interface_info = res.data.data
          // this.$refs.app_interface_info_table.app_interface_list = this.app_interface_info
        })
      },
      search_interface(){
      this.$refs.app_interface_info_table.getAppInterfaceinfo(this.app_name,this.branch_name,this.interface_path,this.interface_method)
    },
    },
    beforeMount() {
      this.window_height = window.innerHeight - 220
    },
    mounted() {
      if(this.$route.query.userName){
        this.$store.state.user.userName = this.$route.query.userName
      }
      console.log(this.$store.state.user.userName)
      // console.log(this.$route.query.user)
      getAppBranchExistInterfaceApi().then(res => {
        console.log('-----------------------------------------------')
        console.log(res.data.data)
        this.app_info_list = res.data.data
        console.log()
      }),
      getNoPermissionModuleName().then(res => {  
        this.app_list = res.data.data 
        })
      // this.$refs.app_interface_info_table.getAppPublishInfo()
    },
    destroyed() {
    }
  }
</script>

<style scoped lang="less">
  .message-page {
    &-con {
      // height: ~"calc(100vh - 176px)";
      min-height: 500;
      display: inline-block;
      vertical-align: top;
      position: relative;

      &.message-category-con {
        border-right: 1px solid #e6e6e6;
        width: 18em;
        height: auto;
      }

      &.message-view-con {
        position: absolute;
        left: 21em;
        top: 1em;
        right: 1em;
        bottom: 2em;
        overflow: auto;
        padding: 1em 1em 0;

        .message-view-header {
          margin-bottom: 20px;

          .message-view-title {
            display: inline-block;
          }

          .message-view-time {
            margin-left: 20px;
          }
        }
      }
    }
  }
</style>
