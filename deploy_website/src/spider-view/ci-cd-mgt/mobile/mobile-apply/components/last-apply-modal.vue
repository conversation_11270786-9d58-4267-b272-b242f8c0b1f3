<template>
  <div>
    <Button
      ghost
      type="primary"
      style="text-align: left; display: inline-block; width:120px;margin-top:-1px"
      v-show="lastApplyButtonShow"
      @click="getLastApplyInfo"
    >最近一次申请详情
    </Button>
    <Modal
      v-model="modal_last_apply_info"
      title="最近一次申请详情">
      <div v-if="lastApplyInfoStatus=='true'">
        <ButtonGroup>
          <Button v-for="(joburl,index) in lastApplyInfoReturnMessage"
                  @click="openJenkinsUrl(joburl)"
                  type="text">
            {{joburl}}
          </Button>
        </ButtonGroup>
      </div>
      <div v-else>
        <p>{{ lastApplyInfoReturnMessage}}</p>
      </div>
    </Modal>
  </div>
</template>

<script>
  import {
    getLastApplyInfoApi,
  } from "@/spider-api/h5";

  export default {
    name: "lastApplyModal",
    props: {
      branch_version: {
        type: String,
        defaults: ''
      },
      environment: {
        type: String,
        defaults: ''
      },
      group: {
        type: String,
        defaults: ''
      },
      tag_name: {
        type: String,
        defaults: ''
      }
    },
    computed: {},
    data() {
      return {
        modal_last_apply_info: false,
        lastApplyButtonShow: false,
        lastApplyInfoStatus: '',
        lastApplyInfoReturnMessage: ''
      }
    },
    methods: {
      openJenkinsUrl(joburl) {
        window.open(joburl)
      },
      /**
       * 点击【最近一次申请详情】按钮
       */
      getLastApplyInfo() {
        this.modal_last_apply_info = true
        let req = {
          'branch_version': this.branch_version,
          'suite_code': this.environment,
          'group_name': this.group,
          'tag_name': this.tag_name
        };
        getLastApplyInfoApi(req).then(res => {
          this.lastApplyInfoStatus = res.data.data.status
          this.lastApplyInfoReturnMessage = res.data.data.return_msg
          console.log("==========" + this.lastApplyInfoStatus)
          console.log("==========" + this.lastApplyInfoReturnMessage)
        })

      },
    },
    created() {

    }
  }
</script>

<style scoped>

</style>
