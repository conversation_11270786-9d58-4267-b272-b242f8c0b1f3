<template>
  <div>
    <Col v-if="showPiggyParams" span="9">
      <Card>
        <Row>
          <i-col style="margin: 10px;text-align: right" span="8">
            <span style="text-align: right; display: inline-block;">piggy起始版本：</span>
          </i-col>
          <Col span="14">
            <!-- @on-change="myOnchange" -->
            <Select v-model="piggy_start_ver" @on-change="piggy_version_change">
              <Option v-if="environment != 'prod'"
                      v-for="(item,index) in piggy_start_ver_list"
                      :value="item.value"
                      :key="index"
              >{{ item.label }}
              </Option>
              <!-- disabled -->
              <Option disabled v-if="environment == 'prod'"
                      v-for="(item,index) in piggy_start_ver_list"
                      :value="item.value"
                      :key="index"
              > {{ item.label }}
                <span v-if="item.value == '3.5.1'" style="float:right;color:#ccc">默认</span>
              </Option>
            </Select>
          </Col>
        </Row>
        <Row>
          <i-col style="margin: 10px;text-align: right" span="8">
            <span style="text-align: right; display: inline-block;">piggy结束版本：</span>
          </i-col>
          <Col span="14">
            <Input v-model="piggy_end_ver" placeholder="请输入">
            </Input>
          </Col>
        </Row>
        <Row>
          <i-col style="margin: 10px;text-align: right" span="8">
            <span style="text-align: right; display: inline-block;">nf-piggy版本：</span>
          </i-col>
          <Col span="14">
            <Select v-model="nf_piggy_ver" @on-change="piggy_version_change" filterable>
              <Option
                v-for="(item,index) in nf_piggy_ver_list"
                :value="item.value"
                :label="item.label"
                :key="index"
              >{{ item.label }}
              </Option>
            </Select>
          </Col>
        </Row>
        <Row type="flex" justify="end" class="code-row-bg">
          <Col span="15">
            <p style="color: red;">nf会默认选择线上版本绑定</p>
          </Col>
        </Row>
        <Row>
          <i-col style="margin: 10px;text-align: right" span="8">
            <span style="text-align: right; display: inline-block;">打包类型：</span>
          </i-col>
          <Col span="14">
            <Checkbox v-model="piggy_is_silent" true-value="1" style="margin-top: 10px"
            >静默
            </Checkbox>
          </Col>
        </Row>
      </Card>
    </Col>
  </div>
</template>

<script>
  import store from "@/spider-store";
  import {findDistVersion, getErrNoticeShowTime} from "@/spider-api/h5";

  export default {
    name: "piggyParam",
    props: {
      environment: {
        type: String,
        defaults: ''
      },
      branch_version: {
        type: String,
        defaults: ''
      }
    },
    computed: {},
    data() {
      return {
        showPiggyParams: false,
        piggy_start_ver: '',
        piggy_start_ver_list: [],
        piggy_end_ver: '',
        piggy_end_ver_list: [],
        nf_piggy_ver: '',
        nf_piggy_ver_list: [],
        //不勾选，默认非静默
        piggy_is_silent: '0',
      }
    },
    methods: {
      piggy_version_change() {
         if (this.environment != 'prod') {
           this.piggy_end_ver = this.piggy_start_ver
         }
      },
      change_piggy_end_version() {
        if (this.environment == 'beta' || this.environment == 'vps' || this.environment == 'pre') {
          if (this.piggy_start_ver != '') {
            this.piggy_end_ver = this.piggy_start_ver
          }
        } else if (this.environment == 'prod') {
          //因为prod下拉框不能选，所以控制不能写在onchange事件中
        }
      },
      piggy_init() {
        let vm = this;
        window.setTimeout(function(){
          let version_param = {
          'iteration_id': store.state.iterationID,
          'br_name': vm.branch_version,
          'suite_code': vm.environment,
        };
        findDistVersion(version_param).then(res => {
          vm.piggy_start_ver_list = res.data.data.piggy_begin_ver_list
          vm.piggy_end_ver_list = res.data.data.piggy_end_ver_list
          vm.nf_piggy_ver_list = res.data.data.nf_piggy_ver_list

          if (vm.environment == 'prod') {
            console.log('====== prod ==========')
            vm.piggy_start_ver = '3.5.1'
            if (vm.piggy_start_ver_list.length > 0) {
              //找到最新的一条记录
              let piggyTemp = vm.piggy_start_ver_list[0].value
              let piggyTempArr = piggyTemp.split('.')
              vm.piggy_end_ver = vm.add_one_upper(piggyTempArr)
            }
          }
          if (vm.environment == 'vph' || vm.environment == 'vps' || vm.environment == 'pre') {
          if (vm.piggy_start_ver != '') {
            vm.piggy_end_ver = vm.piggy_start_ver
          }
        }
        }).catch(err => {
          vm.$Message.error(err)
        })
        },0);

      },
      /**
       * 满十进一 eg:20.0.9 --> 20.1.0     20.9.9 --> 21.0.0
       */
      add_one_upper(arr) {
        //arr 确定只有 length = 3
        if (arr[2] < 9) {
          arr[2] = parseInt(arr[2]) + 1 //直接加会变成字符串拼接
        } else if (arr[2] == 9) {
          arr[2] = 0
          arr[1] = parseInt(arr[1]) + 1
          //看看 arr[1] 是否也该进位了？
          if (arr[1] > 9) {
            arr[1] = 0
            arr[0] = parseInt(arr[0]) + 1
          }
        }
        let usefulStr = ''
        for (let i of arr) {
          usefulStr = usefulStr + i + '.'
        }
        //去掉最后一个 点
        usefulStr = usefulStr.substring(0, usefulStr.length - 1)
        return usefulStr
      },
      checkPiggyParam() {
        if (this.showPiggyParams) {
          if (!this.piggy_start_ver) {
            this.$Notice.error({
              desc: '请输入piggy起始版本',
              duration: getErrNoticeShowTime(),
            });
            return false;
          }
          if (!this.piggy_end_ver) {
            this.$Notice.error({
              desc: '请输入piggy结束版本',
              duration: getErrNoticeShowTime(),
            });
            return false
          }
          if (this.environment == 'prod') {
            if (this.piggy_start_ver_list[0].value >= this.piggy_end_ver) {
              this.$Notice.error({
                desc: 'piggy结束版本应大于起始版本',
                duration: getErrNoticeShowTime(),
              });
              return false
            }
            if (!this.nf_piggy_ver) {
              this.$Notice.info({
                desc: '生产环境必须选择nf-piggy版本',
                duration: getErrNoticeShowTime(),
              });
              return false
            }
          }
          if (!this.nf_piggy_ver) {//若没填值则直接拿已归档版本
            for (let i of this.nf_piggy_ver_list) {
              if (i.label.indexOf('(已归档)') != -1) {
                this.nf_piggy_ver = i.value
                break;
              }
            }
          }
        }
        return true
      },
      getPiggyParamForApply(iterationID, cc, appName) {
        return {
          'suite_code': this.environment,
          'begin_ver': this.piggy_start_ver,
          'end_ver': this.piggy_end_ver,
          'iteration_id': iterationID,
          'app_name': appName,
          'is_silent': this.piggy_is_silent,
          'cc': cc,
          'br_name': this.branch_version,
          'nf_app_name': 'nf-piggy',
          'nf_br_name': this.nf_piggy_ver
        }
      },
      getPiggyParamForMyOnchange() {
        return {
          'piggy_start_ver': this.piggy_start_ver,
          'piggy_end_ver': this.piggy_end_ver,
          'piggy_is_silent': this.piggy_is_silent
        }
      }
    },
    created() {

    }
  }
</script>

<style scoped>

</style>
