<template>
  <div>
    <Col v-if="showAppParams" span="9" style="margin-right: 10px">
      <Card>
        <Row>
          <tag>
            <slot></slot>
          </tag>
        </Row>
        <Row>
          <i-col style="margin: 10px;text-align: right" span="8">
            <span style="text-align: right; display: inline-block;">App版本名称：</span>
          </i-col>
          <Col span="11">
            <Input v-model="appParamForm.appVersion" placeholder="请输入App版本名称" clearable></Input>
          </Col>
        </Row>
        <Row style="margin-top: 0px">
          <i-col style="margin: 10px;text-align: right" span="8">
            <span style="text-align: right; display: inline-block;">H5资源包环境：</span>
          </i-col>
          <Col span="11">
            <Select v-model="appParamForm.h5Env"
                    @on-change="$emit('selectedZipByEnv', appType,appParamForm.h5Env,true)"
                    placeholder="请选择H5资源包对应环境">
              <Option v-for="(item,index) in environment_list"
                      :value="item.value"
                      :key="index">
                {{ item.label }}
              </Option>
            </Select>
          </Col>
        </Row>
        <Row style="margin-top: 0px">
          <i-col style="margin: 10px;text-align: right" span="8">
            <span style="text-align: right; display: inline-block;">H5资源包版本：</span>
          </i-col>
          <Col span="11">
            <Select v-model="appParamForm.h5ZipVersion"  ref="fundH5ZipQuery" 
                    @on-change="$emit('findH5version', h5iterappName,appParamForm.h5Env,appParamForm.h5ZipVersion)"
                    placeholder="请选择当前环境下的H5资源包版本">
              <Option v-for="(item,index) in h5ZipVersionList"
                      :value="item.value"
                      :key="index">
                {{ item.label}}
              </Option>
            </Select>
          </Col>
        </Row>
        <Row>
          <i-col style="margin: 10px;text-align: right" span="8">
            <span style="text-align: right; display: inline-block;">H5资源包迭代版本：</span>
          </i-col>
          <Col span="11" style="margin: 0 auto;">
            <p style="margin: 10px auto; text-align: right; display: inline-block;">  {{h5iterversion}}</p>
          </Col>
        </Row>
      </Card>
    </Col>
  </div>
</template>

<script>
  import store from "@/spider-store";
  import {findH5HDDistVersionByEnv} from "@/spider-api/h5";
  export default {
    name: "appParam",
    props: {
      selectedZipByEnv:Function,
      findH5version:Function,
      environment_list:{
        type:Array,
        defaults:[]
      },
      h5ZipVersionList:{
        type:Array,
        defaults:[]
      },
      appType:{
        type:String,
        defaults:''
      }
    },
    computed: {},
    data() {
      return {
        showAppParams: false,
        appParamForm: {},
        h5iterversion:'',
        h5iterappName:'',
      }
    },
    methods: {
   
    },
    created() {

    }
  }
</script>

<style scoped>

</style>
