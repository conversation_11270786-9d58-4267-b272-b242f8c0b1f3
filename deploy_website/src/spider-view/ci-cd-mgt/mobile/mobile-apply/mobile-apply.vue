<template>
    <div>
        <Card>
            <Row style="margin-top: 10px">
                <i-col style="margin: 10px;" span="2">
                    <span style="display: inline-block;width:60px;text-align: right;">组：</span>
                </i-col>
                <Col span="3">
                    <span style="margin: 10px; text-align: left; display: inline-block; ">{{ group }}</span>
                </Col>
                <i-col style="margin: 10px;text-align: right" span="2">
                    <span style="text-align: right; display: inline-block;">分支版本：</span>
                </i-col>
                <Col span="8">
                    <span style="margin: 10px;text-align: left; display: inline-block;">{{ branch_version }}</span>
                </Col>
                <i-col v-show="show_tag" style="margin: 10px;text-align: right" span="2">
                    <span style="text-align: right; display: inline-block;">TAG版本：</span>
                </i-col>
                <i-col v-show="show_tag" style="margin: 10px" span="3">
                    <span style="text-align: left; display: inline-block;">{{ tag_name }}</span>
                </i-col>
                <i-col style="margin: 10px" span="7">
                    <span
                        style="text-align: left; display: inline-block;color: red">注：若以下应用的配置存放路径有改变，请及时告知</span>
                </i-col>
            </Row>
            <Row style="margin-top: 10px">
                <i-col style="margin: 10px;" span="2">
                    <span style="display: inline-block;width:60px;text-align: right;">环境：</span>
                </i-col>
                <Col span="10">
                    <Select v-model="environment" @on-change="myOnchange">
                        <Option
                            v-for="(item,index) in environment_list"
                            :value="item.value"
                            :key="index"
                        >{{ item.label }}
                        </Option>
                    </Select>
                </Col>
                <Col style="margin-left: 20px" span="2">
                    <EnvDescModal ref="env_desc_modal"></EnvDescModal>
                </Col>
                <Button style="margin-left: 2ex" type="dashed" @click="show_img">移动端上线示意图
                    <Icon type="ios-alert-outline" size="18"/>
                </Button>
                <Modal
                    title=" "
                    width="1000"
                    v-model="show_img_val"
                    :mask-closable="true">
                    <div style="width: 100%">
                        <img :src="imgUrl1" style="width: 100%"/>
                    </div>
                </Modal>
            </Row>
            <Row style="margin-top: 10px">
                <EmailSelect :isMultiple="false"
                             @selectChange="emailSelectChange" ref="confirm_people">
                    <span style="display: inline-block;width:60px;text-align: right;" slot>确认人：</span>
                </EmailSelect>
                <!--<i-col style="margin: 10px;" span="2">
                  <span style="display: inline-block;width:60px;text-align: right;">确认人：</span>
                </i-col>
                <Col span="10">
                  <Select v-model="proposer" filterable clearable @on-change="myOnchange" ref="selectProposer">
                    <Option v-for="(item,index) in allFilterMails" :value="item" :key="index">{{ item }}</Option>
                  </Select>
                </Col>-->
            </Row>
            <Row style="margin-top: 10px">
                <EmailSelect :isMultiple="true"
                             @selectChange="emailSelectChange" ref="cc_people">
                    <span style="display: inline-block;width:60px;text-align: right;" slot>抄送人：</span>
                </EmailSelect>
                <!--<i-col style="margin: 10px;" span="2">
                  <span style="display: inline-block;width:60px;text-align: right;">抄送人：</span>
                </i-col>
                <Col span="10">
                  <Select v-model="cc" multiple filterable @on-change="myOnchange">
                    <Option v-for="(item,index) in allFilterMails" :value="item" :key="index">{{ item }}</Option>
                  </Select>
                </Col>-->
                <Col style="margin-left: 20px" span="1">
                    <Button
                        ghost
                        type="primary"
                        style="text-align: left; display: inline-block; width:60px;margin-top:-1px"
                        @click="continue_apply"
                    >申请
                    </Button>
                </Col>
                <Col style="margin-left: 20px" span="2">
                    <Button
                        ghost
                        type="primary"
                        style="text-align: left; display: inline-block; width:100px;margin-top:-1px"
                        @click="cancel_prod_apply"
                    >取消产线申请
                    </Button>
                </Col>
                <Col style="margin-left: 20px" span="2">
                    <LastApplyModal ref="last_apply_modal"
                                    :branch_version="branch_version"
                                    :environment="environment"
                                    :group="group"
                                    :tag_name="tag_name"></LastApplyModal>
                </Col>
                <Col style="margin-left: 20px " span="2">
                    <div>
                        <h2>申请历史<a
                            href="http://paas.hongkou.howbuy.com/report/#/notebook/2GJU8SF6J/paragraph/20210928-163206_681554684?asIframe"
                            target="abc">查询</a></h2>
                    </div>
                </Col>
            </Row>
            <Row style="margin-top: 10px">
                <i-col style="margin: 5px" span="2">

                    <span style="text-align: right; display: inline-block;width:60px;">申请说明:</span>
                </i-col>
                <Col style="margin-left: 10px" span="1">
                    <Input v-model="prod_content" type="textarea" :rows="3" placeholder="输入产线说明"
                           style="width: 200px"/>
                </Col>
            </Row>
            <Row style="margin-top: 10px">
                <FundParam ref="fund_param"
                           :environment="environment"
                           :branch_version="branch_version"/>
                <PiggyParam ref="piggy_param"
                            :environment="environment"
                            :branch_version="branch_version"/>
                <!--掌上基金参数存放位置-->
                <AppParam ref="fund_app_param"
                          @selectedZipByEnv="selectedZipByEnv"
                          @findH5version="findH5version"
                          :environment_list="environment_list"
                          :appType="fundType"
                          :h5ZipVersionList="h5FundZipVersionList">掌上基金
                </AppParam>

                <!--app储蓄罐参数存放位置-->
                <AppParam ref="piggy_app_param"
                          @selectedZipByEnv="selectedZipByEnv"
                          @findH5version="findH5version"
                          :environment_list="environment_list"
                          :appType="piggyType"
                          :h5ZipVersionList="h5PiggyZipVersionList">储蓄罐
                </AppParam>
                <AppParam ref="crm_app_param"
                          @selectedZipByEnv="selectedZipByEnv"
                          @findH5version="findH5version"
                          :environment_list="environment_list"
                          :appType="fundType"
                          :h5ZipVersionList="h5PiggyZipVersionList">CRM-IOS
                </AppParam>
                <MiniProgramParams v-show="is_mini_program" :upload_version="upload_version" :upload_desc="upload_desc"
                                   @set_upload_version="set_upload_version" @set_upload_desc="set_upload_desc">

                </MiniProgramParams>
            </Row>
            <Row style="margin-top: 10px"></Row>
            <Row style="margin-top: 10px">
                <tables stripe v-model="app_array" :columns="columns"
                        @on-selection-change="apply_list_select">
                </tables>
            </Row>

            <Modal
                v-model="alert"
                title="以下应用正在申请，是否要继续申请？"
                :columns="alerttablecolums"
                :tableData="alertmodal"
            >
                <div>
                    <Row>
                        <Table
                            :columns="alerttablecolums"
                            :data="alertmodal"
                        >
                        </Table>
                    </Row>
                </div>

                <div slot="footer">
                    <Button @click="apply()" :disabled="continue_apply_btn">继续申请</Button>
                    <Button @click="cancel_apply()">取消申请</Button>
                </div>
            </Modal>

            <Modal v-model="modal_cancel_pro_apply">
                <p slot="header" style="color:gray;">
                    <Icon type="md-clipboard"></Icon>
                    <span>取消产线申请</span>
                </p>
                <span>{{modal_cancel_apply_title}}</span>
                <div slot="footer">
                    <Button @click="closeProApplyModal">关闭</Button>
                    <Button ghost type="error" @click="cancelProApplyAck">确定</Button>
                </div>
            </Modal>

            <ConfirmModal ref="hd_confirm_modal"
                          titleInfo="以下为正在进行灰度的分支，请于相关人员确认项目情况无冲突后继续申请！"
                          :IsModalred="true"
                          :columns="conflictTableColumns"
                          :tableData="conflictTableData"
                          @continue="hdApplyCheck"></ConfirmModal>

            <ConfirmModal ref="hd_check_modal"
                          titleInfo="灰度申请检查没有通过，请处理完毕后再行申请！！！"
                          :columns="hdCheckTableColumns"
                          :tableData="hdCheckTableData"></ConfirmModal>

            <ConfirmModal ref="app_check_modal"
                          titleInfo="app申请检查没有通过，请处理完毕后再行申请！！！"
                          :columns="appCheckTableColumns"
                          :tableData="appCheckTableData"></ConfirmModal>


        </Card>
    </div>

</template>
<script>
import Tables from "@/components/tables";
import EmailSelect from "@/spider-components/publish-components/email-select/email-select";
import MiniProgramParams from "@/spider-view/ci-cd-mgt/mobile/mobile-publish-apply/components/mini-program-params";

import FundParam from "./components/fund-param";
import AppParam from "./components/app-param";
import PiggyParam from "./components/piggy-param";
import ConfirmModal from "./components/confirm-modal";
import EnvDescModal from "./components/env-desc-modal";
import LastApplyModal from "./components/last-apply-modal";
import {createH5AppBindNfAppApi, getH5AppBindNfAppApi} from "@/spider-api/h5-ci-cd/nf-app";
import store from "@/spider-store";
import {
    h5IterConfirmStatusApi
} from "@/spider-api/publish";
import {
    h5ProApplyNotice,
    getEmailAddresses, cancelProApply, cancel_prod_apply
} from "@/spider-api/iter-plan";
// import {getEmailAddresses} from "@/api/iterative-plan";
import {
    NoticeApplyByWechat,
    externalServiceResult,
    h5PublishApplyApiGet,
    h5PublishApplyApi,
    miniPublishApplyApi,
    appPublishApplyApi,
    appTagPublishApplyApi,
    testPublishCheckApi,
    userAction,
    userActionGet,
    HdStatusCheckApi,
    findH5ZipVersionByEnv,
    getErrNoticeShowTime,
    getSuccessNoticeShowTime,
    getInfoNoticeShowTime,
    CheckH5ZipVersionWhetherPublish,
    findH5HDDistVersionByEnv,
} from "@/spider-api/h5";
import {
    formatDate
} from "@/spider-api/h5-common";

export default {
    name: "mobile-apply",
    components: {
        Tables,
        FundParam,
        AppParam,
        PiggyParam,
        ConfirmModal,
        EnvDescModal,
        LastApplyModal,
        EmailSelect,
        MiniProgramParams
    },
    data() {
        return {
            upload_version: "",
            upload_desc: "",
            is_mini_program: false,
            prod_content: '',
            alert: false,
            alertmodal: [],
            imgUrl1: require("../../../../img/移动端上线示意图.png"),
            show_img_val: false,
            fundType: 'fund',
            piggyType: 'piggy',
            actionItem: 'publish_apply',
            tag_name: '',
            show_tag: false,
            modal_cancel_pro_apply: false,
            modal_cancel_apply_title: '',
            timeout: '',
            group: '',
            branch_version: '',
            environment: '',
            hd_conflict_msg: '',
            branch_type: '',
            conflictTableData: [],
            showokcontent: '',
            continue_apply_btn: false,
            conflictTableColumns: [
                {
                    title: '灰度环境的分支',
                    key: 'br_name',
                    align: 'center'
                },
                {
                    title: '灰度环境的应用',
                    key: 'app_name',
                    align: 'center'
                },
                {
                    title: '申请人',
                    key: 'user_name',
                    align: 'center'
                },
                {
                    title: '申请时间',
                    key: 'operate_time',
                    align: 'center',
                    render: (h, params) => {
                        return h('div',
                            formatDate(new Date(params.row.operate_time), 'yyyy-MM-dd hh:mm')
                        )
                    }
                }
            ],
            hdCheckTableData: [],
            hdCheckTableColumns: [
                {
                    title: '检查项',
                    key: 'msg',
                    align: 'center',
                },
                {
                    title: '检查是否通过',
                    key: 'status',
                    align: 'center',
                    render: (h, params) => {

                        if (params.row.status.indexOf("success") >= 0) {
                            var color = "green";
                        } else if (params.row.status.indexOf("failure") >= 0) {
                            var color = "red";
                        }
                        return h("div", [
                            h("p",
                                {props: {}, style: {color: color}},
                                params.row.status)
                        ]);
                    }
                }
            ],
            alerttablecolums: [
                {
                    title: '应用',
                    key: 'app_name'
                },
                {
                    title: '状态',
                    key: 'status_display',
                    render: (h, params) => {

                        return h("div", [
                            h("p",
                                {props: {}, style: {color: 'red'}},
                                params.row.status_display)
                        ]);
                    }
                },
                {
                    title: '上一次申请时间',
                    key: "operate_time",
                    //sortable: true,
                    render: (h, params) => {
                        let value = params.row.operate_time
                        if (value) {
                            value = formatDate(new Date(params.row.operate_time), 'yyyy-MM-dd hh:mm:ss')
                        }
                        return h('div', value)
                    }
                },
                {
                    title: '申请提示',
                    key: "alert_content",
                    //sortable: true,
                    render: (h, params) => {
                        return h("div", [
                            h("p",
                                {props: {}, style: {color: params.row.alert_modal_color}},
                                params.row.alert_content)
                        ]);
                    }
                }
            ],
            appCheckTableData: [],
            appCheckTableColumns: [
                {
                    title: '检查项',
                    key: 'msg',
                    align: 'center',
                },
                {
                    title: '检查是否通过',
                    key: 'status',
                    align: 'center',
                    render: (h, params) => {

                        if (params.row.status.indexOf("success") >= 0) {
                            var color = "green";
                        } else if (params.row.status.indexOf("failure") >= 0) {
                            var color = "red";
                        }
                        return h("div", [
                            h("p",
                                {props: {}, style: {color: color}},
                                params.row.status)
                        ]);
                    }
                }
            ],
            environment_list: [
                // {value: 'vph', label: 'vph--灰度环境，验证灰度H5远端'},
                // {value: 'vps', label: 'vps--灰度环境，验证服务端灰度'},
                {value: 'beta', label: 'beta--移动端灰度环境，服务端为产线配置'},
                {value: 'wgq-hd', label: 'wgq-hd--服务端外高桥灰度环境'},
                {value: 'prod', label: 'prod--生产环境'},
                {value: 'pre', label: 'pre--移动端灰度环境，服务端为灰度配置'},
            ],
            proposer: '',
            cc: [],
            add_app: [],
            //app_array 申请页面的数据整合，大家都用它
            app_array: [],
            selected_array: [],
            compile_array: [],
            columns: [
                {
                    type: 'selection',
                    minWidth: 50,
                },
                {title: "应用", minWidth: 100, key: "app_name"},
                {title: "应用描述", minWidth: 100, key: "module_desc"},
                {title: "仓库", minWidth: 160, key: "git_path"},
                {
                    title: "上次申请环境",
                    minWidth: 110,
                    key: "suite_name",
                    render: (h, params) => {
                        let value = params.row.suite_name
                        if (params.row.suite_name == null) {
                            return h('div', '无')
                        } else if (params.row.suite_name != null && params.row.suite_name.length == 0) {
                            return h('div', '无')
                        } else {
                            let str = '';
                            for (let i of value) {
                                str += i
                            }
                            return h('div', str)
                        }
                    }
                },
                {
                    title: "申请时间",
                    minWidth: 150,
                    key: "operate_time",
                    //sortable: true,
                    render: (h, params) => {
                        let value = params.row.operate_time
                        if (value) {
                            value = formatDate(new Date(params.row.operate_time), 'yyyy-MM-dd hh:mm:ss')
                        }
                        return h('div', value)
                    }
                },
                {title: "操作人", minWidth: 100, key: "username"},
                {
                    title: "申请状态", minWidth: 100, key: "status_display",
                    render: (h, params) => {
                        let color = "#2db7f5";
                        if (params.row.status && params.row.status.indexOf("success") >= 0) {
                            color = "green";
                        } else if (params.row.status && params.row.status.indexOf("failure") >= 0) {
                            color = "red";
                        }
                        if (params.row.status && params.row.status.indexOf("running") >= 0) {
                            return h('div',
                                [h('a', {
                                    on: {
                                        click: () => {
                                            let job_url = params.row.job_url
                                            if (job_url) {
                                                window.open(job_url)
                                            } else {
                                                this.$Notice.info({
                                                    title: '暂无详情',
                                                    duration: getInfoNoticeShowTime(),
                                                });
                                            }
                                        }
                                    }
                                }, params.row.status_display)
                                ]);
                        } else {
                            return h("Tooltip", {
                                    props: {
                                        placement: 'top',
                                        content: params.row.message,
                                        "max-width": 500,
                                        transfer: true
                                    }
                                }, [h("p",
                                    {props: {}, style: {color: color}},
                                    params.row.status_display)]
                            );
                        }
                    }
                },
                {title: "确认状态", minWidth: 100, key: "email_status_display"},
                {
                    title: "确认时间", minWidth: 150, key: "affirm_time",
                    render: (h, params) => {
                        let value = params.row.affirm_time
                        if (value == '' || value == null) {

                        } else {
                            value = formatDate(new Date(params.row.affirm_time), 'yyyy-MM-dd hh:mm:ss')
                        }
                        return h('div', value)
                    }
                },
                {title: "确认人", minWidth: 100, key: "assertor"},
                {
                    title: "详情", minWidth: 100, key: "job_url"
                    ,
                    render: (h, params) => {
                        let color = "#2db7f5";
                        if (params.row.status && params.row.status.indexOf("success") >= 0) {
                            color = "green";
                        } else if (params.row.status && params.row.status.indexOf("failure") >= 0) {
                            color = "red";
                        }
                        return h("div", [
                            h(
                                "Button",
                                {

                                    props: {},
                                    style: {
                                        color: color
                                    },
                                    on: {
                                        click: () => {
                                            console.log('')
                                            this.get_ci_info_detail()
                                            if (params.row.job_url == null) {
                                                this.$Message.info({
                                                    content: '暂未生成构建链接，请耐心等待',
                                                    duration: 10
                                                });
                                            } else {
                                                console.log(params.row.job_url)
                                                window.open(
                                                    params.row.job_url
                                                );
                                            }

                                        }
                                    },
                                    attrs: {
                                        class: "ivu-btn ivu-btn-primary ivu-btn-ghost",
                                        title: '构建详情BlueOcean',
                                    },
                                },
                                "详情",
                                params.row.msg,
                            )
                        ]);
                    }
                },
            ],
            h5FundZipVersionList: [],
            h5PiggyZipVersionList: []
        }
    },
    mounted() {
        // this.init()
    },
    computed: {},
    methods: {
        //初始化所有的数据，table，并且回显用户行为数据
        init() {
            this.group = store.state.h5_group
            this.branch_version = store.state.h5_branch_version
            this.tag_name = store.state.tag_name
            this.show_tag = store.state.show_tag
            if (store.state.code_type == "tag") {
                this.environment_list = [
                    // {value: 'vph', label: 'vph--灰度环境，验证灰度H5远端'},
                    // {value: 'vps', label: 'vps--灰度环境，验证服务端灰度'},
                    // {value: 'pre', label: 'pre--灰度环境，和产线配置一致'},
                    // {value: 'wgq-hd', label: 'wgq-hd--外高桥灰度环境，和产线配置一致'}
                    {value: 'pre', label: 'pre--移动端灰度环境，服务端为灰度配置'},
                    {value: 'beta', label: 'beta--移动端灰度环境，服务端为产线配置'},
                    {value: 'wgq-hd', label: 'wgq-hd--服务端外高桥灰度环境'},
                ]
            }
            //查询用户行为  todo 后期整合成一个逻辑，去除不必要的代码
            if (this.group.indexOf("android") != -1 || this.group.indexOf("ios") != -1) {
                this.actionItem = "publish_apply"
            } else {
                this.actionItem = store.state.iterationID + '_' + 'apply'
            }

            let json = []
            let _this = this
            userActionGet(this.actionItem).then(res => {
                if (res.data.data.length > 0) {
                    json = JSON.parse(res.data.data.replace(/'/g, '"'))
                    console.info(json)
                    if (json instanceof Array) {
                        //todo 强烈建议数据一致处理，目前出现行为字段不一致的情况
                        if (this.actionItem == "publish_apply") {
                            this.environment = json[0].suite_code
                        } else {
                            this.environment = json[0].environment
                        }
                        this.$refs.confirm_people.selectValue = json[0].proposer
                        if (json[0].proposer.length > 0) {
                            //this.$refs.selectProposer.query = json[0].proposer
                        }
                        this.$refs.cc_people.selectValue = json[0].cc
                        this.$refs.fund_param.fund_start_ver = json[0].fund_start_ver
                        this.$refs.fund_param.fund_end_ver = json[0].fund_end_ver
                        this.$refs.fund_param.nf_fund_ver = json[0].nf_fund_ver
                        this.$refs.fund_param.fund_is_silent = json[0].fund_is_silent
                        this.$refs.piggy_param.piggy_start_ver = json[0].piggy_start_ver
                        this.$refs.piggy_param.piggy_end_ver = json[0].piggy_end_ver
                        this.$refs.piggy_param.piggy_is_silent = json[0].piggy_is_silent
                    } else {
                        this.environment = json.appEnv
                        this.$refs.confirm_people.selectValue = json.proposer
                        if (json.proposer.length > 0) {
                            //this.$refs.selectProposer.query = json.proposer
                        }
                        this.$refs.cc_people.selectValue = json.cc
                        this.$refs.fund_param.fund_start_ver = json.fund_start_ver
                        this.$refs.fund_param.fund_end_ver = json.fund_end_ver
                        this.$refs.fund_param.nf_fund_ver = json.nf_fund_ver
                        this.$refs.fund_param.fund_is_silent = json.fund_is_silent
                        this.$refs.piggy_param.piggy_start_ver = json.piggy_start_ver
                        this.$refs.piggy_param.piggy_end_ver = json.piggy_end_ver
                        this.$refs.piggy_param.piggy_is_silent = json.piggy_is_silent
                    }
                    if (this.group.indexOf("android") != -1 || this.group.indexOf("ios") != -1) {
                        //alert(JSON.stringify(jsonArray))
                        //第一次进来，给table赋值用户行为信息,查询迭代信息
                        if (this.environment) {
                            h5PublishApplyApiGet(store.state.iterationID, _this.environment, _this.branch_version).then(res => {
                                _this.app_array = res.data.data.ci_info
                                _this.initRowParams(json.requestParams, _this)
                            })
                        }
                    } else {
                        //fund 起始版本回填
                        this.$refs.fund_param.fund_init();
                        //piggy 起始版本回填
                        this.$refs.piggy_param.piggy_init();
                        //第一次进来，给table赋值用户行为信息
                        h5PublishApplyApiGet(store.state.iterationID, this.environment, this.branch_version).then(res => {
                            //我在这里拼装 app_array
                            this.app_array = res.data.data.ci_info
                            console.log("这里调用了")
                            this.init_with_app_array()
                        })
                    }
                } else {
                    h5PublishApplyApiGet(store.state.iterationID, this.environment, this.branch_version).then(res => {
                        this.app_array = res.data.data.ci_info
                    })
                }
                this.waitPolling();
            })
        },
        get_ci_info_detail() {
            h5PublishApplyApiGet(store.state.iterationID, this.environment, this.branch_version).then(res => {
                console.log('获取详情数据')
                console.log(res.data.data)
            })
        }
        ,
        show_img() {
            this.show_img_val = true;
        },
        cancel_apply() {
            this.alert = false
        },
        /**
         * h5模块
         * */

        //获取H5请求参数
        getH5ApplyParam() {
            let iterationID = store.state.iterationID;
            let apply_list = [];
            let apply_obj = {};
            //piggy 和 fund 因为需要 起始终止版本，是否静默，所以单独拿出来二次组装
            //cc 如果没有**************，添加 ["<EMAIL>", "<EMAIL>", __ob__: Observer]
            //todo 建议前后端参数调整，所有类型的参数都在一个维度封装了
            //this.cc = this.add_default_cc(this.cc)
            for (let i = 0; i < this.selected_array.length; i++) {
                if (this.selected_array[i].app_name == 'piggy') {
                    apply_obj = this.$refs.piggy_param.getPiggyParamForApply(iterationID, this.cc, this.selected_array[i].app_name);
                    console.log("===========piggy 申请参数===========")
                    console.log(apply_obj)
                    apply_list.push(apply_obj)
                } else if (this.selected_array[i].app_name == 'fund') {
                    apply_obj = this.$refs.fund_param.getFundParamForApply(iterationID, this.cc, this.selected_array[i].app_name);
                    console.log("===========fund 申请参数===========")
                    console.log(apply_obj)
                    apply_list.push(apply_obj)
                } else if (this.selected_array[i].app_name == 'fund-ios' || this.selected_array[i].app_name == 'fund-android') {
                    //todo  添加app入参数据逻辑  临时解决方案   ，目前参数已经传入后台，但是未进行任务调用处理
                    apply_obj = {
                        'suite_code': this.environment,
                        'iteration_id': iterationID,
                        'br_name': this.branch_version,
                        'proposer': this.proposer,
                        'cc': this.cc,
                        'app_version_name': this.appVersionName,
                        'h5_zip_env': this.h5ZipEnv,
                        'h5_zip_version': this.h5ZipVersion,
                        'app_name': this.selected_array[i].app_name,
                        'begin_ver': '',
                        'end_ver': ''
                    }
                    apply_list.push(apply_obj)
                } else {
                    console.log("==========is_silent")
                    apply_obj = {
                        'suite_code': this.environment,
                        'begin_ver': '',
                        'end_ver': '',
                        'iteration_id': iterationID,
                        'app_name': this.selected_array[i].app_name,
                        'proposer': this.proposer,
                        'cc': this.cc,
                        'is_silent': '1',
                        'br_name': this.branch_version
                    }
                    if (this.is_mini_program) {
                        apply_obj["upload_version"] = this.upload_version
                        apply_obj["upload_desc"] = this.upload_desc
                    }
                    apply_list.push(apply_obj)
                }
            }
            console.log("==============产线申请数据=================")
            console.log(apply_list)
            return apply_list
        },
        /**
         * mobile app模块
         * */
        //app选择h5zip环境查询对应环境下的h5zip版本数据
        selectedZipByEnv(h5AppName, env, type) {
            let _this = this
            findH5ZipVersionByEnv(h5AppName, env).then(res => {
                if (res.data.data) {
                    if (h5AppName == 'fund') {
                        if (type) {
                            _this.$refs.fund_app_param.appParamForm.h5ZipVersion = ''
                        }
                        _this.h5FundZipVersionList = res.data.data
                    } else if (h5AppName == 'piggy') {
                        if (type) {
                            _this.$refs.piggy_app_param.appParamForm.h5ZipVersion = ''
                        }
                        _this.h5PiggyZipVersionList = res.data.data
                    }
                }
            })
        },
        findH5version(h5AppName, env, Version) {
            console.log(this.$refs.fund_app_param.appType)
            console.log(env)
            console.log(Version)
            findH5HDDistVersionByEnv(this.$refs.fund_app_param.appType, env, Version).then(res => {
                console.log(this.h5iterappName)
                this.$refs.fund_app_param.h5iterversion = res.data.data['branch_name']
            })

        },
        //app的应用的必填校验
        checkParamForApp() {
            if (this.$refs.fund_app_param.showAppParams) {
                if (!this.$refs.fund_app_param.appParamForm.appVersion) {
                    this.$Notice.error({
                        desc: 'app版本名称不能为空[掌上基金]',
                        duration: getErrNoticeShowTime(),
                    });
                    return false
                }
                if (!this.$refs.fund_app_param.appParamForm.h5Env) {
                    this.$Notice.error({
                        desc: 'H5资源包环境不能为空[掌上基金]',
                        duration: getErrNoticeShowTime(),
                    });
                    return false
                }
                if (!this.$refs.fund_app_param.appParamForm.h5ZipVersion) {
                    this.$Notice.error({
                        desc: 'H5资源包版本不能为空[掌上基金]',
                        duration: getErrNoticeShowTime(),
                    });
                    return false
                }
            }
            if (this.$refs.piggy_app_param.showAppParams) {
                if (!this.$refs.piggy_app_param.appParamForm.appVersion) {
                    this.$Notice.error({
                        desc: 'app版本名称不能为空[储蓄罐]',
                        duration: getErrNoticeShowTime(),
                    });
                    return false
                }
                if (!this.$refs.piggy_app_param.appParamForm.h5Env) {
                    this.$Notice.error({
                        desc: 'H5资源包环境不能为空[储蓄罐]',
                        duration: getErrNoticeShowTime(),
                    });
                    return false
                }
                if (!this.$refs.piggy_app_param.appParamForm.h5ZipVersion) {
                    this.$Notice.error({
                        desc: 'H5资源包版本不能为空[储蓄罐]',
                        duration: getErrNoticeShowTime(),
                    });
                    return false
                }
            }
            return true
        },
        //获取app客户端参数
        getAppApplyParam() {
            let requestParams = []
            let requestParam = {}
            requestParam.cc = this.cc
            if (requestParam.cc.findIndex(item => item == "<EMAIL>") < 0) {
                requestParam.cc.push("<EMAIL>")
            }
            requestParam.proposer = this.proposer
            requestParam.appEnv = this.environment
            requestParam.appBranch = this.branch_version
            requestParam.iterationID = store.state.iterationID
            requestParam.actionItem = this.actionItem
            let appNameList = []
            for (let row of this.selected_array) {
                if (row.app_name.indexOf('fund') > -1) {
                    appNameList.push(row.app_name)
                    this.$refs.fund_app_param.appParamForm.appName = row.app_name
                    this.$refs.fund_app_param.appParamForm.h5AppName = 'fund'
                    this.$refs.fund_app_param.appParamForm.packageType = row.package_type
                    this.$refs.fund_app_param.appParamForm.repoPath = row.git_path
                    this.$refs.fund_app_param.appParamForm.appEnv = this.environment
                    this.$refs.fund_app_param.appParamForm.appBranch = this.branch_version
                    this.$refs.fund_app_param.appParamForm.actionItem = this.actionItem
                    this.$refs.fund_app_param.appParamForm.iterationID = store.state.iterationID
                    this.$refs.fund_app_param.appParamForm.appTagName = store.state.tag_name
                    this.$refs.fund_app_param.appParamForm.appCodeType = store.state.code_type
                    requestParams.push(this.$refs.fund_app_param.appParamForm)
                }
                if (row.app_name.indexOf('piggy') > -1) {
                    appNameList.push(row.app_name)
                    this.$refs.piggy_app_param.appParamForm.appName = row.app_name
                    this.$refs.piggy_app_param.appParamForm.h5AppName = 'piggy'
                    this.$refs.piggy_app_param.appParamForm.packageType = row.package_type
                    this.$refs.piggy_app_param.appParamForm.repoPath = row.git_path
                    this.$refs.piggy_app_param.appParamForm.appEnv = this.environment
                    this.$refs.piggy_app_param.appParamForm.appBranch = this.branch_version
                    this.$refs.piggy_app_param.appParamForm.actionItem = this.actionItem
                    this.$refs.piggy_app_param.appParamForm.iterationID = store.state.iterationID
                    this.$refs.piggy_app_param.appParamForm.appTagName = store.state.tag_name
                    this.$refs.piggy_app_param.appParamForm.appCodeType = store.state.code_type
                    requestParams.push(this.$refs.piggy_app_param.appParamForm)
                }
                if (row.app_name.indexOf('crm') > -1) {
                    appNameList.push(row.app_name)
                    this.$refs.crm_app_param.appParamForm.appName = row.app_name
                    this.$refs.crm_app_param.appParamForm.h5AppName = 'fund'
                    this.$refs.crm_app_param.appParamForm.packageType = row.package_type
                    this.$refs.crm_app_param.appParamForm.repoPath = row.git_path
                    this.$refs.crm_app_param.appParamForm.appEnv = this.environment
                    this.$refs.crm_app_param.appParamForm.appBranch = this.branch_version
                    this.$refs.crm_app_param.appParamForm.actionItem = this.actionItem
                    this.$refs.crm_app_param.appParamForm.iterationID = store.state.iterationID
                    this.$refs.crm_app_param.appParamForm.appTagName = store.state.tag_name
                    this.$refs.crm_app_param.appParamForm.appCodeType = store.state.code_type
                    requestParams.push(this.$refs.crm_app_param.appParamForm)
                }
            }
            requestParam.appNameList = appNameList
            requestParam.requestParams = requestParams
            return requestParam
        },
        //用户行为记录
        addUserAction(actionItem, actionValue) {
            return new Promise(function (resolve, reject) {
                let req = {
                    action_item: actionItem,
                    action_value: actionValue
                }
                userAction(req).then(res => {
                    resolve(res.data.data)
                }).catch(err => {
                    reject(false)
                })
            })
        },
        //根据用户行为回显模块入参
        initRowParams(params, _this) {
            _this.selected_array = []
            _this.$refs.piggy_app_param.showAppParams = false
            _this.$refs.fund_app_param.showAppParams = false
            _this.$refs.crm_app_param.showAppParams = false
            _this.$refs.fund_param.showFundParams = false
            _this.$refs.piggy_param.showPiggyParams = false

            if (!params) {
                return
            }
            for (let row of params) {
                let index = _this.app_array.findIndex(item => item.app_name == row.appName)
                if (index != -1) {
                    _this.showRowBySelected(this.app_array[index])
                    _this.app_array[index]._checked = true
                    _this.selected_array.push(this.app_array[index])
                    if (row.appName.indexOf('fund') > -1) {
                        _this.$refs.fund_app_param.appParamForm = row
                        _this.$refs.fund_app_param.showAppParams = true
                        _this.selectedZipByEnv('fund', row.h5Env, false)
                    }
                    if (row.appName.indexOf('piggy') > -1) {
                        _this.$refs.piggy_app_param.appParamForm = row
                        _this.$refs.piggy_app_param.showAppParams = true
                        _this.selectedZipByEnv('piggy', row.h5Env, false)
                    }
                    if (row.appName.indexOf('crm') > -1) {
                        _this.$refs.crm_app_param.appParamForm = row
                        _this.$refs.crm_app_param.showAppParams = true

                    }
                }
            }
        },
        /**
         * 公共模块
         * */
        //轮询状态
        waitPolling() {
            console.log("will wait")
            let _this = this
            setTimeout(function () {
                _this.pollingStatus()
            }, 2000)
        },
        /**
         * 执行轮询 2s，修改目前的发布状态
         */
        pollingStatus() {
            let needPool = false
            for (let s of this.app_array) {
                if (s.status != null && s.status.indexOf('running') >= 0) {
                    needPool = true
                    break
                }
            }
            let vm = this
            console.info("needPool=" + needPool)
            //开始新的查询
            if (needPool) {
                console.log(" apply query status")
                h5PublishApplyApiGet(store.state.iterationID, this.environment, this.branch_version).then(res => {
                    console.log(res.data.data)
                    // vm.app_array = res.data.data.ci_info
                    for (let app of vm.app_array) {
                        for (let ci of res.data.data.ci_info) {
                            if (app.app_name == ci.app_name) {
                                app.suite_code = ci.suite_code
                                app.operate_time = ci.operate_time
                                app.username = ci.username
                                app.status_display = ci.status_display
                                app.message = ci.message
                                app.status = ci.status
                            }
                        }
                    }
                    // vm.init_with_app_array()
                    vm.waitPolling()
                })
            }
        },

        //发送领导确认邮件
        sendAffirmEmail() {
            //this.$Spin.show();
            h5IterConfirmStatusApi(store.state.iterationID, this.environment).then(res => {
                if (res.data.status != "success") {
                    let app_name_list = []
                    for (let i of this.selected_array) {
                        app_name_list.push(i.app_name)
                    }
                    //先将发布邮件信息入库，以控制生产发布允许进入
                    console.log('========== email-start =========')
                    if (!this.$refs.fund_param.showFundParams) {
                        this.$refs.fund_param.fund_end_ver = ''
                    }
                    if (!this.$refs.piggy_param.showPiggyParams) {
                        this.$refs.piggy_param.piggy_end_ver = ''
                    }
                    if (this.group == 'h5') {
                        this.Version = ''
                        this.branch = this.getAppApplyParam()['appBranch']
                        console.log('+++++++++++++++++++++++++++++++++++++++')
                        console.log(this.Version)
                        console.log(this.branch)
                        console.log('+++++++++++++++++++++++++++++++++++++++')
                        console.log(this.Version)
                        console.log(this.branch)
                    } else if (this.group == 'howbuy_ios') {
                        if (this.$refs.piggy_app_param.appParamForm.appVersion) {
                            this.Version = this.$refs.piggy_app_param.appParamForm.appVersion
                            this.branch_type = this.$refs.piggy_app_param.appParamForm.h5Env
                        } else if (this.$refs.fund_app_param.appParamForm.appVersion) {
                            this.Version = this.$refs.fund_app_param.appParamForm.appVersion
                            this.branch_type = this.$refs.fund_app_param.appParamForm.h5Env
                        }
                        this.branch = this.getAppApplyParam()['appBranch']
                        this.fundh5ZipVersion = this.$refs.fund_app_param.appParamForm.h5ZipVersion
                        this.piggyh5ZipVersion = this.$refs.piggy_app_param.appParamForm.h5ZipVersion
                        console.log('---------------------------------------------')
                        console.log(this.Version)
                        console.log(this.branch)
                        console.log(store.state.code_type)
                    } else if (this.group == 'howbuy_android') {
                        if (this.$refs.piggy_app_param.appParamForm.appVersion) {
                            this.Version = this.$refs.piggy_app_param.appParamForm.appVersion
                            this.branch_type = this.$refs.piggy_app_param.appParamForm.h5Env
                        } else if (this.$refs.fund_app_param.appParamForm.appVersion) {
                            this.Version = this.$refs.fund_app_param.appParamForm.appVersion
                            this.branch_type = this.$refs.fund_app_param.appParamForm.h5Env
                        }
                        this.branch = this.getAppApplyParam()['appBranch']
                        this.fundh5ZipVersion = this.$refs.fund_app_param.appParamForm.h5ZipVersion
                        this.piggyh5ZipVersion = this.$refs.piggy_app_param.appParamForm.h5ZipVersion
                        console.log('---------------------------------------------')
                        console.log(this.Version)
                        console.log(this.branch)
                        console.log(this.fundh5ZipVersion)
                        console.log(this.piggyh5ZipVersion)
                    }
                    h5ProApplyNotice(store.state.iterationID, this.proposer, 'http://' + window.location.host,
                        this.environment, app_name_list, this.$refs.fund_param.fund_end_ver, this.$refs.piggy_param.piggy_end_ver, this.Version, this.branch, this.fundh5ZipVersion,
                        this.piggyh5ZipVersion, this.branch_type, this.prod_content)
                        .then(result => {
                            if (result.data.status === "success") {
                                this.$Notice.success({
                                    desc: result.data.msg,
                                    duration: getInfoNoticeShowTime(),
                                });
                            } else {
                                this.$Spin.hide();
                                this.$Message.error(result.data.msg);
                            }
                        })
                        .catch(err => {
                            this.$Spin.hide();
                            this.$Message.error(err.response.data.msg);
                        });
                }
            })
        },
        cancel_prod_apply() {
            this.modal_cancel_pro_apply = false
            this.modal_cancel_apply_title = ''
            let iteration_id = this.group + "_" + this.branch_version
            cancel_prod_apply(iteration_id).then(res => {
                if (res.data.status === 'success') {
                    this.modal_cancel_apply_title = res.data.msg
                    this.modal_cancel_pro_apply = true
                } else {
                    this.$Message.error(res.data.msg)
                }
            })
        },

        closeProApplyModal() {
            this.modal_cancel_pro_apply = false
        },

        cancelProApplyAck() {
            this.modal_cancel_pro_apply = false
            let iteration_id = this.group + "_" + this.branch_version
            cancelProApply(iteration_id)
                .then(result => {
                    if (result.data.status === "success") {
                        this.$Message.success(result.data.msg);
                    } else {
                        this.$Message.error(result.data.msg);
                    }
                })
                .catch(err => {
                    this.$Message.error(err.response.data.msg)
                });
        },

        continue_apply() {
            this.alertmodal = []
            console.log('获取移动端打包状态')
            console.log(this.app_array)
            var myDate = new Date()
            formatDate(myDate, 'yyyy-MM-dd h:m:s')
            for (let i of this.app_array) {
                if (i['status_display'] == '发布中') {
                    let end_time = formatDate(myDate, 'yyyy-MM-dd h:m:s').replace(/\-/g, "/")
                    let begin_time = formatDate(new Date(i['operate_time']), 'yyyy-MM-dd hh:mm:ss').replace(/\-/g, "/")
                    let t = parseInt(new Date(end_time) - new Date(begin_time)) / 1000 / 60
                    if (t > 5) {
                        let alert_content = '上一次申请是' + t.toFixed(1) + '分钟之前'
                        let alert_modal_color = 'green'
                        this.alertmodal.push({
                            'app_name': i['app_name'],
                            'status_display': i['status_display'],
                            'operate_time': i['operate_time'],
                            'alert_content': alert_content,
                            'alert_modal_color': alert_modal_color
                        })
                    } else {
                        this.continue_apply_btn = true
                        let apply_time = parseInt(5) - t
                        let alert_content = apply_time.toFixed(1) + '分钟后再申请'
                        let alert_modal_color = 'red'
                        this.alertmodal.push({
                            'app_name': i['app_name'],
                            'status_display': i['status_display'],
                            'operate_time': i['operate_time'],
                            'alert_content': alert_content,
                            'alert_modal_color': alert_modal_color
                        })
                    }

                    console.log(i['app_name'])
                    console.log(i['status_display'])
                }
            }
            if (this.alertmodal.length != 0) {
                console.log('有应用正在申请')
                console.log(this.alertmodal)
                this.alert = true
            } else {
                this.apply()
                console.log('正在申请')
            }

            console.log('===================alertmodal===========')
            console.log(this.alertmodal)

        },
        /**
         * 点击【申请】按钮，页面数据校验检查
         */
        apply() {

            if (this.environment == 'prod') {
                console.log('app_name')
                console.log(this.selected_array)
                for (let a of this.selected_array) {
                    console.log(a['app_name'])
                    console.log(a)
                    if (a['app_name'] == 'fund') {
                        console.log('应用存在')
                        let params = {
                            'app_name': a['app_name'],
                            'suite_code': this.environment,
                            'end_ver': this.$refs.fund_param.fund_end_ver
                        }
                        console.log(params)
                        CheckH5ZipVersionWhetherPublish(params).then(res => {
                            if (res.data.msg == '版本重复') {
                                console.log(res.data.data[0]['datetime'])
                                alert('该版本' + res.data.data[0]['datetime'] + '已申请过')
                                this.$Notice.error({desc: '发布的版本重复',})
                                return
                            }
                        })
                    }
                }
            }
            if (this.selected_array.length == 0) {
                this.$Notice.error({
                    desc: '请选择申请应用',
                    duration: getErrNoticeShowTime(),
                });
                return;
            }
            let today = new Date()
            //确定是否需要发送申请邮件，年月日相同则不需要继续发
            for (let i of this.selected_array) {
                let affirmDate = new Date(i.affirm_time);
                if (today.getFullYear() != affirmDate.getFullYear() || today.getMonth() != affirmDate.getMonth()
                    || today.getDate() != affirmDate.getDate()) {
                    if (!this.proposer || this.proposer.length == 0) {
                        this.$Notice.error({
                            desc: '今日未收到确认，请选择确认人',
                            duration: getErrNoticeShowTime(),
                        });
                        return;
                    }
                    // break;
                }
                //app_name_list.push(i.app_name)
            }
            this.alert = false
            if (!this.$refs.fund_param.checkFundParam() && !this.$refs.piggy_param.checkPiggyParam()) {
                return
            }
            if (!this.checkParamForApp()) {
                return
            }
            if (this.environment == "prod") {
                if (this.selected_array.length != this.app_array.length) {
                    this.$Notice.warning({
                        desc: '生产环境需选择所有应用申请',
                        duration: getErrNoticeShowTime(),
                    });
                    return;
                }
                this.h5PublishApply()
                this.createH5AppBindNfApp()
            } else {
                // check有没有其他应用在灰度
                this.hdApply()
                this.createH5AppBindNfApp()
            }
        },
        createH5AppBindNfApp() {
            let params = this.getH5ApplyParam()
            let params_list = []
            for (let item of params) {
                console.log(item['app_name'])
                if (item['app_name'] == 'fund' || item['app_name'] == 'piggy') {
                    if (this.environment == 'prod') {
                        item['stage'] = '上线'
                    } else {
                        item['stage'] = '灰度'
                    }
                    createH5AppBindNfAppApi(item).then(res => {
                        this.$Message.success(res.data.msg)
                    })
                        .catch(err => {
                            console.log('============= err =========')
                            console.log(err)
                            this.$Message.error(err)
                        })
                }
            }
        },

        /**
         * 灰度申请,检查是否有其它分支正在进入灰度
         */
        hdApply() {
            let app_name_list = []
            for (let i of this.selected_array) {
                app_name_list.push(i.app_name)
            }
            let req = {'iteration_id': store.state.iterationID, 'app_name_list': app_name_list, 'env': this.environment}
            HdStatusCheckApi(req).then(res => {
                if (res.data.status === "success") {
                    //tag 先不进行校验

                    if (store.state.code_type == "tag") {
                        this.h5PublishApply(this.compile_array)
                    } else {
                        this.hdApplyCheck()
                    }
                } else {
                    // 存在差异打开确认框
                    this.$refs.hd_confirm_modal.modal_confirm = true
                    console.log(res.data.data["conflict_app"])
                    this.conflictTableData = res.data.data["conflict_app"]
                }
            })
        },
        /**
         * 灰度申请检查
         */
        hdApplyCheck() {
            /**
             * todo 检查项共有
             * 1: 回合校验
             * 2：是否有编译制品（前端应用h5）
             * 3：编译产物最新校验
             * 4：是否有其它版本进入灰度（前端H5，后期可以去除掉）
             * 5：最后一次的产物是否发布/下载
             * 6：检查当前制品较线上制品是否有变化（制品库制品和中转库制品是否一致）
             * 7：是否有应用迭代占用线上通道（产线）
             */
            //tag 先不进行校验
            if (store.state.code_type == "tag") {
                this.h5PublishApply(this.compile_array)
            } else {

                this.$Spin.show({
                    render: h => {
                        return h("div", [
                            h("Icon", {
                                class: "demo-spin-icon-load",
                                props: {
                                    type: "ios-loading",
                                    size: 18
                                }
                            }),
                            h("div", "正在进行灰度申请校验，请稍等，预计需要两分钟。。。")
                        ]);
                    }
                })
                let app_name_list = []
                for (let i of this.selected_array) {
                    app_name_list.push(i.app_name)
                }
                let req = {
                    'iteration_id': store.state.iterationID,
                    'app_name_list': app_name_list,
                    'env': this.environment
                }
                //H5走这个检查，疑惑点，方法名为测试发布，建议更改
                if (this.group.indexOf("android") != -1 || this.group.indexOf("ios") != -1) {
                    this.h5PublishApply()
                } else {
                    testPublishCheckApi(req).then(res => {
                        if (res.data.status === "success") {
                            this.$Notice.success({
                                desc: res.data.msg,
                                duration: getInfoNoticeShowTime(),
                            });
                            let sid = res.data.data.sid
                            this.externalServiceResult(sid)
                        } else {
                            this.$Notice.error({
                                desc: res.data.msg,
                                duration: getErrNoticeShowTime(),
                            });
                        }
                    })
                }
            }
        },

        /**
         * 轮询使用sid进行检测
         */
        externalServiceResult(sid) {
            let vm = this
            externalServiceResult(sid).then(res => {
                let status = res.data.data.status;
                let detail = res.data.data.detail;
                if (status == "success") {
                    console.log('------ success --------')
                    this.$Notice.success({
                        desc: detail,
                        duration: getInfoNoticeShowTime(),
                    });
                    this.$Spin.hide()
                    //申请
                    this.h5PublishApply(this.compile_array)
                } else if (status == "failure") {
                    console.log('------ failure --------')
                    this.$Spin.hide()
                    this.hdCheckTableData = JSON.parse(res.data.data.detail.replace(/'/g, '"'))
                    this.$refs.hd_check_modal.modal_confirm = true
                } else {
                    setTimeout(function () {
                        vm.externalServiceResult(sid);
                    }, 3000);
                }
            })
        },
        /**
         * 发布申请
         */
        h5PublishApply() {
            /**
             * todo 检查项共有
             * 1：是否有应用迭代占用线上通道（产线）
             * 2: 回合校验
             * 3：是否有编译制品（前端应用h5）
             * 4：编译产物最新校验/当前分支代码是否已经编译过生产产物（前端应用h5）
             * 5：最后一次的产物是否发布/下载(未加入)
             */
            // 关闭确认框
            this.$refs.hd_confirm_modal.modal_confirm = false
            this.$refs.app_check_modal.modal_confirm = false
            this.$Spin.show({
                render: h => {
                    return h("div", [
                        h("Icon", {
                            class: "demo-spin-icon-load",
                            props: {
                                type: "ios-loading",
                                size: 18
                            }
                        }),
                        h("div", "申请中请稍等,请勿关闭当前窗口。。。")
                    ]);
                }
            });
            //调用后台 申请,确定是否有应用申请当前环境上线（prod）
            if (this.group.indexOf("android") != -1 || this.group.indexOf("ios") != -1) {
                //App发布申请接口
                let applyParam = this.getAppApplyParam()
                let _this = this
                this.addUserAction(this.actionItem, applyParam).then(function (data) {
                    if (!data) {
                        _this.$Spin.hide()
                        _this.$Notice.error({
                            desc: "行为记录数据失败，无法执行发布行为",
                            duration: getErrNoticeShowTime(),
                        });
                        return
                    }
                    applyParam.actionId = data
                    let appPublishApply = appPublishApplyApi
                    // tag类型用tag的接口
                    if (store.state.code_type == "tag") {
                        appPublishApply = appTagPublishApplyApi
                    }
                    appPublishApply(applyParam).then(res => {
                        _this.$Spin.hide()
                        if (res.data.status == 'failed') {
                            _this.$Notice.error({
                                desc: res.data.msg,
                                duration: getErrNoticeShowTime(),
                            });
                        } else {
                            _this.$Notice.success({
                                desc: res.data.msg,
                                duration: getSuccessNoticeShowTime(),
                            });
                            // 发送给领导 确认邮件
                            _this.sendAffirmEmail()
                            // _this.$forceUpdate()//强制刷新当前页面
                            _this.init()
                        }
                    }).catch(err => {
                        _this.$Spin.hide()
                        _this.$Notice.error({
                            desc: err,
                            duration: getErrNoticeShowTime(),
                        });
                    })
                })
            } else {

                h5PublishApplyApi(this.getH5ApplyParam()).then(res => {
                    if (res.data.status == 'failed') {
                        //失败
                        NoticeApplyByWechat(store.state.iterationID, this.environment)
                        this.$Notice.error({
                            desc: res.data.msg,
                            duration: getErrNoticeShowTime(),
                        });
                    } else { //成功
                        this.$Notice.success({
                            desc: res.data.msg,
                            duration: getInfoNoticeShowTime(),
                        });
                        // 发送给领导 确认邮件
                        this.sendAffirmEmail()
                        this.$forceUpdate()//强制刷新当前页面
                        this.init()
                    }
                    this.$Spin.hide()
                }).catch(err => {
                    this.$Spin.hide()
                    this.$Notice.error({
                        desc: '接口500错误',
                        duration: getErrNoticeShowTime(),
                    });
                });
            }
            //我们需要的是回填申请人的用户行为记录
            let action_value_obj = {
                'group': this.group,
                'branch_version': this.branch_version,
                'user': this.proposer.split('@')[0],
            }
            //进行分支管理信息记录，便于确认人的行为更改，todo 不建议在这个地方直接操作，会影响确认人的行为实时性
            let action_value_list = []
            action_value_list.push(action_value_obj)
            let param = {
                action_item: 'H5branch',
                action_value: action_value_list,
            }
            this.insertUserAction(param)
        },
        /**
         * 用户行为保存  H5
         * */
        insertUserAction(param) {
            console.log('============ insertUserAction ============')
            console.log(param)
            //用户行为保存
            userAction(param).then(res => {
                console.log(res)
                let status = res.data.status;
                if (status == "success") {
                    console.log('------ success --------')
                } else {
                    console.log('------ error --------')
                }
            })
        },
        emailSelectChange(typeValue, value) {
            if (typeValue) {
                this.cc = value
            } else {
                this.proposer = value
            }
            console.log("触发了邮件改变事件")
            this.myOnchange()
        },
        myOnchange() {
            this.$refs.last_apply_modal.lastApplyButtonShow = true
            if (!(this.group.indexOf("android") != -1 || this.group.indexOf("ios") != -1)) {
                let action_value_obj = {
                    'environment': this.environment,
                    'proposer': this.proposer,
                    'cc': this.cc
                }
                let fund_value_obj = this.$refs.fund_param.getFundParamForMyOnchange();
                let piggy_value_obj = this.$refs.piggy_param.getPiggyParamForMyOnchange();
                Object.assign(action_value_obj, fund_value_obj, piggy_value_obj);
                //记录当前操作 --> 你绑定了环境
                let action_value_list = []
                action_value_list.push(action_value_obj)
                let param = {
                    action_item: store.state.iterationID + '_' + 'apply',
                    action_value: action_value_list,
                }
                //fund 起始版本回填
                this.$refs.fund_param.fund_init();
                //this.$refs.fund_param.change_fund_end_version();
                //piggy 起始版本回填
                this.$refs.piggy_param.piggy_init();
                //this.$refs.piggy_param.change_piggy_end_version();
                //this.insertUserAction(param);

                h5PublishApplyApiGet(store.state.iterationID, this.environment, this.branch_version).then(res => {
                    //我在这里拼装 app_array
                    console.log("这里调用了")
                    this.app_array = res.data.data.ci_info
                    this.init_with_app_array()
                    this.waitPolling()
                })
            } else {
                if (this.environment) {
                    h5PublishApplyApiGet(store.state.iterationID, this.environment, this.branch_version).then(res => {
                        this.app_array = res.data.data.ci_info
                        this.initRowParams(null, this)
                        this.waitPolling()
                    })
                }
            }
        },

        /**
         *多选模式下生效，只要选中项发生变化就会触发,获取当前选中项列表
         */
        apply_list_select(params) {
            this.selected_array = params
            //循環確定選擇的項是否包含頁面模塊展示的應用
            this.$refs.piggy_app_param.showAppParams = false
            this.$refs.fund_app_param.showAppParams = false
            this.$refs.crm_app_param.showAppParams = false
            this.$refs.fund_param.showFundParams = false
            this.$refs.piggy_param.showPiggyParams = false
            this.is_mini_program = false
            for (let i of this.selected_array) {
                this.showRowBySelected(i)
            }
            console.log('00000000000000000000000000000000000')
            console.log(this.selected_array)
        },
        /**
         * 初始化页面模块,哪些顯示，哪些不顯示
         */
        init_with_app_array() {
            //console.log(this.app_array)
            this.selected_array = []
            this.$refs.piggy_app_param.showAppParams = false
            this.$refs.fund_app_param.showAppParams = false
            this.$refs.fund_param.showFundParams = false
            this.$refs.piggy_param.showPiggyParams = false
            for (let i of this.app_array) {
                //确定哪些项是被选择的
                if (i._checked) {
                    this.showRowBySelected(i)
                    //將選擇的對象存入到selected_array里面
                    this.selected_array.push(i)
                }
            }
            console.log(this.app_array)
            console.log(this.selected_array)
            this.apply_list_select(this.selected_array)
        },
        //根据table的内容确定,页面模块化展示的公共方法
        showRowBySelected(i) {
            //判斷是否是ios或者android，同時是上線應用，則顯示對應參數列
            //判斷是否是ios或者android，同時是上線應用，則顯示對應參數列
            if (i.app_name == 'piggy-ios' || i.app_name == 'piggy-android') {
                this.$refs.piggy_app_param.showAppParams = true
            }
            if (i.app_name == 'fund-ios' || i.app_name == 'fund-android') {
                this.$refs.fund_app_param.showAppParams = true
            }
            if (i.app_name == 'crm-ios') {
                this.$refs.crm_app_param.showAppParams = true
            }
            //判斷選中項中是否包含fund，fund是上線應用
            if (i.app_name === 'fund') {
                this.$refs.fund_param.showFundParams = true
            }
            //判斷選中項中是否包含piggy，piggy是上線應用
            if (i.app_name === 'piggy') {
                this.$refs.piggy_param.showPiggyParams = true
            }
            if (i.app_name === 'elephant-weapp') {
                this.is_mini_program = true
            }
        },
        add_default_cc(cc) {
            console.log('======= cc =======')
            console.log(cc)
            //["<EMAIL>", "<EMAIL>", __ob__: Observer]
            //不存在************** --> has_scm = 0
            let has_scm = 0
            for (let i of cc) {
                if (i == '<EMAIL>') {
                    has_scm = 1
                }
            }
            if (has_scm == 0) {
                if (cc == null || cc == undefined || cc == '') {
                    cc = []
                }
                cc.push('<EMAIL>')
            }
            return cc
        },
        set_upload_version(upload_version) {
            console.log(upload_version)
            this.upload_version = upload_version
        },
        set_upload_desc(upload_desc) {
            console.log(upload_desc)
            this.upload_desc = upload_desc
        },
    }
}
</script>

<style scoped>
.demo-spin-icon-load {
    animation: ani-demo-spin 1s linear infinite;
}
</style>
