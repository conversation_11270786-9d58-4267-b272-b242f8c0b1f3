<template>
    <div>
        <Tabs value="name1" v-model="tab_value" @on-click="changeTab" ref="tabs" name="mobile_pipeline">
            <TabPane label="分支管理" name="H5Branch" tab="mobile_pipeline">
                <H5Branch ref="branch_method"></H5Branch>
            </TabPane>
            <TabPane label="测试发布" name="MobileTest" tab="mobile_pipeline">
                <MobileTest :is_test_refresh="is_test_refresh" ref="test_method"> </MobileTest>
            </TabPane>
            <TabPane label="发布申请" name="MobilePublishApply" tab="mobile_pipeline">
                <MobilePublishApply
                    :is_publish_apply_refresh="is_publish_apply_refresh"
                    ref="mobile_publish_apply"
                ></MobilePublishApply>
            </TabPane>
            <!--<TabPane label="发布申请（旧版待废弃）" name="MobileApply" tab="mobile_pipeline">-->
            <!--<MobileApply ref="mobile_apply"></MobileApply>-->
            <!--</TabPane>-->

            <TabPane label="灰度发布" name="H5Gray" :disabled="tab_disable" tab="mobile_pipeline">
                <H5Gray ref="gray_method"></H5Gray>
            </TabPane>
            <TabPane label="生产发布" name="H5Prod" :disabled="tab_disable" tab="mobile_pipeline">
                <H5Prod ref="prod_method"></H5Prod>
            </TabPane>
            <TabPane label="分支归档" name="H5BranchFile" :disabled="tab_disable" tab="mobile_pipeline">
                <H5BranchFile ref="branch_file_method"></H5BranchFile>
            </TabPane>
        </Tabs>
    </div>
</template>

<script>
import store from '@/spider-store'
import H5Branch from '@/spider-view/ci-cd-mgt/h5/h5-branch/h5-branch'
// import MobileApply from '@/spider-view/ci-cd-mgt/mobile/mobile-apply/mobile-apply'
import H5Gray from '@/spider-view/ci-cd-mgt/h5/h5-gray/h5-gray'
import MobileTest from '@/spider-view/ci-cd-mgt/mobile/mobile-test/mobile-test'

import MobilePublishApply from '@/spider-view/ci-cd-mgt/mobile/mobile-publish-apply/mobile-publish-apply'
//import H5Apply from '@/spider-view/ci-cd-mgt/h5/h5-apply/h5-apply'
import H5Prod from '@/spider-view/ci-cd-mgt/h5/h5-prod/h5-prod'
import H5BranchFile from '@/spider-view/ci-cd-mgt/h5/h5-branch/h5-branch-file'
import { h5IterConfirmStatusApi } from '@/spider-api/publish'
import SpiderPipeline from '@/spider-components/spider-pipeline'

export default {
    name: 'MobileMain',
    components: {
        H5Branch,
        MobileTest,
        H5Gray,
        H5Prod,
        // H5Apply,
        // MobileApply,
        MobilePublishApply,
        H5BranchFile,
        SpiderPipeline
    },
    computed: {
        tab_disable() {
            if (store.state.iterationID == '' || store.state.iterationID == undefined) {
                return true
            } else {
                return false
            }
        }
    },
    data() {
        return {
            tab_value: 'H5Branch',
            is_test_refresh: false,
            is_publish_apply_refresh: false
        }
    },
    methods: {
        changeTab(tab_name) {
            if (tab_name === 'H5Branch') {
                console.log(this.$refs.tabs.activeKey)
                this.$refs.branch_method.init()
                this.is_test_refresh = false
                this.is_publish_apply_refresh = false
            } else if (tab_name === 'MobileTest') {
                this.$refs.test_method.init()
                this.is_test_refresh = true
                this.is_publish_apply_refresh = false
            }
            // else if (tab_name === 'MobileApply') {
            //   this.$refs.mobile_apply.init()
            //   this.is_test_refresh = false
            //   this.is_publish_apply_refresh = false
            // }
            else if (tab_name === 'H5Gray') {
                this.is_test_refresh = false
                this.is_publish_apply_refresh = false
                h5IterConfirmStatusApi(store.state.iterationID, 'gray').then(res => {
                    if (res.data.status === 'success') {
                        this.$refs.gray_method.init()
                    } else {
                        alert(res.data.msg)
                        this.$refs.tabs.activeKey = 'MobilePublishApply'
                        this.is_publish_apply_refresh = true
                        this.$refs.mobile_publish_apply.init()
                        //this.$refs.mobile_apply.init()
                    }
                })
                //this.$refs.gray_method.init()
            } else if (tab_name === 'H5Prod') {
                this.is_test_refresh = false
                this.is_publish_apply_refresh = false
                if (store.state.code_type == 'tag') {
                    //alert(store.state.code_type + "不可以归档")
                    this.tab_value = 'H5Branch'
                    this.$refs.tabs.activeKey = 'H5Branch'
                    alert(store.state.code_type + '不支持发布')
                    this.$refs.branch_method.init()
                } else {
                    h5IterConfirmStatusApi(store.state.iterationID, 'prod').then(res => {
                        if (res.data.status === 'success') {
                            this.$refs.prod_method.init()
                        } else {
                            alert(res.data.msg)
                            this.$refs.tabs.activeKey = 'MobilePublishApply'
                            this.is_publish_apply_refresh = true
                            this.$refs.mobile_publish_apply.init()
                            //this.$refs.mobile_apply.init()
                        }
                    })
                    //this.$refs.prod_method.init()
                }
            } else if (tab_name === 'H5BranchFile') {
                this.is_publish_apply_refresh = false
                this.is_test_refresh = false
                if (store.state.code_type == 'tag') {
                    //alert(store.state.code_type + "不可以归档")
                    this.tab_value = 'H5Branch'
                    this.$refs.tabs.activeKey = 'H5Branch'
                    alert(store.state.code_type + '不可以归档')
                    this.$refs.branch_method.init()
                } else {
                    this.$refs.branch_file_method.init()
                }
            } else if (tab_name === 'MobilePublishApply') {
                this.is_publish_apply_refresh = true
                this.is_test_refresh = false
                this.$refs.tabs.activeKey = 'MobilePublishApply'
                console.log(this.is_publish_apply_refresh)
                this.$refs.mobile_publish_apply.init()
                //this.is_publish_apply_refresh = true
                console.log(this.is_publish_apply_refresh)
            }
        }
    },
    beforeMount() {
        //alert("create"+this.$route.query.iteration_id)
        if (this.$route.query.project_group && this.$route.query.branch_name) {
            // console.log("走到beforeMount了")
            // store.commit("setIterationID", this.$route.query.iteration_id);
            // store.commit("setH5Group", "h5")
            // store.commit("setH5BranchVersion", this.$route.query.branch_version)
            this.tab_value = 'H5Branch'
            // this.tab_value="H5Test"
            // alert("h5"+store.state.iterationID)
        }
    },
    mounted() {
        // console.log("走到Mount了")
        // if (this.$route.query.project_group && this.$route.query.branch_name) {
        //   this.$refs.branch_method.init()
        // } else {
        this.$refs.branch_method.init()
        // }
    }
}
</script>

<style scoped></style>
