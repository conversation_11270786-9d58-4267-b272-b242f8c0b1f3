<template>
  <div>
    <Modal v-model="showPublishParamModal" title="发布入参"
           draggable scrollable
           width="550px">
      <Card>
        <Row>
          <i-col style="margin: 10px;text-align: right" span="8">
            <span style="text-align: right; display: inline-block;margin-top: 5px">H5资源包版本:</span>
          </i-col>
          <Col span="14">
            <Select v-model="publishParam.h5ZipVersion" filterable style="margin:10px" ref="select"
                    placeholder="请选择H5全量包版本">
              <Option v-for="(item,index) in h5ZipVersionList"
                      :value="item.value"
                      :label="item.label"
                      :key="index">
                {{ item.label }}
              </Option>
            </Select>
          </Col>
        </Row>
        <Row>
          <i-col style="margin: 10px;text-align: right" span="8">
            <span style="text-align: right; display: inline-block;margin-top: 5px">客户端构建版本:</span>
          </i-col>
          <Col span="14">
            <Input v-model="publishParam.appVersion" placeholder="请填写app构建版本" style="margin: 10px"/>
          </Col>
        </Row>
        <Row v-if="showAndroidParamModal">
          <Row>
            <i-col style="margin: 10px;text-align: right" span="8">
              <span style="text-align: right; display: inline-block;margin-top: 5px">交易渠道号:</span>
            </i-col>
            <Col span="14">
              <Input v-model="publishParam.tradeCoopId" placeholder="请填写交易渠道号" style="margin: 10px"/>
            </Col>
          </Row>
          <Row>
            <i-col style="margin: 10px;text-align: right" span="8">
              <span style="text-align: right; display: inline-block;margin-top: 5px">交易活动号:</span>
            </i-col>
            <Col span="14">
              <Input v-model="publishParam.tradeActionId" placeholder="请填写交易活动号" style="margin: 10px"/>
            </Col>
          </Row>
          <Row>
            <i-col style="margin: 10px;text-align: right" span="8">
              <span style="text-align: right; display: inline-block;margin-top: 5px">App渠道号:</span>
            </i-col>
            <Col span="14">
              <Input v-model="publishParam.channelId" placeholder="请填写App渠道号" style="margin: 10px"/>
            </Col>
          </Row>
          <Row>
            <i-col style="margin: 10px;text-align: right" span="8">
              <span style="text-align: right; display: inline-block;margin-top: 5px">sonar扫描:</span>
            </i-col>
            <Col span="14">
              <Checkbox v-model="publishParam.scan" style="margin: 15px">扫描请勾选</Checkbox>
            </Col>
          </Row>
        </Row>
      </Card>
      <div slot="footer">
        <Button type="text" @click="showPublishParamModal = false">关闭</Button>
        <Button type="primary" @click="publish" :loading="loading">确认</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
  import {
    userAction,
    userActionGet,
    findH5ZipVersionByEnv,
    getErrNoticeShowTime,
    getSuccessNoticeShowTime,
    appTestPublishApi,
    appTagTestPublishApi
  } from "@/spider-api/h5";
  import store from "@/spider-store";
import {AppMergeCheckApi} from "@/spider-api/app-check"
  export default {
    name: "envBind",
    props: {
      h5ZipVersionList: {
        type: Array,
        defaults: []
      }
    },
    data() {
      return {
        publishParam: {},
        loading: false,
        showPublishParamModal: false,
        showAndroidParamModal: true,
        h5ZipVersionList: [],
        appNameList: []
      }
    },
    methods: {
      openPublishModal(row) {
        if (row.status == 'running') {
          this.$Notice.info({
            desc: '选中的应用已经开始发布，请等待发布结束',
            duration: getErrNoticeShowTime(),
          });
          return;
        }
        //获取操作row的索引，便于form表单提交后修改对应的数据,暂时可以不使用，后期需要可以参考这样的写法
        //this.rowIndex = this.app_props.findIndex(item => item.app_name == row.app_name)
        //初始化数据
        let h5AppName = row.app_name.split("-")[0];
        let env = "test";
        let actionItem = "test_publish";
        //获取用户行为
        let _this = this;
        this.selectUserAction(actionItem).then(function (data) {
          let publishParam = {};
          if (data) {
            publishParam = JSON.parse(data)
          }
          if (row.package_type == 'android') {
            _this.publishParam = {
              tradeCoopId: publishParam.tradeCoopId ? publishParam.tradeCoopId : "A20131205",
              tradeActionId: publishParam.tradeActionId ? publishParam.tradeActionId : "HD0001",
              channelId: publishParam.channelId ? publishParam.channelId : "145217337",
              umengChannel: publishParam.umengChannel ? publishParam.umengChannel : "test",
              channel: publishParam.channel ? publishParam.channel : "ErenEben"
            }
            _this.showAndroidParamModal = true
            _this.publishParam.scan = publishParam.scan ? true : false
          } else {
            _this.publishParam = {}
            _this.showAndroidParamModal = false
          }
          _this.publishParam.appVersion = publishParam.appVersion
          _this.publishParam.h5ZipVersion = publishParam.h5ZipVersion
          //_this.$refs.selectH5Zip.query = publishParam.h5ZipVersion
          //查询h5资源包版本
          findH5ZipVersionByEnv(h5AppName, env).then(res => {
            _this.h5ZipVersionList = res.data.data ? res.data.data : [{}]
            _this.showPublishParamModal = true
          }).catch(err => {
            console.info("findH5ZipVersionByEnv:" + err.data)
          })
          _this.appNameList = []
          _this.appNameList.push(row.app_name)
          //组装默认参数
          _this.publishParam.packageType = row.package_type
          _this.publishParam.appName = row.app_name
          _this.publishParam.appEnv = env
          _this.publishParam.appBranch = store.state.h5_branch_version
          _this.publishParam.repoPath = row.git_path
          _this.publishParam.h5AppName = h5AppName
          _this.publishParam.h5Env = env
          _this.publishParam.actionItem = actionItem
          _this.publishParam.appTagName = store.state.tag_name
          _this.publishParam.appCodeType = store.state.code_type
        })
      },
      //todo  触发发布
      publish() {
        //判断必填项 todo 以后可以把上面的modal布局为form表单形式
        if (!this.publishParam.h5ZipVersion) {
          this.$Message.info("所需H5资源包版本不能为空")
          return
        }
        if (!this.publishParam.appVersion) {
          this.$Message.info("客户端构建版本名称不能为空")
          return
        }
        this.loading = true
        this.publishParam.iterationID = store.state.iterationID
        //发布检查
        this.appPublishCheckApi()
        //当前入参存入用户行为表
        let _this = this
        this.addUserAction(_this.publishParam.actionItem, _this.publishParam).then(function (data) {
          if (!data) {
            _this.$Notice.error({
              desc: "行为记录数据失败，无法执行发布行为",
              duration: getErrNoticeShowTime(),
            });
            return
          }
          _this.publishParam.actionId = data
          let testPublish = appTestPublishApi
          // 如果是tag类型调用 tag发布接口
          if (_this.publishParam.appCodeType == "tag") {
            testPublish = appTagTestPublishApi
          }
          //测试发布
          testPublish(_this.publishParam).then(res => {
            _this.loading = false
            if (res.data.status == 'failed') {
              _this.$Notice.error({
                desc: res.data.msg,
                duration: getErrNoticeShowTime(),
              });
            } else {
              _this.$Notice.success({
                desc: res.data.msg,
                duration: getSuccessNoticeShowTime(),
              });
              _this.showPublishParamModal = false
              _this.initTable()
            }
          }).catch(err => {
            console.info(err)
            _this.$Notice.error({
              desc: err,
              duration: getErrNoticeShowTime(),
            });
            _this.loading = false
            _this.showPublishParamModal = false
          })
        })
      },
      /**
       * app发布检查
       * 迭代iteration_id，应用列表app_name_list
       * */
      appPublishCheckApi() {
        let param = {
          iteration_id: store.state.iterationID,
          app_name_list: this.appNameList
        }
        let _this = this
        AppMergeCheckApi(param).then(res => {
          if (res.data.status == 'failed') {
            _this.$Notice.error({
              desc: res.data.msg,
              duration: getErrNoticeShowTime(),
            });
          }
        }).catch(err => {
          console.info(err)
        })
      },
      /**
       * 用户行为录入test_publish
       * action_item    action_value
       * todo 用new Promise构造（作用于链式调用）
       * */
      addUserAction(actionItem, actionValue) {
        return new Promise(function (resolve, reject) {
          let req = {
            action_item: actionItem,
            action_value: actionValue
          }
          userAction(req).then(res => {
            resolve(res.data.data)
          }).catch(err => {
            reject(false)
          })
        })
      },
      /**
       * 用户行为查询test_publish
       * */
      selectUserAction(actionItem) {
        return new Promise(function (resolve, reject) {
          userActionGet(actionItem).then(res => {
            let data
            //console.info(JSON.stringify(res))
            if (res.data.status != 'failed') {
              data = res.data.data.replace(/False/g, "false").replace(/True/g, "true").replace(/'/g, "\"")
            }
            resolve(data)
          }).catch(err => {
            reject(false)
          })
        })
      },
    },
    created() {

    }
  }
</script>

<style scoped>

</style>
