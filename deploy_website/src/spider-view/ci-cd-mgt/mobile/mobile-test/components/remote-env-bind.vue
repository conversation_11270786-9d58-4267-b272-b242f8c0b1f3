<template>
  <div>
    <!--<solt name="button-solt"></solt>-->
    <Button
      ghost
      type="primary"
      style="text-align: left; display: inline-block; width:80px; margin:5px"
      @click="environment_banding"
    >环境绑定
    </Button>
    <Modal
      v-model="environment_modal_val"
      title="批量环境绑定"
      @on-ok="environment_modal_ok"
      @on-cancel="environment_modal_cancel">
      <Card v-if="show_different_card === '1'">
        <Row v-for="(item,index) in table_selection" :key="item.app_name">
          <i-col style="margin: 10px" span="4">
            <span style="text-align: left; display: inline-block;">{{ item.app_name }}</span>
          </i-col>
          <Col span="12" style="margin: 5px">
            <Select v-if="item.app_name=='vendor'" multiple v-model="vendor_list" disabled="">
              <Option
                v-for="(item,index) in vendor_list"
                :value="item"
                :key="item"
              >{{ item }}
              </Option>
            </Select>
            <Select v-if="item.app_name!='vendor'" multiple v-model="environment[index]"
                    @on-change="change_for_vendor(index)">
              <Option
                v-for="item in environment_list[index]"
                :value="item"
                :key="item"
              >{{ item }}
              </Option>
            </Select>
          </Col>
        </Row>
      </Card>
      <Card v-if="show_different_card === '0'">至少勾选一个应用</Card>
    </Modal>
  </div>
</template>

<script>
import {
    userAction
  } from "@/spider-api/h5";
import store from "@/spider-store";
  export default {
    name: "remoteEnvBind",
    props: {
      table_selection: {
        type: Array,
        defaults: []
      },
      remote_props: {
        type: Array
      },
    },
    computed: {
      //控制环境绑定的弹出框弹出哪一个
      show_different_card: function () {
        if (this.table_selection == undefined) {
          return '0'
        }
        if (this.table_selection.length > 0) {
          if (this.table_selection.length == 1 && this.table_selection[0].app_name == 'vendor') {
            return '0'
          } else {
            return '1'
          }
        } else {
          return '0'
        }
      }
    },
    data() {
      return {
        environment_modal_val: false,
        environment_list: [],
        environment: [],
        vendor_list: [],
        vendor_list_temp: [],
        vendor_set: new Set(),
      }
    },
    methods: {
      /**
       * 点击环境绑定按钮
       */
      environment_banding() {
        console.info("调用成功");
        this.environment_modal_val = true;
        this.environment_list = [];
        //由于记录了历史记录，所以我们直接根据历史记录回填的页面，不会走多选框on-change方法 --> 批量发布那里也要加
        if (this.table_selection.length == 0) {
          for (let i of this.remote_props) {
            //如果是选中的话
            if (i._checked == true) {
              this.table_selection.push(i)
            }
          }
        }
        //下拉框的数据绑定 --> 单纯value的list我们无法控制数据顺序，所以设计一个list里面装对象
        let suite_code_list_temp = []
        for (let outer in store.state.suite_code_list) {
          for (let inner of this.table_selection) {
            if (outer === inner.app_name) {
              let temp_obj = {'app_name': inner.app_name, suite_code: store.state.suite_code_list[outer]}
              suite_code_list_temp.push(temp_obj)
            }
          }
        }
        //重新按应用名绑定环境
        for (let i of suite_code_list_temp) {
          for (let inner = 0; inner < this.table_selection.length; inner++) {
            if (i.app_name === this.table_selection[inner].app_name) {
              this.environment_list[inner] = i.suite_code
            }
          }
        }
        for (let i = 0; i < this.table_selection.length; i++) {
          this.environment[i] = ''
        }
        console.info(this.environment_list)
      },
      change_for_vendor(index) {
        // console.log(this.environment[index])
        if (this.table_selection[index].app_name === 'newwap'
          || this.table_selection[index].app_name === 'smasset'
          || this.table_selection[index].app_name === 'newpig') {
          //每次修改一个下拉菜单的值，只会改变list中对应下角标的数据
          this.vendor_list_temp[index] = this.environment[index]
          //环境多选的话 this.environment[index] 是一个Array
          this.vendor_set = new Set()
          for (let temp of this.vendor_list_temp) {
            // this.vendor_set.add(i)
            for (let env of temp) {
              // console.log(env)
              this.vendor_set.add(env)
            }
          }
          // this.vendor_set[index] = this.environment[index];
          this.vendor_list = Array.from(this.vendor_set)
          this.$set(this.table_selection[this.table_selection.length - 1], 'suite_code', this.vendor_list)
        }
      },
      /**
       * 环境绑定 确定
       */
      environment_modal_ok() {
        //当前选择的所有行
        let tableSelection = this.table_selection
        //我们设置的中转 选中行 对象
        let myTableSelection = []
        //当前选定环境
        let environment = this.environment
        //回显用
        let remoteProps = this.remote_props;
        //单独用来处理vendor
        let vendorSet = new Set()
        for (let outer = 0; outer < tableSelection.length; outer++) {
          //所有非vendor的对勾全部去掉，之后再根据选择的绑定环境对应的应用来打勾
          if (this.table_selection[outer].app_name != 'vendor') {
            this.$set(this.table_selection[outer], '_checked', false)
          }
          //多选中向组件添加属性
          if (this.table_selection[outer].app_name != 'vendor'
            && environment[outer] != null && environment[outer] != undefined) {
            this.$set(this.table_selection[outer], 'suite_code', environment[outer])
            myTableSelection.push(this.table_selection[outer])
          }
          for (let inner of remoteProps) {
            //找到我选中的那条记录在table中的位置，向组件添加属性
            if (inner.app_name == tableSelection[outer].app_name
              && environment[outer] != null && environment[outer] != undefined) {
              this.$set(inner, 'suite_code', environment[outer])
              //开始重新回填 对勾 --> 这么做的原因：我们在上边控制了suite_code为空时不记录用户操作，如果对方全选，却不绑定环境，就会出现
              //打着对勾，却没有环境的情况，防止用户发布的时候直接发布了一个未绑定环境的应用  另一个方法，做环境必填，不过客户可能不乐意
              this.$set(inner, '_checked', true)
            }
            if (inner.app_name == 'vendor'
              && environment[outer] != null && environment[outer] != undefined
              && (this.table_selection[outer].app_name === 'newwap'
                || this.table_selection[outer].app_name === 'smasset'
                || this.table_selection[outer].app_name === 'newpig')) {
              for (let env of environment[outer]) {
                vendorSet.add(env)
              }
            }
          }
          //单独添加vendor
          if (vendorSet.size > 0) {
            this.$set(this.remote_props[this.remote_props.length - 1], 'suite_code', Array.from(vendorSet).toString())
          }
        }
        //把选中的数据换成suite_code不为空的
        for (let i of this.table_selection) {
          //vendor默认选中的，iview似乎在回填的时候不认识他
          if (i.app_name == 'vendor') {
            myTableSelection.push(i)
          }
        }
        this.$parent.table_selection = myTableSelection
        //记录当前操作 --> 你绑定了环境
        let action_value_list = []
        let action_value_obj = {}
        for (let i of this.table_selection) {
          if (i.suite_code != null && i.suite_code != undefined) {
            action_value_obj = {"app_name": i.app_name, "suite_code": i.suite_code}
            action_value_list.push(action_value_obj)
          }
        }
        let param = {
          action_item: store.state.iterationID + '_' + 'test_remote_suite_code',
          action_value: action_value_list,
        }
        //用户行为保存
        userAction(param).then(res => {
          let status = res.data.status;
          if (status == "success") {
            console.log('------ 用户行为已保存 --------')
            console.log(res)
          } else {
            console.log('---------- 用户行为记录操作失败 ---------')
          }
        })
      },
       /**
       * 环境绑定 终止
       */
      environment_modal_cancel() {
        // this.$Message.info('Clicked cancel');
      }
    },
    created() {

    }
  }
</script>

<style scoped>

</style>
