<template>
  <div>
    <!--<solt name="button-solt"></solt>-->
    <Button
      ghost
      type="primary"
      style="text-align: left; display: inline-block; width:80px; margin:5px"
      @click="environment_banding"
    >环境绑定
    </Button>
    <Modal
      v-model="environment_modal_val"
      title="批量环境绑定"
      @on-ok="environment_modal_ok"
      @on-cancel="environment_modal_cancel">
      <Card v-if="show_different_card === '1'">
        <Row v-for="(item,index) in table_selection" :key="item.app_name">
          <i-col style="margin: 10px" span="4">
            <span style="text-align: left; display: inline-block;">{{ item.app_name }}</span>
          </i-col>
          <Col span="12" style="margin: 5px">
            <Select multiple v-model="environment[index]">
              <Option
                v-for="item in environment_list[index]"
                :value="item"
                :key="item"
              >{{ item }}</Option>
            </Select>
          </Col>
        </Row>
      </Card>
      <Card v-if="show_different_card === '0'">至少勾选一个应用</Card>
    </Modal>
  </div>
</template>

<script>
import {
    userAction
  } from "@/spider-api/h5";
import store from "@/spider-store";
  export default {
    name: "staticEnvBind",
    props: {
      table_selection: {
        type: Array,
        defaults: []
      },
      static_props: {
        type: Array
      },
    },
    computed: {
      //控制环境绑定的弹出框弹出哪一个
      show_different_card: function () {
        if (this.table_selection == undefined) {
          return '0'
        }
        if (this.table_selection.length > 0) {
          if (this.table_selection.length == 1 && this.table_selection[0].app_name == 'vendor') {
            return '0'
          } else {
            return '1'
          }
        } else {
          return '0'
        }
      }
    },
    data() {
      return {
        environment_modal_val: false,
        environment: [],
        environment_list: []
      }
    },
    methods: {
       /**
       * 点击环境绑定按钮
       */
      environment_banding() {
        this.environment_modal_val = true;
        this.environment_list = new Array()
        console.log('-------- this.table_selection --------')
        console.log(this.table_selection)
        //由于记录了历史记录，所以我们直接根据历史记录回填的页面，不会走多选框on-change方法 --> 批量发布那里也要加
        if (this.table_selection.length == 0) {
          for (let i of this.static_props) {
            //如果是选中的话
            if (i._checked == true) {
              this.table_selection.push(i)
            }
          }
        }
        for (let outer in store.state.suite_code_list) {
          for (let inner of this.table_selection) {
            if (outer === inner.app_name) {
              this.environment_list.push(store.state.suite_code_list[outer])
            }
          }
        }
      },
       /**
       * 环境绑定 确定
       */
      environment_modal_ok() {
        let tableSelection = this.table_selection
        let environment = this.environment
        let staticProps = this.static_props;
        for (let outer = 0; outer < tableSelection.length; outer++) {
          //多选中向组件添加属性
          this.$set(this.table_selection[outer], 'suite_code', environment[outer])
          this.$set(this.table_selection[outer], '_checked', true)
          for (let inner of staticProps) {
            //找到我选中的那条记录在table中的位置，向组件添加属性
            if (inner.app_name == tableSelection[outer].app_name) {
              this.$set(inner, 'suite_code', environment[outer])
              this.$set(inner, '_checked', true)
            }
          }
        }
        //记录当前操作 --> 你绑定了环境
        let action_value_list = []
        let action_value_obj = {}
        for (let i of this.static_props) {
          action_value_obj = {"app_name": i.app_name, "suite_code": i.suite_code}
          action_value_list.push(action_value_obj)
        }
        let param = {
          action_item: store.state.iterationID + '_' + 'test_static_suite_code',
          action_value: action_value_list,
        }
        userAction(param).then(res => {
          console.log(res)
          let status = res.data.status;
          if (status == "success") {
            console.log('------ success --------')
          } else {
            console.log('---------- 用户行为记录操作失败 ---------')
          }
        })
      },
      /**
       * 环境绑定 终止
       */
      environment_modal_cancel() {
        // this.$Message.info('Clicked cancel');
      }
    },
    created() {

    }
  }
</script>

<style scoped>

</style>
