<template>
  <div>
    <Button
      ghost
      type="primary"
      style="text-align: left; display: inline-block; width:80px; margin:5px"
      @click="batch_compile"
    >批量编译
    </Button>
  </div>
</template>

<script>
  import {
    h5CompileApi,
    compileCheckApi,
    externalServiceResult,
    getErrNoticeShowTime
  } from "@/spider-api/h5";
  import store from "@/spider-store";

  export default {
    name: "batchCompile",
    props: {
      table_selection: {
        type: Array,
        defaults: []
      },
      remote_props: {
        type: Array
      },
      initTable: {
        type: Function,
      },
      action_item:String
    },
    computed: {},
    data() {
      return {
        compile_array: [],
        checkMsg: [],
      }
    },
    methods: {
      /**
       * 批量编译
       */
      batch_compile() {
        this.$Loading.start();
        console.log('============ this.remote_props =============');
        console.log(this.remote_props);
        //组装数据
        let app_name_list = [];
        let iteration_id = store.state.h5_group + '_' + store.state.h5_branch_version;
        this.$parent.table_selection = [];
        for (let i of this.remote_props) {
          //如果是选中的话
          if (i._checked == true) {
            this.table_selection.push(i)
          }
        }
        for (let i of this.table_selection) {
          app_name_list.push(i.app_name)
        }
        console.log('====== this.table_selection ========');
        console.log(this.table_selection);

        let req = {'iteration_id': iteration_id, 'app_name_list': app_name_list};
        let compileObj = {};
        let compileList = [];
        // 用于记录所有正在编译的应用，告知用户这些应用正在进行编译，请等待编译结束后再次编译
        let compileRunningList = [];
        for (let i of this.table_selection) {
          if (i.status != 'compile_running') {
            compileObj = {
              'suite_code': i.suite_code,
              'begin_ver': '',
              'end_ver': '',
              'iteration_id': store.state.iterationID,
              'br_name': store.state.h5_branch_version,
              'app_name': i.app_name,
              'is_silent': '1',
              'action_item': this.action_item,
              'status': i.status,
            }
            compileList.push(compileObj)
          } else {
            compileRunningList.push(i.app_name)
          }
        }
        if (compileRunningList.length == 0 && (compileList == null || compileList.length == 0)) {
          this.$Notice.error({
            desc: '至少选择一个应用',
            duration: getErrNoticeShowTime(),
          });
          return;
        }
        if (compileRunningList.length > 0) {
          this.$Notice.info({
            desc: '以下应用' + compileRunningList + '正在进行编译，请等待编译结束后再次编译',
            duration: getErrNoticeShowTime(),
          });
        }
        console.log('============ compileList ============')
        console.log(compileList)
        if (compileList.length > 0) {
          this.checkBeforeCompile(req, compileList)
          this.compile(compileList)
        }
        this.$Loading.finish()
      },
      /**
       * 编译之前，先进行检测
       */
      checkBeforeCompile(req, incomeArray) {
        this.compile_array = incomeArray
        //检测分为两部分，如果返回的全是success，才会执行  编译
        //第一部分
        compileCheckApi(req).then(res => {
          if (res.data.status === "success") {
            /*this.$Notice.success({
              desc: res.data.msg,
              duration: getInfoNoticeShowTime(),
            });*/
            console.log("============res =========")
            console.log(res.data.data)
            let sid = res.data.data.sid
            //检查用的第二步
            //这里是编译的验证 --> 编译的功能写在 第二步 校验的回调函数里了
            this.checkCompileSecondStep(sid)
          } else {
            this.$Notice.error({
              desc: res.data.msg,
              duration: getErrNoticeShowTime(),
            });
          }
        })
      },
      /**
       * 第二步检测(编译)
       */
      checkCompileSecondStep(sid) {
        externalServiceResult(sid).then(res => {
          console.log(res)
          this.checkMsg.push(res);
          let status = res.data.data.status;
          let detail = res.data.data.detail;
          let count = store.state.check_count
          if (status == "success") {
            /*this.$Notice.success({
              desc:detail,
              duration: getInfoNoticeShowTime(),
            });*/
            store.commit("setCheckCount", 0)
            //测试环境特色  不论校验是否成功 都要编译
            // this.compile(this.compile_array)
          } else if (status == "failure") {
            this.$Notice.error({
              desc: detail,
              duration: getErrNoticeShowTime(),
            });
            store.commit("setCheckCount", 0)
            //测试环境特色  不论校验是否成功 都要编译
            // this.compile(this.compile_array)
          } else {
            console.log('======count========')
            console.log(count)
            if (count <= 60) {
              let vm = this;
              setTimeout(function () {
                vm.checkCompileSecondStep(sid);
              }, 5000);
              count++
              store.commit("setCheckCount", count)
            } else {
              store.commit("setCheckCount", 0)
            }
          }
        })
      },
      /**
       * 编译  先编译，再发布
       */
      compile(param) {
        console.log('========= compile START ============')
        let vm = this
        console.log(param)
        h5CompileApi(param).then(res => {
          console.log(res)
          if (res.status == '200') {
            //成功
            /*vm.$Notice.success({
              desc: res.data.msg,
              duration: getInfoNoticeShowTime(),
            });*/
          } else {
            //失败
            vm.$Notice.error({
              desc: res.data.msg,
              duration: getErrNoticeShowTime(),
            });
          }
          this.initTable()
        })
      },
    },
    created() {

    }
  }
</script>

<style scoped>

</style>
