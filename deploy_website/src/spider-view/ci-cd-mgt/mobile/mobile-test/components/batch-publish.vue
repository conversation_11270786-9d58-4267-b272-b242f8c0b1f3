<template>
  <div>
    <Button
      ghost
      type="primary"
      style="text-align: left; display: inline-block; width:80px; margin:5px"
      @click="batch_publish"
    >批量发布
    </Button>
  </div>
</template>

<script>
  import {
    h5TestPublishApi,
    testPublishCheckApi,
    externalServiceResult,
    getErrNoticeShowTime
  } from "@/spider-api/h5";
  import store from "@/spider-store";

  export default {
    name: "batchPublish",
    props: {
      table_selection: {
        type: Array,
        defaults: []
      },
      remote_props: {
        type: Array
      },
      initTable: {
        type: Function,
      },
      action_item: String
    },
    computed: {},
    data() {
      return {
        publish_array: [],
        checkMsg: [],
      }
    },
    methods: {
     /**
       * 批量发布
       */
      batch_publish() {
        this.$Loading.start();
        console.log('========== this.table_selection ========');
        //由于记录了历史记录，所以我们直接根据历史记录回填的页面，不会走多选框on-change方法 --> 批量发布那里也要加
        if (this.table_selection.length == 0) {
          for (let i of this.remote_props) {
            //如果是选中的话
            if (i._checked == true) {
              this.table_selection.push(i)
            }
          }
        }
        console.log(this.table_selection);
        if (this.table_selection.length == 0) {
          this.$Notice.error({
            desc: '至少选择一个应用',
            duration: getErrNoticeShowTime(),
          });
          return;
        }
        //组装数据
        let app_name_list = [];
        let iteration_id = store.state.h5_group + '_' + store.state.h5_branch_version;
        let vendor_list = [];
        let all_have_env = 0;
        for (let i of this.table_selection) {
          if (i.status == 'compile_running') {
            this.$Notice.info({
              desc: '选中的应用已经开始编译，请等待编译结束',
              duration: getErrNoticeShowTime(),
            });
            return;
          }
          if (i.status == 'publish_running') {
            this.$Notice.info({
              desc: '选中的应用已经开始发布，请等待发布结束',
              duration: getErrNoticeShowTime(),
            });
            return;
          }
          //如果存在vendor，我们存他的环境会变成一个数组，而我们需要存一个字符串 --> 现在全变成多选了，除了vendor，别人也要处理
          let vendor_suite_code = [];
          console.log('============= i.suite_code ==========');
          // i.suite_code instanceof String === false --> string变量似乎和new String()类型不同
          if (typeof i.suite_code === 'string') {
            console.log('i am String');
            vendor_suite_code = i.suite_code.split(',')
          }
          if (i.suite_code instanceof Array) {
            console.log('i am Array');
            vendor_suite_code = i.suite_code
          }
          if (vendor_suite_code.length == 0) {
            all_have_env++
          }
          let vendor_obj = {}
          for (let sc of vendor_suite_code) {
            // 是否绑定了环境
            if (sc != null && sc != undefined && sc != '') {
              vendor_obj = {
                "suite_code": sc,
                "begin_ver": "",
                "end_ver": "",
                "iteration_id": i.iteration_id,
                "app_name": i.app_name,
                "is_silent": "1",
                'action_item': this.action_item
              };
              vendor_list.push(vendor_obj)
            }
          }
          app_name_list.push(i.app_name)
        }
        // 因为之前判空过，所以这个时候还没有加入到list中，一定是因为没有绑定环境
        if (all_have_env > 0) {
          this.$Notice.error({
            desc: '存在未绑定环境的应用',
            duration: getErrNoticeShowTime(),
          });
          return;
        }
        let req = {'iteration_id': iteration_id, 'app_name_list': app_name_list};
        console.log('======== START BATCH PUBLISH ============');
        console.log(vendor_list);
        this.checkBeforePublish(req, vendor_list);
        this.publish(vendor_list);
      },
      /**
       * 发布之前，先进行检测
       */
      checkBeforePublish(req, incomeArray) {
        this.publish_array = incomeArray;
        //检测分为两部分，如果返回的全是success，才会执行  编译
        //第一部分
        testPublishCheckApi(req).then(res => {
          if (res.data.status === "success") {
            /*this.$Notice.success({
              desc: res.data.msg,
              duration: getInfoNoticeShowTime(),
            });*/
            console.log("============res =========");
            console.log(res.data.data);
            let sid = res.data.data.sid;
            //检查用的第二步
            //这里是编译的验证 --> 编译的功能写在 第二步 校验的回调函数里了
            this.checkPublishSecondStep(sid, incomeArray)
          } else {
            this.$Notice.error({
              desc: res.data.msg,
              duration: getErrNoticeShowTime(),
            });
          }
        })
      },
      /**
       * 第二步检测(发布)
       */
      checkPublishSecondStep(sid, incomeArray) {
        externalServiceResult(sid).then(res => {
          console.log("======== externalServiceResult ===========");
          console.log(res);
          this.checkMsg.push(res);
          let status = res.data.data.status;
          let detail = res.data.data.detail;
          let count = store.state.check_count;
          if (status == "success") {
            console.log('========== 开始发布 ===========');
            console.log(incomeArray);
            store.commit("setCheckCount", 0)
            //测试环境特色  不论校验是否成功，都要发布
            // this.publishAndCompile(incomeArray)
          } else if (status == "failure") {
            this.$Notice.error({
              desc: detail,
              duration: getErrNoticeShowTime(),
            });
            store.commit("setCheckCount", 0)
            //测试环境特色  不论校验是否成功，都要发布
            // this.publishAndCompile(incomeArray)
          } else {
            console.log('======count========');
            console.log(count);
            if (count <= 60) {
              let vm = this;
              setTimeout(function () {
                vm.checkPublishSecondStep(sid, incomeArray);
              }, 5000);
              count++;
              store.commit("setCheckCount", count)
            } else {
              store.commit("setCheckCount", 0)
            }
          }
        })
      },
      /**
       * 单纯的发布  --> 已编译  状态下才会调用
       */
      publish(param) {
        console.log("========= publish START ==========");
        console.log(param);
        let publish_list = [];
        let publish_obj = {};
        for (let i = 0; i < param.length; i++) {
          publish_obj = {
            'suite_code': param[i].suite_code,
            'begin_ver': '',
            'end_ver': '',
            'iteration_id': store.state.iterationID,
            'br_name': store.state.h5_branch_version,
            'app_name': param[i].app_name,
            'is_silent': '1',
            'action_item': this.action_item
          }
          publish_list.push(publish_obj)
        }
        h5TestPublishApi(publish_list).then(res => {
          console.log(res);
          if (res.data.status == 'success') {
            //成功

          } else {
            //失败
            this.$Notice.error({
              desc: res.data.msg,
              duration: getErrNoticeShowTime(),
            });
          }
          this.initTable()
        });
        this.$Loading.finish();
      },
    },
    created() {

    }
  }
</script>

<style scoped>

</style>
