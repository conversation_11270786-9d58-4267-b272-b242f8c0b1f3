<template>
  <div>

      <Card>
        <Row>
          <i-col style="margin: 10px;text-align: right" span="8">
            <span style="text-align: right; display: inline-block;">App版本名称：</span>
          </i-col>
          <Col span="11">
            <Input v-model="params.app_version" placeholder="请输入App版本名称" clearable></Input>
          </Col>
        </Row>
        <Row>
          <i-col style="margin: 10px;text-align: right" span="8">
            <span style="text-align: right; display: inline-block;">h5资源平台名称：</span>
          </i-col>
          <Col span="11">
            <Input v-model="params.h5PlatFormCode" disabled></Input>
          </Col>
        </Row>

        <Row style="margin-top: 0px">
          <i-col style="margin: 10px;text-align: right" span="8">
            <span style="text-align: right; display: inline-block;">H5资源包环境：</span>
          </i-col>
          <Col span="11">
            <Select v-model="params.h5_env"
                    @on-change="getH5Version(params.app_name, params.h5_env)"
                   placeholder="请选择H5资源包对应环境">
              <Option v-for="(item,index) in env_list"
                      :value="item.value"
                      :key="index">
                {{ item.label }}
              </Option>
            </Select>
          </Col>
        </Row>
        <Row style="margin-top: 0px">
          <i-col style="margin: 10px;text-align: right" span="8">
            <span style="text-align: right; display: inline-block;">H5资源包版本：</span>
          </i-col>
          <Col span="11">
            <Select v-model="params.h5_version"
                    @on-change="findH5Version(params.app_name, params.h5_env, params.h5_version)"
                    placeholder="请选择当前环境下的H5资源包版本">
              <Option v-for="(item,index) in h5_version_list"
                      :value="item.value"
                      :key="index">
                {{ item.label}}
              </Option>
            </Select>
          </Col>
        </Row>
        <Row>
          <i-col style="margin: 10px;text-align: right" span="8">
            <span style="text-align: right; display: inline-block;">H5资源包迭代版本：</span>
          </i-col>
          <Col span="11" style="margin: 0 auto;">
            <p style="margin: 10px auto; text-align: right; display: inline-block;">  {{h5_iter_id}}</p>
          </Col>
        </Row>
      </Card>

  </div>
</template>


<script>
    import {findH5ZipVersionByEnv, findH5HDDistVersionByEnv, findH5ZipVersionByEnvForPlatform } from "@/spider-api/h5";
    export default {
        name: "AppPackageParams",
        data() {
          return {
            h5_version_list: [],
            h5_iter_id:"",

            app_h5_bind:{"fund-ios":"fund",
            "fund-android":"fund",
            "crm-android":"crm",
            "piggy-ios":"piggy",
            "piggy-android":"piggy"},
          }
        },
        props: {
          params: {
                 type: Object,
                 default:() =>{}
            },
          env_list:{
            type:Array,
            default:() => []
          },

        },
        methods: {
            //app选择h5zip环境查询对应环境下的h5zip版本数据
           getH5Version(app_name, env) {
              console.log(app_name)
              console.log(this.app_h5_bind[app_name])
              // this.params.h5_app_name = this.app_h5_bind[app_name]
              findH5ZipVersionByEnvForPlatform(this.params.h5PlatFormCode ,env).then(res => {
              this.h5_version_list = res.data.data
            }).catch(err =>{
              console.info("findH5ZipVersionByEnvForPlatform:"+err.data)
            })
              // findH5ZipVersionByEnv(this.app_h5_bind[app_name], env).then(res => {
              //     this.h5_version_list = res.data.data
              //     console.log('rrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrr')
              // console.log(this.h5ZipVersionList)
              //     })
            },
           findH5Version(app_name, env, h5_version){
              findH5HDDistVersionByEnv(this.params.h5PlatFormCode, env, h5_version).then(res => {
                this.h5_iter_id = res.data.data['branch_name']
            })

            },
        },
    }
</script>

<style scoped>

</style>
