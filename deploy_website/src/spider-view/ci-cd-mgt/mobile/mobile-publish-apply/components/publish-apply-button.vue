<template>
  <div>
    <Button
        ghost
        type="primary"
        style="text-align: left; display: inline-block; width:60px;margin-top:-1px"
        @click="apply"
    >申请
    </Button>

    <Modal
        v-model="alert_modal"
        title="以下应用正在申请，是否要继续申请？"
        :columns="alert_table_columns"
        :tableData="alert_modal_data"
        @on-cancel="cancelApply"
    >
      <div>
        <Row>
          <Table
              :columns="alert_table_columns"
              :data="alert_modal_data"
          >
          </Table>
        </Row>
      </div>

      <div slot="footer">
        <Button @click="apply()" :disabled="continue_apply_btn">继续申请</Button>
        <Button @click="cancelApply()">取消申请</Button>
      </div>
    </Modal>
    <ConfirmModal ref="hd_confirm_modal"
                  titleInfo="以下为正在进行灰度的分支，请于相关人员确认项目情况无冲突后继续申请！"
                  :IsModalred="true"
                  :columns="conflict_table_columns"
                  :tableData="conflict_table_data"
                  @continue="publishApply"></ConfirmModal>
  </div>
</template>

<script>
import {
  NoticeApplyByWechat,
  externalServiceResult,
  h5PublishApplyApi,
  appPublishApplyApi,
  appTagPublishApplyApi,
  testPublishCheckApi,
  CheckH5ZipVersionWhetherPublish,
  HdStatusCheckApi,


} from "@/spider-api/h5";
import {
  formatDate
} from "@/spider-api/h5-common";
import ConfirmModal from "../../mobile-apply/components/confirm-modal";
import {
  h5ProApplyNotice
} from "@/spider-api/iter-plan";
import {
  h5IterConfirmStatusApi
} from "@/spider-api/publish";

export default {
  name: "PublishApplyButton",
  components: {
    ConfirmModal,

  },
  data() {
    return {
      alert_modal: false,
      continue_apply_btn: false,
      alert_modal_data: [],
      alert_table_columns: [
        {
          title: '应用',
          key: 'app_name'
        },
        {
          title: '状态',
          key: 'status_display',
          render: (h, params) => {

            return h("div", [
              h("p",
                  {props: {}, style: {color: 'red'}},
                  params.row.status_display)
            ]);
          }
        },
        {
          title: '上一次申请时间',
          key: "operate_time",
          //sortable: true,
          render: (h, params) => {
            let value = params.row.operate_time
            if (value) {
              value = formatDate(new Date(params.row.operate_time), 'yyyy-MM-dd hh:mm:ss')
            }
            return h('div', value)
          }
        },
        {
          title: '申请提示',
          key: "alert_content",
          //sortable: true,
          render: (h, params) => {
            return h("div", [
              h("p",
                  {props: {}, style: {color: params.row.alert_modal_color}},
                  params.row.alert_content)
            ])
          }
        }],
      conflict_table_data: [],
      conflict_table_columns: [
        {
          title: '灰度环境的分支',
          key: 'br_name',
          align: 'center'
        },
        {
          title: '灰度环境的应用',
          key: 'app_name',
          align: 'center'
        },
        {
          title: '申请人',
          key: 'user_name',
          align: 'center'
        },
        {
          title: '申请时间',
          key: 'operate_time',
          align: 'center',
          render: (h, params) => {
            return h('div',
                formatDate(new Date(params.row.operate_time), 'yyyy-MM-dd hh:mm')
            )
          }
        }
      ],
    }

  },
  props: {
    // publish_apply_data: {
    //       type: Array,
    //       default:() =>[]
    //   },
    select_data: {
      type: Array,
      default: () => []
    },
    app_array: {
      type: Array,
      default: () => []
    },
    iter_id: {
      type: String,
      default: ''
    },
    br_name: {
      type: String,
      default: ''
    },
    code_type: {
      type: String,
      default: ''
    },
    env: {
      type: String,
      default: ''
    },
    group: {
      type: String,
      default: ''
    },
    apply_content: {
      type: String,
      default: ''
    },

  },
  methods: {
    cancelApply() {
      this.alert_modal = false
    },
    checkStatus(select_app_array) {
      var myDate = new Date()
      if (select_app_array.length == 0) {
        this.$Notice.error({
          desc: '请选择申请应用',
          duration: 0,
        });
        return false;
      }
      if (this.env == "prod") {
        console.log('this.selected_array111====' + JSON.stringify(select_app_array))
        console.log('this.app_array111====' + this.app_array)
        if (select_app_array.length != this.app_array.length) {
          this.$Notice.warning({
            desc: '生产环境需选择所有应用申请',
            duration: 0,
          });
          return false;
        }
      }

      for (let row of select_app_array) {
        // 校验dist 是否重复版本
        if (this.env == "prod" && row['package_type'] == 'dist') {
          console.log('应用存在')
          let params = {
            'app_name': row["app_name"],
            'suite_code': row["env"],
            'end_ver': row["end_ver"]
          }
          console.log(params)
          CheckH5ZipVersionWhetherPublish(params).then(res => {
            if (res.data.msg == '版本重复') {
              console.log(res.data.data[0]['datetime'])
              alert('该版本' + res.data.data[0]['datetime'] + '已申请过')
              this.$Notice.error({desc: '发布的版本重复', duration: 0,})
              return false
            }
          })
        }

        // 发布状态校验
        if (row['status_display'] == '发布中') {
          let end_time = formatDate(myDate, 'yyyy-MM-dd h:m:s').replace(/\-/g, "/")
          let begin_time = formatDate(new Date(row['operate_time']), 'yyyy-MM-dd hh:mm:ss').replace(/\-/g, "/")
          let t = parseInt(new Date(end_time) - new Date(begin_time)) / 1000 / 60
          if (t > 5) {
            var alert_content = '上一次申请是' + t.toFixed(1) + '分钟之前'
            var alert_modal_color = 'green'
          } else {
            this.continue_apply_btn = true
            var apply_time = parseInt(5) - t
            var alert_content = apply_time.toFixed(1) + '分钟后再申请'
            var alert_modal_color = 'red'

          }
          this.alert_modal_data.push({
            'app_name': row['app_name'], 'status_display': row['status_display'],
            'operate_time': row['operate_time'], 'alert_content': alert_content, 'alert_modal_color': alert_modal_color
          })
          console.log(row['app_name'])
          console.log(row['status_display'])
        }
      }
      if (this.alert_modal_data.length != 0) {
        console.log('有应用正在申请')
        console.log(this.alert_modal_data)
        this.alert_modal = true
        return false
      } else {
        console.log('没有正在申请应用')
        return true
      }
    },

    publishApply() {
      //this.$emit('click', apply_param);
      this.$refs.hd_confirm_modal.closeModal()
      console.log(this.select_data)
      let res = this.checkStatus(this.select_data)
      console.log(res)
      this.$emit('getApplyParam', val => {

        console.log(val)
        if (val.length == 0) {
          console.log("没有可用数据")
        } else {
          //this.sendAffirmEmail(val)
          console.log("开始发布申请")
          h5PublishApplyApi(val).then(res => {
            if (res.data.status == 'failed') {
              //失败
              //NoticeApplyByWechat(this.iter_id, this.env)
              this.$Notice.error({desc: res.data.msg, duration: 0})
            } else { //成功
              this.$Notice.success({desc: res.data.msg, duration: 5})
              // 发送给领导 确认邮件
              this.sendAffirmEmail(val)
              //this.$forceUpdate()//强制刷新当前页面
              //this.init()
            }
            this.$Spin.hide()
          })
              .catch(err => {
                this.$Spin.hide()
                this.$Notice.error({desc: err, duration: 0})
              });
        }
      })
    },
    //发送领导确认邮件

    sendAffirmEmail(apply_val) {
      //this.$Spin.show();
      h5IterConfirmStatusApi(this.iter_id, this.env).then(res => {
        if (res.data.status != "success") {
          let email_object = {
            "iter_id": this.iter_id,
            "proposer": "",
            "url": 'http://' + window.location.host,
            "env": this.env,
            "app_name_list": [],
            "fund_end_ver": "",
            "piggy_end_ver": "",
            "app_version": "",
            "br_name": this.br_name,
            "fund_h5_version": "",
            "piggy_h5_version": "",
            "h5_env": "",
            "apply_content": this.apply_content
          }
          for (let i of apply_val) {
            console.log(i.proposer)
            email_object.proposer = i.proposer
            // 检验发布申请人
            if (email_object.proposer == "" || email_object.proposer.length == 0) {
              this.$Notice.error({
                desc: '今日未收到确认，请选择确认人',
                duration: 0,
              });
              return false
            }
            email_object.app_name_list.push(i.app_name)
            if (i.package_type == "ios" || i.package_type == "android") {
              // 这里会互相覆盖，后端接口只支持一个打包版本，先兼容着改吧 20220822
              email_object.app_version = i.app_version
              email_object.h5_env = i.h5_env
              if (i.h5_app_name == "fund") {
                email_object.fund_h5_version = i.h5_version
              }
              if (i.h5_app_name == "piggy") {
                email_object.piggy_h5_version = i.h5_version
              }
            } else if (i.package_type == "dist") {
              if (i.app_name == "fund") {
                email_object.fund_end_ver = i.end_ver
              }
              if (i.app_name == "piggy") {
                email_object.piggy_end_ver = i.end_ver
              }
            }
          }
          h5ProApplyNotice(email_object.iter_id, email_object.proposer, email_object.url,
              email_object.env, email_object.app_name_list, email_object.fund_end_ver, email_object.piggy_end_ver,
              email_object.app_version, email_object.br_name, email_object.fund_h5_version,
              email_object.piggy_h5_version, email_object.h5_env, email_object.apply_content)
              .then(result => {
                if (result.data.status === "success") {
                  this.$Notice.success({
                    desc: result.data.msg,
                    duration: 5,
                  });
                } else {
                  this.$Spin.hide();

                  this.$Notice.error({
                    desc: result.data.msg,
                    duration: 0,
                  });
                }
              })
              .catch(err => {
                this.$Spin.hide();
                this.$Notice.error({
                  desc: err.response.data.msg,
                  duration: 0,
                });
              });
        }
      })
    },

    /**
     * 点击【申请】按钮，页面数据校验检查
     */
    apply() {
      this.alert_modal = false
      let app_package_type = ['ios', 'android']
      if (this.env == "prod") {

        console.log('====================开始产线申请===============')
        this.publishApply()
        //this.createH5AppBindNfApp()
      } else {
        console.log(this.select_data)
        for (let item in this.select_data) {
          console.log(this.select_data[item].app_version)
          if (app_package_type.includes(this.select_data[item].package_type)) {
            if (this.select_data[item].app_version.split('.').length != 4) {
              console.log('不符合命名规则')
              alert('App版本名称:' + this.select_data[item].app_version + '不符合灰度App版本命名规则x.x.x.x')
              return false
            }
          }
        }
        console.log('====================开始灰度申请===============')
        // check有没有其他应用在灰度
        this.hdApply()
        //this.createH5AppBindNfApp()
      }
      //this.sendAffirmEmail()
    },

    /**
     * 灰度申请,检查是否有其它分支正在进入灰度
     */
    hdApply() {
      let app_name_list = []
      for (let i of this.select_data) {
        app_name_list.push(i.app_name)
      }
      let req = {'iteration_id': this.iter_id, 'app_name_list': app_name_list, 'env': this.env}
      HdStatusCheckApi(req).then(res => {
        if (res.data.status === "success") {
          this.publishApply()
        } else {
          // 存在差异打开确认框今日未收到确认，请选择确认人
          this.$refs.hd_confirm_modal.modal_confirm = true
          console.log(res.data.data["conflict_app"])
          this.conflict_table_data = res.data.data["conflict_app"]
        }
      })
    },

  }
}
</script>

<style scoped>

</style>
