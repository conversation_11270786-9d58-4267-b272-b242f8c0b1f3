<template>
  <div>
      <Col span="9" style="margin-right: 10px">
      <Card>
        <Row >
           <i-col style="margin: 10px;text-align: right" span="6">
            <span style="text-align: right; display: inline-block;">上传版本：</span>
          </i-col>
         <Col span="14">
            <Input v-model="current_upload_version" @on-change="changeVersion" placeholder="请输入"></Input>
          </Col>
        </Row>
        <Row >
          <i-col style="margin: 10px;text-align: right" span="6">
              <span style="text-align: right; display: inline-block;">上传描述：</span>
            </i-col>
          <Col span="14">
          <Input v-model="current_upload_desc" @on-change="changeDesc" type="textarea" :rows="3" placeholder="上传描述" style="width: 200px"/>
          </Col>
        </Row>
      </Card>
  </Col>
  </div>
</template>

<script>
    export default {
        name: "MiniProgramParams",
        data() {
          return {
            current_upload_version: this.upload_version,
            current_upload_desc: this.upload_desc
          }

        },
        props: {
           upload_version: {
                type: [String, Number],
                default: ''
            },
          upload_desc: {
                type: String,
                default: ''
            },

        },
        methods: {
           changeVersion(){
              this.$emit("set_upload_version",this.current_upload_version)
           },
            changeDesc(){
             this.$emit("set_upload_desc",this.current_upload_desc)
           }
        }
    }
</script>

<style scoped>

</style>
