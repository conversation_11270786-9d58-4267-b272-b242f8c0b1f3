<template>
  <div>
    <Table stripe :data="app_array" :columns="columns"
           @on-select="applyListSelect" @on-select-cancel="cancelApplyListSelect" @on-select-all="applyListSelectAll"
           @on-select-all-cancel="applyListCancelSelectAll" ref="my_table">
    </Table>
  </div>
</template>

<script>
import {
  GetMobileCiInfo
} from '@/spider-api/ci/mobile-ci/mobile-ci-info'
import { getH5AppBindNfAppApi } from '@/spider-api/h5-ci-cd/nf-app'
import store from '@/spider-store'
import {
  h5PublishApplyApiGet,

  getErrNoticeShowTime,
  getSuccessNoticeShowTime,
  getInfoNoticeShowTime

} from '@/spider-api/h5'
import {
  formatDate
} from '@/spider-api/h5-common'
import { refreshExecStatusApi } from '@/spider-view/ci-cd-mgt/mobile/mobile-utils/utils'
import app from '../../../../../store/module/app'

export default {
  name: 'PublishApplyTable',

  props: {
    iter_id: String,
    br_name: String,
    env: String,
    is_refresh: Boolean
  },
  data () {
    return {
      actionItem: 'publish_apply',
      app_array: [],
      selected_array: [],
      columns: [
        {
          type: 'selection',
          minWidth: 50
        },
        { title: '应用', minWidth: 100, key: 'app_name' },
        { title: '应用描述', minWidth: 100, key: 'module_desc' },
        { title: '平台类型', minWidth: 100, key: 'platform_code' },
        { title: '仓库', minWidth: 160, key: 'git_path' },
        {
          title: '上次申请环境',
          minWidth: 110,
          key: 'suite_name',
          render: (h, params) => {
            let value = params.row.suite_name
            if (params.row.suite_name == null) {
              return h('div', '无')
            } else if (params.row.suite_name != null && params.row.suite_name.length == 0) {
              return h('div', '无')
            } else {
              let str = ''
              for (let i of value) {
                str += i
              }
              return h('div', str)
            }
          }
        },
        {
          title: '申请时间',
          minWidth: 150,
          key: 'operate_time',
          // sortable: true,
          render: (h, params) => {
            let value = params.row.operate_time
            if (value) {
              value = formatDate(new Date(params.row.operate_time), 'yyyy-MM-dd hh:mm:ss')
            }
            return h('div', value)
          }
        },
        { title: '操作人', minWidth: 100, key: 'username' },
        {
          title: '申请状态',
          minWidth: 100,
          key: 'status_display',
          render: (h, params) => {
            let color = '#2db7f5'
            if (params.row.status && params.row.status.indexOf('success') >= 0) {
              color = 'green'
            } else if (params.row.status && params.row.status.indexOf('failure') >= 0) {
              color = 'red'
            }
            if (params.row.status && params.row.status.indexOf('running') >= 0) {
              return h('div',
                [h('a', {
                  on: {
                    click: () => {
                      let job_url = params.row.job_url
                      if (job_url) {
                        window.open(job_url)
                      } else {
                        this.$Notice.info({
                          title: '暂无详情',
                          duration: getInfoNoticeShowTime()
                        })
                      }
                    }
                  }
                }, params.row.status_display)
                ])
            } else {
              return h('Tooltip', {
                props: {
                  placement: 'top',
                  content: params.row.message,
                  'max-width': 500,
                  transfer: true
                }
              }, [h('p',
                { props: {}, style: { color: color } },
                params.row.status_display)]
              )
            }
          }
        },
        { title: '确认状态', minWidth: 100, key: 'email_status_display' },
        {
          title: '确认时间',
          minWidth: 150,
          key: 'affirm_time',
          render: (h, params) => {
            let value = params.row.affirm_time
            if (value == '' || value == null) {

            } else {
              value = formatDate(new Date(params.row.affirm_time), 'yyyy-MM-dd hh:mm:ss')
            }
            return h('div', value)
          }
        },
        { title: '确认人', minWidth: 100, key: 'assertor' },
        {
          title: '详情',
          minWidth: 180,
          key: 'job_url',

          render: (h, params) => {
            let color = '#2db7f5'
            if (params.row.status && params.row.status.indexOf('success') >= 0) {
              color = 'green'
            } else if (params.row.status && params.row.status.indexOf('failure') >= 0) {
              color = 'red'
            }
            return h('div', [
              h(
                'Button',
                {

                  props: {},
                  style: {
                    size: 'small',
                    color: color,
                    width: '80px'
                  },
                  on: {
                    click: () => {
                      // this.get_ci_info_detail()
                      if (params.row.job_url == null) {
                        this.$Message.info({
                          content: '暂未生成构建链接，请耐心等待',
                          duration: 10
                        })
                      } else {
                        if (Object.keys(params.row.suite_job_url).length > 0) {
                          window.open(
                            params.row.suite_job_url.main
                          )
                        } else {
                          console.log(params.row.job_url)
                          window.open(
                            params.row.job_url
                          )
                        }
                      }
                    }
                  },
                  attrs: {
                    class: 'ivu-btn ivu-btn-primary ivu-btn-ghost',
                    title: '构建详情BlueOcean'
                  }
                },
                '详情',
                params.row.msg
              )
              // h(
              //   'Button',
              //   {

              //     props: {},
              //     style: {
              //       size: 'small',
              //       color: color,
              //       width: '80px'
              //     },
              //     on: {
              //       click: () => {
              //         // this.get_ci_info_detail()
              //         if (params.row.job_url == null) {
              //           this.$Message.info({
              //             content: '暂未生成构建链接，请耐心等待',
              //             duration: 10
              //           })
              //         } else {
              //           if (Object.keys(params.row.suite_job_url).length > 0) {
              //             window.open(
              //               params.row.suite_job_url.backup
              //             )
              //           } else {
              //             console.log(params.row.job_url)
              //             window.open(
              //               params.row.job_url
              //             )
              //           }
              //         }
              //       }
              //     },
              //     attrs: {
              //       class: 'ivu-btn ivu-btn-primary ivu-btn-ghost',
              //       title: '构建详情BlueOcean'
              //     }
              //   },
              //   '详情（备）',
              //   params.row.msg
              // )
            ])
          }
        },
        {
          title: '操作',
          minWidth: 100,
          render: (h, params) => {
            return h('div', [
              h(
                'Button',
                {

                  props: {},
                  // style: {
                  //   color: color
                  // },
                  on: {
                    click: () => {
                      refreshExecStatusApi({
                        'app_name': params.row.app_name,
                        'iter_id': this.iter_id,
                        'refresh_action_item_list': ['publish_apply']
                      })
                      // this.get_ci_info_detail()
                    }
                  }

                },
                '强制刷新',
                params.row.msg
              )
            ])
          }
        }
      ],
      is_open: true
    }
  },
  methods: {
    init () {

    },
    applyListSelectAll (params) {
      this.applyListSelect(params)
    },
    applyListCancelSelectAll (selection, row) {
      this.app_array.forEach(app => {
        // app._checked = false
        this.$set(app, '_checked', false)
      })
      this.selected_array = []
      this.$emit('set_selected_info', this.selected_array)
    },
    applyListSelect (params) {
      this.selected_array = params
      this.app_array.forEach(app => {
        let index = params.findIndex(item => item.platform_code === app.platform_code && app.package_type === 'dist')
        if (index >= 0) {
          let tempApp = params.find(item => item.app_name === app.app_name)
          if (!tempApp) {
            this.selected_array.push(app)
          }
        }
      })
      this.$emit('set_selected_info', this.selected_array)
      for (let i in this.app_array) {
        let _checked = false
        for (let s in this.selected_array) {
          if (this.selected_array[s].app_name == this.app_array[i].app_name) {
            _checked = true
          }
        }
        // this.app_array[i]._checked = _checked
        this.$set(this.app_array[i], '_checked', _checked)
      }
    },
    cancelApplyListSelect (select, row) {
      let temp = []
      this.selected_array.forEach(app => {
        if (row.package_type !== 'dist') {
          if (row.app_name !== app.app_name) {
            temp.push(app)
          }
        } else if (row.platform_code !== app.platform_code && app.package_type === 'dist') {
          temp.push(app)
        }
      })
      this.selected_array = temp

      this.app_array.forEach(app => {
        if (row.package_type === 'dist') {
          if (row.platform_code === app.platform_code) {
            // app._checked = false
            this.$set(app, '_checked', false)
          }
        } else {
          if (row.app_name === app.app_name) {
            // app._checked = false
            this.$set(app, '_checked', false)
          }
        }
      })

      console.log(this.selected_array)
      this.$emit('set_selected_info', this.selected_array)
    },
    getSelection (app_array) {
      let selectionIndexes = []
      for (let i in app_array) {
        if (app_array[i]._checked) selectionIndexes.push(parseInt(i))
      }
      return JSON.parse(JSON.stringify(app_array.filter((data, index) => selectionIndexes.indexOf(index) > -1)))
    },
    // 轮询状态
    loopTableData () {
      console.debug('will wait')
      let _this = this
      this.getTableData(this.env, false)
      if (this.is_refresh && this.is_open) {
        setTimeout(function () {
          _this.loopTableData()
        }, 4000)
      }
    },
    getTableData (env, is_clare_selection = true) {
      // console.log(is_clare_selection)
      // for (let i in this.app_array) {
      //     console.log("原始的状态11111"+this.app_array[i]._checked)
      //  }
      if (env == '') {
        return
      }

      h5PublishApplyApiGet(this.iter_id, env, this.br_name).then(res => {
        // 我在这里拼装 app_array
        if (is_clare_selection) {
          this.app_array = JSON.parse(JSON.stringify(res.data.data.ci_info))
          for (let item in this.app_array) {
            this.app_array[item]['suite_code'] = env
            this.app_array[item]['is_silent'] = '0'
          }
          let selected_array = this.getSelection(this.app_array)
          // console.log(selected_array)
          this.applyListSelect(selected_array)
        } else {
          // 数据刷新时候处理选中数据 20220818 by 帅
          for (let row_id in this.app_array) {
            // console.log("原始的状态"+this.app_array[row_id]._checked)
            for (let ci of res.data.data.ci_info) {
              if (this.app_array[row_id].app_name === ci.app_name) {
                // console.log("原始的状态"+this.app_array[row_id]._checked)
                this.app_array[row_id].suite_name = ci.suite_name
                this.app_array[row_id].operate_time = ci.operate_time
                this.app_array[row_id].username = ci.username
                this.app_array[row_id].status_display = ci.status_display
                this.app_array[row_id].message = ci.message
                this.app_array[row_id].status = ci.status
                this.app_array[row_id].job_url = ci.job_url
              }
            }
          }
        }
        // this.app_array = res.data.data.ci_info
        console.debug(this.app_array)
        // this.$emit('set_table_info', this.app_array)
        // let selected_array = this.getSelection(this.app_array)
        // console.log(selected_array)
        // this.applyListSelect(selected_array)
      })
    }

  },
  destroyed () {
    this.is_open = false
  }
}
</script>

<style scoped>

</style>
