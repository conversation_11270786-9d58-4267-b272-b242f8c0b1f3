<template>
  <Card>
    <Row>
      <Button
        ghost
        type="primary"
        style="text-align: left; display: inline-block; width:80px; margin:5px"
        @click="static_batch_compile"
      >批量编译</Button>
      <Button
        ghost
        type="primary"
        style="text-align: left; display: inline-block; width:80px; margin:5px"
        @click="batch_publish"
      >批量发布</Button>
      <Button
        ghost
        type="primary"
        style="text-align: left; display: inline-block; width:80px; margin:5px"
        ref="environment_banding"
        @click="environment_banding"
      >环境绑定</Button>
    </Row>
    <Row>
      <tables stripe v-model="static_props" :columns="columns" @on-selection-change="selectChange">

      </tables>
    </Row>
    <Modal
      v-model="environment_modal_val"
      title="批量环境绑定"
      @on-ok="environment_modal_ok"
      @on-cancel="environment_modal_cancel">
      <Card v-if="table_selection.length > 0">
        <Row v-for="(item,index) in table_selection" :key="item.app_name">
          <i-col style="margin: 10px" span="4">
            <span style="text-align: left; display: inline-block;">{{ item.app_name }}</span>
          </i-col>
          <Col span="12" style="margin: 5px">
            <Select multiple v-model="environment[index]">
              <Option
                v-for="item in environment_list[index]"
                :value="item"
                :key="item"
              >{{ item }}</Option>
            </Select>
          </Col>
        </Row>
      </Card>
      <Card v-if="table_selection.length == 0">至少勾选一个应用</Card>
    </Modal>
    <Modal v-model="compile_modal_val"
           title="发布与编译" >
      <Card>
        <Row>
          您可以选择直接发布或先编译后发布
        </Row>
      </Card>
      <div slot="footer">
        <Button size="large" @click="publish(publish_array)">直接发布</Button>
        <Button size="large" @click="publishAndCompile(publish_array)">先编译后发布</Button>
      </div>
    </Modal>
  </Card>
</template>

<script>
import store from "@/spider-store"
import Tables from "@/components/tables";
import {
  h5CompileApi,
  h5TestPublishApi,
  compileCheckApi,
  externalServiceResult,
  userAction,
  h5CiPipelineApi,
  getInfoNoticeShowTime,
  getErrNoticeShowTime,
  h5PipelineStop, h5PipelineIsRunning
} from "@/spider-api/h5";
import {
  h5ElapseStatsApiGet
} from "@/spider-api/h5-stats"
import {
  test,
  formatDate,
} from "@/spider-api/h5-common";
export default {
  name: "h5-test-pc",
  components: {
    Tables,
  },
  props:{
    //这里是table中的数据
    static_props:{
      type:Array
    },
    initTable:{
      type: Function,
    },
  },
  data () {
    return {
      last_elapsed: "-",
      current_elapsed: "准备中",
      table_selection:[],
      environment_modal_val: false,
      compile_modal_val: false,
      environment_list:[],
      environment: [],
      checkMsg:[],
      publish_data:[],
      compile_array:[],
      publish_array:[],
      columns:[
        {
          type: 'selection',
          width: 50,
        },
        {title: "应用", key: "app_name" },
        {title: "仓库", key: "git_path" },
        {
          title: "操作时间",
          key: "operate_time",
          //sortable: true,
          render: (h, params) => {
            let value = params.row.operate_time
            if(value == '' || value == null){

            }else {
              value = formatDate(new Date(params.row.operate_time),'yyyy-MM-dd hh:mm:ss')
            }
            return h('div',value)
          }
        },
        {title:'发布环境', key:'suite_name'},
        {
          title: "绑定环境",
          key: "suite_code",
          render: (h, params) => {
            let value = params.row.suite_code
            if(params.row.suite_code == null || params.row.suite_code == ''){
              return h('div','无')
            }else {
              let str = '';
              if(value instanceof Array){
                console.log('i am Array')
                console.log('=========== value =========')
                console.log(value)
                for(let i of value){
                  str += i + ','
                }
              }
              if(typeof value === 'string'){
                console.log('i am String')
                str += value
              }
              //有多选却不输入值的
              let returnArray = []
              let returnStr = ''
              returnArray = str.split(',')
              for(let i of returnArray){
                if(i != '' && i != null && i != undefined){
                  console.log('我这个环境不为空')
                  returnStr += i + ','
                  console.log(returnStr)
                }
              }
              if(returnStr != '' && returnStr.charAt(returnStr.length -1) == ','){
                returnStr = returnStr.substr(0,returnStr.length-1)
              }
              return h('div',returnStr)
            }
          }
        },
        /*{title: "申请状态", key: "email_status" },*/
        {
          title: "发布状态",
          key: "status",
          render: (h, params) => {
            let stat_dict = {
              'running': '执行中',
              'success': '执行成功',
              'failure': '执行失败',
              'compile_running': '编译中',
              'compile_success': '编译成功',
              'compile_failure': '编译失败',
              'publish_running': '发布中',
              'publish_success': '发布成功',
              'publish_failure': '发布失败',
              'aborted': '已终止',
            }
            let status = params.row.status
            if (stat_dict.hasOwnProperty(status)) {
              var status_display = stat_dict[status]
            } else {
              var status_display = status
            }
            let action_type = ""
            if (status == 'compile_running') {
              action_type = 'compile'
            } else if (status == 'publish_running') {
              action_type = 'remote'
            }
            if (action_type) {
              return h('div',
                [h('a',
                  [h('Poptip',
                    {
                      props: {
                        transfer: true,
                        trigger: 'click',
                        title: '上次耗时: ' + this.last_elapsed,
                        content: '当前耗时：' + this.current_elapsed,
                        size: 'small'
                      },
                      on: {
                        'on-popper-show': () => {
                          h5ElapseStatsApiGet(params.row.app_name, action_type, store.state.h5_branch_version).then(res => {
                            if (res.data.status === "success") {
                              this.last_elapsed = res.data.data['last_elapsed']
                              this.current_elapsed = res.data.data['current_elapsed']
                            }
                          })
                        },
                        'on-popper-hide': () => {
                          this.last_elapsed = 0
                          this.current_elapsed = 0
                        }
                      }
                    },status_display)
                  ])
                ])
            } else if(status == 'compile_success' || status == 'publish_success'){
              return h('p',
                {style: {color: 'green'}},
                status_display)
            } else if(status == 'compile_failure' || status == 'publish_failure'){
              return h('p',
                {style: {color: 'red'}},
                status_display)
            } else {
              return h('p',
                {style: {color: '#515a6e'}},
                status_display)
            }
          }
        },
        {title: "操作人", key: "username" },
        {
          title: '操作',
          key: 'action',
          width: 200,
          align: 'center',
          render: (h, params) => {
            return h('div', [
              // h('Button', {
              //   props: {
              //     type: 'default',
              //     size: 'small'
              //   },
              //   style: {
              //     marginRight: '5px'
              //   },
              //   on: {
              //     click: () => {
              //       if(params.row.suite_code == '' || params.row.suite_code == null){
              //         console.log(params.row.suite_code)
              //         this.$Notice.info({
              //           title: '发布前需要先绑定环境',
              //           duration: getInfoNoticeShowTime(),
              //         });
              //         return;
              //       }
              //       let publish_array = []
              //       publish_array.push(params.row)
              //       console.log("====params.row=======")
              //       console.log(params.row)
              //       //检查用的第一步
              //       //组装数据
              //       let app_name_list = []
              //       let iteration_id = store.state.h5_group + '_' + store.state.h5_branch_version
              //       let req = {'iteration_id':iteration_id , 'app_name_list':app_name_list}
              //       app_name_list.push(params.row.app_name)
              //       this.publish_array = publish_array
              //       if(params.row.status == 'compile_running'){
              //         console.log('running')
              //         this.$Notice.info({
              //           title: '已经开始编译，请等待编译结束',
              //           duration: getInfoNoticeShowTime(),
              //         });
              //       }else if(params.row.status == 'publish_running'){
              //         this.$Notice.info({
              //           title: '已经开始发布，请等待发布结束',
              //           duration: getInfoNoticeShowTime(),
              //         });
              //       }else if(params.row.status == 'compile_success' || params.row.status == 'publish_success'){
              //         // 测试环境的check和发布/编译完全分开，写成两个异步接口
              //         // this.checkBeforePublish(req,publish_array)
              //         //如果是编译成功，只执行发布
              //         this.publish(publish_array)
              //       }else {
              //         // compile_failure编译失败  aborted已取消  发布失败publish_failure
              //         // 弹框 --> 是否编译
              //         this.compile_modal_val = true
              //
              //       }/*else{
              //         console.log('=============== publish_array =============')
              //         console.log(publish_array)
              //         //既编译又发布，调用编译检查接口
              //         this.checkBeforeCompile(req,publish_array)
              //         this.publishAndCompile(publish_array)
              //       }*/
              //     }
              //   }
              // }, '发布'),
              h('Button', {
                props: {
                  type: 'default',
                  size: 'small'
                },
                style: {
                  marginRight: '5px'
                },
                on: {
                  click: () => {
                    if (params.row.status == undefined) {
                      this.$Notice.info({
                        desc: '未执行',
                        duration: getInfoNoticeShowTime(),
                      });
                      return;
                    }
                    if (params.row.status.indexOf('running') < 0) {
                      this.$Notice.info({
                        desc: '不处于运行中',
                        duration: getInfoNoticeShowTime(),
                      });
                      return;
                    }

                    this.stopRun(params.row)
                  }
                }
              }, '终止'),
              h('Button', {
                props: {
                  type: 'default',
                  size: 'small'
                },
                style: {
                  marginRight: '5px'
                },
                on: {
                  click: () => {
                    let job_url = params.row.job_url
                    if(job_url != null){
                      window.open(params.row.job_url)
                    }else{
                      this.$Notice.info({
                        title: '暂无详情',
                        duration: getInfoNoticeShowTime(),
                      });
                    }
                  }
                }
              }, '详情')
            ]);
          }
        },
      ],
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init(){
    },
    /**
     * iview 自带表格多选
     */
    selectChange(selection, row){
      console.log(selection)
      this.table_selection = selection;
      //不光要改selection，static_props作为父组件传入的remote全部数据，我们需要修改他的_checked属性，
      // 在点击【环境绑定】按钮会根据static_props的_checked属性确定有多少个下拉框
      for(let i of this.static_props){
        if(i.app_name != 'vendor'){
          i._checked = false
        }
      }
      for(let s of selection){
        for(let r of this.static_props){
          if(s.app_name == r.app_name){
            r._checked = s._checked
          }
        }
      }
    },
    /**
     * 批量编译
     */
    static_batch_compile(){
      //组装数据
      let app_name_list = []
      let iteration_id = store.state.h5_group + '_' + store.state.h5_branch_version
      //由于记录了历史记录，所以我们直接根据历史记录回填的页面，不会走多选框on-change方法 --> 批量发布那里也要加
      /*if(this.table_selection.length == 0){
        for(let i of this.static_props){
          //如果是选中的话
          if(i._checked == true){
            this.table_selection.push(i)
          }
        }
      }*/
      this.table_selection = []
      for(let i of this.static_props){
        //如果是选中的话
        if(i._checked == true){
          this.table_selection.push(i)
        }
      }
      for(let i of this.table_selection){
        app_name_list.push(i.app_name)
      }
      let req = {'iteration_id':iteration_id , 'app_name_list':app_name_list}
      let compileObj = {}
      let compileList = []
      // 用于记录所有正在编译的应用，告知用户这些应用正在进行编译，请等待编译结束后再次编译
      let compileRunningList = []
      for(let i of this.table_selection){
        if(i.status != 'compile_running'){
          compileObj = {
            'suite_code':i.suite_code,
            'begin_ver':'',
            'end_ver':'',
            'iteration_id':store.state.iterationID,
            'br_name': store.state.h5_branch_version,
            'app_name':i.app_name,
            'is_silent':'1',
            'action_item':store.state.iterationID + '_' + 'test_static_compile',
            'status':i.status,
          }
          compileList.push(compileObj)
        } else {
          compileRunningList.push(i.app_name)
        }
      }
      if(compileRunningList.length == 0 && (compileList == null || compileList.length == 0)){
        this.$Notice.error({
          desc: '至少选择一个应用',
          duration: getErrNoticeShowTime(),
        });
        return;
      }
      if(compileRunningList.length > 0){
        this.$Notice.info({
          desc: '以下应用' + compileRunningList + '正在进行编译，请等待编译结束后再次编译',
          duration: getErrNoticeShowTime(),
        });
      }
      console.log('============ compileList ============')
      console.log(compileList)
      if(compileList.length > 0 ){
        this.checkBeforeCompile(req,compileList)
        this.compile(compileList)
      }
    },
    /**
     * 编译之前，先进行检测
     */
    checkBeforeCompile(req,incomeArray){
      this.compile_array = incomeArray
      console.log('========= res out ===========')
      console.log(this.compile_array)
      //检测分为两部分，如果返回的全是success，才会执行  编译
      //第一部分
      compileCheckApi(req).then(res => {
        if (res.data.status === "success") {
          /*this.$Notice.success({
            desc: res.data.msg,
            duration: getInfoNoticeShowTime(),
          });*/
          console.log("============res =========")
          console.log(res.data.data)
          let sid = res.data.data.sid
          //检查用的第二步
          //这里是编译的验证 --> 编译的功能写在 第二步 校验的回调函数里了
          console.log('======== 进入第二步之前的 incomeArray ========')
          console.log(this.compile_array)
          this.checkCompileSecondStep(sid,incomeArray)
        } else {
          this.$Notice.error({
            desc: res.data.msg,
            duration: getErrNoticeShowTime(),
          });
        }
      })
    },
    /**
     * 第二步检测(编译)
     */
    checkCompileSecondStep(sid,incomeArray){
      externalServiceResult(sid).then(res => {
        console.log("======== externalServiceResult ===========")
        console.log(res)
        this.checkMsg.push(res)
        console.log('===== incomeArray '+store.state.count+' =========')
        console.log(this.compile_array)
        let status = res.data.data.status;
        let detail = res.data.data.detail;
        let count = store.state.check_count
        if (status == "success") {
          /*this.$Notice.success({
            desc:detail,
            duration: getInfoNoticeShowTime(),
          });*/
          store.commit("setCheckCount", 0)
          //开始编译
          // this.compile(this.compile_array)
        } else if (status == "failure") {
          this.$Notice.error({
            desc:detail,
            duration: getErrNoticeShowTime(),
          });
          store.commit("setCheckCount", 0)
        } else {
          console.log('======count========')
          console.log(count)
          if(count <= 60){
            let vm = this;
            setTimeout(function () {
              vm.checkCompileSecondStep(sid);
            }, 5000);
            count++
            store.commit("setCheckCount", count)
          }else {
            store.commit("setCheckCount", 0)
          }
        }
      })
    },
    /**
     * 编译  先编译，再发布
     */
    compile(param){
      console.log('========= compile START ============')
      let vm = this
      h5CompileApi(param).then(res => {
        console.log(res)
        if(res.status == '200'){
          //成功
          /*vm.$Notice.success({
            desc: res.data.msg,
            duration: getInfoNoticeShowTime(),
          });*/
        }else{
          //失败
          vm.$Notice.error({
            desc: res.data.msg,
            duration: getErrNoticeShowTime(),
          });
        }
        this.initTable()
      })
    },
    batch_publish(){
      //由于记录了历史记录，所以我们直接根据历史记录回填的页面，不会走多选框on-change方法 --> 批量发布那里也要加
      if(this.table_selection.length == 0){
        for(let i of this.static_props){
          //如果是选中的话
          if(i._checked == true){
            this.table_selection.push(i)
          }
        }
      }
      console.log(this.table_selection)
      //组装数据
      let app_name_list = []
      let iteration_id = store.state.h5_group + '_' + store.state.h5_branch_version
      let vendor_list = []
      for(let i of this.table_selection){
        if(i.status == 'compile_running'){
          this.$Notice.info({
            desc: '选中的应用已经开始编译，请等待编译结束',
            duration: getErrNoticeShowTime(),
          });
          return;
        }
        if(i.status == 'publish_running'){
          this.$Notice.info({
            desc: '选中的应用已经开始发布，请等待发布结束',
            duration: getErrNoticeShowTime(),
          });
          return;
        }
        //如果存在vendor，我们存他的环境会变成一个数组，而我们需要存一个字符串 --> 现在全变成多选了，除了vendor，别人也要处理
        let vendor_suite_code = []
        console.log('============= i.suite_code ==========')
        // i.suite_code instanceof String === false --> string变量似乎和new String()类型不同
        if(typeof i.suite_code === 'string'){
          console.log('i am String')
          vendor_suite_code = i.suite_code.split(',')
        }
        if(i.suite_code instanceof Array){
          console.log('i am Array')
          vendor_suite_code = i.suite_code
        }
        console.log('========= vendor =============')
        console.log(vendor_suite_code)
        let vendor_obj = {}
        for(let sc of vendor_suite_code){
          if(sc != null && sc != undefined && sc != ''){
            vendor_obj = {
              "suite_code": sc,
              "begin_ver": "",
              "end_ver": "",
              "iteration_id": i.iteration_id,
              "app_name": i.app_name,
              "is_silent": "1",
              'action_item':store.state.iterationID + '_' + 'test_static_publish'
            }
            vendor_list.push(vendor_obj)
          }
        }
        app_name_list.push(i.app_name)
      }
      if(vendor_list.length == 0){
        this.$Notice.error({
          desc: '至少选择一个应用',
          duration: getErrNoticeShowTime(),
        });
        return;
      }
      let req = {'iteration_id':iteration_id , 'app_name_list':app_name_list}
      console.log('======== START BATCH PUBLISH ============')
      console.log(vendor_list)
      this.checkBeforePublish(req)
      this.publish(vendor_list)
    },
    /**
     * 点击环境绑定按钮
     */
    environment_banding(){
      this.environment_modal_val = true;
      this.environment_list = new Array()
      console.log('-------- this.table_selection --------')
      console.log(this.table_selection)
      //由于记录了历史记录，所以我们直接根据历史记录回填的页面，不会走多选框on-change方法 --> 批量发布那里也要加
      if(this.table_selection.length == 0){
        for(let i of this.static_props){
          //如果是选中的话
          if(i._checked == true){
            this.table_selection.push(i)
          }
        }
      }
      for(let outer in store.state.suite_code_list){
        for(let inner of this.table_selection){
          if(outer === inner.app_name){
            this.environment_list.push(store.state.suite_code_list[outer])
          }
        }
      }
      //environment --> modal框中下拉框环境回填
      /*for(let i=0;i< this.table_selection.length;i++){
        this.environment[i] = this.table_selection[i].suite_code
      }*/
    },
    /**
     * 环境绑定 确定
     */
    environment_modal_ok () {
      let tableSelection = this.table_selection
      let environment = this.environment
      let staticProps = this.static_props;
      for(let outer=0; outer< tableSelection.length; outer++){
        //多选中向组件添加属性
        this.$set(this.table_selection[outer],'suite_code',environment[outer])
        this.$set(this.table_selection[outer],'_checked',true)
        for(let inner of staticProps){
          //找到我选中的那条记录在table中的位置，向组件添加属性
          if(inner.app_name == tableSelection[outer].app_name){
            this.$set(inner,'suite_code',environment[outer])
            this.$set(inner,'_checked',true)
          }
        }
      }
      //记录当前操作 --> 你绑定了环境
      let action_value_list = []
      let action_value_obj = {}
      for(let i of this.static_props){
        action_value_obj = {"app_name" : i.app_name, "suite_code" : i.suite_code}
        action_value_list.push(action_value_obj)
      }
      let param = {
        action_item: store.state.iterationID + '_' + 'test_static_suite_code',
        action_value:action_value_list,
      }
      userAction(param).then(res => {
        console.log(res)
        let status = res.data.status;
        if (status == "success") {
          console.log('------ success --------')
        } else {
          console.log('---------- 用户行为记录操作失败 ---------')
        }
      })
    },
    /**
     * 环境绑定 终止
     */
    environment_modal_cancel () {
      // this.$Message.info('Clicked cancel');
    },
    /**
     * 发布之前，先进行检测
     */
    checkBeforePublish(req){
      //检测分为两部分，如果返回的全是success，才会执行  编译
      //第一部分
      compileCheckApi(req).then(res => {
        if (res.data.status === "success") {
          /*this.$Notice.success({
            desc: res.data.msg,
            duration: getInfoNoticeShowTime(),
          });*/
          console.log("============res =========")
          console.log(res.data.data)
          let sid = res.data.data.sid
          //检查用的第二步
          //这里是编译的验证 --> 编译的功能写在 第二步 校验的回调函数里了
          console.log('======== 进入第二步之前的 incomeArray ========')
          console.log(this.publish_array)
          this.checkPublishSecondStep(sid,this.publish_array)
        } else {
          this.$Notice.error({
            desc: res.data.msg,
            duration: getErrNoticeShowTime(),
          });
        }
      })
    },
    /**
     * 第二步检测(发布)
     */
    checkPublishSecondStep(sid,incomeArray){
      externalServiceResult(sid).then(res => {
        console.log("======== externalServiceResult ===========")
        console.log(res)
        this.checkMsg.push(res)
        let status = res.data.data.status;
        let detail = res.data.data.detail;
        let count = store.state.check_count
        if (status == "success") {
          /*this.$Notice.success({
            desc:detail,
            duration: getInfoNoticeShowTime(),
          });*/
          store.commit("setCheckCount", 0)
          // this.publish(incomeArray)
        } else if (status == "failure") {
          this.$Notice.error({
            desc:detail,
            duration: getErrNoticeShowTime(),
          });
          store.commit("setCheckCount", 0)
        } else {
          console.log('======count========')
          console.log(count)
          if(count <= 60){
            let vm = this;
            setTimeout(function () {
              vm.checkPublishSecondStep(sid,incomeArray);
            }, 5000);
            count++
            store.commit("setCheckCount", count)
          }else {
            store.commit("setCheckCount", 0)
          }
        }
      })
    },
    //发布 现在变成了
    publish(param){
      let publish_list = []
      let publish_obj = {}
      for(let i=0; i< param.length; i++){
        publish_obj = {
          'suite_code':param[i].suite_code,
          'begin_ver':'',
          'end_ver':'',
          'iteration_id':store.state.iterationID,
          'br_name': store.state.h5_branch_version,
          'app_name':param[i].app_name,
          'is_silent':'1',
          'action_item':store.state.iterationID + '_' + 'test_static_publish'
        }
        publish_list.push(publish_obj)
      }
      console.log('============ publish_list =============')
      console.log(publish_list)
      h5TestPublishApi(publish_list).then(res => {
        console.log(res)
        if(res.data.status == 'success'){
          //成功
          /*this.$Notice.success({
            desc: res.data.msg,
            duration: getInfoNoticeShowTime(),
          });*/
        }else{
          //失败
          this.$Notice.error({
            desc: res.data.msg,
            duration: getErrNoticeShowTime(),
          });
        }
        this.initTable()
        this.compile_modal_val = false
      })
    },
    /**
     * 编译 + 发布
     * @param param
     */
    publishAndCompile(param){
      let publish_list = []
      let publish_obj = {}
      for(let i=0; i< param.length; i++){
        publish_obj = {
          'suite_code':param[i].suite_code,
          'begin_ver':'',
          'end_ver':'',
          'iteration_id':store.state.iterationID,
          'br_name': store.state.h5_branch_version,
          'app_name':param[i].app_name,
          'is_silent':'1',
          'action_item':store.state.iterationID + '_' + 'test_static_publish'
        }
        publish_list.push(publish_obj)
      }
      console.log('============ publish_list =============')
      console.log(publish_list)
      //h5TestPublishApi
      h5CiPipelineApi(publish_list).then(res => {
        console.log(res)
        if(res.data.status == 'success'){
          //成功
          /*this.$Notice.success({
            desc: res.data.msg,
            duration: getInfoNoticeShowTime(),
          });*/
        }else{
          //失败
          this.$Notice.error({
            desc: res.data.msg,
            duration: getErrNoticeShowTime(),
          });
        }
        this.initTable()
        this.compile_modal_val = false
      })
    },
    stopRun(params) {
      params['iteration_id'] = store.state.iterationID
      h5PipelineIsRunning(params).then(res => {
        console.log(res.data.data.running)

        if (res.data.data.running) {
          h5PipelineStop(params).then(res => {
            this.$Notice.info({
              desc: '正在终止！',
              duration: getInfoNoticeShowTime() + 10,
            });
          })
        } else {
          this.$Notice.error({
            desc: '未找到运行中的jenkins信息，可能还在准备中！',
            duration: getInfoNoticeShowTime(),
          });
        }
      })
    }
  },
}
</script>

<style scoped>

</style>
