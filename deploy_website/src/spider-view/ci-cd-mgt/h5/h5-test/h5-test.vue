<template>
  <div>
    <Card>
      <Row>
        <i-col style="margin: 10px;text-align: right" span="1">
          <span style="text-align: right; display: inline-block;">组：</span>
        </i-col>
        <i-col style="margin: 10px" span="3">
          <span style="text-align: left; display: inline-block;">{{group}}</span>
        </i-col>
        <i-col style="margin: 10px;text-align: right" span="2">
          <span style="text-align: right; display: inline-block;">分支版本：</span>
        </i-col>
        <i-col style="margin: 10px" span="3">
          <span style="text-align: left; display: inline-block;">{{branch_version}}</span>
        </i-col>
         <i-col v-show="show_tag" style="margin: 10px;text-align: right" span="2">
          <span style="text-align: right; display: inline-block;">TAG版本：</span>
        </i-col>
         <i-col v-show="show_tag" style="margin: 10px" span="3">
          <span style="text-align: left; display: inline-block;">{{tag_name}}</span>
        </i-col>
        <i-col style="margin: 10px" span="7">
          <span style="text-align: left; display: inline-block;color: red">注：若以下应用的配置存放路径有改变，请及时告知</span>
        </i-col>
      </Row>
      <Row>
        <Collapse v-model="collapse_value">
          <Panel name="1" v-if="showPC">
            PC发布
            <p slot="content"><H5TestPc :static_props=static_props :initTable="initTable"></H5TestPc></p>
          </Panel>
          <Panel name="2" v-if="showRemote">
            移动端远端发布
            <p slot="content"><H5TestRemote :remote_props=remote_props :initTable="initTable"
            ></H5TestRemote></p>
          </Panel>
          <Panel name="3" v-if="showDist">
            H5资源包发布
            <p slot="content"><H5TestZip :dist_props=dist_props :initTable="initTable" ref="h5_test_zip_method"></H5TestZip></p>
          </Panel>
          <Panel name="4" v-if="showApp">
            App资源包发布
            <p slot="content"><H5TestApp :app_props=app_props :initTable="initTable"></H5TestApp></p>
          </Panel>
        </Collapse>
      </Row>
    </Card>
  </div>
</template>

<script>
import store from "@/spider-store"
import H5TestPc from '@/spider-view/ci-cd-mgt/h5/h5-test/h5-test-pc'
import H5TestRemote from '@/spider-view/ci-cd-mgt/h5/h5-test/h5-test-remote'
import H5TestZip from '@/spider-view/ci-cd-mgt/h5/h5-test/h5-test-zip'
import H5TestApp from '@/spider-view/ci-cd-mgt/h5/h5-test/h5-test-app'
import {
  appSuiteCodeApi,
  userActionEnvGet,
  h5CiPipelineApiGet,
  userLatestActionEnvGet,
} from "@/spider-api/h5";
export default {
  name: "h5-test",
  components: {
    H5TestPc,
    H5TestRemote,
    H5TestZip,
    H5TestApp,
  },
  data () {
    return {
      collapse_value: '',//(store.state.h5_app_names)?'fund':1,
      group: '',
      branch_version: '',
      tag_name:'',
      show_tag:false,
      static_props :[],
      remote_props : [],
      dist_props: [],
      app_props: [],
      showPC: false,
      showRemote: false,
      showDist: false,
      showApp: false,
    }
  },
  mounted() {
    //this.init()
  },
  computed:{

  },
  methods: {
    //根据分支中选择不同的应用，控制不同Panel展开
    init() {
      this.initTable();
      //table数据拼装用环境 --> 转移到测试页面
      appSuiteCodeApi(store.state.iterationID).then(res => {
        store.commit("setSuiteCodeList",res.data.data.app_suite_info)//setSuiteCodeList
      })
    },
    /**
     * 第一次进入测试发布页面，进行初始化
     */
    initTable(){
      //从h5-branch.vue中带来 组
      this.group = store.state.h5_group
      //从h5-branch.vue中带来 分支版本
      this.branch_version = store.state.h5_branch_version
      this.tag_name= store.state.tag_name
      this.show_tag= store.state.show_tag
      this.iteration_id = store.state.iterationID
      //重新初始化，防止越加越多
      this.static_props = new Array()
      this.remote_props = new Array()
      this.dist_props = new Array()
      this.app_props = new Array()

      console.log('========== initTable ============')
      h5CiPipelineApiGet(this.iteration_id).then(res => {
        console.log('============ h5CiPipelineApiGet =========')
        console.log(res.data.data.ci_info)
        let return_array = [];
        let tempVendor = {};
        this.showPC = false;
        this.showRemote = false;
        this.showDist = false;
        this.showApp = false;
        for (let i of res.data.data.ci_info) {
          /**
           * app_mgt_app_build.package_type
           * = 'static'  --> PC发布
           * = 'remote'  --> 移动端远端发布
           * = 'dist'  --> H5资源包发布
           * = 'app'  --> app发布
           */
          if (i.package_type === 'static') {
            this.showPC = true;
            return_array.push('1')
            this.static_props.push(i)
          } else if (i.package_type === 'remote') {
            this.showRemote = true;
            return_array.push('2')
            if (i.app_name == 'vendor') {
              //将vendor默认勾选并且不可取消
              i._disabled = true
              i._checked = true
              //中间变量保存vendor，最后放入remote池中
              tempVendor = i
            } else {
              this.remote_props.push(i)
            }
          } else if (i.package_type === 'dist') {
            this.showDist = true;
            return_array.push('3')
            this.dist_props.push(i)
          } else if((i.package_type == 'ios' || i.package_type == 'android') && i.need_ops == 1){
            console.log('============ Im APP =========')
            // console.log(i)
            this.showApp = true;
            return_array.push('4')
            i._disabled = true
            i._checked = false
            this.app_props.push(i)
            console.log(this.app_props)
          }
          this.collapse_value = return_array
        }
        //确保vendor在列表的最后
        console.log('=========== tempVendor ==========')
        console.log(tempVendor)
        if(tempVendor != null && JSON.stringify(tempVendor) != '{}'){
          this.remote_props.push(tempVendor)
        }
        //看看以下三个行为谁是最新发生的 --> 环境绑定，编译，发布
        //环境绑定控制 对勾 && 绑定环境
        //发布 & 编译 控制 对勾
        //eg: 如果最新发生的行为是 环境绑定 --> 在static/remote/dist绑定过环境，查询最新的环境记录，并回填对勾
        //eg: 如果最新发生的行为是 发布 || 编译 --> 在static/remote/dist 发布 || 编译过，回填对勾

        //先看看 环境绑定，编译，发布 谁是最后一个动作
        // remote
        let remoteLatestParamList = []
        remoteLatestParamList.push(store.state.iterationID + '_' + 'test_remote_compile')
        remoteLatestParamList.push(store.state.iterationID + '_' + 'test_remote_publish')
        remoteLatestParamList.push(store.state.iterationID + '_' + 'test_remote_suite_code')
        this.userLatestActionEnvGet(remoteLatestParamList)
        // static
        let staticLatestParamList = []
        staticLatestParamList.push(store.state.iterationID + '_' + 'test_static_compile')
        staticLatestParamList.push(store.state.iterationID + '_' + 'test_static_publish')
        staticLatestParamList.push(store.state.iterationID + '_' + 'test_static_suite_code')
        this.userLatestActionEnvGet(staticLatestParamList)
        // dist
        let distLatestParamList = []
        distLatestParamList.push(store.state.iterationID + '_' + 'test_dist_compile')
        distLatestParamList.push(store.state.iterationID + '_' + 'test_dist_publish')
        distLatestParamList.push(store.state.iterationID + '_' + 'test_dist_suite_code')//暂时dist没有绑定环境，不过无所谓
        this.userLatestActionEnvGet(distLatestParamList)
        // 环境绑定
        let paramList = []
        let paramStatic = store.state.iterationID + '_' + 'test_static_suite_code'
        let paramRemote = store.state.iterationID + '_' + 'test_remote_suite_code'
        let paramDist = store.state.iterationID + '_' + 'test_dist_suite_code'
        paramList.push(paramStatic)
        paramList.push(paramRemote)
        paramList.push(paramDist)
        for(let param of paramList){
          let jsonArray = []
          userActionEnvGet(param).then(res => {
            console.log('========== userActionEnvGet  RES =======')
            console.log(res)
            if(res.data.data != null && res.data.data != undefined && res.data.data.length > 0){
              console.log('========== userActionGet =============')
              jsonArray = JSON.parse(res.data.data.replace(/'/g, '"'))//JSON.parse(res.data.data)//eval('('+res.data.data+')');
              console.log(jsonArray)
            }
            //如果有数据，替换 --> 暂时只处理remote
            if(jsonArray != null && jsonArray != undefined && jsonArray.length > 0){
              console.log('======== 上一次操作记录回填中 ==========')
              for(let outer of jsonArray){
                for(let r of this.remote_props){
                  if(r.app_name == outer.app_name){
                    if(outer.suite_code instanceof Array){
                      outer.suite_code = outer.suite_code.toString()
                    }
                    console.log('替换remote_props===========')
                    console.log(outer.suite_code)
                    //替换suite_code
                    this.$set(r,'suite_code',outer.suite_code)
                    /*if(outer.suite_code != null && outer.suite_code != ''){
                      this.$set(r,'_checked',true)
                    }*/
                  }
                }
                for(let r of this.static_props){
                  if(r.app_name == outer.app_name){
                    if(outer.suite_code instanceof Array){
                      outer.suite_code = outer.suite_code.toString()
                    }
                    console.log('替换static_props===========')
                    console.log(outer.suite_code)
                    //替换suite_code
                    this.$set(r,'suite_code',outer.suite_code)
                    /*if(outer.suite_code != null && outer.suite_code != ''){
                      this.$set(r,'_checked',true)
                    }*/
                  }
                }
              }
            }
          })
        }

      })

      //开始轮询10s修改status --> 记得打开
      this.queryState(this);
    },
    /**
     * 执行轮询 10s，修改目前的发布状态
     */
    queryState(h5_this) {
      //接着查询状态
      h5CiPipelineApiGet(h5_this.iteration_id).then(res => {
        console.log('轮询中。。。')
        if (res.data.data.ci_info != null) {
          let operator_info = {}
          for (let i of res.data.data.ci_info) {
            let app_name = i['app_name']
            let status = i['status']

            // 多机器执行
            if (app_name in operator_info) {
              let last_status = operator_info[app_name]['status']
              operator_info[app_name]['status'] = last_status == 'success' ? status : last_status
            } else {
              operator_info[app_name] = i
            }
          }
          console.log(operator_info)

          if (h5_this.static_props.length > 0) {
            for (let i in h5_this.static_props) {
              let app_name = h5_this.static_props[i]['app_name']
              let op_log = operator_info[app_name]

              for (let key in op_log) {
                if(key != 'suite_code'){
                  h5_this.$set(h5_this.static_props[i], key, op_log[key])
                }
              }
            }
          }
          if (h5_this.remote_props.length > 0) {
            for (let i in h5_this.remote_props) {
              let app_name = h5_this.remote_props[i]['app_name']
              let op_log = operator_info[app_name]

              for (let key in op_log) {
                //只刷新状态了 --> 发布环境不要刷掉
                if(key != 'suite_code'){
                  h5_this.$set(h5_this.remote_props[i], key, op_log[key])
                }
              }
            }
          }
          if (h5_this.dist_props.length > 0) {
            for (let i in h5_this.dist_props) {
              let app_name = h5_this.dist_props[i]['app_name']
              let op_log = operator_info[app_name]

              for (let key in op_log) {
                if(key != 'suite_code'){
                  h5_this.$set(h5_this.dist_props[i], key, op_log[key])
                }
              }
            }
          }
          if (h5_this.app_props.length > 0) {
            for (let i in h5_this.app_props) {
              let app_name = h5_this.app_props[i]['app_name']
              let op_log = operator_info[app_name]

              for (let key in op_log) {
                if(key != 'suite_code'){
                  h5_this.$set(h5_this.app_props[i], key, op_log[key])
                }
              }
            }
          }
        } else {
          if (h5_this.static_props.length > 0) {
            for (let i in h5_this.static_props) {
              h5_this.$set(h5_this.static_props[i], 'status', '')
            }
          }
          if (h5_this.remote_props.length > 0) {
            for (let i in h5_this.remote_props) {
              h5_this.$set(h5_this.remote_props[i], 'status', '')
            }
          }
          if (h5_this.dist_props.length > 0) {
            for (let i in h5_this.dist_props) {
              h5_this.$set(h5_this.dist_props[i], 'status', '')
            }
          }
          if (h5_this.app_props.length > 0) {
            for (let i in h5_this.app_props) {
              h5_this.$set(h5_this.app_props[i], 'status', '')
            }
          }
        }

        let waitTimeQueryAgain = false

        if (!waitTimeQueryAgain) {
          //判断是否需要轮训查询结果
          for (let i in h5_this.static_props) {
            if (h5_this.static_props[i]['status'] != null && h5_this.static_props[i]['status'].indexOf('running')>=0) {
              waitTimeQueryAgain = true
              break
            }
          }
        }
        if (!waitTimeQueryAgain) {
          //判断是否需要轮训查询结果
          for (let i in h5_this.remote_props) {
            if (h5_this.remote_props[i]['status'] != null && h5_this.remote_props[i]['status'].indexOf('running')>=0) {
              waitTimeQueryAgain = true
              break
            }
          }
        }
        if (!waitTimeQueryAgain) {
          //判断是否需要轮训查询结果
          for (let i in h5_this.dist_props) {
            if (h5_this.dist_props[i]['status'] != null && h5_this.dist_props[i]['status'].indexOf('running')>=0) {
              waitTimeQueryAgain = true
              break
            }
          }
        }
        if (!waitTimeQueryAgain) {
          //判断是否需要轮训查询结果
          for (let i in h5_this.app_props) {
            if (h5_this.app_props[i]['status'] != null && h5_this.app_props[i]['status'].indexOf('running')>=0) {
              waitTimeQueryAgain = true
              break
            }
          }
        }

        if (waitTimeQueryAgain) {
          console.log("h5_this is wait running")
          setTimeout(function () {
            h5_this.queryState(h5_this)
          }, 10000)
        }
      })
    },
    /**
     * 专门用于区别 static/remote/dist 发布/编译 对勾的回填
     * @param latestParamList
     */
    userLatestActionEnvGet(latestParamList){
      console.log('========== userLatestActionEnvGet ==========')
      console.log(latestParamList)
      console.log('============ this.remote_props =============')
      console.log(this.static_props)
      console.log(this.remote_props)
      console.log(this.dist_props)
      userLatestActionEnvGet(latestParamList).then(res => {
        console.log(res)
        let jsonArray = []
        console.log('=========== JSON.parse ==========')
        console.log(res.data.data)
        if(res.data.data != null && res.data.data != undefined && res.data.data.length > 0){
          jsonArray = JSON.parse(res.data.data.replace(/'/g, '"').replace('None','""'))//JSON.parse(res.data.data)//eval('('+res.data.data+')');
        }
        if(jsonArray != null && jsonArray != undefined && jsonArray.length > 0) {
          console.log('======== 上一次操作记录回填中 ==========')
          console.log(jsonArray)
          for (let outer of jsonArray) {
            for (let r of this.remote_props) {
              if(outer.app_name === r.app_name){
                this.$set(r,'_checked',true)
              }
            }
            for(let s of this.static_props){
              if(outer.app_name === s.app_name){
                this.$set(s,'_checked',true)
              }
            }
            for(let d of this.dist_props){
              if(outer.app_name === d.app_name){
                this.$set(d,'_checked',true)
              }
            }
            for(let d of this.app_props){
              if(outer.app_name === d.app_name){
                // this.$set(d,'_checked',true)
              }
            }
          }
        }
      })
    },

  }
}
</script>

<style scoped>

</style>
