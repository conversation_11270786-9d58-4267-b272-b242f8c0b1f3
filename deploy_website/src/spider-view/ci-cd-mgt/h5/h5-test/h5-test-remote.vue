<template>
  <Card>
    <Row>
      <Button
        ghost
        type="primary"
        style="text-align: left; display: inline-block; width:80px; margin:5px"
        @click="batch_compile"
      >批量编译</Button>
      <Button
        ghost
        type="primary"
        style="text-align: left; display: inline-block; width:80px; margin:5px"
        @click="batch_publish"
      >批量发布</Button>
      <Button
        ghost
        type="primary"
        style="text-align: left; display: inline-block; width:80px; margin:5px"
        ref="environment_banding"
        @click="environment_banding"
      >环境绑定</Button>
    </Row>

    <Row>
      <tables stripe v-model="remote_props" :columns="columns" @on-selection-change="selectChange" >

      </tables>
    </Row>
    <Modal
      v-model="environment_modal_val"
      title="批量环境绑定"
      @on-ok="environment_modal_ok"
      @on-cancel="environment_modal_cancel">
      <Card v-if="show_different_card === '1'">
        <Row v-for="(item,index) in table_selection" :key="item.app_name">
          <i-col style="margin: 10px" span="4">
            <span style="text-align: left; display: inline-block;">{{ item.app_name }}</span>
          </i-col>
          <Col span="12" style="margin: 5px">
            <Select v-if="item.app_name=='vendor'"  multiple v-model="vendor_list" disabled="">
              <Option
                v-for="(item,index) in vendor_list"
                :value="item"
                :key="item"
              >{{ item }}</Option>
            </Select>
            <Select v-if="item.app_name!='vendor'" multiple v-model="environment[index]" @on-change="change_for_vendor(index)">
              <Option
                v-for="item in environment_list[index]"
                :value="item"
                :key="item"
              >{{ item }}</Option>
            </Select>
          </Col>
        </Row>
      </Card>
      <Card v-if="show_different_card === '0'">至少勾选一个应用</Card>
    </Modal>
    <Modal
      v-model="difference_modal"
      title="差异"
      width="70%"
      :styles="{height: '60%'}"
      footer-hide>
      <Card>
        <Row>
          未查询到差异
        </Row>
      </Card>
    </Modal>
    <Modal v-model="compile_modal_val"
           title="发布与编译" >
      <Card>
        <Row>
          您可以选择直接发布或先编译后发布
        </Row>
      </Card>
      <div slot="footer">
        <Button size="large" @click="publish(publish_array)">直接发布</Button>
        <Button size="large" @click="publishAndCompile(publish_array)">先编译后发布</Button>
      </div>
    </Modal>
  </Card>
</template>

<script>
  import Tables from "@/components/tables";
  import store from "@/spider-store";
  import {
    h5CompileApi,
    h5TestPublishApi,
    compileCheckApi,
    testPublishCheckApi,
    externalServiceResult,
    userAction,
    h5CiPipelineApi,
    getInfoNoticeShowTime,
    getErrNoticeShowTime,
    h5PipelineStop, h5PipelineIsRunning
  } from "@/spider-api/h5";
  import {
    h5ElapseStatsApiGet
  } from "@/spider-api/h5-stats"
  import {
    formatDate,
  } from "@/spider-api/h5-common";
  import Cookies from "js-cookie";
  export default {
    name: "h5-test-remote",
    props:{
      //这里是table中的数据
      remote_props:{
        type:Array
      },
      initTable:{
        type: Function,
      },
    },
    components: {
      Tables,
    },
    computed: {
      //控制环境绑定的弹出框弹出哪一个
      show_different_card:function () {
        if(this.table_selection == undefined){
          return '0'
        }
        if(this.table_selection.length > 0 ){
          if(this.table_selection.length == 1 && this.table_selection[0].app_name == 'vendor'){
            return '0'
          }else {
            return '1'
          }
        }else {
          return '0'
        }
      }
    },
    data () {
      return {
        last_elapsed: "-",
        current_elapsed: "准备中",
        checkMsg:[],
        publish_data:[],
        table_selection:[],
        environment_modal_val: false,
        difference_modal:false,
        compile_modal_val: false,
        environment_list:[],
        environment: [],
        vendor_list:[],
        //用于复制一个vendor_list，防止修改了源数据
        vendor_list_temp:[],
        vendor_set:new Set(),
        compile_array:[],
        publish_array:[],
        columns:[
          {
            type: 'selection',
            width: 50,
          },
          {title: "应用", key: "app_name" },
          {title: "仓库", key: "git_path" },
          {
            title: "操作时间",
            key: "operate_time",
            //sortable: true,
            render: (h, params) => {
              let value = params.row.operate_time
              if(value == '' || value == null){

              }else {
                value = formatDate(new Date(params.row.operate_time),'yyyy-MM-dd hh:mm:ss')
              }
              return h('div',value)
            }
          },
          {title: "发布环境", key: "suite_name"},
          {
            title: "绑定环境",
            key: "suite_code",
            render: (h, params) => {
              let value = params.row.suite_code
              if(params.row.suite_code == null || params.row.suite_code == ''){
                return h('div','无')
              }else {
                let str = '';
                if(value instanceof Array){
                  console.log('i am Array')
                  console.log('=========== value =========')
                  console.log(value)
                  for(let i of value){
                    str += i + ','
                  }
                }
                if(typeof value === 'string'){
                  console.log('i am String')
                  str += value
                }
                //有多选却不输入值的
                let returnArray = []
                let returnStr = ''
                returnArray = str.split(',')
                for(let i of returnArray){
                  if(i != '' && i != null && i != undefined){
                    returnStr += i + ','
                  }
                }
                if(returnStr != '' && returnStr.charAt(returnStr.length -1) == ','){
                  returnStr = returnStr.substr(0,returnStr.length-1)
                }
                return h('div',returnStr)
              }
            }
          },
          /*{title: "申请状态", key: "email_status" },*/
          {
            title: "发布状态",
            key: "status",
            render: (h, params) => {
              let stat_dict = {
                'running': '执行中',
                'success': '执行成功',
                'failure': '执行失败',
                'compile_running': '编译中',
                'compile_success': '编译成功',
                'compile_failure': '编译失败',
                'publish_running': '发布中',
                'publish_success': '发布成功',
                'publish_failure': '发布失败',
                'aborted': '已终止',
              }
              let status = params.row.status
              if (stat_dict.hasOwnProperty(status)) {
                var status_display = stat_dict[status]
              } else {
                var status_display = status
              }
              let action_type = ""
              if (status == 'compile_running') {
                action_type = 'compile'
              } else if (status == 'publish_running') {
                action_type = 'remote'
              }
              if (action_type) {
                return h('div',
                  [h('a',
                    [h('Poptip',
                      {
                        props: {
                          transfer: true,
                          trigger: 'click',
                          title: '上次耗时: ' + this.last_elapsed,
                          content: '当前耗时：' + this.current_elapsed,
                          size: 'small'
                        },
                        on: {
                          'on-popper-show': () => {
                            h5ElapseStatsApiGet(params.row.app_name, action_type, store.state.h5_branch_version).then(res => {
                              if (res.data.status === "success") {
                                this.last_elapsed = res.data.data['last_elapsed']
                                this.current_elapsed = res.data.data['current_elapsed']
                              }
                            })
                          },
                          'on-popper-hide': () => {
                            this.last_elapsed = 0
                            this.current_elapsed = 0
                          }
                        }
                      },status_display)
                    ])
                  ])
              }else if(status == 'compile_success' || status == 'publish_success'){
                return h('p',
                  {style: {color: 'green'}},
                  status_display)
              } else if(status == 'compile_failure' || status == 'publish_failure'){
                return h('p',
                  {style: {color: 'red'}},
                  status_display)
              } else {
                return h('p',
                  {style: {color: '#515a6e'}},
                  status_display)
              }
            }
          },
          {title: "操作人", key: "username" },
          {
            title: '操作',
            key: 'action',
            width: 200,
            align: 'center',
            render: (h, params) => {
              //on事件中的this指向的并不是vue实例
              let vm = this;
              let is_display = false;
              if(params.row.app_name != 'vendor'){
                is_display = true;
              }
              return h('div', [
                // is_display?h('Button', {
                //   props: {
                //     type: 'default',
                //     size: 'small',
                //   },
                //   style: {
                //     marginRight: '5px',
                //   },
                //   on: {
                //     click: () => {
                //       if(params.row.status === 'running'
                //         || params.row.status === 'compile_running'
                //         || params.row.status === 'publish_running'){
                //         this.$Notice.info({
                //           desc: '请等待执行完成',
                //           duration: getInfoNoticeShowTime(),
                //         });
                //         return;
                //       }
                //       if(params.row.suite_code == '' || params.row.suite_code == null){
                //         this.$Notice.info({
                //           desc: '发布前需要先绑定环境',
                //           duration: getInfoNoticeShowTime(),
                //         });
                //         return;
                //       }else{
                //         /*this.$Notice.info({
                //           desc: '发布开始',
                //           duration: getInfoNoticeShowTime(),
                //         });*/
                //       }
                //       let publish_array = []
                //       publish_array.push({
                //         'suite_code': params.row.suite_code,
                //         'begin_ver': params.row.begin_ver,
                //         'end_ver': params.row.end_ver,
                //         'iteration_id': store.state.iterationID,
                //         'br_name': store.state.h5_branch_version,
                //         'app_name': params.row.app_name,
                //         'is_silent': '0',
                //       })
                //       //检查用的第一步
                //       //组装数据
                //       let app_name_list = []
                //       app_name_list.push(params.row.app_name)
                //       this.publish_array = publish_array
                //       let req = {'iteration_id':store.state.iterationID , 'app_name_list':app_name_list}
                //       if(params.row.status == 'compile_running'){
                //         console.log('running')
                //         this.$Notice.info({
                //           title: '已经开始编译，请等待编译结束',
                //           duration: getInfoNoticeShowTime(),
                //         });
                //       }else if(params.row.status == 'publish_running'){
                //         this.$Notice.info({
                //           title: '已经开始发布，请等待发布结束',
                //           duration: getInfoNoticeShowTime(),
                //         });
                //       }else if(params.row.status == 'compile_success' || params.row.status == 'publish_success'){
                //         //测试环境的check和发布/编译完全分开，写成两个异步接口
                //         // this.checkBeforePublish(req,publish_array)
                //         //如果是编译成功，只执行发布
                //         this.publish(publish_array)
                //       }else {
                //         //弹框 --> 是否编译
                //         this.compile_modal_val = true
                //       }/*else{
                //         console.log('=============== publish_array =============')
                //         console.log(publish_array)
                //         //既编译又发布，调用编译检查接口
                //         this.checkBeforeCompile(req,publish_array)
                //         this.publishAndCompile(publish_array)
                //       }*/
                //     }
                //   }
                // }, '发布'):'',
                h('Button', {
                  props: {
                    type: 'default',
                    size: 'small'
                  },
                  style: {
                    marginRight: '5px'
                  },
                  on: {
                    click: () => {
                      if (params.row.status == undefined) {
                        this.$Notice.info({
                          desc: '未执行',
                          duration: getInfoNoticeShowTime(),
                        });
                        return;
                      }
                      if (params.row.status.indexOf('running') < 0) {
                        this.$Notice.info({
                          desc: '不处于运行中',
                          duration: getInfoNoticeShowTime(),
                        });
                        return;
                      }

                      this.stopRun(params.row)
                    }
                  }
                }, '终止'),
                h('Button', {
                  props: {
                    type: 'default',
                    size: 'small'
                  },
                  style: {
                    marginRight: '5px'
                  },
                  on: {
                    click: () => {
                      //去往blueocean查看详情
                      let job_url = params.row.job_url
                      if(job_url != null){
                        window.open(params.row.job_url)
                      }else{
                        this.$Notice.info({
                          title: '暂无详情',
                          duration: getInfoNoticeShowTime(),
                        });
                      }
                    }
                  }
                }, '详情')
              ]);
            }
          },
        ],
      }
    },
    mounted() {
      this.init()
    },
    methods: {
      init(){
        console.log('remote init -------')
      },
      /**
       * iview 自带表格多选
       */
      selectChange(selection, row){
        console.log('======== selection =======')
        console.log(selection)
        this.table_selection = selection;
        //不光要改selection，remote_props作为父组件传入的remote全部数据，我们需要修改他的_checked属性，
        // 在点击【环境绑定】按钮会根据remote_props的_checked属性确定有多少个下拉框
        for(let i of this.remote_props){
          if(i.app_name != 'vendor'){
            i._checked = false
          }
        }
        for(let s of selection){
          for(let r of this.remote_props){
            if(s.app_name == r.app_name){
              r._checked = true
            }
          }
        }
        console.log('======== this.remote_props =======')
        console.log(this.remote_props)
        //如果只有vendor的情况下，不做显示
        if(selection.length == 1 && selection[0].app_name === 'vendor'){
          this.table_selection = [];
          for(let i of this.remote_props){
            if(i.app_name != 'vendor'){
              i._checked = false
            }
          }
        }
      },
      /**
       * 编译之前，先进行检测
       */
      checkBeforeCompile(req,incomeArray){
        this.compile_array = incomeArray
        //检测分为两部分，如果返回的全是success，才会执行  编译
        //第一部分
        compileCheckApi(req).then(res => {
          if (res.data.status === "success") {
            /*this.$Notice.success({
              desc: res.data.msg,
              duration: getInfoNoticeShowTime(),
            });*/
            console.log("============res =========")
            console.log(res.data.data)
            let sid = res.data.data.sid
            //检查用的第二步
            //这里是编译的验证 --> 编译的功能写在 第二步 校验的回调函数里了
            this.checkCompileSecondStep(sid)
          } else {
            this.$Notice.error({
              desc: res.data.msg,
              duration: getErrNoticeShowTime(),
            });
          }
        })
      },
      /**
       * 第二步检测(编译)
       */
      checkCompileSecondStep(sid){
        externalServiceResult(sid).then(res => {
          console.log(res)
          this.checkMsg.push(res)
          let status = res.data.data.status;
          let detail = res.data.data.detail;
          let count = store.state.check_count
          if (status == "success") {
            /*this.$Notice.success({
              desc:detail,
              duration: getInfoNoticeShowTime(),
            });*/
            store.commit("setCheckCount", 0)
            //测试环境特色  不论校验是否成功 都要编译
            // this.compile(this.compile_array)
          } else if (status == "failure") {
            this.$Notice.error({
              desc:detail,
              duration: getErrNoticeShowTime(),
            });
            store.commit("setCheckCount", 0)
            //测试环境特色  不论校验是否成功 都要编译
            // this.compile(this.compile_array)
          } else {
            console.log('======count========')
            console.log(count)
            if(count <= 60){
              let vm = this;
              setTimeout(function () {
                vm.checkCompileSecondStep(sid);
              }, 5000);
              count++
              store.commit("setCheckCount", count)
            }else {
              store.commit("setCheckCount", 0)
            }
          }
        })
      },
      /**
       * 发布之前，先进行检测
       */
      checkBeforePublish(req,incomeArray){
        this.publish_array = incomeArray
        //检测分为两部分，如果返回的全是success，才会执行  编译
        //第一部分
        testPublishCheckApi(req).then(res => {
          if (res.data.status === "success") {
            /*this.$Notice.success({
              desc: res.data.msg,
              duration: getInfoNoticeShowTime(),
            });*/
            console.log("============res =========")
            console.log(res.data.data)
            let sid = res.data.data.sid
            //检查用的第二步
            //这里是编译的验证 --> 编译的功能写在 第二步 校验的回调函数里了
            this.checkPublishSecondStep(sid,incomeArray)
          } else {
            this.$Notice.error({
              desc: res.data.msg,
              duration: getErrNoticeShowTime(),
            });
          }
        })
      },
      /**
       * 第二步检测(发布)
       */
      checkPublishSecondStep(sid,incomeArray){
        externalServiceResult(sid).then(res => {
          console.log("======== externalServiceResult ===========")
          console.log(res)
          this.checkMsg.push(res)
          let status = res.data.data.status;
          let detail = res.data.data.detail;
          let count = store.state.check_count
          if (status == "success") {
            console.log('========== 开始发布 ===========')
            console.log(incomeArray)
            /*this.$Notice.success({
              desc:detail,
              duration: getInfoNoticeShowTime(),
            });*/
            store.commit("setCheckCount", 0)
            //测试环境特色  不论校验是否成功，都要发布
            // this.publishAndCompile(incomeArray)
          } else if (status == "failure") {
            this.$Notice.error({
              desc:detail,
              duration: getErrNoticeShowTime(),
            });
            store.commit("setCheckCount", 0)
            //测试环境特色  不论校验是否成功，都要发布
            // this.publishAndCompile(incomeArray)
          } else {
            console.log('======count========')
            console.log(count)
            if(count <= 60){
              let vm = this;
              setTimeout(function () {
                vm.checkPublishSecondStep(sid,incomeArray);
              }, 5000);
              count++
              store.commit("setCheckCount", count)
            }else {
              store.commit("setCheckCount", 0)
            }
          }
        })
      },
      /**
       * 编译  先编译，再发布
       */
      compile(param){
        console.log('========= compile START ============')
        let vm = this
        console.log(param)
        h5CompileApi(param).then(res => {
          console.log(res)
          if(res.status == '200'){
            //成功
            /*vm.$Notice.success({
              desc: res.data.msg,
              duration: getInfoNoticeShowTime(),
            });*/
          }else{
            //失败
            vm.$Notice.error({
              desc: res.data.msg,
              duration: getErrNoticeShowTime(),
            });
          }
          this.initTable()
        })
      },
      /**
       * 批量编译
       */
      batch_compile(){
        console.log('============ this.remote_props =============')
        console.log(this.remote_props)
        //组装数据
        let app_name_list = []
        let iteration_id = store.state.h5_group + '_' + store.state.h5_branch_version
        //由于记录了历史记录，所以我们直接根据历史记录回填的页面，不会走多选框on-change方法 --> 批量发布那里也要加
        /*if(this.table_selection.length == 0){
          console.log('=======  INNNNNNNNNNNN =========')
          for(let i of this.remote_props){
            //如果是选中的话
            if(i._checked == true){
              console.log('iiiiiiiiiiiiii')
              console.log(i)
              this.table_selection.push(i)
            }
          }
        }*/
        this.table_selection = []
        for(let i of this.remote_props){
          //如果是选中的话
          if(i._checked == true){
            this.table_selection.push(i)
          }
        }
        for(let i of this.table_selection){
          app_name_list.push(i.app_name)
        }
        console.log('====== this.table_selection ========')
        console.log(this.table_selection)

        let req = {'iteration_id':iteration_id , 'app_name_list':app_name_list}
        let compileObj = {}
        let compileList = []
        // 用于记录所有正在编译的应用，告知用户这些应用正在进行编译，请等待编译结束后再次编译
        let compileRunningList = []
        for(let i of this.table_selection){
          if(i.status != 'compile_running'){
            compileObj = {
              'suite_code':i.suite_code,
              'begin_ver':'',
              'end_ver':'',
              'iteration_id':store.state.iterationID,
              'br_name': store.state.h5_branch_version,
              'app_name':i.app_name,
              'is_silent':'1',
              'action_item':store.state.iterationID + '_' + 'test_remote_compile',
              'status':i.status,
            }
            compileList.push(compileObj)
          } else {
            compileRunningList.push(i.app_name)
          }
        }
        if (compileRunningList.length == 0 && (compileList == null || compileList.length == 0)) {
          this.$Notice.error({
            desc: '至少选择一个应用',
            duration: getErrNoticeShowTime(),
          });
          return;
        }
        if(compileRunningList.length > 0){
          this.$Notice.info({
            desc: '以下应用' + compileRunningList + '正在进行编译，请等待编译结束后再次编译',
            duration: getErrNoticeShowTime(),
          });
        }
        console.log('============ compileList ============')
        console.log(compileList)
        if(compileList.length > 0 ){
          this.checkBeforeCompile(req,compileList)
          this.compile(compileList)
        }
      },
      /**
       * 先编译，再发布
       * @param param
       */
      publishAndCompile(param){
        console.log("========= publishAndCompile START ==========")
        console.log(param)
        let publish_list = []
        let publish_obj = {}
        for(let i=0; i< param.length; i++){
          publish_obj = {
            'suite_code':param[i].suite_code,
            'begin_ver':'',
            'end_ver':'',
            'iteration_id':store.state.iterationID,
            'br_name': store.state.h5_branch_version,
            'app_name':param[i].app_name,
            'is_silent':'1',
            'action_item':store.state.iterationID + '_' + 'test_remote_publish'}
          publish_list.push(publish_obj)
        }
        h5CiPipelineApi(publish_list).then(res => {
          console.log(res)
          if(res.data.status == 'success'){
            //成功
            /*this.$Notice.success({
              desc: res.data.msg,
              duration: getInfoNoticeShowTime(),
            });*/
          }else{
            //失败
            this.$Notice.error({
              desc: res.data.msg,
              duration: getErrNoticeShowTime(),
            });
          }
          this.initTable()
          this.compile_modal_val = false
        })
      },

      /**
       * 单纯的发布  --> 已编译  状态下才会调用
       */
      publish(param){
        console.log("========= publish START ==========")
        console.log(param)
        let publish_list = []
        let publish_obj = {}
        for(let i=0; i< param.length; i++){
          publish_obj = {
            'suite_code':param[i].suite_code,
            'begin_ver':'',
            'end_ver':'',
            'iteration_id':store.state.iterationID,
            'br_name': store.state.h5_branch_version,
            'app_name':param[i].app_name,
            'is_silent':'1',
            'action_item':store.state.iterationID + '_' + 'test_remote_publish'}
          publish_list.push(publish_obj)
        }
        h5TestPublishApi(publish_list).then(res => {
          console.log(res)
          if(res.data.status == 'success'){
            //成功
            /*this.$Notice.success({
              desc: res.data.msg,
              duration: getInfoNoticeShowTime(),
            });*/
          }else{
            //失败
            this.$Notice.error({
              desc: res.data.msg,
              duration: getErrNoticeShowTime(),
            });
          }
          this.initTable()
          this.compile_modal_val = false
        })
      },

      /**
       * 批量发布
       */
      batch_publish(){
        console.log('========== this.table_selection ========')
        //由于记录了历史记录，所以我们直接根据历史记录回填的页面，不会走多选框on-change方法 --> 批量发布那里也要加
        if(this.table_selection.length == 0){
          for(let i of this.remote_props){
            //如果是选中的话
            if(i._checked == true){
              this.table_selection.push(i)
            }
          }
        }
        console.log(this.table_selection)
        if (this.table_selection.length == 0) {
          this.$Notice.error({
            desc: '至少选择一个应用',
            duration: getErrNoticeShowTime(),
          });
          return;
        }
        //组装数据
        let app_name_list = []
        let iteration_id = store.state.h5_group + '_' + store.state.h5_branch_version
        let vendor_list = []
        let all_have_env = 0
        for(let i of this.table_selection){
          if(i.status == 'compile_running'){
            this.$Notice.info({
              desc: '选中的应用已经开始编译，请等待编译结束',
              duration: getErrNoticeShowTime(),
            });
            return;
          }
          if(i.status == 'publish_running'){
            this.$Notice.info({
              desc: '选中的应用已经开始发布，请等待发布结束',
              duration: getErrNoticeShowTime(),
            });
            return;
          }
          //如果存在vendor，我们存他的环境会变成一个数组，而我们需要存一个字符串 --> 现在全变成多选了，除了vendor，别人也要处理
          let vendor_suite_code = []
          console.log('============= i.suite_code ==========')
          // i.suite_code instanceof String === false --> string变量似乎和new String()类型不同
          if(typeof i.suite_code === 'string'){
            console.log('i am String')
            vendor_suite_code = i.suite_code.split(',')
          }
          if(i.suite_code instanceof Array){
            console.log('i am Array')
            vendor_suite_code = i.suite_code
          }
          if(vendor_suite_code.length == 0){
            all_have_env ++
          }
          let vendor_obj = {}
          for(let sc of vendor_suite_code){
            // 是否绑定了环境
            if(sc != null && sc != undefined && sc != ''){
              vendor_obj = {
                "suite_code": sc,
                "begin_ver": "",
                "end_ver": "",
                "iteration_id": i.iteration_id,
                "app_name": i.app_name,
                "is_silent": "1"
              }
              vendor_list.push(vendor_obj)
            }
          }
          app_name_list.push(i.app_name)
        }
        // 因为之前判空过，所以这个时候还没有加入到list中，一定是因为没有绑定环境
        if(all_have_env > 0){
          this.$Notice.error({
            desc: '存在未绑定环境的应用',
            duration: getErrNoticeShowTime(),
          });
          return;
        }
        let req = {'iteration_id':iteration_id , 'app_name_list':app_name_list}
        console.log('======== START BATCH PUBLISH ============')
        console.log(vendor_list)
        this.checkBeforePublish(req,vendor_list)
        this.publish(vendor_list)
      },
      /**
       * 点击环境绑定按钮
       */
      environment_banding(){
        this.environment_modal_val = true;
        this.environment_list = new Array()
        //由于记录了历史记录，所以我们直接根据历史记录回填的页面，不会走多选框on-change方法 --> 批量发布那里也要加
        if(this.table_selection.length == 0){
          for(let i of this.remote_props){
            //如果是选中的话
            if(i._checked == true){
              this.table_selection.push(i)
            }
          }
        }
        //下拉框的数据绑定 --> 单纯value的list我们无法控制数据顺序，所以设计一个list里面装对象
        let suite_code_list_temp = []
        for(let outer in store.state.suite_code_list){
          for(let inner of this.table_selection){
            if(outer === inner.app_name){
              let temp_obj = {'app_name':inner.app_name,suite_code:store.state.suite_code_list[outer]}
              suite_code_list_temp.push(temp_obj)
            }
          }
        }
        //重新按应用名绑定环境
        for(let i of suite_code_list_temp){
          for(let inner =0;inner< this.table_selection.length;inner++){
            if(i.app_name === this.table_selection[inner].app_name){
              this.environment_list[inner] = i.suite_code
            }
          }
        }

        //environment --> modal框中下拉框环境回填
        /*for(let i=0;i< this.table_selection.length;i++){
          this.environment[i] = this.table_selection[i].suite_code
          console.log('this.environment['+i+']')
          console.log(this.environment[i])
        }*/
        for(let i=0;i< this.table_selection.length;i++){
          this.environment[i] = ''//this.table_selection[i].suite_code
          // console.log('this.environment['+i+']')
          // console.log(this.environment[i])
        }
      },
      /**
       * 环境绑定 确定
       */
      environment_modal_ok () {
        //当前选择的所有行
        let tableSelection = this.table_selection
        //我们设置的中转 选中行 对象
        let myTableSelection = []
        //当前选定环境
        let environment = this.environment
        //回显用
        let remoteProps = this.remote_props;
        //单独用来处理vendor
        let vendorSet = new Set()
        for(let outer=0; outer< tableSelection.length; outer++){
          //所有非vendor的对勾全部去掉，之后再根据选择的绑定环境对应的应用来打勾
          if(this.table_selection[outer].app_name != 'vendor'){
            this.$set(this.table_selection[outer],'_checked',false)
          }
          //多选中向组件添加属性
          if(this.table_selection[outer].app_name != 'vendor'
            && environment[outer] != null && environment[outer] != undefined){
            this.$set(this.table_selection[outer],'suite_code',environment[outer])
            myTableSelection.push(this.table_selection[outer])
          }
          for(let inner of remoteProps){
            //找到我选中的那条记录在table中的位置，向组件添加属性
            if(inner.app_name == tableSelection[outer].app_name
              && environment[outer] != null && environment[outer] != undefined){
              this.$set(inner,'suite_code',environment[outer])
              //开始重新回填 对勾 --> 这么做的原因：我们在上边控制了suite_code为空时不记录用户操作，如果对方全选，却不绑定环境，就会出现
              //打着对勾，却没有环境的情况，防止用户发布的时候直接发布了一个未绑定环境的应用  另一个方法，做环境必填，不过客户可能不乐意
              this.$set(inner,'_checked',true)
            }
            if(inner.app_name == 'vendor'
              && environment[outer] != null && environment[outer] != undefined
              && (this.table_selection[outer].app_name === 'newwap'
                || this.table_selection[outer].app_name === 'smasset'
                || this.table_selection[outer].app_name === 'newpig')){
              for(let env of environment[outer]){
                vendorSet.add(env)
              }
            }
          }
          //单独添加vendor
          if(vendorSet.size > 0){
            this.$set(this.remote_props[this.remote_props.length-1],'suite_code',Array.from(vendorSet).toString())
          }
        }
        //把选中的数据换成suite_code不为空的
        for(let i of this.table_selection){
          //vendor默认选中的，iview似乎在回填的时候不认识他
          if(i.app_name == 'vendor'){
            myTableSelection.push(i)
          }
        }
        this.table_selection = myTableSelection
        //记录当前操作 --> 你绑定了环境
        let action_value_list = []
        let action_value_obj = {}
        for(let i of this.table_selection){
          if(i.suite_code != null && i.suite_code != undefined){
            action_value_obj = {"app_name" : i.app_name, "suite_code" : i.suite_code}
            action_value_list.push(action_value_obj)
          }
        }
        let param = {
          action_item: store.state.iterationID + '_' + 'test_remote_suite_code',
          action_value:action_value_list,
        }
        //用户行为保存
        userAction(param).then(res => {
          let status = res.data.status;
          if (status == "success") {
            console.log('------ 用户行为已保存 --------')
            console.log(res)
          } else {
            console.log('---------- 用户行为记录操作失败 ---------')
          }
        })

      },
      /**
       * 环境绑定 终止
       */
      environment_modal_cancel () {
        // this.$Message.info('Clicked cancel');
      },
      change_for_vendor(index){
        // console.log(this.environment[index])
        if(this.table_selection[index].app_name === 'newwap'
          || this.table_selection[index].app_name === 'smasset'
          || this.table_selection[index].app_name === 'newpig'){
          //每次修改一个下拉菜单的值，只会改变list中对应下角标的数据
          this.vendor_list_temp[index] = this.environment[index]
          //环境多选的话 this.environment[index] 是一个Array
          this.vendor_set = new Set()
          for(let temp of this.vendor_list_temp){
            // this.vendor_set.add(i)
            for (let env of temp){
              // console.log(env)
              this.vendor_set.add(env)
            }
          }
          // this.vendor_set[index] = this.environment[index];
          this.vendor_list = Array.from(this.vendor_set)
          this.$set(this.table_selection[this.table_selection.length-1],'suite_code',this.vendor_list)
        }
      },
      stopRun(params) {
        params['iteration_id'] = store.state.iterationID
        h5PipelineIsRunning(params).then(res => {
          console.log(res.data.data.running)

          if (res.data.data.running) {
            h5PipelineStop(params).then(res => {
              this.$Notice.info({
                desc: '正在终止！',
                duration: getInfoNoticeShowTime() + 10,
              });
            })
          } else {
            this.$Notice.error({
              desc: '未找到运行中的jenkins信息，可能还在准备中！',
              duration: getInfoNoticeShowTime(),
            });
          }
        })
      }
    },

  }
</script>

<style scoped>

</style>
