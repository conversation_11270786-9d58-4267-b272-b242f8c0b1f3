<template>
  <Card>
    <Row>
      <Button
        ghost
        type="primary"
        style="text-align: left; display: inline-block; width:80px; margin:5px"
        @click="dist_batch_compile"
      >批量编译
      </Button>
      <Button
        ghost
        type="primary"
        style="text-align: left; display: inline-block; width:80px; margin:5px"
        @click="show_dist_batch_publish"
      >批量发布
      </Button>
      <Button
        ghost
        type="primary"
        style="text-align: left; display: inline-block; width:120px; margin:5px"
        @click="show_nf_bind"
      >绑定 NF 应用分支
      </Button>
    </Row>
    <Row>
      <tables stripe v-model="dist_props" :columns="columns" @on-selection-change="selectChange">

      </tables>
    </Row>
    <Modal
      :styles="{width: '70%'}"
      v-model="batch_publish_modal"
      title="批量发布"
      @on-ok="batch_publish_modal_ok"
      @on-cancel="batch_publish_modal_cancel"
    >
      <Row style="margin-top: 10px">
        <Col v-if="fund_show" span="8">
          <Card>
            <Row>
              <i-col style="margin: 10px;text-align: right" span="8">
                <span style="text-align: right; display: inline-block;">fund起始版本:</span>
              </i-col>
              <Col span="14">
                <!--                <Input v-model="fund_begin_ver" placeholder="起始版本" style="margin: 10px" />-->
                <Select v-model="fund_begin_ver" @on-change="change_fund" span="10" style="margin:10px" clearable filterable>
                  <Option
                    v-for="item in fund_begin_ver_list"
                    :value="item.value"
                    :label="item.label"
                    :key="item.value"
                  >{{ item.label }}
                  </Option>
                </Select>
              </Col>
            </Row>
            <Row>
              <i-col style="margin: 10px;text-align: right" span="8">
                <span style="text-align: right; display: inline-block;">fund结束版本:</span>
              </i-col>
              <Col span="14">
                <Input v-model="fund_end_ver" placeholder="结束版本" style="margin: 10px"/>
              </Col>
            </Row>
            <Row>
              <i-col style="margin: 10px;text-align: right" span="8">
                <span style="text-align: right; display: inline-block;">nf-fund版本：</span>
              </i-col>
              <Col span="14">
                <!--<Select v-model="nf_fund_ver" span="10" style="margin:10px" clearable filterable>-->
                  <!--<Option-->
                    <!--v-for="(item,index) in nf_fund_ver_list"-->
                    <!--:value="item.value"-->
                    <!--:key="item.value"-->
                  <!--&gt;{{ item.label }}-->
                  <!--</Option>-->
                <!--</Select>-->
                <Input v-model="nf_fund_ver" placeholder="nf-fund版本" disabled readonly style="margin: 10px"/>
              </Col>
            </Row>
            <Row>
              <i-col style="margin: 10px;text-align: right" span="8">
                <span style="text-align: right; display: inline-block;">打包类型:</span>
              </i-col>
              <Col span="14">
                <Checkbox v-model="fund_is_silent" :true-value="1" :false-value="0" style="margin-top: 10px">静默
                </Checkbox>
              </Col>
            </Row>
          </Card>
        </Col>
        <!-- 好看的间隔 -->
        <i-col v-if="fund_show" style="margin: 10px;text-align: right" span="1">
          <span style="text-align: right; display: inline-block;"></span>
        </i-col>
        <Col v-if="piggy_show" span="8">
          <Card>
            <Row>
              <i-col style="margin: 10px;text-align: right" span="8">
                <span style="text-align: right; display: inline-block;">piggy起始版本:</span>
              </i-col>
              <Col span="14">
                <!--                <Input v-model="piggy_begin_ver" placeholder="起始版本" style="margin: 10px" />-->
                <Select v-model="piggy_begin_ver" @on-change="change_piggy" span="10" style="margin:10px" clearable filterable>
                  <Option
                    v-for="item in piggy_begin_ver_list"
                    :value="item.value"
                    :label="item.label"
                    :key="item.value"
                  >{{ item.label }}
                  </Option>
                </Select>
              </Col>
            </Row>
            <Row>
              <i-col style="margin: 10px;text-align: right" span="8">
                <span style="text-align: right; display: inline-block;">piggy结束版本:</span>
              </i-col>
              <Col span="14">
                <Input v-model="piggy_end_ver" placeholder="结束版本" style="margin: 10px"/>
              </Col>
            </Row>
            <Row>
              <i-col style="margin: 10px;text-align: right" span="8">
                <span style="text-align: right; display: inline-block;">nf-piggy版本:</span>
              </i-col>
              <Col span="14">
                <Input v-model="nf_piggy_ver" placeholder="piggy结束版本" disabled readonly style="margin: 10px"/>
                <!--<Select v-model="nf_piggy_ver" span="10" style="margin:10px" clearable filterable>-->
                  <!--<Option-->
                    <!--v-for="(item,index) in nf_piggy_ver_list"-->
                    <!--:value="item.value"-->
                    <!--:key="item.value"-->
                  <!--&gt;{{ item.label }}-->
                  <!--</Option>-->
                <!--</Select>-->
              </Col>

            </Row>
            <Row>
              <i-col style="margin: 10px;text-align: right" span="8">
                <span style="text-align: right; display: inline-block;">打包类型:</span>
              </i-col>
              <Col span="14">
                <Checkbox v-model="piggy_is_silent" :true-value="1" :false-value="0" style="margin-top: 10px">静默
                </Checkbox>
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>
    </Modal>
     <Modal
        title="绑定nf 应用"
        :styles="{width: '70%'}"
        v-model="bind_nf_modal"
        :mask-closable="true"
      >
       <Row style="margin-top: 10px">
        <Col v-if="fund_show" span="8">
          <Card>
            <Row>
              <i-col style="margin: 10px;text-align: right" span="8">
                <span style="text-align: right; display: inline-block;">nf-fund版本:</span>
              </i-col>
              <Col span="14">
                <Select v-model="nf_fund_ver" span="10" style="margin:10px" clearable filterable>
                  <Option
                    v-for="(item,index) in nf_fund_ver_list"
                    :value="item.value"
                    :label="item.label"
                    :key="item.value"
                  >{{ item.label }}
                  </Option>
                </Select>
              </Col>
            </Row>
          </Card>
        </Col>
        <Col v-if="piggy_show" span="8">
          <Card>
            <Row>
              <i-col style="margin: 10px;text-align: right" span="8">
                <span style="text-align: right; display: inline-block;">nf-piggy版本:</span>
              </i-col>
              <Col span="14">
                <Select v-model="nf_piggy_ver" span="10" style="margin:10px" clearable filterable>
                  <Option
                    v-for="(item,index) in nf_piggy_ver_list"
                    :value="item.value"
                    :label="item.label"
                    :key="item.value"
                  >{{ item.label }}
                  </Option>
                </Select>
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>

        <div slot="footer">
          <Button @click="createH5AppBindNfApp(fund_show, piggy_show)">保存</Button>
          <Button @click="closeBindModal">关闭</Button>
        </div>
    </Modal>
    <!--<Modal-->
      <!--v-model="fund"-->
      <!--title="发布fund"-->
      <!--@on-ok="fund_modal_ok"-->
      <!--@on-cancel="fund_modal_cancel"-->
    <!--&gt;-->
      <!--<Card>-->
        <!--<Row>-->
          <!--<i-col style="margin: 10px" span="6">-->
            <!--<span style="text-align: left; display: inline-block; width:80px;margin:10px">起始版本</span>-->
          <!--</i-col>-->
          <!--<Col span="12">-->
            <!--&lt;!&ndash;            <Input v-model="fund_begin_ver" placeholder="起始版本" style="margin: 10px" />&ndash;&gt;-->
            <!--<Select v-model="fund_begin_ver" @on-change="change_fund" span="10" style="margin:10px" clearable filterable>-->
              <!--<Option-->
                <!--v-for="item in fund_begin_ver_list"-->
                <!--:value="item.value"-->
                <!--:key="item.value"-->
              <!--&gt;{{ item.label }}-->
              <!--</Option>-->
            <!--</Select>-->
          <!--</Col>-->
        <!--</Row>-->
        <!--<Row>-->
          <!--<i-col style="margin: 10px" span="6">-->
            <!--<span style="text-align: left; display: inline-block; width:80px;margin:10px">结束版本</span>-->
          <!--</i-col>-->
          <!--<Col span="12">-->
            <!--<Input v-model="fund_end_ver" placeholder="结束版本" style="margin: 10px"/>-->
          <!--</Col>-->
        <!--</Row>-->
        <!--<Row>-->
          <!--<i-col style="margin: 10px;" span="6">-->
            <!--<span style="text-align: left; display: inline-block; width:80px;margin:10px">nf-fund版本</span>-->
          <!--</i-col>-->
          <!--<Col span="12">-->
            <!--<Select v-model="nf_fund_ver" span="10" style="margin:10px" clearable filterable>-->
              <!--<Option-->
                <!--v-for="(item,index) in nf_fund_ver_list"-->
                <!--:value="item.value"-->
                <!--:key="item.value"-->
              <!--&gt;{{ item.label }}-->
              <!--</Option>-->
            <!--</Select>-->
          <!--</Col>-->
        <!--</Row>-->
        <!--<Row>-->
          <!--<i-col style="margin: 10px" span="4">-->
            <!--<span style="text-align: left; display: inline-block; width:60px;margin:10px">打包类型</span>-->
          <!--</i-col>-->
          <!--<Col span="12">-->
            <!--<Checkbox v-model="fund_is_silent" :true-value="1" :false-value="0" style="margin: 20px">静默</Checkbox>-->
          <!--</Col>-->
        <!--</Row>-->
      <!--</Card>-->
    <!--</Modal>-->
    <!--<Modal-->
      <!--v-model="piggy"-->
      <!--title="发布piggy"-->
      <!--@on-ok="piggy_modal_ok"-->
      <!--@on-cancel="piggy_modal_cancel">-->
      <!--<Card>-->
        <!--<Row>-->
          <!--<i-col style="margin: 10px" span="6">-->
            <!--<span style="text-align: left; display: inline-block;width:80px;margin:10px">起始版本</span>-->
          <!--</i-col>-->
          <!--<Col span="12">-->
            <!--&lt;!&ndash;             <Input v-model="piggy_begin_ver" placeholder="起始版本" style="margin: 10px" />&ndash;&gt;-->
            <!--<Select v-model="piggy_begin_ver" @on-change="change_piggy" span="10" style="margin:10px" clearable filterable>-->
              <!--<Option-->
                <!--v-for="item in piggy_begin_ver_list"-->
                <!--:value="item.value"-->
                <!--:key="item.value"-->
              <!--&gt;{{ item.label }}-->
              <!--</Option>-->
            <!--</Select>-->
          <!--</Col>-->
        <!--</Row>-->
        <!--<Row>-->
          <!--<i-col style="margin: 10px" span="6">-->
            <!--<span style="text-align: left; display: inline-block; width:80px;margin:10px">结束版本</span>-->
          <!--</i-col>-->
          <!--<Col span="12">-->
            <!--<Input v-model="piggy_end_ver" placeholder="结束版本" style="margin: 10px"/>-->
          <!--</Col>-->
        <!--</Row>-->
        <!--<Row>-->
          <!--<i-col style="margin: 10px;" span="6">-->
            <!--<span style="text-align: left; display: inline-block;width:80px;margin:10px">nf-piggy版本</span>-->
          <!--</i-col>-->
          <!--<Col span="12">-->
            <!--<Select v-model="nf_piggy_ver" span="10" style="margin:10px" clearable filterable>-->
              <!--<Option-->
                <!--v-for="(item,index) in nf_piggy_ver_list"-->
                <!--:value="item.value"-->
                <!--:key="item.value"-->
              <!--&gt;{{ item.label }}-->
              <!--</Option>-->
            <!--</Select>-->
          <!--</Col>-->
        <!--</Row>-->
        <!--<Row>-->
          <!--<i-col style="margin: 10px" span="4">-->
            <!--<span style="text-align: left; display: inline-block; width:60px;margin:10px">打包类型</span>-->
          <!--</i-col>-->
          <!--<Col span="12">-->
            <!--<Checkbox v-model="piggy_is_silent" :true-value="1" :false-value="0" style="margin: 20px">静默</Checkbox>-->
          <!--</Col>-->
        <!--</Row>-->
      <!--</Card>-->
    <!--</Modal>-->
    <Modal
      :styles="{width: '60%'}"
      v-model="download_modal"
      title="下载（右键点击【下载】按钮选择【链接另存为】可直接保存下载）"
      footer-hide
    >
      <Card>
        <Tabs ref="download_tabs">
          <!--          <TabPane label="H5资源包" name="DownloadZip"><DownloadZip :download_zip_props=download_zip_props></DownloadZip></TabPane>-->
          <!--          <TabPane label="MD5文件" name="DownloadMd5"><DownloadMd5  ref="DownloadMd5"></DownloadMd5></TabPane>-->
          <TabPane label="H5资源包" name="DownloadZip">
            <DownloadZip :download_file_props=download_zip_props></DownloadZip>
          </TabPane>
        </Tabs>
      </Card>
    </Modal>
    <Modal v-model="compile_modal_val"
           title="发布与编译" >
      <Card>
        <Row>
          您可以选择直接发布或先编译后发布
        </Row>
      </Card>
      <div slot="footer">
        <Button size="large" @click="publish(publish_array)">直接发布</Button>
        <Button size="large" @click="publishAndCompile(publish_array)">先编译后发布</Button>
      </div>
    </Modal>
  </Card>
</template>

<script>
import Tables from "@/components/tables";
import store from "@/spider-store"
import DownloadZip from '@/spider-view/ci-cd-mgt/h5/h5-download/h5-download-zip'
import DownloadMd5 from '@/spider-view/ci-cd-mgt/h5/h5-download/h5-download-md5'
import {
  h5TestPublishApi,
  compileCheckApi,
  externalServiceResult,
  getResourceInfo,
  h5CompileApi,
  h5CiPipelineApi,
  getInfoNoticeShowTime,
  getErrNoticeShowTime,
  findDistVersion,
  h5PipelineStop, h5PipelineIsRunning
} from "@/spider-api/h5";
import {createH5AppBindNfAppApi, getH5AppBindNfAppApi} from "@/spider-api/h5-ci-cd/nf-app"
import {
  h5ElapseStatsApiGet
} from "@/spider-api/h5-stats"
import {
  test,
  formatDate,
} from "@/spider-api/h5-common";

export default {
  name: "h5-test-zip",
  components: {
    Tables,
    DownloadZip,
    DownloadMd5,
  },
  props: {
    //这里是table中的数据
    dist_props: {
      type: Array
    },
    initTable: {
      type: Function,
    },
  },
  data() {
    return {
      last_elapsed: "-",
      current_elapsed: "准备中",
      publish_data: [],
      checkMsg: [],
      table_selection: [],
      compile_array: [],
      publish_array: [],
      bind_nf_modal:false,
      batch_publish_modal: false,
      // fund: false,
      // piggy: false,
      download_modal: false,
      compile_modal_val: false,
      fund_end_ver: '',
      piggy_end_ver: '',
      fund_begin_ver: '',
      fund_begin_ver_list: [],
      piggy_begin_ver: '',
      piggy_begin_ver_list: [],
      nf_fund_ver: '',
      nf_fund_ver_list: [],
      nf_piggy_ver: '',
      nf_piggy_ver_list: [],
      fund_is_silent: '',
      piggy_is_silent: '',
      fund_param: {},
      piggy_param: {},
      add_app: [],
      download_zip_props: [],
      columns: [
        {
          type: 'selection',
          width: 50,
        },
        {title: "应用", key: "app_name"},
        {title: "仓库", key: "git_path"},
        {title: "起始版本", key: "begin_ver", /*sortable: true*/},
        {title: "结束版本", key: "end_ver", /*sortable: true */},
        {
          title: "操作时间",
          key: "operate_time",
          //sortable: true,
          render: (h, params) => {
            let value = params.row.operate_time
            if (value == '' || value == null) {

            } else {
              value = formatDate(new Date(params.row.operate_time), 'yyyy-MM-dd hh:mm:ss')
            }
            return h('div', value)
          }
        },
        /*{title: "申请状态", key: "email_status" },*/
        {
          title: "发布状态",
          key: "status",
          render: (h, params) => {
            let stat_dict = {
              'running': '执行中',
              'success': '执行成功',
              'failure': '执行失败',
              'compile_running': '编译中',
              'compile_success': '编译成功',
              'compile_failure': '编译失败',
              'publish_running': '发布中',
              'publish_success': '发布成功',
              'publish_failure': '发布失败',
              'aborted': '已终止',
            }
            let status = params.row.status
            if (stat_dict.hasOwnProperty(status)) {
              var status_display = stat_dict[status]
            } else {
              var status_display = status
            }
            let action_type = ""
            if (status == 'compile_running') {
              action_type = 'compile'
            } else if (status == 'publish_running') {
              action_type = 'dist'
            }
            if (action_type) {
              return h('div',
                [h('a',
                  [h('Poptip',
                    {
                      props: {
                        transfer: true,
                        trigger: 'click',
                        title: '上次耗时: ' + this.last_elapsed,
                        content: '当前耗时：' + this.current_elapsed,
                        size: 'small'
                      },
                      on: {
                        'on-popper-show': () => {
                          h5ElapseStatsApiGet(params.row.app_name, action_type, store.state.h5_branch_version).then(res => {
                            if (res.data.status === "success") {
                              this.last_elapsed = res.data.data['last_elapsed']
                              this.current_elapsed = res.data.data['current_elapsed']
                            }
                          })
                        },
                        'on-popper-hide': () => {
                          this.last_elapsed = 0
                          this.current_elapsed = 0
                        }
                      }
                    }, status_display)
                  ])
                ])
            } else if(status == 'compile_success' || status == 'publish_success'){
              return h('p',
                {style: {color: 'green'}},
                status_display)
            } else if(status == 'compile_failure' || status == 'publish_failure'){
              return h('p',
                {style: {color: 'red'}},
                status_display)
            } else {
              return h('p',
                {style: {color: '#515a6e'}},
                status_display)
            }
          }
        },
        {title: "操作人", key: "username"},
        {
          title: 'NF迭代页面',
          key: 'action',
          width: 100,
          align: 'center',
          render: (h, params) => {
            return h('div', [
              h('a', {
                props: {
                  type: 'default',
                  size: 'small',
                  value:this.nf_piggy_ver_list
                },
                style: {
                  marginRight: '5px',
                  display: params.row.app_name == 'nf-fund' || params.row.app_name == 'nf-piggy' ? 'none' : 'inline-block'
                },
                on: {
                  click: () => {
                     if (params.row.app_name === 'fund') {
                       let routeDate= this.$router.resolve({name:"h5_pipeline",
                         query: {"iteration_id":"h5_" +this.nf_fund_ver, "branch_version":this.nf_fund_ver}})
                       window.open(routeDate.href, "_blank")
                     }
                      if (params.row.app_name === 'piggy') {
                       let routeDate= this.$router.resolve({name:"h5_pipeline",
                         query: {"iteration_id":"h5_" +this.nf_piggy_ver, "branch_version": this.nf_piggy_ver}})
                       window.open(routeDate.href, "_blank")
                     }

                  }
                }
              }, '跳转到...'),
            ]);
          }
        },
        {
          title: '操作',
          key: 'action',
          width: 250,
          align: 'center',
          render: (h, params) => {
            return h('div', [
              // h('Button', {
              //   props: {
              //     type: 'default',
              //     size: 'small'
              //   },
              //   //临时解决方案 --> app_name = nf-fund || app_name = nf-piggy ，隐藏或禁用
              //   /*attrs: {
              //     //按钮禁用
              //     disabled: params.row.need_online == 1 ? false : true
              //   },*/
              //   style: {
              //     marginRight: '5px',
              //     display: params.row.app_name == 'nf-fund' || params.row.app_name == 'nf-piggy' ? 'none' : 'inline-block'
              //   },
              //   on: {
              //     click: () => {
              //       console.log(params.row)
              //       if (params.row.status == 'compile_running') {
              //         this.$Notice.info({
              //           desc: '选中的应用已经开始编译，请等待编译结束',
              //           duration: getErrNoticeShowTime(),
              //         });
              //         return;
              //       }
              //       if (params.row.status == 'publish_running') {
              //         this.$Notice.info({
              //           desc: '选中的应用已经开始发布，请等待发布结束',
              //           duration: getErrNoticeShowTime(),
              //         });
              //         return;
              //       }
              //       if (params.row.status == 'compile_failure') {
              //         this.$Notice.info({
              //           desc: '编译失败，请重新编译',
              //           duration: getErrNoticeShowTime(),
              //         });
              //         return;
              //       }
              //       if (params.row.app_name === 'fund') {
              //         this.fund_param = params.row
              //         if (params.row.begin_ver == ''
              //           || params.row.begin_ver == null
              //           || params.row.begin_ver == undefined) {
              //           this.fund_begin_ver = store.state.h5_branch_version
              //           this.fund_end_ver = store.state.h5_branch_version
              //         }
              //       } else if (params.row.app_name === 'piggy') {
              //         this.piggy_param = params.row
              //         if (params.row.begin_ver == ''
              //           || params.row.begin_ver == null
              //           || params.row.begin_ver == undefined) {
              //           this.piggy_begin_ver = store.state.h5_branch_version
              //           this.piggy_end_ver = store.state.h5_branch_version
              //         }
              //       }
              //       console.log(params.row)
              //       this.show_package(params)
              //     }
              //   }
              // }, '发布'),
              h('Button', {
                props: {
                  type: 'default',
                  size: 'small'
                },
                style: {
                  marginRight: '5px'
                },
                on: {
                  click: () => {
                    if (params.row.status == undefined) {
                      this.$Notice.info({
                        desc: '未执行',
                        duration: getInfoNoticeShowTime(),
                      });
                      return;
                    }
                    if (params.row.status.indexOf('running') < 0) {
                      this.$Notice.info({
                        desc: '不处于运行中',
                        duration: getInfoNoticeShowTime(),
                      });
                      return;
                    }

                    this.stopRun(params.row)
                  }
                }
              }, '终止'),
              h('Button', {
                props: {
                  type: 'default',
                  size: 'small'
                },
                /*attrs: {
                  //按钮禁用
                  disabled: params.row.need_online == 1 ? false : true
                },*/
                style: {
                  marginRight: '5px',
                  display: params.row.app_name == 'nf-fund' || params.row.app_name == 'nf-piggy' ? 'none' : 'inline-block'
                },
                on: {
                  click: () => {
                    this.showDownload(params.row.app_name)
                    /*// console.log(this)
                    this.download_modal = true
                    //为子页面传参
                    let param = {'app_name':params.row.app_name,//'fund',
                      'iteration_id':store.state.iterationID,//'h5_h5_pkg_fund-f201201-huangmin_compile',
                      'suite_code':'test'};//'prod'
                    getResourceInfo(param).then(res => {
                      console.log('========= getResourceInfo ==========')
                      console.log(res)
                      this.download_zip_props = res.data.data
                    })*/
                  }
                }
              }, '下载'),
              h('Button', {
                props: {
                  type: 'default',
                  size: 'small'
                },
                style: {
                  marginRight: '5px'
                },
                on: {
                  click: () => {
                    let job_url = params.row.job_url
                    if (job_url != null) {
                      window.open(params.row.job_url)
                    } else {
                      this.$Notice.info({
                        title: '暂无详情',
                        duration: getInfoNoticeShowTime(),
                      });
                    }
                  }
                }
              }, '详情')
            ]);
          }
        },
      ],
    }
  },
  mounted() {
   // alert("实例化了"+store.state.h5_branch_version)
    this.init()
  },
  computed: {
    fund_show() {
      for (let i of this.table_selection) {
        if (i.app_name == 'fund') {
          return true
        }
      }
    },
    piggy_show() {
      for (let i of this.table_selection) {
        if (i.app_name == 'piggy') {
          return true
        }
      }
    },
  },
  methods: {
    init() {
      console.log('============ dist init ============')
      console.log(this.dist_props)
      //不这样设定的话，Modal中的Tabs不会默认是第一个Tab页
      this.$refs.download_tabs.activeKey = 'DownloadZip'
      //fund & piggy 起始版本回填
      let version_param = {
        'br_name': store.state.h5_branch_version,
        'suite_code': 'test',
      }
      findDistVersion(version_param).then(res => {
        this.fund_begin_ver_list = res.data.data.fund_begin_ver_list
        this.piggy_begin_ver_list = res.data.data.piggy_begin_ver_list
        this.nf_fund_ver_list = res.data.data.nf_fund_ver_list
        this.nf_piggy_ver_list = res.data.data.nf_piggy_ver_list
      }).catch(err => {
        console.log('============= err =========')
        console.log(err)
        this.$Message.error(err)
      })
      // 获取绑定fund 的nf-fund 的分支号
      getH5AppBindNfAppApi({"iteration_id":store.state.iterationID,"app_name":"fund",'suite_code': 'test'}).then(res => {

        this.nf_fund_ver= res.data.data.nf_br_name
      })
      // 获取绑定piggy的 nf-piggy的分支号
       getH5AppBindNfAppApi({"iteration_id":store.state.iterationID,"app_name":"piggy",'suite_code': 'test'}).then(res => {
        this.nf_piggy_ver= res.data.data.nf_br_name
      })
    },
    change_fund() {
      this.fund_end_ver = this.fund_begin_ver
    },
    change_piggy() {
      this.piggy_end_ver = this.piggy_begin_ver
    },
    // 创建 h5 app 与nf应用的绑定关系
    createH5AppBindNfApp(fund_show, piggy_show){
      let params_list = []
      if (fund_show){
        params_list.push({
      iteration_id: store.state.iterationID,
      app_name: "fund",
      nf_br_name: this.nf_fund_ver,
      nf_app_name: "nf-fund"
      })
      }
      if (piggy_show){
        params_list.push({
      iteration_id: store.state.iterationID,
      app_name: "piggy",
      nf_br_name: this.nf_piggy_ver,
      nf_app_name: "nf-piggy"
      })
      }
      for (let row of params_list) {
        createH5AppBindNfAppApi(row).then(res => {
          this.$Message.success(res.data.msg)
        })
          .catch(err => {
            console.log('============= err =========')
            console.log(err)
            this.$Message.error(err)
          })
      }
      this.bind_nf_modal= false

    },
    selectChange(selection, row) {
      this.table_selection = selection;
      for(let s of selection){
        for(let r of this.dist_props){
          if(s.app_name == r.app_name){
            r._checked = true
          }
        }
      }
    },
    get_table_select(){
      if (this.table_selection.length == 0) {
        for (let i of this.dist_props) {
          //如果是选中的话
          if (i._checked == true) {
            this.table_selection.push(i)
          }
        }
      }
      if (this.table_selection.length == 0) {
        this.$Notice.error({
          desc: '至少选择一个应用',
          duration: getErrNoticeShowTime(),
        });
        return;
      }
    },
    show_dist_batch_publish() {
      this.get_table_select()
      this.batch_publish_modal = true
    },
    show_nf_bind() {
      this.get_table_select()
      this.bind_nf_modal = true
    },
    show_package(params) {
      if (params.row.app_name == 'fund') {
        //fund
        this.fund = true;
      } else if (params.row.app_name == 'piggy') {
        //piggy
        this.piggy = true;
      } else {
        console.log("暂时还没有这总项目类型")
      }
    },
    /**
     * 批量发布录入 起始版本，结束版本，是否静默
     */
    batch_publish_modal_ok() {
      let publish_array = []
      if (this.fund_is_silent == '') {
        this.fund_is_silent = '0'
      }
      if (this.piggy_is_silent == '') {
        this.piggy_is_silent = '0'
      }
      for (let i of this.table_selection) {

        if (i.status == 'compile_running') {
          this.$Notice.info({
            desc: '选中的应用已经开始编译，请等待编译结束',
            duration: getErrNoticeShowTime(),
          });
          return;
        }
        if (i.status == 'publish_running') {
          this.$Notice.info({
            desc: '选中的应用已经开始发布，请等待发布结束',
            duration: getErrNoticeShowTime(),
          });
          return;
        }
        //nf-fund nf-piggy 如果没选择版本号，默认选择最近的已归档版本
        if(this.nf_fund_ver == null || this.nf_fund_ver == '' || this.nf_fund_ver == undefined){
          for(let i of this.nf_fund_ver_list){
            if(i.label.indexOf('(已归档') != -1){
              this.nf_fund_ver = i.value
              break;
            }
          }
        }
        if(this.nf_piggy_ver == null || this.nf_piggy_ver == '' || this.nf_piggy_ver == undefined){
          for(let i of this.nf_piggy_ver_list){
            if(i.label.indexOf('(已归档') != -1){
              this.nf_piggy_ver = i.value
              break;
            }
          }
        }
        if (i.app_name === 'fund') {
          publish_array.push({
            'suite_code': 'test',
            'begin_ver': this.fund_begin_ver,
            'end_ver': this.fund_end_ver,
            'iteration_id': store.state.iterationID,
            'br_name': store.state.h5_branch_version,
            'app_name': i.app_name,
            'is_silent': this.fund_is_silent,
            'action_item': store.state.iterationID + '_' + 'test_dist_publish',
            'nf_app_name': 'nf-fund',
            'nf_br_name': this.nf_fund_ver,
          })
        } else if (i.app_name === 'piggy') {
          publish_array.push({
            'suite_code': 'test',
            'begin_ver': this.piggy_begin_ver,
            'end_ver': this.piggy_end_ver,
            'iteration_id': store.state.iterationID,
            'br_name': store.state.h5_branch_version,
            'app_name': i.app_name,
            'is_silent': this.piggy_is_silent,
            'action_item': store.state.iterationID + '_' + 'test_dist_publish',
            'nf_app_name': 'nf-piggy',
            'nf_br_name': this.nf_piggy_ver,
          })
        } else {
          console.log('没有这种。。。')
        }
      }
      this.publish_array = publish_array
      console.log('========== this.publish_array =========')
      console.log(this.publish_array)
      this.batch_publish()
    },
    batch_publish_modal_cancel() {

    },
    closeBindModal(){
      this.bind_nf_modal= false
    },
    batch_publish() {
      //组装数据
      let app_name_list = []
      let iteration_id = store.state.h5_group + '_' + store.state.h5_branch_version
      for (let i of this.table_selection) {
        app_name_list.push(i.app_name)
      }
      let req = {'iteration_id': iteration_id, 'app_name_list': app_name_list}
      this.checkBeforePublish(req)
      this.publish(this.publish_array)
    },
    //fund 确定
    fund_modal_ok() {
      if (this.fund_begin_ver != null && this.fund_end_ver != null) {
        /*this.$Notice.info({
          desc: '发布开始',
          duration: getInfoNoticeShowTime(),
        });*/
      } else {
        this.$Notice.error({
          desc: '版本不能为空',
          duration: getErrNoticeShowTime(),
        });
        return;
      }
      //nf-fund 如果没选择版本号，默认选择最近的已归档版本
      if(!this.nf_fund_ver){
        for(let i of this.nf_fund_ver_list){
          if(i.label.indexOf('已归档') != -1){
            this.nf_fund_ver = i.value
            break;
          }
        }
      }
      let publish_array = []
      publish_array.push({
        'suite_code': 'test',
        'begin_ver': this.fund_begin_ver,
        'end_ver': this.fund_end_ver,
        'iteration_id': store.state.iterationID,
        'br_name': store.state.h5_branch_version,
        'app_name': this.fund_param.app_name,
        'is_silent': this.fund_is_silent,
        'action_item': store.state.iterationID + '_' + 'test_dist_publish',
        'nf_app_name': 'nf-fund',
        'nf_br_name': this.nf_fund_ver,
      })
      //检查用的第一步
      //组装数据
      let app_name_list = []
      app_name_list.push(this.fund_param.app_name)
      let req = {'iteration_id': store.state.iterationID, 'app_name_list': app_name_list}
      this.publish_array = publish_array
      if (this.fund_param.status == 'compile_running') {
        this.$Notice.info({
          title: '已经开始编译，请等待编译结束',
          duration: getInfoNoticeShowTime(),
        });
      } else if (this.fund_param.status == 'publish_running') {
        this.$Notice.info({
          title: '已经开始发布，请等待发布结束',
          duration: getInfoNoticeShowTime(),
        });
      } else if (this.fund_param.status == 'compile_success' || this.fund_param.status == 'publish_success') {
        //如果是编译成功，只执行发布
        this.publish(publish_array)
      } else {
        this.compile_modal_val = true
      }
    },
    //fund 取消
    fund_modal_cancel() {
      // this.$Message.info('Clicked cancel');
    },
    //piggy 确定
    piggy_modal_ok() {
      if (this.piggy_begin_ver != null && this.piggy_end_ver != null) {
        /*this.$Notice.info({
          desc: '发布开始',
          duration: getInfoNoticeShowTime(),
        });*/
      } else {
        this.$Notice.error({
          desc: '版本不能为空',
          duration: getErrNoticeShowTime(),
        });
        return;
      }
      //nf-piggy 如果没选择版本号，默认选择最近的已归档版本
      if(this.nf_piggy_ver == null || this.nf_piggy_ver == '' || this.nf_piggy_ver == undefined){
        for(let i of this.nf_piggy_ver_list){
          if(i.label.indexOf('(已归档') != -1){
            this.nf_piggy_ver = i.value
            break;
          }
        }
      }
      let publish_array = []
      publish_array.push({
        'suite_code': 'test',
        'begin_ver': this.piggy_begin_ver,
        'end_ver': this.piggy_end_ver,
        'iteration_id': store.state.iterationID,
        'br_name': store.state.h5_branch_version,
        'app_name': this.piggy_param.app_name,
        'is_silent': this.piggy_is_silent,
        'action_item': store.state.iterationID + '_' + 'test_dist_publish',
        'nf_app_name': 'nf-piggy',
        'nf_br_name': this.nf_piggy_ver,
      })
      //检查用的第一步
      //组装数据
      let app_name_list = []
      let req = {'iteration_id': store.state.iterationID, 'app_name_list': app_name_list}
      app_name_list.push(this.piggy_param.app_name)
      this.publish_array = publish_array
      if (this.piggy_param.status == 'compile_running') {
        this.$Notice.info({
          title: '已经开始编译，请等待发编译结束',
          duration: getInfoNoticeShowTime(),
        });
      } else if (this.piggy_param.status == 'publish_running') {
        this.$Notice.info({
          title: '已经开始发布，请等待发布结束',
          duration: getInfoNoticeShowTime(),
        });
      } else if (this.piggy_param.status == 'compile_success' || this.piggy_param.status == 'publish_success') {
        //如果是编译成功，只执行发布
        this.publish(publish_array)
      } else {
        this.compile_modal_val = true
      }
    },
    //fund 取消
    piggy_modal_cancel() {
      // this.$Message.info('Clicked cancel');
    },
    return_test(h, params) {
      return h('Button', {
        props: {type: 'error', size: 'small'},
      }, [
        h('Poptip', {
          props: {
            confirm: true,
            transfer: true,
            title: '确定要删除吗！',
            type: 'error',
            size: 'small',
          },
          on: {
            'on-ok': () => {
              this.remove(params.index)
            },
            'on-cancel': function () {

            }
          }
        }, '删除')
      ])
    },
    /**
     * 编译之前，先进行检测
     */
    checkBeforeCompile(req, incomeArray) {
      this.compile_array = incomeArray
      console.log('========= res out ===========')
      console.log(this.compile_array)
      //检测分为两部分，如果返回的全是success，才会执行  编译
      //第一部分
      compileCheckApi(req).then(res => {
        if (res.data.status === "success") {
          /*this.$Notice.success({
            desc: res.data.msg,
            duration: getInfoNoticeShowTime(),
          });*/
          console.log("============res =========")
          console.log(res.data.data)
          let sid = res.data.data.sid
          //检查用的第二步
          //这里是编译的验证 --> 编译的功能写在 第二步 校验的回调函数里了
          console.log('======== 进入第二步之前的 incomeArray ========')
          console.log(this.compile_array)
          this.checkCompileSecondStep(sid, incomeArray)
        } else {
          this.$Notice.error({
            desc: res.data.msg,
            duration: getErrNoticeShowTime(),
          });
        }
      })
    },
    /**
     * 第二步检测(编译)
     */
    checkCompileSecondStep(sid, incomeArray) {
      externalServiceResult(sid).then(res => {
        console.log("======== externalServiceResult ===========")
        console.log(res)
        this.checkMsg.push(res)
        console.log('===== incomeArray ' + store.state.count + ' =========')
        console.log(this.compile_array)
        let status = res.data.data.status;
        let detail = res.data.data.detail;
        let count = store.state.check_count
        if (status == "success") {
          /*this.$Notice.success({
            desc:detail,
            duration: getInfoNoticeShowTime(),
          });*/
          store.commit("setCheckCount", 0)
          //开始编译
          // this.compile(this.compile_array)
        } else if (status == "failure") {
          this.$Notice.error({
            desc: detail,
            duration: getErrNoticeShowTime(),
          });
          store.commit("setCheckCount", 0)
        } else {
          console.log('======count========')
          console.log(count)
          if (count <= 60) {
            let vm = this;
            setTimeout(function () {
              vm.checkCompileSecondStep(sid);
            }, 5000);
            count++
            store.commit("setCheckCount", count)
          } else {
            store.commit("setCheckCount", 0)
          }
        }
      })
    },
    /**
     * 编译
     */
    compile(param) {
      console.log('========= compile START ============')
      this.compile_modal_val = false
      let vm = this
      console.log(param)
      h5CompileApi(param).then(res => {
        console.log(res)
        if (res.status == '200') {
          //成功
          /*vm.$Notice.success({
            desc: res.data.msg,
            duration: getInfoNoticeShowTime(),
          });*/
        } else {
          //失败
          vm.$Notice.error({
            desc: res.data.msg,
            duration: getErrNoticeShowTime(),
          });
        }
        this.initTable()
      })
    },
    /**
     * 发布之前，先进行检测
     */
    checkBeforePublish(req) {
      /*console.log('=========== this.publish_array ============')
      console.log(this.publish_array)*/
      //检测分为两部分，如果返回的全是success，才会执行  编译
      //第一部分
      compileCheckApi(req).then(res => {
        if (res.data.status === "success") {
          /*this.$Notice.success({
            desc: res.data.msg,
            duration: getInfoNoticeShowTime(),
          });*/
          /*console.log("============res =========")
          console.log(res.data.data)*/
          let sid = res.data.data.sid
          //检查用的第二步
          //这里是编译的验证 --> 编译的功能写在 第二步 校验的回调函数里了
          /*console.log('======== 进入第二步之前的 incomeArray ========')
          console.log(this.publish_array)*/
          this.checkPublishSecondStep(sid)
        } else {
          this.$Notice.error({
            desc: res.data.msg,
            duration: getErrNoticeShowTime(),
          });
        }
      })
    },
    /**
     * 第二步检测(发布)
     */
    checkPublishSecondStep(sid) {
      /*console.log('========== incomeArray =============')
      console.log(this.publish_array)*/
      externalServiceResult(sid).then(res => {
        /*console.log("======== externalServiceResult ===========")
        console.log(res)*/
        this.checkMsg.push(res)
        let status = res.data.data.status;
        let detail = res.data.data.detail;
        let count = store.state.check_count
        if (status == "success") {
          /*this.$Notice.success({
            desc:detail,
            duration: getInfoNoticeShowTime(),
          });*/
          store.commit("setCheckCount", 0)
          // this.publish(this.publish_array)
        } else if (status == "failure") {
          this.$Notice.error({
            desc: detail,
            duration: getErrNoticeShowTime(),
          });
          store.commit("setCheckCount", 0)
          // this.publish(this.publish_array)
        } else {
          console.log('======count========')
          console.log(count)
          if (count <= 60) {
            let vm = this;
            setTimeout(function () {
              vm.checkPublishSecondStep(sid);
            }, 5000);
            count++
            store.commit("setCheckCount", count)
          } else {
            store.commit("setCheckCount", 0)
          }
        }
      })
    },
    /**
     * 先编译，再发布
     * @param param
     */
    publishAndCompile(param) {
      h5CiPipelineApi(param).then(res => {
        console.log('========== publishAndCompile =========')
        console.log(res)
        if (res.status == '200') {
          //成功
          /*this.$Notice.success({
            desc:res.data.msg,
            duration: getInfoNoticeShowTime(),
          });*/
        } else {
          //失败
          this.$Notice.error({
            desc: res.data.msg,
            duration: getErrNoticeShowTime(),
          });
        }
      })
      this.initTable()
      this.compile_modal_val = false
    },
    /**
     * 只发布
     */
    publish(param) {
      this.compile_modal_val = false
      h5TestPublishApi(param).then(res => {
        console.log('============ h5TestPublishApi ===========')
        console.log(res)
        if (res.status == '200') {
          //成功
          /*this.$Notice.success({
            desc:res.data.msg,
            duration: getInfoNoticeShowTime(),
          });*/
        } else {
          //失败
          this.$Notice.error({
            desc: res.data.msg,
            duration: getErrNoticeShowTime(),
          });
        }
      })
      this.initTable()
    },
    /**
     * 批量编译
     */
    dist_batch_compile() {
      /*if (this.table_selection.length == 0) {
        for (let i of this.dist_props) {
          //如果是选中的话
          if (i._checked == true) {
            this.table_selection.push(i)
          }
        }
      }*/
      this.table_selection = []
      for (let i of this.dist_props) {
        //如果是选中的话
        if (i._checked == true) {
          this.table_selection.push(i)
        }
      }
      console.log('======= this.table_selection ======')
      console.log(this.table_selection)
      console.log('======== this.dist_props ======')
      console.log(this.dist_props)
      //组装数据，开始编译fund和piggy
      let app_name_list = []
      let iteration_id = store.state.h5_group + '_' + store.state.h5_branch_version
      for (let i of this.table_selection) {
        app_name_list.push(i.app_name)
      }
      let req = {'iteration_id': iteration_id, 'app_name_list': app_name_list}
      let compileObj = {}
      let compileList = []
      // 用于记录所有正在编译的应用，告知用户这些应用正在进行编译，请等待编译结束后再次编译
      let compileRunningList = []
      for (let i of this.table_selection) {
        console.log('======= status ======')
        console.log(i.status)
        if(i.status != 'compile_running'){
          compileObj = {
            'suite_code': i.suite_code,
            'begin_ver': '',
            'end_ver': '',
            'iteration_id': store.state.iterationID,
            'br_name': store.state.h5_branch_version,
            'app_name': i.app_name,
            'is_silent': '1',
            'action_item': store.state.iterationID + '_' + 'test_dist_compile',
            'status':i.status,
          }
          compileList.push(compileObj)
        } else {
          compileRunningList.push(i.app_name)
        }
      }
      if (compileRunningList.length == 0 && (compileList == null || compileList.length == 0)) {
        this.$Notice.error({
          desc: '至少选择一个应用',
          duration: getErrNoticeShowTime(),
        });
        return;
      }
      console.log('======== compileRunningList =========')
      console.log(compileRunningList)
      console.log('======== compileList ======')
      console.log(compileList)
      if(compileRunningList.length > 0){
        this.$Notice.info({
          desc: '以下应用' + compileRunningList + '正在进行编译，请等待编译结束后再次编译',
          duration: getErrNoticeShowTime(),
        });
      }
      if(compileList.length > 0 ){
        this.checkBeforeCompile(req, compileList)
        this.compile(compileList)
      }
    },
    /**
     * 批量发布
     */
    dist_batch_publish() {
      console.log(this.table_selection)
      if (this.table_selection.length == 0) {
        for (let i of this.dist_props) {
          //如果是选中的话
          if (i._checked == true) {
            this.table_selection.push(i)
          }
        }
      }
      //组装数据，将编译好的产物打包发布到ng服务器
      let app_name_list = []
      let iteration_id = store.state.h5_group + '_' + store.state.h5_branch_version
      for (let i of this.table_selection) {
        app_name_list.push(i.app_name)
      }
      let req = {'iteration_id': iteration_id, 'app_name_list': app_name_list}
      this.checkBeforePublish(req, this.table_selection)
      this.publish(this.publish_array)
    },
    showDownload(app_name) {
      let param = {
        'app_name': app_name,//'fund',
        'iteration_id': store.state.iterationID,
        'suite_code': 'test'
      };//
      //下载信息
      getResourceInfo(param).then(res => {
        console.log(res.data.data)
        this.download_zip_props = res.data.data
        this.download_modal = true
      })
    },
    stopRun(params) {
      params['iteration_id'] = store.state.iterationID
      h5PipelineIsRunning(params).then(res => {
        console.log(res.data.data.running)

        if (res.data.data.running) {
          h5PipelineStop(params).then(res => {
            this.$Notice.info({
              desc: '正在终止！',
              duration: getInfoNoticeShowTime() + 10,
            });
          })
        } else {
          this.$Notice.error({
            desc: '未找到运行中的jenkins信息，可能还在准备中！',
            duration: getInfoNoticeShowTime(),
          });
        }
      })
    }
  },
}
</script>

<style scoped>

</style>
