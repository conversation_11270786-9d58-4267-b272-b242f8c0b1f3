<template>
  <Card>
    <Row><!--@on-selection-change="selectChange"-->
      <tables stripe v-model="app_props" :columns="columns" >

      </tables>
    </Row>
    <Modal v-model="showPublishParamModal" title="发布入参"
           draggable scrollable
           width="550px">
      <Card>
        <Row>
          <i-col style="margin: 10px;text-align: right" span="8">
            <span style="text-align: right; display: inline-block;margin-top: 5px">测试环境:</span>
          </i-col>\
          <Col span="14">
            <Select v-model="publishParam.testSuiteCodeOfH5Zip" filterable style="margin:10px" ref="select1"
                    placeholder="请选择测试环境" @on-change="onChangeFindH5ZipVersionByEnv">
              <Option v-for="(item,index) in testSuiteCodeOfH5ZipList "
                      :value="item"
                      :label="item"
                      :key="index">
                {{ item }}</Option>
            </Select>
          </Col>
        </Row>
        <Row>
          <i-col style="margin: 10px;text-align: right" span="8">
            <span style="text-align: right; display: inline-block;margin-top: 5px">H5资源包版本:</span>
          </i-col>
          <Col span="14">
            <Select v-model="publishParam.h5ZipVersion" filterable style="margin:10px" ref="select2"
                    placeholder="请选择H5全量包版本">
              <Option v-for="(item,index) in h5ZipVersionList"
                      :value="item.value"
                      :label="item.label"
                      :key="index">
                {{ item.label }}</Option>
            </Select>
          </Col>
        </Row>
        <Row>
          <i-col style="margin: 10px;text-align: right" span="8">
            <span style="text-align: right; display: inline-block;margin-top: 5px">客户端构建版本:</span>
          </i-col>
          <Col span="14">
            <Input v-model="publishParam.appVersion" placeholder="请填写app构建版本" style="margin: 10px"/>
          </Col>
        </Row>
        <Row v-if="showAndroidParamModal">
          <Row>
            <i-col style="margin: 10px;text-align: right" span="8">
              <span style="text-align: right; display: inline-block;margin-top: 5px">交易渠道号:</span>
            </i-col>
            <Col span="14">
              <Input v-model="publishParam.tradeCoopId" placeholder="请填写交易渠道号" style="margin: 10px"/>
            </Col>
          </Row>
          <Row>
            <i-col style="margin: 10px;text-align: right" span="8">
              <span style="text-align: right; display: inline-block;margin-top: 5px">交易活动号:</span>
            </i-col>
            <Col span="14">
              <Input v-model="publishParam.tradeActionId" placeholder="请填写交易活动号" style="margin: 10px"/>
            </Col>
          </Row>
          <Row>
            <i-col style="margin: 10px;text-align: right" span="8">
              <span style="text-align: right; display: inline-block;margin-top: 5px">App渠道号:</span>
            </i-col>
            <Col span="14">
              <Input v-model="publishParam.channelId" placeholder="请填写App渠道号" style="margin: 10px"/>
            </Col>
          </Row>
          <Row>
            <i-col style="margin: 10px;text-align: right" span="8">
              <span style="text-align: right; display: inline-block;margin-top: 5px">sonar扫描:</span>
            </i-col>
            <Col span="14">
              <Checkbox v-model="publishParam.scan" style="margin: 15px">扫描请勾选</Checkbox>
            </Col>
          </Row>
        </Row>
        <!--todo 这段可以先不删，后期渠道能够选择的时候可以放开-->
       <!-- <Row>
          <i-col style="margin: 10px;text-align: right" span="8">
            <span style="text-align: right; display: inline-block;margin-top: 5px">客户端渠道选择:</span>
          </i-col>
          <Col span="14">
            <Select v-model="publishParam.channel" @on-change="selectContent" filterable style="margin:10px"
                    placeholder="请选择客户端渠道">
              <Option v-for="(item,index) in channelList" :value="item.value" :key="index">{{ item.label }}</Option>
            </Select>
          </Col>
        </Row>-->
      </Card>
      <div slot="footer">
        <Button type="text" @click="showPublishParamModal = false">关闭</Button>
        <Button type="primary" @click="publish" :loading="loading">确认</Button>
      </div>
    </Modal>
    <Modal
    :styles="{width: '60%'}"
    v-model="showDownloadModal"
    title="下载（右键点击【下载】按钮选择【链接另存为】可直接保存下载）"
    footer-hide
  >
    <Card>
      <Tabs ref="download_tabs">
        <TabPane label="客户端构建包" name="DownloadFile">
          <DownloadFile :download_file_props=downloadAppList></DownloadFile>
        </TabPane>
      </Tabs>
    </Card>
  </Modal>
  </Card>
</template>

<script>
import store from "@/spider-store"
import Tables from "@/components/tables";
import DownloadFile from '@/spider-view/ci-cd-mgt/h5/h5-download/h5-download-zip'
import {AppMergeCheckApi} from "@/spider-api/app-check"
import {
  getResourceInfo,
  externalServiceResult,
  userAction,
  userActionGet,
  h5CiPipelineApi,
  findH5ZipVersionByEnv,
  findTestSuiteCodeOfH5Zip,
  getInfoNoticeShowTime,
  getErrNoticeShowTime,
  getSuccessNoticeShowTime,
  appTestPublishApi,
  appTagTestPublishApi,
  h5PipelineStop,
  h5PipelineIsRunning
} from "@/spider-api/h5";
import {
  h5ElapseStatsApiGet
} from "@/spider-api/h5-stats"
import {
  test,
  formatDate,
} from "@/spider-api/h5-common";
export default {
  name: "h5-test-app",
  components: {
    Tables,
    DownloadFile
  },
  props:{
    //这里是table中的数据
    app_props:{
      type:Array
    },
    initTable:{
      type: Function,
    },
  },
  data () {
    return {
      lastElapsed: "-",
      currentElapsed: "准备中",
      showPublishParamModal:false,
      showDownloadModal:false,
      showAndroidParamModal:true,
      //rowIndex : -1,
      loading: false,
      appNameList:[],
      publishParam:{},
      h5ZipVersionList:[],
      testSuiteCodeOfH5ZipList:[],
      //channelList:[],
      downloadAppList:[],
      columns:[
        // {
        //   type: 'selection',
        //   width: 50,
        // },
        {title: "应用", key: "app_name" },
        {title: "仓库", key: "git_path" },
        {
          title: "操作时间",
          key: "operate_time",
          //sortable: true,
          render: (h, params) => {
            let value = params.row.operate_time
            if(value){
              value = formatDate(new Date(params.row.operate_time),'yyyy-MM-dd hh:mm:ss')
            }
            return h('div',value)
          }
        },
        {
          title: "发布状态",
          key: "status",
          render: (h, params) => {
            let status = params.row.status
            if(status && status.indexOf("running") > -1){
              return h('div',
                [h('a',
                  [h('Poptip',
                    {
                      props: {
                        transfer: true,
                        trigger: 'click',
                        title: '上次耗时: ' + this.lastElapsed,
                        content: '当前耗时：' + this.currentElapsed,
                        size: 'small'
                      },
                      on: {
                        'on-popper-show': () => {
                          h5ElapseStatsApiGet(params.row.app_name, params.row.package_type, store.state.h5_branch_version).then(res => {
                            if (res.data.status === "success") {
                              this.lastElapsed = res.data.data['last_elapsed']
                              this.currentElapsed = res.data.data['current_elapsed']
                            }
                          })
                        },
                        'on-popper-hide': () => {
                          this.lastElapsed = 0
                          this.currentElapsed = 0
                        }
                      }
                    },params.row.status_display)
                  ])
                ])
            }else if(status && status.indexOf("failure") > -1 || status == 'aborted'){
              return h('p',{style:{color: 'red'}},params.row.status_display)
            }else if(status && status.indexOf("success") > -1){
              return h('p',{style:{color: 'green'}},params.row.status_display)
            }else{
              return h('p',params.row.status_display)
            }
          }
        },
        {title: "操作人", key: "username" },
        {
          title: '操作',
          key: 'action',
          width: 250,
          align: 'center',
          render: (h, params) => {
            return h('div', [
              h('Button', {
                props: {
                  type: 'default',
                  size: 'small'
                },
                style: {
                  marginRight: '5px'
                },
                on: {
                  click: () => {
                    this.showPublishModal(params.row)
                  }
                }
              }, '发布'),
              h('Button', {
                props: {
                  type: 'default',
                  size: 'small'
                },
                style: {
                  marginRight: '5px'
                },
                on: {
                  click: () => {
                    if (!params.row.status || params.row.status.indexOf('running') < 0) {
                      this.$Notice.info({
                        desc: '不处于运行中',
                        duration: getInfoNoticeShowTime(),
                      });
                      return;
                    }
                    this.stopRun(params.row)
                  }
                }
              }, '终止'),
              h('Button', {
                props: {
                  type: 'default',
                  size: 'small'
                },
                style: {
                  marginRight: '5px'
                },
                on: {
                  click: () => {
                    //todo  参入传入downloadURL  注意：需要打包服务端将现有目录结构改造，并将对应的产物打一个压缩包
                    this.showDownload(params.row.app_name)
                  }
                }
              }, '下载'),
              h('Button', {
                props: {
                  type: 'default',
                  size: 'small'
                },
                style: {
                  marginRight: '5px'
                },
                on: {
                  click: () => {
                    let job_url = params.row.job_url
                    if (job_url) {
                      window.open(params.row.job_url)
                    } else {
                      this.$Notice.info({
                        title: '暂无详情',
                        duration: getInfoNoticeShowTime(),
                      });
                    }
                  }
                }
              }, '详情')
            ]);
          }
        },
      ],
    }
  },
  mounted() {
    //this.init()
  },
  methods: {
    /**
    * 显示发布的modal
    **/
    showPublishModal(row){
      if (row.status == 'running') {
        this.$Notice.info({
          desc: '选中的应用已经开始发布，请等待发布结束',
          duration: getErrNoticeShowTime(),
        });
        return;
      }
      //获取操作row的索引，便于form表单提交后修改对应的数据,暂时可以不使用，后期需要可以参考这样的写法
      //this.rowIndex = this.app_props.findIndex(item => item.app_name == row.app_name)
      //初始化数据
      let h5AppName = row.app_name.split("-")[0]
      let env = "test"
      let actionItem = "test_publish"
      //获取用户行为
      let _this = this
      this.selectUserAction(actionItem).then(function (data) {
        let publishParam = {}
        if(data) {
          publishParam = JSON.parse(data)
        }
        if(row.package_type == 'android'){
          _this.publishParam = {
            tradeCoopId:publishParam.tradeCoopId?publishParam.tradeCoopId:"A20131205",
            tradeActionId:publishParam.tradeActionId?publishParam.tradeActionId:"HD0001",
            channelId:publishParam.channelId?publishParam.channelId:"145217337",
            umengChannel:publishParam.umengChannel?publishParam.umengChannel:"test",
            channel:publishParam.channel?publishParam.channel:"ErenEben"
          }
          _this.showAndroidParamModal = true
          _this.publishParam.scan = publishParam.scan?true:false
        }else{
          _this.publishParam = {}
          _this.showAndroidParamModal = false
        }
        _this.publishParam.appVersion = publishParam.appVersion
        _this.publishParam.h5ZipVersion = publishParam.h5ZipVersion
        //_this.$refs.selectH5Zip.query = publishParam.h5ZipVersion
        //查打过H5zip包的测试环境列表
        findTestSuiteCodeOfH5Zip().then(res => {
          _this.testSuiteCodeOfH5ZipList = res.data.data?res.data.data:[{}]
          console.info("testSuiteCodeOfH5ZipList========="+_this.testSuiteCodeOfH5ZipList)
          _this.showPublishParamModal = true
        }).catch(err => {
          console.info("findTestSuiteCodeOfH5Zip:"+err.data)
          }
        )


        _this.appNameList = []
        _this.appNameList.push(row.app_name)
        //组装默认参数
        _this.publishParam.packageType = row.package_type
        _this.publishParam.appName = row.app_name
        _this.publishParam.appEnv = env
        _this.publishParam.appBranch = store.state.h5_branch_version
        _this.publishParam.repoPath = row.git_path
        _this.publishParam.h5AppName = h5AppName
        _this.publishParam.h5Env = env
        _this.publishParam.actionItem = actionItem
        _this.publishParam.appTagName = store.state.tag_name
        _this.publishParam.appCodeType = store.state.code_type
      })
    },

    onChangeFindH5ZipVersionByEnv(val){
      delete this.publishParam.h5ZipVersion
      this.h5ZipVersionList=[]
    //查询h5资源包版本
      findH5ZipVersionByEnv(this.publishParam.h5AppName ,val).then(res => {
        this.h5ZipVersionList = res.data.data
      }).catch(err =>{
        console.info("findH5ZipVersionByEnv:"+err.data)
      })
    },

    //todo  触发发布
    publish(){
      //判断必填项 todo 以后可以把上面的modal布局为form表单形式
      if(!this.publishParam.h5ZipVersion){
        this.$Message.info("所需H5资源包版本不能为空")
        return
      }
      if(!this.publishParam.appVersion){
        this.$Message.info("客户端构建版本名称不能为空")
        return
      }
      this.loading = true
      this.publishParam.iterationID = store.state.iterationID
      //发布检查
      this.appPublishCheckApi()
      //当前入参存入用户行为表
      let _this = this
      this.addUserAction(_this.publishParam.actionItem,_this.publishParam).then(function (data) {
        if(!data){
          _this.$Notice.error({
            desc: "行为记录数据失败，无法执行发布行为",
            duration: getErrNoticeShowTime(),
          });
          return
        }
        _this.publishParam.actionId = data
        let testPublish = appTestPublishApi
        // 如果是tag类型调用 tag发布接口
        if(_this.publishParam.appCodeType == "tag"){
            testPublish = appTagTestPublishApi
        }
        //测试发布
        testPublish(_this.publishParam).then(res => {
          _this.loading = false
          if(res.data.status == 'failed'){
            _this.$Notice.error({
              desc: res.data.msg,
              duration: getErrNoticeShowTime(),
            });
          }else{
            _this.$Notice.success({
              desc: res.data.msg,
              duration: getSuccessNoticeShowTime(),
            });
            _this.showPublishParamModal = false
            _this.initTable()
          }
        }).catch(err =>{
          console.info(err)
          _this.$Notice.error({
            desc: err,
            duration: getErrNoticeShowTime(),
          });
          _this.loading = false
          _this.showPublishParamModal = false
        })
      })
    },
    /**
     * app发布检查
     * 迭代iteration_id，应用列表app_name_list
     * */
    appPublishCheckApi(){
      let param ={
        iteration_id:store.state.iterationID,
        app_name_list:this.appNameList
      }
      let _this = this
      AppMergeCheckApi(param).then(res => {
        if (res.data.status == 'failed') {
          _this.$Notice.error({
            desc: res.data.msg,
            duration: getErrNoticeShowTime(),
          });
        }
      }).catch(err =>{
        console.info(err)
      })
    },
    /**
     * 用户行为录入test_publish
     * action_item    action_value
     * todo 用new Promise构造（作用于链式调用）
     * */
     addUserAction(actionItem,actionValue){
      return new Promise(function(resolve, reject){
        let req = {
          action_item:actionItem,
          action_value:actionValue
        }
        userAction(req).then(res =>{
          resolve(res.data.data)
        }).catch(err =>{
          reject(false)
        })
      })
    },
    /**
     * 用户行为查询test_publish
     * */
    selectUserAction(actionItem){
      return new Promise(function (resolve, reject) {
        userActionGet(actionItem).then(res =>{
          let data
          //console.info(JSON.stringify(res))
          if(res.data.status != 'failed'){
            data = res.data.data.replace(/False/g,"false").replace(/True/g,"true").replace(/'/g,"\"")
          }
          resolve(data)
        }).catch(err =>{
          reject(false)
        })
      })
    },
    /**
     * 停止运行
     * @param params
     */
    stopRun(params) {
      params['iteration_id'] = store.state.iterationID
      h5PipelineIsRunning(params).then(res => {
        if (res.data.data.running) {
          h5PipelineStop(params).then(res => {
            this.$Notice.info({
              desc: '正在终止！',
              duration: getInfoNoticeShowTime() + 10,
            });
          })
        } else {
          this.$Notice.error({
            desc: '未找到运行中的jenkins信息，可能还在准备中！',
            duration: getInfoNoticeShowTime(),
          });
        }
      })
    },
    /**
     * 通过【应用】【迭代】【环境】下载对应的资源
     * @param app_name
     */
    showDownload(app_name) {
      let param = {
        'app_name': app_name,//'fund',
        'iteration_id': store.state.iterationID,
        'suite_code': 'test'
      };//
      //下载信息
      getResourceInfo(param).then(res => {
        this.downloadAppList = res.data.data
        this.showDownloadModal = true
      })
    }
  },
}
</script>

<style scoped>

</style>
