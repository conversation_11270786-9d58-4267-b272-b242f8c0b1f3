<template>
    <div>
        <Card>
            <Row style="margin-top: 10px">
                <i-col style="margin: 10px;" span="2">
                    <span style="display: inline-block;width:60px;text-align: right;">代码类型</span>
                </i-col>
                <Col style="margin: 10px;" span="8">
                    <Radio-group v-model="code_type" @on-change="change_code_type" size="large">
                        <Radio label="branch">分支</Radio>
                        <Radio label="tag" :style="'color: #DDDDDD'">TAG</Radio>
                    </Radio-group>
                </Col>
                <Col style="margin-left: 20px" span="2">
                    <div style="margin-bottom: 15px">
                        <h4>
                            上线申请模板<a href="http://download.howbuy.pa/H5上线研发提供.xlsx" target="abc">下载</a>
                        </h4>
                    </div>
                </Col>
                <Col style="margin-right: 1px" span="1">
                    <Icon style="margin-bottom: 10px" type="md-cloud-download" />
                </Col>
                <!--    功能迁移至mantis tapd gateway 20240524 by fwm    -->
                <!--        <i-col style="margin: 10px">-->
                <!--          <Icon type="md-list"></Icon>&nbsp;-->
                <!--          <Button-->
                <!--            ghost-->
                <!--            type="primary"-->
                <!--            style="text-align: left; display: inline-block; width:120px;"-->
                <!--            @click="syncIterToTapd"-->
                <!--          >同步迭代至TAPD-->
                <!--          </Button>-->
                <!--        </i-col>-->
            </Row>
            <Row style="margin-top: 10px">
                <i-col style="margin: 10px;" span="2">
                    <span style="display: inline-block;width:60px;text-align: right;">组</span>
                </i-col>
                <Col span="8">
                    <Select v-model="group" @on-change="change_group" filterable ref="groupQuery">
                        <Option v-for="item in group_list" :value="item.value" :label="item.label" :key="item.value"
                            >{{ item.label }}
                        </Option>
                    </Select>
                </Col>
            </Row>

            <Row style="margin-top: 10px">
                <i-col style="margin: 10px;" span="2">
                    <span style="display: inline-block;width:60px;text-align: right;">分支版本</span>
                </i-col>
                <Col span="8">
                    <Select v-model="branch_version" @on-change="get_git_repos" filterable ref="branchQuery">
                        <Option
                            v-for="item in branch_version_list"
                            :value="item.value"
                            :label="item.label"
                            :key="item.value"
                            >{{ item.label }}
                        </Option>
                    </Select>
                </Col>
            </Row>

            <Row v-show="show_tag" style="margin-top: 10px">
                <i-col style="margin: 10px" span="2">
                    <span style="display: inline-block; width:60px;text-align: right;">TAG版本</span>
                </i-col>
                <Col span="8">
                    <span style="margin: 10px; text-align: left; display: inline-block; ">{{ tag_name }}</span>
                </Col>
            </Row>
            <Row style="margin-left: 5px">
                <i-col style="margin: 5px" span="2">
                    <Icon type="ios-list-box" style="margin-right: 8px" />
                    <span style="text-align: left; display: inline-block; width:60px;">申请时间</span>
                </i-col>
                <i-col style="margin: 5px" span="8">
                    <p style="width: 300px">{{ iterativeInfo.br_start_date }}</p>
                </i-col>
            </Row>
            <Row style="margin-top: 10px">
                <i-col style="margin: 10px" span="2">
                    <span style="display: inline-block; width:60px;text-align: right;">截止时间</span>
                </i-col>
                <Col span="8">
                    <DatePicker type="date" placeholder="截止时间" v-model="deadline"></DatePicker>
                </Col>
            </Row>
            <Row style="margin-top: 10px">
                <i-col style="margin: 10px" span="2">
                    <span style="display: inline-block; width:60px;text-align: right;">发布类型</span>
                </i-col>
                <Col span="8">
                    <span style="margin: 10px; text-align: left; display: inline-block; ">{{ publish_type }}</span>
                </Col>
            </Row>
            <Row style="margin-top: 10px">
                <i-col style="margin: 10px" span="2">
                    <span style="display: inline-block; width:60px;text-align: right;">迭代版本</span>
                </i-col>
                <Col span="8">
                    <span
                        v-if="branch_version != '' && group != '' && branch_version != undefined"
                        style="margin: 10px; text-align: left; display: inline-block; "
                    >
                        {{ group + '_' + branch_version }}</span
                    >
                </Col>
            </Row>
            <Row>
                <tables stripe v-model="add_app" :columns="columns"> </tables>
            </Row>
            <Row style="margin: 10px">
                <Button
                    ghost
                    type="primary"
                    style="text-align: left;float: right; display: inline-block; width:80px;"
                    @click="showAddRepo"
                    >申请应用
                </Button>
            </Row>
            <Drawer title="应用列表" placement="left" :closable="false" v-model="appListShow" width="30%">
                <Tree
                    :data="group_repos"
                    ref="group_repos_validate"
                    expand="true"
                    show-checkbox
                    v-model="add_project_list"
                ></Tree>
                <div class="demo-drawer-footer">
                    <Button style="margin-right: 8px" @click="appListShow = false">取消</Button>
                    <Button type="primary" @click="addSyss">保存</Button>
                </div>
            </Drawer>
        </Card>
        <Modal title="上线申请确认" width="800" v-model="comfirm_status" @on-ok="confirmProdPublishApply">
            <div>
                <Row>
                    <Table :columns="comfirm_table" :data="comfirm_tabledata"> </Table>
                    <Table :columns="h5_comfirm_table" :data="comfirm_tabledata"> </Table>
                </Row>
            </div>
        </Modal>
    </div>
</template>

<script>
import Cookies from 'js-cookie'
import Tables from '@/components/tables'
import store from '@/spider-store'
import {
    getIterListApi,
    getIterInfo,
    getIterGitRopesApi,
    iterGroupOtherReposInfoApi,
    getHistoryIterListApi,
    getH5Group
} from '@/spider-api/get-iter-info'
import { userAction, userActionGet } from '@/spider-api/h5'
import { test } from '@/spider-api/h5-common'
import { createIter, getCreateIterStatusApi } from '@/spider-api/create-iter'
import { addRepos, delRepos, modIterInfo } from '@/spider-api/mgt-iter'
import { h5ConfirmProdApply, syncIterationToTapd } from '@/spider-api/iter-plan'
import { getUserName } from '@/libs/util'

export default {
    name: 'h5-branch',
    components: {
        Tables
    },
    data() {
        return {
            comfirm_status: false,
            comfirm_tabledata: [],
            publish_type: '',
            show_tag: false,
            tag_name: '',
            code_type: 'branch',
            group: '',
            git_repo: '',
            branch_version: '',
            add_app: [],
            appListShow: false,
            modal_val: false,
            appTree: [],
            group_repos: [],
            add_project_list: [],
            description: '',
            iterative_list: [],
            iteration_id: '',
            deadline: '',
            repoTableData: [],
            br_start_date: '',
            //应用table
            columns: [
                { title: '应用', key: 'app_name' },
                { title: '仓库', key: 'gitRepo' },
                { title: '申请人', key: 'proposer' },
                { title: '中文名', key: 'cname' },
                /*{title: "描述", key: "description" },*/
                {
                    title: '操作',
                    key: 'action',
                    width: 150,
                    align: 'center',
                    options: ['delete'],

                    render: (h, params) => {
                        return h('div', [
                            h(
                                'Button',
                                {
                                    props: { type: 'error', size: 'small' }
                                },
                                [
                                    h(
                                        'Poptip',
                                        {
                                            props: {
                                                confirm: true,
                                                transfer: true,
                                                title: '你确定要删除吗?',
                                                type: 'error',
                                                size: 'small'
                                            },
                                            on: {
                                                'on-ok': () => {
                                                    console.log(params.row.proposer)
                                                    console.log(store.state.uname)
                                                    console.log(this.$store.state.user.userName)
                                                    if (this.code_type == 'tag') {
                                                        this.$Message.error('tag不能删除')
                                                    } else {
                                                        if (params.row.proposer == getUserName()) {
                                                            this.$Spin.show({
                                                                render: h => {
                                                                    return h('div', [
                                                                        h('Icon', {
                                                                            class: 'demo-spin-icon-load',
                                                                            props: {
                                                                                type: 'ios-loading',
                                                                                size: 18
                                                                            }
                                                                        }),
                                                                        h('div', '分支删除中请稍等。。。')
                                                                    ])
                                                                }
                                                            })

                                                            this.remove(params.index)
                                                            console.log(params.row.gitRepo)
                                                            console.log(params.row.app_name)
                                                            console.log(this.iteration_id)
                                                            delRepos({
                                                                repos_str: [
                                                                    {
                                                                        repos_path: params.row.gitRepo,
                                                                        module_name: params.row.app_name
                                                                    }
                                                                ],
                                                                iteration_id: this.group + '_' + this.branch_version
                                                            }).then(result => {
                                                                // console.log(JSON.stringify(result));
                                                                let sid = result.data.data['sid']
                                                                console.log(sid)
                                                                this.getStatus(sid)
                                                            })
                                                        } else {
                                                            this.$Message.error(
                                                                '没有权限删除' + params.row.proposer + '用户申请的分支'
                                                            )
                                                        }
                                                    }
                                                },
                                                'on-cancel': function() {}
                                            }
                                        },
                                        '删除'
                                    )
                                ]
                            )
                            //return-end
                        ])
                    }
                }
            ],
            //组的下拉框
            group_list: [],
            git_repo_list: [
                {
                    value: '1',
                    label: '1'
                }
            ],
            //分支版本下拉框
            branch_version_list: [],
            comfirm_table: [
                {
                    title: '分支版本',
                    key: 'branch_name'
                },
                {
                    title: '版本',
                    key: 'apph5version'
                },
                {
                    title: '环境',
                    key: 'suite_name'
                }
            ],
            h5_comfirm_table: [
                {
                    title: 'h5分支版本',
                    key: 'h5_resource_branch'
                },
                {
                    title: 'h5版本',
                    key: 'h5ZipVersion'
                },
                {
                    title: 'h5分支类型',
                    key: 'branch_type'
                }
            ]
        }
    },
    computed: {
        /**
         * 迁移来的一堆计算属性，我们只需要他的 截止时间
         */
        iterativeInfo() {
            let iterativeInfo = { br_start_date: '', deadline: '', description: '' }
            let iteration_id = this.group + '_' + this.branch_version
            console.log(iteration_id)
            for (let i = 0; i < this.iterative_list.length; i++) {
                if (iteration_id == this.iterative_list[i].pipeline_id) {
                    ;(iterativeInfo.br_start_date = this.iterative_list[i].br_start_date),
                        (iterativeInfo.deadline = this.iterative_list[i].duedate),
                        (iterativeInfo.description = this.iterative_list[i].description),
                        (iterativeInfo.tapd_id = this.iterative_list[i].tapd_id),
                        (iterativeInfo.branch_name = this.iterative_list[i].branch_name)
                    iterativeInfo.tag_name = this.iterative_list[i].tag_name
                }
            }
            return iterativeInfo
        }
    },
    mounted() {
        //alert(this.$route.query.iteration_id)
        //this.init()
        this.comfirm_tabledata = [this.$route.query]
        console.log('this.comfirm_tabledata====' + this.comfirm_tabledata)
        console.log('this.$route.query====' + this.$route.query)
        this.comfirm_status = this.comfirm_tabledata[0]['comfirm_status']
        if (this.comfirm_status == 'true') {
            this.comfirm_status = true
        }
        console.log('typeof this.comfirm_status====' + typeof this.comfirm_status)
        console.log('JSON.stringify(this.$route.query)====' + JSON.stringify(this.$route.query))
        // if (JSON.stringify(this.$route.query) !== "{}" && this.$route.query.md5Str) {
        //   this.confirmProdPublishApply(this.$route.query);
        // }
    },
    methods: {
        /**
         * 初始化组数据
         */
        init() {
            if (this.$route.query.project_group) {
                this.group = this.$route.query.project_group
                console.log('this.group====' + this.group)
                this.change_group()
            }
            if (this.$route.query.branch_name) {
                this.branch_version = this.$route.query.branch_name
                console.log('this.branch_version====' + this.branch_version)
            }
            let iteration_id = ''
            if (this.group != '' && this.branch_version != '') {
                iteration_id = this.group + '_' + this.branch_version
            }
            let getIterInfo = getIterListApi
            this.show_tag = false
            if (this.code_type == 'tag') {
                getIterInfo = getHistoryIterListApi
                this.show_tag = true
            }
            store.commit('setIterationID', this.iteration_id)
            store.commit('setShowTag', this.show_tag)
            store.commit('setCodeType', this.code_type)
            //alert(store.state.code_type)
            getIterInfo('h5').then(res => {
                let iterative_list = res.data.data['iterative_list'] //branch_version_list
                this.iterative_list = res.data.data['iterative_list']
                //返回用 分支版本list
                let array_branch_version = []
                //返回用 组list
                let array_project_group = []
                //循环中 去重用 分支版本set
                let branch_version_set = new Set()
                //循环中 去重用 组set
                let project_group_set = new Set()
                //第一次循环，去重
                for (let i = 0; i < iterative_list.length; i++) {
                    //分支版本(初始化暂时取消分支版本，以后可以做 一个分支版本对应多个组，那个时候再放开)
                    branch_version_set.add(iterative_list[i].branch_name)
                    //组
                    project_group_set.add(iterative_list[i].project_group)
                }
                //循环 分支版本set，封装对象
                for (let i of branch_version_set) {
                    let branchObj = {}
                    branchObj.value = i
                    branchObj.label = i
                    array_branch_version.push(branchObj)
                }
                for (let i of project_group_set) {
                    let groupObj = {}
                    groupObj.value = i
                    groupObj.label = i
                    array_project_group.push(groupObj)
                }
                //页面展示的 分支版本
                this.branch_version_list = array_branch_version
                //页面展示的 组
                this.group_list = array_project_group
                //过滤分组
                getH5Group().then(res => {
                    console.log(this.group_list)
                    console.log(typeof this.group_list)
                    let group_bak_list = []
                    let i = 0
                    for (let a of this.group_list) {
                        console.log(a)
                        if (res.data.data.h5_group_list.indexOf(a.value) > -1) {
                            group_bak_list.push(a)
                            console.log(a.value)
                            console.log(this.group_list.indexOf(a))
                        }
                        i = i + 1
                    }
                    this.group_list = group_bak_list
                    console.log(this.group_list)
                    console.log(res.data.data.h5_group_list)

                    //开始回填
                    let param = 'H5branch'
                    let jsonArray = []
                    if (this.$route.query.project_group && this.$route.query.branch_name) {
                        this.get_git_repos()
                    } else {
                        userActionGet(param).then(res => {
                            console.log('============= userActionApplyGet ============')
                            jsonArray = JSON.parse(res.data.data.replace(/'/g, '"'))
                            this.group = jsonArray[0].group
                            store.commit('setH5Group', this.group)

                            this.branch_version = jsonArray[0].branch_version
                            store.commit('setH5BranchVersion', this.branch_version)
                            getIterInfo({ iterationID: iteration_id }).then(res => {
                                //因为全是异步调用，很可能init()方法中的getIterListApi()查询方法还未执行，就出现了先执行
                                //this.change_group() 和 this.get_git_repos_common() 方法的情况，加入回调函数，确保this.iterative_list有值
                                this.iterative_list = res.data.data['iterative_list']
                                //触发一下组选择
                                this.change_group()
                                //触发一下分支版本选择
                                this.get_git_repos_common()
                            })
                        })
                    }
                })
                //分组过滤
            })
        },
        confirmProdPublishApply(query) {
            if (JSON.stringify(this.$route.query) !== '{}' && this.$route.query.md5Str) {
                console.log('打印产线申请确认')
                this.comfirm_tabledata = [this.$route.query]
                console.log(this.comfirm_tabledata)
                console.log(this.$route.query)
                let query = this.$route.query
                let md5_str = query['md5Str']
                let suite_name = query['suite_name']
                this.$Spin.show({
                    render: h => {
                        return h('div', [
                            h('Icon', {
                                class: 'demo-spin-icon-load',
                                props: {
                                    type: 'ios-loading',
                                    size: 18
                                }
                            }),
                            h('div', '确认产线发布申请中...')
                        ])
                    }
                })
                h5ConfirmProdApply(md5_str, suite_name)
                    .then(result => {
                        if (result.data.status === 'success') {
                            this.$Message.success(result.data.msg)
                        } else {
                            this.$Message.error(result.data.msg)
                        }
                        this.$Spin.hide()
                    })
                    .catch(err => {
                        this.$Spin.hide()
                        this.$Message.error(err.response.data.msg)
                    })
            }
        },
        /**
         * 左侧框 显示
         */
        showAddRepo() {
            this.appListShow = true
            let iteration_id = ''
            if (this.group != '' && this.branch_version != '') {
                iteration_id = this.group + '_' + this.branch_version
            }
            this.iteration_id = iteration_id
            iterGroupOtherReposInfoApi({ iterationID: iteration_id }).then(res => {
                this.group_repos = res.data.data['repos_list']
                console.log(res.data.data['repos_list'])
            })
        },
        /**
         * 左边框 应用列表 保存
         */
        addSyss() {
            this.$Spin.show({
                render: h => {
                    return h('div', [
                        h('Icon', {
                            class: 'demo-spin-icon-load',
                            props: {
                                type: 'ios-loading',
                                size: 18
                            }
                        }),
                        h('div', '分支拉取中请稍等。。。')
                    ])
                }
            })

            let vm = this
            let choicesAll = vm.$refs['group_repos_validate'].getCheckedNodes()
            let project_list = []
            //用于组成调用原有接口对象
            let project_list2 = []
            let group_count = 0
            choicesAll.forEach(item => {
                if ('group_name' in item) {
                    this.groupName = item.title
                    group_count = group_count + 1
                } else if ('children' in item) {
                } else {
                    if ('module_name' in item) {
                        project_list.push({ app_name: item.repos_path, module_name: item.module_name })
                        project_list2.push({ repos_path: item.repos_path, module_name: item.module_name })
                    } else {
                        project_list.push({ app_name: item.repos_path })
                        project_list2.push({ repos_path: item.repos_path })
                    }
                }
            })
            this.appListShow = false
            store.commit('setH5AppNames', this.add_app)
            console.log('========= store.state.h5_app_names ==========')
            console.log(store.state.h5_app_names)
            // console.log(vm.iterativeInfo)
            let branch_info = {
                branch_name: vm.iterativeInfo.branch_name,
                branch_type: 'release', //vm.iterative_type,
                is_update_out_dep: '1',
                deadline: vm.iterativeInfo.deadline,
                desc: vm.iterativeInfo.description,
                tapd_id: vm.iterativeInfo.branch_name,
                tag_name: vm.iterativeInfo.tag_name,
                gitlab_group: vm.group,
                repos_str: project_list2
            }

            createIter(branch_info).then(result => {
                //console.log(JSON.stringify(result))
                if (result.data['status'] == 'failed') {
                    alert(JSON.stringify(result.data['msg']))
                    this.$Spin.hide()
                    // this.btnDisabled = false;
                } else {
                    let sid = result.data.data['sid']
                    this.getStatus(sid)
                }
            })
        },
        getStatus(sid) {
            getCreateIterStatusApi({ sid: sid })
                .then(res => {
                    if (res.data.msg == 'running') {
                        let vm = this
                        setTimeout(function() {
                            vm.getStatus(sid)
                        }, 2000)
                    } else {
                        this.$Message.success(res.data['msg'])
                        alert(res.data['msg'])
                        getIterGitRopesApi({ iterationID: this.iteration_id }).then(res => {
                            //获取git仓库信息
                            let repoTableData = res.data.data['git_repo_list']
                            //获取应用信息
                            let projecttableData = res.data.data['appname_list']
                            console.log(projecttableData)
                            //将 repoTableData,projecttableData 两个数组数据合并
                            let app_array = []

                            // for(let i of projecttableData){
                            //   for(let j of repoTableData){
                            //     if(j.app_name == i.appName){
                            //       let obj = {}
                            //       console.log(i.appName)
                            //       obj = ({"app_name":j.app_name,"sys_status":i.sys_status,"appType":i.appType,
                            //         "jdkVersion":i.jdkVersion,"cname":j.cname,"gitRepo":j.gitRepo,"gitRepo":j.gitRepo,
                            //         "proposer":j.proposer,"br_start_date":this.br_start_date})
                            //       app_array.push(obj)
                            //     }
                            //   }
                            // }
                            //回填到页面
                            //this.add_app = app_array

                            this.add_app = repoTableData
                            store.commit('setAppArray', this.add_app)
                        })
                        this.$Spin.hide()
                        // this.btnDisabled = false;
                    }
                })
                .catch(err => {
                    this.$Message.error(err.response.data.msg)
                    alert(err.response.data.msg)
                    this.$Spin.hide()
                    this.btnDisabled = false
                })
        },
        //删除按钮
        remove(index) {
            this.add_app.splice(index, 1)
        },
        show(index) {},
        modal_ok() {
            this.$Message.info('已保存')
            //暂时的回填
            console.log(this.description)
            let action_value = []

            let param = {
                action_item: 'branch_description',
                action_value: this.remote_props
            }
        },
        modal_cancel() {
            // this.$Message.info('Clicked cancel');
        },
        /**
         * 组切换触发
         */
        change_group(val) {
            if (val) {
                // 切换分组，清空后续信息数据 - 会触发分支版本select的change事件
                this.branch_version = ''
            }

            console.log('进change_group()了')
            let group = this.group
            store.commit('setH5Group', this.group)
            let branch_version_list = []
            let branch_version_set = new Set()
            // console.log("this.iterative_list===" + this.iterative_list)
            for (let i of this.iterative_list) {
                let project_group = i.project_group
                let branch_name = i.branch_name
                if (group == project_group) {
                    branch_version_set.add(branch_name)
                }
            }
            for (let i of branch_version_set) {
                branch_version_list.push({ value: i, label: i })
            }
            this.branch_version_list = branch_version_list
            //this.$refs.branchQuery.query = ""
        },
        /**
         * 代码类型切换触发
         */
        change_code_type() {
            // 重新初始化
            this.init()
        },

        /**
         * 分支版本切换触发
         */
        get_git_repos() {
            this.get_git_repos_common()
            //记录当前操作 --> 你绑定了环境
            let action_value_list = []
            let action_value_obj = { group: this.group, branch_version: this.branch_version }
            action_value_list.push(action_value_obj)
            let param = {
                action_item: 'H5branch',
                action_value: action_value_list
            }
            //用户行为保存
            userAction(param).then(res => {
                console.log(res)
                let status = res.data.status
                if (status == 'success') {
                    console.log('------ success --------')
                } else {
                    console.log('------ error --------')
                }
            })
        },
        /**
         * 分支版本触发的统一方法
         */
        get_git_repos_common() {
            let iteration_id = ''
            if (this.group != '' && this.branch_version != '') {
                iteration_id = this.group + '_' + this.branch_version
            }
            store.commit('setIterationID', iteration_id)
            store.commit('setH5Group', this.group)
            this.iteration_id = iteration_id
            for (let i = 0; i < this.iterative_list.length; i++) {
                if (iteration_id == this.iterative_list[i].pipeline_id) {
                    this.deadline = this.iterative_list[i].duedate
                    store.commit('setDeadLine', this.deadline)
                }
            }
            store.commit('setH5BranchVersion', this.branch_version)
            getIterGitRopesApi({ iterationID: iteration_id }).then(res => {
                //获取git仓库信息
                let repoTableData = res.data.data['git_repo_list']
                //获取应用信息
                let projecttableData = res.data.data['appname_list']
                console.log('========= repoTableData ===========')
                console.log(repoTableData)
                console.log('========= projecttableData ==========')
                console.log(projecttableData)
                this.add_app = repoTableData //repoTableData//projecttableData
                store.commit('setAppArray', this.add_app)
            })
            //回填发布类型
            console.log('============= this.iterative_list ==========')
            // console.log(JSON.stringify(this.iterative_list))
            for (let i of this.iterative_list) {
                if (this.branch_version === i.branch_name) {
                    this.publish_type = i.br_style
                    if (i.tag_name != undefined) {
                        this.tag_name = i.tag_name
                    } else {
                        this.tag_name = ''
                    }
                    store.commit('setTagName', this.tag_name)
                    store.commit('setPublishType', this.publish_type)
                    this.br_start_date = i.br_start_date
                }
            }
        },
        syncIterToTapd() {
            syncIterationToTapd(this.iteration_id).then(res => {
                if (res.data.status == 'success') {
                    this.$Notice.success({
                        desc: '同步成功'
                    })
                } else {
                    this.$Notice.error({
                        desc: '同步失败'
                    })
                }
            })
        }
    }
}
</script>

<style scoped>
.demo-spin-icon-load {
    animation: ani-demo-spin 1s linear infinite;
}
</style>
