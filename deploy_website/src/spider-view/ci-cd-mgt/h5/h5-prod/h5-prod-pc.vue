<template>
  <Card>
    <Row>
      <tables stripe v-model="static_props" :columns="columns">

      </tables>
    </Row>
    <Modal
      v-model="publish_env_model"
      title="发布环境信息"
      width="35%"
      :styles="{height: '60%'}"
      footer-hide>
      <Card>
        <Row>
           <CheckboxGroup v-model="checkbox">
            <Checkbox  v-for="(item,index) in publish_env_list"
                        :label="item.suite_code">
              <span>{{item.suite_desc}}</span>
            </Checkbox>
          </CheckboxGroup>
        </Row>
        <br/>
        <br/>
        <Row>
          <Button @click="environment_modal_cancel">取消</Button>
          <Button @click="environment_modal_ok">确认</Button>
        </Row>
      </Card>
    </Modal>
    <Modal
      v-model="detail_modal"
      title="详情"
      width="70%"
      :styles="{height: '600px'}"
      footer-hide>
      <Card>
        <Row v-html="publish_detail_info">
        </Row>
      </Card>
    </Modal>
  </Card>
</template>

<script>
    import store from "@/spider-store"
    import Tables from "@/components/tables";
    import {
        h5ProdPublishApi,
        h5ProPublishStateApi,
        getInfoNoticeShowTime,
        getErrNoticeShowTime,
        h5ProPublishEnv
    } from "@/spider-api/h5";

    export default {
        name: "h5-prod-pc",
        components: {
            Tables,
        },
        props: {
            //这里是table中的数据
            static_props: {
                type: Array
            },
            queryState: {
                type: Function,
                default: null
            },
            vue_this: {
                type: Object
            }
        },
        data() {
            return {
                publish_detail_info: "",
                detail_modal: false,
                publish_env_model: false,
                checkbox: [],
                need_publish_data:[],
                publish_env_list:[],
                columns: [
                    {title: "应用", key: "app_name"},
                    {title: "仓库", key: "git_path"},
                    {
                        title: "操作时间",
                        key: "operate_time",
                        //sortable: true,
                        render: (h, params) => {
                            let value = params.row.operate_time
                            if (value == '' || value == null) {
                            } else {
                                value = this.formatDate(new Date(params.row.operate_time), 'yyyy-MM-dd hh:mm:ss')
                            }
                            return h('div', value)
                        }
                    },
                    {title: "发布人", key: "username"},
                    {title: "申请状态", key: "apply_status_display",
                      render: (h, params) => {
                        let color = "#2db7f5";
                        if (params.row.apply_status && params.row.apply_status.indexOf("success") >= 0) {
                          color = "green";
                        } else if (params.row.apply_status && params.row.apply_status.indexOf("failure") >= 0) {
                          color = "red";
                        }

                        return h("Tooltip", {
                          props: {
                            placement: 'top',
                            content: params.row.apply_message,
                            "max-width": 500,
                            transfer: true
                          }
                        }, [
                          h("p",
                            {props: {}, style: {color: color}},
                            params.row.apply_status_display)
                        ]);
                      }
                    },
                    {title: "发布状态", key: "status_display",
                        render: (h, params) => {
                          let color = "#2db7f5";
                          if (params.row.status && params.row.status.indexOf("success") >= 0) {
                            color = "green";
                          } else if (params.row.status && params.row.status.indexOf("failure") >= 0) {
                            color = "red";
                          }
                          else if (params.row.status && params.row.status.indexOf("warning") >= 0) {
                          color = "#ff9900";
                          }
                          /*return h("div", [
                            h("p",
                              {props: {}, style: {color: color}},
                              params.row.status)
                          ]);*/
                          return h("Tooltip", {
                            props: {
                              placement: 'top',
                              content: params.row.message,
                              "max-width": 500,
                              transfer: true
                            }
                          }, [
                            h("p",
                              {props: {}, style: {color: color}},
                              params.row.status_display)
                          ]);
                        }
                    },
                    {
                        title: '操作',
                        key: 'action',
                        width: 200,
                        align: 'center',
                        render: (h, params) => {
                            return h('div', [
                                h('Button', {
                                    props: {
                                        type: 'default',
                                        size: 'small'
                                    },
                                    style: {
                                        marginRight: '5px'
                                    },
                                    on: {
                                        click: () => {
                                            let compile_array = []
                                            compile_array.push(params.row)
                                            // this.publish(compile_array)
                                            this.showPublishEvn(compile_array)
                                        }
                                    }
                                }, '发布'),
                                h('Button', {
                                    props: {
                                        type: 'default',
                                        size: 'small'
                                    },
                                    style: {
                                        marginRight: '5px'
                                    },
                                    on: {
                                        click: () => {
                                            this.showDetail(params.row.app_name)
                                        }
                                    }
                                }, '详情'),
                            ]);
                        }
                    },
                ],
            }
        },
        mounted() {
            this.init()
        },
        methods: {
            init() {
            },
            showPublishEvn(param){

              for (let i = 0; i < param.length; i++) {
                if(param[i].apply_status.indexOf('success') < 0){
                  this.$Notice.error({
                    title: param[i].app_name + '申请未成功',
                    duration: getErrNoticeShowTime(),
                  });
                  return
                }
              }

              this.need_publish_data = param;

              let app_name_list = []
              for (let i = 0; i < param.length; i++) {
                app_name_list.push(param[i].app_name)
              }
              let req_param = {
                'iteration_id': store.state.iterationID,
                "app_name_list": "'" + app_name_list.join("','") + "'"
              }

              h5ProPublishEnv(req_param).then(res => {
                console.log(res)
                this.publish_env_model = true;
                this.publish_env_list =  res.data.data
                this.checkbox=[]
                for (let row of this.publish_env_list)
                {
                  this.checkbox.push(row.suite_code)
                }

                // for (let i = 0; i < res.data.data.length; i++) {
                //   this.publish_env_list.push({'env': res.data.data[i]})
                // }
              })

            },
            environment_modal_ok(){
              //if(this.checkbox.length != this.publish_env_list.length){
              if(this.checkbox.length == 0){
                  this.$Notice.error({
                    title: '需要选择环境发布',
                    duration: getErrNoticeShowTime(),
                  });
                }
                else{
                  this.publish_env_model=false;
                  this.publish(this.need_publish_data)
                }
            },
            environment_modal_cancel(){
              this.publish_env_model=false;
            },
            //发布
            publish(param) {
                let publish_list = []
                let publish_obj = {}
                console.log(param)
               // 环境套 支持多个环境
                let suite_name = ""
                for (let row of this.checkbox){
                  if (suite_name == ""){
                    suite_name = row
                  }
                  else{
                    suite_name = suite_name +","+ row
                  }
                }

                for (let i = 0; i < param.length; i++) {
                    let app_name = param[i].app_name
                    //let suite_name = param[i].suite_name

                    publish_obj = {
                        'suite_name': suite_name,
                        'op_type': 'code_update',
                        'app_name': app_name,
                        'iteration_id': store.state.iterationID
                    }
                    publish_list.push(publish_obj)
                }
                console.log(publish_list)
                if (publish_list.length > 0) {
                    h5ProdPublishApi(publish_list).then(res => {
                        console.log(res)
                        if (res.status == '200') {
                            //成功
                            this.$Notice.success({
                                title: res.data.msg,
                                duration: getInfoNoticeShowTime(),
                            });

                            console.log(" will call parent method query status")
                            this.queryState(this.vue_this)
                        } else {
                            //失败
                            this.$Notice.error({
                                title: res.data.msg,
                                duration: getErrNoticeShowTime(),
                            });
                        }
                    })
                }
            },
            formatDate(date, fmt) {
                let o = {
                    'M+': date.getMonth() + 1, // 月份
                    'd+': date.getDate(), // 日
                    'h+': date.getHours(), // 小时
                    'm+': date.getMinutes(), // 分
                    's+': date.getSeconds(), // 秒
                    'S': date.getMilliseconds() // 毫秒
                }
                if (/(y+)/.test(fmt)) {
                    fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length))
                }
                for (var k in o) {
                    if (new RegExp('(' + k + ')').test(fmt)) {
                        fmt = fmt.replace(RegExp.$1, (RegExp.$1.length === 1) ? (o[k]) : (('00' + o[k]).substr(('' + o[k]).length)))
                    }
                }
                return fmt
            },
            showDetail(app_name) {
                //查看发布信息
                h5ProPublishStateApi(store.state.iterationID).then(res => {
                    console.log(res.data.data.operate_info)
                    let detail_info = "<div>";
                    for (let i in res.data.data.operate_info) {
                        let publish_info_data = res.data.data.operate_info[i]
                        console.log(app_name)
                        console.log(publish_info_data.app_name)
                        console.log(publish_info_data.app_name === app_name)
                        if (publish_info_data.app_name === app_name) {
                            let result = publish_info_data.message
                            let ip = publish_info_data.ip
                            if (result != null) {
                                let htmlResult = "<p>ip:" + ip + "</p>" + "<div><pre>" + (result.replaceAll("\\n", "<br/>")) + "</pre></div>"
                                detail_info +=htmlResult
                            }
                        }
                    }
                    detail_info += "</div>";
                    this.publish_detail_info = detail_info
                    this.detail_modal = true
                })
            },
        },
    }
</script>

<style scoped>

</style>
