<template>
  <Card>
    <Row>
      <tables stripe v-model="static_props" :columns="columns">

      </tables>
    </Row>
    <Modal
      v-model="detail_modal"
      title="详情"
      width="70%"
      :styles="{height: '60%'}"
      footer-hide>
      <Card>
        <Row v-html="publish_detail_info">
        </Row>
      </Card>
    </Modal>
  </Card>
</template>

<script>
    import store from "@/spider-store"
    import Tables from "@/components/tables";
    import {
        h5HdPublishApi,
        h5HdPublishStateApi,
        getErrNoticeShowTime,
        getInfoNoticeShowTime
    } from "@/spider-api/h5";

    export default {
        name: "h5-gray-pc",
        components: {
            Tables,
        },
        props: {
            //这里是table中的数据
            static_props: {
                type: Array
            },
            queryState: {
                type: Function,
                default: null
            },
            vue_this: {
                type: Object
            }
        },
        data() {
            return {
                publish_detail_info: "",
                detail_modal: false,
                columns: [
                    {title: "应用", key: "app_name"},
                    {title: "仓库", key: "git_path"},
                    {
                        title: "操作时间",
                        key: "operate_time",
                        //sortable: true,
                        render: (h, params) => {
                            let value = params.row.operate_time
                            if (value == '' || value == null) {
                            } else {
                                value = this.formatDate(new Date(params.row.operate_time), 'yyyy-MM-dd hh:mm:ss')
                            }
                            return h('div', value)
                        }
                    },
                    {title: "发布人", key: "username"},
                    {title: "申请状态", key: "apply_status_display",
                      render: (h, params) => {
                        let color = "#2db7f5";
                        if (params.row.apply_status && params.row.apply_status.indexOf("success") >= 0) {
                          color = "green";
                        } else if (params.row.apply_status && params.row.apply_status.indexOf("failure") >= 0) {
                          color = "red";
                        }

                        return h("Tooltip", {
                          props: {
                            placement: 'top',
                            content: params.row.apply_message,
                            "max-width": 500,
                            transfer: true
                          }
                        }, [
                          h("p",
                            {props: {}, style: {color: color}},
                            params.row.apply_status_display)
                        ]);
                      }
                    },
                    {title: "发布状态", key: "status_display",
                        render: (h, params) => {
                          let color = "#2db7f5";
                          if (params.row.status && params.row.status.indexOf("success") >= 0) {
                            color = "green";
                          } else if (params.row.status && params.row.status.indexOf("failure") >= 0) {
                            color = "red";
                          }
                          else if (params.row.status && params.row.status.indexOf("warning") >= 0) {
                          color = "#ff9900";
                          }
                          /*return h("div", [
                            h("p",
                              {props: {}, style: {color: color}},
                              params.row.status)
                          ]);*/
                          return h("Tooltip", {
                            props: {
                              placement: 'top',
                              content: params.row.message,
                              "max-width": 500,
                              transfer: true
                            }
                          }, [
                            h("p",
                              {props: {}, style: {color: color}},
                              params.row.status_display)
                          ]);
                        }
                    },
                    {
                        title: '操作',
                        key: 'action',
                        width: 200,
                        align: 'center',
                        render: (h, params) => {
                            return h('div', [
                                h('Button', {
                                    props: {
                                        type: 'default',
                                        size: 'small'
                                    },
                                    style: {
                                        marginRight: '5px'
                                    },
                                    on: {
                                        click: () => {
                                            let compile_array = []
                                            compile_array.push(params.row)
                                            this.publish(compile_array)
                                        }
                                    }
                                }, '发布'),
                                h('Button', {
                                    props: {
                                        type: 'default',
                                        size: 'small'
                                    },
                                    style: {
                                        marginRight: '5px'
                                    },
                                    on: {
                                        click: () => {
                                            this.showDetail(params.row.app_name)
                                        }
                                    }
                                }, '详情'),
                            ]);
                        }
                    },
                ],
            }
        },
        mounted() {
            this.init()
        },
        methods: {
            init() {
            },
            //发布
            publish(param) {

                for (let i = 0; i < param.length; i++) {
                    if(param[i].apply_status.indexOf('success') < 0){
                        this.$Notice.error({
                          title: param[i].app_name + '申请还未成功',
                          duration: getErrNoticeShowTime(),
                        });
                        return
                    }
                }

                let publish_list = []
                let publish_obj = {}
                console.log(param)
                for (let i = 0; i < param.length; i++) {
                    let app_name = param[i].app_name
                    let suite_name = param[i].suite_name

                    publish_obj = {
                        'suite_name': suite_name,
                        'op_type': 'code_update',
                        'app_name': app_name,
                        'iteration_id': store.state.iterationID
                    }
                    publish_list.push(publish_obj)
                }

                console.log(publish_list)
                if (publish_list.length > 0) {
                    h5HdPublishApi(publish_list).then(res => {
                        console.log(res)
                        if (res.status == '200') {
                            //成功
                            this.$Notice.success({
                                title: res.data.msg,
                                duration: getInfoNoticeShowTime(),
                            });

                            console.log(" will call parent method query status")
                            this.queryState(this.vue_this)
                        } else {
                            //失败
                            this.$Notice.error({
                                title: res.data.msg,
                                duration: getErrNoticeShowTime(),
                            });
                        }
                    })
                }
            },
            formatDate(date, fmt) {
                let o = {
                    'M+': date.getMonth() + 1, // 月份
                    'd+': date.getDate(), // 日
                    'h+': date.getHours(), // 小时
                    'm+': date.getMinutes(), // 分
                    's+': date.getSeconds(), // 秒
                    'S': date.getMilliseconds() // 毫秒
                }
                if (/(y+)/.test(fmt)) {
                    fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length))
                }
                for (var k in o) {
                    if (new RegExp('(' + k + ')').test(fmt)) {
                        fmt = fmt.replace(RegExp.$1, (RegExp.$1.length === 1) ? (o[k]) : (('00' + o[k]).substr(('' + o[k]).length)))
                    }
                }
                return fmt
            },
            showDetail(app_name) {
                //查看发布信息
                h5HdPublishStateApi(store.state.iterationID).then(res => {
                    console.log(res.data.data.operate_info)
                    let detail_info = "<div>";
                    for (let i in res.data.data.operate_info) {
                        let publish_info_data = res.data.data.operate_info[i]
                        if (publish_info_data.app_name === app_name) {
                          let result = publish_info_data.message
                          let ip = publish_info_data.ip
                          if (result != null) {
                            let htmlResult = "<p>ip:" + ip + "</p>" + "<div><pre>" + (result.replaceAll("\\n", "<br/>")) + "</pre></div>"
                            detail_info +=htmlResult
                          }
                        }
                    }

                    detail_info += "</div>";

                    this.publish_detail_info = detail_info
                    this.detail_modal = true
                })
            },
        },
    }
</script>

<style scoped>

</style>
