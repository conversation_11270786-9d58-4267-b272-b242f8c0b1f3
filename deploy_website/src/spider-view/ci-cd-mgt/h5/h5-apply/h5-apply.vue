<template>
  <div>
    <Card>
      <Row style="margin-top: 10px">
        <i-col style="margin: 10px;" span="2">
          <span style="display: inline-block;width:60px;text-align: right;">组：</span>
        </i-col>
        <Col span="3">
          <span style="margin: 10px; text-align: left; display: inline-block; ">{{ group }}</span>
        </Col>
        <i-col style="margin: 10px;text-align: right" span="2">
          <span style="text-align: right; display: inline-block;">分支版本：</span>
        </i-col>
        <Col span="8">
          <span style="margin: 10px;text-align: left; display: inline-block;">{{ branch_version }}</span>
        </Col>
        <i-col v-show="show_tag" style="margin: 10px;text-align: right" span="2">
          <span style="text-align: right; display: inline-block;">TAG版本：</span>
        </i-col>
         <i-col v-show="show_tag" style="margin: 10px" span="3">
          <span style="text-align: left; display: inline-block;">{{tag_name}}</span>
        </i-col>
        <i-col style="margin: 10px" span="7">
          <span style="text-align: left; display: inline-block;color: red">注：若以下应用的配置存放路径有改变，请及时告知</span>
        </i-col>
      </Row>
      <Row style="margin-top: 10px">
        <i-col style="margin: 10px;" span="2">
          <span style="display: inline-block;width:60px;text-align: right;">环境：</span>
        </i-col>
        <Col span="10">
          <Select v-model="environment" @on-change="myOnchange">
            <Option
              v-for="(item,index) in environment_list"
              :value="item.value"
              :key="index"
            >{{ item.label }}
            </Option>
          </Select>
        </Col>
        <Col style="margin-left: 20px" span="2">
          <Button
            ghost
            type="primary"
            style="text-align: left; display: inline-block; width:80px;margin-top:-1px"
            @click="modal_env_desc = true"
          >环境说明
          </Button>
        </Col>
      </Row>
      <Row style="margin-top: 10px">
        <i-col style="margin: 10px;" span="2">
          <span style="display: inline-block;width:60px;text-align: right;">确认人：</span>
        </i-col>
        <Col span="10">
          <Select v-model="proposer" filterable clearable @on-change="myOnchange" ref="selectProposer">
            <Option v-for="(item,index) in allFilterMails"
                    :value="item"
                    :label="item"
                    :key="index" >
              {{ item }} </Option>
          </Select>
        </Col>
        <Modal
        title="vph环境最近一次发布版本"
        width="800"
        v-model="last_version_table"
        :mask-closable="true"
      >
        <div>
          <Row>
            <Table
              :columns="last_version_columns"
              :data="last_version_table_data"
            >
            </Table>
          </Row>
        </div>

      </Modal>
      <Col style="margin-left: 20px" span="2">
          <Button
            ghost
            type="primary"
            style="text-align: left; display: inline-block; width:80px;margin-top:-1px"
            @click="get_env_h5_app_api_info(app_name,'vph')"
          >vph查询
          </Button>
        </Col>
      </Row>
      <Row style="margin-top: 10px">
        <i-col style="margin: 10px;" span="2">
          <span style="display: inline-block;width:60px;text-align: right;">抄送人：</span>
        </i-col>
        <Col span="10">
          <Select v-model="cc" multiple filterable @on-change="myOnchange">
            <Option v-for="(item,index) in allFilterMails"
                    :value="item"
                    :label="item"
                    :key="index" >
              {{ item }} </Option>
          </Select>
        </Col>
        <Col style="margin-left: 20px" span="2">
          <Button
            ghost
            type="primary"
            style="text-align: left; display: inline-block; width:60px;margin-top:-1px"
            @click="apply"
          >申请
          </Button>
        </Col>
        <Col style="margin-left: 20px" span="2">
          <Button
            ghost
            type="primary"
            style="text-align: left; display: inline-block; width:120px;margin-top:-1px"
            v-show="lastApplyButtonShow"
            @click="getLastApplyInfo"
          >最近一次申请详情
          </Button>
        </Col>
      </Row>
      <Row style="margin-top: 10px">
        <Col v-if="showFundParams" span="9" style="margin-right: 10px">
          <Card>
            <Row>
              <i-col style="margin: 10px;text-align: right" span="8">
                <span style="text-align: right; display: inline-block;">fund起始版本：</span>
              </i-col>
              <Col span="14">
                <!-- @on-change="myOnchange" -->
                <Select v-model="fund_start_ver" @on-change="start_version_change">
                  <Option v-if="environment != 'prod'"
                          v-for="(item,index) in fund_start_ver_list"
                          :value="item.value"
                          :key="index"
                  >{{ item.label }}
                  </Option>
                  <!-- disabled -->
                  <Option disabled v-if="environment == 'prod'"
                          v-for="(item,index) in fund_start_ver_list"
                          :value="item.value"
                          :key="index">
                    {{ item.label }}
                    <span v-if="item.value == '20.6.5'" style="float:right;color:#ccc">默认</span>
                  </Option>
                </Select>
              </Col>
            </Row>
            <Row>
              <i-col style="margin: 10px;text-align: right" span="8">
                <span style="text-align: right; display: inline-block;">fund结束版本：</span>
              </i-col>
              <Col span="14">
                <Input v-model="fund_end_ver" placeholder="请输入">
                </Input>
              </Col>
            </Row>
            <Row>
              <i-col style="margin: 10px;text-align: right" span="8">
                <span style="text-align: right; display: inline-block;">nf-fund版本：</span>
              </i-col>
              <Col span="14">
                <Select v-model="nf_fund_ver" @on-change="start_version_change" filterable>
                  <Option
                    v-for="(item,index) in nf_fund_ver_list"
                    :value="item.value"
                    :label="item.label"
                    :key="index"
                  >{{ item.label }}
                  </Option>
                </Select>
              </Col>
            </Row>
            <Row>
              <i-col style="margin: 10px;text-align: right" span="8">
                <span style="text-align: right; display: inline-block;">打包类型：</span>
              </i-col>
              <Col span="14">
                <Checkbox v-model="fund_is_silent" true-value="1" style="margin-top: 10px"
                >静默
                </Checkbox>
              </Col>
            </Row>
          </Card>
        </Col>
        <!-- 占位间隔 -->
        <!--<i-col v-if="showFundParams" style="margin: 10px;text-align: right" span="1">
          <span style="text-align: right; display: inline-block;"></span>
        </i-col>-->
        <Col v-if="showPiggyParams" span="9">
          <Card>
            <Row>
              <i-col style="margin: 10px;text-align: right" span="8">
                <span style="text-align: right; display: inline-block;">piggy起始版本：</span>
              </i-col>
              <Col span="14">
                <!-- @on-change="myOnchange" -->
                <Select v-model="piggy_start_ver" @on-change="start_version_change" >
                  <Option v-if="environment != 'prod'"
                          v-for="(item,index) in piggy_start_ver_list"
                          :value="item.value"
                          :key="index"
                  >{{ item.label }}
                  </Option>
                  <!-- disabled -->
                  <Option disabled v-if="environment == 'prod'"
                          v-for="(item,index) in piggy_start_ver_list"
                          :value="item.value"
                          :key="index"
                  > {{ item.label }}
                    <span v-if="item.value == '3.5.1'" style="float:right;color:#ccc">默认</span>
                  </Option>
                </Select>
              </Col>
            </Row>
            <Row>
              <i-col style="margin: 10px;text-align: right" span="8">
                <span style="text-align: right; display: inline-block;">piggy结束版本：</span>
              </i-col>
              <Col span="14">
                <Input v-model="piggy_end_ver" placeholder="请输入">
                </Input>
              </Col>
            </Row>
            <Row>
              <i-col style="margin: 10px;text-align: right" span="8">
                <span style="text-align: right; display: inline-block;">nf-piggy版本：</span>
              </i-col>
              <Col span="14">
                <Select v-model="nf_piggy_ver" @on-change="start_version_change" filterable>
                  <Option
                    v-for="(item,index) in nf_piggy_ver_list"
                    :value="item.value"
                    :label="item.label"
                    :key="index"
                  >{{ item.label }}
                  </Option>
                </Select>
              </Col>
            </Row>
            <Row>
              <i-col style="margin: 10px;text-align: right" span="8">
                <span style="text-align: right; display: inline-block;">打包类型：</span>
              </i-col>
              <Col span="14">
                <Checkbox v-model="piggy_is_silent" true-value="1" style="margin-top: 10px"
                >静默
                </Checkbox>
              </Col>
            </Row>
          </Card>
        </Col>
        <!--掌上基金参数存放位置-->
        <Col v-if="showFundAppParams" span="9" style="margin-right: 10px">
          <Card>
            <Row><tag>掌上基金</tag></Row>
            <Row>
              <i-col style="margin: 10px;text-align: right" span="8">
                <span style="text-align: right; display: inline-block;">App版本名称：</span>
              </i-col>
              <Col span="11">
                <Input v-model="fundAppParamForm.appVersion" placeholder="请输入App版本名称" clearable></Input>
              </Col>
            </Row>
            <Row style="margin-top: 0px">
              <i-col style="margin: 10px;text-align: right" span="8">
                <span style="text-align: right; display: inline-block;">H5资源包环境：</span>
              </i-col>
              <Col span="11">
                <Select v-model="fundAppParamForm.h5Env" @on-change="selectedZipByEnv('fund',fundAppParamForm.h5Env,true)"
                        placeholder="请选择H5资源包对应环境">
                  <Option  v-for="(item,index) in environment_list" :value="item.value"  :key="index" >{{ item.label }}</Option>
                </Select>
              </Col>
            </Row>
            <Row style="margin-top: 0px">
              <i-col style="margin: 10px;text-align: right" span="8">
                <span style="text-align: right; display: inline-block;">H5资源包版本：</span>
              </i-col>
              <Col span="11">
                <Select v-model="fundAppParamForm.h5ZipVersion" filterable ref="fundH5ZipQuery"
                        placeholder="请选择当前环境下的H5资源包版本">
                  <Option v-for="(item,index) in h5FundZipVersionList"
                          :value="item.value"
                          :label="item.label"
                          :key="index">
                    {{ item.label }}</Option>
                </Select>
              </Col>
            </Row>
          </Card>
        </Col>
        <!--app储蓄罐参数存放位置-->
        <Col v-if="showPiggyAppParams" span="9">
          <Card>
            <Row><tag>储蓄罐</tag></Row>
            <Row>
              <i-col style="margin: 10px;text-align: right" span="8">
                <span style="text-align: right; display: inline-block;">App版本名称：</span>
              </i-col>
              <Col span="11">
                <Input v-model="piggyAppParamForm.appVersion" placeholder="请输入App版本名称" clearable></Input>
                <!--<label>最后一次版本名称：{{oldVersion}}</label>-->
              </Col>
            </Row>
            <Row style="margin-top: 0px">
              <i-col style="margin: 10px;text-align: right" span="8">
                <span style="text-align: right; display: inline-block;">H5资源包环境：</span>
              </i-col>
              <Col span="11">
                <Select v-model="piggyAppParamForm.h5Env" @on-change="selectedZipByEnv('piggy',piggyAppParamForm.h5Env,true)"
                        placeholder="请选择H5资源包对应环境">
                  <Option  v-for="(item,index) in environment_list" :value="item.value"  :key="index" >{{ item.label }}</Option>
                </Select>
              </Col>
            </Row>
            <Row style="margin-top: 0px">
              <i-col style="margin: 10px;text-align: right" span="8">
                <span style="text-align: right; display: inline-block;">H5资源包版本：</span>
              </i-col>
              <Col span="11">
                <Select v-model="piggyAppParamForm.h5ZipVersion" filterable ref="piggyH5ZipQuery"
                        placeholder="请选择当前环境下的H5资源包版本">
                  <Option v-for="(item,index) in h5PiggyZipVersionList"
                          :value="item.value"
                          :label="item.label"
                          :key="index">
                    {{ item.label }}</Option>
                </Select>
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>
      <Row style="margin-top: 10px"></Row>
      <Row style="margin-top: 10px">
        <tables stripe v-model="app_array" :columns="columns"
                @on-selection-change="apply_list_select">
        </tables>
      </Row>

      <Modal
        title="以下为正在进行灰度的分支，若无影响可继续申请！"
        width="800"
        v-model="hd_conflict_confirm"
        :mask-closable="true"
      >
        <div>
          <Row>
            <Table
              :columns="conflictTableColumns"
              :data="conflictTableData"
            >
            </Table>
          </Row>
        </div>
        <div slot="footer">
          <Button @click="hdApplyCheck">继续申请</Button>
          <Button @click="closeConfirmModal">关闭</Button>
        </div>
      </Modal>

      <Modal
        title="灰度申请检查没有通过，请处理完毕后再行申请！！！"
        width="800"
        v-model="hd_check_confirm"
        :mask-closable="true"
      >
        <div>
          <Row>

          </Row>
          <Row>
            <Table
              :columns="hdCheckTableColumns"
              :data="hdCheckTableData"
            >
            </Table>
          </Row>
        </div>
        <div slot="footer">
          <!--<Button @click="h5PublishApply">继续申请</Button>-->
          <Button @click="closeConfirmModal">关闭</Button>
        </div>
      </Modal>

      <Modal
        title="app申请检查没有通过，请处理完毕后再行申请！！！"
        width="800"
        v-model="app_check_confirm"
        :mask-closable="true"
      >
        <div>
          <Row>

          </Row>
          <Row>
            <Table
              :columns="appCheckTableColumns"
              :data="appCheckTableData"
            >
            </Table>
          </Row>
        </div>
        <div slot="footer">
          <!--<Button @click="h5PublishApply">继续申请</Button>-->
          <Button @click="closeConfirmModal">关闭</Button>
        </div>
      </Modal>
      <Modal
        v-model="modal_env_desc"
        title="环境说明">
        <template>
            <Table border :columns="columns1" :data="data1"></Table>
        </template>
      </Modal>

      <Modal
        v-model="modal_last_apply_info"
        title="最近一次申请详情">
        <div v-if="lastApplyInfoStatus=='true'">
          <ButtonGroup>
            <Button v-for="(joburl,index) in lastApplyInfoReturnMessage" @click="openJenkinsUrl(joburl)" type="text" >
              {{joburl}}
            </Button>
            </ButtonGroup>
          </div>
        <div v-else>
          <p>{{ lastApplyInfoReturnMessage}}</p>
        </div>
      </Modal>
    <Modal
        v-model="alert"
        title="以下应用正在申请，是否要继续申请？"
        okText = '继续申请'
        cancelText = '取消申请'
        :columns="alerttablecolums"
        :tableData="alertmodal"

        @on-ok="continue_apply">
        <div>
          <Row>
            <Table
              :columns="alerttablecolums"
              :data="alertmodal"
            >
            </Table>
          </Row>
        </div>

    </Modal>
    </Card>
  </div>
</template>

<script>
import Tables from '@/components/tables'
import store from '@/spider-store'
import {
  AppCommitCheckApi,
  AppMergeCheckApi
} from '@/spider-api/app-check'
import {
  h5IterConfirmStatusApi
} from '@/spider-api/publish'
import {
  getPlanInfo,
  h5ProApplyNotice,
  getEmailAddresses
} from '@/spider-api/iter-plan'
// import {getEmailAddresses} from "@/api/iterative-plan";
import {
  externalServiceResult,
  h5PublishApplyApiGet,
  h5PublishApplyApi,
  appPublishApplyApi,
  appTagPublishApplyApi,
  testPublishCheckApi,
  userAction,
  userActionGet,
  HdStatusCheckApi,
  getLastApplyInfoApi,
  findDistVersion,
  findH5ZipVersionByEnv,
  getErrNoticeShowTime,
  getSuccessNoticeShowTime,
  getInfoNoticeShowTime,
  CheckH5ZipVersionWhetherPublish,
  get_env_h5_app_api
} from '@/spider-api/h5'
import {
  formatDate
} from '@/spider-api/h5-common'

export default {
  name: 'h5-apply',
  components: {
    Tables
  },
  data () {
    return {
      alert: true,
      alertmodal: [],
      app_name: '',
      last_version_table_dict: '',
      last_version_table: false,
      last_version_table_data: [],
      lastApplyInfoStatus: '',
      lastApplyInfoReturnMessage: '',
      lastApplyButtonShow: false,
      modal_last_apply_info: false,
      modal_env_desc: false,
      showPiggyParams: false,
      showFundParams: false,
      hd_check_confirm: false,
      app_check_confirm: false,
      hd_conflict_confirm: false,
      showPiggyAppParams: false,
      showFundAppParams: false,
      actionItem: 'publish_apply',
      tag_name: '',
      show_tag: false,
      timeout: '',
      group: '',
      branch_version: '',
      environment: '',
      hd_conflict_msg: '',
      conflictTableData: [],
      conflictTableColumns: [
        {
          title: '灰度环境的分支',
          key: 'br_name',
          align: 'center'
        },
        {
          title: '灰度环境的应用',
          key: 'app_name',
          align: 'center'
        }
      ],
      hdCheckTableData: [],
      hdCheckTableColumns: [
        {
          title: '检查项',
          key: 'msg',
          align: 'center'
        },
        {
          title: '检查是否通过',
          key: 'status',
          align: 'center',
          render: (h, params) => {
            if (params.row.status.indexOf('success') >= 0) {
              var color = 'green'
            } else if (params.row.status.indexOf('failure') >= 0) {
              var color = 'red'
            }
            return h('div', [
              h('p',
                { props: {}, style: { color: color } },
                params.row.status)
            ])
          }
        }
      ],
      appCheckTableData: [],
      appCheckTableColumns: [
        {
          title: '检查项',
          key: 'msg',
          align: 'center'
        },
        {
          title: '检查是否通过',
          key: 'status',
          align: 'center',
          render: (h, params) => {
            if (params.row.status.indexOf('success') >= 0) {
              var color = 'green'
            } else if (params.row.status.indexOf('failure') >= 0) {
              var color = 'red'
            }
            return h('div', [
              h('p',
                { props: {}, style: { color: color } },
                params.row.status)
            ])
          }
        }
      ],
      environment_list: [
        { value: 'vph', label: 'vph--灰度环境，验证灰度H5远端' },
        { value: 'vps', label: 'vps--灰度环境，验证服务端灰度' },
        { value: 'pre', label: 'pre--灰度环境，和产线配置一致' },
        { value: 'hd', label: 'hd--灰度环境，和产线配置一致' },
        { value: 'prod', label: 'prod--生产环境' }
      ],
      fund_start_ver: '',
      fund_start_ver_list: [
        /* {value: '20.6.5', label: '20.6.5'},
        {value: '29.9.9', label: '29.9.9'},
        {value: '30.0.0', label: '30.0.0'}, */
      ],
      fund_end_ver: '',
      fund_end_ver_list: [],
      nf_fund_ver: '',
      nf_fund_ver_list: [],
      piggy_start_ver: '',
      piggy_start_ver_list: [
        /* {value: '2.6.1', label: '2.6.1'},
        {value: '9.9.8', label: '9.9.8'},
        {value: '9.9.9', label: '9.9.9'}, */
      ],
      piggy_end_ver: '',
      piggy_end_ver_list: [],
      nf_piggy_ver: '',
      nf_piggy_ver_list: [],
      proposer: [],
      allFilterMails: [],
      cc: [],
      add_app: [],
      // app_array 申请页面的数据整合，大家都用它
      app_array: [],
      selected_array: [],
      compile_array: [],
      // 不勾选，默认非静默
      fund_is_silent: '0',
      // 不勾选，默认非静默
      piggy_is_silent: '0',
      columns: [
        {
          type: 'selection',
          minWidth: 50
        },
        { title: '应用', minWidth: 100, key: 'app_name' },
        { title: '仓库', minWidth: 160, key: 'git_path' },
        {
          title: '上次申请环境',
          minWidth: 110,
          key: 'suite_name',
          render: (h, params) => {
            let value = params.row.suite_name
            if (params.row.suite_name == null) {
              return h('div', '无')
            } else if (params.row.suite_name != null && params.row.suite_name.length == 0) {
              return h('div', '无')
            } else {
              let str = ''
              for (let i of value) {
                str += i
              }
              return h('div', str)
            }
          }
        },
        {
          title: '申请时间',
          minWidth: 150,
          key: 'operate_time',
          // sortable: true,
          render: (h, params) => {
            let value = params.row.operate_time
            if (value) {
              value = formatDate(new Date(params.row.operate_time), 'yyyy-MM-dd hh:mm:ss')
            }
            return h('div', value)
          }
        },
        { title: '操作人', minWidth: 100, key: 'username' },
        {
          title: '申请状态',
          minWidth: 100,
          key: 'status_display',
          render: (h, params) => {
            let color = '#2db7f5'
            if (params.row.status && params.row.status.indexOf('success') >= 0) {
              color = 'green'
            } else if (params.row.status && params.row.status.indexOf('failure') >= 0) {
              color = 'red'
            }
            if (params.row.status && params.row.status.indexOf('running') >= 0) {
              return h('div',
                [h('a', {
                  on: {
                    click: () => {
                      let job_url = params.row.job_url
                      if (job_url) {
                        window.open(job_url)
                      } else {
                        this.$Notice.info({
                          title: '暂无详情',
                          duration: getInfoNoticeShowTime()
                        })
                      }
                    }
                  }
                }, params.row.status_display)
                ])
            } else {
              return h('Tooltip', {
                props: {
                  placement: 'top',
                  content: params.row.message,
                  'max-width': 500,
                  transfer: true
                }
              }, [h('p',
                { props: {}, style: { color: color } },
                params.row.status_display)]
              )
            }
          }
        },
        { title: '确认状态', minWidth: 100, key: 'email_status_display' },
        {
          title: '确认时间',
          minWidth: 150,
          key: 'affirm_time',
          render: (h, params) => {
            let value = params.row.affirm_time
            if (value == '' || value == null) {

            } else {
              value = formatDate(new Date(params.row.affirm_time), 'yyyy-MM-dd hh:mm:ss')
            }
            return h('div', value)
          }
        },
        { title: '确认人', minWidth: 100, key: 'assertor' },
        { title: '详情', minWidth: 100, key: 'code_message' }
      ],
      piggyAppParamForm: {},
      fundAppParamForm: {},
      h5FundZipVersionList: [],
      h5PiggyZipVersionList: [],
      columns1: [
        {
          title: '环境',
          key: 'name'
        },
        {
          title: '使用场景',
          key: 'scenario',
          width: 250
        },
        {
          title: '后端直连服务',
          key: 'server'
        }
      ],
      data1: [
        {
          name: 'vph',
          scenario: 'h5和客户端打灰度包时常用环境,配置标识为灰度配置',
          server: '生产'
        },
        {
          name: 'vps',
          scenario: 'h5和客户端打灰度包，指定后端服务，比如CGI是灰度环境时使用',
          server: '灰度'
        },
        {
          name: 'pre',
          scenario: 'h5和客户端打灰度包，配置标识为产线配置，一般是打生产包之前的最后一次验证',
          server: '生产'
        },
        {
          name: 'wgq-hd',
          scenario: '静态资源(非H5和客户端)，比如：otc-web-static做灰度验证时使用',
          server: '生产'
        },
        {
          name: 'prod',
          scenario: '打生产包时使用',
          server: '生产'
        }
      ],
      last_version_columns: [
        {
          title: '应用',
          key: 'app_name'
        },
        {
          title: 'h5版本',
          key: 'br_name'
        }
      ],
      alerttablecolums: [
        {
          title: '应用',
          key: 'app_name'
        },
        {
          title: '状态',
          key: 'status_display',
          render: (h, params) => {
            return h('div', [
              h('p',
                { props: {}, style: { color: 'red' } },
                params.row.status_display)
            ])
          }
        }
      ]
    }
  },
  mounted () {
    // this.init()
  },
  computed: {
  },
  methods: {
    // 初始化所有的数据，table，并且回显用户行为数据
    init () {
      this.group = store.state.h5_group
      this.branch_version = store.state.h5_branch_version
      this.tag_name = store.state.tag_name
      this.show_tag = store.state.show_tag
      if (store.state.code_type == 'tag') {
        this.environment_list = [
          { value: 'vph', label: 'vph--灰度环境，验证灰度H5远端' },
          { value: 'vps', label: 'vps--灰度环境，验证服务端灰度' },
          { value: 'pre', label: 'pre--灰度环境，和产线配置一致' },
          { value: 'wgq-hd', label: 'wgq-hd--外高桥灰度环境，和产线配置一致' }
        ]
      }
      getEmailAddresses().then(res => {
        if (res.data.code === 0) {
          this.allFilterMails = res.data.data
        }
      })

      // 查询用户行为  todo 后期整合成一个逻辑，去除不必要的代码
      if (this.group.indexOf('android') != -1 || this.group.indexOf('ios') != -1) {
        this.actionItem = 'publish_apply'
      } else {
        this.actionItem = store.state.iterationID + '_' + 'apply'
      }

      let json = []
      let _this = this
      userActionGet(this.actionItem).then(res => {
        if (res.data.data.length > 0) {
          json = JSON.parse(res.data.data.replace(/'/g, '"'))
          if (json instanceof Array) {
            // todo 强烈建议数据一致处理，目前出现行为字段不一致的情况
            if (this.actionItem == 'publish_apply') {
              this.environment = json[0].suite_code
            } else {
              this.environment = json[0].environment
            }
            this.proposer = json[0].proposer
            this.$refs.selectProposer.query = json[0].proposer
            this.cc = json[0].cc
          } else {
            this.environment = json.appEnv
            this.proposer = json.proposer
            this.$refs.selectProposer.query = json.proposer
            this.cc = json.cc
          }
          if (this.group.indexOf('android') != -1 || this.group.indexOf('ios') != -1) {
            // alert(JSON.stringify(jsonArray))
            // 第一次进来，给table赋值用户行为信息,查询迭代信息
            if (this.environment) {
              h5PublishApplyApiGet(store.state.iterationID, _this.environment, _this.branch_version).then(res => {
                _this.app_array = res.data.data.ci_info
                _this.initRowParams(json.requestParams, _this)
              })
            }
          } else {
            let version_param = {
              'iteration_id': store.state.iterationID,
              'br_name': this.branch_version,
              'suite_code': this.environment
            }
            // fund & piggy 起始版本回填
            findDistVersion(version_param).then(res => {
              this.fund_start_ver_list = res.data.data.fund_begin_ver_list
              this.piggy_start_ver_list = res.data.data.piggy_begin_ver_list
              this.nf_fund_ver_list = res.data.data.nf_fund_ver_list
              this.nf_piggy_ver_list = res.data.data.nf_piggy_ver_list
              // start_ver影响end_ver
              if (this.environment == 'prod') {
                console.log('====== prod ==========')
                console.log(this.fund_start_ver_list)
                // 记得放开
                this.fund_start_ver = '21.2.0'
                this.piggy_start_ver = '2.6.1'
                if (this.fund_start_ver_list.length > 0) {
                  // 找到最新的一条记录
                  let fundTemp = this.fund_start_ver_list[0].value
                  let fundTempArr = fundTemp.split('.')
                  this.fund_end_ver = this.add_one_upper(fundTempArr)
                }
                if (this.piggy_start_ver_list.length > 0) {
                  // 找到最新的一条记录
                  let piggyTemp = this.piggy_start_ver_list[0].value
                  let piggyTempArr = piggyTemp.split('.')
                  this.piggy_end_ver = this.add_one_upper(piggyTempArr)
                }
              }
            }).catch(err => {
              this.$Message.error(err)
            })
            // 第一次进来，给table赋值用户行为信息
            h5PublishApplyApiGet(store.state.iterationID, this.environment, this.branch_version).then(res => {
              // 我在这里拼装 app_array
              this.app_array = res.data.data.ci_info
              this.init_with_app_array()
            })
          }
        } else {
          h5PublishApplyApiGet(store.state.iterationID, this.environment, this.branch_version).then(res => {
            this.app_array = res.data.data.ci_info
          })
        }
        this.waitPolling()
      })
    },
    get_env_h5_app_api_info () {
      var appName = new Array()
      this.last_version_table = this.app_array
      for (let i of this.app_array) {
        console.log('111111111111111')
        console.log(i.app_name)
        appName.push(i.app_name)
      }
      this.env = 'vph'
      get_env_h5_app_api(appName, this.env).then(res => {
        console.log('=====test=================')
        this.last_version_table_data = res.data.data.last_version_table_data
        console.log(this.last_version_table_data)
        console.log('=====test=================')
      })
    },
    /**
     * h5模块
     * */
    // dist的初始版本变化事件
    start_version_change () {
      if (this.environment != 'prod') {
        this.fund_end_ver = this.fund_start_ver
        this.piggy_end_ver = this.piggy_start_ver
      }
    },
    // 检查H5入参和初始化
    checkParamForH5 () {
      // 有fund应用时的必填校验
      if (this.showFundParams) {
        if (!this.fund_start_ver) {
          this.$Notice.error({
            desc: '请输入fund起始版本',
            duration: getErrNoticeShowTime()
          })
          return false
        }
        if (!this.fund_end_ver) {
          this.$Notice.error({
            desc: '请输入fund结束版本',
            duration: getErrNoticeShowTime()
          })
          return false
        }
        if (this.environment == 'prod') {
          if (this.fund_start_ver_list[0].value >= this.fund_end_ver) {
            this.$Notice.error({
              desc: 'fund结束版本应大于起始版本',
              duration: getErrNoticeShowTime()
            })
            return false
          }
          if (!this.nf_fund_ver) {
            this.$Notice.info({
              desc: '生产环境必须选择nf-fund版本',
              duration: getErrNoticeShowTime()
            })
            return false
          }
        }
        if (!this.nf_fund_ver) { // 若没填值则直接拿已归档版本
          for (let i of this.nf_fund_ver_list) {
            if (i.label.indexOf('(已归档)') != -1) {
              this.nf_fund_ver = i.value
              break
            }
          }
        }
      }
      // 有piggy应用时的必填校验
      if (this.showPiggyParams) {
        if (!this.piggy_start_ver) {
          this.$Notice.error({
            desc: '请输入piggy起始版本',
            duration: getErrNoticeShowTime()
          })
          return false
        }
        if (!this.piggy_end_ver) {
          this.$Notice.error({
            desc: '请输入piggy结束版本',
            duration: getErrNoticeShowTime()
          })
          return false
        }
        if (this.environment == 'prod') {
          if (this.piggy_start_ver_list[0].value >= this.piggy_end_ver) {
            this.$Notice.error({
              desc: 'piggy结束版本应大于起始版本',
              duration: getErrNoticeShowTime()
            })
            return false
          }
          if (!this.nf_piggy_ver) {
            this.$Notice.info({
              desc: '生产环境必须选择nf-piggy版本',
              duration: getErrNoticeShowTime()
            })
            return false
          }
        }
        if (!this.nf_piggy_ver) { // 若没填值则直接拿已归档版本
          for (let i of this.nf_piggy_ver_list) {
            if (i.label.indexOf('(已归档)') != -1) {
              this.nf_piggy_ver = i.value
              break
            }
          }
        }
      }
      return true
    },
    // 获取H5请求参数
    getH5ApplyParam () {
      let iterationID = store.state.iterationID
      let apply_list = []
      let apply_obj = {}
      // piggy 和 fund 因为需要 起始终止版本，是否静默，所以单独拿出来二次组装
      // cc 如果没有**************，添加 ["<EMAIL>", "<EMAIL>", __ob__: Observer]
      // todo 建议前后端参数调整，所有类型的参数都在一个维度封装了
      this.cc = this.add_default_cc(this.cc)
      for (let i = 0; i < this.selected_array.length; i++) {
        if (this.selected_array[i].app_name == 'piggy') {
          apply_obj = {
            'suite_code': this.environment,
            'begin_ver': this.piggy_start_ver,
            'end_ver': this.piggy_end_ver,
            'iteration_id': iterationID,
            'app_name': this.selected_array[i].app_name,
            'is_silent': this.piggy_is_silent,
            'cc': this.cc,
            'br_name': this.branch_version,
            'nf_app_name': 'nf-piggy',
            'nf_br_name': this.nf_piggy_ver
          }
          apply_list.push(apply_obj)
        } else if (this.selected_array[i].app_name == 'fund') {
          apply_obj = {
            'suite_code': this.environment,
            'begin_ver': this.fund_start_ver,
            'end_ver': this.fund_end_ver,
            'iteration_id': iterationID,
            'app_name': this.selected_array[i].app_name,
            'is_silent': this.fund_is_silent,
            'cc': this.cc,
            'br_name': this.branch_version,
            'nf_app_name': 'nf-fund',
            'nf_br_name': this.nf_fund_ver
          }
          apply_list.push(apply_obj)
        } else if (this.selected_array[i].app_name == 'fund-ios' || this.selected_array[i].app_name == 'fund-android') {
          // todo  添加app入参数据逻辑  临时解决方案   ，目前参数已经传入后台，但是未进行任务调用处理
          apply_obj = {
            'suite_code': this.environment,
            'iteration_id': iterationID,
            'br_name': this.branch_version,
            'proposer': this.proposer,
            'cc': this.cc,
            'app_version_name': this.appVersionName,
            'h5_zip_env': this.h5ZipEnv,
            'h5_zip_version': this.h5ZipVersion,
            'app_name': this.selected_array[i].app_name,
            'begin_ver': '',
            'end_ver': ''
          }
          apply_list.push(apply_obj)
        } else {
          apply_obj = {
            'suite_code': this.environment,
            'begin_ver': '',
            'end_ver': '',
            'iteration_id': iterationID,
            'app_name': this.selected_array[i].app_name,
            'proposer': this.proposer,
            'cc': this.cc,
            'is_silent': '1',
            'br_name': this.branch_version
          }
          apply_list.push(apply_obj)
        }
      }
      return apply_list
    },
    /**
     * mobile app模块
     * */
    // app选择h5zip环境查询对应环境下的h5zip版本数据
    selectedZipByEnv (h5AppName, env, type) {
      let _this = this
      findH5ZipVersionByEnv(h5AppName, env).then(res => {
        if (res.data.data) {
          if (h5AppName == 'fund') {
            if (type) {
              _this.fundAppParamForm.h5ZipVersion = ''
              // _this.$refs.fundH5ZipQuery.query = null
            }
            _this.h5FundZipVersionList = res.data.data
          } else if (h5AppName == 'piggy') {
            if (type) {
              _this.piggyAppParamForm.h5ZipVersion = ''
              // _this.$refs.piggyH5ZipQuery.query = null
            }
            _this.h5PiggyZipVersionList = res.data.data
          }
        }
      })
      // todo 暂时不存   选择环境后存入useraction
      // this.myOnchange()
    },
    // app的应用的必填校验
    checkParamForApp () {
      if (this.showFundAppParams) {
        if (!this.fundAppParamForm.appVersion) {
          this.$Notice.error({
            desc: 'app版本名称不能为空[掌上基金]',
            duration: getErrNoticeShowTime()
          })
          return false
        }
        if (!this.fundAppParamForm.h5Env) {
          this.$Notice.error({
            desc: 'H5资源包环境不能为空[掌上基金]',
            duration: getErrNoticeShowTime()
          })
          return false
        }
        if (!this.fundAppParamForm.h5ZipVersion) {
          this.$Notice.error({
            desc: 'H5资源包版本不能为空[掌上基金]',
            duration: getErrNoticeShowTime()
          })
          return false
        }
      }
      if (this.showPiggyAppParams) {
        if (!this.piggyAppParamForm.appVersion) {
          this.$Notice.error({
            desc: 'app版本名称不能为空[储蓄罐]',
            duration: getErrNoticeShowTime()
          })
          return false
        }
        if (!this.piggyAppParamForm.h5Env) {
          this.$Notice.error({
            desc: 'H5资源包环境不能为空[储蓄罐]',
            duration: getErrNoticeShowTime()
          })
          return false
        }
        if (!this.piggyAppParamForm.h5ZipVersion) {
          this.$Notice.error({
            desc: 'H5资源包版本不能为空[储蓄罐]',
            duration: getErrNoticeShowTime()
          })
          return false
        }
      }
      return true
    },
    // 获取app客户端参数
    getAppApplyParam () {
      let requestParams = []
      let requestParam = {}
      requestParam.cc = this.cc
      if (requestParam.cc.findIndex(item => item == '<EMAIL>') < 0) {
        requestParam.cc.push('<EMAIL>')
      }
      requestParam.proposer = this.proposer
      requestParam.appEnv = this.environment
      requestParam.appBranch = this.branch_version
      requestParam.iterationID = store.state.iterationID
      requestParam.actionItem = this.actionItem
      let appNameList = []
      for (let row of this.selected_array) {
        if (row.app_name.indexOf('fund') > -1) {
          appNameList.push(row.app_name)
          this.fundAppParamForm.appName = row.app_name
          this.fundAppParamForm.h5AppName = 'fund'
          this.fundAppParamForm.packageType = row.package_type
          this.fundAppParamForm.repoPath = row.git_path
          this.fundAppParamForm.appEnv = this.environment
          this.fundAppParamForm.appBranch = this.branch_version
          this.fundAppParamForm.actionItem = this.actionItem
          this.fundAppParamForm.iterationID = store.state.iterationID
          this.fundAppParamForm.appTagName = store.state.tag_name
          this.fundAppParamForm.appCodeType = store.state.code_type
          requestParams.push(this.fundAppParamForm)
        }
        if (row.app_name.indexOf('piggy') > -1) {
          appNameList.push(row.app_name)
          this.piggyAppParamForm.appName = row.app_name
          this.piggyAppParamForm.h5AppName = 'piggy'
          this.piggyAppParamForm.packageType = row.package_type
          this.piggyAppParamForm.repoPath = row.git_path
          this.piggyAppParamForm.appEnv = this.environment
          this.piggyAppParamForm.appBranch = this.branch_version
          this.piggyAppParamForm.actionItem = this.actionItem
          this.piggyAppParamForm.iterationID = store.state.iterationID
          this.piggyAppParamForm.appTagName = store.state.tag_name
          this.piggyAppParamForm.appCodeType = store.state.code_type
          requestParams.push(this.piggyAppParamForm)
        }
      }
      requestParam.appNameList = appNameList
      requestParam.requestParams = requestParams
      return requestParam
    },
    // 用户行为记录
    addUserAction (actionItem, actionValue) {
      return new Promise(function (resolve, reject) {
        let req = {
          action_item: actionItem,
          action_value: actionValue
        }
        userAction(req).then(res => {
          resolve(res.data.data)
        }).catch(err => {
          reject(false)
        })
      })
    },
    // 根据用户行为回显模块入参
    initRowParams (params, _this) {
      _this.selected_array = []
      _this.showPiggyAppParams = false
      _this.showFundAppParams = false
      _this.showFundParams = false
      _this.showPiggyParams = false

      if (!params) {
        return
      }
      for (let row of params) {
        let index = _this.app_array.findIndex(item => item.app_name == row.appName)
        if (index != -1) {
          _this.showRowBySelected(this.app_array[index])
          _this.app_array[index]._checked = true
          _this.selected_array.push(this.app_array[index])
          if (row.appName.indexOf('fund') > -1) {
            _this.fundAppParamForm = row
            _this.showFundAppParams = true
            _this.selectedZipByEnv('fund', row.h5Env, false)
          }
          if (row.appName.indexOf('piggy') > -1) {
            _this.piggyAppParamForm = row
            _this.showPiggyAppParams = true
            _this.selectedZipByEnv('piggy', row.h5Env, false)
          }
        }
      }
    },
    /**
     * 公共模块
     * */
    // 轮询状态
    waitPolling () {
      console.debug('will wait')
      let _this = this
      setTimeout(function () {
        _this.pollingStatus()
      }, 2000)
    },
    /**
     * 执行轮询 2s，修改目前的发布状态
     */
    pollingStatus () {
      let needPool = false
      for (let s of this.app_array) {
        if (s.status != null && s.status.indexOf('running') >= 0) {
          needPool = true
          break
        }
      }
      let vm = this
      // 开始新的查询
      if (needPool) {
        console.log(' apply query status')
        h5PublishApplyApiGet(store.state.iterationID, this.environment, this.branch_version).then(res => {
          console.debug(res.data.data)
          // vm.app_array = res.data.data.ci_info
          for (let app of vm.app_array) {
            for (let ci of res.data.data.ci_info) {
              if (app.app_name == ci.app_name) {
                app.suite_code = ci.suite_code
                app.operate_time = ci.operate_time
                app.username = ci.username
                app.status_display = ci.status_display
                app.message = ci.message
                app.status = ci.status
              }
            }
          }
          // vm.init_with_app_array()
          vm.waitPolling()
        })
      }
    },

    closeConfirmModal () {
      this.hd_conflict_confirm = false
      this.hd_check_confirm = false
      this.app_check_confirm = false
    },

    openJenkinsUrl (joburl) {
      window.open(joburl)
    },

    // 发送领导确认邮件
    sendAffirmEmail () {
      h5IterConfirmStatusApi(store.state.iterationID, this.environment).then(res => {
        if (res.data.status != 'success') {
          let app_name_list = []
          for (let i of this.selected_array) {
            app_name_list.push(i.app_name)
          }
          // 先将发布邮件信息入库，以控制生产发布允许进入
          console.log('========== email-start =========')
          if (!this.showFundParams) {
            this.fund_end_ver = ''
          }
          if (!this.showPiggyParams) {
            this.piggy_end_ver = ''
          }
          // console.log(this.fund_end_ver)
          // console.log(this.piggy_end_ver)
          h5ProApplyNotice(store.state.iterationID, this.proposer, 'http://' + window.location.host,
            this.environment, app_name_list, this.fund_end_ver, this.piggy_end_ver)
            .then(result => {
              if (result.data.status === 'success') {
                this.$Notice.success({
                  desc: result.data.msg,
                  duration: getInfoNoticeShowTime()
                })
              } else {
                this.$Spin.hide()
                this.$Message.error(result.data.msg)
              }
            })
            .catch(err => {
              this.$Spin.hide()
              this.$Message.error(err.response.data.msg)
            })
        }
      })
    },

    /**
     * 点击【申请】按钮，页面数据校验检查
     */
    apply () {
      if (this.environment == 'prod') {
        console.log('app_name')
        console.log(this.selected_array)
        for (let a of this.selected_array) {
          console.log(a['app_name'])
          console.log(a)
          if (a['app_name'] == 'fund') {
            console.log('应用存在')
            let params = { 'app_name': a['app_name'], 'suite_code': this.environment, 'end_ver': this.fund_end_ver }
            console.log(params)
            // this.params['app_name'] = a['app_name']
            // this.params['suite_code'] = a['suite_code']
            // this.params['suite_code'] = a['end_ver']
            CheckH5ZipVersionWhetherPublish(params).then(res => {
              if (res.data.msg == '版本重复') {
                console.log(res.data.data[0]['datetime'])
                alert('该版本' + res.data.data[0]['datetime'] + '已申请过')
                this.$Notice.error({ desc: '发布的版本重复' })
              }
            })
          }
        }
      }
      if (this.selected_array.length == 0) {
        this.$Notice.error({
          desc: '请选择申请应用',
          duration: getErrNoticeShowTime()
        })
        return
      }
      let today = new Date()
      // 确定是否需要发送申请邮件，年月日相同则不需要继续发
      for (let i of this.selected_array) {
        let affirmDate = new Date(i.affirm_time)
        if (today.getFullYear() != affirmDate.getFullYear() || today.getMonth() != affirmDate.getMonth() ||
          today.getDate() != affirmDate.getDate()) {
          if (!this.proposer || this.proposer.length == 0) {
            this.$Notice.error({
              desc: '今日未收到确认，请选择确认人',
              duration: getErrNoticeShowTime()
            })
            return
          }
          // break;
        }
        // app_name_list.push(i.app_name)
      }
      if (!this.checkParamForH5()) {
        return
      }
      if (!this.checkParamForApp()) {
        return
      }
      if (this.environment == 'prod') {
        if (this.selected_array.length != this.app_array.length) {
          this.$Notice.warning({
            desc: '生产环境需选择所有应用申请',
            duration: getErrNoticeShowTime()
          })
          return
        }
        this.h5PublishApply()
      } else {
        // check有没有其他应用在灰度
        this.hdApply()
      }
    },
    /**
     * 点击【最近一次申请详情】按钮
     */
    getLastApplyInfo () {
      this.modal_last_apply_info = true
      let req = { 'branch_version': this.branch_version, 'suite_code': this.environment, 'group_name': this.group, 'tag_name': this.tag_name }
      getLastApplyInfoApi(req).then(res => {
        this.lastApplyInfoStatus = res.data.data.status
        this.lastApplyInfoReturnMessage = res.data.data.return_msg
        console.log('==========' + this.lastApplyInfoStatus)
        console.log('==========' + this.lastApplyInfoReturnMessage)
      })
    },

    /**
     * 灰度申请,检查是否有其它分支正在进入灰度
     */
    hdApply () {
      let app_name_list = []
      for (let i of this.selected_array) {
        app_name_list.push(i.app_name)
      }
      let req = { 'iteration_id': store.state.iterationID, 'app_name_list': app_name_list, 'env': this.environment }
      HdStatusCheckApi(req).then(res => {
        if (res.data.status === 'success') {
          this.hdApplyCheck()
        } else {
          // 存在差异打开确认框
          this.hd_conflict_confirm = true
          console.log(res.data.data['conflict_app'])
          this.conflictTableData = res.data.data['conflict_app']
        }
      })
    },
    /**
     * 灰度申请检查
     */
    hdApplyCheck () {
      /**
       * todo 检查项共有
       * 1: 回合校验
       * 2：是否有编译制品（前端应用h5）
       * 3：编译产物最新校验
       * 4：是否有其它版本进入灰度（前端H5，后期可以去除掉）
       * 5：最后一次的产物是否发布/下载
       * 6：检查当前制品较线上制品是否有变化（制品库制品和中转库制品是否一致）
       * 7：是否有应用迭代占用线上通道（产线）
       */
      this.$Spin.show({
        render: h => {
          return h('div', [
            h('Icon', {
              class: 'demo-spin-icon-load',
              props: {
                type: 'ios-loading',
                size: 18
              }
            }),
            h('div', '正在进行灰度申请校验，请稍等，预计需要两分钟。。。')
          ])
        }
      })
      let app_name_list = []
      for (let i of this.selected_array) {
        app_name_list.push(i.app_name)
      }
      let req = { 'iteration_id': store.state.iterationID, 'app_name_list': app_name_list, 'env': this.environment }
      // H5走这个检查，疑惑点，方法名为测试发布，建议更改
      if (this.group.indexOf('android') != -1 || this.group.indexOf('ios') != -1) {
        this.h5PublishApply()
      } else {
        testPublishCheckApi(req).then(res => {
          if (res.data.status === 'success') {
            this.$Notice.success({
              desc: res.data.msg,
              duration: getInfoNoticeShowTime()
            })
            let sid = res.data.data.sid
            this.externalServiceResult(sid)
          } else {
            this.$Notice.error({
              desc: res.data.msg,
              duration: getErrNoticeShowTime()
            })
          }
        })
      }
    },

    /**
     * 轮询使用sid进行检测
     */
    externalServiceResult (sid) {
      let vm = this
      externalServiceResult(sid).then(res => {
        let status = res.data.data.status
        let detail = res.data.data.detail
        if (status == 'success') {
          console.log('------ success --------')
          this.$Notice.success({
            desc: detail,
            duration: getInfoNoticeShowTime()
          })
          this.$Spin.hide()
          // 申请
          this.h5PublishApply(this.compile_array)
        } else if (status == 'failure') {
          console.log('------ failure --------')
          this.$Spin.hide()
          this.hdCheckTableData = JSON.parse(res.data.data.detail.replace(/'/g, '"'))
          this.hd_check_confirm = true
        } else {
          setTimeout(function () {
            vm.externalServiceResult(sid)
          }, 3000)
        }
      })
    },
    /**
     * 发布申请
     */
    h5PublishApply () {
      /**
       * todo 检查项共有
       * 1：是否有应用迭代占用线上通道（产线）
       * 2: 回合校验
       * 3：是否有编译制品（前端应用h5）
       * 4：编译产物最新校验/当前分支代码是否已经编译过生产产物（前端应用h5）
       * 5：最后一次的产物是否发布/下载(未加入)
       */
      // 关闭确认框
      this.hd_conflict_confirm = false
      this.app_check_confirm = false
      this.$Spin.show({
        render: h => {
          return h('div', [
            h('Icon', {
              class: 'demo-spin-icon-load',
              props: {
                type: 'ios-loading',
                size: 18
              }
            }),
            h('div', '申请中请稍等,请勿关闭当前窗口。。。')
          ])
        }
      })
      // 调用后台 申请,确定是否有应用申请当前环境上线（prod）
      if (this.group.indexOf('android') != -1 || this.group.indexOf('ios') != -1) {
        // App发布申请接口
        let applyParam = this.getAppApplyParam()
        let _this = this
        this.addUserAction(this.actionItem, applyParam).then(function (data) {
          if (!data) {
            _this.$Spin.hide()
            _this.$Notice.error({
              desc: '行为记录数据失败，无法执行发布行为',
              duration: getErrNoticeShowTime()
            })
            return
          }
          applyParam.actionId = data
          let appPublishApply = appPublishApplyApi
          // tag类型用tag的接口
          if (store.state.code_type == 'tag') {
            appPublishApply = appTagPublishApplyApi
          }
          appPublishApply(applyParam).then(res => {
            _this.$Spin.hide()
            if (res.data.status == 'failed') {
              _this.$Notice.error({
                desc: res.data.msg,
                duration: getErrNoticeShowTime()
              })
            } else {
              _this.$Notice.success({
                desc: res.data.msg,
                duration: getSuccessNoticeShowTime()
              })
              // 发送给领导 确认邮件
              _this.sendAffirmEmail()
              // _this.$forceUpdate()//强制刷新当前页面
              _this.init()
            }
          }).catch(err => {
            _this.$Spin.hide()
            _this.$Notice.error({
              desc: err,
              duration: getErrNoticeShowTime()
            })
          })
        })
      } else {
        h5PublishApplyApi(this.getH5ApplyParam()).then(res => {
          if (res.data.status == 'failed') {
            // 失败
            this.$Notice.error({
              desc: res.data.msg,
              duration: getErrNoticeShowTime()
            })
          } else { // 成功
            this.$Notice.success({
              desc: res.data.msg,
              duration: getInfoNoticeShowTime()
            })
            // 发送给领导 确认邮件
            this.sendAffirmEmail()
            this.$forceUpdate()// 强制刷新当前页面
            this.init()
          }
          this.$Spin.hide()
        }).catch(err => {
          this.$Spin.hide()
          this.$Notice.error({
            desc: '接口500错误',
            duration: getErrNoticeShowTime()
          })
        })
      }
      // 我们需要的是回填申请人的用户行为记录
      let action_value_obj = {
        'group': this.group,
        'branch_version': this.branch_version,
        'user': this.proposer.split('@')[0]
      }
      // 进行分支管理信息记录，便于确认人的行为更改，todo 不建议在这个地方直接操作，会影响确认人的行为实时性
      let action_value_list = []
      action_value_list.push(action_value_obj)
      let param = {
        action_item: 'H5branch',
        action_value: action_value_list
      }
      this.insertUserAction(param)
    },
    /**
     * 用户行为保存  H5
     * */
    insertUserAction (param) {
      console.log('============ insertUserAction ============')
      console.log(param)
      // 用户行为保存
      userAction(param).then(res => {
        console.log(res)
        let status = res.data.status
        if (status == 'success') {
          console.log('------ success --------')
        } else {
          console.log('------ error --------')
        }
      })
    },
    /**
     * 记录用户操作记录，顺带回填一些数据
     */
    myOnchange2 () {},
    myOnchange () {
      this.lastApplyButtonShow = true

      if (!(this.group.indexOf('android') != -1 || this.group.indexOf('ios') != -1)) {
        let action_value_obj = {
          'environment': this.environment,
          'proposer': this.proposer,
          'cc': this.cc,
          'fund_start_ver': this.fund_start_ver,
          'fund_end_ver': this.fund_end_ver,
          'fund_is_silent': this.fund_is_silent,
          'piggy_start_ver': this.piggy_start_ver,
          'piggy_end_ver': this.piggy_end_ver,
          'piggy_is_silent': this.piggy_is_silent
        }
        // 记录当前操作 --> 你绑定了环境
        let action_value_list = []
        action_value_list.push(action_value_obj)
        let param = {
          action_item: store.state.iterationID + '_' + 'apply',
          action_value: action_value_list
        }
        this.insertUserAction(param)
        // fund & piggy 起始版本回填
        let version_param = {
          'iteration_id': store.state.iterationID,
          'br_name': this.branch_version,
          'suite_code': this.environment
        }
        findDistVersion(version_param).then(res => {
          this.fund_start_ver_list = res.data.data.fund_begin_ver_list
          this.fund_end_ver_list = res.data.data.fund_end_ver_list
          this.piggy_start_ver_list = res.data.data.piggy_begin_ver_list
          this.piggy_end_ver_list = res.data.data.piggy_end_ver_list
          this.nf_fund_ver_list = res.data.data.nf_fund_ver_list
          this.nf_piggy_ver_list = res.data.data.nf_piggy_ver_list

          if (this.environment == 'prod') {
            console.log('====== prod ==========')
            console.log(this.fund_start_ver_list)
            this.fund_start_ver = '21.2.0'
            this.piggy_start_ver = '2.6.1'
            if (this.fund_start_ver_list.length > 0) {
              // 找到最新的一条记录
              let fundTemp = this.fund_start_ver_list[0].value
              let fundTempArr = fundTemp.split('.')
              this.fund_end_ver = this.add_one_upper(fundTempArr)
            }
            if (this.piggy_start_ver_list.length > 0) {
              // 找到最新的一条记录
              let piggyTemp = this.piggy_start_ver_list[0].value
              let piggyTempArr = piggyTemp.split('.')
              this.piggy_end_ver = this.add_one_upper(piggyTempArr)
            }
          }
          // 修改 fund 和 piggy 结束版本
          this.change_end_version()
        }).catch(err => {
          console.log('============= err =========')
          console.log(err)
          this.$Message.error(err)
        })
        h5PublishApplyApiGet(store.state.iterationID, this.environment, this.branch_version).then(res => {
          // 我在这里拼装 app_array
          this.app_array = res.data.data.ci_info
          this.init_with_app_array()
          this.waitPolling()
        })
      } else {
        if (this.environment) {
          h5PublishApplyApiGet(store.state.iterationID, this.environment, this.branch_version).then(res => {
            this.app_array = res.data.data.ci_info
            this.initRowParams(null, this)
            this.waitPolling()
          })
        }
      }
    },
    /**
     * 修改 fund 和 piggy 结束版本
     */
    change_end_version () {
      if (this.environment == 'vph' || this.environment == 'vps' || this.environment == 'pre') {
        if (this.fund_start_ver != '') {
          this.fund_end_ver = this.fund_start_ver
        }
        if (this.piggy_start_ver != '') {
          this.piggy_end_ver = this.piggy_start_ver
        }
      } else if (this.environment == 'prod') {
        // 因为prod下拉框不能选，所以控制不能写在onchange事件中
      }
    },
    /**
     *多选模式下生效，只要选中项发生变化就会触发,获取当前选中项列表
     */
    apply_list_select (params) {
      this.selected_array = params
      // 循環確定選擇的項是否包含頁面模塊展示的應用
      this.showPiggyAppParams = false
      this.showFundAppParams = false
      this.showFundParams = false
      this.showPiggyParams = false
      for (let i of this.selected_array) {
        this.showRowBySelected(i)
      }
    },
    /**
     * 初始化页面模块,哪些顯示，哪些不顯示
     */
    init_with_app_array () {
      // console.log(this.app_array)
      this.selected_array = []
      this.showPiggyAppParams = false
      this.showFundAppParams = false
      this.showFundParams = false
      this.showPiggyParams = false
      for (let i of this.app_array) {
        // 确定哪些项是被选择的
        if (i._checked) {
          this.showRowBySelected(i)
          // 將選擇的對象存入到selected_array里面
          this.selected_array.push(i)
        }
      }
    },
    // 根据table的内容确定,页面模块化展示的公共方法
    showRowBySelected (i) {
      // 判斷是否是ios或者android，同時是上線應用，則顯示對應參數列
      // 判斷是否是ios或者android，同時是上線應用，則顯示對應參數列
      if (i.app_name == 'piggy-ios' || i.app_name == 'piggy-android') {
        this.showPiggyAppParams = true
      }
      if (i.app_name == 'fund-ios' || i.app_name == 'fund-android') {
        this.showFundAppParams = true
      }
      // 判斷選中項中是否包含fund，fund是上線應用
      if (i.app_name === 'fund') {
        this.showFundParams = true
      }
      // 判斷選中項中是否包含piggy，piggy是上線應用
      if (i.app_name === 'piggy') {
        this.showPiggyParams = true
      }
    },
    /**
     * 满十进一 eg:20.0.9 --> 20.1.0     20.9.9 --> 21.0.0
     */
    add_one_upper (arr) {
      // arr 确定只有 length = 3
      if (arr[2] < 9) {
        arr[2] = parseInt(arr[2]) + 1 // 直接加会变成字符串拼接
      } else if (arr[2] == 9) {
        arr[2] = 0
        arr[1] = parseInt(arr[1]) + 1
        // 看看 arr[1] 是否也该进位了？
        if (arr[1] > 9) {
          arr[1] = 0
          arr[0] = parseInt(arr[0]) + 1
        }
      }
      let usefulStr = ''
      for (let i of arr) {
        usefulStr = usefulStr + i + '.'
      }
      // 去掉最后一个 点
      usefulStr = usefulStr.substring(0, usefulStr.length - 1)
      return usefulStr
    },
    add_default_cc (cc) {
      console.log('======= cc =======')
      console.log(cc)
      // ["<EMAIL>", "<EMAIL>", __ob__: Observer]
      // 不存在************** --> has_scm = 0
      let has_scm = 0
      for (let i of cc) {
        if (i == '<EMAIL>') {
          has_scm = 1
        }
      }
      if (has_scm == 0) {
        if (cc == null || cc == undefined || cc == '') {
          cc = []
        }
        cc.push('<EMAIL>')
      }
      return cc
    }
  }
}
</script>

<style scoped>
  .demo-spin-icon-load {
    animation: ani-demo-spin 1s linear infinite;
  }
</style>
