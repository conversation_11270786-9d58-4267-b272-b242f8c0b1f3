<template>
    <Card>
        <Row>
            <Button
                ghost
                type="primary"
                style="text-align: left; display: inline-block; width:80px; margin:5px"
                @click="hdPublishCheck"
                >一键发布
            </Button>
        </Row>

        <Row>
            <tables stripe v-model="remote_props" :columns="columns"> </tables>
        </Row>

        <Modal v-model="difference_modal" title="差异" width="70%" :styles="{ height: '60%' }" footer-hide>
            <Card>
                <Row v-html="git_diff_info"> </Row>
            </Card>
        </Modal>
        <Modal v-model="detail_modal" title="详情" width="70%" :styles="{ height: '60%' }" footer-hide>
            <Card>
                <Row v-html="publish_detail_info"> </Row>
            </Card>
        </Modal>
        <Modal
            title="灰度发布检查没有通过，请处理完毕后再行申请！！！！！！"
            width="800"
            v-model="hd_check_confirm"
            :mask-closable="true"
        >
            <div>
                <Row> </Row>
                <Row>
                    <Table :columns="hdCheckTableColumns" :data="hdCheckTableData"> </Table>
                </Row>
            </div>
            <div slot="footer">
                <!--<Button @click="batch_publish">继续申请</Button>-->
                <Button @click="closeConfirmModal">关闭</Button>
            </div>
        </Modal>
    </Card>
</template>

<script>
import Tables from '@/components/tables'
import store from '@/spider-store'
import { AppListPublishCheckApi } from '@/spider-api/publish'
import {
    externalServiceResult,
    h5HdPublishApi,
    h5HdPublishStateApi,
    getOnlineRepoDiff,
    getErrNoticeShowTime,
    getInfoNoticeShowTime
} from '@/spider-api/h5'

export default {
    name: 'h5-gray-remote',
    props: {
        //这里是table中的数据
        remote_props: {
            type: Array
        },
        queryState: {
            type: Function,
            default: null
        },
        vue_this: {
            type: Object
        }
    },
    components: {
        Tables
    },
    data() {
        return {
            git_diff_info: '',
            publish_detail_info: '',
            difference_modal: false,
            detail_modal: false,
            hd_check_confirm: false,
            hdCheckTableData: [],
            hdCheckTableColumns: [
                {
                    title: '检查项',
                    key: 'msg',
                    align: 'center'
                },
                {
                    title: '检查是否通过',
                    key: 'status',
                    align: 'center',
                    render: (h, params) => {
                        if (params.row.status.indexOf('success') >= 0) {
                            var color = 'green'
                        } else if (params.row.status.indexOf('failure') >= 0) {
                            var color = 'red'
                        }
                        return h('div', [h('p', { props: {}, style: { color: color } }, params.row.status)])
                    }
                }
            ],
            columns: [
                { title: '应用', key: 'app_name' },
                { title: '仓库', key: 'git_path' },
                {
                    title: '操作时间',
                    key: 'operate_time',
                    //sortable: true,
                    render: (h, params) => {
                        let value = params.row.operate_time
                        if (value == '' || value == null) {
                        } else {
                            value = this.formatDate(new Date(params.row.operate_time), 'yyyy-MM-dd hh:mm:ss')
                        }
                        return h('div', value)
                    }
                },
                // {title: "发布环境", key: "suite_code"},
                { title: '发布人', key: 'username' },
                {
                    title: '申请状态',
                    key: 'apply_status_display',
                    render: (h, params) => {
                        let color = '#2db7f5'
                        if (params.row.apply_status && params.row.apply_status.indexOf('success') >= 0) {
                            color = 'green'
                        } else if (params.row.apply_status && params.row.apply_status.indexOf('failure') >= 0) {
                            color = 'red'
                        }

                        return h(
                            'Tooltip',
                            {
                                props: {
                                    placement: 'top',
                                    content: params.row.apply_message,
                                    'max-width': 500,
                                    transfer: true
                                }
                            },
                            [h('p', { props: {}, style: { color: color } }, params.row.apply_status_display)]
                        )
                    }
                },
                {
                    title: '发布状态',
                    key: 'status_display',
                    render: (h, params) => {
                        let color = '#2db7f5'
                        if (params.row.status && params.row.status.indexOf('success') >= 0) {
                            color = 'green'
                        } else if (params.row.status && params.row.status.indexOf('failure') >= 0) {
                            color = 'red'
                        } else if (params.row.status && params.row.status.indexOf('warning') >= 0) {
                            color = '#ff9900'
                        }
                        /*return h("div", [
                            h("p",
                              {props: {}, style: {color: color}},
                              params.row.status)
                          ]);*/
                        return h(
                            'Tooltip',
                            {
                                props: {
                                    placement: 'top',
                                    content: params.row.message,
                                    'max-width': 500,
                                    transfer: true
                                }
                            },
                            [h('p', { props: {}, style: { color: color } }, params.row.status_display)]
                        )
                    }
                },
                {
                    title: '操作',
                    key: 'action',
                    width: 200,
                    align: 'center',
                    render: (h, params) => {
                        return h('div', [
                            h(
                                'Button',
                                {
                                    props: {
                                        type: 'default',
                                        size: 'small'
                                    },
                                    style: {
                                        marginRight: '5px'
                                    },
                                    on: {
                                        click: () => {
                                            this.showDiff(params.row.app_name)
                                        }
                                    }
                                },
                                '差异'
                            ),
                            h(
                                'Button',
                                {
                                    props: {
                                        type: 'default',
                                        size: 'small'
                                    },
                                    style: {
                                        marginRight: '5px'
                                    },
                                    on: {
                                        click: () => {
                                            this.showDetail(params.row.app_name)
                                        }
                                    }
                                },
                                '详情'
                            )
                        ])
                    }
                }
            ]
        }
    },
    mounted() {
        this.init()
    },
    methods: {
        init() {
            console.log('remote init -------')
        },

        closeConfirmModal() {
            this.hd_check_confirm = false
        },

        /**
         * 灰度发布申请检查
         */

        hdPublishCheck() {
            for (let i = 0; i < this.remote_props.length; i++) {
                if (this.remote_props[i].apply_status.indexOf('success') < 0) {
                    this.$Notice.error({
                        title: this.remote_props[i].app_name + '申请还未成功',
                        duration: getErrNoticeShowTime()
                    })
                    return
                }
            }

            this.$Spin.show({
                render: h => {
                    return h('div', [
                        h('Icon', {
                            class: 'demo-spin-icon-load',
                            props: {
                                type: 'ios-loading',
                                size: 18
                            }
                        }),
                        h('div', '正在进行灰度申请校验，请稍等，预计需要两分钟。。。')
                    ])
                }
            })
            let app_name_list = []
            let suite_name = ''
            for (let i = 0; i < this.remote_props.length; i++) {
                app_name_list.push(this.remote_props[i].app_name)
                suite_name = this.remote_props[i].suite_name
            }

            AppListPublishCheckApi(store.state.iterationID, app_name_list, suite_name).then(res => {
                if (res.data.status === 'success') {
                    this.$Notice.success({
                        desc: res.data.msg,
                        duration: getInfoNoticeShowTime()
                    })
                    console.log('============res =========')
                    console.log(res.data.data)
                    let sid = res.data.data.sid
                    this.externalServiceResult(sid)
                } else {
                    this.$Notice.error({
                        desc: res.data.msg,
                        duration: getErrNoticeShowTime()
                    })
                }
            })
        },

        /**
         * 批量发布
         */
        batch_publish() {
            let publish_list = []
            let publish_obj = {}
            console.log(this.remote_props)
            for (let i = 0; i < this.remote_props.length; i++) {
                let app_name = this.remote_props[i].app_name
                let suite_name = this.remote_props[i].suite_name

                publish_obj = {
                    suite_name: suite_name,
                    op_type: 'code_update',
                    app_name: app_name,
                    iteration_id: store.state.iterationID
                }
                publish_list.push(publish_obj)
            }

            if (publish_list.length > 0) {
                h5HdPublishApi(publish_list).then(res => {
                    console.log(res)
                    if (res.status == '200') {
                        //成功
                        this.$Notice.success({
                            title: res.data.msg,
                            duration: getInfoNoticeShowTime()
                        })

                        console.log(' will call parent method query status')
                        this.queryState(this.vue_this)
                    } else {
                        //失败
                        this.$Notice.error({
                            title: res.data.msg,
                            duration: getErrNoticeShowTime()
                        })
                    }
                })
            }
        },

        /**
         * 轮询使用sid进行检测
         */
        externalServiceResult(sid) {
            let vm = this
            externalServiceResult(sid).then(res => {
                let status = res.data.data.status
                let detail = res.data.data.detail
                if (status == 'success') {
                    console.log('------ success --------')
                    this.$Notice.success({
                        desc: detail,
                        duration: getInfoNoticeShowTime()
                    })
                    this.$Spin.hide()
                    //申请
                    this.batch_publish()
                } else if (status == 'failure') {
                    console.log('------ failure --------')
                    this.$Spin.hide()
                    this.hdCheckTableData = JSON.parse(res.data.data.detail.replace(/'/g, '"'))
                    this.hd_check_confirm = true
                } else {
                    setTimeout(function() {
                        vm.externalServiceResult(sid)
                    }, 3000)
                }
            })
        },
        formatDate(date, fmt) {
            let o = {
                'M+': date.getMonth() + 1, // 月份
                'd+': date.getDate(), // 日
                'h+': date.getHours(), // 小时
                'm+': date.getMinutes(), // 分
                's+': date.getSeconds(), // 秒
                S: date.getMilliseconds() // 毫秒
            }
            if (/(y+)/.test(fmt)) {
                fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length))
            }
            for (var k in o) {
                if (new RegExp('(' + k + ')').test(fmt)) {
                    fmt = fmt.replace(
                        RegExp.$1,
                        RegExp.$1.length === 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length)
                    )
                }
            }
            return fmt
        },
        showDiff(app_name) {
            let req = {
                app_name: app_name,
                iteration_id: store.state.iterationID,
                suite_code: this.vue_this.suite_code
            }
            getOnlineRepoDiff(req).then(res => {
                console.log(res)
                if (res.status == '200') {
                    //成功
                    let data_list = res.data.data
                    let diff_text = '<div>'
                    console.log(data_list)
                    console.log(data_list.length)
                    if (data_list.length > 0) {
                        for (let i = 0; i < data_list.length; i++) {
                            let data_val = data_list[i]
                            diff_text += '<p>' + data_val['node_ip'] + ' 差异：</p>'
                            let diff_info = data_val['diff_content']
                            let diff_splits = diff_info.split('\n')
                            diff_text += '<pre>'
                            for (let i in diff_splits) {
                                diff_text += '    ' + diff_splits[i] + '</br>'
                            }
                            diff_info += '</pre></br></br>'
                        }
                    } else {
                        diff_text = '无差异'
                    }
                    this.git_diff_info = diff_text
                    this.difference_modal = true
                } else {
                    //失败
                    this.$Notice.error({
                        title: res.data.msg,
                        duration: getErrNoticeShowTime()
                    })
                }
            })
        },
        showDetail(app_name) {
            //查看发布信息
            h5HdPublishStateApi(store.state.iterationID).then(res => {
                console.log(res.data.data.operate_info)
                let detail_info = '<div>'
                for (let i in res.data.data.operate_info) {
                    let publish_info_data = res.data.data.operate_info[i]
                    if (publish_info_data.app_name === app_name) {
                        let result = publish_info_data.message
                        let ip = publish_info_data.ip
                        if (result != null) {
                            let htmlResult =
                                '<p>ip:' +
                                ip +
                                '</p>' +
                                '<div><pre>' +
                                result.replaceAll('\\n', '<br/>') +
                                '</pre></div>'
                            detail_info += htmlResult
                        }
                    }
                }

                detail_info += '</div>'

                this.publish_detail_info = detail_info
                this.detail_modal = true
            })
        }
    }
}
</script>

<style scoped></style>
