<template>
    <Card>
        <ExpressBuildService :dist_app_list="dist_props"> </ExpressBuildService>
        <Row>
            <tables stripe v-model="dist_props" :columns="columns"> </tables>
        </Row>
        <!-- <Modal
      v-model="detail_modal"
      title="详情"
      width="70%"
      :styles="{height: '60%'}"
      footer-hide>
      <Card>
        <Row v-html="publish_detail_info">
        </Row>
      </Card>
    </Modal>-->
        <Modal
            :styles="{ width: '60%' }"
            v-model="download_modal"
            title="下载（右键点击【下载】按钮选择【链接另存为】可直接保存下载）"
            footer-hide
        >
            >
            <Card>
                <Tabs ref="download_tabs">
                    <TabPane label="H5源包" name="DownloadZip">
                        <DownloadZip :download_file_props="download_zip_props"></DownloadZip>
                    </TabPane>
                </Tabs>
            </Card>
        </Modal>
    </Card>
</template>

<script>
import Tables from '@/components/tables'
import store from '@/spider-store'
import DownloadZip from '../h5-download/h5-download-zip.vue'
import DownloadMd5 from '../h5-download/h5-download-md5.vue'
import {
    h5HdPublishApi,
    h5HdPublishStateApi,
    getResourceInfo,
    getInfoNoticeShowTime,
    getErrNoticeShowTime
} from '@/spider-api/h5'
import ExpressBuildService from '../../mobile-gray/express-build-service.vue'

export default {
    name: 'h5-gray-zip',
    components: {
        Tables,
        DownloadZip,
        DownloadMd5,
        ExpressBuildService
    },
    props: {
        //这里是table中的数据
        dist_props: {
            type: Array
        },
        queryState: {
            type: Function,
            default: null
        },
        vue_this: {
            type: Object
        }
    },
    data() {
        return {
            h5_app_list: ['fund', 'piggy'],
            publish_detail_info: '',
            detail_modal: false,
            download_modal: false,
            download_zip_props: [],
            columns: [
                { title: '应用', key: 'app_name' },
                { title: '仓库', key: 'git_path' },
                { title: '起始版本', key: 'begin_ver' /*sortable: true*/ },
                { title: '结束版本', key: 'end_ver' /*sortable: true*/ },
                {
                    title: '申请状态',
                    key: 'apply_status_display',
                    render: (h, params) => {
                        let color = '#2db7f5'
                        if (params.row.apply_status && params.row.apply_status.indexOf('success') >= 0) {
                            color = 'green'
                        } else if (params.row.apply_status && params.row.apply_status.indexOf('failure') >= 0) {
                            color = 'red'
                        }

                        return h(
                            'Tooltip',
                            {
                                props: {
                                    placement: 'top',
                                    content: params.row.apply_message,
                                    'max-width': 500,
                                    transfer: true
                                }
                            },
                            [h('p', { props: {}, style: { color: color } }, params.row.apply_status_display)]
                        )
                    }
                },
                // {title: "操作时间", key: "publish_date", /*sortable: true*/},
                // {title: "状态", key: "status",
                //   render: (h, params) => {
                //     let status = params.row.status
                //     let action_item = params.row.action_item
                //     //这里全是发布
                //     if(action_item === 'test_publish'){
                //       if(status === 'success'){
                //         return h('div','发布成功')
                //       }else if (status === 'running'){
                //         return h('div','正在发布')
                //       }else if(status === 'failed'){
                //         return h('div','发布失败')
                //       }else if(status === '' || status === null){
                //         return h('div','已编译未发布')
                //       }
                //     }else if(action_item === 'h5_compile'){
                //       //这里全是编译
                //       if(status === 'success'){
                //         return h('div','编译成功')
                //       }else if (status === 'running'){
                //         return h('div','正在编译')
                //       }else if(status === 'failed'){
                //         return h('div','编译失败')
                //       }else if(status === '' || status === null){
                //         return h('div','未编译')
                //       }
                //     }
                //   }
                // },
                // {title: "操作人", key: "publisher"},
                {
                    title: '操作',
                    key: 'action',
                    width: 250,
                    align: 'center',
                    render: (h, params) => {
                        let publish_status = params.row.publish_status
                        return h('div', [
                            h(
                                'Button',
                                {
                                    props: {
                                        type: 'default',
                                        size: 'small'
                                    },
                                    style: {
                                        marginRight: '5px'
                                    },
                                    on: {
                                        click: () => {
                                            this.showDownload(params.row.app_name, params.row.suite_name)
                                        }
                                    }
                                },
                                '下载'
                            )
                        ])
                    }
                }
            ]
        }
    },
    mounted() {
        this.init()
    },
    methods: {
        init() {},
        //发布
        publish(param) {
            for (let i = 0; i < param.length; i++) {
                if (param[i].apply_status.indexOf('success') < 0) {
                    this.$Notice.error({
                        title: param[i].app_name + '申请还未成功',
                        duration: getErrNoticeShowTime()
                    })
                    return
                }
            }

            let publish_list = []
            let publish_obj = {}
            console.log(param)
            for (let i = 0; i < param.length; i++) {
                let app_name = param[i].app_name
                let suite_name = param[i].suite_name

                publish_obj = {
                    suite_name: suite_name,
                    op_type: 'code_update',
                    app_name: app_name,
                    iteration_id: store.state.iterationID
                }
                publish_list.push(publish_obj)
            }
            console.log(publish_list)
            if (publish_list.length > 0) {
                h5HdPublishApi(publish_list).then(res => {
                    console.log(res)
                    if (res.status == '200') {
                        //成功
                        this.$Notice.success({
                            title: res.data.msg,
                            duration: getInfoNoticeShowTime()
                        })

                        console.log(' will call parent method query status')
                        this.queryState(this.vue_this)
                    } else {
                        //失败
                        this.$Notice.error({
                            title: res.data.msg,
                            duration: getErrNoticeShowTime()
                        })
                    }
                })
            }
        },
        showDetail(app_name) {
            //查看发布信息
            h5HdPublishStateApi(store.state.iterationID).then(res => {
                console.log(res.data.data.operate_info)
                let detail_info = '<div>'
                for (let i in res.data.data.operate_info) {
                    let publish_info_data = res.data.data.operate_info[i]
                    if (publish_info_data.app_name === app_name) {
                        let result = publish_info_data.message
                        let ip = publish_info_data.ip
                        if (result != null) {
                            let htmlResult =
                                '<p>ip:' +
                                ip +
                                '</p>' +
                                '<div><pre>' +
                                result.replaceAll('\\n', '<br/>') +
                                '</pre></div>'
                            detail_info += htmlResult
                        }
                    }
                }

                detail_info += '</div>'

                this.publish_detail_info = detail_info
                this.detail_modal = true
            })
        },
        showDownload(app_name, suite_name) {
            let _this = this
            let param = {
                app_name: app_name, //'fund',
                iteration_id: store.state.iterationID,
                suite_code: suite_name
            } //
            //下载信息
            getResourceInfo(param).then(res => {
                console.log(res.data.data)
                _this.download_zip_props = res.data.data
                _this.download_modal = true
            })
        }
    }
}
</script>
<style scoped></style>
