<template>
    <div>
        <Row>
            <tables stripe v-model="remote_props" :columns="columns"> </tables>
        </Row>
    </div>
</template>

<script>
import Tables from '@/components/tables'
export default {
    name: 'h5-download-md5',
    components: {
        Tables
    },
    data() {
        return {
            remote_props: [],
            columns: [
                { title: '文件版本', key: 'version' },
                { title: '文件大小', key: 'size' },
                {
                    title: '操作',
                    key: 'action',
                    width: 200,
                    align: 'center',
                    render: (h, params) => {
                        return h('div', [
                            h(
                                'Button',
                                {
                                    props: {
                                        type: 'default',
                                        size: 'small'
                                    },
                                    style: {
                                        marginRight: '5px'
                                    },
                                    on: {
                                        click: () => {
                                            window.open('')
                                        }
                                    }
                                },
                                '下载'
                            )
                        ])
                    }
                }
            ]
        }
    }
}
</script>

<style scoped></style>
