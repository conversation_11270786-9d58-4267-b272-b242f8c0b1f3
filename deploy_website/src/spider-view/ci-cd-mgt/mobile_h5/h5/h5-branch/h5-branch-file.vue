<template>
    <div>
        <Card>
            <Row style="margin-top: 10px">
                <i-col style="margin: 10px;text-align: right" span="2">
                    <span style="text-align: right; display: inline-block;">组：</span>
                </i-col>
                <i-col style="margin: 10px" span="8">
                    <span style="text-align: left; display: inline-block;">{{ group }}</span>
                </i-col>
                <i-col style="margin: 10px;text-align: right" span="2">
                    <span style="text-align: right; display: inline-block;">分支版本：</span>
                </i-col>
                <i-col style="margin: 10px" span="8">
                    <span style="text-align: left; display: inline-block;">{{ branch_version }}</span>
                </i-col>
            </Row>
            <Row style="margin-top: 10px">
                <i-col style="margin: 10px;text-align: right" span="2">
                    <span style="text-align: right; display: inline-block;">发布类型：</span>
                </i-col>
                <i-col style="margin: 10px" span="8">
                    <span style="text-align: left; display: inline-block;">{{ publish_type }}</span>
                </i-col>
                <i-col style="margin: 10px;text-align: right" span="2">
                    <span style="text-align: right; display: inline-block;">迭代版本：</span>
                </i-col>
                <i-col style="margin: 10px" span="8">
                    <span v-if="branch_version != '' && group != ''" style="text-align: left; display: inline-block;">
                        {{ group + '_' + branch_version }}</span
                    >
                </i-col>
            </Row>
            <Row style="margin-top: 10px">
                <i-col style="margin: 10px;text-align: right" span="2">
                    <span style="text-align: right; display: inline-block;">截止时间：</span>
                </i-col>
                <Col span="5">
                    <DatePicker type="date" placeholder="截止时间" v-model="deadline"></DatePicker>
                </Col>
            </Row>
            <Row style="margin-top: 10px">
                <i-col style="margin: 10px;text-align: right" span="2">
                    <span style="text-align: right; display: inline-block;">抄送人：</span>
                </i-col>
                <Col span="10">
                    <Select v-model="cc" multiple filterable>
                        <Option v-for="(item, index) in allFilterMails" :value="item" :label="item" :key="item"
                            >{{ item }}
                        </Option>
                    </Select>
                </Col>
                <Col span="2" style="margin-left: 20px">
                    <Button
                        ghost
                        type="primary"
                        style="text-align: left; display: inline-block; width:125px;margin-top:-1px"
                        @click="showArchiveAckModal"
                        >上线完成(含归档)
                    </Button>
                </Col>
            </Row>
            <Row style="margin-top: 10px">
                <tables stripe v-model="add_app" :columns="columns"> </tables>
            </Row>
            <Modal v-model="modal_archive_ack" width="1000">
                <p slot="header" style="color: rgb(128,128,128)">
                    <Icon type="md-clipboard"></Icon>
                    <span>迭代归档</span>
                </p>
                <span>确定将 {{ this.pipeline_id }} 归档?</span>
                <div style="margin: 20px 0;" v-if="reasonValidate.length > 0">
                    <Table :columns="reasonColumns" :data="reasonValidate" height="500"></Table>
                </div>
                <div slot="footer">
                    <Button @click="closeArchiveAckModal">关闭</Button>
                    <Button ghost type="success" @click="do_archive_apply">确定</Button>
                </div>
            </Modal>
        </Card>
    </div>
</template>

<script>
import Tables from '@/components/tables'
import store from '@/spider-store'
import { getEmailAddresses, publishApply, getServiceResult, archiveApi, archiveCheckApi } from '@/spider-api/iter-plan'
import { syncConfigApi, CheckConfigSyncApi, checkConfigConsistentApi, fileConfigBranch } from '@/spider-api/zues'
import { getIterGitRopesApi } from '@/spider-api/get-iter-info'
import { h5ProdPublishInfo, h5CiPipelineApiGet, h5HdPublishInfo, getErrNoticeShowTime } from '@/spider-api/h5'
import { h5PublishApplyStatus, getNeedReasonNodeInfoApi, createPublishReasonApi } from '@/spider-api/h5-ci-cd/nf-app'
import { formatDate } from '@/spider-api/h5-common'
import { android_push, android_check } from '@/spider-api/publish'

const OP_TYPE_CN = {
    deploy: '发布',
    update_and_deploy: '配置更新+发布',
    restart: '重启',
    stop: '停止',
    rollback: '回滚',
    update: '配置更新',
    code_update: '代码更新',
    update_and_deploy_and_verify: 'jenkins批量发布'
}

export default {
    name: 'h5_branch_file',
    components: {
        Tables
    },
    data() {
        return {
            group: '',
            branch_version: '',
            publish_type: '',
            environment_list: [],
            add_app: [],
            app_name_list: [],
            archive_check_app_name_list: [],
            compile_array: [],
            deadline: '',
            allSelectedMails: ['<EMAIL>'],
            allFilterMails: [],
            allMails: [],
            cc: '',
            pipeline_id: '',
            modal_archive_ack: false,
            nf_sys_status: '',
            nf_br_name: '',
            if_same_iter: '',
            android_is_pushing: false,
            columns: [
                { title: '应用', key: 'app_name' },
                { title: '仓库', key: 'git_path' },
                {
                    title: '操作时间',
                    key: 'operate_time',
                    render: (h, params) => {
                        let value = params.row.operate_time
                        if (value == '' || value == null) {
                        } else {
                            value = formatDate(new Date(params.row.operate_time), 'yyyy-MM-dd hh:mm:ss')
                        }
                        return h('div', value)
                    }
                },
                { title: '操作人', key: 'username' },
                { title: '阶段', key: 'sys_status' },
                // {title: "描述", key: "description" },
                {
                    title: '状态',
                    key: 'status',
                    render: (h, params) => {
                        let status = params.row.status
                        if (status === 'running') {
                            return h('div', '发布中')
                        } else if (status === 'success') {
                            return h('div', { style: { color: 'green' } }, '发布成功')
                        } else if (status === 'failure') {
                            return h('div', { style: { color: 'red' } }, '发布失败')
                        } else if (status === 'compile_running') {
                            return h('div', '编译中')
                        } else if (status === 'compile_success') {
                            return h('div', { style: { color: 'green' } }, '编译成功')
                        } else if (status === 'compile_failure') {
                            return h('div', { style: { color: 'red' } }, '编译失败')
                        } else if (status === 'publish_running') {
                            return h('div', '发布中')
                        } else if (status === 'publish_success') {
                            return h('div', { style: { color: 'green' } }, '发布成功')
                        } else if (status === 'publish_failure') {
                            return h('div', { style: { color: 'red' } }, '发布失败')
                        }
                    }
                },
                {
                    title: '操作',
                    key: 'action',
                    width: 250,
                    align: 'center',
                    render: (h, params) => {
                        let app_name = params.row.app_name

                        let is_android = app_name === 'fund-android'
                        // let is_android = true
                        return h(
                            'div',
                            {
                                class: 'bc'
                            },
                            [
                                h(
                                    'Button',
                                    {
                                        props: {
                                            type: 'default',
                                            size: 'small',
                                            loading: this.android_is_pushing
                                        },
                                        style: {
                                            marginRight: '5px',
                                            // display: 'none'
                                            display: is_android ? 'inline-block' : 'none'
                                        },
                                        on: {
                                            click: () => {
                                                this.android_push(params.row)
                                                // alert(">>>>is_android = " + params.row.app_name)
                                            }
                                        }
                                    },
                                    this.android_is_pushing === true ? '推包中' : '安卓推包'
                                ),
                                h(
                                    'Button',
                                    {
                                        props: {
                                            type: 'default',
                                            size: 'small',
                                            loading: this.android_is_pushing
                                        },
                                        style: {
                                            marginRight: '5px',
                                            display: is_android ? 'inline-block' : 'none'
                                        },
                                        on: {
                                            click: () => {
                                                this.android_check(params.row)
                                            }
                                        }
                                    },
                                    this.android_is_pushing === true ? '检查中' : '安卓包一致性检查'
                                )
                            ]
                        )
                    }
                }
            ],
            reasonColumns: [
                // {
                //     title: '应用名',
                //     key: 'app_name',
                //     width: 150
                // },
                // {
                //     title: '环境',
                //     key: 'suite_name',
                //     width: 100
                // },
                // {
                //     title: '节点',
                //     key: 'node_ip',
                //     width: 140
                // },
                // {
                //     title: '操作人',
                //     key: 'op_user',
                //     width: 100
                // },
                // {
                //     title: '操作时间',
                //     key: 'op_time',
                //     render: (h, params) => {
                //         return h(
                //             'span',
                //             params.row.op_time
                //                 ? this.dateFormat('YYYY-mm-dd HH:MM:SS', new Date(params.row.op_time))
                //                 : ''
                //         )
                //     }
                // },
                // {
                //     title: '操作类型',
                //     key: 'op_type',
                //     render: (h, params) => {
                //         return h('span', OP_TYPE_CN[params.row.op_type])
                //     }
                // },
                {
                    title: '重复产线申请时间',
                    key: 'apply_at'
                },
                {
                    title: '申请人',
                    key: 'applicant'
                },
                {
                    title: '发布统计',
                    key: 'publish_detail'
                },
                {
                    title: '原因',
                    key: 'opt_reason',
                    width: 220,
                    render: (h, params) => {
                        return h('Input', {
                            props: {
                                value: params.row.opt_reason,
                                type: 'textarea',
                                autosize: { minRows: 1, maxRows: 3 },
                                placeholder: '请输入原因',
                                maxlength: 100
                            },
                            on: {
                                input: value => {
                                    params.row.opt_reason = value
                                    this.reasonValidate.map(item => {
                                        if (item.id === params.row.id) {
                                            item.opt_reason = value
                                        }
                                        return item
                                    })
                                },
                                'on-blur': event => {
                                    if (event.target.value.length < 10) {
                                        this.$Message.error('原因至少包含10个字符，请重新输入!')
                                    }
                                }
                            }
                        })
                    }
                }
            ],
            reasonValidate: []
        }
    },
    mounted() {
        // this.init()
    },
    methods: {
        dateFormat(fmt, date) {
            let ret
            const opt = {
                'Y+': date.getFullYear().toString(), // 年
                'm+': (date.getMonth() + 1).toString(), // 月
                'd+': date.getDate().toString(), // 日
                'H+': date.getHours().toString(), // 时
                'M+': date.getMinutes().toString(), // 分
                'S+': date.getSeconds().toString() // 秒
                // 有其他格式化字符需求可以继续添加，必须转化成字符串
            }
            for (let k in opt) {
                ret = new RegExp('(' + k + ')').exec(fmt)
                if (ret) {
                    fmt = fmt.replace(ret[1], ret[1].length == 1 ? opt[k] : opt[k].padStart(ret[1].length, '0'))
                }
            }
            return fmt
        },
        init() {
            //初始化，防止切不同分支数据越来越多
            this.add_app = []
            this.group = store.state.h5_group
            this.branch_version = store.state.h5_branch_version
            this.publish_type = store.state.publish_type
            getEmailAddresses().then(res => {
                if (res.data.status === 'success') {
                    this.allMails = res.data.data
                    this.allFilterMails = this.allMails
                }
            })
            this.pipeline_id = store.state.iterationID
            this.deadline = store.state.deadline
            //分支归档页面查询
            getIterGitRopesApi({ iterationID: store.state.iterationID }).then(res => {
                //获取git仓库信息
                let repoTableData = res.data.data['git_repo_list']
                //获取应用信息
                let projectTableData = res.data.data['appname_list']
                let app = {}
                for (let p of projectTableData) {
                    this.app_name_list.push(p.appName)
                    for (let r of repoTableData) {
                        if (p.appName == r.app_name) {
                            app = {
                                app_name: r.app_name,
                                git_path: r.gitRepo,
                                operate_time: '',
                                username: '',
                                sys_status: p.sys_status,
                                status: '',
                                need_ops: p.need_ops
                            }
                            this.add_app.push(app)
                        }
                    }
                }
                //拼装 this.add_app
                //调用【生产】查询接口，对已有数据的状态是【上线中】的，写入操作人，操作时间，状态
                h5ProdPublishInfo(store.state.iterationID).then(res => {
                    let prod_publish_info = res.data.data.prod_publish_info
                    if (prod_publish_info.length > 0) {
                        for (let p of prod_publish_info) {
                            for (let i of this.add_app) {
                                if (p.app_name == i.app_name && i.sys_status == '上线中') {
                                    i.operate_time = p.operate_time
                                    i.username = p.username
                                    i.status = p.status
                                }
                            }
                        }
                    }
                })
                //调用【测试】查询接口，对已有数据的状态是【测试中】的，写入操作人，操作时间，状态
                h5CiPipelineApiGet(store.state.iterationID).then(res => {
                    let ci_info = res.data.data.ci_info
                    if (ci_info.length > 0) {
                        for (let c of ci_info) {
                            for (let i of this.add_app) {
                                if (
                                    c.app_name == i.app_name &&
                                    i.sys_status == '测试中' &&
                                    c.status != '' &&
                                    c.status != null
                                ) {
                                    i.operate_time = c.operate_time
                                    i.username = c.username
                                    i.status = c.status
                                }
                                if (
                                    c.app_name == i.app_name &&
                                    i.sys_status == '测试中' &&
                                    (c.status == '' || c.status == null)
                                ) {
                                    i.sys_status = '未测试'
                                }
                            }
                        }
                    }
                })
                //调用【灰度】查询接口，对已有数据的状态是【灰度中】的，写入操作人，操作时间，状态
                h5HdPublishInfo(store.state.iterationID).then(res => {
                    let prod_publish_info = res.data.data.prod_publish_info
                    if (prod_publish_info.length > 0) {
                        for (let p of prod_publish_info) {
                            for (let i of this.add_app) {
                                if (p.app_name == i.app_name && i.sys_status == '灰度中') {
                                    i.operate_time = p.operate_time
                                    i.username = p.username
                                    i.status = p.status
                                }
                            }
                        }
                    }
                })
            })
        },
        showArchiveAckModal() {
            this.reasonValidate = []
            // 获取需要填写原因的节点列表
            getNeedReasonNodeInfoApi({
                iteration_id: store.state.iterationID
            })
                .then(res => {
                    if (res.data && res.data.status === 'success') {
                        const { data } = res.data
                        if (data.length > 0) {
                            data.forEach((item, index) => {
                                // this.reasonValidate.push({
                                //     id: index,
                                //     opt_reason: '',
                                //     app_name: item.app_name,
                                //     node_ip: item.node_ip,
                                //     op_time: item.op_time,
                                //     op_type: item.op_type,
                                //     op_user: item.op_user,
                                //     suite_name: item.suite_name,
                                //     iteration_id: item.iteration_id
                                // })
                                this.reasonValidate.push({
                                    id: index,
                                    opt_reason: item.opt_reason || '',
                                    apply_at: item.apply_at,
                                    applicant: item.applicant,
                                    publish_detail: item.publish_detail,
                                    iteration_id: store.state.iterationID
                                })
                            })
                        }
                    }
                })
                .catch(ex => console.error(ex))
            //首先判断是否所有数据都处于【上线中】状态 && need_ops，如果不是，不允许归档，need_ops 为0或null，不要求状态是【上线中】
            for (let i of this.add_app) {
                if (i.need_ops == 1) {
                    //只有接入到流水线整个流程的需要校验，
                    //console.info("this.archive_check_app_name_list:"+JSON.stringify(this.archive_check_app_name_list))
                    if (!(i.sys_status == '上线中' && i.status.indexOf('success') > -1)) {
                        this.$Notice.error({
                            desc: i.app_name + '应用未处于上线中【发布成功】阶段，不允许归档',
                            duration: getErrNoticeShowTime()
                        })
                        return
                    }
                }
                h5PublishApplyStatus(store.state.iterationID, i.app_name).then(res => {
                    if (res.data.status == 'failed') {
                        alert(store.state.iterationID + '没做过产线申请，请先申请再归档！！！！')
                    } else {
                        this.modal_archive_ack = true
                    }
                })
            }
            // this.modal_archive_ack = true
        },
        closeArchiveAckModal() {
            this.modal_archive_ack = false
        },
        //执行归档
        do_archive_apply() {
            const check = this.reasonValidate.every(item => {
                return item.opt_reason.length >= 10
            })
            // 校验是否有数据的“原因”字段小于10个字符
            if (!check) {
                this.$Message.error('归档原因字数不能少于10个字符，请检查后重新提交。')
                return
            }

            const hasNoneReason = this.reasonValidate.filter(item => !item.opt_reason)

            if (hasNoneReason.length > 0) {
                this.$Message.error('部分节点原因未填写，请检查并填写后再次提交。')
                return
            }

            createPublishReasonApi({
                publish_reason_list: this.reasonValidate.map(item => {
                    return {
                        // module_name: item.app_name,
                        // suite_code: item.suite_name,
                        // node_ip: item.node_ip,
                        // opt_type: item.op_type,
                        // opt_time: item.op_time,
                        // iteration_id: item.iteration_id,
                        // opt_reason: item.opt_reason
                        apply_at: item.apply_at,
                        applicant: item.applicant,
                        publish_detail: item.publish_detail,
                        iteration_id: item.iteration_id,
                        opt_reason: item.opt_reason
                    }
                })
            })
                .then(res => {
                    if (res.data && res.data.status === 'success') {
                        this.modal_archive_ack = false
                        this.$Spin.show({
                            render: h => {
                                return h('div', [
                                    h('Icon', {
                                        class: 'demo-spin-icon-load',
                                        props: {
                                            type: 'ios-loading',
                                            size: 18
                                        }
                                    }),
                                    h('div', '归档中...')
                                ])
                            }
                        })
                        this.doArchiveCheck()
                    } else {
                        this.$Message.error(res.data.msg)
                    }
                })
                .catch(ex => console.error(ex))
        },
        doArchiveCheck() {
            archiveCheckApi(store.state.iterationID)
                .then(result => {
                    if (result.data.status === 'success') {
                        let sid = result.data.data.sid
                        this.get_check_service_result(sid, 'archive_check')
                    } else {
                        alert(result.data.msg)
                        this.$Spin.hide()
                    }
                })
                .catch(err => {
                    this.$Spin.hide()
                })
        },
        get_check_service_result(sid, business_name) {
            getServiceResult(sid)
                .then(res => {
                    if (res.data.status === 'success') {
                        let status = res.data.data.status
                        let detail = res.data.data.detail
                        console.log(status, business_name, sid, detail)
                        if (status === 'success') {
                            if (business_name === 'publish_check') {
                                this.doApply()
                            } else if (business_name === 'archive_check') {
                                this.doArchive()
                            } else {
                                if (detail) {
                                    alert(detail)
                                } else {
                                    store.commit('setH5BranchVersion', '')
                                    alert('执行成功')
                                }
                                this.$Spin.hide()
                            }
                        } else if (status === 'failure') {
                            if (detail) {
                                alert(detail)
                            } else {
                                alert('执行失败')
                            }
                            this.$Spin.hide()
                        } else {
                            let vm = this
                            setTimeout(function() {
                                vm.get_check_service_result(sid, business_name)
                            }, 2000)
                        }
                    } else {
                        this.$Message.error(res.data.msg)
                        alert(res.data.msg)
                        this.$Spin.hide()
                    }
                })
                .catch(err => {
                    alert('接口调用异常')
                    this.$Spin.hide()
                })
        },
        doApply() {
            syncConfigApi(store.state.iterationID, this.app_name_list, this.env_name).then(result => {
                if (result.data.status === 'success') {
                    this.$Message.success(result.data.msg)
                    //调用同步检查接口
                    CheckConfigSyncApi(store.state.iterationID, this.app_name_list, this.env_name).then(result => {
                        if (result.data.status === 'success') {
                            this.$Message.success(result.data.msg)
                            //校验成功后执行发布动作
                            publishApply(store.state.iterationID, this.env_name, this.app_name_list, this.send_email)
                                .then(result => {
                                    if (result.data.status === 'success') {
                                        this.$Message.success(result.data.msg)
                                        let sid = result.data.data.sid
                                        this.get_check_service_result(sid, 'publish_apply')
                                    } else {
                                        alert(result.data.msg)
                                        this.$Spin.hide()
                                    }
                                })
                                .catch(err => {
                                    this.$Spin.hide()
                                })
                        } else {
                            alert(result.data.msg)
                            this.$Spin.hide()
                        }
                    })
                } else {
                    alert(result.data.msg)
                    this.$Spin.hide()
                }
            })
        },
        doArchive() {
            checkConfigConsistentApi(store.state.iterationID, this.app_name_list, 'prod').then(result => {
                if (result.data.status === 'success') {
                    // 执行配置归档
                    fileConfigBranch(store.state.iterationID, this.app_name_list, 'prod').then(result => {
                        if (result.data.status === 'success') {
                            archiveApi(store.state.iterationID)
                                .then(result => {
                                    if (result.data.status === 'success') {
                                        //this.$Message.success(result.data.msg);
                                        let sid = result.data.data.sid
                                        this.get_check_service_result(sid, 'archive')
                                    } else {
                                        alert(result.data.msg)
                                        this.$Spin.hide()
                                    }
                                })
                                .catch(err => {
                                    this.$Spin.hide()
                                })
                        } else {
                            alert(result.data.msg)
                            this.$Spin.hide()
                        }
                    })
                } else {
                    alert(result.data.msg)
                    this.$Spin.hide()
                }
            })
        },
        android_push(item) {
            console.info('>>>> android_push()')
            let android_app_name = item.app_name
            let android_iter_id = this.group + '_' + this.branch_version
            // 只有产线才有推包和检查功能。
            let android_suite_code = 'prod'
            console.info('>>>> android_app_name = ' + android_app_name)
            console.info('>>>> android_iter_id = ' + android_iter_id)
            console.info('>>>> android_suite_code = ' + android_suite_code)

            this.android_is_pushing = true
            // let params = {'app_name':android_app_name, 'iteration_id':android_iter_id, 'suite_code':android_suite_code}
            android_push(android_app_name, android_iter_id, android_suite_code)
                .then(res => {
                    this.android_is_pushing = false
                    if (res.data.status === 'success') {
                        alert('推包成功')
                    } else {
                        alert('推包失败' + res.data.msg)
                    }
                })
                .catch(error => {
                    this.android_is_pushing = false
                    console.error(error)
                    alert('推包异常' + error)
                })
        },
        android_check(item) {
            console.info('>>>> android_check()')
            let android_app_name = item.app_name
            let android_iter_id = this.group + '_' + this.branch_version
            // 只有产线才有推包和检查功能。
            let android_suite_code = 'prod'
            console.info('>>>> android_app_name = ' + android_app_name)
            console.info('>>>> android_iter_id = ' + android_iter_id)
            console.info('>>>> android_suite_code = ' + android_suite_code)

            this.android_is_pushing = true
            // let params = {'app_name':android_app_name, 'iteration_id':android_iter_id, 'suite_code':android_suite_code}
            android_check(android_app_name, android_iter_id, android_suite_code)
                .then(res => {
                    this.android_is_pushing = false
                    if (res.data.status === 'success') {
                        alert('检查成功')
                    } else {
                        alert(res.data.msg)
                    }
                })
                .catch(error => {
                    this.android_is_pushing = false
                    console.error(error)
                    alert('检查异常' + error)
                })
        }
    }
}
</script>

<style scoped></style>
