<template>
    <div>
        <Card>
            <Row style="margin-top: 10px">
                <i-col style="margin: 10px;" span="2">
                    <span style="display: inline-block;width:60px;text-align: right;">组：</span>
                </i-col>
                <Col span="3">
                    <span style="margin: 10px; text-align: left; display: inline-block; ">{{ group }}</span>
                </Col>
                <i-col style="margin: 10px;text-align: right" span="2">
                    <span style="text-align: right; display: inline-block;">分支版本：</span>
                </i-col>
                <Col span="8">
                    <span style="margin: 10px;text-align: left; display: inline-block;">{{ br_name }}</span>
                </Col>
                <i-col v-show="show_tag" style="margin: 10px;text-align: right" span="2">
                    <span style="text-align: right; display: inline-block;">TAG版本：</span>
                </i-col>
                <i-col v-show="show_tag" style="margin: 10px" span="3">
                    <span style="text-align: left; display: inline-block;">{{ tag_name }}</span>
                </i-col>
            </Row>
            <Row style="margin-top: 10px">
                <i-col style="margin: 10px;" span="2">
                    <span style="display: inline-block;width:120px;text-align: right;">环境：</span>
                </i-col>
                <Col span="10">
                    <Select v-model="env" @on-change="envChange">
                        <Option v-for="(item, index) in logic_suite_list" :value="item.logic_suite_code" :key="index">
                            {{ item.logic_suite_label }}
                        </Option>
                    </Select>
                </Col>
                <Col style="margin-left: 20px" span="2">
                    <EnvDescModal ref="env_desc_modal"></EnvDescModal>
                </Col>
                <MobilePublishDiagram></MobilePublishDiagram>
            </Row>
            <Row style="margin-top: 10px">
                <EmailSelect :isMultiple="false" ref="confirm_person">
                    <span style="display: inline-block;width:120px;text-align: right;" slot>确认人：</span>
                </EmailSelect>
            </Row>
            <Row style="margin-top: 10px">
                <EmailSelect :isMultiple="true" ref="cc_person">
                    <span style="display: inline-block;width:120px;text-align: right;" slot>抄送人：</span>
                </EmailSelect>
                <Col style="margin-left: 20px" span="1">
                    <PublishApplyButton
                        :select_data="select_data_info"
                        :app_array="data_info"
                        :action_item="action_item"
                        :iter_id="iter_id"
                        :br_name="br_name"
                        :env="env"
                        :code_type="code_type"
                        :group="group"
                        :apply_content="apply_content"
                        @getApplyParam="getApplyParam"
                    ></PublishApplyButton>
                </Col>
                <Col style="margin-left: 20px" span="2">
                    <CancelPublishApply :iter_id="iter_id"></CancelPublishApply>
                </Col>
                <Col style="margin-left: 20px" span="2">
                    <LastApplyModal
                        ref="last_apply_modal"
                        :branch_version="br_name"
                        :environment="env"
                        :group="group"
                        :tag_name="tag_name"
                    ></LastApplyModal>
                </Col>
                <Col style="margin-left: 20px " span="2">
                    <div>
                        <h2>
                            申请历史<a
                                href="http://paas.hongkou.howbuy.com/report/#/notebook/2GJU8SF6J/paragraph/20210928-163206_681554684?asIframe"
                                target="abc"
                                >查询</a
                            >
                        </h2>
                    </div>
                </Col>
            </Row>

            <Row style="margin-top: 10px">
                <i-col style="margin: 5px" span="2">
                    <span style="text-align: right; display: inline-block;width:120px;">申请说明:</span>
                </i-col>
                <Col style="margin-left: 10px" span="1">
                    <Input
                        v-model="apply_content"
                        type="textarea"
                        :rows="3"
                        placeholder="输入产线说明"
                        style="width: 200px"
                    />
                </Col>
            </Row>
            <Row style="margin-top: 10px">
                <PackageParams
                    :select_data="select_data_info"
                    @set_selected_info="get_package_param_info"
                    :env_list="env_list"
                    :env="env"
                    ref="packageparams"
                ></PackageParams>
            </Row>
            <Row style="margin-top: 10px">
                <PublishApplyTable
                    :iter_id="iter_id"
                    :env="env"
                    :br_name="br_name"
                    :is_refresh="is_publish_apply_refresh"
                    @set_selected_info="get_table_select_info"
                    @set_table_info="get_table_info"
                    ref="publish_apply_table"
                ></PublishApplyTable>
            </Row>
        </Card>
    </div>
</template>

<script>
import EnvDescModal from '../mobile-apply/components/env-desc-modal.vue'
import store from '@/spider-store'
import EmailSelect from '@/spider-components/publish-components/email-select/howbuy-email-select'
import PublishApplyTable from './components/publish-apply-table.vue'
import PublishApplyButton from './components/publish-apply-button.vue'
import PackageParams from './components/package-params.vue'
import CancelPublishApply from './components/cancel-publish-apply.vue'
import MobilePublishDiagram from './components/mobile-publish-diagram.vue'
import LastApplyModal from '../mobile-apply/components/last-apply-modal.vue'
import { getPackageTypeInIterApi } from '@/spider-api/get-iter-info'
import { getLogicSuiteList, getRegionInfo } from '@/spider-api/mgt-env'
import { getNodeVm } from '@/spider-api/mgt-node'

export default {
    name: 'MobilePublishApply',
    data() {
        return {
            br_name: '',
            group: store.state.group,
            data_info: [],
            select_data_info: [],
            iter_id: store.state.iterationID,
            action_item: store.state.iterationID + '_' + 'publish_apply',
            env: '',
            show_tag: false,
            apply_content: '',

            code_type: store.state.code_type,
            tag_name: store.state.tag_name,
            env_list: [
                { value: 'beta', label: 'beta--移动端灰度环境，服务端为产线配置' },
                // { value: 'wgq-hd', label: 'wgq-hd--服务端外高桥灰度环境' },
                { value: 'hd', label: 'hd--服务端灰度环境' },
                { value: 'prod', label: 'prod--生产环境' }
                // {value: 'pre', label: 'pre--移动端灰度环境，服务端为灰度配置'},
            ],
            logic_suite_list: []
        }
    },
    props: {
        is_publish_apply_refresh: Boolean
    },
    components: {
        EnvDescModal,
        EmailSelect,
        PublishApplyTable,
        PublishApplyButton,
        PackageParams,
        CancelPublishApply,
        MobilePublishDiagram,
        LastApplyModal
    },
    methods: {
        init() {
            this.group = store.state.h5_group
            this.br_name = store.state.h5_branch_version
            this.tag_name = store.state.tag_name
            this.show_tag = store.state.show_tag
            this.iter_id = store.state.iterationID
            this.code_type = store.state.code_type
            this.tag_name = store.state.tag_name
            this.env = ''
            this.$refs.confirm_person.init()
            this.$refs.cc_person.init()
            getPackageTypeInIterApi(store.state.iterationID).then(res => {
                console.log(this.is_publish_apply_refresh)

                this.$refs.publish_apply_table.loopTableData()
            })
        },
        get_package_param_info(select_data_info) {
            console.log(select_data_info)
            this.select_data_info = select_data_info
        },
        get_table_select_info(select_data_info) {
            // alert(data_info)
            let select_data = []
            let select_app = []
            console.log('==============选中数据=================')
            console.log(select_data_info)
            select_data_info.forEach(item => {
                if (select_app.includes(item.app_name)) {
                    console.log('去重')
                } else {
                    if (item.package_type == 'ios' || item.package_type == 'android') {
                        item['h5PlatFormCode'] = item.app_name.split('-')[0] + '-h5-res'
                    }
                    select_app.push(item.app_name)
                    select_data.push(item)
                }
            })
            this.select_data_info = select_data
            this.$refs.packageparams.addDisplayed(select_data)
        },
        get_table_info(data_info) {
            console.log('data_info=====' + JSON.stringify(data_info))
            this.data_info = data_info
        },
        envChange() {
            this.$refs.last_apply_modal.lastApplyButtonShow = true
            console.log(this.env)
            this.$refs.publish_apply_table.getTableData(this.env)
        },
        getApplyParam(callback) {
            let basic_params = {
                suite_code: this.env,
                logic_env: this.env,
                iteration_id: this.iter_id,
                proposer: this.$refs.confirm_person.selectValue,
                cc: this.$refs.cc_person.selectValue,
                br_name: this.br_name,
                code_type: this.code_type,
                tag_name: this.tag_name
            }

            if (basic_params.cc == undefined || basic_params.cc == '') {
                this.$Notice.error({ desc: '请选择邮件抄送人', duration: 0 })
                callback([])
                return
            }
            let apply_list = []
            console.log(this.select_data_info)
            // piggy 和 fund 因为需要 起始终止版本，是否静默，所以单独拿出来二次组装
            // cc 如果没有**************，添加 ["<EMAIL>", "<EMAIL>", __ob__: Observer]

            // this.cc = this.add_default_cc(this.cc)
            for (let row of this.select_data_info) {
                let apply_obj = JSON.parse(JSON.stringify(basic_params))
                apply_obj['app_name'] = row.app_name
                apply_obj['repo_path'] = row.git_path
                apply_obj['package_type'] = row.package_type
                if (row.package_type == 'dist') {
                    if (row.begin_ver == undefined || row.begin_ver == '') {
                        this.$Notice.error({ desc: '请选择打包起始版本', duration: 0 })
                        callback([])
                        return
                    }
                    if (row.end_ver == undefined || row.end_ver == '') {
                        this.$Notice.error({ desc: '请填写打包结束版本', duration: 0 })
                        callback([])
                        return
                    }
                    //  if (row.nf_ver == undefined || row.nf_ver == ""){
                    //       this.$Notice.error({desc: "请选择nf绑定版本",duration: 0})
                    //       callback([])
                    //       return
                    //   }
                    apply_obj['begin_ver'] = row.begin_ver
                    apply_obj['end_ver'] = row.end_ver
                    apply_obj['nf_ver'] = row.nf_ver
                    apply_obj['is_silent'] = row.is_silent
                    apply_obj['br_name'] = row.br_name
                    //  apply_obj["nf_app_name"] = "nf-" + row.app_name
                    //  apply_obj["nf_br_name"] = row.nf_ver
                    apply_obj['platform_code'] = row.platform_code

                    // console.log(apply_obj)
                    // apply_list.push(apply_obj)
                } else if (row.package_type == 'ios' || row.package_type == 'android') {
                    if (row.h5_env == undefined || row.h5_env == '') {
                        this.$Notice.error({ desc: '需要选择h5资源 环境', duration: 0 })
                        callback([])
                        return
                    }
                    if (row.app_name != 'crm-ios') {
                        if (row.h5_version == undefined || row.h5_version == '') {
                            this.$Notice.error({ desc: '需要选择h5资源 版本', duration: 0 })
                            callback([])
                            return
                        }
                    }
                    if (row.app_version == undefined || row.app_version == '') {
                        this.$Notice.error({ desc: '打包的app版本可以为空', duration: 0 })
                        callback([])
                        return
                    }
                    apply_obj['h5_env'] = row.h5_env
                    apply_obj['h5_app_name'] = row.h5_app_name
                    apply_obj['h5_version'] = row.h5_version
                    apply_obj['app_version'] = row.app_version
                    apply_obj['h5PlatFormCode'] = row.app_name.split('-')[0] + '-h5-res'
                    if (row.app_name == 'crm-ios') {
                        apply_obj['h5_app_name'] = ''
                        apply_obj['h5_version'] = ''
                    }
                } else if (row.package_type == 'mini-program') {
                    if (row.upload_version == undefined || row.upload_version == '') {
                        this.$Notice.error({ desc: '上传版本不可为空', duration: 0 })
                        callback([])
                        return
                    }
                    if (row.upload_desc == undefined || row.upload_desc == '') {
                        this.$Notice.error({ desc: '上传描述不可为空', duration: 0 })
                        callback([])
                        return
                    }
                    apply_obj['upload_version'] = row.upload_version
                    apply_obj['upload_desc'] = row.upload_desc
                }
                apply_list.push(apply_obj)
            }
            console.log('==============产线申请数据=================')
            console.log(apply_list)
            // this.publish_apply_data = apply_list
            callback(apply_list)
        }
    },
    mounted() {
        let search_data = {
            app_type: 'M'
        }
        getLogicSuiteList(search_data).then(res => {
            this.logic_suite_list = res.data.data['logic_suite_list']
            console.log('>>>>>logic_suite_list' + this.logic_suite_list)
        })
    } //mounted
}
</script>

<style scoped></style>
