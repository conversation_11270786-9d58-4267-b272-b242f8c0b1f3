<template>
    <div>
        <Card style="text-align: left;">
            <div>
                <Row>
                    <i-col style="margin: 10px;text-align: right" span="8">
                        <span style="text-align: right; display: inline-block;">资源包版本范围：</span>
                    </i-col>
                    <Col span="35">
                        <Select
                            v-model="begin_ver"
                            @on-change="fund_version_change"
                            style="width: 30%; display: inline-block;"
                        >
                            <Option
                                v-if="suite_code != 'prod'"
                                v-for="(item, index) in start_ver_list"
                                :value="item.value"
                                :key="index"
                                >{{ item.label }}
                            </Option>
                            <Option
                                disabled
                                v-if="suite_code == 'prod'"
                                v-for="(item, index) in start_ver_list"
                                :value="item.value"
                                :key="index"
                            >
                                {{ item.label }}
                                <span v-if="item.value == begin_ver" style="float:right;color:#ccc">默认</span>
                            </Option>
                        </Select>
                        <Input
                            v-model="end_ver"
                            placeholder="请输入"
                            style="width: 30%; display: inline-block;"
                        ></Input>
                    </Col>
                </Row>
            </div>
            <div v-for="(app, index) in select_data" :key="index">
                <Row>
                    <i-col style="margin: 10px;text-align: right" span="8">
                        <span style="text-align: right; display: inline-block;">{{ app.app_name }}分支信息：</span>
                    </i-col>
                    <Col span="35">
                        <Input
                            v-model="app.br_name"
                            placeholder="请输入"
                            style="width: 30%; display: inline-block;"
                            v-bind:readonly="true"
                            :disabled="true"
                        ></Input>
                    </Col>
                </Row>
            </div>
            <Row>
                <i-col style="margin: 10px;text-align: right" span="8">
                    <span style="text-align: right; display: inline-block;">打包类型:</span>
                </i-col>
                <Col span="35">
                    <Checkbox
                        v-model="is_silent"
                        @on-change="changeIsSilent"
                        :true-value="1"
                        :false-value="0"
                        style="margin-top: 10px"
                        >静默
                    </Checkbox>
                </Col>
            </Row>
        </Card>
    </div>
</template>

<script>
import store from '@/spider-store'
import { findDistVersion, findPlatformDistVersion } from '@/spider-api/h5'
import { getAppBranchInfoApi } from '@/spider-api/mgt-iter'
import { createH5AppBindNfAppApi, getH5AppBindNfAppApi } from '@/spider-api/h5-ci-cd/nf-app'
import { fund_begin_ver, piggy_begin_ver } from "@/const";
export default {
    name: 'H5DistApplyNew',
    props: {
        select_data: {
            type: Array,
            defaults: ''
        },
        suite_code: {
            type: String,
            defaults: ''
        }
    },
    data() {
        return {
            showFundParams: true,
            app_name: '',
            start_ver_list: '',
            app: '',
            end_ver_list: '',
            nf_ver_list: '',
            environment: '',
            stage: '',
            begin_ver: '',
            // suite_code:'',
            end_ver: '',
            is_silent: 0
        }
    },
    methods: {
        changeIsSilent() {
            this.select_data.forEach(element => {
                element.is_silent = this.is_silent
            })
        },
        changeData() {
            this.select_data.forEach(element => {
                element.end_ver = this.end_ver
                element.is_silent = this.is_silent
                element.begin_ver = this.begin_ver
            })
        },
        fund_log(select_data) {
            for (let item in select_data) {
                console.log(select_data[item])
                if (select_data[item]['app_name']) {
                    this.showFundParams = true
                }
            }
        },
        add_one_upper(arr) {
            //arr 确定只有 length = 3
            if (arr[2] < 9) {
                arr[2] = parseInt(arr[2]) + 1 //直接加会变成字符串拼接
            } else if (arr[2] == 9) {
                arr[2] = 0
                arr[1] = parseInt(arr[1]) + 1
                //看看 arr[1] 是否也该进位了？
                if (arr[1] > 9) {
                    arr[1] = 0
                    arr[0] = parseInt(arr[0]) + 1
                }
            }
            let usefulStr = ''
            for (let i of arr) {
                usefulStr = usefulStr + i + '.'
            }
            //去掉最后一个 点
            usefulStr = usefulStr.substring(0, usefulStr.length - 1)
            return usefulStr
        },
        fund_version_change() {
            if (
                this.suite_code == 'beta' ||
                this.suite_code == 'zb' ||
                this.suite_code == 'vps' ||
                this.suite_code == 'pre'
            ) {
                this.end_ver = this.begin_ver
                if (this.begin_ver != '') {
                    this.end_ver = this.begin_ver
                }

                this.select_data.forEach(element => {
                    element.begin_ver = this.begin_ver
                    element.end_ver = this.end_ver
                })
            }
            this.$emit('select_change', this.select_data)
        },
        version_change() {
            this.$emit('select_change', this.select_data)
            if (this.select_data.suite_code == 'prod') {
                this.stage = '上线'
            } else {
                this.stage = '灰度'
            }
        },
        async init() {
            let result = ''
            let version_param = {
                iteration_id: store.state.iterationID,
                br_name: store.state.h5_branch_version,
                suite_code: this.suite_code
            }
            await findPlatformDistVersion(version_param).then(res => {
                this.start_ver_list = []
                result = res.data.data
                for (let item in this.select_data) {
                    let select_data_item = this.select_data[item]
                    if (select_data_item.platform_code == 'fund-h5-res') {
                        this.start_ver_list = res.data.data.fund_begin_ver_list
                        this.end_ver_list = res.data.data.fund_end_ver_list
                        select_data_item['is_silent'] = this.is_silent
                    } else if (select_data_item.platform_code == 'piggy-h5-res') {
                        this.start_ver_list = res.data.data.piggy_begin_ver_list
                        this.end_ver_list = res.data.data.piggy_end_ver_list
                        select_data_item['is_silent'] = this.is_silent
                    }
                    //start_ver影响end_ver

                    let Temp = this.start_ver_list[0].value
                    let TempArr = Temp.split('.')
                    select_data_item.end_ver = this.add_one_upper(TempArr)
                    this.end_ver = select_data_item.end_ver
                    console.log(select_data_item.end_ver)

                    if (select_data_item.suite_code == 'prod') {
                        this.suite_code = select_data_item.suite_code
                        if (select_data_item.platform_code == 'fund-h5-res') {
                            select_data_item.begin_ver = fund_begin_ver
                            this.begin_ver = fund_begin_ver
                        } else if (select_data_item.platform_code == 'piggy-h5-res') {
                            select_data_item.begin_ver = piggy_begin_ver
                            this.begin_ver = piggy_begin_ver
                        }
                    }
                    if (
                        this.suite_code == 'beta' ||
                        this.suite_code == 'zb' ||
                        this.suite_code == 'vps' ||
                        this.suite_code == 'pre'
                    ) {
                        this.end_ver = this.begin_ver
                        if (this.begin_ver != '') {
                            this.end_ver = this.begin_ver
                        }
                    }

                    this.$emit('select_change', this.select_data)
                }
            })
        }
    },
    created() {
        this.init()
        // this.getNfBranch()
    },
    watch: {
        end_ver(val) {
            this.select_data.forEach(element => {
                element.end_ver = this.end_ver
            })
        },
        select_data(val) {
            this.changeData()
        }
    },
    mounted() {}
}
</script>

<style scoped></style>
