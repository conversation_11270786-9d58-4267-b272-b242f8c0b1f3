<template>
    <div>
        <Card>
            <Row>
                <i-col style="margin: 10px;text-align: right; white-space: nowrap;" span="1">
                    <span style="text-align: right; display: inline-block; font-weight: bold;">{{
                        params.app_name
                    }}</span>
                </i-col>
            </Row>
            <Row>
                <i-col style="margin: 10px;text-align: right" span="6">
                    <span style="text-align: right; display: inline-block;">上传版本：</span>
                </i-col>
                <Col span="14">
                    <Input v-model="params.upload_version" placeholder="请输入"></Input>
                </Col>
            </Row>
            <Row>
                <i-col style="margin: 10px;text-align: right" span="6">
                    <span style="text-align: right; display: inline-block;">上传描述：</span>
                </i-col>
                <Col span="14">
                    <Input
                        v-model="params.upload_desc"
                        type="textarea"
                        :rows="3"
                        placeholder="上传描述"
                        style="width: 200px"
                    />
                </Col>
            </Row>
        </Card>
    </div>
</template>

<script>
export default {
    name: 'MiniProgramPackageParams',
    props: {
        params: {
            type: Object,
            default: () => {}
        }
    }
}
</script>

<style scoped></style>
