<template>
    <div>
        <Row>
            <Col v-for="row in select_data" span="9" style="margin-right: 10px">
                <div v-if="row.package_type == 'mini-program'">
                    <MiniProgramPackageParams :params="row"></MiniProgramPackageParams>
                </div>
                <!-- <div v-if="row.package_type == 'dist'&& displayed.filter(item => item === row.platform_code).length == 1">
            <H5DistApply ref="h5_dist_apply" :select_data="row" @select_change='get_select_version'></H5DistApply>
          </div> -->
                <div v-if="row.package_type == 'ios' || row.package_type == 'android'">
                    <AppPackageParams :params="row" :env_list="env_list"></AppPackageParams>
                </div>
            </Col>
            <Col span="9" style="margin-right: 10px">
                <div v-if="fund_dist == true">
                    <H5DistApplyNew
                        ref="h5_dist_apply"
                        :select_data="fund_platform"
                        :suite_code="env"
                        @select_change="get_select_version"
                    ></H5DistApplyNew>
                </div>
            </Col>
            <Col span="9" style="margin-right: 10px">
                <div v-if="piggy_dist == true">
                    <H5DistApplyNew
                        ref="h5_dist_apply"
                        :select_data="piggy_platform"
                        :suite_code="env"
                        @select_change="get_select_version"
                    ></H5DistApplyNew>
                </div>
            </Col>
        </Row>
    </div>
</template>

<script>
import MiniProgramPackageParams from './mini-program-package-params.vue'
import AppPackageParams from './app-package-params.vue'
import H5DistApply from './h5-dist-apply.vue'
import H5DistApplyNew from './h5-dist-apply-new.vue'
import { findDistVersion, getLastProdDistInfo, findPlatformDistVersion, getLastProdDistAppInfo } from '@/spider-api/h5'
import store from '@/spider-store'
import { fund_begin_ver, piggy_begin_ver } from "@/const";
export default {
    name: 'PackageParams',
    data() {
        return {
            package_params: [],
            displayed: [],
            fund_platform: [],
            piggy_platform: [],
            fund_dist: false,
            piggy_dist: false
        }
    },
    components: {
        MiniProgramPackageParams,
        H5DistApply,
        AppPackageParams,
        H5DistApplyNew
    },
    props: {
        select_data: {
            type: Array,
            default: () => []
        },
        env_list: {
            type: Array,
            default: () => []
        },
        env: {
            type: String,
            default: ''
        }
    },
    methods: {
        miniProgramParams() {},
        add_one_upper(arr) {
            //arr 确定只有 length = 3
            if (arr[2] < 9) {
                arr[2] = parseInt(arr[2]) + 1 //直接加会变成字符串拼接
            } else if (arr[2] == 9) {
                arr[2] = 0
                arr[1] = parseInt(arr[1]) + 1
                //看看 arr[1] 是否也该进位了？
                if (arr[1] > 9) {
                    arr[1] = 0
                    arr[0] = parseInt(arr[0]) + 1
                }
            }
            let usefulStr = ''
            for (let i of arr) {
                usefulStr = usefulStr + i + '.'
            }
            //去掉最后一个 点
            usefulStr = usefulStr.substring(0, usefulStr.length - 1)
            return usefulStr
        },
        get_select_version(select_data) {
            console.log('子组件传回来的数据')
            console.log(select_data)
        },
        setPackageParams(select_data) {
            for (let row in select_data) {
                if (row.package_type == 'mini-program') {
                    let mini_ = { upload_version: '', upload_desc: '' }
                }
            }
        },
        async addDisplayed(rows) {
            let diff_fund = []
            let diff_piggy = []
            let diff = []
            for (let row in rows) {
                if (rows[row]['platform_code'] == 'fund-h5-res') {
                    diff_fund.push(rows[row]['app_name'])
                } else if (rows[row]['platform_code'] == 'piggy-h5-res') {
                    diff_piggy.push(rows[row]['app_name'])
                }
            }
            diff = { 'fund-h5-res': diff_fund, 'piggy-h5-res': diff_piggy }
            console.log(diff)
            let version_param = {
                iteration_id: store.state.iterationID,
                br_name: store.state.h5_branch_version,
                suite_code: this.env
            }
            this.fund_platform = []
            this.piggy_platform = []
            this.$Spin.show({
                render: h => {
                    return h('div', [
                        h('Icon', {
                            class: 'demo-spin-icon-load',
                            props: {
                                type: 'ios-loading',
                                size: 18
                            }
                        }),
                        h('div', '发布分支计算中请稍等。。。')
                    ])
                }
            })
            //   setTimeout(() => {
            //   this.$Spin.hide()
            // }, 1000);
            getLastProdDistAppInfo(diff).then(res => {
                let result = res.data.data
                rows = rows.concat(result.ci_info)
                console.log(version_param)
                findPlatformDistVersion(version_param).then(res => {
                    console.log(res.data.data)
                    for (let it in rows) {
                        let start_ver_list = []
                        if (rows[it].platform_code == 'fund-h5-res') {
                            start_ver_list = res.data.data.fund_begin_ver_list
                            let Temp = start_ver_list[0].value
                            let TempArr = Temp.split('.')
                            rows[it].end_ver = this.add_one_upper(TempArr)
                        } else if (rows[it].platform_code == 'piggy-h5-res') {
                            start_ver_list = res.data.data.piggy_begin_ver_list
                            let Temp = start_ver_list[0].value
                            let TempArr = Temp.split('.')
                            rows[it].end_ver = this.add_one_upper(TempArr)
                        }
                        if (rows[it].package_type == 'dist' && rows[it].platform_code == 'fund-h5-res') {
                            rows[it].begin_ver = fund_begin_ver
                            this.fund_platform.push(rows[it])
                        } else if (rows[it].package_type == 'dist' && rows[it].platform_code == 'piggy-h5-res') {
                            rows[it].begin_ver = piggy_begin_ver
                            this.piggy_platform.push(rows[it])
                            console.log(this.displayed)
                        }
                    }
                    if (this.fund_platform.length > 0) {
                        this.fund_dist = true
                    } else {
                        this.fund_dist = false
                    }
                    if (this.piggy_platform.length > 0) {
                        this.piggy_dist = true
                    } else {
                        this.piggy_dist = false
                    }
                    this.fund_platform.sort(function(a, b) {
                        return a.app_name.length - b.app_name.length
                    })
                    this.piggy_platform.sort(function(a, b) {
                        return a.app_name.length - b.app_name.length
                    })
                    this.$emit('set_selected_info', rows)
                    this.$Spin.hide()
                })
            })

            console.log(this.fund_platform)
        }
    }
}
</script>

<style scoped></style>
