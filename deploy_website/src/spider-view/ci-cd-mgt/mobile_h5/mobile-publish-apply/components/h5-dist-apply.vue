<template>
    <div>
        <Card>
            <Row>
                <i-col style="margin: 10px;text-align: right" span="8">
                    <span style="text-align: right; display: inline-block;">{{ select_data.app_name }}起始版本：</span>
                </i-col>
                <Col span="14">
                    <Select v-model="select_data.begin_ver" @on-change="fund_version_change">
                        <Option
                            v-if="select_data.suite_code != 'prod'"
                            v-for="(item, index) in start_ver_list"
                            :value="item.value"
                            :key="index"
                            >{{ item.label }}
                        </Option>
                        <Option
                            disabled
                            v-if="select_data.suite_code == 'prod'"
                            v-for="(item, index) in start_ver_list"
                            :value="item.value"
                            :key="index"
                        >
                            {{ item.label }}
                            <span v-if="item.value == '23.4.0'" style="float:right;color:#ccc">默认</span>
                        </Option>
                    </Select>
                </Col>
            </Row>
            <Row>
                <i-col style="margin: 10px;text-align: right" span="8">
                    <span style="text-align: right; display: inline-block;">{{ select_data.app_name }}结束版本：</span>
                </i-col>
                <Col span="14">
                    <Input v-model="select_data.end_ver" placeholder="请输入"> </Input>
                </Col>
            </Row>
            <Row>
                <i-col style="margin: 10px;text-align: right" span="8">
                    <span style="text-align: right; display: inline-block;">nf-{{ select_data.app_name }}版本：</span>
                </i-col>
                <Col span="14">
                    <Select v-model="select_data.nf_ver" @on-change="version_change" filterable>
                        <Option
                            v-for="(item, index) in nf_ver_list"
                            :value="item.value"
                            :label="item.label"
                            :key="index"
                            >{{ item.label }}
                        </Option>
                    </Select>
                </Col>
            </Row>
            <Row type="flex" justify="end" class="code-row-bg">
                <Col span="15">
                    <p style="color: red;">nf-{{ select_data.app_name }}会默认选择线上版本绑定</p>
                </Col>
            </Row>
            <Row>
                <i-col style="margin: 10px;text-align: right" span="8">
                    <span style="text-align: right; display: inline-block;">打包类型：</span>
                </i-col>
                <Col span="14">
                    <Checkbox v-model="select_data.is_silent" true-value="1" false-value="0" style="margin-top: 10px"
                        >静默
                    </Checkbox>
                </Col>
            </Row>
        </Card>
    </div>
</template>

<script>
import store from '@/spider-store'
import { findDistVersion } from '@/spider-api/h5'
import { getAppBranchInfoApi } from '@/spider-api/mgt-iter'
import { createH5AppBindNfAppApi, getH5AppBindNfAppApi } from '@/spider-api/h5-ci-cd/nf-app'
export default {
    name: 'H5DistApply',
    props: {
        // environment: {
        // type: String,
        // defaults: ''
        // },
        select_data: {
            type: Object,
            defaults: ''
        }
    },
    data() {
        return {
            showFundParams: true,
            app_name: '',
            start_ver_list: '',
            end_ver_list: '',
            nf_ver_list: '',
            environment: '',
            stage: ''
            // end_ver:'',
            // start_ver:'',
            // nf_ver:'',
            // is_silent:'',
        }
    },
    methods: {
        fund_log(select_data) {
            for (let item in select_data) {
                console.log(select_data[item])
                if (select_data[item]['app_name']) {
                    this.showFundParams = true
                }
            }
        },
        add_one_upper(arr) {
            //arr 确定只有 length = 3
            if (arr[2] < 9) {
                arr[2] = parseInt(arr[2]) + 1 //直接加会变成字符串拼接
            } else if (arr[2] == 9) {
                arr[2] = 0
                arr[1] = parseInt(arr[1]) + 1
                //看看 arr[1] 是否也该进位了？
                if (arr[1] > 9) {
                    arr[1] = 0
                    arr[0] = parseInt(arr[0]) + 1
                }
            }
            let usefulStr = ''
            for (let i of arr) {
                usefulStr = usefulStr + i + '.'
            }
            //去掉最后一个 点
            usefulStr = usefulStr.substring(0, usefulStr.length - 1)
            return usefulStr
        },
        fund_version_change() {
            if (
                this.select_data.suite_code == 'beta' ||
                this.select_data.suite_code == 'vps' ||
                this.select_data.suite_code == 'pre'
            ) {
                if (this.select_data.begin_ver != '') {
                    this.select_data.end_ver = this.select_data.begin_ver
                }
            }
            this.$emit('select_change', this.select_data)
        },
        version_change() {
            this.$emit('select_change', this.select_data)
            if (this.select_data.suite_code == 'prod') {
                this.stage = '上线'
            } else {
                this.stage = '灰度'
            }
            createH5AppBindNfAppApi({
                iteration_id: store.state.iterationID,
                app_name: this.select_data.app_name,
                nf_br_name: this.select_data.nf_ver,
                nf_app_name: 'nf-' + this.select_data.app_name,
                stage: this.stage
            })
                .then(res => {
                    //this.$emit("nf_ver",nf_ver)
                    this.$Message.success(res.data.msg)
                })
                .catch(err => {
                    console.log('============= err =========')
                    console.log(err)
                    this.$Message.error(err)
                })
        },
        init() {
            let version_param = {
                iteration_id: store.state.iterationID,
                br_name: this.select_data.br_name,
                suite_code: this.select_data.suite_code
            }
            findDistVersion(version_param).then(res => {
                if (this.select_data.app_name == 'fund') {
                    this.start_ver_list = res.data.data.fund_begin_ver_list
                    this.end_ver_list = res.data.data.fund_end_ver_list
                    this.nf_ver_list = res.data.data.nf_fund_ver_list
                } else if (this.select_data.app_name == 'piggy') {
                    this.start_ver_list = res.data.data.piggy_begin_ver_list
                    this.end_ver_list = res.data.data.piggy_end_ver_list
                    this.nf_ver_list = res.data.data.nf_piggy_ver_list
                }
                //start_ver影响end_ver
                if (this.select_data.suite_code == 'prod') {
                    if (this.select_data.app_name == 'fund') {
                        this.select_data.begin_ver = '23.4.0'
                    } else if (this.select_data.app_name == 'piggy') {
                        this.select_data.begin_ver = '4.4.0'
                    }
                    console.log('====== prod ==========')
                    console.log(this.select_data.app_name)
                    console.log(this.start_ver_list)
                    // 记得放开
                    // this.select_data.start_ver = '21.2.0'
                    if (this.start_ver_list.length > 0) {
                        //找到最新的一条记录
                        let Temp = this.start_ver_list[0].value
                        let TempArr = Temp.split('.')
                        this.select_data.end_ver = this.add_one_upper(TempArr)
                    }
                }
                if (
                    this.select_data.suite_code == 'beta' ||
                    this.select_data.suite_code == 'vps' ||
                    this.select_data.suite_code == 'pre'
                ) {
                    if (this.select_data.begin_ver != '') {
                        this.select_data.end_ver = this.select_data.begin_ver
                    }
                }
            })
        },
        getNfBranch() {
            getH5AppBindNfAppApi({
                iteration_id: store.state.iterationID,
                app_name: this.select_data.app_name,
                suite_code: this.select_data.suite_code
            }).then(res => {
                this.select_data.nf_ver = res.data.data.nf_br_name
            })
        }
    },
    created() {
        this.init()
        // this.getNfBranch()
    },
    mounted() {}
}
</script>

<style scoped></style>
