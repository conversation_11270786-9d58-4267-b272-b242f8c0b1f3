<template>
    <div>
        <Button style="margin-left: 2ex" type="dashed" @click="showImg"
            >移动端上线示意图 <Icon type="ios-alert-outline" size="18" />
        </Button>
        <Modal title=" " width="1000" v-model="show_img_val" :mask-closable="true">
            <div style="width: 100%">
                <img :src="img_url" style="width: 100%" />
            </div>
        </Modal>
    </div>
</template>

<script>
export default {
    name: 'MobilePublishDiagram',
    data() {
        return {
            img_url: require('../../../../../img/移动端上线示意图.png'),
            show_img_val: false
        }
    },
    methods: {
        showImg() {
            this.show_img_val = true
        }
    }
}
</script>

<style scoped></style>
