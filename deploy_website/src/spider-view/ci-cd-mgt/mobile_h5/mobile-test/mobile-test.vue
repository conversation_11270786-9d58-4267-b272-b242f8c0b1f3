<template>
    <div>
        <Card>
            <Row>
                <i-col style="margin: 10px;text-align: right" span="1">
                    <span style="text-align: right; display: inline-block;">组：</span>
                </i-col>
                <i-col style="margin: 10px" span="3">
                    <span style="text-align: left; display: inline-block;">{{ group }}</span>
                </i-col>
                <i-col style="margin: 10px;text-align: right" span="2">
                    <span style="text-align: right; display: inline-block;">分支版本：</span>
                </i-col>
                <i-col style="margin: 10px" span="3">
                    <span style="text-align: left; display: inline-block;">{{ branch_version }}</span>
                </i-col>
                <i-col v-show="show_tag" style="margin: 10px;text-align: right" span="2">
                    <span style="text-align: right; display: inline-block;">TAG版本：</span>
                </i-col>
                <i-col v-show="show_tag" style="margin: 10px" span="3">
                    <span style="text-align: left; display: inline-block;">{{ tag_name }}</span>
                </i-col>
                <i-col style="margin: 10px" span="7">
                    <span style="text-align: left; display: inline-block;color: red"
                        >注：若以下应用的配置存放路径有改变，请及时告知</span
                    >
                </i-col>
            </Row>
            <Row>
                <Collapse v-model="collapse_value">
                    <Panel name="static" v-show="show_static" :hideArrow="true">
                        PC发布
                        <p slot="content">
                            <MobileTestStatic :is_refresh="is_test_refresh" ref="mobileTestStatic"></MobileTestStatic>
                        </p>
                    </Panel>
                    <Panel name="remote" v-show="show_remote" :hideArrow="true">
                        移动端远端发布
                        <p slot="content">
                            <MobileTestRemote :is_refresh="is_test_refresh" ref="mobileTestRemote"></MobileTestRemote>
                        </p>
                    </Panel>
                    <Panel name="dist" v-show="show_dist" :hideArrow="true">
                        H5资源包发布
                        <p slot="content">
                            <MobileTestDist :is_refresh="is_test_refresh" ref="mobileTestDist"></MobileTestDist>
                        </p>
                    </Panel>
                    <Panel name="ios" v-show="show_ios" :hideArrow="true">
                        ios资源包发布
                        <p slot="content">
                            <MobileTestApp :is_refresh="is_test_refresh" ref="iosMobileTestApp"></MobileTestApp>
                        </p>
                    </Panel>
                    <Panel name="android" v-show="show_android" :hideArrow="true">
                        安卓资源包发布
                        <p slot="content">
                            <MobileTestApp :is_refresh="is_test_refresh" ref="androidMobileTestApp"></MobileTestApp>
                        </p>
                    </Panel>
                    <Panel name="android" v-show="show_app_component" :hideArrow="true">
                        客户端组件包发布
                        <p slot="content">
                            <MobileTestAppComponent
                                :is_refresh="is_test_refresh"
                                ref="mobileTestAppComponent"
                            ></MobileTestAppComponent>
                        </p>
                    </Panel>
                    <Panel name="param-remote" v-show="show_param_remote" :hideArrow="true">
                        小程序&SSR远端发布
                        <p slot="content">
                            <MobileTestParamRemote
                                :is_refresh="is_test_refresh"
                                ref="mobileTestParamRemote"
                            ></MobileTestParamRemote>
                        </p>
                    </Panel>
                    <Panel name="mini-program" v-show="show_mini_program" :hideArrow="true">
                        小程序发布
                        <p slot="content">
                            <MobileTestMiniProgram
                                :is_refresh="is_test_refresh"
                                ref="mobileTestMiniProgram"
                            ></MobileTestMiniProgram>
                        </p>
                    </Panel>
                </Collapse>
            </Row>
        </Card>
    </div>
</template>

<script>
import store from '@/spider-store'
import MobileTestStatic from './mobile-test-static.vue'
import MobileTestRemote from './mobile-test-remote.vue'
import MobileTestDist from './mobile-test-dist.vue'
import MobileTestApp from './mobile-test-app.vue'
import MobileTestParamRemote from './mobile-test-param-remote.vue'
import MobileTestMiniProgram from './mobile-test-mini-program.vue'
import MobileTestAppComponent from './mobile-test-app-component.vue'

import { getPackageTypeInIterApi } from '@/spider-api/get-iter-info'

export default {
    name: 'MobileTest',
    components: {
        MobileTestStatic,
        MobileTestRemote,
        MobileTestDist,
        MobileTestApp,
        MobileTestParamRemote,
        MobileTestMiniProgram,
        MobileTestAppComponent
    },
    props: {
        is_test_refresh: Boolean
    },
    data() {
        return {
            collapse_value: [], //(store.state.h5_app_names)?'fund':1,
            group: store.state.h5_group,
            branch_version: store.state.h5_branch_version,
            tag_name: '',
            show_tag: false,
            show_static: false,
            show_remote: false,
            show_dist: false,
            show_ios: false,
            show_android: false,
            show_param_remote: false,
            show_mini_program: false,
            show_app_component: false
        }
    },
    mounted() {
        // this.init()
    },
    computed: {},
    methods: {
        //根据分支中选择不同的应用，控制不同Panel展开
        init() {
            this.show_dist = false
            this.show_remote = false
            this.show_static = false
            this.show_ios = false
            this.show_android = false
            this.show_param_remote = false
            this.show_app_component = false
            this.group = store.state.h5_group
            this.branch_version = store.state.h5_branch_version
            // 根据迭代下存在的类型选择 展开那些页面
            getPackageTypeInIterApi(store.state.iterationID).then(res => {
                // console.log(res.data.data)
                this.collapse_value = res.data.data['package_type_list']
                // console.log(this.collapse_value)

                for (let package_type of this.collapse_value) {
                    // console.log(">>>> package_type:" + package_type)
                    if (package_type == 'dist') {
                        this.show_dist = true
                        this.$refs.mobileTestDist.init()
                    } else if (package_type == 'remote') {
                        this.show_remote = true
                        this.$refs.mobileTestRemote.init()
                    } else if (package_type == 'static') {
                        this.show_static = true
                        this.$refs.mobileTestStatic.init()
                    } else if (package_type == 'ios') {
                        this.show_ios = true
                        this.$refs.iosMobileTestApp.init()
                    } else if (package_type == 'android') {
                        this.show_android = true
                        this.$refs.androidMobileTestApp.init()
                    } else if (package_type == 'param-remote' || package_type == 'ssr-remote') {
                        // console.log(">>>> ssr-remote!!!")
                        this.show_param_remote = true
                        this.$refs.mobileTestParamRemote.init()
                    } else if (package_type == 'mini-program') {
                        this.show_mini_program = true
                        this.$refs.mobileTestMiniProgram.init()
                    } else if (package_type == 'android-com' || package_type == 'ios-com') {
                        this.show_app_component = true
                        this.$refs.mobileTestAppComponent.init()
                    }
                }
            })
        }
    }
}
</script>

<style scoped></style>
