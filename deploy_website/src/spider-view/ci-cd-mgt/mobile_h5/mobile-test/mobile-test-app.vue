<template>
    <Card>
        <Row v-if="code_type == 'tag'">
            <i-col style="margin: 10px;text-align: right" span="2">
                <span style="text-align: right; display: inline-block;">TAG版本：</span>
            </i-col>
            <i-col style="margin: 10px" span="3">
                <span style="text-align: left; display: inline-block;">{{ tag_name }}</span>
            </i-col>
        </Row>
        <Row>
            <Col span="2">
                <TestPublish
                    :publish_data="date_info"
                    :action_item="publish_action_item"
                    :iter_id="iter_id"
                    :br_name="br_name"
                    :code_type="code_type"
                    :tag_name="tag_name"
                    :package_type="app_type_list"
                >
                </TestPublish>
            </Col>
        </Row>
        <Row>
            <CITable
                :columns="columns"
                :is_refresh="is_refresh"
                @set_table_info="get_table_info"
                ref="ciTable"
            ></CITable>
        </Row>
        <Row>
            <DownloadResource ref="downloadResource"> </DownloadResource>
        </Row>
    </Card>
</template>

<script>
import store from '@/spider-store'
import { getInfoNoticeShowTime, userActionGet } from '@/spider-api/h5'

import { formatDate } from '@/spider-api/h5-common'

import CITable from '@/spider-components/spider-table/mobile-table'
import TestPublish from '@/spider-components/spider-buttons/test-publish'
import DownloadResource from '@/spider-components/spider-buttons/download-resource'
import { refreshExecStatusApi, stopTask } from '../mobile-utils/utils.js'

export default {
    name: 'MobileTestApp',
    components: {
        CITable,
        TestPublish,
        DownloadResource
    },
    props: {
        is_refresh: Boolean
        // app_suite_info: Object,
        //这里是table中的数据
    },

    data() {
        return {
            publish_action_item: store.state.iterationID + '_' + 'test_app_publish',
            lastElapsed: '-',
            currentElapsed: '准备中',
            showDownloadModal: false,
            tag_name: '',
            downloadAppList: [],
            iter_id: store.state.iterationID,
            br_name: '',
            app_type_list: ['ios', 'android'],
            code_type: '',
            date_info: [],
            columns: [
                {
                    type: 'selection',
                    width: 50
                },
                { title: '应用', key: 'app_name' },
                {
                    title: '发布状态',
                    key: 'test_publish_status',
                    render: (h, params) => {
                        let stat_dict = {
                            running: '执行中',
                            success: '执行成功',
                            failure: '执行失败',
                            publish_running: '发布中',
                            publish_success: '发布成功',
                            publish_failure: '发布失败',
                            aborted: '已终止'
                        }
                        let status = params.row.test_publish_status
                        if (stat_dict.hasOwnProperty(status)) {
                            var status_display = stat_dict[status]
                        } else {
                            var status_display = status
                        }
                        let action_type = ''
                        if (status == 'publish_running') {
                            action_type = 'remote'
                        }
                        if (action_type) {
                            return h('div', [
                                h('a', [
                                    h(
                                        'Poptip',
                                        {
                                            props: {
                                                transfer: true,
                                                trigger: 'click',
                                                title: '上次耗时: ' + this.last_elapsed,
                                                content: '当前耗时：' + this.current_elapsed,
                                                size: 'small'
                                            },
                                            on: {
                                                'on-popper-show': () => {
                                                    h5ElapseStatsApiGet(
                                                        params.row.app_name,
                                                        action_type,
                                                        store.state.h5_branch_version
                                                    ).then(res => {
                                                        if (res.data.status === 'success') {
                                                            this.last_elapsed = res.data.data['last_elapsed']
                                                            this.current_elapsed = res.data.data['current_elapsed']
                                                        }
                                                    })
                                                },
                                                'on-popper-hide': () => {
                                                    this.last_elapsed = 0
                                                    this.current_elapsed = 0
                                                }
                                            }
                                        },
                                        status_display
                                    )
                                ])
                            ])
                        } else if (status == 'publish_success') {
                            return h('p', { style: { color: 'green' } }, status_display)
                        } else if (status == 'publish_failure') {
                            return h('p', { style: { color: 'red' } }, status_display)
                        } else {
                            return h('p', { style: { color: '#515a6e' } }, status_display)
                        }
                    }
                },
                {
                    title: '发布开始时间',
                    key: 'op_time',
                    render: (h, params) => {
                        let value = params.row.op_time
                        if (value == '' || value == null) {
                        } else {
                            value = formatDate(new Date(params.row.op_time), 'yyyy-MM-dd hh:mm:ss')
                        }
                        return h('div', value)
                    }
                },
                {
                    title: '最后一次编译结束时间',
                    key: 'end_time',
                    //sortable: true,
                    render: (h, params) => {
                        let value = params.row.end_time
                        if (value == '' || value == null) {
                        } else {
                            value = formatDate(new Date(params.row.end_time), 'yyyy-MM-dd hh:mm:ss')
                        }
                        return h('div', value)
                    }
                },
                { title: '操作人', key: 'op_user' },
                {
                    title: '操作',
                    key: 'action',
                    width: 300,
                    align: 'center',
                    render: (h, params) => {
                        return h('div', [
                            h(
                                'Button',
                                {
                                    props: {
                                        type: 'default',
                                        size: 'small'
                                    },
                                    style: {
                                        marginRight: '5px'
                                    },
                                    on: {
                                        click: () => {
                                            let job_url = params.row.publish_url
                                            if (job_url != null) {
                                                window.open(params.row.publish_url)
                                            } else {
                                                this.$Notice.info({
                                                    title: '发布详情',
                                                    message: params.row.publish_message,
                                                    duration: getInfoNoticeShowTime()
                                                })
                                            }
                                        }
                                    }
                                },
                                '发布页'
                            ),
                            h(
                                'Button',
                                {
                                    props: {
                                        type: 'default',
                                        size: 'small'
                                    },
                                    style: {
                                        marginRight: '5px',
                                        display:
                                            params.row.app_name == 'nf-fund' || params.row.app_name == 'nf-piggy'
                                                ? 'none'
                                                : 'inline-block'
                                    },
                                    on: {
                                        click: () => {
                                            if (params.row.suite_code == undefined) {
                                                this.$Notice.info({
                                                    desc: '未绑定测试环境',
                                                    duration: getInfoNoticeShowTime()
                                                })
                                                return
                                            }
                                            this.$refs.downloadResource.showDownload(
                                                this.iter_id,
                                                params.row.app_name,
                                                params.row.suite_code
                                            )
                                        }
                                    }
                                },
                                '下载'
                            ),
                            h(
                                'Button',
                                {
                                    props: { type: 'warning', size: 'small' },
                                    style: {
                                        marginRight: '5px'
                                    }
                                },
                                [
                                    h(
                                        'Poptip',
                                        {
                                            props: {
                                                confirm: true,
                                                transfer: true,
                                                title: '你确定要强制刷新吗?',
                                                type: 'error',
                                                size: 'small'
                                            },
                                            on: {
                                                'on-ok': () => {
                                                    refreshExecStatusApi({
                                                        app_name: params.row.app_name,
                                                        iter_id: this.iter_id,
                                                        refresh_action_item_list: [this.publish_action_item]
                                                    })
                                                },
                                                'on-cancel': function() {}
                                            }
                                        },
                                        '刷新'
                                    )
                                ]
                            ),
                            h(
                                'Button',
                                {
                                    props: { type: 'error', size: 'small' },
                                    style: {
                                        marginRight: '5px'
                                    }
                                },
                                [
                                    h(
                                        'Poptip',
                                        {
                                            props: {
                                                confirm: true,
                                                transfer: true,
                                                title: '你确定要强制中止吗?',
                                                type: 'error',
                                                size: 'small'
                                            },
                                            on: {
                                                'on-ok': () => {
                                                    stopTask({
                                                        app_name: params.row.app_name,
                                                        iter_id: this.iter_id,
                                                        action_item_list: [
                                                            this.compile_action_item,
                                                            this.publish_action_item
                                                        ]
                                                    })
                                                },
                                                'on-cancel': function() {}
                                            }
                                        },
                                        '终止'
                                    )
                                ]
                            )
                        ])
                    }
                }
            ]
        }
    },
    mounted() {
        //this.initTable()
    },
    methods: {
        init() {
            //this.$refs.ciTable.selected_table=[]
            console.log('============ dist init ============')
            this.br_name = store.state.h5_branch_version
            this.iter_id = store.state.iterationID
            this.code_type = store.state.code_type
            this.tag_name = 'tag_' + this.br_name
            this.publish_action_item = this.iter_id + '_' + 'test_app_publish'
            let vm = this
            let selectTableData = []
            this.get_user_action(this.publish_action_item).then(function(data) {
                if (!data) {
                    console.info('用户行为数据查询失败！！')
                } else {
                    selectTableData =
                        data.length > 0
                            ? JSON.parse(
                                  data
                                      .replace(/False/g, 'false')
                                      .replace(/True/g, 'true')
                                      .replace(/'/g, '"')
                                      .replace(/None/g, null)
                              )
                            : data
                }
                selectTableData = data
                vm.$refs.ciTable.get_ci_info(vm.iter_id, [vm.publish_action_item], vm.app_type_list, selectTableData)
            })

            //console.log(this.$refs.ciTable.my_table.selection)
        },
        get_user_action(action_item) {
            return new Promise(function(resolve, reject) {
                userActionGet(action_item)
                    .then(res => {
                        resolve(res.data.data)
                    })
                    .catch(err => {
                        reject(false)
                    })
            })
        },
        get_table_info(date_info) {
            //alert(date_info)
            this.date_info = date_info
        }
    }
}
</script>

<style scoped></style>
