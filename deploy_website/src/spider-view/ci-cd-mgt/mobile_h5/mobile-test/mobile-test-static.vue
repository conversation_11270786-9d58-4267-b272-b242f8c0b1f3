<template>
    <Card>
        <Row>
            <Col v-if="code_type != 'tag'" span="2">
                <Compile
                    :table_data="date_info"
                    :br_name="br_name"
                    :action_item="compile_action_item"
                    :iter_id="iter_id"
                ></Compile>
            </Col>
            <Col span="2">
                <TestPublish
                    :publish_data="date_info"
                    :action_item="publish_action_item"
                    :iter_id="iter_id"
                    :br_name="br_name"
                    :package_type="app_type_list"
                >
                </TestPublish>
            </Col>
        </Row>
        <Row>
            <CITable
                :columns="columns"
                :is_refresh="is_refresh"
                @set_table_info="get_table_select_info"
                ref="ciTable"
            ></CITable>
        </Row>
    </Card>
</template>

<script>
import Compile from '@/spider-components/spider-buttons/compile'
import CITable from '@/spider-components/spider-table/mobile-table'
import TestPublish from '@/spider-components/spider-buttons/test-publish'
import store from '@/spider-store'
import { getInfoNoticeShowTime, userActionGet } from '@/spider-api/h5'
import { h5ElapseStatsApiGet } from '@/spider-api/h5-stats'
import { formatDate } from '@/spider-api/h5-common'

import { refreshExecStatusApi, stopTask } from '../mobile-utils/utils.js'

export default {
    name: 'MobileTestStatic',
    components: {
        CITable,
        Compile,
        TestPublish
    },
    props: {
        is_refresh: Boolean
    },

    data() {
        return {
            compile_action_item: store.state.iterationID + '_' + 'test_static_compile',
            publish_action_item: store.state.iterationID + '_' + 'test_static_publish',
            date_info: [],
            br_name: '',
            last_elapsed: '-',
            code_type: '',
            current_elapsed: '准备中',
            table_selection: [],
            iter_id: store.state.iterationID,
            app_type_list: ['static'],
            columns: [
                {
                    type: 'selection',
                    width: 50
                },
                { title: '应用', key: 'app_name' },
                /*{title: "仓库", key: "git_path"},*/
                { title: '发布环境', key: 'suite_code' },
                {
                    title: '绑定环境',
                    key: 'suite_code',
                    render: (h, params) => {
                        let value = params.row.suite_code
                        if (params.row.suite_code == null || params.row.suite_code == '') {
                            return h('div', '无')
                        } else {
                            let str = ''
                            if (value instanceof Array) {
                                console.log('i am Array')
                                console.log('=========== value =========')
                                console.log(value)
                                for (let i of value) {
                                    str += i + ','
                                }
                            }
                            if (typeof value === 'string') {
                                console.log('i am String')
                                str += value
                            }
                            //有多选却不输入值的
                            let returnArray = []
                            let returnStr = ''
                            returnArray = str.split(',')
                            for (let i of returnArray) {
                                if (i != '' && i != null && i != undefined) {
                                    returnStr += i + ','
                                }
                            }
                            if (returnStr != '' && returnStr.charAt(returnStr.length - 1) == ',') {
                                returnStr = returnStr.substr(0, returnStr.length - 1)
                            }
                            return h('div', returnStr)
                        }
                    }
                },
                {
                    title: '编译状态',
                    key: 'compile_status',
                    render: (h, params) => {
                        let stat_dict = {
                            running: '执行中',
                            success: '执行成功',
                            failure: '执行失败',
                            compile_running: '编译中',
                            compile_success: '编译成功',
                            compile_failure: '编译失败',
                            aborted: '已终止'
                        }
                        let status = params.row.compile_status
                        if (stat_dict.hasOwnProperty(status)) {
                            var status_display = stat_dict[status]
                        } else {
                            var status_display = status
                        }
                        let action_type = ''
                        if (status == 'compile_running') {
                            action_type = 'compile'
                        }
                        if (action_type) {
                            return h('div', [
                                h('a', [
                                    h(
                                        'Poptip',
                                        {
                                            props: {
                                                transfer: true,
                                                trigger: 'click',
                                                title: '上次耗时: ' + this.last_elapsed,
                                                content: '当前耗时：' + this.current_elapsed,
                                                size: 'small'
                                            },
                                            on: {
                                                'on-popper-show': () => {
                                                    h5ElapseStatsApiGet(
                                                        params.row.app_name,
                                                        action_type,
                                                        store.state.h5_branch_version
                                                    ).then(res => {
                                                        if (res.data.status === 'success') {
                                                            this.last_elapsed = res.data.data['last_elapsed']
                                                            this.current_elapsed = res.data.data['current_elapsed']
                                                        }
                                                    })
                                                },
                                                'on-popper-hide': () => {
                                                    this.last_elapsed = 0
                                                    this.current_elapsed = 0
                                                }
                                            }
                                        },
                                        status_display
                                    )
                                ])
                            ])
                        } else if (status == 'compile_success') {
                            return h('p', { style: { color: 'green' } }, status_display)
                        } else if (status == 'compile_failure') {
                            return h('p', { style: { color: 'red' } }, status_display)
                        } else {
                            return h('p', { style: { color: '#515a6e' } }, status_display)
                        }
                    }
                },
                {
                    title: '发布状态',
                    key: 'test_publish_status',
                    render: (h, params) => {
                        let stat_dict = {
                            running: '执行中',
                            success: '执行成功',
                            failure: '执行失败',
                            publish_running: '发布中',
                            publish_success: '发布成功',
                            publish_failure: '发布失败',
                            aborted: '已终止'
                        }
                        let status = params.row.test_publish_status
                        if (stat_dict.hasOwnProperty(status)) {
                            var status_display = stat_dict[status]
                        } else {
                            var status_display = status
                        }
                        let action_type = ''
                        if (status == 'publish_running') {
                            action_type = 'remote'
                        }
                        if (action_type) {
                            return h('div', [
                                h('a', [
                                    h(
                                        'Poptip',
                                        {
                                            props: {
                                                transfer: true,
                                                trigger: 'click',
                                                title: '上次耗时: ' + this.last_elapsed,
                                                content: '当前耗时：' + this.current_elapsed,
                                                size: 'small'
                                            },
                                            on: {
                                                'on-popper-show': () => {
                                                    h5ElapseStatsApiGet(
                                                        params.row.app_name,
                                                        action_type,
                                                        store.state.h5_branch_version
                                                    ).then(res => {
                                                        if (res.data.status === 'success') {
                                                            this.last_elapsed = res.data.data['last_elapsed']
                                                            this.current_elapsed = res.data.data['current_elapsed']
                                                        }
                                                    })
                                                },
                                                'on-popper-hide': () => {
                                                    this.last_elapsed = 0
                                                    this.current_elapsed = 0
                                                }
                                            }
                                        },
                                        status_display
                                    )
                                ])
                            ])
                        } else if (status == 'publish_success') {
                            return h('p', { style: { color: 'green' } }, status_display)
                        } else if (status == 'publish_failure') {
                            return h('p', { style: { color: 'red' } }, status_display)
                        } else {
                            return h('p', { style: { color: '#515a6e' } }, status_display)
                        }
                    }
                },
                {
                    title: '操作时间',
                    key: 'op_time',
                    //sortable: true,
                    render: (h, params) => {
                        let value = params.row.op_time
                        if (value == '' || value == null) {
                        } else {
                            value = formatDate(new Date(params.row.op_time), 'yyyy-MM-dd hh:mm:ss')
                        }
                        return h('div', value)
                    }
                },
                {
                    title: '结束时间',
                    key: 'end_time',
                    //sortable: true,
                    render: (h, params) => {
                        let value = params.row.end_time
                        if (value == '' || value == null) {
                        } else {
                            value = formatDate(new Date(params.row.end_time), 'yyyy-MM-dd hh:mm:ss')
                        }
                        return h('div', value)
                    }
                },
                { title: '操作人', key: 'op_user' },
                {
                    title: '操作',
                    key: 'action',
                    width: 300,
                    align: 'center',
                    render: (h, params) => {
                        //on事件中的this指向的并不是vue实例
                        let vm = this
                        let is_display = false
                        if (params.row.app_name != 'vendor') {
                            is_display = true
                        }
                        return h('div', [
                            h(
                                'Button',
                                {
                                    props: {
                                        type: 'default',
                                        size: 'small'
                                    },
                                    style: {
                                        marginRight: '5px'
                                    },
                                    on: {
                                        click: () => {
                                            let job_url = params.row.compile_url
                                            if (job_url != null) {
                                                window.open(params.row.compile_url)
                                            } else {
                                                this.$Notice.info({
                                                    title: '编译详情',
                                                    message: params.row.compile_message,
                                                    duration: getInfoNoticeShowTime()
                                                })
                                            }
                                        }
                                    }
                                },
                                '编译详情'
                            ),
                            h(
                                'Button',
                                {
                                    props: {
                                        type: 'default',
                                        size: 'small'
                                    },
                                    style: {
                                        marginRight: '5px'
                                    },
                                    on: {
                                        click: () => {
                                            let job_url = params.row.publish_url
                                            if (job_url != null) {
                                                window.open(params.row.publish_url)
                                            } else {
                                                this.$Notice.info({
                                                    title: '发布详情',
                                                    message: params.row.publish_message,
                                                    duration: getInfoNoticeShowTime()
                                                })
                                            }
                                        }
                                    }
                                },
                                '发布详情'
                            ),
                            h(
                                'Button',
                                {
                                    props: { type: 'warning', size: 'small' },
                                    style: {
                                        marginRight: '5px'
                                    }
                                },
                                [
                                    h(
                                        'Poptip',
                                        {
                                            props: {
                                                confirm: true,
                                                transfer: true,
                                                title: '你确定要强制刷新吗?',
                                                type: 'error',
                                                size: 'small'
                                            },
                                            on: {
                                                'on-ok': () => {
                                                    refreshExecStatusApi({
                                                        app_name: params.row.app_name,
                                                        iter_id: this.iter_id,
                                                        refresh_action_item_list: [
                                                            this.compile_action_item,
                                                            this.publish_action_item
                                                        ]
                                                    })
                                                },
                                                'on-cancel': function() {}
                                            }
                                        },
                                        '强制刷新'
                                    )
                                ]
                            ),
                            h(
                                'Button',
                                {
                                    props: { type: 'error', size: 'small' },
                                    style: {
                                        marginRight: '5px'
                                    }
                                },

                                [
                                    h(
                                        'Poptip',
                                        {
                                            props: {
                                                confirm: true,
                                                transfer: true,
                                                title: '你确定要强制中止吗?',
                                                type: 'error',
                                                size: 'small'
                                            },
                                            on: {
                                                'on-ok': () => {
                                                    stopTask({
                                                        app_name: params.row.app_name,
                                                        iter_id: this.iter_id,
                                                        action_item_list: [
                                                            this.compile_action_item,
                                                            this.publish_action_item
                                                        ]
                                                    })
                                                },
                                                'on-cancel': function() {}
                                            }
                                        },
                                        '终止'
                                    )
                                ]
                            )
                        ])
                    }
                }
            ]
        }
    },
    mounted() {
        //this.initTable()
    },
    methods: {
        init() {
            this.iter_id = store.state.iterationID
            this.br_name = store.state.h5_branch_version
            this.code_type = store.state.code_type
            this.compile_action_item = this.iter_id + '_' + 'test_static_compile'
            this.publish_action_item = this.iter_id + '_' + 'test_static_publish'
            let vm = this
            let selectTableData = []
            this.get_user_action(this.publish_action_item).then(function(data) {
                if (!data) {
                    console.info('用户行为数据查询失败！！')
                } else {
                    selectTableData =
                        data.length > 0
                            ? JSON.parse(
                                  data
                                      .replace(/False/g, 'false')
                                      .replace(/True/g, 'true')
                                      .replace(/'/g, '"')
                                      .replace(/None/g, null)
                              )
                            : data
                }
                selectTableData = data
                vm.$refs.ciTable.get_ci_info(
                    vm.iter_id,
                    [vm.compile_action_item, vm.publish_action_item],
                    vm.app_type_list,
                    selectTableData
                )
            })
        },
        get_user_action(action_item) {
            return new Promise(function(resolve, reject) {
                userActionGet(action_item)
                    .then(res => {
                        resolve(res.data.data)
                    })
                    .catch(err => {
                        reject(false)
                    })
            })
        },
        get_table_select_info(date_info) {
            this.date_info = date_info
        }
    }
}
</script>

<style scoped></style>
