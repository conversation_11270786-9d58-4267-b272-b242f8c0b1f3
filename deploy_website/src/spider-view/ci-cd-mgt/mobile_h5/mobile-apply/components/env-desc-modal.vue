<template>
    <div>
        <Button
            ghost
            type="primary"
            style="text-align: left; display: inline-block; width:80px;margin-top:-1px"
            @click="modal_env_desc = true"
            >环境说明
        </Button>
        <Modal v-model="modal_env_desc" title="环境说明">
            <template>
                <Table border :columns="columns1" :data="data1"></Table>
            </template>
        </Modal>
    </div>
</template>

<script>
import store from '@/spider-store'
import {} from '@/spider-api/h5'

export default {
    name: 'envDescModal',
    props: {},
    computed: {},
    data() {
        return {
            modal_env_desc: false,
            columns1: [
                {
                    title: '环境',
                    key: 'name'
                },
                {
                    title: '使用场景',
                    key: 'scenario',
                    width: 250
                },
                {
                    title: '后端直连服务',
                    key: 'server'
                }
            ],
            data1: [
                // {
                //   name: 'vph',
                //   scenario: 'h5和客户端打灰度包时常用环境,配置标识为灰度配置',
                //   server: '生产'
                // },
                // {
                //   name: 'vps',
                //   scenario: 'h5和客户端打灰度包，指定后端服务，比如CGI是灰度环境时使用',
                //   server: '灰度'
                // },
                // {
                //   name: 'pre',
                //   scenario: 'h5和客户端打灰度包，配置标识为产线配置，一般是打生产包之前的最后一次验证',
                //   server: '生产'
                // },
                {
                    name: 'pre',
                    scenario: 'h5和客户端打灰度包，指定后端服务，比如CGI是灰度环境时使用',
                    server: '灰度'
                },
                {
                    name: 'beta',
                    scenario: 'h5和客户端打灰度包时常用环境,配置标识为灰度配置',
                    server: '生产'
                },
                {
                    name: 'wgq-hd',
                    scenario: '静态资源(非H5和客户端)，比如：otc-web-static做灰度验证时使用',
                    server: '生产'
                },
                {
                    name: 'prod',
                    scenario: '打生产包时使用',
                    server: '生产'
                }
            ]
        }
    },
    methods: {},
    created() {}
}
</script>

<style scoped></style>
