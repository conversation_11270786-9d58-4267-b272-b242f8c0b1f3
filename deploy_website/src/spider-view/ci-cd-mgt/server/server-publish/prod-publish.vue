<template>
  <div>
    <div style="margin-bottom: 20px;">
      <ServicePublish :iterationId="iteration_id"></ServicePublish>
    </div>
    <Card shadow>
      <div>
        <div class="message-page-con message-category-con">
          <p>应用名称</p>

          <Scroll style="margin-top: 1em" :height="window_height">
            <ul class="ivu-menu ivu-menu-light ivu-menu-vertical" v-for="item in app_list" :app_list="app_list"
                :key="item">
              <li class="ivu-menu-item" @click="clickPanel(item)">{{ item }}</li>
            </ul>
          </Scroll>
        </div>
        <div class="message-page-con message-view-con">
          <div>
            <i-col>
              <Tag><span style="color: #17233d">产线节点：黑色</span></Tag>
              <Tag><span style="color: #19be6b">灾备节点：绿色</span></Tag>
              <Tag><span style="color: #ff9900">灰度节点：黄色</span></Tag>
            </i-col>
            <Divider>{{ iteration_id }}</Divider>
            <Divider>{{ app_name }}</Divider>
            <SpiderPublishHistory
              ref="spider_publish_history"
              :app_name_c="app_name"
              :pipeline_id_p="iteration_id"
            ></SpiderPublishHistory>

            <i-col v-show="app_name" style="margin-top: 1em;">
              <PublishTable :iteration_id="iteration_id" :is_prod_refresh="is_prod_refresh"
                            ref="publish_table"></PublishTable>
            </i-col>
          </div>
        </div>
      </div>


    </Card>
  </div>
</template>

<script>
import store from "@/spider-store";
import ServicePublish from "@/spider-components/service-publish";

import {
  getIterPublishAppInfoApi,
} from '@/spider-api/prod-publish/publish-info'
import PublishTable from "@/spider-components/spider-table/server-table/publish-table"
import SpiderPublishHistory from "@/spider-components/spider-publish-history"

export default {
  name: 'ops_publish_page',
  components: {
    PublishTable,
    SpiderPublishHistory,
    ServicePublish,
  },
  props: {
    is_prod_refresh: Boolean
  },
  data() {
    return {
      iteration_id: store.state.iterationID,
      consistentTime: '',
      consistentNode: true,
      nodeInfo: '',
      window_height: 500,
      historyCont: [],
      switch_history: '',
      select_group: '',
      select_ip: '',
      last_deploy: '',
      pipeline_id: '',
      modal_title: '',
      app_name: '',
      app_list: [],

      envSelect: 0,
      // regionSelect: 0,
      groupSelect: 0,
      envList: [
        {
          value: 0,
          label: "全部"
        },
      ],
      groupList: [
        {
          value: 0,
          label: "全部"
        },
      ],

    }
  },
  watch: {},
  methods: {
    init_filter() {
      this.envList = [{value: 0, label: "全部"}]
      // this.regionList = [{value: 0, label: "全部"}]
      this.groupList = [{value: 0, label: "全部"}]
      this.envSelect = 0
      // this.regionSelect = 0
      this.groupSelect = 0
    },
    clickPanel(val) {
      if (val.length) {
        this.app_name = val
        this.$refs.publish_table.getAppPublishInfo(this.app_name)
      }
    },

    initThisVue() {
      this.iteration_id = store.state.iterationID

      getIterPublishAppInfoApi(this.iteration_id).then(res => {
        if (res.data.status === "success") {
          this.app_list = res.data.data.app_list
          // 初始化的时候默认带入第一个应用
          if (this.app_list.length > 0) {
            this.app_name = this.app_list[0]
            this.$refs.publish_table.getAppPublishInfo(this.app_name)
          }
        }
      })


    },
  },
  beforeMount() {
    this.window_height = window.innerHeight - 220
  },
  mounted() {
  },
  destroyed() {
  }
}
</script>

<style scoped lang="less">
.message-page {
  &-con {
    // height: ~"calc(100vh - 176px)";
    min-height: 500em;
    display: inline-block;
    vertical-align: top;
    position: relative;

    &.message-category-con {
      border-right: 1px solid #e6e6e6;
      width: 18em;
      height: auto;
    }

    &.message-view-con {
      position: absolute;
      left: 21em;
      top: 1em;
      right: 1em;
      bottom: 2em;
      overflow: auto;
      padding: 1em 1em 0;

      .message-view-header {
        margin-bottom: 20px;

        .message-view-title {
          display: inline-block;
        }

        .message-view-time {
          margin-left: 20px;
        }
      }
    }
  }
}
</style>
