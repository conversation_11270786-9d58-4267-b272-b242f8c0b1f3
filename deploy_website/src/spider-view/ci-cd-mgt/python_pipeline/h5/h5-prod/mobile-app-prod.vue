<template>
    <Card>
        <Row>
            <tables stripe v-model="mobile_app_props" :columns="columns"> </tables>
        </Row>
        <Modal
            :styles="{ width: '60%' }"
            v-model="showDownloadModal"
            title="下载（右键点击【下载】按钮选择【链接另存为】可直接保存下载）"
            footer-hide
        >
            <Card>
                <Tabs ref="download_tabs">
                    <TabPane label="客户端构建包" name="DownloadFile">
                        <DownloadFile :download_file_props="downloadAppList"></DownloadFile>
                    </TabPane>
                </Tabs>
            </Card>
        </Modal>
    </Card>
</template>

<script>
import Tables from '@/components/tables'
import store from '@/spider-store'
import DownloadFile from '../h5-download/h5-download-zip.vue'
import {
    getResourceInfo,
    getInfoNoticeShowTime,
    getErrNoticeShowTime,
    formatDate,
    userAction,
    mobileAppPublishApi,
    h5ProdPublishApi
} from '@/spider-api/h5'
import { android_publish_api, android_publish_check_api, android_push_package } from '@/spider-api/publish'

export default {
    name: 'mobile-app-gray',
    components: {
        Tables,
        DownloadFile
    },
    props: {
        //这里是table中的数据
        mobile_app_props: {
            type: Array
        },
        queryState: {
            type: Function,
            default: null
        },
        vue_this: {
            type: Object
        }
    },
    data() {
        return {
            actionItem: 'pro_publish',
            showDownloadModal: false,
            pushingPackage: false,
            publishingCheck: false,
            publishing: false,
            downloadAppList: [],
            columns: [
                { title: '应用', key: 'app_name' },
                { title: '仓库', key: 'git_path' },
                {
                    title: '操作时间',
                    key: 'operate_time',
                    render: (h, params) => {
                        let value = params.row.operate_time
                        if (value) {
                            value = formatDate(new Date(params.row.operate_time), 'yyyy-MM-dd hh:mm:ss')
                        }
                        return h('div', value)
                    }
                },
                {
                    title: '申请状态',
                    key: 'apply_status_display',
                    render: (h, params) => {
                        let color = '#2db7f5'
                        if (params.row.apply_status && params.row.apply_status.indexOf('success') >= 0) {
                            color = 'green'
                        } else if (params.row.apply_status && params.row.apply_status.indexOf('failure') >= 0) {
                            color = 'red'
                        }

                        return h(
                            'Tooltip',
                            {
                                props: {
                                    placement: 'top',
                                    content: params.row.apply_message,
                                    'max-width': 500,
                                    transfer: true
                                }
                            },
                            [h('p', { props: {}, style: { color: color } }, params.row.apply_status_display)]
                        )
                    }
                },
                {
                    title: '发布状态',
                    key: 'status_display',
                    render: (h, params) => {
                        let color = '#2db7f5'
                        if (params.row.status && params.row.status.indexOf('success') >= 0) {
                            color = 'green'
                        } else if (params.row.status && params.row.status.indexOf('failure') >= 0) {
                            color = 'red'
                        }
                        return h(
                            'Tooltip',
                            {
                                props: {
                                    placement: 'top',
                                    content: params.row.message,
                                    'max-width': 500,
                                    transfer: true
                                }
                            },
                            [h('p', { props: {}, style: { color: color } }, params.row.status_display)]
                        )
                    }
                },
                { title: '发布人', key: 'username' },
                {
                    title: '操作',
                    key: 'action',
                    width: 250,
                    align: 'center',
                    render: (h, params) => {
                        return h('div', [
                            h(
                                'Button',
                                {
                                    props: {
                                        type: 'default',
                                        size: 'small'
                                    },
                                    style: {
                                        marginRight: '5px'
                                    },
                                    on: {
                                        click: () => {
                                            this.showDownload(params.row.app_name, params.row.suite_name)
                                        }
                                    }
                                },
                                '①下载'
                            ),
                            h(
                                'Button',
                                {
                                    props: {
                                        type: 'default',
                                        size: 'small',
                                        loading: this.pushingPackage
                                    },
                                    style: {
                                        marginRight: '5px'
                                    },
                                    on: {
                                        click: () => {
                                            this.push_android_package(params.row)
                                        }
                                    }
                                },
                                this.pushingPackage == true ? '推包中' : '②推包'
                            ),
                            h(
                                'Button',
                                {
                                    props: {
                                        type: 'default',
                                        size: 'small',
                                        loading: this.publishingCheck
                                    },
                                    style: {
                                        marginRight: '5px'
                                    },
                                    on: {
                                        click: () => {
                                            this.android_publish_check(params.row)
                                        }
                                    }
                                },
                                this.publishingCheck == true ? '检查' : '③发布检查'
                            ),
                            h(
                                'Button',
                                {
                                    props: {
                                        type: 'default',
                                        size: 'small',
                                        loading: this.publishing
                                    },
                                    style: {
                                        marginRight: '5px'
                                    },
                                    on: {
                                        click: () => {
                                            this.android_publish(params.row)
                                        }
                                    }
                                },
                                this.publishing == true ? '发布中' : '④发布'
                            )
                        ])
                    }
                }
            ]
        }
    },
    mounted() {
        this.init()
    },
    methods: {
        init() {},
        android_publish(item) {
            if ('publish_running' !== item.apply_status) {
                this.$Message.error('请先确认产线申请再推包')
                return false
            }
            this.publishing = true
            let publish_list = []
            let publish_obj = {}
            publish_obj = {
                suite_name: item.suite_name,
                op_type: 'deploy',
                app_name: item.app_name,
                iteration_id: item.iteration_id
            }
            publish_list.push(publish_obj)
            h5ProdPublishApi(publish_list)
                .then(res => {
                    console.log(res)
                    this.publishing = false
                    if (res.data.status === 'success') {
                        alert('发布成功')
                    } else {
                        alert(res.data.msg)
                    }
                })
                .catch(error => {
                    console.error(error)
                    this.publishing = false
                    alert('发布异常')
                })
        },
        android_publish_check(item) {
            this.publishingCheck = true
            let params = { app_name: item.app_name, iteration_id: item.iteration_id, suite_code: item.suite_name }
            android_publish_check_api(params)
                .then(res => {
                    this.publishingCheck = false
                    if (res.data.status === 'success') {
                        alert('检查成功')
                    } else {
                        alert(res.data.msg)
                    }
                })
                .catch(error => {
                    this.publishingCheck = false
                    console.error(error)
                    alert('检查异常' + error)
                })
        },
        push_android_package(item) {
            console.info('push_android_package')
            console.info(item)
            this.pushingPackage = true
            let params = { app_name: item.app_name, iteration_id: item.iteration_id, suite_code: item.suite_name }
            android_push_package(params)
                .then(res => {
                    this.pushingPackage = false
                    if (res.data.status === 'success') {
                        alert('推包成功')
                    } else {
                        alert('推包失败' + res.data.msg)
                    }
                })
                .catch(error => {
                    console.error(error)
                    alert('推包异常' + error)
                    this.pushingPackage = false
                })
        },
        showDownload(app_name, suite_name) {
            //记录点击下载记录 todo
            let _this = this
            let itemValue = { itemValue: '客户端app下载行为' }
            this.addUserAction(this.actionItem, itemValue).then(function(data) {
                //alert(data)
                if (!data) {
                    _this.$Notice.error({
                        desc: '行为记录数据失败，无法执行发布行为',
                        duration: getErrNoticeShowTime()
                    })
                    return
                }
                //新增状态数据，拼接如参
                let reqParams = {}
                reqParams.appName = app_name
                reqParams.iterationID = store.state.iterationID
                reqParams.actionId = data
                reqParams.actionItem = _this.actionItem
                reqParams.appEnv = suite_name
                mobileAppPublishApi(reqParams)
                    .then(res => {
                        //更新状态为发布成功
                        let param = {
                            app_name: app_name, //'fund',
                            iteration_id: store.state.iterationID,
                            suite_code: suite_name
                        }
                        //下载信息
                        getResourceInfo(param).then(res => {
                            console.log(res.data.data)
                            _this.downloadAppList = res.data.data
                            _this.showDownloadModal = true
                        })
                    })
                    .catch(err => {
                        _this.$Notice.error({
                            desc: '请求下载异常',
                            duration: getErrNoticeShowTime() + err
                        })
                        return
                    })
            })
        },
        addUserAction(actionItem, actionValue) {
            return new Promise(function(resolve, reject) {
                let req = {
                    action_item: actionItem,
                    action_value: actionValue
                }
                userAction(req)
                    .then(res => {
                        resolve(res.data.data)
                    })
                    .catch(err => {
                        reject(false)
                    })
            })
        }
    }
}
</script>
<style scoped></style>
