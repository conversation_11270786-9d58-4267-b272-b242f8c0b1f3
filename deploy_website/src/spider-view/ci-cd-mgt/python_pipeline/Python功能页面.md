下面是基于你给的入口页对“分支管理（H5Branch）”和“测试发布（MobileTest）”两个 Tab 的代码行为分析，并分别整理成可直接复制到 Claude Code 使用的高质量提示词模板（含上下文、任务目标、交付物与验收标准）。你可以按需替换其中的占位符与细节。

一、代码上下文简析（供你理解与后续提示词引用）

-   文件：/website_web/deploy_website/src/spider-view/ci-cd-mgt/python_pipeline/index.vue
-   组件与 Tab：
    -   分支管理：TabPane name="H5Branch"，子组件 H5Branch，ref="branch_method"
    -   测试发布：TabPane name="MobileTest"，子组件 MobileTest，ref="test_method"，props: is_test_refresh
-   Tabs 交互：
    -   v-model="tab_value"；@on-click="changeTab"
    -   changeTab('H5Branch') 时：调用 this.\$refs.branch_method.init()，关闭两个刷新标记
    -   changeTab('MobileTest') 时：调用 this.\$refs.test_method.init()，is_test_refresh = true，is_publish_apply_refresh = false
-   全局依赖：
    -   store.state.iterationID 控制某些 Tab 可用状态（tab_disable 计算属性）
-   其它接口/引用：
    -   PythonPipelineApi（已使用于 test() 调试函数）
    -   h5IterConfirmStatusApi（用于灰度/生产发布 Tab 的前置检查，当前与本两 Tab 不直接耦合）
-   Lint 提示：
    -   refreshExecStatusApi、stopTask 在 index.vue 被导入但未使用（可考虑清理或迁移使用场景至 MobileTest 子组件）

二、Claude Code 提示词模板

1. “分支管理”Tab（H5Branch）提示词模板
   标题：完善“分支管理（H5Branch）”Tab 的初始化与联动逻辑

## 背景与上下文

-   当前页面是 Python 功能构件入口页，使用 Tabs/TabPane 组织多个发布相关子流程
-   “分支管理”Tab 映射组件 H5Branch（ref="branch_method"）
-   切换到该 Tab 时，会立即调用 this.\$refs.branch_method.init()，并将 is_test_refresh / is_publish_apply_refresh 置为 false
-   当路由 query 带有 project_group 与 branch_name 时，beforeMount 将默认激活 H5Branch；mounted 时会调用 this.\$refs.branch_method.init()

## 目标

-   确认并完善 H5Branch 组件的 init 初始化流程与数据拉取、界面刷新
-   保持与 Tabs 的联动一致：首次进入页面、切换 Tab、从其它 Tab 返回时，均能稳定、幂等地初始化
-   不破坏既有 Options API 代码风格，新增逻辑与事件联动需清晰可维护

## 现有代码要点

-   Tabs v-model="tab_value"
-   changeTab('H5Branch') → this.\$refs.branch_method.init()
-   tab_disable 由 store.state.iterationID 控制
-   路由 query 告知：当携带 project_group、branch_name 时默认进入“分支管理”

## store 存储数据

    -   store.commit('setPyIterationID', this.iteration_id)
    -   store.commit('setPyCodeType', this.code_type)
    -   store.commit('setPyGroup', this.group)
    -   store.commit('setPyBranchVersion', this.branch_version)
    -   store.commit('setDeadLine', this.deadline)
    -   store.commit('setPublishType', this.publish_type)

## 需要你完成的内容

-   为 H5Branch 组件补全/优化：
    -   提供健壮的 init()：确保重复调用幂等，不会造成重复请求或状态错乱（可利用内部加载状态或缓存上次入参）
    -   若存在依赖 store.state.iterationID 或外部条件，需在 init 内做前置校验并给出可观测反馈（loading/空态/错误提示）
    -   保证首次 mounted、Tab 切换、路由参数切换三种场景下表现一致
-   与入口页的联动：
    -   验证切到 H5Branch 时关闭其他刷新标记不会影响 H5Branch 的 UI 刷新
    -   若需要根据路由 query（project_group、branch_name）做额外过滤或预选，可在 init 里接收参数或读取当前路由
-   代码风格与约束：
    -   保持 4 空格缩进、单引号、无分号
    -   注释（中文）需补全函数用途、参数与返回值
    -   不对全局状态做隐式副作用，除非有明确说明
-   删除所有 tag 相关逻辑

## 输出要求

-   若需要修改 H5Branch 组件，给出变更点说明与关键方法签名
-   说明 init() 的调用时序、幂等策略与异常处理策略
-   列出任何需要新增的事件、props、emit 或 store 字段

## 验收标准

-   打开页面或切换至“分支管理”Tab 时，能稳定看到正确的分支数据
-   多次切换 Tab 或多次调用 init() 不会重复叠加请求/监听器
-   在 iterationID 缺失或路由参数异常时，UI 给出合理反馈，不报错不白屏

## 参考文件

-   /website_web/deploy_website/src/spider-view/ci-cd-mgt/python_pipeline/index.vue
-   H5Branch 组件文件：./h5/h5-branch/h5-branch.vue（请在项目内定位）

# 2. “测试发布”Tab（MobileTest）提示词模板

标题：完善“测试发布（MobileTest）”Tab 的初始化、刷新与任务控制

## 背景与上下文

-   “测试发布”Tab 映射组件 MobileTest（ref="test_method"）
-   切换到该 Tab 时：调用 this.\$refs.test_method.init()，设置 is_test_refresh = true
-   MobileTest 组件接受 props: is_test_refresh，用于控制内部刷新逻辑
-   当前入口页存在 PythonPipelineApi（可触发流水线），另有 refreshExecStatusApi、stopTask 曾用于调试但在 index.vue 中为未使用导入

## 目标

-   在 MobileTest 组件中实现/完善 init 与刷新链路：
    -   Tab 切入即初始化
    -   通过 is_test_refresh 的变化触发数据刷新或状态同步（建议在子组件使用 watch 监听该 prop）
-   加入对执行状态刷新与任务停止的能力封装（优先放到 MobileTest 内使用，避免入口页出现未使用导入）
-   清理 index.vue 的 Lint 问题（未使用导入），或将相关能力转移至 MobileTest 并在入口页移除无用导入

## 现有代码要点

-   changeTab('MobileTest') → this.\$refs.test_method.init()；is_test_refresh = true
-   test() 方法里曾用 PythonPipelineApi/refreshExecStatusApi/stopTask 做过手动调用，目前相关调用被注释
-   is_publish_apply_refresh 在该 Tab 下应为 false，避免互相干扰

## store 使用

    -   store.state.ci_info = this.ci_info
    -   store.state.python_group
    -   store.state.python_branch_version
    -   store.state.python_iterationID
    -   store.state.python_code_type
    -   store.state.ci_info

## 需要你完成的内容

-   在 MobileTest 组件内实现：
    -   init()：拉取/展示该 Tab 所需数据，保证幂等
    -   watch: is_test_refresh(newVal)：当为 true 时执行刷新动作，并在合适时机将内部刷新状态复位（避免死循环）
    -   封装方法：
        -   triggerPipeline(payload)：调用 PythonPipelineApi 发起编译/部署；对异常与进行态给出 UI 反馈
        -   refreshExecStatus(params)：包装 refreshExecStatusApi，轮询或单次刷新执行状态；提供取消机制
        -   stopRunningTasks(params)：包装 stopTask，提供任务终止能力
-   在入口页 index.vue：
    -   如果上述封装已在 MobileTest 中使用，则移除 refreshExecStatusApi、stopTask 未使用导入，消除 Lint 报错
-   代码风格与约束：
    -   保持 4 空格缩进、单引号、无分号；中文注释完整
    -   函数尽量纯净，I/O 隔离，避免全局副作用
    -   不破坏 Tabs 与其它 Tab 的既有行为
-   删除所有 tag 相关逻辑

## 输出要求

-   给出 MobileTest 内新增/修改的方法签名与调用时序（init、watch、触发/刷新/停止）
-   说明 is_test_refresh 的使用策略与复位方案（例如：父组件仅负责置 true，子组件内部自行消化并在完成后通过事件告知父组件复位）
-   指明 index.vue 需要删除的未使用导入（若已迁移）与对应原因

## 验收标准

-   切换至“测试发布”Tab 时能稳定初始化，不出现重复请求或卡死
-   手动触发一次发布 → 能看到执行状态刷新；可在执行中途调用停止并得到正确 UI 反馈
-   入口页不再有 refreshExecStatusApi 与 stopTask 的未使用 Lint 报错（若已迁移）

## 参考文件

-   /website_web/deploy_website/src/spider-view/ci-cd-mgt/python_pipeline/index.vue
-   MobileTest 组件文件：./mobile-test/mobile-test.vue（请在项目内定位）
