<template>
    <div>
        <Tabs value="name1" v-model="tab_value" @on-click="changeTab" ref="tabs" name="mobile_pipeline">
            <TabPane label="分支管理" name="H5Branch" tab="mobile_pipeline">
                <H5Branch ref="branch_method"></H5Branch>
            </TabPane>
            <TabPane label="测试发布" name="MobileTest" tab="mobile_pipeline">
                <MobileTest :is_test_refresh="is_test_refresh" ref="test_method"> </MobileTest>
            </TabPane>
            <TabPane label="发布申请" name="MobilePublishApply" tab="mobile_pipeline">
                <MobilePublishApply
                    :is_publish_apply_refresh="is_publish_apply_refresh"
                    ref="mobile_publish_apply"
                ></MobilePublishApply>
            </TabPane>
            <TabPane label="灰度发布" name="PyGray" :disabled="tab_disable" tab="mobile_pipeline">
                <PyGray ref="gray_method"></PyGray>
            </TabPane>
            <TabPane label="生产发布" name="PyProd" :disabled="tab_disable" tab="mobile_pipeline">
                <PyProd ref="prod_method"></PyProd>
            </TabPane>
            <TabPane label="分支归档" name="H5BranchFile" :disabled="tab_disable" tab="mobile_pipeline">
                <H5BranchFile ref="branch_file_method"></H5BranchFile>
            </TabPane>
        </Tabs>
    </div>
</template>

<script>
import store from '@/spider-store'
import H5Branch from './h5/h5-branch/h5-branch.vue'
import H5BranchFile from './h5/h5-branch/h5-branch-file.vue'
import PyGray from './gray/index.vue'
import PyProd from './prod/index.vue'
import MobileTest from './mobile-test/mobile-test.vue'
import MobilePublishApply from './mobile-publish-apply/mobile-publish-apply.vue'

import { h5IterConfirmStatusApi } from '@/spider-api/publish'
import SpiderPipeline from '@/spider-components/spider-pipeline'

export default {
    name: 'MobileMain',
    components: {
        H5Branch,
        MobileTest,
        PyGray,
        PyProd,
        // H5Apply,
        // MobileApply,
        MobilePublishApply,
        H5BranchFile,
        SpiderPipeline
    },
    computed: {
        tab_disable() {
            if (store.state.python_iterationID == '' || store.state.python_iterationID == undefined) {
                return true
            } else {
                return false
            }
        }
    },
    data() {
        return {
            tab_value: 'H5Branch',
            is_test_refresh: false,
            is_publish_apply_refresh: false
        }
    },
    methods: {
        changeTab(tab_name) {
            if (tab_name === 'H5Branch') {
                this.$refs.branch_method.init()
                this.is_test_refresh = false
                this.is_publish_apply_refresh = false
            } else if (tab_name === 'MobileTest') {
                this.$refs.test_method.init()
                this.is_test_refresh = true
                this.is_publish_apply_refresh = false
            } else if (tab_name === 'PyGray') {
                this.is_test_refresh = false
                this.is_publish_apply_refresh = false
                h5IterConfirmStatusApi(store.state.python_iterationID, 'gray').then(res => {
                    if (res.data.status === 'success') {
                        this.$refs.gray_method.init()
                    } else {
                        alert(res.data.msg)
                        this.$refs.tabs.activeKey = 'MobilePublishApply'
                        this.is_publish_apply_refresh = true
                        this.$refs.mobile_publish_apply.init()
                    }
                })
            } else if (tab_name === 'PyProd') {
                this.is_test_refresh = false
                this.is_publish_apply_refresh = false
                h5IterConfirmStatusApi(store.state.python_iterationID, 'prod').then(res => {
                    if (res.data.status === 'success') {
                        this.$refs.prod_method.init()
                    } else {
                        alert(res.data.msg)
                        this.$refs.tabs.activeKey = 'MobilePublishApply'
                        this.is_publish_apply_refresh = true
                        this.$refs.mobile_publish_apply.init()
                    }
                })
            } else if (tab_name === 'H5BranchFile') {
                this.is_publish_apply_refresh = false
                this.is_test_refresh = false
                if (store.state.code_type == 'tag') {
                    this.tab_value = 'H5Branch'
                    this.$refs.tabs.activeKey = 'H5Branch'
                    alert(store.state.code_type + '不可以归档')
                    this.$refs.branch_method.init()
                } else {
                    this.$refs.branch_file_method.init()
                }
            } else if (tab_name === 'MobilePublishApply') {
                this.is_publish_apply_refresh = true
                this.is_test_refresh = false
                this.$refs.tabs.activeKey = 'MobilePublishApply'
                this.$refs.mobile_publish_apply.init()
            }
        }
    },
    beforeMount() {
        if (this.$route.query.project_group && this.$route.query.branch_name) {
            this.tab_value = 'H5Branch'
        }
    },
    mounted() {
        this.$refs.branch_method.init()
    }
}
</script>

<style scoped></style>
