<template>
    <div>
        <i-col>
            <Button
                ghost
                type="primary"
                style="text-align: left; display: inline-block; width:100px; margin:5px"
                @click="Open"
                >app快速构建</Button
            >
        </i-col>
        <Modal :title="my_title" width="1200" v-model="visible" :mask-closable="true">
            <div>
                <Row>
                    <Table style="margin-top: 1em" :columns="publish_columns" :data="publish_data"></Table>
                </Row>

                <Row style="margin-top: 10px">
                    <i-col style="margin: 5px" span="2">
                        <span style="text-align: right; display: inline-block;width:150px; font-size: 16px"
                            >灰度申请信息如下：</span
                        >
                    </i-col>
                </Row>

                <Row>
                    <Table style="margin-top: 1em" :data="dist_app_list" :columns="dist_columns"> </Table>
                </Row>
                <DownloadResource ref="downloadResource"> </DownloadResource>
            </div>
            <div slot="footer">
                <!--<Button @click="$emit('continut')">继续申请</Button>-->
                <Button @click="Cancel">关闭</Button>
            </div>
        </Modal>
    </div>
</template>

<script>
import Tables from '@/components/tables'
import {
    getLastApplyInfoApi,
    appTagPublishApplyApi,
    userAction,
    userActionGet,
    getSuccessNoticeShowTime,
    getErrNoticeShowTime,
    getInfoNoticeShowTime
} from '@/spider-api/h5'
import DownloadResource from '@/spider-components/spider-buttons/download-resource'
import { getAppExpressBuildServiceApi } from '@/spider-api/mobile-cd/mobile-gray'

export default {
    name: 'ExpressBuildService',
    components: {
        Tables,
        DownloadResource
    },
    props: {
        // value:{
        //     type:Boolean,
        //     defaults:false
        //   },
        //这里是table中的数据
        dist_app_list: {
            type: Array
        }

        // vue_this: {
        //     type: Object
        // }
    },
    data() {
        return {
            visible: false,
            my_title: 'app快速构建',
            action_item: 'publish_apply',
            dist_columns: [
                { title: '应用', key: 'app_name' },
                { title: '申请人', key: 'apply_user' },
                { title: '申请时间', key: 'apply_time' },
                // {title: "起始版本", key: "begin_ver", /*sortable: true*/},
                // {title: "结束版本", key: "end_ver", /*sortable: true*/},
                {
                    title: '申请状态',
                    key: 'apply_status_display',
                    render: (h, params) => {
                        let color = '#2db7f5'
                        if (params.row.apply_status && params.row.apply_status.indexOf('success') >= 0) {
                            color = 'green'
                        } else if (params.row.apply_status && params.row.apply_status.indexOf('failure') >= 0) {
                            color = 'red'
                        }

                        return h(
                            'Tooltip',
                            {
                                props: {
                                    placement: 'top',
                                    content: params.row.apply_message,
                                    'max-width': 500,
                                    transfer: true
                                }
                            },
                            [h('p', { props: {}, style: { color: color } }, params.row.apply_status_display)]
                        )
                    }
                }
            ],
            publish_data: [],
            publish_columns: [
                {
                    title: '应用名',
                    key: 'app_name',
                    width: 150
                },
                {
                    title: '线上分支',
                    key: 'br_name'
                    // width: 150
                },
                {
                    title: '环境',
                    key: 'env',
                    width: 90
                },
                {
                    title: '线上版本',
                    key: 'online_version',
                    width: 90
                },
                {
                    title: '灰度版本',
                    key: 'app_version',
                    // width: 150,
                    // render (row, column, index) {
                    //           return '<i-input type="primary" size="small" value="${row.app_version}">${row.app_version}</i-input>'
                    // }

                    // width: 150,
                    render: (h, params) => {
                        return h('div', [
                            h(
                                'Input',
                                {
                                    attrs: {
                                        class: 'ivu-btn ivu-btn-primary ivu-btn-small ivu-btn-ghost'
                                    },
                                    props: {
                                        value: params.row.app_version
                                    },
                                    style: {
                                        marginRight: '1em'
                                    },

                                    on: {
                                        input: val => {
                                            console.log(val)
                                            params.row.app_version = val
                                        }
                                        // 'on-change': (event) => {
                                        //   //this.params = params
                                        //   console.log(event)
                                        //   params.row.app_version = event.data
                                        //
                                        // },
                                    }
                                }
                                // params.row.app_version
                            )
                        ])
                    }
                },
                // {
                //   title: "迭代号",
                //   key: "iter_id",
                //   width: 90,
                // },
                //          {
                //   title: "包类型",
                //   key: "package_type",
                //   width: 90,
                // },
                //          {
                //   title: "代码库",
                //   key: "git_repo",
                //   width: 90,
                // },
                //                   {
                //   title: "h5应用名",
                //   key: "h5_name",
                //   width: 90,
                // },

                // 先做成不可选的
                // {
                //   title: "服务器",
                //   key: "ip",
                //   width: 190,
                //   render: (h, params) => {
                //     let nodes = [];
                //     params.row.ip.forEach((item) => {
                //       let vnode = h("Option", {
                //         props: {
                //           value: item,
                //         },
                //       });
                //       nodes.push(vnode);
                //     });
                //     return h(
                //       "Select",
                //       {
                //         style: {},
                //         props: {
                //           placeholder: "",
                //           value: "",
                //           size: "small",
                //           transfer:true
                //         },
                //         on: {
                //           "on-change": (val) => {
                //              params.row.showLogIp = val;
                //             getProdRollbackStat(params.row.appName, val)
                //               .then((res) => {
                //                 if (res.data.status === "success") {
                //                   // 回滚可用
                //                   params.row.rollbackStatus = false;
                //                   this.$set(this.publish_columns, params);
                //                 } else {
                //                   // 回滚不可用
                //                   params.row.rollbackStatus = true;
                //                   this.$set(this.publish_columns, params);
                //                 }
                //               })
                //               .catch((err) => {
                //                 this.$Message.error(err.response.data.msg);
                //               });
                //             this.deployNodeChange(params, val);
                //           },
                //         },
                //       },
                //       nodes
                //     );
                //   },
                // },
                {
                    title: '操作',
                    width: 135,
                    align: 'center',
                    render: (h, params) => {
                        return h('div', [
                            h(
                                'Button',
                                {
                                    attrs: {
                                        class: 'ivu-btn ivu-btn-primary ivu-btn-small ivu-btn-ghost'
                                    },
                                    props: {
                                        loading: params.row.loading_btn
                                    },
                                    style: {
                                        marginRight: '1em'
                                    },
                                    on: {
                                        click: () => {
                                            //this.params = params
                                            params.row.loading_btn = true
                                            this.appBuild(params.row)
                                            setTimeout(() => {
                                                params.row.loading_btn = false
                                            }, 10000)
                                        }
                                    }
                                },
                                '快速构建'
                            )
                        ])
                    }
                },
                {
                    title: '跳转到流水线',
                    width: 150,
                    align: 'center',
                    render: (h, params) => {
                        return h('div', [
                            h(
                                'Button',
                                {
                                    attrs: {
                                        class: 'ivu-btn ivu-btn-success ivu-btn-small ivu-btn-ghost'
                                    },
                                    props: {
                                        loading: params.row.loading_btn
                                    },
                                    style: {},
                                    on: {
                                        click: () => {
                                            params.row.loading_btn = true
                                            console.log(params.row.loading_btn)
                                            this.getLastApplyInfo(params.row)
                                            setTimeout(() => {
                                                params.row.loading_btn = false
                                            }, 10000)
                                        }
                                    }
                                },
                                '最近一次执行详情'
                            )
                        ])
                    }
                },

                {
                    title: '下载包',
                    width: 90,
                    align: 'center',
                    render: (h, params) => {
                        return h('div', [
                            h(
                                'Button',
                                {
                                    props: {
                                        type: 'default',
                                        size: 'small'
                                    },
                                    style: {
                                        marginRight: '5px',
                                        display:
                                            params.row.app_name == 'nf-fund' || params.row.app_name == 'nf-piggy'
                                                ? 'none'
                                                : 'inline-block'
                                    },
                                    on: {
                                        click: () => {
                                            if (params.row.env == undefined) {
                                                this.$Notice.info({
                                                    desc: '未绑定环境',
                                                    duration: getInfoNoticeShowTime()
                                                })
                                                return
                                            }
                                            this.$refs.downloadResource.showDownload(
                                                params.row.iter_id,
                                                params.row.app_name,
                                                params.row.env
                                            )
                                        }
                                    }
                                },
                                '下载'
                            )
                        ])
                    }
                }
            ]
        }
    },
    methods: {
        getLastApplyInfo(row) {
            //this.modal_last_apply_info = true
            let req = {
                branch_version: row.br_name,
                suite_code: row.env,
                group_name: row.group_name,
                tag_name: 'tag_' + row.br_name
            }
            getLastApplyInfoApi(req)
                .then(res => {
                    if (res.data.status === 'success') {
                        console.log('==========' + res.data.data.status)
                        if (res.data.data.status == 'true') {
                            console.log('==========' + res.data.data.return_msg)
                            window.open(res.data.data.return_msg)
                        } else {
                            alert(res.data.data.return_msg)
                        }
                    } else {
                        alert(res.data.data)
                    }

                    // this.lastApplyInfoStatus = res.data.data.status
                    // this.lastApplyInfoReturnMessage = res.data.data.return_msg
                })
                .catch(err => {
                    alert(err)
                })
        },
        appBuild(row) {
            console.log(row)
            let request_param = this.getAppApplyParam([row])
            console.log(request_param)
            let action_param = {
                action_item: this.action_item,
                action_value: request_param
            }
            console.log(action_param)
            userAction(action_param).then(res => {
                console.log(res)
                if (!res.data.data) {
                    this.$Notice.error({
                        desc: '行为记录数据失败，无法执行发布行为',
                        duration: getErrNoticeShowTime()
                    })
                    return
                }
                request_param.actionId = res.data.data

                appTagPublishApplyApi(request_param)
                    .then(res => {
                        if (res.data.status == 'failed') {
                            alert(res.data.msg)
                        } else {
                            this.$Notice.success({
                                desc: res.data.msg,
                                duration: getSuccessNoticeShowTime()
                            })
                        }
                    })
                    .catch(err => {
                        alert(err)
                    })
            })
        },
        Open() {
            this.visible = true
            let h5_app = []
            let h5_platform_code = []
            for (let row of this.dist_app_list) {
                if (h5_platform_code.includes(row.platform_code)) {
                    console.log('去重')
                } else {
                    h5_platform_code.push(row.platform_code)
                }
                h5_app.push(row.app_name)
            }
            getAppExpressBuildServiceApi({ h5_platform_code: h5_platform_code }).then(res => {
                if (res.data.status === 'success') {
                    console.log(res.data.data)
                    this.publish_data = res.data.data['app_online_version_info']
                }
            })
            console.log(this.visible)
        },

        Cancel() {
            this.visible = false
            // this.$emit('input', this.visible)
        },
        //获取app客户端参数
        getAppApplyParam(rows) {
            let rows_params = []
            let request_param = {}
            let app_name_list = []

            for (let row of rows) {
                let row_params = {}
                console.log(row)
                app_name_list.push(row.app_name)
                //row_params.cc = '<EMAIL>'
                row_params.actionItem = this.action_item
                row_params.iterationID = row.iter_id
                row_params.appName = row.app_name
                row_params.appEnv = row.env
                row_params.appBranch = row.br_name
                row_params.appTagName = 'tag_' + row.br_name
                row_params.appCodeType = 'tag'
                row_params.packageType = row.package_type
                row_params.repoPath = row.git_repo
                row_params.appVersion = row.app_version
                row_params.h5AppName = row.h5_name
                // row_params.h5PlatFormCode = row.platform_code
                for (let dist_row of this.dist_app_list) {
                    if (dist_row.platform_code == row.h5_platform_code) {
                        row_params.h5ZipVersion = dist_row.end_ver
                        row_params.h5Env = dist_row.suite_name
                        row_params['h5PlatFormCode'] = dist_row.platform_code
                    }
                }
                rows_params.push(row_params)
            }

            request_param.appNameList = app_name_list
            request_param.requestParams = rows_params
            request_param.appEnv = request_param.requestParams[0].appEnv
            request_param.appBranch = request_param.requestParams[0].appBranch
            request_param.iterationID = request_param.requestParams[0].iterationID
            request_param.actionItem = this.action_item
            request_param.cc = '<EMAIL>'
            return request_param
        }
    },
    watch: {
        // value (val) {
        //     this.visible = val;
        // },
        // open(){
        //
        // },
        // visible (val) {
        //     if (val === true) {
        //       getAppExpressBuildServiceApi({"h5_app_name": dist_app_list}).then(res => {
        //          if (res.data.status === "success") {
        //            console.log(res.data.data)
        //            this.publish_data = res.data.data["app_online_version_info"]
        //          }
        //
        //       })
        //     }
        // }
    }
}
</script>

<style scoped></style>
