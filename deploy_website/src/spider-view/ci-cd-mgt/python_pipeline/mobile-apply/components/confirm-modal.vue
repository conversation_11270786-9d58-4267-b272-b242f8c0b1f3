<template>
    <div>
        <Modal :title="titleInfo" width="800" v-model="modal_confirm" :mask-closable="true" @on-cancel="closeModal">
            <div>
                <Row>
                    <Table :columns="columns" :data="tableData"> </Table>
                </Row>
            </div>
            <div slot="footer">
                <Button @click="$emit('continue')">继续申请</Button>
                <Button @click="closeModal">关闭</Button>
            </div>
        </Modal>
    </div>
</template>

<script>
import store from '@/spider-store'
import {} from '@/spider-api/h5'

export default {
    name: 'confirmModal',
    props: {
        titleInfo: {
            type: String,
            defaults: ''
        },

        columns: {},

        tableData: {
            type: Array,
            defaults: []
        }
    },
    computed: {},
    data() {
        return {
            modal_confirm: false
        }
    },
    methods: {
        closeModal() {
            this.modal_confirm = false
        }
    },
    created() {}
}
</script>

<style scoped lang="less">
.modalRed /deep/.ivu-modal-header-inner {
    color: red;
    font-weight: 700;
}
.modalGreen /deep/.ivu-modal-header-inner {
    color: green;
    font-weight: 100;
}
</style>
