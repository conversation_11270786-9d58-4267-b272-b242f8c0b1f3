<!--
 * @Author: chao.bao <EMAIL>
 * @Date: 2025-08-15 10:44:30
 * @LastEditors: chao.bao <EMAIL>
 * @LastEditTime: 2025-08-29 15:16:51
 * @FilePath: /website_web/deploy_website/src/spider-view/ci-cd-mgt/python_pipeline/mobile-apply/components/last-apply-modal.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div>
        <Button
            ghost
            type="primary"
            style="text-align: left; display: inline-block; width:120px;margin-top:-1px"
            v-show="lastApplyButtonShow"
            @click="getLastApplyInfo"
            >最近一次申请详情
        </Button>
        <Modal v-model="modal_last_apply_info" title="最近一次申请详情">
            <div v-if="lastApplyInfoStatus == 'true'">
                <ButtonGroup>
                    <Button
                        v-for="(joburl, index) in lastApplyInfoReturnMessage"
                        :key="index"
                        @click="openJenkinsUrl(joburl)"
                        type="text"
                    >
                        {{ joburl }}
                    </Button>
                </ButtonGroup>
            </div>
            <div v-else>
                <p>{{ lastApplyInfoReturnMessage }}</p>
            </div>
        </Modal>
    </div>
</template>

<script>
import { getLastApplyInfoApi } from '@/spider-api/python'

export default {
    name: 'lastApplyModal',
    props: {
        branch_version: {
            type: String,
            defaults: ''
        },
        environment: {
            type: String,
            defaults: ''
        },
        group: {
            type: String,
            defaults: ''
        }
    },
    computed: {},
    data() {
        return {
            modal_last_apply_info: false,
            lastApplyButtonShow: false,
            lastApplyInfoStatus: '',
            lastApplyInfoReturnMessage: ''
        }
    },
    methods: {
        openJenkinsUrl(joburl) {
            window.open(joburl)
        },
        /**
         * 点击【最近一次申请详情】按钮
         */
        getLastApplyInfo() {
            this.modal_last_apply_info = true
            let req = {
                branch_name: this.branch_version,
                suite_code: this.environment,
                group_name: this.group,
                op_type: 'python_apply'
            }
            getLastApplyInfoApi(req).then(res => {
                this.lastApplyInfoStatus = res.data.data.status
                this.lastApplyInfoReturnMessage = res.data.data.return_msg
                console.log('==========' + this.lastApplyInfoStatus)
                console.log('==========' + this.lastApplyInfoReturnMessage)
            })
        }
    },
    created() {}
}
</script>

<style scoped></style>
