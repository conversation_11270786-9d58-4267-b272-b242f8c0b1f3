import { RefreshExecStatusApi } from '@/spider-api/rsync-data-calibration'
import { stopTaskApi } from '@/spider-api/task-action'

/**
   * 强制刷新页面功能
 *  let params = {"app_name":date_info.app_name,
      "iter_id":this.iter_id,
      "refresh_action_item_list":[this.compile_action_item, this.publish_action_item]}
   */
export const refreshExecStatusApi = params => {
    console.log('========强制刷新参数============')
    console.log(params)
    RefreshExecStatusApi(params).then(res => {
        console.log(res.data.data)
        alert(JSON.stringify(res.data.data))
    })
}

/**
停止jenkins功能提取出来
*/
export const stopTask = params => {
    console.log('========停止任务参数============')
    console.log(params)
    stopTaskApi(params).then(res => {
        console.log(res.data.data)
        alert(JSON.stringify(res.data.data))
    })
}
