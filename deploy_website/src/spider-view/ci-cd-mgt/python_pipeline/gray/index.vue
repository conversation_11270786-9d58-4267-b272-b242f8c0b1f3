<template>
    <div>
        <Card shadow>
            <div>
                <div class="message-page-con message-category-con">
                    <p>应用名称</p>

                    <Scroll style="margin-top: 1em" :height="window_height">
                        <ul
                            class="ivu-menu ivu-menu-light ivu-menu-vertical"
                            v-for="item in app_list"
                            :app_list="app_list"
                            :key="item"
                        >
                            <li class="ivu-menu-item" @click="clickPanel(item)">{{ item }}</li>
                        </ul>
                    </Scroll>
                </div>
                <div class="message-page-con message-view-con">
                    <div>
                        <i-col>
                            <Tag><span style="color: #17233d">产线节点：黑色</span></Tag>
                            <Tag><span style="color: #19be6b">灾备节点：绿色</span></Tag>
                            <Tag><span style="color: #ff9900">灰度节点：黄色</span></Tag>
                        </i-col>
                        <Divider>{{ iteration_id }}</Divider>
                        <Divider>{{ app_name }}</Divider>
                        <SpiderPublishHistory
                            ref="spider_publish_history"
                            :app_name_c="app_name"
                            :pipeline_id_p="iteration_id"
                        ></SpiderPublishHistory>

                        <i-col v-show="app_name" style="margin-top: 1em;">
                            <PublishTable
                                :iteration_id="iteration_id"
                                :is_prod_refresh="is_prod_refresh"
                                publish_type="beta"
                                ref="publish_table"
                            ></PublishTable>
                        </i-col>
                    </div>
                </div>
            </div>
        </Card>
    </div>
</template>

<script>
import store from '@/spider-store'

import { getIterPublishAppInfoApi } from '@/spider-api/python'
import PublishTable from '../server-table/publish-table.vue'
import SpiderPublishHistory from '@/spider-components/spider-publish-history'

export default {
    name: 'gray_publish_page',
    components: {
        PublishTable,
        SpiderPublishHistory
    },
    data() {
        return {
            is_prod_refresh: true,
            iteration_id: store.state.python_iterationID,
            window_height: 500,
            app_name: '',
            app_list: []
        }
    },
    methods: {
        clickPanel(val) {
            if (val.length) {
                this.app_name = val
                this.$refs.publish_table.getAppPublishInfo(this.app_name)
            }
        },

        init() {
            this.iteration_id = store.state.python_iterationID

            getIterPublishAppInfoApi({ pipeline_id: this.iteration_id, sys_status: '灰度中' }).then(res => {
                if (res.data.code === '0000') {
                    this.app_list = res.data.data.app_list
                    // 初始化的时候默认带入第一个应用
                    if (this.app_list.length > 0) {
                        this.app_name = this.app_list[0]
                        this.$refs.publish_table.getAppPublishInfo(this.app_name)
                    }
                }
            })
        }
    },
    beforeMount() {
        this.window_height = window.innerHeight - 220
    }
}
</script>

<style scoped lang="less">
.message-page {
    &-con {
        min-height: 500em;
        display: inline-block;
        vertical-align: top;
        position: relative;

        &.message-category-con {
            border-right: 1px solid #e6e6e6;
            width: 18em;
            height: auto;
        }

        &.message-view-con {
            position: absolute;
            left: 21em;
            top: 1em;
            right: 1em;
            bottom: 2em;
            overflow: auto;
            padding: 1em 1em 0;

            .message-view-header {
                margin-bottom: 20px;

                .message-view-title {
                    display: inline-block;
                }

                .message-view-time {
                    margin-left: 20px;
                }
            }
        }
    }
}
</style>
