<template>
    <div>
        <Card>
            <Row style="margin-top: 10px">
                <i-col style="margin: 10px;" span="2">
                    <span style="display: inline-block;width:60px;text-align: right;">组：</span>
                </i-col>
                <Col span="3">
                    <span style="margin: 10px; text-align: left; display: inline-block; ">{{ group }}</span>
                </Col>
                <i-col style="margin: 10px;text-align: right" span="2">
                    <span style="text-align: right; display: inline-block;">分支版本：</span>
                </i-col>
                <Col span="8">
                    <span style="margin: 10px;text-align: left; display: inline-block;">{{ br_name }}</span>
                </Col>
            </Row>
            <Row style="margin-top: 10px">
                <i-col style="margin: 10px;" span="2">
                    <span style="display: inline-block;width:120px;text-align: right;">环境：</span>
                </i-col>
                <Col span="10">
                    <Select v-model="env" @on-change="envChange">
                        <Option v-for="(item, index) in logic_suite_list" :value="item.logic_suite_code" :key="index">
                            {{ item.logic_suite_label }}
                        </Option>
                    </Select>
                </Col>
            </Row>
            <Row style="margin-top: 10px">
                <EmailSelect :isMultiple="false" ref="confirm_person">
                    <span style="display: inline-block;width:120px;text-align: right;" slot>确认人：</span>
                </EmailSelect>
            </Row>
            <Row style="margin-top: 10px">
                <EmailSelect :isMultiple="true" ref="cc_person">
                    <span style="display: inline-block;width:120px;text-align: right;" slot>抄送人：</span>
                </EmailSelect>
                <Col style="margin-left: 20px" span="1">
                    <PublishApplyButton
                        :select_data="select_data_info"
                        :app_array="data_info"
                        :iter_id="iter_id"
                        :br_name="br_name"
                        :env="env"
                        :code_type="code_type"
                        :group="group"
                        :apply_content="apply_content"
                        @getApplyParam="getApplyParam"
                    ></PublishApplyButton>
                </Col>
                <Col style="margin-left: 20px" span="2">
                    <CancelPublishApply :iter_id="iter_id"></CancelPublishApply>
                </Col>
                <Col style="margin-left: 20px" span="2">
                    <LastApplyModal
                        ref="last_apply_modal"
                        :branch_version="br_name"
                        :environment="env"
                        :group="group"
                    ></LastApplyModal>
                </Col>
                <Col style="margin-left: 20px " span="2">
                    <div>
                        <h2>
                            申请历史<a
                                href="http://paas.hongkou.howbuy.com/report/#/notebook/2GJU8SF6J/paragraph/20210928-163206_681554684?asIframe"
                                target="abc"
                                >查询</a
                            >
                        </h2>
                    </div>
                </Col>
            </Row>

            <Row style="margin-top: 10px">
                <i-col style="margin: 5px" span="2">
                    <span style="text-align: right; display: inline-block;width:120px;">申请说明:</span>
                </i-col>
                <Col style="margin-left: 10px" span="1">
                    <Input
                        v-model="apply_content"
                        type="textarea"
                        :rows="3"
                        placeholder="输入产线说明"
                        style="width: 200px"
                    />
                </Col>
            </Row>
            <Row style="margin-top: 10px">
                <PublishApplyTable
                    :iter_id="iter_id"
                    :env="env"
                    :br_name="br_name"
                    :is_refresh="is_publish_apply_refresh"
                    @set_selected_info="get_table_select_info"
                    @set_table_info="get_table_info"
                    ref="publish_apply_table"
                ></PublishApplyTable>
            </Row>
        </Card>
    </div>
</template>

<script>
import store from '@/spider-store'
import EmailSelect from '@/spider-components/publish-components/email-select/howbuy-email-select'
import PublishApplyTable from './components/publish-apply-table.vue'
import PublishApplyButton from './components/publish-apply-button.vue'
import CancelPublishApply from './components/cancel-publish-apply.vue'
import LastApplyModal from '../mobile-apply/components/last-apply-modal.vue'
import { getPackageTypeInIterApi } from '@/spider-api/get-iter-info'

export default {
    name: 'MobilePublishApply',
    data() {
        return {
            br_name: '',
            group: store.state.python_group,
            data_info: [],
            select_data_info: [],
            iter_id: store.state.python_iterationID,
            env: '',
            apply_content: '',
            code_type: store.state.python_code_type,
            logic_suite_list: [
                {
                    logic_suite_code: 'beta',
                    logic_suite_label: 'beta-灰度阶段'
                },
                {
                    logic_suite_code: 'prod',
                    logic_suite_label: 'prod-生产阶段'
                }
            ]
        }
    },
    props: {
        is_publish_apply_refresh: Boolean
    },
    components: {
        EmailSelect,
        PublishApplyTable,
        PublishApplyButton,
        CancelPublishApply,
        LastApplyModal
    },
    methods: {
        init() {
            this.group = store.state.python_group
            this.br_name = store.state.python_branch_version
            this.iter_id = store.state.python_iterationID
            this.code_type = store.state.python_code_type
            this.env = ''
            this.$refs.confirm_person.init()
            this.$refs.cc_person.init()
            getPackageTypeInIterApi(store.state.python_iterationID).then(res => {
                this.$refs.publish_apply_table.loopTableData()
            })
        },
        get_package_param_info(select_data_info) {
            this.select_data_info = select_data_info
        },
        get_table_select_info(select_data_info) {
            let select_data = []
            let select_app = []
            select_data_info.forEach(item => {
                if (!select_app.includes(item.app_name)) {
                    select_app.push(item.app_name)
                    select_data.push(item)
                }
            })
            this.select_data_info = select_data
        },
        get_table_info(data_info) {
            this.data_info = data_info
        },
        envChange() {
            this.$refs.last_apply_modal.lastApplyButtonShow = true
            this.$refs.publish_apply_table.getTableData(this.env)
        },
        getApplyParam(callback) {
            let basic_params = {
                suite_code: this.env,
                phase_code: this.env,
                iteration_id: this.iter_id,
                proposer: this.$refs.confirm_person.selectValue,
                cc: this.$refs.cc_person.selectValue,
                br_name: this.br_name,
                apply_content: this.apply_content,
                description: this.apply_content,
                action_item: 'python_apply',
                opt_user: this.$store.state.user.userName
            }

            if (basic_params.cc == undefined || basic_params.cc == '') {
                this.$Notice.error({ desc: '请选择邮件抄送人', duration: 0 })
                callback([])
                return
            }
            let apply_list = []
            for (let row of this.select_data_info) {
                let apply_obj = JSON.parse(JSON.stringify(basic_params))
                apply_obj['app_name'] = row.app_name
                apply_obj['repo_path'] = row.git_path
                apply_obj['package_type'] = row.package_type
                apply_list.push(apply_obj)
            }
            callback(apply_list)
        }
    }
}
</script>

<style scoped></style>
