<template>
    <div>
        <Button
            ghost
            type="primary"
            style="text-align: left; display: inline-block; width:60px;margin-top:-1px"
            @click="apply"
            >申请
        </Button>

        <Modal
            v-model="alert_modal"
            title="以下应用正在申请，是否要继续申请？"
            :columns="alert_table_columns"
            :tableData="alert_modal_data"
            @on-cancel="cancelApply"
        >
            <div>
                <Row>
                    <Table :columns="alert_table_columns" :data="alert_modal_data"> </Table>
                </Row>
            </div>

            <div slot="footer">
                <Button @click="apply()" :disabled="continue_apply_btn">继续申请</Button>
                <Button @click="cancelApply()">取消申请</Button>
            </div>
        </Modal>
        <ConfirmModal
            ref="hd_confirm_modal"
            titleInfo="以下为正在进行灰度的分支，请于相关人员确认项目情况无冲突后继续申请！"
            :IsModalred="true"
            :columns="conflict_table_columns"
            :tableData="conflict_table_data"
            @continue="publishApply"
        ></ConfirmModal>
    </div>
</template>

<script>
import { pyPublishApplyApi } from '@/spider-api/python'
import { formatDate } from '@/spider-api/h5-common'
import ConfirmModal from '../../mobile-apply/components/confirm-modal.vue'
import { h5ProApplyNotice } from '@/spider-api/iter-plan'
import { h5IterConfirmStatusApi } from '@/spider-api/publish'

export default {
    name: 'PublishApplyButton',
    components: {
        ConfirmModal
    },
    data() {
        return {
            alert_modal: false,
            continue_apply_btn: false,
            alert_modal_data: [],
            alert_table_columns: [
                {
                    title: '应用',
                    key: 'app_name'
                },
                {
                    title: '状态',
                    key: 'status_display',
                    render: (h, params) => {
                        return h('div', [h('p', { props: {}, style: { color: 'red' } }, params.row.status_display)])
                    }
                },
                {
                    title: '上一次申请时间',
                    key: 'operate_time',
                    //sortable: true,
                    render: (h, params) => {
                        let value = params.row.operate_time
                        if (value) {
                            value = formatDate(new Date(params.row.operate_time), 'yyyy-MM-dd hh:mm:ss')
                        }
                        return h('div', value)
                    }
                },
                {
                    title: '申请提示',
                    key: 'alert_content',
                    //sortable: true,
                    render: (h, params) => {
                        return h('div', [
                            h(
                                'p',
                                { props: {}, style: { color: params.row.alert_modal_color } },
                                params.row.alert_content
                            )
                        ])
                    }
                }
            ],
            conflict_table_data: [],
            conflict_table_columns: [
                {
                    title: '灰度环境的分支',
                    key: 'br_name',
                    align: 'center'
                },
                {
                    title: '灰度环境的应用',
                    key: 'app_name',
                    align: 'center'
                },
                {
                    title: '申请人',
                    key: 'user_name',
                    align: 'center'
                },
                {
                    title: '申请时间',
                    key: 'operate_time',
                    align: 'center',
                    render: (h, params) => {
                        return h('div', formatDate(new Date(params.row.operate_time), 'yyyy-MM-dd hh:mm'))
                    }
                }
            ]
        }
    },
    props: {
        // publish_apply_data: {
        //       type: Array,
        //       default:() =>[]
        //   },
        select_data: {
            type: Array,
            default: () => []
        },
        app_array: {
            type: Array,
            default: () => []
        },
        iter_id: {
            type: String,
            default: ''
        },
        br_name: {
            type: String,
            default: ''
        },
        code_type: {
            type: String,
            default: ''
        },
        env: {
            type: String,
            default: ''
        },
        group: {
            type: String,
            default: ''
        },
        apply_content: {
            type: String,
            default: ''
        }
    },
    methods: {
        cancelApply() {
            this.alert_modal = false
        },
        checkStatus(select_app_array) {
            var myDate = new Date()
            if (select_app_array.length == 0) {
                this.$Notice.error({
                    desc: '请选择申请应用',
                    duration: 0
                })
                return false
            }
            if (this.env == 'prod') {
                console.log('this.selected_array111====' + JSON.stringify(select_app_array))
                console.log('this.app_array111====' + this.app_array)
                if (select_app_array.length != this.app_array.length) {
                    // this.$Notice.warning({
                    //     desc: '生产环境需选择所有应用申请',
                    //     duration: 0
                    // })
                    return false
                }
            }

            for (let row of select_app_array) {
                // 发布状态校验
                if (row['status_display'] == '发布中') {
                    let end_time = formatDate(myDate, 'yyyy-MM-dd h:m:s').replace(/\-/g, '/')
                    let begin_time = formatDate(new Date(row['operate_time']), 'yyyy-MM-dd hh:mm:ss').replace(
                        /\-/g,
                        '/'
                    )
                    let t = parseInt(new Date(end_time) - new Date(begin_time)) / 1000 / 60
                    if (t > 5) {
                        var alert_content = '上一次申请是' + t.toFixed(1) + '分钟之前'
                        var alert_modal_color = 'green'
                    } else {
                        this.continue_apply_btn = true
                        var apply_time = parseInt(5) - t
                        var alert_content = apply_time.toFixed(1) + '分钟后再申请'
                        var alert_modal_color = 'red'
                    }
                    this.alert_modal_data.push({
                        app_name: row['app_name'],
                        status_display: row['status_display'],
                        operate_time: row['operate_time'],
                        alert_content: alert_content,
                        alert_modal_color: alert_modal_color
                    })
                }
            }
            if (this.alert_modal_data.length != 0) {
                this.alert_modal = true
                return false
            } else {
                return true
            }
        },

        publishApply() {
            this.$refs.hd_confirm_modal.closeModal()
            this.checkStatus(this.select_data)
            this.$emit('getApplyParam', val => {
                if (val.length > 0) {
                    pyPublishApplyApi(val)
                        .then(res => {
                            if (res.data.status == 'failed') {
                                //失败
                                this.$Notice.error({ desc: res.data.message, duration: 0 })
                            } else {
                                //成功
                                this.$Notice.success({ desc: res.data.message, duration: 5 })
                                // 发送给领导 确认邮件
                                this.sendAffirmEmail(val)
                            }
                            this.$Spin.hide()
                        })
                        .catch(err => {
                            this.$Spin.hide()
                            this.$Notice.error({ desc: err, duration: 0 })
                        })
                }
            })
        },
        //发送领导确认邮件

        sendAffirmEmail(apply_val) {
            h5IterConfirmStatusApi(this.iter_id, this.env).then(res => {
                if (res.data.status != 'success') {
                    let email_object = {
                        iter_id: this.iter_id,
                        proposer: '',
                        url: 'http://' + window.location.host,
                        env: this.env,
                        app_name_list: [],
                        fund_end_ver: '',
                        piggy_end_ver: '',
                        app_version: '',
                        br_name: this.br_name,
                        fund_h5_version: '',
                        piggy_h5_version: '',
                        h5_env: '',
                        apply_content: this.apply_content
                    }
                    for (let i of apply_val) {
                        email_object.proposer = i.proposer
                        // 检验发布申请人
                        if (email_object.proposer == '' || email_object.proposer.length == 0) {
                            this.$Notice.error({
                                desc: '今日未收到确认，请选择确认人',
                                duration: 0
                            })
                            return false
                        }
                        email_object.app_name_list.push(i.app_name)
                    }
                    h5ProApplyNotice(
                        email_object.iter_id,
                        email_object.proposer,
                        email_object.url,
                        email_object.env,
                        email_object.app_name_list,
                        email_object.fund_end_ver,
                        email_object.piggy_end_ver,
                        email_object.app_version,
                        email_object.br_name,
                        email_object.fund_h5_version,
                        email_object.piggy_h5_version,
                        email_object.h5_env,
                        email_object.apply_content
                    )
                        .then(result => {
                            if (result.data.status === 'success') {
                                this.$Notice.success({
                                    desc: result.data.msg,
                                    duration: 5
                                })
                            } else {
                                this.$Spin.hide()

                                this.$Notice.error({
                                    desc: result.data.msg,
                                    duration: 0
                                })
                            }
                        })
                        .catch(err => {
                            this.$Spin.hide()
                            this.$Notice.error({
                                desc: err.response.data.msg,
                                duration: 0
                            })
                        })
                }
            })
        },

        /**
         * 点击【申请】按钮，页面数据校验检查
         */
        apply() {
            this.alert_modal = false
            if (this.env == 'prod') {
                // 开始生产申请
                this.publishApply()
            } else {
                // 开始灰度申请
                this.hdApply()
            }
        },

        /**
         * 灰度申请,检查是否有其它分支正在进入灰度
         */
        hdApply() {
            this.publishApply()
            // let app_name_list = []
            // for (let i of this.select_data) {
            //     app_name_list.push(i.app_name)
            // }
            // let req = { iteration_id: this.iter_id, app_name_list: app_name_list, env: this.env }
            // HdStatusCheckApi(req).then(res => {
            //     if (res.data.status === 'success') {
            //         this.publishApply()
            //     } else {
            //         // 存在差异打开确认框今日未收到确认，请选择确认人
            //         this.$refs.hd_confirm_modal.modal_confirm = true
            //         this.conflict_table_data = res.data.data['conflict_app']
            //     }
            // })
        }
    }
}
</script>

<style scoped></style>
