<template>
    <div>
        <Button
            ghost
            type="primary"
            style="text-align: left; display: inline-block; width:100px;margin-top:-1px"
            @click="cancelProdApply"
        >取消产线申请
        </Button>
        <Modal v-model="modal_cancel_apply" @on-cancel="closeApplyModal">
            <p slot="header" style="color:gray;">
                <Icon type="md-clipboard"></Icon>
                <span>取消产线申请</span>
            </p>
            <span>迭代：{{ iter_id }}。 </span>
            <span>{{ modal_cancel_apply_title }}</span>
            <div slot="footer">
                <Button @click="closeApplyModal">关闭</Button>
                <Button ghost type="error" @click="cancelProApplyAck">确定</Button>
            </div>
        </Modal>
    </div>
</template>

<script>
import {cancel_prod_apply, cancelProApply} from "@/spider-api/iter-plan";
import store from "@/spider-store";

export default {
    name: "CancelPublishApply",
    data() {
        return {
            modal_cancel_apply: false,
            modal_cancel_apply_title: ''
        }
    },
    props: {
        iter_id: {
            type: String,
            default: ""
        },
    },
    methods: {
        cancelProdApply() {
            this.modal_cancel_apply = false
            this.modal_cancel_apply_title = ''
            cancel_prod_apply(this.iter_id).then(res => {
                if (res.data.status === 'success') {
                    this.modal_cancel_apply_title = res.data.msg
                    this.modal_cancel_apply = true
                } else {
                    this.$Message.error(res.data.msg)
                }
            })
        },
        closeApplyModal() {
            this.modal_cancel_apply = false
        },
        cancelProApplyAck() {
            this.modal_cancel_apply = false
            cancelProApply(this.iter_id)
                .then(result => {
                    if (result.data.status === "success") {
                        this.$Message.success(result.data.msg);
                    } else {
                        this.$Message.error(result.data.msg);
                    }
                })
                .catch(err => {
                    this.$Message.error(err.response.data.msg)
                });
        },
    }
}
</script>

<style scoped>

</style>
