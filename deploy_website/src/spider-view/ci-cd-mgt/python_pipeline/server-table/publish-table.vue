<template>
    <Card>
        <div>
            <tables stripe v-model="app_data" :columns="columns" ref="my_table"> </tables>
        </div>
        <Modal v-model="detail_modal" title="详情" width="70%" :styles="{ height: '600px' }" footer-hide>
            <Card>
                <Row v-html="publish_detail_info"> </Row>
            </Card>
        </Modal>
        <CheckNotice
            v-model="check_notice"
            :check_notice_title="check_notice_title"
            :check_table_data="check_notice_data"
            :check_notice_msg="check_notice_msg"
        ></CheckNotice>
    </Card>
</template>

<script>
import Tables from '@/components/tables'
import { getPublishAppBindApi, getProdMultiNodeOperateInfoApi } from '@/spider-api/prod-publish/publish-info'
import { prodMultiNodeOperateApi } from '@/spider-api/prod-publish/prod-operate'
import ConfigUpdateNotice from '@/spider-components/spider-notice/config-update-notice'
import CheckNotice from '@/spider-components/spider-notice/check-notice'

export default {
    name: 'PublishTable',
    components: {
        Tables,
        ConfigUpdateNotice,
        CheckNotice
    },
    props: {
        iteration_id: String,
        is_prod_refresh: Boolean,
        publish_type: String
    },
    data() {
        return {
            check_notice: false,
            check_notice_data: [],
            check_notice_title: '校验失败',
            check_notice_msg: '',
            detail_modal: false,
            is_open: true,
            disable_publish: false,
            app_name: '',
            publish_detail_info: '',
            columns: [
                {
                    title: '节点地址',
                    align: 'center',
                    key: 'node_ip',
                    width: 140,
                    render: (h, params) => {
                        let suite_code = params.row.suite_code
                        let node_env_color = '#17233d'
                        if (suite_code) {
                            if (suite_code.indexOf('-zb') != -1) {
                                node_env_color = '#19be6b'
                            } else if (suite_code.indexOf('-prod') != -1) {
                                node_env_color = '#17233d'
                            } else if (suite_code.indexOf('-hd') != -1) {
                                node_env_color = '#ff9900'
                            }
                        }

                        return h('div', [
                            h(
                                'p',
                                {
                                    style: {
                                        display: 'inline',
                                        color: node_env_color
                                    }
                                },
                                params.row.node_ip
                            ),
                            h(
                                'a',
                                {
                                    attrs: {
                                        class: 'ivu-icon ivu-icon-ios-information-circle-outline'
                                    },
                                    style: {
                                        color: node_env_color,
                                        'font-size': '18px',
                                        'margin-left': '3px'
                                    }
                                },
                                ''
                            )
                        ])
                    }
                },
                {
                    title: '环境套',
                    align: 'center',
                    width: 80,
                    key: 'suite_code',
                    tooltip: true
                },
                {
                    title: '组名',
                    align: 'center',
                    width: 100,
                    key: 'deploy_group_name',
                    tooltip: true
                },
                {
                    title: '状态',
                    align: 'center',
                    width: 100,
                    key: 'publish_status',
                    tooltip: true,
                    render: (h, params) => {
                        let stat_dict = {
                            running: '执行中',
                            success: '执行成功',
                            failure: '执行失败',
                            compile_running: '编译中',
                            publish_success: '发布成功',
                            script_failure: '校验失败',
                            aborted: '已终止',
                            warning: '未知结果'
                        }
                        let status = params.row.publish_status
                        if (status) {
                            if (stat_dict.hasOwnProperty(status)) {
                                var status_display = stat_dict[status]
                            } else {
                                var status_display = status
                            }
                            if (status.indexOf('success') != -1) {
                                return h('p', { style: { color: 'green' } }, status_display)
                            } else if (status.indexOf('failure') != -1) {
                                return h(
                                    'Tooltip',
                                    {
                                        props: {
                                            placement: 'top',
                                            content: params.row.deploy_stages,
                                            'max-width': 500,
                                            transfer: true
                                        },
                                        style: { color: 'red' }
                                    },
                                    status_display
                                )
                            } else if (status.indexOf('warning') != -1) {
                                return h('p', { style: { color: '#ff9900' } }, status_display)
                            } else {
                                return h('p', { style: { color: '#515a6e' } }, status_display)
                            }
                        }
                    }
                },
                {
                    title: '操作',
                    align: 'center',
                    key: 'operate',
                    render: (h, params) => {
                        let operate_disable_publish = params.row.batch_publish === 1
                        if (this.iteration_id === '') {
                            this.disable_publish = true
                            operate_disable_publish = true
                        }
                        return h('div', [
                            h(
                                'Button',
                                {
                                    attrs: {
                                        class: 'ivu-btn ivu-btn-primary ivu-btn-small ivu-btn-ghost'
                                    },
                                    props: {
                                        loading: params.row.loading_btn
                                    },
                                    style: {
                                        margin: '2px'
                                    }
                                },
                                [
                                    h(
                                        'Poptip',
                                        {
                                            props: {
                                                confirm: true,
                                                transfer: true,
                                                title: '发布+启动 (' + params.row.node_ip + ')',
                                                size: 'small'
                                            },
                                            on: {
                                                'on-ok': () => {
                                                    let node_list = [params.row.node_ip]
                                                    // params.row.loading_btn = true
                                                    this.prodMultiNodeOperate(
                                                        'deploy',
                                                        node_list,
                                                        params.row.suite_code,
                                                        params
                                                    )
                                                },
                                                'on-cancel': () => {
                                                    this.$Message.info('取消')
                                                }
                                            }
                                        },
                                        '发布+启动'
                                    )
                                ]
                            ),
                            h(
                                'Button',
                                {
                                    attrs: {
                                        class: 'ivu-btn ivu-btn-primary ivu-btn-small ivu-btn-ghost'
                                    },
                                    props: {
                                        loading: params.row.loading_btn
                                    },
                                    style: {
                                        margin: '2px'
                                    }
                                },
                                [
                                    h(
                                        'Poptip',
                                        {
                                            props: {
                                                confirm: true,
                                                transfer: true,
                                                title: '重启 (' + params.row.node_ip + ')',
                                                size: 'small'
                                            },
                                            on: {
                                                'on-ok': () => {
                                                    let node_list = [params.row.node_ip]
                                                    // params.row.loading_btn = true
                                                    this.prodMultiNodeOperate(
                                                        'restart',
                                                        node_list,
                                                        params.row.suite_code,
                                                        params
                                                    )
                                                },
                                                'on-cancel': () => {
                                                    this.$Message.info('取消')
                                                }
                                            }
                                        },
                                        '重启'
                                    )
                                ]
                            ),
                            h(
                                'Button',
                                {
                                    attrs: {
                                        class: 'ivu-btn ivu-btn-primary ivu-btn-small ivu-btn-ghost'
                                    },
                                    props: {
                                        // disabled: default_btn
                                        loading: params.row.loading_btn
                                    },
                                    style: {
                                        margin: '2px'
                                    }
                                },
                                [
                                    h(
                                        'Poptip',
                                        {
                                            props: {
                                                confirm: true,
                                                transfer: true,
                                                title: '停止 (' + params.row.node_ip + ')',
                                                size: 'small'
                                            },
                                            on: {
                                                'on-ok': () => {
                                                    let node_list = [params.row.node_ip]
                                                    // params.row.loading_btn = true
                                                    this.prodMultiNodeOperate('stop', node_list, params.row.suite_code),
                                                        params
                                                },
                                                'on-cancel': () => {
                                                    this.$Message.info('取消')
                                                }
                                            }
                                        },
                                        '停止'
                                    )
                                ]
                            ),
                            // h(
                            //     'Button',
                            //     {
                            //         attrs: {
                            //             class: 'ivu-btn ivu-btn-primary ivu-btn-small ivu-btn-ghost'
                            //         },
                            //         props: {
                            //             disabled: operate_disable_publish,
                            //             loading: params.row.loading_btn
                            //         },
                            //         style: {
                            //             margin: '2px'
                            //         }
                            //     },
                            //     [
                            //         h(
                            //             'Poptip',
                            //             {
                            //                 props: {
                            //                     confirm: true,
                            //                     transfer: true,
                            //                     title: '配置更新 (' + params.row.node_ip + ')',
                            //                     size: 'small'
                            //                 },
                            //                 on: {
                            //                     'on-ok': () => {
                            //                         let node_list = [params.row.node_ip]
                            //                         params.row.loading_btn = true
                            //                         this.prodMultiNodeOperate(
                            //                             'update',
                            //                             node_list,
                            //                             params.row.suite_code
                            //                         )
                            //                     },
                            //                     'on-cancel': () => {
                            //                         this.$Message.info('取消')
                            //                     }
                            //                 }
                            //             },
                            //             '配置更新'
                            //         )
                            //     ]
                            // ),
                            h(
                                'Button',
                                {
                                    attrs: {
                                        class: 'ivu-btn ivu-btn-primary ivu-btn-small ivu-btn-ghost'
                                    },
                                    props: {
                                        disabled: operate_disable_publish,
                                        loading: params.row.loading_btn
                                    },
                                    style: {
                                        margin: '2px'
                                    }
                                },
                                [
                                    h(
                                        'Poptip',
                                        {
                                            props: {
                                                confirm: true,
                                                transfer: true,
                                                title: '代码更新 (' + params.row.node_ip + ')',
                                                size: 'small'
                                            },
                                            on: {
                                                'on-ok': () => {
                                                    let node_list = [params.row.node_ip]
                                                    // params.row.loading_btn = true
                                                    this.prodMultiNodeOperate(
                                                        'code_update',
                                                        node_list,
                                                        params.row.suite_code,
                                                        params
                                                    )
                                                },
                                                'on-cancel': () => {
                                                    this.$Message.info('取消')
                                                }
                                            }
                                        },
                                        '代码更新'
                                    )
                                ]
                            ),
                            h(
                                'Button',
                                {
                                    props: {
                                        type: 'default',
                                        size: 'small'
                                    },
                                    style: {
                                        margin: '2px'
                                    },
                                    on: {
                                        click: () => {
                                            this.showDetail(params.row.publish_message)
                                        }
                                    }
                                },
                                '详情'
                            )
                        ])
                    }
                }
                // {
                //     title: '上线验证',
                //     align: 'center',
                //     key: 'operate',
                //     width: 150,
                //     render: (h, params) => {
                //         if (this.iteration_id === '') {
                //             this.disable_publish = true
                //         }
                //         return h('div', [
                //             h(
                //                 'Button',
                //                 {
                //                     attrs: {
                //                         class: 'ivu-btn ivu-btn-primary ivu-btn-small ivu-btn-ghost'
                //                     },
                //                     props: {
                //                         disabled: this.disable_publish,
                //                         loading: params.row.loading_btn
                //                     },
                //                     style: {
                //                         margin: '2px'
                //                     },
                //                     on: {
                //                         click: () => {
                //                             let node_list = [params.row.node_ip]
                //                             this.prodMultiNodeOperate('verify', node_list, params.row.suite_code)
                //                         }
                //                     }
                //                 },
                //                 '服务验证'
                //             )
                //         ])
                //     }
                // }
            ],
            app_data: []
        }
    },
    methods: {
        getAppPublishInfo(app_name) {
            let vm = this

            this.app_name = app_name
            // 获取节点绑定信息
            getPublishAppBindApi(this.publish_type, app_name, this.iteration_id)
                .then(res => {
                    if (res.data.status === 'success') {
                        vm.app_data = res.data.data
                        let node_list = []
                        for (let row of vm.app_data) {
                            node_list.push(row['node_ip'])
                        }
                        this.queryStatus(app_name, node_list, 'prod')
                    } else {
                        vm.app_data = []
                        vm.$Message.error(res.data.msg)
                    }
                })
                .catch(function(err) {
                    vm.app_data = []
                    vm.$Message.error('发生异常')
                })
        },
        prodMultiNodeOperate(op_type, node_list, suite_code, params) {
            let data = {
                op_type: op_type,
                iteration_id: this.iteration_id,
                node_list: node_list,
                app_name: this.app_name,
                suite_code: suite_code
            }
            this.app_data[params.index].loading_btn = true
            prodMultiNodeOperateApi(data).then(res => {
                if (res.data.status === 'success') {
                    this.$Message.info(res.data.msg)
                    this.queryStatus(this.app_name, node_list, 'prod')
                } else {
                    this.$Message.error(res.data.msg)
                    this.app_data[params.index].loading_btn = false
                }
            })
        },
        queryStatus(app_name, node_list, region_group) {
            let vm = this
            getProdMultiNodeOperateInfoApi(this.iteration_id, app_name, node_list, region_group)
                .then(res => {
                    let is_running = false
                    let new_data = []
                    let running_list = []
                    if (res.data.status === 'success') {
                        for (let row of vm.app_data) {
                            let status_mark = 0
                            for (let res_row of res.data.data) {
                                if (res_row['node_ip'] == row['node_ip']) {
                                    status_mark = 1
                                    row['publish_status'] = res_row['publish_status']
                                    row['publish_message'] = res_row['publish_message']
                                    row['deploy_stages'] = res_row['deploy_stages']
                                    row['request_status'] = res_row['request_status']
                                    running_list.push(row['publish_status'])

                                    if (row['publish_status'] === 'running') {
                                        row['loading_btn'] = true
                                        is_running = true
                                    } else {
                                        if (res_row['lock_status'] == true) {
                                            row['loading_btn'] = true
                                        } else {
                                            row['loading_btn'] = false
                                        }
                                        // row["loading_btn"] = false
                                    }

                                    new_data.push({ ...row, op_time: res_row['op_time'] })
                                }
                            }
                            if (status_mark == 0) {
                                new_data.push(row)
                            }
                        }
                        // 对new_data进行数据过滤，过滤条件：IP+应用 相同的只保留一条，保留的一条取重复数据中op_time最大的
                        let filter_data = []
                        for (let row of new_data) {
                            let existingIndex = -1

                            // 查找是否存在相同的 IP+应用 组合
                            for (let i = 0; i < filter_data.length; i++) {
                                if (
                                    filter_data[i]['node_ip'] == row['node_ip'] &&
                                    filter_data[i]['app_name'] == row['app_name']
                                ) {
                                    existingIndex = i
                                    break
                                }
                            }

                            if (existingIndex !== -1) {
                                // 存在重复数据，比较时间并保留最新的
                                if (filter_data[existingIndex]['op_time'] < row['op_time']) {
                                    // 直接替换整个对象，避免引用污染
                                    filter_data[existingIndex] = { ...row }
                                }
                            } else {
                                // 不存在重复，直接添加新数据的副本
                                filter_data.push({ ...row })
                            }
                        }
                        vm.app_data = filter_data
                        if (is_running == true && vm.is_open && vm.is_prod_refresh) {
                            setTimeout(function() {
                                vm.queryStatus(app_name, node_list, region_group)
                            }, 10000)
                        }
                    } else {
                        vm.$Message.error(res.data.msg)
                        // 发布异常 处理 20220330
                        this.alertErrorNotice(res.data.msg)
                    }
                })
                .catch(function(err) {
                    vm.$Message.error('发生异常')
                })
        },
        // 提取 失败告警
        alertErrorNotice(publish_apply_message) {
            this.$Spin.hide()
            // 返回的错误类型可能不是json格式 20220329 by 帅
            try {
                this.check_notice_data = JSON.parse(publish_apply_message.replace(/'/g, '"'))
            } catch (e) {
                // 无法解析 需要清除 table数据 防止无法显示msg提示
                this.check_notice_data = []
                this.check_notice_msg = publish_apply_message
            }
            this.check_notice_title = '！！！！！！！！失败告警！！！！！！！！！'
            this.check_notice = true
        },

        showDetail(data_msg) {
            console.log('data_msg----', data_msg)
            this.publish_detail_info = data_msg && data_msg.replaceAll('\n', '<br/>')
            this.detail_modal = true
        }
    },

    destroyed() {
        this.is_open = false
    }
}
</script>

<style lang="less" scoped>
.table-bordered {
    border-collapse: collapse;
}

.table-bordered th,
.table-bordered td {
    border: 1px solid black;
    padding: 10px;
}

.button-container {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 10px;
    margin-top: 20px;
    padding: 20px;
}
</style>
