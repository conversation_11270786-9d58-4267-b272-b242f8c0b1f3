<!--
 * @Author: chao.bao <EMAIL>
 * @Date: 2025-08-11 17:13:13
 * @LastEditors: chao.bao <EMAIL>
 * @LastEditTime: 2025-08-15 13:44:15
 * @FilePath: /website_web/deploy_website/src/spider-view/ci-cd-mgt/python_pipeline/mobile-test/mobile-test.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div>
        <Card>
            <Row>
                <i-col style="margin: 10px;text-align: right" span="1">
                    <span style="text-align: right; display: inline-block;">组：</span>
                </i-col>
                <i-col style="margin: 10px" span="3">
                    <span style="text-align: left; display: inline-block;">{{ group }}</span>
                </i-col>
                <i-col style="margin: 10px;text-align: right" span="2">
                    <span style="text-align: right; display: inline-block;">分支版本：</span>
                </i-col>
                <i-col style="margin: 10px" span="3">
                    <span style="text-align: left; display: inline-block;">{{ branch_version }}</span>
                </i-col>
                <i-col style="margin: 10px" span="7">
                    <span style="text-align: left; display: inline-block;color: red"
                        >注：若以下应用的配置存放路径有改变，请及时告知</span
                    >
                </i-col>
            </Row>
            <Row>
                <Collapse v-model="collapse_value">
                    <!-- v-show="show_python" -->
                    <Panel name="python" :hideArrow="true">
                        python测试发布
                        <p slot="content">
                            <PythonTestPublish
                                :is_refresh="is_test_refresh"
                                ref="pythonTestPublishRef"
                            ></PythonTestPublish>
                        </p>
                    </Panel>
                </Collapse>
            </Row>
        </Card>
    </div>
</template>

<script>
import store from '@/spider-store'
import PythonTestPublish from './python-test-publish.vue'

import { getPackageTypeInIterApi } from '@/spider-api/get-iter-info'

export default {
    name: 'MobileTest',
    components: {
        PythonTestPublish
    },
    props: {
        is_test_refresh: Boolean
    },
    data() {
        return {
            collapse_value: ['python'],
            group: store.state.python_group,
            branch_version: store.state.python_branch_version,
            show_python: true
        }
    },
    mounted() {
        // this.init()
    },
    computed: {},
    methods: {
        //根据分支中选择不同的应用，控制不同Panel展开
        init() {
            this.group = store.state.python_group
            this.branch_version = store.state.python_branch_version
            this.show_python = true
            this.$refs.pythonTestPublishRef.init()
            // 根据迭代下存在的类型选择 展开那些页面
            // getPackageTypeInIterApi(store.state.setPyIterationID).then(res => {
            //     this.collapse_value = res.data.data['package_type_list']
            //     for (let package_type of this.collapse_value) {
            //         if (package_type == 'py') {
            //             this.show_python = true
            //             this.$refs.pythonTestPublishRef.init()
            //         }
            //     }
            // })
        }
    }
}
</script>

<style scoped></style>
