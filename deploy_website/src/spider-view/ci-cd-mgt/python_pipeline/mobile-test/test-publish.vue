<template>
    <div>
        <Button
            type="primary"
            :loading="button_disabled"
            style="text-align: left; display: inline-block; margin:5px"
            @click="batch_publish"
        >
            <slot>批量构建发布</slot>
        </Button>
        <Modal :styles="{ width: '70%' }" v-model="dist_publish_modal" title="批量发布" ref="publishModal">
            <PackageParamsEdit
                v-for="item in publish_info_list"
                :key="item.iter_id"
                :iter_id="iter_id"
                :br_name="br_name"
                :checked="dist_publish_modal"
                :item="item"
            ></PackageParamsEdit>
            <div slot="footer">
                <Button @click="dist_publish_modal_cancel">取消</Button>
                <Button type="primary" @click="publish(publish_info_list, 'dist_publish_modal')">确定</Button>
            </div>
        </Modal>
    </div>
</template>

<script>
import { getErrNoticeShowTime, pipelineEnvBindGet, userActionGet } from '@/spider-api/h5'
import { PythonPipelineApi } from '@/spider-api/python'
import PackageParamsEdit from './package-params-edit.vue'
import DistPackageParamsEdit from '@/spider-components/spider-buttons/dist-package-params-edit'
import AppPublishParamsEdit from '@/spider-components/spider-buttons/app-publish-params-edit'

export default {
    name: 'TestPublish',
    components: {
        PackageParamsEdit,
        AppPublishParamsEdit,
        DistPackageParamsEdit
    },
    props: {
        publish_data: Array,
        action_item: String,
        iter_id: String,
        br_name: String,
        tag_name: String,
        code_type: String,
        package_type: Array
    },
    data() {
        return {
            spinShow: false,
            bind_env_list: {},
            app_publish_modal: false,
            dist_publish_modal: false,
            h5_dist_publish_modal: false,
            app_component_publish_modal: false,
            publish_info_list: [],
            button_name: '批量发布',
            button_disabled: false
        }
    },
    methods: {
        publish_status_check(build_info_list) {
            for (let row of build_info_list) {
                if (row.compile_status === 'compile_running' || row.compile_status === 'running') {
                    this.$Notice.error({
                        desc:
                            '以下应用' + row.app_name + '正在进行' + row.compile_status + '，请等待编译结束后再次发布',
                        duration: 100
                    })
                    return false
                }
                if (row.publish_status === 'publish_running' || row.publish_status === 'running') {
                    this.$Notice.error({
                        desc:
                            '以下应用' + row.app_name + '正在进行' + row.publish_status + '，请等待发布结束后再次发布',
                        duration: 100
                    })
                    return false
                }
            }
            return true
        },

        dist_publish_modal_cancel() {
            this.dist_publish_modal = false
        },
        app_publish_modal_cancel() {
            this.app_publish_modal = false
        },
        buttonWait(num) {
            let vm = this
            setTimeout(function() {
                if (num > 0) {
                    num = num - 1
                    vm.button_name = 'Loading ' + num + 's'
                    vm.buttonWait(num)
                } else {
                    vm.button_name = '批量发布'
                    vm.button_disabled = false
                }
            }, 1000)
        },

        batch_publish() {
            if (this.publish_status_check(this.publish_data) == false) {
                return false
            }
            this.h5_batch_publish()
        },

        h5_batch_publish() {
            this.publish_info_list = []
            pipelineEnvBindGet(this.iter_id).then(res => {
                this.$Spin.show({
                    render: h => {
                        return h('div', [
                            h('Icon', {
                                class: 'demo-spin-icon-load',
                                props: {
                                    type: 'ios-loading',
                                    size: 18
                                }
                            }),
                            h('div', '发布分支计算中请稍等。。。')
                        ])
                    }
                })

                setTimeout(() => {
                    this.$Spin.hide()
                }, 1000)
                this.bind_env_list = res.data.data['bind_env_list']

                let selectTableData = []
                let vm = this
                this.get_user_action(this.action_item).then(data => {
                    if (data) {
                        selectTableData =
                            data.length > 0
                                ? JSON.parse(
                                      data
                                          .replace(/False/g, 'false')
                                          .replace(/True/g, 'true')
                                          .replace(/'/g, '"')
                                  )
                                : data
                    }
                    for (let row of vm.publish_data) {
                        let publish_row = {}
                        publish_row['phase_code'] = 'test'
                        publish_row['iteration_id'] = vm.iter_id
                        publish_row['br_name'] = vm.br_name
                        publish_row['code_type'] = this.code_type
                        publish_row['action_item'] = vm.action_item
                        publish_row['app_name'] = row['app_name']
                        publish_row['package_type'] = row['package_type']
                        publish_row['_checked'] = row['_checked']
                        publish_row['suite_code'] = publish_row['suite_code']
                        publish_row['opt_user'] = this.$store.state.user.userName
                        if (row['app_name'] in vm.bind_env_list) {
                            publish_row['suite_code'] = vm.bind_env_list[row['app_name']]
                            publish_row['suite_code'] = publish_row['suite_code'].join(',')
                        } else {
                            publish_row['suite_code'] = ''
                        }

                        vm.publish_info_list.push(publish_row)
                    }

                    if (vm.publish_info_list.length == 0) {
                        vm.$Notice.error({
                            desc: '至少选择一个应用',
                            duration: getErrNoticeShowTime()
                        })
                        return false
                    }
                    this.$Spin.hide()
                    vm.dist_publish_modal = true
                })
            })
        },
        get_user_action(action_item) {
            return new Promise(function(resolve, reject) {
                userActionGet(action_item)
                    .then(res => {
                        resolve(res.data.data)
                    })
                    .catch(err => {
                        reject(false)
                    })
            })
        },

        /**
         * 发布
         */
        publish(publish_info_list, type) {
            publish_info_list = this.publish_info_list
            let vm = this
            this.button_disabled = true
            this.buttonWait(10)
            if (type == 'dist_publish_modal') {
                this.dist_publish_modal = false
            }
            PythonPipelineApi(publish_info_list).then(res => {
                if (res.data.code == '0000') {
                    return true
                    // 接口返回200但有可能是失败的
                } else {
                    vm.$Notice.error({
                        desc: res.data.message,
                        duration: 100
                    })
                    return false
                }
            })
        }
    }
}
</script>

<style scoped></style>
