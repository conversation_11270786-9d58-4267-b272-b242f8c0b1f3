<!--
 * @Author: chao.bao <EMAIL>
 * @Date: 2025-08-12 17:13:43
 * @LastEditors: chao.bao <EMAIL>
 * @LastEditTime: 2025-08-12 17:27:03
 * @FilePath: /website_web/deploy_website/src/spider-view/ci-cd-mgt/python_pipeline/mobile-test/package-params-edit.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div>
        <Row style="margin-top: 10px">
            <Card>
                <Row v-show="item.suite_code != undefined">
                    <i-col style="margin: 10px;text-align: right" span="8">
                        <span style="text-align: right; display: inline-block;">{{ item.app_name }}发布环境:</span>
                    </i-col>
                    <Col span="14">
                        <Select
                            v-model="item.suite_code"
                            @on-change="changeSuiteCode"
                            span="10"
                            style="margin:10px"
                            filterable
                        >
                            <Option v-for="item in app_env_list" :value="item" :label="item" :key="item"
                                >{{ item }}
                            </Option>
                        </Select>
                    </Col>
                </Row>
            </Card>
        </Row>
    </div>
</template>

<script>
import { appSuiteCodeApi, pipelineEnvBind } from '@/spider-api/h5'

export default {
    name: 'PackageParamsEdit',

    props: {
        item: Object,
        iter_id: String,
        br_name: String,
        checked: Boolean
    },
    computed: {},
    data() {
        return {
            app_env_list: []
        }
    },
    watch: {
        checked(val) {
            if (val) {
                this.getEnvInfo()
            }
        }
    },
    mounted() {
        this.getEnvInfo()
    },
    methods: {
        changeSuiteCode(suite_code) {
            pipelineEnvBind({ iter_id: this.iter_id, bind_env_list: { [this.item.app_name]: [suite_code] } })
                .then(res => {
                    this.$Message.success(res.data.msg)
                })
                .catch(err => {
                    console.log('============= err =========')
                    console.log(err)
                    this.$Message.error(err)
                })
        },
        getEnvInfo() {
            appSuiteCodeApi(this.iter_id).then(res => {
                this.app_env_list = res.data.data.app_suite_info[this.item.app_name]
            })
        }
    }
}
</script>

<style scoped></style>
