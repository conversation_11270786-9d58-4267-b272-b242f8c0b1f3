<template>
    <div>
        <Button
            type="primary"
            :loading="button_disabled"
            style="text-align: left; display: inline-block; margin:5px"
            @click="batch_publish"
        >
            <slot>批量发布</slot>
        </Button>
        <!-- @on-ok="publish(publish_info_list)"
            @on-cancel="dist_publish_modal_cancel" -->
        <Modal :styles="{ width: '70%' }" v-model="dist_publish_modal" title="批量发布" ref="publishModal">
            <PackageParamsEdit
                v-for="item in publish_info_list"
                :iter_id="iter_id"
                :br_name="br_name"
                :checked="dist_publish_modal"
                :item="item"
                ref="packageParamsEdit"
            ></PackageParamsEdit>
            <div slot="footer">
                <Button @click="dist_publish_modal_cancel">取消</Button>
                <Button type="primary" @click="publish(publish_info_list, 'dist_publish_modal')">确定</Button>
            </div>
        </Modal>

        <Modal
            :styles="{ width: '70%' }"
            v-model="h5_dist_publish_modal"
            title="批量发布"
            @on-ok="publish(publish_info_list)"
            @on-cancel="dist_publish_modal_cancel"
            ref="publishModal"
        >
            <div style="display: flex; flex-wrap: wrap;">
                <Spin fix v-if="spinShow">
                    <Icon type="ios-loading" size="18"></Icon>
                    <div>Loading</div>
                </Spin>
                <div v-if="fund_dist == true" style="flex: 1; margin-right: 10px;">
                    <DistPackageParamsEdit
                        ref="distPackageFundParamsEdit"
                        :iter_id="iter_id"
                        :app_env_list="fund_app_env_list"
                        :select_data="fund_platform"
                        @set_select_change="get_fund_select_version"
                    ></DistPackageParamsEdit>
                </div>
                <div v-if="piggy_dist == true" style="flex: 1; margin-right: 10px;">
                    <DistPackageParamsEdit
                        ref="distPackagePiggyParamsEdit"
                        :iter_id="iter_id"
                        :app_env_list="piggy_app_env_list"
                        :select_data="piggy_platform"
                        @set_select_change="get_piggy_select_version"
                    ></DistPackageParamsEdit>
                </div>
            </div>
        </Modal>

        <Modal
            :styles="{ width: '70%' }"
            v-model="app_publish_modal"
            title="批量发布"
            @on-ok="app_publish(publish_info_list)"
            @on-cancel="app_publish_modal_cancel"
            ref="appPublishModal"
        >
            <AppPublishParamsEdit
                v-for="item in publish_info_list"
                :iter_id="iter_id"
                :br_name="br_name"
                :tag_name="tag_name"
                :item="item"
                :checked="app_publish_modal"
                ref="appPublishParamsEdit"
            ></AppPublishParamsEdit>
        </Modal>

        <Modal
            :styles="{ width: '70%' }"
            v-model="app_component_publish_modal"
            title="批量发布"
            @on-ok="app_component_publish(publish_info_list)"
            @on-cancel="app_publish_modal_cancel"
            ref="appComponentPublishModal"
        >
        </Modal>
    </div>
</template>

<script>
import store from '@/spider-store'
import {
    h5TestPublishApi,
    testPublishCheckApi,
    externalServiceResult,
    appTestPublishApi,
    appTagTestPublishApi,
    getErrNoticeShowTime,
    pipelineEnvBindGet,
    userActionGet,
    mobile_branch_status,
    mobileMiniPublishApi,
    getTestPublishDistInfo,
    platformSuiteCodeApi
} from '@/spider-api/h5'
import PackageParamsEdit from '@/spider-components/spider-buttons/package-params-edit'
import DistPackageParamsEdit from '@/spider-components/spider-buttons/dist-package-params-edit'
import AppPublishParamsEdit from '@/spider-components/spider-buttons/app-publish-params-edit'
import { getH5AppBindNfAppApi } from '@/spider-api/h5-ci-cd/nf-app'

export default {
    name: 'TestPublish',
    components: {
        PackageParamsEdit,
        AppPublishParamsEdit,
        DistPackageParamsEdit
    },
    props: {
        publish_data: Array,
        action_item: String,
        iter_id: String,
        br_name: String,
        tag_name: String,
        code_type: String,
        package_type: Array
    },
    data() {
        return {
            spinShow: false,
            bind_env_list: {},
            app_publish_modal: false,
            dist_publish_modal: false,
            h5_dist_publish_modal: false,
            app_component_publish_modal: false,
            publish_info_list: [],
            button_name: '批量发布',
            button_disabled: false,
            fund_dist: true,
            piggy_dist: false,
            fund_platform: [],
            piggy_platform: [],
            app_env_list: [],
            fund_app_env_list: [],
            piggy_app_env_list: []
        }
    },
    methods: {
        /**
         * 发布前检查
         */
        get_select_version(sel) {
            console.log('vvvvvvvvvvvvvvvvnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnmmmmmmmmmmmmmmmmmmmmmmmmmmmm')
            console.log(sel)
        },
        get_fund_select_version(fund_select) {
            this.fund_platform = fund_select
        },
        get_piggy_select_version(piggy_select) {
            this.piggy_platform = piggy_select
        },
        publish_status_check(build_info_list) {
            console.log(build_info_list)
            for (let row of build_info_list) {
                if (row.compile_status === 'compile_running' || row.compile_status === 'running') {
                    this.$Notice.error({
                        desc:
                            '以下应用' + row.app_name + '正在进行' + row.compile_status + '，请等待编译结束后再次发布',
                        duration: 100
                    })
                    return false
                }
                if (row.publish_status === 'publish_running' || row.publish_status === 'running') {
                    this.$Notice.error({
                        desc:
                            '以下应用' + row.app_name + '正在进行' + row.publish_status + '，请等待发布结束后再次发布',
                        duration: 100
                    })
                    return false
                }
            }
            return true
        },

        dist_publish_modal_cancel() {
            this.dist_publish_modal = false
        },
        app_publish_modal_cancel() {
            this.app_publish_modal = false
        },
        buttonWait(num) {
            let vm = this
            setTimeout(function() {
                if (num > 0) {
                    num = num - 1
                    vm.button_name = 'Loading ' + num + 's'
                    vm.buttonWait(num)
                } else {
                    vm.button_name = '批量发布'
                    vm.button_disabled = false
                }
            }, 1000)
        },

        batch_publish() {
            console.log('====this.package_type====' + this.package_type)
            platformSuiteCodeApi(this.iter_id).then(res => {
                console.log(res.data.data)
                this.fund_app_env_list = res.data.data.fund_platform_suite
                this.piggy_app_env_list = res.data.data.piggy_platform_suite
            })
            if (this.publish_status_check(this.publish_data) == false) {
                return false
            }
            if (this.package_type == 'ios,android,ios-global,android-global') {
                console.log(1111)
                this.app_batch_publish()
            } else if (this.package_type == 'ios-com,android-com') {
                console.log(222)
                this.app_component_batch_publish()
            } else {
                console.log(333)
                this.h5_batch_publish()
            }
        },
        app_batch_publish() {
            this.publish_info_list = []
            for (let row of this.publish_data) {
                console.log(row)
                let publish_row = {}
                publish_row['iterationID'] = this.iter_id
                publish_row['appBranch'] = this.br_name
                publish_row['appTagName'] = this.tag_name
                publish_row['appCodeType'] = this.code_type
                publish_row['actionItem'] = this.action_item
                publish_row['appEnv'] = 'test'
                publish_row['appName'] = row['app_name']
                publish_row['h5PlatFormCode'] = row['app_name'].split('-')[0] + '-h5-res'
                publish_row['package_type'] = row['package_type']
                publish_row['packageType'] = row['package_type']
                publish_row['appVersion'] = ''
                if (publish_row['package_type'] == 'android') {
                    publish_row['tradeCoopId'] = 'A20131205'
                    publish_row['tradeActionId'] = 'HD0001'
                    publish_row['channelId'] = '145217337'
                    publish_row['umengChannel'] = 'test'
                    publish_row['channel'] = 'ErenEben'
                    publish_row['scan'] = false
                } else if (publish_row['package_type'] == 'android-global') {
                    publish_row['tradeCoopId'] = 'PH2312W02'
                    publish_row['tradeActionId'] = 'HD0001'
                    publish_row['channelId'] = '145236645'
                    publish_row['umengChannel'] = 'test'
                    publish_row['channel'] = 'ErenEben'
                    publish_row['scan'] = false
                }
                console.log('publish_row=========================', publish_row)
                this.publish_info_list.push(publish_row)
            }
            mobile_branch_status(this.publish_info_list).then(res => {
                console.log(res.data.msg)
                if (res.data.msg == '上线中') {
                    this.$Modal.warning({
                        title: '警告',
                        content: '该迭代正在上线中，返回测试阶段会导致无法归档'
                    })
                } else {
                    this.app_publish_modal = true
                }
            })
        },

        app_component_batch_publish() {
            this.publish_info_list = []
            for (let row of this.publish_data) {
                console.log(row)
                let publish_row = {}
                publish_row['iterationID'] = this.iter_id
                publish_row['appBranch'] = this.br_name
                publish_row['appCodeType'] = this.code_type
                publish_row['actionItem'] = this.action_item
                publish_row['appEnv'] = 'test'
                publish_row['appName'] = row['app_name']
                publish_row['package_type'] = row['package_type']
                publish_row['packageType'] = row['package_type']
                this.publish_info_list.push(publish_row)
            }
            this.app_component_publish_modal = true
        },

        h5_batch_publish() {
            this.publish_info_list = []
            pipelineEnvBindGet(this.iter_id).then(res => {
                this.$Spin.show({
                    render: h => {
                        return h('div', [
                            h('Icon', {
                                class: 'demo-spin-icon-load',
                                props: {
                                    type: 'ios-loading',
                                    size: 18
                                }
                            }),
                            h('div', '发布分支计算中请稍等。。。')
                        ])
                    }
                })

                setTimeout(() => {
                    this.$Spin.hide()
                }, 1000)
                console.log('bind_env_list=====' + res.data.data['bind_env_list'])
                this.bind_env_list = res.data.data['bind_env_list']

                let is_exist_dist = false
                let selectTableData = []
                let vm = this
                this.get_user_action(this.action_item).then(data => {
                    if (!data) {
                        console.info('用户行为数据查询失败！！')
                    } else {
                        selectTableData =
                            data.length > 0
                                ? JSON.parse(
                                      data
                                          .replace(/False/g, 'false')
                                          .replace(/True/g, 'true')
                                          .replace(/'/g, '"')
                                  )
                                : data
                    }
                    for (let row of vm.publish_data) {
                        let publish_row = {}
                        publish_row['iteration_id'] = vm.iter_id
                        publish_row['br_name'] = vm.br_name
                        publish_row['action_item'] = vm.action_item
                        publish_row['app_name'] = row['app_name']
                        publish_row['package_type'] = row['package_type']
                        publish_row['h5_env'] = 'test'
                        publish_row['platform_code'] = row['platform_code']
                        publish_row['_checked'] = row['_checked']
                        publish_row['suite_code'] = publish_row['suite_code']
                        if (row['app_name'] in vm.bind_env_list) {
                            publish_row['suite_code'] = vm.bind_env_list[row['app_name']]
                            publish_row['suite_code'] = publish_row['suite_code'].join(',')
                        } else {
                            publish_row['suite_code'] = ''
                        }

                        if (row.package_type == ['dist']) {
                            is_exist_dist = true
                            publish_row['begin_ver'] = vm.br_name
                            publish_row['end_ver'] = vm.br_name
                            publish_row['is_silent'] = '0'
                            let ele = selectTableData.find(n => n.app_name == row.app_name)
                            console.info(ele)
                            if (ele) {
                                publish_row['begin_ver'] = ele.begin_ver
                                publish_row['end_ver'] = ele.end_ver
                                publish_row['is_silent'] = ele.is_silent
                            }
                        }
                        vm.publish_info_list.push(publish_row)
                    }

                    if (vm.publish_info_list.length == 0) {
                        vm.$Notice.error({
                            desc: '至少选择一个应用',
                            duration: getErrNoticeShowTime()
                        })
                        return false
                    }
                    if (is_exist_dist) {
                        if (this.fund_dist == true) {
                            this.$refs.distPackageFundParamsEdit.getDistBeginVersion()
                        } else if (this.piggy_dist == true) {
                            this.$refs.distPackagePiggyParamsEdit.getDistBeginVersion()
                        }
                        this.spinShow = true
                        getTestPublishDistInfo(vm.publish_info_list)
                            .then(res => {
                                this.fund_dist = res.data.data.fund_dist
                                this.piggy_dist = res.data.data.piggy_dist
                                this.fund_platform = res.data.data.fund_platform
                                this.piggy_platform = res.data.data.piggy_platform
                                this.fund_platform.sort(function(a, b) {
                                    return a.app_name.length - b.app_name.length
                                })
                                this.piggy_platform.sort(function(a, b) {
                                    return a.app_name.length - b.app_name.length
                                })
                                this.$Spin.hide()
                                this.spinShow = false
                            })
                            .catch(() => {
                                this.spinShow = false
                            })
                        // this.$Spin.hide()
                        this.h5_dist_publish_modal = true
                        // this.$Spin.hide()
                    } else {
                        this.$Spin.hide()
                        vm.dist_publish_modal = true
                    }
                })
            })
        },
        get_user_action(action_item) {
            return new Promise(function(resolve, reject) {
                userActionGet(action_item)
                    .then(res => {
                        resolve(res.data.data)
                    })
                    .catch(err => {
                        reject(false)
                    })
            })
        },
        /**
         * 发布前检查项目
         */
        checkBeforePublish(req) {
            testPublishCheckApi(req).then(res => {
                if (res.data.status === 'success') {
                    console.log('============res =========')
                    console.log(res.data.data)
                    let sid = res.data.data.sid
                    this.checkPublishResult(sid)
                } else {
                    this.$Notice.error({
                        desc: res.data.msg,
                        duration: getErrNoticeShowTime()
                    })
                }
            })
        },

        /**
         * 发布结果轮训查看
         */
        checkPublishResult(sid) {
            externalServiceResult(sid).then(res => {
                console.log('======== externalServiceResult ===========')
                console.log(res)
                let status = res.data.data.status
                let detail = res.data.data.detail
                if (status == 'success') {
                } else if (status == 'failure') {
                    this.$Notice.error({
                        desc: detail,
                        duration: getErrNoticeShowTime()
                    })
                } else {
                    let vm = this
                    setTimeout(function() {
                        vm.checkPublishResult(sid)
                    }, 2000)
                }
            })
        },

        /**
         * 发布
         */
        publish(publish_info_list, type) {
            if (this.fund_dist == true) {
                this.$refs.distPackageFundParamsEdit.changeData()
            } else if (this.piggy_dist == true) {
                this.$refs.distPackagePiggyParamsEdit.changeData()
            }

            let publish_info_temp_list = this.fund_platform.concat(this.piggy_platform)
            if (publish_info_temp_list.length > 0) {
                publish_info_list = publish_info_temp_list
            } else {
                publish_info_list = this.publish_info_list
            }
            console.log('========= TEST PUBLISH START ============')
            let vm = this
            console.log(publish_info_list)
            console.log(this.publish_info_list)
            let publishApi = h5TestPublishApi
            // param_remote 不同的api
            if (
                this.action_item.indexOf('param_remote_compile') != -1 ||
                this.action_item.indexOf('mini_program_compile') != -1
            ) {
                publishApi = mobileMiniPublishApi
            }
            // 小程序发布需要添加校验
            let checkResult = true
            let fialMsg = ''
            publish_info_list.map(item => {
                console.log('item------', item)
                if (item.package_type == 'mini-program') {
                    if (!item.upload_version) {
                        checkResult = false
                        fialMsg = '存在上传版本未填写'
                    } else if (!item.actionId) {
                        checkResult = false
                        fialMsg = '存在actionId未填写'
                    } else if (!item.upload_desc) {
                        checkResult = false
                        fialMsg = '存在上传描述未填写'
                    }
                }
            })
            if (!checkResult) {
                this.$Message.error(fialMsg)
                return
            }
            this.button_disabled = true
            this.buttonWait(10)
            if (type == 'dist_publish_modal') {
                this.dist_publish_modal = false
            }
            publishApi(publish_info_list).then(res => {
                console.log(res)
                if (res.data.status == 'success') {
                    return true
                    // 接口返回200但有可能是失败的
                } else {
                    vm.$Notice.error({
                        desc: res.data.msg,
                        duration: 100
                    })
                    return false
                }
            })
        },

        /**
         * app测试发布
         */
        app_publish(publish_info_list) {
            this.button_disabled = true
            this.buttonWait(10)
            console.log('========= TEST PUBLISH START ============')
            let vm = this
            console.log(publish_info_list)
            console.log(this.publish_info_list)
            let testPublish = appTestPublishApi

            if (this.code_type == 'tag') {
                console.log('this.tag_name====' + this.tag_name)
                testPublish = appTagTestPublishApi
            }
            testPublish(publish_info_list).then(res => {
                console.log(res)
                if (res.status == '200') {
                    return true
                } else {
                    // 失败
                    vm.$Notice.error({
                        desc: res.data.msg,
                        duration: getErrNoticeShowTime()
                    })
                    return false
                }
            })
        },

        /**
         * app组件发布
         */
        app_component_publish(publish_info_list) {
            this.button_disabled = true
            this.buttonWait(10)
            console.log('========= TEST PUBLISH START ============')
            let vm = this
            console.log(publish_info_list)
            console.log(this.publish_info_list)
            let testPublish = appTestPublishApi

            if (this.code_type == 'tag') {
                console.log('this.tag_name====' + this.tag_name)
                testPublish = appTagTestPublishApi
            }
            testPublish(publish_info_list).then(res => {
                console.log(res)
                if (res.status == '200') {
                    return true
                } else {
                    // 失败
                    vm.$Notice.error({
                        desc: res.data.msg,
                        duration: getErrNoticeShowTime()
                    })
                    return false
                }
            })
        }
    }
}
</script>

<style scoped></style>
