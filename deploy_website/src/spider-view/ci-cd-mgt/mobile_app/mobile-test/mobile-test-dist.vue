<template>
    <Card>
        <Row>
            <Col v-if="code_type != 'tag'" span="2">
                <Compile
                    :table_data="same_repo_date_info"
                    :br_name="br_name"
                    :action_item="compile_action_item"
                    :iter_id="iter_id"
                ></Compile>
            </Col>
            <Col v-show="!is_nf_app" span="2">
                <TestPublish
                    :publish_data="date_info"
                    :action_item="publish_action_item"
                    :iter_id="iter_id"
                    :br_name="br_name"
                    :package_type="app_type_list"
                >
                </TestPublish>
            </Col>
        </Row>
        <Row>
            <CITable
                :columns="columns"
                :is_refresh="is_refresh"
                @set_table_info="get_table_info"
                ref="ciTable"
            ></CITable>
        </Row>
        <Row>
            <DownloadResource ref="downloadResource"> </DownloadResource>
        </Row>
    </Card>
</template>

<script>
import store from '@/spider-store'
import {
    getInfoNoticeShowTime,
    getErrNoticeShowTime,
    h5PipelineStop,
    h5PipelineIsRunning,
    userActionGet
} from '@/spider-api/h5'

import { formatDate } from '@/spider-api/h5-common'
import TestEnvBind from '@/spider-components/spider-buttons/test-env-bind'
import Compile from '@/spider-components/spider-buttons/compile'
import CITable from '@/spider-components/spider-table/mobile-table'
import TestPublish from '@/spider-components/spider-buttons/test-publish'
import DownloadResource from '@/spider-components/spider-buttons/download-resource'
import { getH5AppBindNfAppApi } from '@/spider-api/h5-ci-cd/nf-app'

import { refreshExecStatusApi, stopTask } from '../mobile-utils/utils.js'

export default {
    name: 'MobileTestDist',
    components: {
        Compile,
        CITable,
        TestPublish,
        DownloadResource
    },
    props: {
        is_refresh: Boolean
        // app_suite_info: Object,
        //这里是table中的数据
    },
    data() {
        return {
            br_name: '',
            iter_id: '',
            compile_action_item: '',
            publish_action_item: '',
            date_info: [],
            same_repo_date_info: [],
            app_list: [],
            app_env_list: [],
            bind_env_list: {},
            code_type: '',
            is_nf_app: false,
            app_type_list: ['dist'],
            columns: [
                {
                    type: 'selection',
                    width: 50
                },
                { title: '应用', key: 'app_name' },
                { title: '平台类型', key: 'platform_name' },
                { title: '起始版本', key: 'begin_ver' /*sortable: true*/ },
                { title: '结束版本', key: 'end_ver' /*sortable: true */ },
                {
                    title: '打包环境',
                    key: 'suite_code',
                    render: (h, params) => {
                        let value = params.row.suite_code
                        if (params.row.suite_code == null || params.row.suite_code == '') {
                            return h('div', '无')
                        } else {
                            let str = ''
                            if (value instanceof Array) {
                                console.log('i am Array')
                                console.log('=========== value =========')
                                console.log(value)
                                for (let i of value) {
                                    str += i + ','
                                }
                            }
                            if (typeof value === 'string') {
                                console.log('i am String')
                                str += value
                            }
                            //有多选却不输入值的
                            let returnArray = []
                            let returnStr = ''
                            returnArray = str.split(',')
                            for (let i of returnArray) {
                                if (i != '' && i != null && i != undefined) {
                                    returnStr += i + ','
                                }
                            }
                            if (returnStr != '' && returnStr.charAt(returnStr.length - 1) == ',') {
                                returnStr = returnStr.substr(0, returnStr.length - 1)
                            }
                            return h('div', returnStr)
                        }
                    }
                },
                {
                    title: '编译状态',
                    key: 'compile_status',
                    render: (h, params) => {
                        let stat_dict = {
                            running: '执行中',
                            success: '执行成功',
                            failure: '执行失败',
                            compile_running: '编译中',
                            compile_success: '编译成功',
                            compile_failure: '编译失败',
                            aborted: '已终止'
                        }
                        let status = params.row.compile_status
                        if (stat_dict.hasOwnProperty(status)) {
                            var status_display = stat_dict[status]
                        } else {
                            var status_display = status
                        }
                        let action_type = ''
                        if (status == 'compile_running') {
                            action_type = 'compile'
                        }
                        if (action_type) {
                            return h('div', [
                                h('a', [
                                    h(
                                        'Poptip',
                                        {
                                            props: {
                                                transfer: true,
                                                trigger: 'click',
                                                title: '上次耗时: ' + this.last_elapsed,
                                                content: '当前耗时：' + this.current_elapsed,
                                                size: 'small'
                                            },
                                            on: {
                                                'on-popper-show': () => {
                                                    h5ElapseStatsApiGet(
                                                        params.row.app_name,
                                                        action_type,
                                                        store.state.h5_branch_version
                                                    ).then(res => {
                                                        if (res.data.status === 'success') {
                                                            this.last_elapsed = res.data.data['last_elapsed']
                                                            this.current_elapsed = res.data.data['current_elapsed']
                                                        }
                                                    })
                                                },
                                                'on-popper-hide': () => {
                                                    this.last_elapsed = 0
                                                    this.current_elapsed = 0
                                                }
                                            }
                                        },
                                        status_display
                                    )
                                ])
                            ])
                        } else if (status == 'compile_success') {
                            return h('p', { style: { color: 'green' } }, status_display)
                        } else if (status == 'compile_failure') {
                            return h(
                                'Tooltip',
                                {
                                    props: {
                                        placement: 'top',
                                        content: params.row.compile_message,
                                        'max-width': 500,
                                        transfer: true
                                    },
                                    style: { color: 'red' }
                                },
                                status_display
                            )
                        } else {
                            return h('p', { style: { color: '#515a6e' } }, status_display)
                        }
                    }
                },
                {
                    title: '发布状态',
                    key: 'test_publish_status',
                    render: (h, params) => {
                        let stat_dict = {
                            running: '执行中',
                            success: '执行成功',
                            failure: '执行失败',
                            publish_running: '发布中',
                            publish_success: '发布成功',
                            publish_failure: '发布失败',
                            aborted: '已终止'
                        }
                        let status = params.row.test_publish_status
                        if (stat_dict.hasOwnProperty(status)) {
                            var status_display = stat_dict[status]
                        } else {
                            var status_display = status
                        }
                        let action_type = ''
                        if (status == 'publish_running') {
                            action_type = 'remote'
                        }
                        if (action_type) {
                            return h('div', [
                                h('a', [
                                    h(
                                        'Poptip',
                                        {
                                            props: {
                                                transfer: true,
                                                trigger: 'click',
                                                title: '上次耗时: ' + this.last_elapsed,
                                                content: '当前耗时：' + this.current_elapsed,
                                                size: 'small'
                                            },
                                            on: {
                                                'on-popper-show': () => {
                                                    h5ElapseStatsApiGet(
                                                        params.row.app_name,
                                                        action_type,
                                                        store.state.h5_branch_version
                                                    ).then(res => {
                                                        if (res.data.status === 'success') {
                                                            this.last_elapsed = res.data.data['last_elapsed']
                                                            this.current_elapsed = res.data.data['current_elapsed']
                                                        }
                                                    })
                                                },
                                                'on-popper-hide': () => {
                                                    this.last_elapsed = 0
                                                    this.current_elapsed = 0
                                                }
                                            }
                                        },
                                        status_display
                                    )
                                ])
                            ])
                        } else if (status == 'publish_success') {
                            return h('p', { style: { color: 'green' } }, status_display)
                        } else if (status == 'publish_failure') {
                            return h(
                                'Tooltip',
                                {
                                    props: {
                                        placement: 'top',
                                        content: params.row.publish_message,
                                        'max-width': 500,
                                        transfer: true
                                    },
                                    style: { color: 'red' }
                                },
                                status_display
                            )
                        } else {
                            return h('p', { style: { color: '#515a6e' } }, status_display)
                        }
                    }
                },
                { title: '操作人', key: 'op_user' },
                {
                    title: '发布开始时间',
                    key: 'op_time',
                    render: (h, params) => {
                        let value = params.row.op_time
                        if (value == '' || value == null) {
                        } else {
                            value = formatDate(new Date(params.row.op_time), 'yyyy-MM-dd hh:mm:ss')
                        }
                        return h('div', value)
                    }
                },
                {
                    title: '最后一次编译结束时间',
                    key: 'end_time',
                    //sortable: true,
                    render: (h, params) => {
                        let value = params.row.end_time
                        if (value == '' || value == null) {
                        } else {
                            value = formatDate(new Date(params.row.end_time), 'yyyy-MM-dd hh:mm:ss')
                        }
                        return h('div', value)
                    }
                },
                {
                    title: '操作',
                    key: 'action',
                    width: 200,
                    align: 'center',
                    render: (h, params) => {
                        return h('div', [
                            h(
                                'Button',
                                {
                                    props: {
                                        type: 'default',
                                        size: 'small'
                                    },
                                    style: {
                                        marginRight: '5px'
                                    },
                                    on: {
                                        click: () => {
                                            let job_url = params.row.compile_url
                                            if (job_url != null) {
                                                window.open(params.row.compile_url)
                                            } else {
                                                this.$Notice.info({
                                                    title: '编译详情',
                                                    message: params.row.compile_message,
                                                    duration: getInfoNoticeShowTime()
                                                })
                                            }
                                        }
                                    }
                                },
                                '编译页'
                            ),
                            h(
                                'Button',
                                {
                                    props: {
                                        type: 'default',
                                        size: 'small'
                                    },
                                    style: {
                                        marginRight: '5px'
                                    },
                                    on: {
                                        click: () => {
                                            let job_url = params.row.publish_url
                                            if (job_url != null) {
                                                window.open(params.row.publish_url)
                                            } else {
                                                this.$Notice.info({
                                                    title: '发布详情',
                                                    message: params.row.publish_message,
                                                    duration: getInfoNoticeShowTime()
                                                })
                                            }
                                        }
                                    }
                                },
                                '发布页'
                            ),
                            h(
                                'Button',
                                {
                                    props: {
                                        type: 'default',
                                        size: 'small'
                                    },
                                    style: {
                                        marginRight: '5px',
                                        display: params.row.app_name == 'nf-fund' || params.row.app_name == 'nf-piggy'
                                    },
                                    on: {
                                        click: () => {
                                            if (params.row.suite_code == undefined) {
                                                this.$Notice.info({
                                                    desc: '未绑定测试环境',
                                                    duration: getInfoNoticeShowTime()
                                                })
                                                return
                                            }
                                            this.$refs.downloadResource.showDownload(
                                                this.iter_id,
                                                params.row.app_name,
                                                params.row.suite_code
                                            )
                                        }
                                    }
                                },
                                '下载'
                            ),
                            h(
                                'Button',
                                {
                                    props: { type: 'warning', size: 'small' },
                                    style: {
                                        marginRight: '5px'
                                    }
                                },
                                [
                                    h(
                                        'Poptip',
                                        {
                                            props: {
                                                confirm: true,
                                                transfer: true,
                                                title: '你确定要强制刷新吗?',
                                                type: 'error',
                                                size: 'small'
                                            },
                                            on: {
                                                'on-ok': () => {
                                                    refreshExecStatusApi({
                                                        app_name: params.row.app_name,
                                                        iter_id: this.iter_id,
                                                        refresh_action_item_list: [
                                                            this.compile_action_item,
                                                            this.publish_action_item
                                                        ]
                                                    })
                                                },
                                                'on-cancel': function() {}
                                            }
                                        },
                                        '刷新'
                                    )
                                ]
                            ),
                            h(
                                'Button',
                                {
                                    props: { type: 'error', size: 'small' },
                                    style: {
                                        marginRight: '5px'
                                    }
                                },
                                [
                                    h(
                                        'Poptip',
                                        {
                                            props: {
                                                confirm: true,
                                                transfer: true,
                                                title: '你确定要强制中止吗?',
                                                type: 'error',
                                                size: 'small'
                                            },
                                            on: {
                                                'on-ok': () => {
                                                    stopTask({
                                                        app_name: params.row.app_name,
                                                        iter_id: this.iter_id,
                                                        action_item_list: [
                                                            this.compile_action_item,
                                                            this.publish_action_item
                                                        ]
                                                    })
                                                },
                                                'on-cancel': function() {}
                                            }
                                        },
                                        '终止'
                                    )
                                ]
                            )
                        ])
                    }
                }
            ]
        }
    },

    computed: {},
    methods: {
        init() {
            console.log('============ dist init ============')
            this.br_name = store.state.h5_branch_version
            this.iter_id = store.state.iterationID
            this.compile_action_item = this.iter_id + '_' + 'test_dist_compile'
            this.publish_action_item = this.iter_id + '_' + 'test_dist_publish'
            this.code_type = store.state.code_type
            let vm = this
            let selectTableData = []
            this.get_user_action(vm.compile_action_item).then(function(data) {
                if (!data) {
                    console.info('用户行为数据查询失败！！')
                } else {
                    selectTableData =
                        data.length > 0
                            ? JSON.parse(
                                  data
                                      .replace(/False/g, 'false')
                                      .replace(/True/g, 'true')
                                      .replace(/'/g, '"')
                                      .replace(/None/g, null)
                              )
                            : data
                }
                vm.$refs.ciTable.get_ci_info(
                    vm.iter_id,
                    [vm.compile_action_item, vm.publish_action_item],
                    vm.app_type_list,
                    selectTableData
                )
            })
        },
        get_user_action(action_item) {
            return new Promise(function(resolve, reject) {
                userActionGet(action_item)
                    .then(res => {
                        resolve(res.data.data)
                    })
                    .catch(err => {
                        reject(false)
                    })
            })
        },
        get_table_info(date_info) {
            //alert(date_info)
            let selectRowList = []
            let same_platform_date_info = []
            this.date_info = date_info
            console.log('============ dist get_table_info ============')
            console.log(date_info)
            console.log(store.state.ci_info)
            store.state.ci_info.forEach(it => {
                date_info.forEach(item => {
                    if (it['repo_path'] === item['repo_path']) {
                        if (selectRowList.includes(it)) {
                            console.log('去重')
                        } else {
                            it['check'] = true
                            selectRowList.push(it)
                        }
                    }
                    if (it['platform_code'] === item['platform_code'] && it['package_type'] == 'dist') {
                        if (same_platform_date_info.includes(it)) {
                            console.log('去重')
                        } else {
                            console.log(
                                '=====================================same_platform_date_info======================================'
                            )
                            same_platform_date_info.push(it)
                            this.date_info = same_platform_date_info
                            console.log(this.date_info)
                        }
                    }
                })
            })
            console.log('vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv')
            console.log(selectRowList)
            this.same_repo_date_info = selectRowList
        },
        changeTableEnvData(bind_env_list) {
            // 修改table中的环境套数据
            for (let i of this.date_info) {
                for (let app_name in bind_env_list) {
                    if (i['app_name'] === app_name) {
                        this.$set(i, 'suite_code', bind_env_list[app_name])
                    }
                }
            }
            console.log(this.date_info)
        }
    }
}
</script>

<style scoped></style>
