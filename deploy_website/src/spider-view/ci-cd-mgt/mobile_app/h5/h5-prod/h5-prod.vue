<template>
    <div>
        <Card>
            <Row>
                <i-col style="margin: 10px;text-align: right" span="1">
                    <span style="text-align: right; display: inline-block;">组：</span>
                </i-col>
                <i-col style="margin: 10px" span="3">
                    <span style="text-align: left; display: inline-block;">{{ group }}</span>
                </i-col>
                <i-col style="margin: 10px;text-align: right" span="2">
                    <span style="text-align: right; display: inline-block;">分支版本：</span>
                </i-col>
                <i-col style="margin: 10px" span="3">
                    <span style="text-align: left; display: inline-block;">{{ branch_version }}</span>
                </i-col>
                <i-col v-show="show_tag" style="margin: 10px;text-align: right" span="2">
                    <span style="text-align: right; display: inline-block;">TAG版本：</span>
                </i-col>
                <i-col v-show="show_tag" style="margin: 10px" span="3">
                    <span style="text-align: left; display: inline-block;">{{ tag_name }}</span>
                </i-col>
            </Row>
            <Row>
                <Collapse v-model="collapse_value">
                    <Panel name="1" v-if="showPC">
                        PC发布
                        <p slot="content">
                            <H5ProdPc :static_props="static_props" :queryState="queryState" :vue_this="this"></H5ProdPc>
                        </p>
                    </Panel>
                    <Panel name="2" v-if="showRemote">
                        移动端远端发布
                        <p slot="content">
                            <H5ProdRemote
                                v-bind:remote_props="remote_props"
                                :queryState="queryState"
                                :vue_this="this"
                            ></H5ProdRemote>
                        </p>
                    </Panel>
                    <Panel name="3" v-if="showDist">
                        H5资源包发布
                        <p slot="content">
                            <H5ProdZip :dist_props="dist_props" :queryState="queryState" :vue_this="this"></H5ProdZip>
                        </p>
                    </Panel>
                    <Panel name="4" v-if="showMobileApp">
                        App资源包发布
                        <p slot="content">
                            <MobileAppProd
                                :mobile_app_props="mobile_app_props"
                                :queryState="queryState"
                                :vue_this="this"
                            ></MobileAppProd>
                        </p>
                    </Panel>
                    <Panel name="5" v-if="showSsrRemote">
                        SSR-Remote发布
                        <p slot="content">
                            <SsrRemoteProd
                                :ssr_remote_props="ssr_remote_props"
                                :queryState="queryState"
                                :vue_this="this"
                            ></SsrRemoteProd>
                        </p>
                    </Panel>
                </Collapse>
            </Row>
            <Row>
                <p style="color:red ;font-size:17px;font-weight:900">
                    代码更新就需要重新申请！！！
                </p>
            </Row>
        </Card>
    </div>
</template>

<script>
import store from '@/spider-store'
import H5ProdPc from './h5-prod-pc.vue'
import H5ProdRemote from './h5-prod-remote.vue'
import H5ProdZip from './h5-prod-zip.vue'
import MobileAppProd from './mobile-app-prod.vue'
import SsrRemoteProd from './ssr-prod-remote.vue'
import { h5ProPublishStateApi, h5ProdPublishInfo } from '@/spider-api/h5'

export default {
    name: 'h5-prod',
    components: {
        H5ProdPc,
        H5ProdRemote,
        H5ProdZip,
        MobileAppProd,
        SsrRemoteProd
    },
    data() {
        return {
            collapse_value: '', //(store.state.h5_app_names)?'fund':1,
            group: '',
            branch_version: '',
            tag_name: '',
            show_tag: false,
            static_props: [],
            remote_props: [],
            dist_props: [],
            mobile_app_props: [],
            ssr_remote_props: [],
            showPC: false,
            showRemote: false,
            showweapp: true,
            showDist: false,
            showMobileApp: false,
            showSsrRemote: false
        }
    },
    mounted() {
        // this.init()
    },
    methods: {
        //根据分支中选择不同的应用，控制不同Panel展开
        init() {
            console.log('======prod init=========')
            // this.remote_props = store.state.app_array
            ////从h5-branch.vue中带来 组
            this.group = store.state.h5_group
            //从h5-branch.vue中带来 分支版本
            this.branch_version = store.state.h5_branch_version
            this.iteration_id = store.state.iterationID
            this.tag_name = store.state.tag_name
            this.show_tag = store.state.show_tag
            //重新初始化，防止越加越多
            this.static_props = new Array()
            this.remote_props = new Array()
            this.dist_props = new Array()
            this.mobile_app_props = new Array()
            this.ssr_remote_props = new Array()

            //查看发布信息
            h5ProdPublishInfo(this.iteration_id).then(res => {
                //初始化折叠全部展示
                let return_array = []
                this.showPC = false
                this.showRemote = false
                this.showDist = false
                this.showMobileApp = false
                this.showSsrRemote = false
                for (let i in res.data.data.prod_publish_info) {
                    let publish_info_data = res.data.data.prod_publish_info[i]
                    publish_info_data['apply_status'] = publish_info_data['status']
                    publish_info_data['apply_status_display'] = publish_info_data['status_display']
                    publish_info_data['apply_message'] = publish_info_data['message']
                    if (publish_info_data.package_type === 'static') {
                        this.showPC = true
                        return_array.push('1')
                        this.static_props.push(publish_info_data)
                    } else if (
                        publish_info_data.package_type === 'remote' ||
                        publish_info_data.package_type === 'param-remote' ||
                        publish_info_data.package_type === 'mini-program'
                    ) {
                        this.showRemote = true
                        return_array.push('2')
                        this.remote_props.push(publish_info_data)
                    } else if (publish_info_data.package_type === 'dist') {
                        return_array.push('3')
                        this.showDist = true
                        this.dist_props.push(publish_info_data)
                    } else if (
                        publish_info_data.package_type === 'ios' ||
                        publish_info_data.package_type === 'android' ||
                        publish_info_data.package_type === 'android-global' ||
                        publish_info_data.package_type === 'ios-global'
                    ) {
                        this.showMobileApp = true
                        return_array.push('4')
                        this.mobile_app_props.push(publish_info_data)
                    } else if (publish_info_data.package_type === 'ssr-remote') {
                        return_array.push('5')
                        this.showSsrRemote = true
                        this.ssr_remote_props.push(publish_info_data)
                    }

                    //处理可发网络信息
                    this.collapse_value = return_array
                }
                this.loopH5ProdPublishInfo()
                this.queryState(this)
            })
        },
        loopH5ProdPublishInfo() {
            let waitTimeQueryAgain = false

            if (!waitTimeQueryAgain) {
                //判断是否需要轮训查询结果
                for (let i in this.static_props) {
                    if (
                        this.static_props[i]['apply_status'] != null &&
                        this.static_props[i]['apply_status'].indexOf('running') >= 0
                    ) {
                        waitTimeQueryAgain = true
                        break
                    }
                }
            }
            if (!waitTimeQueryAgain) {
                //判断是否需要轮训查询结果
                for (let i in this.remote_props) {
                    if (
                        this.remote_props[i]['apply_status'] != null &&
                        this.remote_props[i]['apply_status'].indexOf('running') >= 0
                    ) {
                        waitTimeQueryAgain = true
                        break
                    }
                }
            }
            if (!waitTimeQueryAgain) {
                //判断是否需要轮训查询结果
                for (let i in this.dist_props) {
                    if (
                        this.dist_props[i]['apply_status'] != null &&
                        this.dist_props[i]['apply_status'].indexOf('running') >= 0
                    ) {
                        waitTimeQueryAgain = true
                        break
                    }
                }
            }
            if (!waitTimeQueryAgain) {
                //判断是否需要轮训查询结果
                for (let i in this.mobile_app_props) {
                    if (
                        this.mobile_app_props[i]['status'] != null &&
                        this.mobile_app_props[i]['status'].indexOf('running') >= 0
                    ) {
                        waitTimeQueryAgain = true
                        break
                    }
                }
            }
            if (!waitTimeQueryAgain) {
                //判断是否需要轮训查询结果
                for (let i in this.ssr_remote_props) {
                    if (
                        this.ssr_remote_props[i]['apply_status'] != null &&
                        this.ssr_remote_props[i]['apply_status'].indexOf('running') >= 0
                    ) {
                        waitTimeQueryAgain = true
                        break
                    }
                }
            }
            if (waitTimeQueryAgain) {
                console.log('this is wait running')
                let _this = this
                setTimeout(function() {
                    //查看发布信息
                    h5ProdPublishInfo(_this.iteration_id).then(res => {
                        let operator_info = {}
                        for (let i of res.data.data.prod_publish_info) {
                            let app_name = i['app_name']
                            let status = i['status']
                            let status_display = i['status_display']
                            let message = i['message']

                            // 多机器执行
                            if (app_name in operator_info) {
                                let last_status = operator_info[app_name]['status']
                                let last_status_display = operator_info[app_name]['status_display']
                                let last_message = operator_info[app_name]['message']
                                operator_info[app_name]['apply_status'] =
                                    last_status.indexOf('success') >= 0 ? status : last_status
                                operator_info[app_name]['apply_status_display'] =
                                    last_status.indexOf('success') >= 0 ? status_display : last_status_display
                                operator_info[app_name]['apply_message'] =
                                    last_status.indexOf('success') >= 0 ? message : last_message
                            } else {
                                operator_info[app_name] = {}
                                operator_info[app_name]['apply_status'] = i['status']
                                operator_info[app_name]['apply_status_display'] = i['status_display']
                                operator_info[app_name]['apply_message'] = i['message']
                            }
                        }
                        console.log('apply_operator_info')
                        console.log(operator_info)

                        if (_this.static_props.length > 0) {
                            for (let i in _this.static_props) {
                                let app_name = _this.static_props[i]['app_name']
                                let op_log = operator_info[app_name]

                                for (let key in op_log) {
                                    _this.$set(_this.static_props[i], key, op_log[key])
                                }
                            }
                        }
                        if (_this.remote_props.length > 0) {
                            for (let i in _this.remote_props) {
                                let app_name = _this.remote_props[i]['app_name']
                                let op_log = operator_info[app_name]

                                for (let key in op_log) {
                                    _this.$set(_this.remote_props[i], key, op_log[key])
                                }
                            }
                        }
                        if (_this.dist_props.length > 0) {
                            for (let i in _this.dist_props) {
                                let app_name = _this.dist_props[i]['app_name']
                                let op_log = operator_info[app_name]

                                for (let key in op_log) {
                                    _this.$set(_this.dist_props[i], key, op_log[key])
                                }
                            }
                        }
                        if (_this.ssr_remote_props.length > 0) {
                            for (let i in _this.ssr_remote_props) {
                                let app_name = _this.ssr_remote_props[i]['app_name']
                                let op_log = operator_info[app_name]

                                for (let key in op_log) {
                                    _this.$set(_this.ssr_remote_props[i], key, op_log[key])
                                }
                            }
                        }
                        _this.loopH5ProdPublishInfo()
                    })
                }, 10000)
            }
        },
        queryState(h5_this) {
            //接着查询状态
            h5ProPublishStateApi(h5_this.iteration_id).then(res => {
                console.log('============ res =========')
                console.log(res.data.data.operate_info)
                console.log(res.data.data.operate_info != null)

                if (res.data.data.operate_info != null && res.data.data.operate_info.length > 0) {
                    let operator_info = {}
                    for (let i of res.data.data.operate_info) {
                        let app_name = i['app_name']
                        let status = i['status']
                        let status_display = i['status_display']
                        let message = i['message']

                        // 多机器执行
                        if (app_name in operator_info) {
                            let last_status = operator_info[app_name]['status']
                            let last_status_display = operator_info[app_name]['status_display']
                            let last_message = operator_info[app_name]['message']
                            operator_info[app_name]['status'] =
                                last_status.indexOf('success') >= 0 ? status : last_status
                            operator_info[app_name]['status_display'] =
                                last_status.indexOf('success') >= 0 ? status_display : last_status_display
                            operator_info[app_name]['message'] =
                                last_status.indexOf('success') >= 0 ? message : last_message
                        } else {
                            operator_info[app_name] = {}
                            operator_info[app_name]['status'] = i['status']
                            operator_info[app_name]['status_display'] = i['status_display']
                            operator_info[app_name]['message'] = i['message']
                            operator_info[app_name]['operate_time'] = i['operate_time']
                        }
                    }
                    console.log('operator_info')
                    console.log(operator_info)

                    if (h5_this.static_props.length > 0) {
                        for (let i in h5_this.static_props) {
                            let app_name = h5_this.static_props[i]['app_name']
                            let op_log = operator_info[app_name]

                            for (let key in op_log) {
                                h5_this.$set(h5_this.static_props[i], key, op_log[key])
                            }
                            if (op_log == null) {
                                h5_this.$set(h5_this.static_props[i], 'status', '')
                                h5_this.$set(h5_this.static_props[i], 'status_display', '')
                            }
                        }
                    }
                    if (h5_this.remote_props.length > 0) {
                        for (let i in h5_this.remote_props) {
                            let app_name = h5_this.remote_props[i]['app_name']
                            let op_log = operator_info[app_name]

                            for (let key in op_log) {
                                h5_this.$set(h5_this.remote_props[i], key, op_log[key])
                            }
                            if (op_log == null) {
                                h5_this.$set(h5_this.remote_props[i], 'status', '')
                                h5_this.$set(h5_this.remote_props[i], 'status_display', '')
                            }
                        }
                    }
                    if (h5_this.dist_props.length > 0) {
                        for (let i in h5_this.dist_props) {
                            let app_name = h5_this.dist_props[i]['app_name']
                            let op_log = operator_info[app_name]

                            for (let key in op_log) {
                                h5_this.$set(h5_this.dist_props[i], key, op_log[key])
                            }
                            if (op_log == null) {
                                h5_this.$set(h5_this.dist_props[i], 'status', '')
                                h5_this.$set(h5_this.dist_props[i], 'status_display', '')
                            }
                        }
                    }
                    if (h5_this.mobile_app_props.length > 0) {
                        for (let i in h5_this.mobile_app_props) {
                            let app_name = h5_this.mobile_app_props[i]['app_name']
                            let op_log = operator_info[app_name]

                            for (let key in op_log) {
                                h5_this.$set(h5_this.mobile_app_props[i], key, op_log[key])
                            }

                            if (op_log == null) {
                                h5_this.$set(h5_this.mobile_app_props[i], 'status', '')
                                h5_this.$set(h5_this.mobile_app_props[i], 'status_display', '')
                            }
                        }
                    }
                    if (h5_this.ssr_remote_props.length > 0) {
                        for (let i in h5_this.ssr_remote_props) {
                            let app_name = h5_this.ssr_remote_props[i]['app_name']
                            let op_log = operator_info[app_name]

                            for (let key in op_log) {
                                h5_this.$set(h5_this.ssr_remote_props[i], key, op_log[key])
                            }

                            if (op_log == null) {
                                h5_this.$set(h5_this.ssr_remote_props[i], 'status', '')
                                h5_this.$set(h5_this.ssr_remote_props[i], 'status_display', '')
                            }
                        }
                    }
                } else {
                    console.log('---- reset status ')
                    if (h5_this.static_props.length > 0) {
                        for (let i in h5_this.static_props) {
                            h5_this.$set(h5_this.static_props[i], 'status', '')
                            h5_this.$set(h5_this.static_props[i], 'status_display', '')
                        }
                    }
                    if (h5_this.remote_props.length > 0) {
                        for (let i in h5_this.remote_props) {
                            h5_this.$set(h5_this.remote_props[i], 'status', '')
                            h5_this.$set(h5_this.remote_props[i], 'status_display', '')
                        }
                    }
                    if (h5_this.dist_props.length > 0) {
                        for (let i in h5_this.dist_props) {
                            h5_this.$set(h5_this.dist_props[i], 'status', '')
                            h5_this.$set(h5_this.dist_props[i], 'status_display', '')
                        }
                    }
                    if (h5_this.mobile_app_props.length > 0) {
                        for (let i in h5_this.mobile_app_props) {
                            h5_this.$set(h5_this.mobile_app_props[i], 'status', '')
                            h5_this.$set(h5_this.mobile_app_props[i], 'status_display', '')
                        }
                    }
                    if (h5_this.ssr_remote_props.length > 0) {
                        for (let i in h5_this.ssr_remote_props) {
                            h5_this.$set(h5_this.ssr_remote_props[i], 'status', '')
                            h5_this.$set(h5_this.ssr_remote_props[i], 'status_display', '')
                        }
                    }
                }

                let waitTimeQueryAgain = false

                if (!waitTimeQueryAgain) {
                    //判断是否需要轮训查询结果
                    for (let i in h5_this.static_props) {
                        if (
                            h5_this.static_props[i]['status'] != null &&
                            h5_this.static_props[i]['status'].indexOf('running') >= 0
                        ) {
                            waitTimeQueryAgain = true
                            break
                        }
                    }
                }
                if (!waitTimeQueryAgain) {
                    //判断是否需要轮训查询结果
                    for (let i in h5_this.remote_props) {
                        if (
                            h5_this.remote_props[i]['status'] != null &&
                            h5_this.remote_props[i]['status'].indexOf('running') >= 0
                        ) {
                            waitTimeQueryAgain = true
                            break
                        }
                    }
                }
                if (!waitTimeQueryAgain) {
                    //判断是否需要轮训查询结果
                    for (let i in h5_this.dist_props) {
                        if (
                            h5_this.dist_props[i]['status'] != null &&
                            h5_this.dist_props[i]['status'].indexOf('running') >= 0
                        ) {
                            waitTimeQueryAgain = true
                            break
                        }
                    }
                }
                if (!waitTimeQueryAgain) {
                    //判断是否需要轮训查询结果
                    for (let i in h5_this.mobile_app_props) {
                        if (
                            h5_this.mobile_app_props[i]['status'] != null &&
                            h5_this.mobile_app_props[i]['status'].indexOf('running') >= 0
                        ) {
                            waitTimeQueryAgain = true
                            break
                        }
                    }
                }
                if (!waitTimeQueryAgain) {
                    //判断是否需要轮训查询结果
                    for (let i in h5_this.ssr_remote_props) {
                        if (
                            h5_this.ssr_remote_props[i]['status'] != null &&
                            h5_this.ssr_remote_props[i]['status'].indexOf('running') >= 0
                        ) {
                            waitTimeQueryAgain = true
                            break
                        }
                    }
                }

                if (waitTimeQueryAgain) {
                    console.log('h5_this is wait running')
                    setTimeout(function() {
                        h5_this.queryState(h5_this)
                    }, 10000)
                }
            })
        }
    }
}
</script>

<style scoped></style>
