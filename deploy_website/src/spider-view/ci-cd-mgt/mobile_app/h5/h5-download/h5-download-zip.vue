<template>
    <div>
        <Row>
            <tables height="300" stripe v-model="download_file_props" :columns="columns"> </tables>
        </Row>
    </div>
</template>

<script>
import Tables from '@/components/tables'

export default {
    name: 'download-file',
    components: {
        Tables
    },
    props: {
        //这里是table中的数据
        download_file_props: {
            type: Array
        }
    },
    data() {
        return {
            remote_props: [],

            columns: [
                { title: '名称', key: 'file_name' },
                { title: '版本', key: 'version' },
                {
                    title: '文件大小',
                    key: 'file_size',
                    render: (h, params) => {
                        let kbSize = 1024
                        let mbSize = 1024 * 1024
                        if (params.row.file_size < kbSize) {
                            return h('div', params.row.file_size + 'Byte')
                        } else if (params.row.file_size >= kbSize && params.row.file_size < mbSize) {
                            return h('div', (params.row.file_size / kbSize).toFixed(2) + 'KB')
                        } else {
                            return h('div', (params.row.file_size / mbSize).toFixed(2) + 'MB')
                        }
                    }
                },
                { title: '时间', key: 'datetime' },
                {
                    title: '操作',
                    key: 'action',
                    width: 200,
                    align: 'center',
                    render: (h, params) => {
                        let url = params.row.download_url
                        let fileName = params.row.filename
                        return h(
                            'a',
                            {
                                attrs: {
                                    href: url,
                                    target: '_blank'
                                    // download : fileName 由于指向的是ip或非同一域名，出现跨域三方资源访问，导致download标签无效，后期优化
                                }
                            },
                            '下载'
                        )
                    }
                }
            ]
        }
    },
    methods: {
        init() {}
    }
}
</script>

<style scoped></style>
