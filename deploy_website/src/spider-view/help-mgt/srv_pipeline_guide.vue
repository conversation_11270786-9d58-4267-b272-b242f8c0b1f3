<template>
  <Card>
    <Row style="padding-left: 25px; margin-bottom: -10px">
      <h1>「服务端」流水线上线流程</h1>
    </Row>
    <Divider />
    <Row>
      <img :src="imgUrl1">
    </Row>
    <Divider />
    <Row style="padding-left: 25px; margin-bottom: -10px">
    <h1>流程处理</h1>
       </Row>
     <Divider />
    <p style="font-size: 20px;"> 应用名</p>
    <p>&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp应用名需要和pom.xml中保持一致，并且全公司唯一。</p>
    <p style="font-size: 20px;"> 迭代名</p>
    <p>&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp迭代名 = 分组_分支号。</p>
    <p style="font-size: 20px;"> 分支上线</p>
    <p>&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp采用分支上线，归档时产生Tag，Tag名称 = tag_分支号。</p>
    <Divider />
    <Row>
      <img :src="imgUrl2">
    </Row>
    <Divider />
    <Row style="padding-left: 25px; margin-bottom: -10px">
      <h1>迭代申请</h1>
    </Row>
    <Divider />
    <p style="font-size: 20px;"> 注意事项</p>
    <p>&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp1、选择研发类型时，分支名将自动添加br_的前缀；</p>
    <p>&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp2、选择紧急类型时，分支名将自动添加jinji_的前缀；</p>
    <p>&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp3、一次只能申请一个git组下的迭代；</p>
    <p>&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp4、只显示当前用户有代码权限的仓库；</p>
     <p>&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp5、超过截止日期的迭代将不再迭代列表显示。（此功能目前未上线）</p>
    <Divider />
    <Row>
      <img :src="imgUrl3">
    </Row>

    <Divider />
    <Row style="padding-left: 25px; margin-bottom: -10px">
      <h1>分支管理</h1>
    </Row>
    <Divider />
    <p style="font-size: 20px;"> 注意事项</p>
    <p>&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp1、只有申请人可以删除迭代中的仓库；</p>
    <p>&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp2、删除仓库会将分支代码一并删除，谨慎操作。</p>
    <Divider />
    <Row>
      <img :src="imgUrl4">
    </Row>
    <Divider />
    <Row style="padding-left: 25px; margin-bottom: -10px">
      <h1>编译</h1>
    </Row>
    <Divider />
    <p style="font-size: 20px;"> 测试包和生产包编译差别</p>
    <p>&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp1、编译的机器不同；</p>
    <p>&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp2、测试包在共享目录取包；</p>
     <p>&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp3、产线包推送至制品库。</p>
    <p style="font-size: 20px;"> 操作步骤</p>
    <p>&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp1、选择编译项配置；</p>
    <p>&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp2、勾选需要编译的项目；</p>
      <p>&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp3、点击执行按钮；</p>
    <p>&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp4、测试环境编译完成后到取包地址拿包部署。</p>
    <Divider />
    <Row>
      <img :src="imgUrl5">
    </Row>
    <Divider />
       <Row style="padding-left: 25px; margin-bottom: -10px">
      <h1>编译日志查看</h1>
    </Row>
    <Divider />
    <Row>
      <img :src="imgUrl6_1">
       <img :src="imgUrl6_2">
    </Row>
        <Row>
      <img :src="imgUrl6_3">

    </Row>
    <Divider />
       <Row style="padding-left: 25px; margin-bottom: -10px">
      <h1>迭代计划</h1>
    </Row>
    <Divider />
     <p style="font-size: 20px;"> 注意事项</p>
    <p>&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp1、邮件将发送给项目涉及人员，默认带出gitlab仓库权限人员，其他人员请手动添加；</p>
    <p>&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp2、上线应用列表只显示编译过产线包的应用；</p>
      <p>&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp3、填写sql后，项目涉及人员将自动添加dba；</p>
    <p>&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp4、点击上线完成将进行归档操作，分支代码将合并到master上。</p>
    <Divider />
    <Row>
      <img :src="imgUrl7">
    </Row>
    <Divider />
       <Row style="padding-left: 25px; margin-bottom: -10px">
      <h1>仿真发布</h1>
    </Row>
    <Divider />
     <p style="font-size: 20px;"> 注意事项</p>
    <p>&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp1、只显示推送到制品库中的应用；</p>
    <p>&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp2、最新代码没有编译，无法操作；</p>
      <p>&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp3、和制品库中版本不一致，无法操作。</p>
    <Divider />
    <Row>
      <img :src="imgUrl8">
    </Row>
    <Divider />
       <Row style="padding-left: 25px; margin-bottom: -10px">
      <h1>上线发布</h1>
    </Row>
    <Divider />
         <p style="font-size: 20px;"> 注意事项</p>
    <p>&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp1、只显示推送到制品库中的应用；</p>
    <p>&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp2、有gitlab库代码权限的人员可操作；</p>
    <p>&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp3、最新代码没有编译，无法操作；</p>
     <p>&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp4、和制品库中版本不一致，无法操作。</p>
    <Divider />
    <Row>
      <img :src="imgUrl9">
    </Row>
    <Divider />
  </Card>

</template>

<script>
    export default {
      name: "git_guide",
      data() {
        return {
          imgUrl1: require("../../../public/imgs/help_mgt/pipeline_guide/git_guide.png"),
          imgUrl2: require("../../../public/imgs/help_mgt/pipeline_guide/guide_o.png"),
          imgUrl3: require("../../../public/imgs/help_mgt/pipeline_guide/create_branch.png"),
          imgUrl4: require("../../../public/imgs/help_mgt/pipeline_guide/branch_manager.png"),
          imgUrl5: require("../../../public/imgs/help_mgt/pipeline_guide/complie.png"),
          imgUrl6_1: require("../../../public/imgs/help_mgt/pipeline_guide/log-1.png"),
          imgUrl6_2: require("../../../public/imgs/help_mgt/pipeline_guide/log-2.png"),
          imgUrl6_3: require("../../../public/imgs/help_mgt/pipeline_guide/log-3.png"),
          imgUrl7: require("../../../public/imgs/help_mgt/pipeline_guide/plan.png"),
          imgUrl8: require("../../../public/imgs/help_mgt/pipeline_guide/publish_per.png"),
          imgUrl9: require("../../../public/imgs/help_mgt/pipeline_guide/publish.png"),
        }
      }
    }
</script>

<style scoped>

</style>
