<template>
  <Card class="branch_card" shadow style="overflow-y:auto;">
    <Col span="17">
      <Card dis-hover>
        <p slot="title" style="width: 100%;text-align: center;">
          分支申请单
        </p>
        <Form
          ref="formValidate"
          :model="formValidate"
          :rules="ruleValidate"
          :label-width="120"
          style="text-align: left"
        >
          <FormItem label="分支类型：" prop="branch_type">
            <RadioGroup v-model="formValidate.branch_type" @on-change="changeType">
              <Radio label="release">release</Radio>
              <Radio label="bugfix">bugfix</Radio>
              <Radio label="feature">feature</Radio>
            </RadioGroup>
          </FormItem>
          <FormItem v-show="gr_show" label="分组列表：" prop="groupName">
            <Select v-model="formValidate.groupName" style="width: 300px" @on-change="changeGroup" filterable>
              <Option v-for="(item,index) in groupList "
                      :value="item['value']"
                      :label="item['value']"
                      :key="item+index">
                {{ item['value'] }}
              </Option>
            </Select>
          </FormItem>
          <FormItem v-show="gr_show" label="分支来源：" prop="re_br_name">
            <Select ref="re_br_name" v-model="formValidate.re_br_name" style="width: 300px" @on-change="changeIter"
                    filterable>
              <Option v-for="item in reBranchList "
                      :value="item"
                      :label="item"
                      :key="item">
                {{ item }}
              </Option>
            </Select>
          </FormItem>
          <FormItem v-show="gr_show" label="仓库名称" prop="project_list">
            <Tree :data="feature_data" ref="feature_Validate" show-checkbox v-model="formValidate.project_list"></Tree>
            <!--<CheckboxGroup v-model="formValidate.git_repos">-->
            <!--<Checkbox v-for="item in git_path_list" :label="item['value']" :key="item['value']"></Checkbox>-->
            <!--</CheckboxGroup>-->
          </FormItem>

          <i-button :icon="collepseAll? 'ios-arrow-up':'ios-arrow-down'" style="margin-left: 110px;" type="text"
                    @click="showCheckBox()">{{ collepseAll ? '全部收起' : '全部展开' }}
          </i-button>

          <!-- <Checkbox v-model="single" span="7" style="font-weight:600;margin-left: 120px;" @on-change="showCheckBox">
            展开/折叠
          </Checkbox> -->

          <FormItem v-show="br_show" label="应用选择：" prop="project_list">
            <Tree :data="data2"
                  ref="treeValidate"
                  show-checkbox
                  v-model="formValidate.project_list"
                  @on-check-change="projectChange"
            ></Tree>

          </FormItem>

          <FormItem v-show="out_dep_show" label="是否升级外部依赖版本：" prop="is_update_out_dep">
            <Row>
              <Col span="7">
                <RadioGroup v-model="formValidate.is_update_out_dep">
                  <Radio label="0">否</Radio>
                  <Radio label="1">是</Radio>
                </RadioGroup>
              </Col>
              <Col span="12" offset="1">
                说明：bugfix分支，默认不升级外部依赖包至最新归档版本，保留原master中的版本，如需升级，请选择“是”
              </Col>
            </Row>
          </FormItem>
          <FormItem v-show="br_show" label="分支版本号：" prop="branch_name">
            <Row>
              <Col span="7">
                <Input v-model="formValidate.branch_name" :maxlength="50" placeholder="填写分支名称"
                       @on-blur='checkBranchNameLenth'/>
              </Col>
              <Col span="2" offset="1">
                <Button type="primary" @click="query_recommend_version">查询</Button>
              </Col>
              <Col span="12" offset="1">
                如果不知道用什么版本号，不妨在这里点一点
              </Col>
            </Row>
          </FormItem>
          <FormItem label="截止日期：">
            <Row>
              <i-col span="8">
                <FormItem prop="date">
                  <DatePicker type="date" placeholder="Select date" v-model="formValidate.date"></DatePicker>
                </FormItem>
              </i-col>
              <i-col span="16">
                说明：默认截止日期为往后延十四个自然日，到期后会有邮件提醒，如果延期，请到“分支管理”页面修改截止日期
              </i-col>
            </Row>
          </FormItem>
          <FormItem v-show="br_show" label="TAPD_ID：" prop="tapd_id">
            <Row>
              <Col span="7">
                <Input readonly v-model="formValidate.tapd_id" placeholder="TAPD项目ID"/>
              </Col>
              <Col span="16" offset="1">
                说明：默认应用归属团队对应的TAPD_ID
              </Col>
            </Row>
          </FormItem>
          <FormItem label="描述：" prop="desc">
            <Input
              v-model="formValidate.desc"
              type="textarea"
              :autosize="{minRows: 2,maxRows: 5}"
              placeholder="Enter something..."
            />
          </FormItem>
          <FormItem>
            <Button type="primary" @click="handleSubmit('formValidate')" :loading="btnDisabled">提交</Button>
            <Button @click="handleReset('formValidate')" style="margin-left: 8px">重置</Button>
          </FormItem>
        </Form>
      </Card>
    </Col>
    <Col span="7">
      <Card dis-hover>
        <p slot="title">
          Feature分支规则
        </p>
        <div style="font-size:5px">
          <p> 一、分支拉取源：</p>
          <p> &nbsp;&nbsp;&nbsp;&nbsp;1、任意release分支。</p>
          <p> &nbsp;&nbsp;&nbsp;&nbsp;2、master分支。（暂不支持）</p>
          <p>二、分支名称自动生成规则：</p>
          <p>&nbsp;&nbsp;&nbsp;&nbsp;源分支名-第几次拉取。</p>
          <p>&nbsp;&nbsp;&nbsp;&nbsp;ps：如从master上拉取，第一次feature为 master-1，第二次拉取为master-2。</p>

          <p>
            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;如从release分支4.3.1上拉取，第一次为4.3.1-1，第二次为4.3.1-2。 </p>
          <p>三、版本规则：</p>
          <p>&nbsp;&nbsp;&nbsp;&nbsp;1、从master上拉取 pom文件版本统一为 master-SNAPSHOT。</p>
          <p>&nbsp;&nbsp;&nbsp;&nbsp;2、从release分支上拉取的feature分支，版本号和release分支一致，版本为SNAPSHOT。</p>
          <p> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; ps：release分支 4.3.1 上拉取的所有feature分支
            版本都为4.3.1-SNAPSHOT。</p>
        </div>
      </Card>
    </Col>
    <Modal
      title="   "
      width="800"
      v-model="recommend_version_modal_show"
      :mask-closable="true"
      ok-text="确定"
      @on-ok="confirmVersionNumber">
      <div>
        <Row>
          <Table :loading="tableLoading"
                 :columns="tableColumns"
                 :data="tableData"
                 :no-data-text="noDataText"
          >
          </Table>
        </Row>
        <Row>
          <Col span="3">
            推荐版本号：
          </Col>
          <Col span="18">
            <Input readonly v-model="recommend_version_number"/>
          </Col>
          <Col span="3">
            <Button @click="unSelectBranchName" style="margin-left: 8px">取消选中</Button>
          </Col>
        </Row>
        <Row>
          <Col span="3">
            推荐规则举例说明：
          </Col>
          <Col span="21">
            比如：当前存在开发中的分支0.2.0，已归档分支0.1.2<br>
            如果是release分支，推荐用0.3.0，算法：最新的开发中或者测试中的分支号的第二位数字+1<br>
            如果是bugfix分支，推荐用0.1.3，算法：最新的归档分支号的第三位数字+1<br>
            其他情况，默认1.0.0
          </Col>
        </Row>
      </div>
    </Modal>
  </Card>
</template>

<script>
import {
  createIter,
  get_breach_info_for_apply,
  getCreateIterStatusApi,
  getIterApplyTapdIdApi,
  syncAppBranchInfo
} from '@/spider-api/create-iter'
import {
  getCheckServiceMobile
} from '@/spider-api/iter-plan'
import { getGitGroupRopesApi, getGitRopesApi, getIterListApi, getIterInfo } from '@/spider-api/get-iter-info'

export default {
  data () {
    return {
      single: false,
      recommend_version_modal_show: false,
      recommend_version_number: '',
      data2: [],
      feature_data: [],
      br_show: true,
      gr_show: false,
      out_dep_show: false,
      btnDisabled: false,
      collepseAll: false,
      groupList: [],
      iterative_list: [],
      git_path_list: [],
      groupName: '',
      gourp_cout: 0,
      lock_apply: 0,
      formValidate: {
        branch_name: '',
        git_repos: [],
        is_update_out_dep: '1',
        branch_type: 'release',
        project_list: [],
        groupName: '',
        re_br_name: '',
        tapd_id: '',
        date: new Date(new Date().getTime() + 1209600000), // 1209600000
        desc: ''
      },
      currentid: 0,
      noDataText: '相关应用信息查询为空',
      tableLoading: false,
      tableData: [],
      tableColumns: [
        {
          title: '迭代号',
          key: 'pipeline_id',
          align: 'center'
        },
        {
          title: '应用名',
          key: 'appName',
          align: 'center'
        },
        {
          title: '分支版本号',
          key: 'br_name',
          align: 'center'
        },
        {
          title: '最近归档状态',
          key: 'sys_status',
          align: 'center'
        },
        {
          title: '归档时间',
          key: 'br_end_date',
          align: 'center',
          width: 180
        },
        {
          title: '操作',
          align: 'center',
          render: (h, params) => {
            if (params.row.br_end_date == null) {
              let id = params.row.id
              let flag = false
              if (this.currentid === id) {
                flag = true
              }
              let self = this
              return h(
                'Radio',
                {
                  props: {
                    value: flag
                  },
                  style: {
                    marginRight: '5px'
                  },
                  on: {
                    'on-change': (val) => {
                      self.currentid = id
                      this.selectBranchName(val, params.row.br_name)
                    }
                  }
                },
                ''
              )
            }
          }
        }
      ]
    }
  },

  computed: {
    ruleValidate () {
      if (this.formValidate.branch_type === 'feature') {
        return {
          branch_type: [
            { required: true, message: '请选择迭代类型', trigger: 'change' }
          ],
          re_br_name: [
            { required: true, message: '请选择分支源', trigger: 'change' }
          ],
          groupName: [
            { required: true, message: '请选择分组', trigger: 'change' }
          ],
          project_list: [
            {
              required: true,
              type: 'array',
              message: '请选择',
              trigger: 'change'
            }
          ],
          date: [
            {
              required: true,
              type: 'date',
              message: '请选择日期',
              trigger: 'change'
            }
          ],
          tapd_id: [
            {
              required: false,
              message: '',
              trigger: 'blur'
            }
          ],
          desc: [
            {
              required: true,
              message: '填写功能描述',
              trigger: 'blur'
            },
            {
              type: 'string',
              min: 5,
              message: '不可以少于5个字的描述',
              trigger: 'blur'
            }
          ]
        }
      } else {
        return {
          branch_type: [
            { required: true, message: '请选择迭代类型', trigger: 'change' }
          ],
          branch_name: [
            { required: true, message: '请填写分支名称', trigger: 'change' }
          ],
          date: [
            {
              required: true,
              type: 'date',
              message: '请选择日期',
              trigger: 'change'
            }
          ],
          project_list: [
            {
              required: true,
              type: 'array',
              message: '请选择仓库',
              trigger: 'change'
            }
          ],

          tapd_id: [
            {
              required: false,
              message: '',
              trigger: 'blur'
            }
          ],
          desc: [
            {
              required: true,
              message: '填写功能描述',
              trigger: 'blur'
            },
            {
              type: 'string',
              min: 5,
              message: '不可以少于5个字的描述',
              trigger: 'blur'
            }
          ]
        }
      }
    },
    reBranchList () {
      let re_br_list = []
      for (let i = 0; i < this.iterative_list.length; i++) {
        // console.log(this.iterative_list[i].br_style)
        if (this.iterative_list[i].br_style === 'release' && this.iterative_list[i].project_group === this.formValidate.groupName) {
          // console.log(this.iterative_list[i].pipeline_id);
          re_br_list.push(this.iterative_list[i].pipeline_id)
        }
      }
      re_br_list.push('master')
      // console.log(re_br_list);
      return re_br_list
    }

  },
  methods: {
    checkBranchNameLenth () {
      if (this.formValidate.branch_name.length >= 50) {
        alert('分支名过长，请重新申请')
      }
    },
    selectBranchName (stateVal, br_name) {
      this.recommend_version_number = br_name
    },
    unSelectBranchName () {
      this.currentid = 0
      this.recommend_version_number = ''
      this.count_recommend_version()
    },
    confirmVersionNumber () {
      this.formValidate.branch_name = this.recommend_version_number
    },
    query_recommend_version () {
      if (this.formValidate.branch_type === '') {
        this.$Message.error('请先选择分支类型！！！')
        return
      }
      let checkedNodes = this.$refs['treeValidate'].getCheckedNodes()
      if (checkedNodes.length > 0) {
        let app_name_list = []
        for (let checkedNode of checkedNodes) {
          if (checkedNode.level === 1) {
            app_name_list.push(checkedNode.title)
          }
        }
        get_breach_info_for_apply({ 'app_name_list': app_name_list.join(',') }).then(res => {
          this.tableData = res.data.data
          this.count_recommend_version()
        })
        this.recommend_version_modal_show = true
      } else {
        this.$Message.error('请先选择要申请的应用！！！')
      }
    },
    count_recommend_version () {
      let newList = this.tableData.filter(function (val) {
        return val.sys_status === '已归档'
      })
      let otherList = this.tableData.filter(function (val) {
        return val.sys_status !== '已归档'
      })
      if (this.formValidate.branch_type === 'release') {
        if (otherList.length > 0) {
          otherList.sort(function (a, b) {
            return b.id - a.id
          })
          let brName = otherList[0].br_name
          let br_name_list = brName.split('.')
          try {
            let num = parseInt(br_name_list[1]) + 1
            this.recommend_version_number = br_name_list[0] + '.' + num + '.' + '0'
          } catch (e) {
            this.recommend_version_number = '1.0.0'
          }
        } else {
          if (newList.length > 0) {
            newList.sort(function (a, b) {
              return new Date(a.br_end_date).getTime() - new Date(b.br_end_date).getTime()
            })
            let brName = newList[newList.length - 1].br_name
            let br_name_list = brName.split('.')
            try {
              let num = parseInt(br_name_list[1]) + 1
              this.recommend_version_number = br_name_list[0] + '.' + num + '.' + '0'
            } catch (e) {
              this.recommend_version_number = '1.0.0'
            }
          } else {
            this.recommend_version_number = '1.0.0'
          }
        }
      } else {
        // console.info(newList);
        if (newList.length > 0) {
          newList.sort(function (a, b) {
            return new Date(a.br_end_date).getTime() - new Date(b.br_end_date).getTime()
          })
          // console.info(newList);
          let brName = newList[newList.length - 1].br_name
          let br_name_list = brName.split('.')
          try {
            if (br_name_list.length >= 3) {
              let num = parseInt(br_name_list[2]) + 1
              this.recommend_version_number = br_name_list[0] + '.' + br_name_list[1] + '.' + num
            } else {
              this.recommend_version_number = br_name_list[0] + '.' + br_name_list[1] + '.' + 1
            }
          } catch (e) {
            this.recommend_version_number = '1.0.1'
          }
        } else {
          if (otherList.length > 0) {
            otherList.sort(function (a, b) {
              return a.id - b.id
            })
            let brName = otherList[0].br_name
            let br_name_list = brName.split('.')
            try {
              if (br_name_list.length >= 3) {
                let num = parseInt(br_name_list[2]) + 1
                this.recommend_version_number = br_name_list[0] + '.' + br_name_list[1] + '.' + num
              } else {
                this.recommend_version_number = br_name_list[0] + '.' + br_name_list[1] + '.' + 1
              }
            } catch (e) {
              this.recommend_version_number = '1.0.1'
            }
          } else {
            this.recommend_version_number = '1.0.1'
          }
        }
      }
      // if (newList.length > 0) {
      //   newList.sort(function (a, b) {
      //     return b.br_end_date - a.br_end_date
      //   });
      //   let brName = newList[0].br_name;
      //   let br_name_list = brName.split(".");
      //   if (this.formValidate.branch_type === 'release') {
      //     try {
      //       let num = parseInt(br_name_list[1]) + 1;
      //       this.recommend_version_number = br_name_list[0] + '.' + num + '.' + '0'
      //     } catch (e) {
      //       this.recommend_version_number = '1.0.0'
      //     }
      //   } else {
      //     try {
      //       if (br_name_list.length >= 3) {
      //         let num = parseInt(br_name_list[2]) + 1;
      //         this.recommend_version_number = br_name_list[0] + '.' + br_name_list[1] + '.' + num
      //       } else {
      //         this.recommend_version_number = br_name_list[0] + '.' + br_name_list[1] + '.' + 1
      //       }
      //     } catch (e) {
      //       this.recommend_version_number = '1.0.1'
      //     }
      //
      //   }
      //
      // } else {
      //   if (this.tableData.length > 0) {
      //
      //   } else {
      //     if (this.formValidate.branch_type === 'release') {
      //       this.recommend_version_number = '1.0.0'
      //     } else {
      //       this.recommend_version_number = '1.0.1'
      //     }
      //
      //   }
      //
      // }
    },
    // 应用选择改变触发方法
    projectChange (nodeList, node) {
      let checkedAndIndeterminateNodes = this.$refs['treeValidate'].getCheckedAndIndeterminateNodes()
      let parentList = []
      let parentList2 = []
      for (let checkedAndIndeterminateNode of checkedAndIndeterminateNodes) {
        if (checkedAndIndeterminateNode.level === 0) {
          parentList.push(checkedAndIndeterminateNode)
        }
        if (checkedAndIndeterminateNode.level === 2) {
          parentList2.push(checkedAndIndeterminateNode)
        }
        // checkedNodeKey.push(checkedAndIndeterminateNode.nodeKey)
      }
      // console.info(parentList);
      if (parentList.length > 1) {
        // if (node.level === 0) {
        //   // console.info(node.children);
        //   for (let child of node.children) {
        //     if (child.level === 2) {
        //       for (let child2 of child.children) {
        //         child2.checked = false
        //       }
        //       child.checked = false
        //       child.indeterminate = false
        //     } else {
        //       child.checked = false
        //     }
        //     // console.info(child);
        //   }
        //   node.checked = false
        //   node.indeterminate = false
        // } else if (node.level === 2) {
        //   // console.info(node);
        //   for (let child of node.children) {
        //     child.checked = false
        //     // console.info(child);
        //   }
        //   node.checked = false
        //   node.indeterminate = false
        //   for (let parentListElement of parentList) {
        //     if (node.repos_path.indexOf(parentListElement.title) > -1) {
        //       parentListElement.indeterminate = false
        //     }
        //   }
        // } else {
        //   node.checked = false
        //   for (let parentListElement of parentList) {
        //     if (node.repos_path.indexOf(parentListElement.title) > -1) {
        //       parentListElement.indeterminate = false
        //     }
        //   }
        //   for (let parentListElement of parentList2) {
        //     if (node.repos_path.indexOf(parentListElement.title) > -1) {
        //       parentListElement.indeterminate = false
        //     }
        //   }
        // }
        this.$Message.info('正在同时选择多个团队下的应用！！！')
      } else {
        let checkedNodes = this.$refs['treeValidate'].getCheckedNodes()
        if (checkedNodes.length > 0) {
          let app_name = ''
          for (let checkedNode of checkedNodes) {
            if (checkedNode.level === 1) {
              app_name = checkedNode.title
              break
            }
          }
          getIterApplyTapdIdApi({ 'app_name': app_name }).then(res => {
            this.formValidate.tapd_id = res.data.msg
          })
        } else {
          this.formValidate.tapd_id = ''
        }
      }
    },
    changeIter (params) {
      if (params === 'master') {
        params = this.formValidate.groupName + '_master'
      }
      getGitGroupRopesApi({ iterationID: params }).then(res => {
        console.log(JSON.stringify(res.data.data['repos_list']))
        this.feature_data = res.data.data['repos_list']
      })
    },
    changeGroup (params) {
      this.formValidate.branch_name = ''
      this.$refs['re_br_name'].value = ''
      this.feature_data = []
      // this.git_path_list=[]
    },
    getReposList (choicesAll) {
      // let choicesAll = vm.$refs["treeValidate"].getCheckedAndIndeterminateNodes();
      // console.log(JSON.stringify(choicesAll))
      let group_project = {}
      let project_list = []

      this.gourp_cout = 0
      choicesAll.forEach(item => {
        console.log(JSON.stringify(item))
        if ('group_name' in item) {
          this.groupName = item.title
          this.gourp_cout = this.gourp_cout + 1
        } else if ('children' in item) {
        } else {
          let repoGroupName = item.repos_path.split('/')[0]
          if (!group_project.hasOwnProperty(repoGroupName)) {
            group_project[repoGroupName] = []
          }
          if ('module_name' in item) {
            group_project[repoGroupName].push({ 'repos_path': item.repos_path, 'module_name': item.module_name })
            project_list.push({ 'repos_path': item.repos_path, 'module_name': item.module_name })
          } else {
            group_project[repoGroupName].push({ 'repos_path': item.repos_path })
            project_list.push({ 'repos_path': item.repos_path })
          }
        }
      })
      this.formValidate.project_list = project_list
      return group_project
      // if (this.gourp_cout > 1) {
      //   this.$Message.error('不能同时申请两个以上gitlab组的分支!!!')
      //   this.$Spin.hide()
      //   this.btnDisabled = false
      // }
      // this.formValidate.project_list = project_list
      // console.log(this.groupName)
      // console.log(this.formValidate.project_list)
    },

    changeType (params) {
      if (params === 'feature') {
        this.br_show = false
        this.gr_show = true
        this.out_dep_show = false
        this.formValidate.is_update_out_dep = '1'
        this.getIterList()
        // (params)
      } else if (params === 'bugfix') {
        this.br_show = true
        this.gr_show = false
        this.out_dep_show = true
        this.formValidate.is_update_out_dep = '0'
      } else {
        this.br_show = true
        this.gr_show = false
        this.out_dep_show = false
        this.formValidate.is_update_out_dep = '1'
      }
    },
    getIterList (params) {
      getIterListApi().then(res => {
        // console.log(JSON.stringify(res))
        this.iterative_list = res.data.data['iterative_list']
        // alert(JSON.stringify(this.iterative_list))
        // this.columns[2].filters = res.data.group_list
        this.groupList = res.data.data['group_list']

        // alert(JSON.stringify(this.iterative_list))
      })
    },
    getStatus (sid) {
      getCreateIterStatusApi({ 'sid': sid }).then(res => {
        if (res.data.msg === 'running') {
          let vm = this
          setTimeout(function () {
            vm.getStatus(sid)
          }, 2000)
        } else {
          this.$Message.success(res.data['msg'])
          alert(res.data['msg'])
          this.unlockApply()
          syncAppBranchInfo().then(res => {
            if (res.data.msg === 'success') {
              this.$Message.success('"触发同步应用分支信息任务成功！"')
              // alert("触发同步分支任务成功！")
            } else {
              alert('触发同步应用分支信息任务失败！可尝试手动执行jenkins job。')
            }
          })
        }
      })
        .catch(err => {
          this.$Message.error(err.response.data.msg)
          alert(err.response.data.msg)
          this.unlockApply()
        })
    },
    unlockApply () {
      this.lock_apply = this.lock_apply - 1
      if (this.lock_apply == 0) {
        this.$Spin.hide()
        this.btnDisabled = false
      }
    },
    handleSubmit (param) {
      let myDate = new Date()
      console.log('开始拉分支')
      console.log(myDate.getTime())
      this.btnDisabled = true
      console.log(this.btnDisabled)
      this.$Spin.show({
        render: (h) => {
          return h('div', [
            h('Icon', {
              'class': 'demo-spin-icon-load',
              props: {
                type: 'ios-loading',
                size: 18
              }
            }),
            h('div', '分支拉取中请稍等。。。')
          ])
        }
      })
      var vm = this
      if (vm.formValidate.branch_type === 'feature') {
        var choicesAll = vm.$refs['feature_Validate'].getCheckedAndIndeterminateNodes()
      } else {
        var choicesAll = vm.$refs['treeValidate'].getCheckedAndIndeterminateNodes()
      }

      const groupProject = this.getReposList(choicesAll)
      console.log(groupProject)
      // this.btnDisabled = false;
      if (this.btnDisabled) {
        vm.$refs[param].validate(valid => {
          if (valid) {
            for (let repoGroupName in groupProject) {
              console.log(repoGroupName)
              this.lock_apply = this.lock_apply + 1
              let branch_info = {}
              if (vm.formValidate.branch_type === 'feature') {
                let re_br_name = vm.formValidate.re_br_name.split('_')[1]
                if (vm.formValidate.re_br_name === 'master') {
                  re_br_name = vm.formValidate.re_br_name
                }
                branch_info = {
                  branch_name: re_br_name,
                  branch_type: vm.formValidate.branch_type,
                  is_update_out_dep: vm.formValidate.is_update_out_dep,
                  deadline: vm.formValidate.date,
                  desc: vm.formValidate.desc,
                  gitlab_group: repoGroupName,
                  repos_str: groupProject[repoGroupName],
                  tapd_id: ''
                }
              } else {
                console.log(vm.formValidate.is_update_out_dep)
                branch_info = {
                  branch_name: vm.formValidate.branch_name,
                  branch_type: vm.formValidate.branch_type,
                  is_update_out_dep: vm.formValidate.is_update_out_dep,
                  deadline: vm.formValidate.date,
                  desc: vm.formValidate.desc,
                  tapd_id: vm.formValidate.tapd_id,
                  gitlab_group: repoGroupName,
                  repos_str: groupProject[repoGroupName]
                }
              }
              console.log(branch_info)
              if (vm.formValidate.branch_type === 'feature') {
                createIter(branch_info).then(result => {
                  console.log('============' + JSON.stringify(result))
                  if (result.data['status'] === 'failed') {
                    alert(JSON.stringify(result.data['msg']))
                    this.unlockApply()
                  } else {
                    let sid = result.data.data['sid']
                    // alert(result.data)
                    this.getStatus(sid)
                  }
                })
              } else {
                getCheckServiceMobile(vm.formValidate).then(result => {
                  if (result.data.status == 'failed') {
                    alert('移动端和服务端不能放在同一个迭代')
                    this.unlockApply()
                    return false
                  } else {
                    getIterInfo(branch_info['branch_name'], branch_info['gitlab_group']).then(res => {
                      console.log(res.data.msg)
                      if (res.data.msg === '该迭代已存在') {
                        console.log('该迭代已存在')
                        alert('该迭代已存在,若想把以下应用加入该迭代，请在迭代列表选择该迭代，然后点击迭代名进入分支管理页面，进行仓库追加！')
                        this.unlockApply()
                      } else if (res.data.msg === '该迭代已关闭') {
                        alert('该迭代已归档，请重新选择分支！')
                        this.unlockApply()
                      } else {
                        createIter(branch_info).then(result => {
                          console.log(JSON.stringify(result))
                          if (result.data['status'] === 'failed') {
                            alert(JSON.stringify(result.data['msg']))
                            this.unlockApply()
                          } else {
                            let sid = result.data.data['sid']
                            // alert(result.data)
                            this.getStatus(sid)
                          }
                        })
                      }
                    })
                  }
                })
              }
            }
          } else {
            this.$Message.error('有必填项未填写!')
            this.$Spin.hide()
            this.btnDisabled = false
          }
        })
      }
      console.log('拉分支结束')
      console.log(myDate.getTime())
    },
    handleReset (name) {
      this.$refs[name].resetFields()
    },
    getGitRopes () {
      getGitRopesApi()
        .then(res => {
          this.data2 = res.data.data['repos_tree']
        })
    },
    showCheckBox () {
      this.collepseAll = !this.collepseAll
      for (let item in this.data2) {
        this.data2[item]['expand'] = this.collepseAll
      }
    }
  },
  mounted () {
    this.getGitRopes()
  }

}
</script>

<style>
</style>
