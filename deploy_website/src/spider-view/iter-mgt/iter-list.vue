<template>
    <div>
        <div style="margin-bottom: 15px">
            <h2>与我相关的项目</h2>
        </div>
        <Card>
            <div style="margin-bottom:10px">
                <span align="right" style="font-size: 1rem; width: 5em; display:inline-block;">迭代名称</span>
                <Input
                    style="width: 14em; font-size: 1rem; padding-left: 1em; color: #2d8cf0;"
                    clearable
                    v-model="pipeline_id_search"
                    @on-enter="getTable(1)"
                />
                <span align="right" style="font-size: 1rem; width: 5em; display:inline-block;margin-right:10px"
                    >分支类型</span
                >
                <Select v-model="br_style_search" style="width:100px;text-align: left" filterable clearable>
                    <Option v-for="item in iter_list" :value="item.value" :label="item.label" :key="item.value">
                        {{ item.label }}
                    </Option>
                </Select>
                <span align="right" style="font-size: 1rem; width: 5em; display:inline-block;margin-right:10px"
                    >git分组</span
                >
                <Select v-model="project_group_search" style="width:100px;text-align: left" filterable clearable>
                    <Option
                        v-for="item in git_urls"
                        :value="item.git_urls_name"
                        :label="item.git_urls_name"
                        :key="item.git_urls_name"
                    >
                        {{ item.git_urls_name }}
                    </Option>
                </Select>
                <span align="right" style="font-size: 1rem; width: 5em; display:inline-block;">功能描述</span>
                <Input
                    style="width: 14em; font-size: 1rem; padding-left: 1em; color: #2d8cf0;"
                    clearable
                    v-model="description_content_search"
                />

                <span align="right" style="font-size: 1rem; width: 5em; display:inline-block;margin-right:10px"
                    >分支分类</span
                >
                <Select v-model="branch_is_new" style="width:100px;text-align: left" filterable clearable>
                    <Option
                        v-for="item in branch_is_new_list"
                        :value="item.value"
                        :label="item.label"
                        :key="item.value"
                    >
                        {{ item.label }}
                    </Option>
                </Select>

                <Button @click="getTable(1)" type="info" style="#f8f8f9;margin-left:10px">查询</Button>
            </div>
            <tables ref="tables" v-model="tableData" :columns="columns" filteredValue />
            <i-col>
                <Page
                    style="margin: 5px;"
                    :total="pageTotal"
                    :current="pageNum"
                    @page-size="pageSize"
                    @on-change="changePage"
                    show-total
                ></Page>
            </i-col>
        </Card>
    </div>
</template>
<script>
import Tables from '_c/tables'
import { getIterListSearchApi, saveSelfTestFlag } from '@/spider-api/get-iter-info'

export default {
    name: 'iter_list',
    components: {
        Tables
    },
    data() {
        return {
            pipeline_id_search: '',
            project_group_search: '',
            br_style_search: '',
            description_content_search: '',
            branch_is_new: '',
            pageNum: 1,
            pageSize: 10,
            // "pageTotal": this.pageTotal,
            pageTotal: 0,
            page: [],
            git_urls: [],
            iter_list: [
                {
                    value: 'release',
                    label: 'release'
                },
                {
                    value: 'bugfix',
                    label: 'bugfix'
                },
                {
                    value: 'feature',
                    label: 'feature'
                }
            ],
            branch_is_new_list: [
                {
                    value: 1,
                    label: '新分支'
                },
                {
                    value: 0,
                    label: '旧分支'
                }
            ],
            columns: [
                {
                    title: '迭代名称',
                    key: 'pipeline_id',
                    render: (h, params) => {
                        return h('div', [
                            h(
                                'a',
                                {
                                    on: {
                                        click: () => {
                                            const row = params.row
                                            this.iterationPage(row)
                                            // 处理点击事件
                                        }
                                    }
                                },
                                params.row.pipeline_id
                            )
                        ])
                    }
                },
                { title: '迭代类型', key: 'br_style' },
                {
                    title: '研发自测',
                    key: 'self_test_flag',
                    render: (h, params) => {
                        return h('Checkbox', {
                            props: {
                                value: params.row.self_test_flag
                            },
                            on: {
                                input: val => {
                                    params.row.self_test_flag = val
                                    console.log('params.row.self_test_flag===' + params.row.self_test_flag)
                                    // 执行保存操作
                                    this.save_self_test_flag(params.row)
                                }
                            }
                        })
                    }
                },
                { title: '申请人', key: 'proposer' },
                { title: '分组', key: 'project_group' },
                { title: '创建时间', key: 'br_start_date', sortable: true },
                { title: '截止时间', key: 'duedate', sortable: true },
                { title: '功能描述', key: 'description', sortable: true }
            ],
            tableData: []
        }
    },

    methods: {
        save_self_test_flag(row) {
            saveSelfTestFlag(row).then(res => {
                this.getTable(1)
                alert(res.data.msg)
            })
        },
        handleDelete(params) {
            // console.log(params)
        },
        changePage(idx) {
            this.pageNum = idx
            this.getTable(idx)
        },
        iterationPage(par) {
            console.log('par====' + JSON.stringify(par))
            if (par.pipeline_type === 'server') {
                this.$router.push({
                    name: 'pipeline_page',
                    params: { iteration_id: par.pipeline_id, iterative_type: par.br_style }
                })
            } else if (par.pipeline_type === 'h5') {
                this.$router.push({
                    name: 'h5_mobile_pipeline',
                    query: { project_group: par.project_group, branch_name: par.branch_name }
                })
            } else if (par.pipeline_type === 'app') {
                this.$router.push({
                    name: 'app_mobile_pipeline',
                    query: { project_group: par.project_group, branch_name: par.branch_name }
                })
            } else if (par.pipeline_type === 'py') {
                this.$router.push({
                    name: 'python_pipeline',
                    query: { project_group: par.project_group, branch_name: par.branch_name }
                })
            }
            // else {
            //     this.$router.push({
            //         name: 'mobile_pipeline',
            //         query: { project_group: par.project_group, branch_name: par.branch_name }
            //     })
            // }
        },
        getTable(idx) {
            let data = {
                pipeline_id_search: this.pipeline_id_search,
                project_group_search: this.project_group_search,
                br_style_search: this.br_style_search,
                pageNum: idx,
                pageSize: this.pageSize,
                pageTotal: 0,
                description_content_search: this.description_content_search,
                branch_is_new: this.branch_is_new
            }
            getIterListSearchApi(data).then(res => {
                let page = res.data.data['page']
                // console.log(res.data.data["iterative_list"])
                this.pageNum = page['num']
                this.pageSize = page['size']
                this.pageTotal = page['total']
                this.tableData = res.data.data['iterative_list']
                // this.columns[2].filters=res.data.data["group_list"]
                this.git_urls = res.data.data['git_urls']
                this.page = res.data.data['page']

                console.log(this)
                //this.groupList=res.data.group_list;
                //alert(this.groupList)
            })
        }
    },
    mounted() {
        this.getTable(1)
        // getIterListApi().then(res => {
        //   this.tableData = res.data.data["iterative_list"]
        //   this.columns[2].filters=res.data.data["group_list"]
        //   this.git_urls = res.data.data["git_urls"]
        //   this.page = res.data.data["page"]
        //   //this.groupList=res.data.group_list;
        //   //alert(this.groupList)

        // })
    }
}
</script>

<style></style>
