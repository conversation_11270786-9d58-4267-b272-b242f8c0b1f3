<template>
  <Card shadow>
    <Row style="margin-top: 2em">
      <i-col span="2" style="font-weight: bold">我的流水线</i-col>
      <Button ghost type="success" style="float: left" @click="show_img_modal=true">流水线概念图</Button>
    </Row>
    <Row style="margin-top: 2em">
      <i-col span="2" style="font-weight: bold">迭代名称</i-col>
      <i-col span="6" style="font-weight: bold">
        <Input placeholder="输入迭代名称" v-model="query_param"></Input>
      </i-col>
      <i-col span="2" style="font-weight: bold">
        <Button ghost type="success" style="float: right" @click="query_info">查询</Button>
      </i-col>
    </Row>
    <Table
      style="margin-top: 1em"
      border
      width="1150"
      :columns="mypipeline_columns"
      :data="mypipeline_data"
    ></Table>
    <Page class="pag"
          :total="pagination.total"
          :current="pagination.page"
          :page-size="pagination.size"
          show-total
          show-elevator
          show-sizer
          @on-change="pageChange"
          @on-page-size-change="pageSizeChange"
    />
    <Modal width="560" v-model="modal_compile" title="流水线执行概况">
      <Table border :columns="compile_columns" :data="compile_data"></Table>
    </Modal>

    <Modal
      title=""
      v-model="select_env_modal_show"
      width="580"
      :mask-closable="true"
      @on-ok="submitChangeSubmitAndContinuousIntegration"
    >
      <Row style="margin-top: 2em">
        <RadioGroup v-model="select_env">
          <Radio v-for="item in select_env_list"
                 :label="item">
          </Radio>
        </RadioGroup>
      </Row>
      <Row style="margin-top: 2em">
        <i-col span="15">说明：持续集成流水线暂时只支持推单个环境， 请选择一个环境套进行部署</i-col>
      </Row>
    </Modal>
    <Modal
      title=""
      v-model="con_select_env_modal_show"
      width="580"
      :mask-closable="true"
      @on-ok="submitContinuousIntegration"
    >
      <Row style="margin-top: 2em">
        <RadioGroup v-model="con_select_env">
          <Radio v-for="item in con_select_env_list"
                 :label="item">
          </Radio>
        </RadioGroup>
      </Row>
      <Row style="margin-top: 2em">
        <i-col span="15">说明：持续集成流水线暂时只支持推单个环境， 请选择一个环境套进行部署</i-col>
      </Row>
    </Modal>
    <Modal
      title=" "
      width="1000"
      v-model="show_img_modal"
      :mask-closable="true">
      <div style="width: 120%">
        <img :src="imgUrl" style="width: 900px"/>
      </div>
    </Modal>
    <Row style="margin-top: 2em">
      <i-col span="12">
        <i-col span="22">备注：</i-col>
        <i-col span="22">变更提交：完整构建并推送制品库。</i-col>
        <i-col span="22">变更-推包-CI ：推送制品到测试环境并启动。</i-col>
        <i-col span="22">推包-CI：完整构建推送制品库，并自动推送制品到测试环境并启动（需要提前给流水线做测试环境绑定）。</i-col>
        <i-col span="22">详情1：跳转变更提交Jenkins Blueocean详情页</i-col>
        <i-col span="22">详情2：跳转持续集成Jenkins Blueocean详情页</i-col>
        <i-col span="22">详情请查看流水线概念图</i-col>
      </i-col>
      <i-col span="12">
        执行历史：
        <Card shadow style="height: 150px;overflow-y: auto;margin-top: 5px;">
          <p v-for="optitem in opt_history_data">{{optitem}}</p>
        </Card>
      </i-col>
    </Row>
    <Drawer title="编译列表" :mask-closable="false" v-model="drawer_compile" :closable="false" placement="right"
            width="460px">
      <slot>
        <Tree ref="compile_list" :data="compile_list" show-checkbox multiple></Tree>
        <Button
          type="success"
          @click="startCustomCompile"
          style="margin-top: 2em; margin-right: 1em"
        >开始构建
        </Button>
        <Button
          type="error"
          @click="closeDrawer"
          style="margin-top: 2em;"
        >取消
        </Button>
      </slot>
    </Drawer>
  </Card>
</template>

<script>
  import {
    getEnvBindInfo,
    getPipelineInfo,
    doJenkinsCompileJob,
    parseCompileApp,
    doCustomCompile,
    saveOrUpdateEnvBind,
    doEnvBind,
    pushTestEnv
  } from "@/spider-api/pipeline";
  import {
    getMyPipelineInfo
  } from "@/spider-api/my-pipeline";
  import {
    createPipeLineLog
  } from "@/spider-api/pipeline-log";

  export default {
    data() {
      return {
        show_img_modal: false,
        imgUrl: require("../../img/流水线概念图.png"),
        switch_pipeline_info: "",
        pipeline_id:'',
        query_param: "",
        pagination: {
          page: 1,
          size: 10,
          total: 0,
        },
        mypipeline_data: [],
        opt_history_data: [],
        mypipeline_columns: [
          {
            type: "index",
            width: 50,
            align: "center"
          },
          {
            title: "流水线名称",
            key: "job_name",
            width: 200
          },
          {
            title: "迭代名称",
            width: 100,
            align: "center",
            render: (h, params) => {
              return h('a', {
                on: {
                  click: () => {
                    this.iterationPage(params.row)
                  }
                },
                domProps: {
                  innerHTML: params.row.pipeline_id
                },
              }, '')
            }
          },
          {
            title: "已绑定测试环境",
            key: "env_list",
            width: 150,
            align: "center"
          },

          {
            title: "运行状态",
            width: 100,
            align: "center",
            render: (h, params) => {
              let color = '';
              if (params.row.status === 'SUCCESS') {
                color = 'green';
              } else if (params.row.status === 'FALIED') {
                color = 'red';
              } else if (params.row.status === 'RUNNING') {
                color = '#ff9900';
              }
              return h('div', {
                style: {
                  color: color
                },
                domProps: {
                  innerHTML: params.row.status
                },
              }, '')
            }
          },
          {
            title: "触发操作",
            align: "center",
            render: (h, params) => {
              return h("div", [
                h(
                  "Button",
                  {
                    props: {
                      //disabled: "disabled"
                    },
                    style: {
                      marginRight: "1em"
                    },
                    on: {
                      click: () => {
                        this.beginChangeSubmit(params.row)
                        let job_name = params.row.job_name
                        let opt_desc = job_name +"'变更提交'流水线"
                        let pipeline_id = params.row.pipeline_id
                        createPipeLineLog(opt_desc,pipeline_id,job_name).then(res => {
                        }).catch(err => {
                          this.$Message.error(err.response.data.msg);
                        })
                      }
                    }
                  },
                  "变更提交"
                ),
                h(
                  "Button",
                  {
                    props: {
                      //disabled: "disabled"
                    },
                    style: {
                      marginRight: "1em"
                    },
                    on: {
                      click: () => {
                        this.beginChangeSubmitAndContinuousIntegration(params.row)
                        /*this.jenkinsCompileExec(
                          this.pipeline_data[params.index]["job_name"],this.pipeline_data[params.index]["need_mock"], 0, 0
                        );*/
                      }
                    }
                  },
                  "变更-推包-CI"
                ),
                h(
                  "Button",
                  {
                    props: {
                      //disabled: "disabled"
                    },
                    style: {
                      marginRight: "1em"
                    },
                    on: {
                      click: () => {
                        this.beginContinuousIntegration(params.row)
                        let job_name = params.row.job_name
                        let opt_desc = job_name + "'推包-CI'流水线"+ "("+params.row.env_list+")"
                        let pipeline_id = params.row.pipeline_id
                        createPipeLineLog(opt_desc,pipeline_id,job_name).then(res => {
                        }).catch(err => {
                          this.$Message.error(err.response.data.msg);
                        })
                      }
                    }
                  },
                  "推包-CI"
                ),
                h(
                  "Button",
                  {
                    attrs: {
                      class: "ivu-btn ivu-btn-primary ivu-btn-ghost",
                      title:'跳转变更提交Jenkins Blueocean详情页',
                    },
                    props: {
                      //disabled: "disabled"
                    },
                    style: {
                      marginRight: "1em"
                    },
                    on: {
                      click: () => {
                        window.open(
                          this.mypipeline_data[params.index].jenkins_job_url
                        );
                      }
                    }
                  },
                  "详情1"
                ),
                h(
                  "Button",
                  {
                    attrs: {
                      class: "ivu-btn ivu-btn-primary ivu-btn-ghost",
                      title:'跳转持续集成Jenkins Blueocean详情页',
                    },
                    props: {
                      //disabled: "disabled"
                    },
                    style: {
                      marginRight: "1em"
                    },
                    on: {
                      click: () => {
                        window.open(
                          this.mypipeline_data[params.index].continuous_integration_url
                        );
                      }
                    }
                  },
                  "详情2"
                )
              ]);
            }
          }
        ],
        modal_compile: false,
        compile_data: [],
        compile_columns: [
          {
            title: "流水线名称",
            key: "job_name",
            width: 240
          },
          {
            title: "详情",
            key: "msg",
            width: 260,
            render: (h, params) => {
              if (params.row.result === "success") {
                var color = "green";
              } else {
                var color = "red";
              }
              return h("div", [
                h(
                  "p",
                  {
                    props: {},
                    style: {
                      color: color
                    }
                  },
                  params.row.msg
                )
              ]);
            }
          }
        ],
        select_env: '',
        select_param: '',
        select_env_list: [],
        select_env_modal_show: false,
        con_select_env: '',
        con_select_param: '',
        con_select_env_list: [],
        con_select_env_modal_show: false,
        drawer_compile: false,
        compile_list: [],
        custom_job_name: '',

      };
    },
    methods: {
      init() {
        let data = {
          page: this.pagination.page,
          size: this.pagination.size,
        };
        this.getMypipelineData(data)
      },
      query_info() {
        this.pagination.page = 1;
        let data = {
          page: 1,
          size: this.pagination.size,
          query_param: this.query_param
        };
        this.getMypipelineData(data)
      },
      iterationPage(par) {
        this.$router.push({
          name: "pipeline_page",
          params: {"iteration_id": par.pipeline_id, "iterative_type": par.br_style}
        })
      },
      getMypipelineData(data) {
        getMyPipelineInfo(data).then(res => {
          if (res.data.status === "success") {
            this.mypipeline_data = res.data.data.datalist;
            this.pagination.total = res.data.msg;
            this.opt_history_data = res.data.data.opt_his_list;
          } else {
            this.$Message.error(res.data.msg);
          }
        }).catch(err => {
          this.$Message.error(err.response.data.msg);
        })
      },
      pageChange(page) {
        /**
         * 分页中改变size触发的函数
         */
        this.pagination.page = page;
        let data = {
          page: this.pagination.page,
          size: this.pagination.size,
          query_param: this.query_param
        };
        this.getMypipelineData(data)
      },
      pageSizeChange(size) {
        this.pagination.size = size;
        this.pagination.page = 1;
        let data = {
          page: 1,
          size: this.pagination.size,
          query_param: this.query_param
        };
        this.getMypipelineData(data)
      },
      initThisVue() {
        let data = {
          page: this.pagination.page,
          size: this.pagination.size,
          query_param: this.query_param
        };
        this.getMypipelineData(data);
        if (!this.switch_pipeline_info) {
          this.switch_pipeline_info = setInterval(this.getMypipelineData, 15000, data)
        } else {
          clearInterval(this.switch_pipeline_info);
          this.switch_pipeline_info = setInterval(this.getMypipelineData, 15000, data)
        }
      },
      submitChangeSubmitAndContinuousIntegration() {
        /*选择环境后执行变更提交+持续集成*/
        doEnvBind(this.select_param.job_name, this.select_param.app_name, this.select_env)
          .then(res => {
            if (res.data.status === "success") {
              //this.$Message.success(res.data.msg);
              this.jenkinsCompileExec(
                this.select_param.job_name,
                this.select_param.need_mock,
                0,
                0
              );
            } else {
              this.$Message.error(res.data.msg);
            }
          })
          .catch(err => {
            this.$Message.error(err.response.data.msg);
          });

          let job_name = this.select_param.job_name
          let opt_desc = job_name+"'变更-推包-CI'流水线" + "("+this.select_env+")"
          let pipeline_id = this.pipeline_id;
          createPipeLineLog(opt_desc,pipeline_id,job_name).then(res => {
          }).catch(err => {
            this.$Message.error(err.response.data.msg);
          })
      },
      submitContinuousIntegration() {
        /*选择环境后执行持续集成*/
         if (this.con_select_param.git_repos_time === null || this.con_select_param.git_repos_time === '') {
          this.$Message.error("该应用无可用制品,请触发变更提交流水线构建可用制品！");
          return;
        }
        this.$Spin.show({
          render: h => {
            return h("div", [
              h("Icon", {
                class: "demo-spin-icon-load",
                props: {
                  type: "ios-loading",
                  size: 32,
                  color: "#2d8cf0"
                }
              })
            ]);
          }
        });
        pushTestEnv(this.con_select_param.pipeline_id, this.con_select_param.node, this.con_select_param.app_name, this.con_select_env)
          .then(res => {
            this.$Spin.hide();
            if (res.data.status === "success") {
              this.$Message.success(res.data.msg);
            } else {
              this.$Message.error(res.data.msg);
            }
          })
          .catch(err => {
            this.$Spin.hide();
            this.$Message.error("发生异常");
          });
      },
      startCustomCompile() {
        let all_choice = this.$refs.compile_list.getCheckedAndIndeterminateNodes();
        let compile_list = []
        all_choice.forEach(item => {
          compile_list.push(item.title)
        })
        this.$Spin.show({
          render: h => {
            return h("div", [
              h("Icon", {
                class: "demo-spin-icon-load",
                props: {
                  type: "ios-loading",
                  size: 18
                }
              }),
              h("div", "调用自定义构建job...")
            ]);
          }
        });
        doCustomCompile(this.custom_job_name, compile_list)
          .then(res => {
            if (res.data.status === "success") {
              this.$Message.success(res.data.msg);
            } else {
              this.$Message.error(res.data.msg);
            }
            this.$Spin.hide();
            this.closeDrawer()
          })
          .catch(err => {
            this.$Spin.hide();
            this.closeDrawer()
            this.$Message.error(err.response.data.msg);
          });
      },
      closeDrawer() {
        this.drawer_compile = false
      },
      beginChangeSubmit(param) {
        /*开始变更提交*/
        doEnvBind(param.job_name, param.app_name, '')
          .then(res => {
            if (res.data.status === "success") {
              //this.$Message.success(res.data.msg);
              this.jenkinsCompileExec(param.job_name, param.need_mock, 1, 0);
            } else {
              this.$Message.error(res.data.msg);
            }
          })
          .catch(err => {
            this.$Message.error(err.response.data.msg);
          });
      },
      beginChangeSubmitAndContinuousIntegration(param) {
        /*开始变更提交+持续集成*/
        this.select_param = param;
        let envs = param.env_list;
        this.pipeline_id = param.pipeline_id;
        if (envs != null && envs.trim() !== '') {
          let env_list = envs.split(',');
          if (env_list.length > 1) {
            this.select_env_list = env_list;
            this.select_env = env_list[0];
            this.select_env_modal_show = true;
          } else {
            this.select_env = env_list[0];
            this.submitChangeSubmitAndContinuousIntegration();
          }
        } else {
          this.$Message.error('请先绑定环境套！');
        }
      },
      beginContinuousIntegration(param) {
        /*开始持续集成*/
         this.con_select_param = param;
        let envs = param.env_list;
        if (envs != null && envs.trim() !== '') {
          let env_list = envs.split(',');
          if (env_list.length > 1) {
            this.con_select_env_list = env_list;
            this.con_select_env = env_list[0];
            this.con_select_env_modal_show = true;
          } else {
            this.con_select_env = env_list[0];
            this.submitContinuousIntegration();
          }
        } else {
          this.$Message.error('请先绑定环境套！');
        }

      },
      jenkinsCompileExec(job_name, need_mock, skip, build_mock) {
        this.$Spin.show({
          render: h => {
            return h("div", [
              h("Icon", {
                class: "demo-spin-icon-load",
                props: {
                  type: "ios-loading",
                  size: 18
                }
              }),
              h("div", "调用构建job...")
            ]);
          }
        });
        doJenkinsCompileJob(job_name, skip)
          .then(res => {
            if (res.data.status === "success") {
              this.compile_data = res.data.data;
              this.modal_compile = true;
            } else {
              this.$Message.error(res.data.msg);
            }
            this.$Spin.hide();
          })
          .catch(err => {
            this.$Spin.hide();
            this.$Message.error(err.response.data.msg);
          });
      },

    },
    mounted() {
      this.init();
    },
    destroyed() {
      if (this.switch_pipeline_info) {
        clearInterval(this.switch_pipeline_info)
      }
    }
  };
</script>
