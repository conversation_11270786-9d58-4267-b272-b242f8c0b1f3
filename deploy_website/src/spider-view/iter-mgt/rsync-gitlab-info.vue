<template>
   <card>
     <row >
  <Button @click="syncGitlabMenber" style="margin-left: 8px; margin-top:5px">同步gitlab用户到平台</Button>
     </row>

     <!--<row >-->
   <!--<Button @click="getGitRopes('true')" style="margin-left: 8px; margin-top:5px">同步gitlab库到平台</Button>-->
     <!--</row>-->
   </card>
</template>


<script>

import { rsyncGitlabReposApi,********************* } from "@/spider-api/rsync-gitlab-info";

  export default {
    name: 'rsync-gitlab-info',
    data () {
      return {
      }
    },

    methods: {
          getGitRopes(sync_gitlab){
              this.$Spin.show({
                    render: (h) => {
                        return h('div', [
                            h('Icon', {
                                'class': 'demo-spin-icon-load',
                                props: {
                                    type: 'ios-loading',
                                    size: 18
                                }
                            }),
                            h('div', '仓库同步中请稍等。。。')
                        ])
                    }
                });
    let data = {"sync_gitlab":sync_gitlab};
    rsyncGitlabReposApi(data)
       .then(res => {
         alert(res.data["msg"])
          this.$Spin.hide()
         //this.data2 = res.data['repos_tree'];
       })
  },
      syncGitlabMenber(){
        this.$Spin.show({
                    render: (h) => {
                        return h('div', [
                            h('Icon', {
                                'class': 'demo-spin-icon-load',
                                props: {
                                    type: 'ios-loading',
                                    size: 18
                                }
                            }),
                            h('div', '用户同步中请稍等。。。')
                        ])
                    }
                });
        *********************()
          .then(res => {
            alert("用户同步完毕")
            this.$Spin.hide()
          })
      }
    }
    }
</script>
