<template>
    <Card shadow>
        <div>
            <div class="message-page-con message-category-con">
                <!--<p>应用名称</p>-->
                <Input
                    prefix="ios-search"
                    placeholder="应用名称"
                    v-model="match_name"
                    clearable
                    style="width:12em; margin-right: 1em"
                />
                <Button size="small" type="primary" @click="search_apps(match_name)">搜索</Button>
                <Scroll style="margin-top: 1em" :height="window_height">
                    <ul
                        class="ivu-menu ivu-menu-light ivu-menu-vertical"
                        v-for="item in app_list"
                        :app_list="app_list"
                        :key="item"
                    >
                        <li class="ivu-menu-item" @click="clickPanel(item)">{{ item }}</li>
                    </ul>
                </Scroll>
            </div>
            <div class="message-page-con message-view-con">
                <div>
                    <!--<i-col>-->
                    <!--<Tag>用户组: <span style="color: #2d8cf0">{{this.$store.state.user.role_role}}</span></Tag>-->
                    <!--<Tag>应用权限: <span style="color: #2d8cf0">{{this.$store.state.user.role_project}}</span></Tag>-->
                    <!--</i-col>-->
                    <i-col>
                        <!--<Tag><span style="color: #2d8cf0">仿真节点：蓝色</span></Tag>-->
                        <Tag><span style="color: #17233d">产线节点：黑色</span></Tag>
                        <Tag><span style="color: #19be6b">灾备节点：绿色</span></Tag>
                        <Tag><span style="color: #ff9900">灰度节点：黄色</span></Tag>
                    </i-col>
                    <!--<Divider>{{iteration_id}}</Divider>-->
                    <Divider>{{ app_name }}</Divider>
                    <i-col v-if="app_name">
                        <!--<Button style="margin-right: 1em;" size="small" ghost type="success" @click="show_prod_health">产线健康状态-->
                        <!--</Button>-->
                        <!-- <Button style="margin-right: 1em;" size="small" ghost type="success" @click="history">执行历史</Button> -->
                        <SpiderPublishHistory
                            ref="spider_publish_history_all"
                            :app_name_c="app_name"
                            :pipeline_id_p="iteration_id"
                            :showChange="true"
                            :curDataInfo="curDataInfo"
                            @changeHandler="changeHandler"
                            @endHandler="endHandler"
                        >
                        </SpiderPublishHistory>

                        <!--<i-col v-if="app_name" style="margin-top: 1em;">-->
                        <!--<span style="color:#515a6e; margin-right:3em;">选择环境</span>-->
                        <!--&lt;!&ndash; <span style="color:#515a6e; margin-right:3em;">选择机房</span> &ndash;&gt;-->
                        <!--<span style="color:#515a6e; margin-right:3em;">选择组</span>-->
                        <!--&lt;!&ndash;<span style="color:#515a6e; margin-left:12em;">批量操作</span>&ndash;&gt;-->
                        <!--</i-col>-->

                        <!--<i-col v-if="app_name" style="margin-top: 2px">-->
                        <!--<Select v-if="app_name" size="small" v-model="envSelect" style="width:6em; margin-right:1em;">-->
                        <!--<Option v-for="item in envList" :value="item.value" :key="item.value">{{ item.label }}</Option>-->
                        <!--</Select>-->
                        <!--&lt;!&ndash; <Select v-if="app_name" size="small" v-model="regionSelect" style="width:6em; margin-right:1em;"> &ndash;&gt;-->
                        <!--&lt;!&ndash; <Option v-for="item in regionList" :value="item.value" :key="item.value">{{ item.label }}</Option> &ndash;&gt;-->
                        <!--&lt;!&ndash; </Select> &ndash;&gt;-->
                        <!--<Select v-if="app_name" size="small" v-model="groupSelect" style="width:6em; margin-right:1em;">-->
                        <!--<Option v-for="item in groupList" :value="item.value" :key="item.value">{{ item.label }}</Option>-->
                        <!--</Select>-->
                        <!--<span>节点是否一致：</span>-->
                        <!--<span style="color: green" v-if="consistentNode">一致 </span>-->
                        <!--<span style="color: red" v-else v-bind:title="nodeInfo">不一致</span>-->
                        <!--<Button type="primary" shape="circle" icon="ios-refresh" size="small"-->
                        <!--@click="refreshNodeConsistent"></Button>-->
                        <!--<span style="color:#a59f9f">{{consistentTime}}</span>-->

                        <!--<i-col v-if="app_name" style="margin-top: 1em;">-->
                        <!--<Table :columns="columns" :data="data"></Table>-->
                        <!--</i-col>-->
                    </i-col>
                    <i-col v-show="app_name" style="margin-top: 1em;">
                        <PublishTable
                            :iteration_id="iteration_id"
                            :is_prod_refresh="is_prod_refresh"
                            @reloadStatus="reloadStatus"
                            ref="publish_table"
                        ></PublishTable>
                    </i-col>
                </div>
            </div>
        </div>

        <!--<Modal-->
        <!--v-model="ops_operate_modal"-->
        <!--width="680"-->
        <!--:mask-closable="false"-->
        <!--@on-cancel="cancel">-->
        <!--<p slot="header" style="color:gray;">-->
        <!--<Icon type="md-clipboard"></Icon>-->
        <!--<span> {{ app_name }} 执行历史 </span>-->
        <!--</p>-->
        <!--<div style="height:300px; overflow-y: auto;">-->
        <!--<table style="margin: 10px;" v-for="cont in historyCont" :key="cont.index">-->
        <!--<tr>-->
        <!--<td width="15px">-->
        <!--<Icon type="md-arrow-round-forward"></Icon>-->
        <!--</td>-->
        <!--<td width="100px" style="color: darkblue;">{{ cont.operator }}</td>-->
        <!--<td width="50px" style="color: black">{{ cont.type }}</td>-->
        <!--<td width="400px">{{ cont.operateTime }}</td>-->
        <!--</tr>-->
        <!--<tr>-->
        <!--<td width="15px"></td>-->
        <!--<td width="100px" style="border-bottom: #DDDDDD solid 2px; color: black;">{{ cont.ip }}</td>-->
        <!--<td width="450px" colspan="2" style="border-bottom: #DDDDDD solid 2px;" v-if="cont.detail !== 'error'"><span-->
        <!--v-html="cont.detail"></span></td>-->
        <!--<td width="450px" colspan="2" style="border-bottom: #DDDDDD solid 2px; color: red;" v-else><span-->
        <!--v-html="cont.detail"></span></td>-->
        <!--</tr>-->
        <!--</table>-->
        <!--</div>-->
        <!--<div slot="footer">-->
        <!--<Button @click="cancel">关闭</Button>-->
        <!--</div>-->
        <!--</Modal>-->
    </Card>
</template>

<script>
import store from '@/spider-store'
import { getNodeConsistentInfo } from '@/log/log'
import { formatDateHour } from '@/libs/util'
import { getPublishAppListApi, getCurRollInfo, postEndRollback } from '@/spider-api/prod-publish/publish-info'
import PublishTable from '@/spider-components/spider-table/server-table/publish-table'

import SpiderPublishHistory from '@/spider-components/spider-publish-history'

export default {
    name: 'PublishService',
    components: {
        PublishTable,
        SpiderPublishHistory
    },

    data() {
        return {
            iteration_id: '',
            consistentTime: '',
            match_name: '',
            is_prod_refresh: true,
            consistentNode: true,
            nodeInfo: '',
            window_height: 500,
            historyCont: [],
            switch_history: '',
            select_group: '',
            select_ip: '',
            last_deploy: '',

            modal_title: '',
            app_name: '',
            app_list: [],
            app_init_list: [],
            node_list: [],
            // regionSelect: 0,
            groupSelect: 0,
            envList: [
                {
                    value: 0,
                    label: '全部'
                }
            ],
            groupList: [
                {
                    value: 0,
                    label: '全部'
                }
            ],
            curDataInfo: {
                module_name: '',
                branch_name: '',
                lib_repo_version: ''
            }
        }
    },
    watch: {},
    methods: {
        endHandler() {
            postEndRollback({ module_name: this.app_name }).then(res => {
                console.log(res)
                if (res.data.code === '0000') {
                    this.$Message.success(res.data.message)
                    this.reloadStatus()
                } else {
                    this.$Message.error(res.data.message)
                }
            })
        },
        changeHandler() {
            this.$refs.publish_table.RollBackInfoDetail(
                this.curDataInfo.node_ip,
                this.curDataInfo.module_name,
                this.curDataInfo.suite_code,
                this.iteration_id,
                true
            )
        },
        search_apps(match_name) {
            console.log(match_name)
            let new_app_list = []
            for (let app_name of this.app_init_list) {
                if (app_name.indexOf(match_name) > -1) {
                    new_app_list.push(app_name)
                }
            }
            console.log(new_app_list)
            this.app_list = new_app_list
        },
        //打开日志查看tab
        renderFunc(ip, app_name) {
            let routeData = this.$router.resolve({
                path: '/log',
                query: {
                    ip: ip,
                    app_name: app_name
                }
            })
            window.open(routeData.href, '_blank')
        },

        // get_history_data() {
        //
        // },
        history() {
            this.$refs.spider_publish_history_all.ops_operate_modal = true
            this.$refs.spider_publish_history_all.app_name = this.app_name
            this.$refs.spider_publish_history_all.showHistory(this.app_name)
        },

        init_filter() {
            this.envList = [{ value: 0, label: '全部' }]
            // this.regionList = [{value: 0, label: "全部"}]
            this.groupList = [{ value: 0, label: '全部' }]
            this.envSelect = 0
            // this.regionSelect = 0
            this.groupSelect = 0
        },
        getCurRollbackInfo(module_name) {
            getCurRollInfo({ module_name }).then(res => {
                if (res.data.code === '0000') {
                    this.curDataInfo = res.data.data
                }
            })
        },
        clickPanel(val) {
            if (val.length) {
                this.app_name = val
                this.$refs.publish_table.getAppPublishInfo(this.app_name)
                //this.appGetInfo()
                // 根据点击的应用名称，获取应用的信息
                this.getCurRollbackInfo(this.app_name)
            }
        },
        reloadStatus() {
            // 重新获取当前回滚版本信息
            this.getCurRollbackInfo(this.app_name)
            // 刷新列表数据
            this.$refs.publish_table.getAppPublishInfo(this.app_name)
        },

        initThisVue() {
            getPublishAppListApi().then(res => {
                if (res.data.status === 'success') {
                    this.app_init_list = res.data.data.app_list
                    this.app_list = res.data.data.app_list
                    // 初始化的时候默认带入第一个应用
                    if (this.app_list.length > 0) {
                        this.app_name = this.app_list[0]
                        this.$refs.publish_table.getAppPublishInfo(this.app_name)
                        // 根据点击的应用名称，获取应用的信息
                        this.getCurRollbackInfo(this.app_name)
                    }
                }
            })
        }
    },
    beforeMount() {
        this.window_height = window.innerHeight - 220
    },
    mounted() {
        this.initThisVue()
    },
    destroyed() {}
}
</script>

<style scoped lang="less">
.message-page {
    &-con {
        // height: ~"calc(100vh - 176px)";
        min-height: 500em;
        display: inline-block;
        vertical-align: top;
        position: relative;

        &.message-category-con {
            border-right: 1px solid #e6e6e6;
            width: 18em;
            height: auto;
        }

        &.message-view-con {
            position: absolute;
            left: 21em;
            top: 1em;
            right: 1em;
            bottom: 2em;
            overflow: auto;
            padding: 1em 1em 0;

            .message-view-header {
                margin-bottom: 20px;

                .message-view-title {
                    display: inline-block;
                }

                .message-view-time {
                    margin-left: 20px;
                }
            }
        }
    }
}
</style>
