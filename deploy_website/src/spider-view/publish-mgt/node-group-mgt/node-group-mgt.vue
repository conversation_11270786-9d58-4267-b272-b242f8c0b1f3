<template>
  <Card shadow>
    <div>
      <div class="message-page-con message-category-con">
        <Input @keyup.enter.native="search_apps" prefix="ios-search" placeholder="应用名称" v-model="match_name"
               style="width:12em; margin-right: 1em"/>
        <Button size="small" type="primary" @click="search_apps(match_name)">搜索</Button>
        <Scroll style="margin-top: 1em" :height="window_height">
          <ul class="ivu-menu ivu-menu-light ivu-menu-vertical" v-for="item in app_list" :app_list="app_list" :key="item">
            <li class="ivu-menu-item" @click="clickPanel(item)">{{item}}</li>
          </ul>
        </Scroll>
      </div>
      <div class="message-page-con message-view-con">
        <i-col>
            <Tag><span style="color: #17233d">产线节点：黑色</span></Tag>
            <Tag><span style="color: #19be6b">灾备节点：绿色</span></Tag>
            <Tag><span style="color: #ff9900">灰度节点：黄色</span></Tag>
        </i-col>
        <div style="margin-bottom: 10px;">
          <Divider>{{app_name}} 组管理</Divider>
          <Button v-if="app_name" style="margin: 5px;" type="success" size="small" @click="show('add')">新建</Button>
          <Table style="margin-top: 10px; width: 320px;" :columns="group_columns" :data="app_groups"></Table>
        </div>
        <div>
          <Divider>{{app_name}} 节点分组</Divider>
          <Table style="margin-top: 10px; width: 580px;" :columns="columns" :data="data"></Table>
        </div>
      </div>
    </div>
    <Modal
      v-model="group_modal"
      @on-cancel="cancel"
      @on-ok="ok">
      <p slot="header">
        <span> {{ app_name }} {{ modal_title }} </span>
      </p>
      <div v-if="g_stat==='add'">
        <p style="font-size: 1rem; margin-left: 6rem; margin-right: 1rem; display:inline;"><span style="color: red"> * </span>组名</p>
        <Input style="width: auto" v-model="new_group_name" />
      </div>
      <div v-else-if="g_stat==='del'">
        <p style="font-size: 1rem; margin-left: 6rem; margin-right: 1rem;">是否删除<span style="color: red"> {{select_group}} </span>?</p>
      </div>
      <div v-if="g_stat==='set'">
        <p style="font-size: 1rem; margin-left: 4rem; display:inline;">组名<span style="color: red"> {{select_group}} </span>更名为</p>
        <Input style="width: auto; margin-left: 1rem;" v-model="new_group_name" />
      </div>
      <div v-if="g_stat==='init'">
        <p style="font-size: 1rem; margin-left: 4rem; display:inline;"> {{select_ip}} <span style="color: red"> 完成初始化 </span>?</p>
      </div>
    </Modal>
  </Card>
</template>

<script>

  import {
    getPublishAppListApi,
  } from '@/spider-api/prod-publish/publish-info'
  import {
    changeAppGroupNodeApi,
    getAppGroupNodeApi,
    getAppGroupApi,
    addAppGroupApi,
    setAppGroupApi,
    delAppGroupApi,
  } from '@/spider-api/app-group-mgt/app-group-mgt'

export default {
  name: 'NodeGroupMgt',
  components: {
    // PublishStat
  },
  data () {
    return {
      window_height: 500,
      new_group_name: '',
      select_group: '',
      match_name:"",
      select_ip: '',
      modal_title: '',
      group_modal: false,
      g_stat: '',
      appSearch: '',
      app_name: '',
      all_app_list: [],
      app_list: [],
      app_init_list: [],
      app_groups: [],
      app_groups_show: [],
      data: [],
      columns: [
        {
          title: '节点地址',
          width: 150,
          align: 'center',
          render: (h, params) => {
            var node_env_color = '#17233d'
            if (params.row.suite_name.indexOf("灾备") != -1) {
              node_env_color = '#19be6b'
            } else if (params.row.suite_name.indexOf("生产'") != -1) {
              node_env_color = '#17233d'
            }
            else if (params.row.suite_name.indexOf("灰度") != -1) {
              node_env_color = '#ff9900'
            }

            return h('div', [
              h('p', {
                style: {
                  display: 'inline',
                  color: node_env_color
                }
              }, params.row.ip),
            ])
          }
        },
        {
          title: '环境套',
          width: 120,
          align: 'center',
          key: 'suite_name'
        },
        {
          title: '组名',
          align: 'center',
          width: 150,
          render: (h, params) => {
            let op_list = [];
            let enable_button = false
            if (params.row.group === '默认分组' ) {
              enable_button = true
            }
            this.app_groups.forEach( item => {
              let vnode = h('Option', {
                props: {
                  value: item.name
                }
              });
              // if (item.name !== "默认分组") {
                // op_list.push(vnode)
              // }
              op_list.push(vnode)
            });

            return h('Select', {
              props: {
                placeholder: params.row.group,
                value: params.row.group,
                transfer: true,
                disabled: enable_button,
              },
              style: {
                width: '100px'
              },
              on: {
                'on-change': (val) => {
                  this.appChangeGroup(params.row.ip, val)
                }
              }
            }, op_list);
          }
        },
        {
          title: '状态',
          render: (h, params) => {
            let enable_button = true;
            let button_name = '已初始化';
            if (params.row.group === '默认分组') {
              enable_button = false
              button_name = '完成初始化'
            }
            return h('div', [
              h('Button', {
                attrs: {
                  class: 'ivu-btn ivu-btn-primary ivu-btn-small ivu-btn-ghost'
                },
                props: {
                  disabled: enable_button
                },
                style: {
                  marginRight: '5px'
                },
                on: {
                  click: () => {
                    this.select_ip = params.row.ip
                    this.show('init')
                  }
                }
              }, button_name),
            ]);
          }
        },
      ],
      group_columns: [
        {
          title: '组名',
          width: 120,
          align: 'center',
          key: 'name'
        },
        {
          title: '操作',
           align: 'center',
          render: (h, params) => {
            return h('div', [
              h('Button', {
                props: {
                  type: 'primary',
                  size: 'small',
                  disabled: false
                },
                style: {
                  marginRight: '5px'
                },
                on: {
                  click: () => {
                    this.select_group = params.row.name
                    this.show('set')
                  }
                }
              }, '编辑'),
              h('Button', {
                props: {
                  type: 'error',
                  size: 'small',
                  disabled: false
                },
                style: {
                  marginRight: '5px'
                },
                on: {
                  click: () => {
                    this.select_group = params.row.name
                    this.show('del')
                  }
                }
              }, '删除'),
            ]);
          }
        },
      ],
    }
  },
  watch: {
    'appSearch': function(){
      if ( this.appSearch ) {
        this.app_list = this.all_app_list.filter(item => item.indexOf(this.appSearch) > -1);
      } else {
        this.app_list = this.all_app_list
      }
    }
  },
  methods: {
    show (val) {
      if (val === 'add') {
        this.modal_title = '组新建'
        this.g_stat = val
      } else if (val === 'set') {
        this.modal_title = '组更名'
        this.g_stat = val
      } else if (val === 'del') {
        this.modal_title = '组删除'
        this.g_stat = val
      } else if (val === 'init') {
        this.modal_title = '完成初始化'
        this.g_stat = val
      }
      this.group_modal = true
    },
    init_modal (){
      this.new_group_name = ''
      this.select_group = ''
      this.select_ip = ''
      this.g_stat = ''
      this.group_modal = false
    },
    appAddGroup (){
      var data = {
        'app_name': this.app_name,
        'group_name': this.new_group_name
      }
      addAppGroupApi(data).then(res => {
        if (res.data.status === 'success') {
          this.$Message.success(res.data.msg);
        } else {
          this.$Message.error(res.data.msg);
        }
      })
    },
    appSetGroup (){
      var data = {
        'app_name': this.app_name,
        'cur_name': this.select_group,
        'new_name': this.new_group_name
      }
      setAppGroupApi(data).then(res => {
        if (res.data.status === 'success') {
          this.$Message.success(res.data.msg);
        } else {
          this.$Message.error(res.data.msg);
        }
      })
    },
    appDelGroup (){
      var data = {
        'app_name': this.app_name,
        'group_name': this.select_group
      }
      delAppGroupApi(data).then(res => {
        if (res.data.status === 'success') {
          this.$Message.success(res.data.msg);
        } else {
          this.$Message.error(res.data.msg);
        }
      })
    },

    ok () {
      if (this.g_stat === 'add') {
        this.appAddGroup()
      } else if (this.g_stat === 'set') {
        this.appSetGroup()
      } else if (this.g_stat === 'del') {
        this.appDelGroup()
      } else if (this.g_stat === 'init') {
        this.appChangeGroup(this.select_ip, 'ready')
      }
      let vm = this
      setTimeout(function(){vm.appGetInfo()}, 400)
      this.init_modal()
    },
    cancel () {
      this.init_modal()
    },
    appGetInfo () {
      var data = {'app_name': this.app_name}
      getAppGroupApi(data).then(res => {
        if (res.data.status === "success") {
          this.app_groups = res.data.data.groups
        }
      })
      getAppGroupNodeApi(data).then(res => {
        if (res.data.status === "success") {
          // this.app_groups = res.data.data.groups
          // this.app_groups_show = []
          // for (let n=0; n < this.app_groups.length; n++) {
          //   let name = this.app_groups[n].name
          //   if (name === "ready" || name === "默认分组") {
          //     console.log(this.app_groups[n])
          //   } else {
          //     this.app_groups_show.push(this.app_groups[n])
          //   }
          // }
          this.data = res.data.data.node_group_list
        } else {
          this.app_groups = []
          this.data = []
          this.$Message.error(res.data.msg);
        }
      }).catch(function (err){
        this.app_groups = []
        this.data = []
        vm.$Message.error('发生异常')
      })
    },
    clickPanel(val) {
      if (val.length) {
        this.app_name = val
        this.appGetInfo()
      }
    },
    appChangeGroup(ip, to_group) {
      var data = {
        'app_name': this.app_name,
        'ip': ip,
        'to_group':to_group
      }
      changeAppGroupNodeApi(data).then(res => {
        if (res.data.status === "success") {
          this.$Message.success(res.data.msg);
        } else {
          this.$Message.error(res.data.msg);
        }
      }).catch(function (err){
        vm.$Message.error('发生异常')
      })
      let vm = this
      setTimeout(function(){vm.appGetInfo()}, 300)
    },
    search_apps(match_name) {
         console.log(match_name)
          let new_app_list = []
          for (let app_name of this.app_init_list){
            if (app_name.indexOf(match_name)>-1){
              new_app_list.push(app_name)
            }
          }
          console.log(new_app_list)
          this.app_list = new_app_list
      },
    initThisVue(){
        getPublishAppListApi().then(res => {
        if (res.data.status === "success") {
          this.app_init_list= res.data.data.app_list
          this.app_list =  res.data.data.app_list
            // 初始化的时候默认带入第一个应用

          }
      })


      },
  },
  beforeMount () {
    this.window_height = window.innerHeight - 220
  },
  mounted () {
   this.initThisVue()
  },
  destroyed () {
  }
}
</script>

<style scoped lang="less">
.message-page{
  &-con{
    // height: ~"calc(100vh - 176px)";
    min-height: 500em;
    display: inline-block;
    vertical-align: top;
    position: relative;
    &.message-category-con{
      border-right: 1px solid #e6e6e6;
      width: 18em;
      height: auto;
    }
    &.message-view-con{
      position: absolute;
      left: 21em;
      top: 1em;
      right: 1em;
      bottom: 2em;
      overflow: auto;
      padding: 1em 1em 0;
      .message-view-header{
        margin-bottom: 20px;
        .message-view-title{
          display: inline-block;
        }
        .message-view-time{
          margin-left: 20px;
        }
      }
    }
  }
}
</style>
