<template>
  <Card shadow>
    <div>
      <div class="message-page-con message-category-con">
        <Input @keyup.enter.native="search_apps" prefix="ios-search" placeholder="应用名称或IP" v-model="appSearch"
               style="width:12em; margin-right: 1em"/>
        <Button size="small" type="primary" @click="search_apps">搜索</Button>
        <Scroll style="margin-top: 1em" :height="window_height">
          <ul class="ivu-menu ivu-menu-light ivu-menu-vertical" v-for="item in app_list" :app_list="app_list"
              :key="item">
            <li class="ivu-menu-item" @click="clickPanel(item)">{{item}}</li>
          </ul>
        </Scroll>
      </div>
      <div class="message-page-con message-view-con">
        <div>
          <i-col>
            <Tag>用户组: <span style="color: #2d8cf0">{{this.$store.state.user.role_role}}</span></Tag>
            <Tag>应用权限: <span style="color: #2d8cf0">{{this.$store.state.user.role_project}}</span></Tag>
          </i-col>
          <i-col>
            <Tag><span style="color: #2d8cf0">仿真节点：蓝色</span></Tag>
            <Tag><span style="color: #19be6b">灾备节点：绿色</span></Tag>
            <Tag><span style="color: #17233d">产线节点：黑色</span></Tag>
            <Tag><span style="color: #ff9900">灰度节点：黄色</span></Tag>
          </i-col>
          <Divider>{{app_name}}</Divider>
    
          <i-col v-if="app_name" style="margin-top: 1em;">
            <span style="color:#515a6e; margin-right:3em;">选择环境</span>
            <!-- <span style="color:#515a6e; margin-right:3em;">选择机房</span> -->
            <span style="color:#515a6e; margin-right:3em;">选择组</span>
            
          </i-col>
          <i-col v-if="app_name" style="margin-top: 2px">
            <Select v-if="app_name" size="small" v-model="envSelect" style="width:6em; margin-right:1em;">
              <Option v-for="item in envList" :value="item.value" :key="item.value">{{ item.label }}</Option>
            </Select>
            <!-- <Select v-if="app_name" size="small" v-model="regionSelect" style="width:6em; margin-right:1em;"> -->
            <!-- <Option v-for="item in regionList" :value="item.value" :key="item.value">{{ item.label }}</Option> -->
            <!-- </Select> -->
            <Select v-if="app_name" size="small" v-model="groupSelect" style="width:6em; margin-right:1em;">
              <Option v-for="item in groupList" :value="item.value" :key="item.value">{{ item.label }}</Option>
            </Select>
          </i-col>
          <publish_info_table ref="publish_info_data"  :app_name= "app_name"> </publish_info_table>
          
        </div>
      </div>
    </div>
    <maintenanceCmd ref = "maintenance_cmd_data"></maintenanceCmd>
  </Card>
</template>

<script>
  import maintenanceCmd  from "./components/maintenance-cmd";
  import publish_info_table from "./components/publish-info-table";
  import {
    getModuleName
  } from '@/spider-api/prod-publish/publish-info'

  export default {
    name: 'publish_cmd',
    components: {
      // PublishStat
      maintenanceCmd,
      publish_info_table
    },
    data() {
      return {
        consistentNode: true,
        nodeInfo:'',
        bat_deploy_btn: true,
        bat_restart_btn: true,
        bat_rollback_btn: true,
        bat_update_btn: true,
        bat_codeupdate_btn: true,
        ops_operate_modal: false,
        info_modal: false,
        window_height: 500,
        historyCont: [],
        switch_history: '',
        appSearch: '',
        app_name: '',
        all_app_list: [],
        app_list: [],
        data: [],
        app_data: [],
        envSelect: 0,
        // regionSelect: 0,
        groupSelect: 0,
        health_modal: false,
        
        health_data: [],
        envList: [
          {
            value: 0,
            label: "全部"
          },
        ],
        groupList: [
          {
            value: 0,
            label: "全部"
          },
        ],
        columns: [
          
        ],
      }
    },
    watch: {
      'envSelect': function () {
        this.filter_app_data()
      },
      'groupSelect': function () {
        this.filter_app_data()
      },
    },
    methods: {
      //打开日志查看tab
      
      //刷新节点是否一致状态
      search_apps() {
        let data = {'search': this.appSearch}
        getModuleName(this.appSearch).then(res => {  
        this.app_list = res.data.data 
        })
        // getProdAppData(data).then(res => {
        //   if (res.data.code === 0) {
        //     this.all_app_list = res.data.data
        //     this.app_list = this.all_app_list
        //   } else {
        //     this.$Message.error('获取应用列表失败');
        //   }
        // })
      },
      show() {
        this.info_modal = true
      },
      init_filter() {
        this.envList = [{value: 0, label: "全部"}]
        this.groupList = [{value: 0, label: "全部"}]
        this.envSelect = 0
        this.groupSelect = 0
      },
      filter_app_data() {
        let tmp_data = []
        let match_list = []

        if (this.envSelect) {
          match_list.push(['environ', this.envList[this.envSelect]['label']])
        }
        if (this.groupSelect) {
          match_list.push(['group', this.groupList[this.groupSelect]['label']])
        }

        for (let n = 0; n < this.app_data.length; n++) {
          let flag = true
          for (let m = 0; m < match_list.length; m++) {
            if (this.app_data[n][match_list[m][0]] !== match_list[m][1]) {
              flag = false
            }
          }
          if (flag) {
            tmp_data.push(this.app_data[n])
          }
        }

        this.data = tmp_data
      },
      append_filter_item(the_list, the_item) {
        let flag = true
        for (let n = 0; n < the_list.length; n++) {
          if (the_list[n]['label'] === the_item) {
            flag = false
          }
        }
        if (flag) {
          the_list.push({'value': the_list.length, 'label': the_item})
        }
      },
      clickPanel(val) {
        if (val.length) {
          this.app_name = val
          this.$refs.publish_info_data.getAppPublishInfo(this.app_name)
        }
      },
      closeWebSocket() {
        try {
          this.socket.close()
          this.socket = null
        } catch (error) {
        }
      },
    },
    beforeMount() {
      this.window_height = window.innerHeight - 220
    },
    mounted() {
      getModuleName().then(res => {  
        this.app_list = res.data.data 
        })
      this.$refs.publish_info_data.getAppPublishInfo()
    },
    destroyed() {
    }
  }
</script>

<style scoped lang="less">
  .message-page {
    &-con {
      // height: ~"calc(100vh - 176px)";
      min-height: 500;
      display: inline-block;
      vertical-align: top;
      position: relative;

      &.message-category-con {
        border-right: 1px solid #e6e6e6;
        width: 18em;
        height: auto;
      }

      &.message-view-con {
        position: absolute;
        left: 21em;
        top: 1em;
        right: 1em;
        bottom: 2em;
        overflow: auto;
        padding: 1em 1em 0;

        .message-view-header {
          margin-bottom: 20px;

          .message-view-title {
            display: inline-block;
          }

          .message-view-time {
            margin-left: 20px;
          }
        }
      }
    }
  }
</style>
