<template>
  <div>
    <i-col v-if="app_name" style="margin-top: 1em; width:552px;">
      <Table :columns="table_columns" :data="app_data"></Table>
    </i-col>
    <maintenanceCmd ref="maintenance_cmd_data"></maintenanceCmd>
  </div>

</template>

<script>
  import maintenanceCmd  from "./maintenance-cmd";
  import {
    getPublishAppBindApi, getPublishAppBindApiByRegionGroupList,
    getPublishStatusApi
  } from '@/spider-api/prod-publish/publish-info'
  export default {
    name: 'publish_info_table',
    components: {
      maintenanceCmd,
    },
    props: {

    },
        data() {
      return {
        app_name:'',
        git_version:'',
        last_deploy:'',
        app_data:[],
        data: [

],
        table_columns: [
          {
            title: '节点地址',
            width: 170,
            render: (h, params) => {
              var git_version_color = '#19be6b'
              if (params.row.is_git_latest === 1) {
                git_version_color = '#ff9900'
              } else if (params.row.is_git_latest === 2) {
                git_version_color = 'black'
              }

              var node_env_color = '#17233d'
              if (params.row.environ == '仿真环境') {
                node_env_color = '#2d8cf0'
              } else if (params.row.environ == '生产环境' && params.row.ip.indexOf('10.11.') > -1) {
                node_env_color = '#19be6b'
              }

              if (params.row.group == 'hd') {
                node_env_color = '#ff9900'
              }

              return h('div', [
                h('p', {
                  style: {
                    display: 'inline',
                    color: node_env_color
                  }
                }, params.row.node_ip),
                h('a', {
                  attrs: {
                    class: "ivu-icon ivu-icon-ios-information-circle-outline"
                  },
                  style: {
                    color: git_version_color,
                    "font-size": "18px",
                    'margin-left': '3px'
                  },
                  on: {
                    click: () => {
                      this.select_ip = params.row.node_ip
                      this.show()
                    }
                  },
                }, ''),
              ])
            }
          },
          {
            title: '环境套',
            width: 90,
            key: 'suite_code',
            tooltip: true
          },
          {
            title: '组名',
            width: 90,
            key: 'deploy_group_name',
            tooltip: false
          },
          {
            title: '操作',
            width: 200,
            render: (h, params) => {
              return h('div', [
                h('Button', {
                  attrs: {
                    title: 'salt维护',
                    type : 'dashed'
                  },

                  style: {
                    color: 'black',
                    "font-size": "12px",
                  },
                  on: {
                    click: () => {
                      this.$refs.maintenance_cmd_data.operateCommand = {}
                      this.$refs.maintenance_cmd_data.app_name = this.app_name
                      this.$refs.maintenance_cmd_data.select_ip = params.row.node_ip
                      this.$refs.maintenance_cmd_data.bind_id = params.row.bind_id
                      this.$refs.maintenance_cmd_data.get_operate_command(this.app_name, params.row.bind_id)
                      this.$refs.maintenance_cmd_data.command_modal = true
                    }
                  },
                }, 'salt维护'),

              ]);
            }
          },
        ],
      }},
      methods: {
        getAppPublishInfo(app_name) {
        let vm = this
        let region_group_list = ['prod','hd', 'beta', 'zb']
        this.app_name = app_name
        // 获取节点绑定信息
        getPublishAppBindApiByRegionGroupList(region_group_list, app_name).then(res => {
          console.log(res)
          if (res.data.status === "success") {
            vm.app_data = res.data.data
             let new_data = []
            // 获取发布状态信息组装
              }
          else {
            vm.app_data = []
            vm.$Message.error(res.data.msg);
          }
        }).catch(function (err) {
          console.log(err)
          vm.app_data = []
          vm.$Message.error('发生异常')
        })

      },

      },

  }

</script>
