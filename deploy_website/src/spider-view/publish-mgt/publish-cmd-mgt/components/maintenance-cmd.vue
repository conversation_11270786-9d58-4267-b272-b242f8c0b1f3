<template>
<div>
<Modal
      v-model="command_modal"
      width="46em"
      @on-cancel="cancel">
      <p slot="header">
        <span> {{ app_name }} {{ select_ip}} 命令预览 </span>
      </p>
      <Row>
        <Col span="16">
          <div>
            <p align="right" style="font-size: 1rem; width: 4em; display:inline-block;">minion_id:</p>
            <span
              style=" font-size: 1rem; padding-left: 2em; color: #2d8cf0;">{{minion_id}}</span>
          </div>
        </Col>
        <Col span="8">
          <div>
            <p align="right" style="font-size: 1rem; width: 4em; display:inline-block;">环境:</p>
            <span
              style=" font-size: 1rem; padding-left: 2em; color: #2d8cf0;">{{suite_code}}</span>
          </div>
        </Col>

      </Row>
      <ul>
        <li v-for="(item, index) in Object.keys(operateCommand)" :key="index">
          <div style="margin-top: 1em">
            <p align="right" style="font-size: 1em; width: 4em; display:inline-block;">{{operateCommand[item]['operate_cname']}}</p>
            <Input style="width: 8em; padding-left: 1em"  v-model="operateCommand[item]['salt_func']"/>
            <Input style="width: 32em; padding-left: 1em"  type='textarea' v-model="operateCommand[item]['exec_cmd']"/>
            <Button style="margin-left: 1em;" ghost type="primary" size="small" @click="save_cmd(item)">保存</Button>
            <p align="right" style="font-size: 1em; color: red; width: 6em; display:inline-block;" v-if="operateCommand[item]['is_default'] === '1'">请点击保存</p>
          </div>
        </li>
      </ul>
      <div slot="footer">
        <Button @click="cancel">关闭</Button>
      </div>
    </Modal>
    </div>
    </template>
    <script>

  import {
    saveOpsOperateCommand,
  } from '@/api/ops-service'
  import publishSaltInfoModel  from "./maintenance-cmd";
  import {
    publishExecSaltCmdApi,
    getPublishExecSaltCmdApi,
  } from '@/spider-api/prod-publish/publish-info'
  export default {
    name: "maintenanceCmd",

    computed: {},
    data() {
      return {
        select_ip: '',
        command_modal: false,
        last_salt_info:{},
        app_name: '',
        minion_id:'',
        suite_code:'',
        salt_id:'',
        all_app_list: [],
        data: [],
        operateCommand:{},
      }
    },
    methods: {
      save_cmd(opt_type) {
        this.operateCommand[opt_type]
        let data = {
          'bind_id':this.operateCommand[opt_type]['bind_id'],
          'salt_func':this.operateCommand[opt_type]['salt_func'],
          'exec_cmd':this.operateCommand[opt_type]['exec_cmd'],
          'salt_id' :this.operateCommand[opt_type]['id'],
          'last_salt_cmd' :this.last_salt_info[opt_type]['exec_cmd'],
          'operate_type':opt_type,
          'suite_code':this.operateCommand[opt_type]['suite_code'],
          'minion_id':this.operateCommand['code_update']['minion_id'],
          'app_name': this.operateCommand[opt_type]['app_name'],
        }
        publishExecSaltCmdApi(data).then(res => {
          if (res.data.status==='success') {
            this.$Message.success(res.data.msg)
            this.get_operate_command(data.app_name, data.bind_id)
          } else {
            this.$Message.error(res.data.msg)
          }
        })
      },
      init_modal() {
        this.command_modal = false
        if (this.switch_history) {
          clearInterval(this.switch_history)
        }
      },
      cancel() {
        this.init_modal()
      },
      get_operate_command(app_name, bind_id) {
        getPublishExecSaltCmdApi(app_name,  bind_id).then(res => {
          if (res.data.status==='success') {
            this.operateCommand = res.data.data
            for (let i in res.data.data)
            {
              this.last_salt_info = JSON.parse(JSON.stringify(res.data.data))
              //此处解决浅复制的问题
            }
            this.minion_id = this.operateCommand.code_update.minion_id
            this.salt_update_id = this.operateCommand.code_update.id
            this.suite_code = this.operateCommand.code_update.suite_code
          } else {
            this.operateCommand = {}
          }
        })
      },
    },
    created() {

    }
  }
</script>
