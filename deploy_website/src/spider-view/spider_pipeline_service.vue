<template>
    <div>
        <Tabs v-model="tab_name" ref="tabs" @on-click="changeTab">
            <TabPane label="分支管理" name="spider_branch_manager">
                <SpiderIterativeOverview ref="spiderIterativeOverview"></SpiderIterativeOverview>
            </TabPane>
            <TabPane label="变更提交与集成" name="spider_pipeline" :disabled="tab_disable">
                <SpiderPipeline ref="spiderPipeline"></SpiderPipeline>
            </TabPane>
            <TabPane label="持续交付" name="iter_publish_plan" :disabled="tab_disable">
                <IterPublishPlan ref="iterPublishPlan"></IterPublishPlan>
            </TabPane>
            <TabPane label="发布申请与完成" name="spider_iterative_plan" :disabled="tab_disable">
                <SpiderIterativePlan ref="spiderIterativePlan"></SpiderIterativePlan>
            </TabPane>
            <!--      <TabPane label="仿真发布" name="spider_uat_publish" :disabled="tab_disable">-->
            <!--        <SpiderUATPublish ref="spiderUATPublish"></SpiderUATPublish>-->
            <!--      </TabPane>-->
            <!--      <TabPane label="灰度发布" name="spider_hd_publish" :disabled="tab_disable">-->
            <!--        <SpiderHdPublish ref="spiderHdPublish"></SpiderHdPublish>-->
            <!--      </TabPane>-->
            <TabPane label="产线发布" name="prod_publish" :disabled="tab_disable">
                <ProdPublish v-if="showProdPublish" :is_prod_refresh="is_prod_refresh" ref="prodPublish"></ProdPublish>
            </TabPane>
        </Tabs>
    </div>
</template>

<script>
import SpiderIterativeOverview from '@/spider-components/spider-iterative-overview'
import SpiderIterativePlan from '@/spider-components/spider-iterative-plan'
import SpiderPipeline from '@/spider-components/spider-pipeline'
import SpiderTestPublish from '@/spider-components/spider-test-publish'
import SpiderUATPublish from '@/spider-components/spider-uat-publish'
import SpiderHdPublish from '@/spider-components/spider-hd-publish'
import SpiderProdPublish from '@/spider-components/spider-prod-publish'
import ProdPublish from '@/spider-view/ci-cd-mgt/server/server-publish/prod-publish'

import store from '@/spider-store'
import { iterConfirmStatusApi, iterPublishAllowApi } from '@/spider-api/publish'
import IterPublishPlan from '@/spider-components/spider-iter-publish-plan/iter-publish-plan.vue'

export default {
    name: 'spider_pipeline_service',

    created() {},
    data() {
        return {
            tab_name: 'spider_branch_manager',
            is_prod_refresh: false,
            showProdPublish: false
        }
    },
    components: {
        IterPublishPlan,
        SpiderIterativeOverview,
        SpiderPipeline,
        SpiderTestPublish,
        SpiderUATPublish,
        SpiderProdPublish,
        SpiderHdPublish,
        SpiderIterativePlan,
        ProdPublish
    },
    methods: {
        changeTab(tab_name) {
            if (tab_name === 'iter_publish_plan') {
                this.$refs.iterPublishPlan.initThisVue()
                this.$refs.iterPublishPlan.initRequest()
            } else if (tab_name === 'spider_iterative_plan') {
                this.$refs.spiderIterativePlan.initThisVue()
                this.$refs.spiderPipeline.closeJenkinsStatus()
            } else if (tab_name === 'spider_pipeline') {
                this.$refs.spiderPipeline.initThisVue()
            } else if (tab_name === 'spider_test_publish') {
                this.$refs.spiderTestPublish.initThisVue()
                this.$refs.spiderPipeline.closeJenkinsStatus()
            } else if (tab_name === 'spider_uat_publish') {
                this.$refs.spiderUATPublish.initThisVue()
                this.$refs.spiderPipeline.closeJenkinsStatus()
            } else if (tab_name === 'spider_hd_publish') {
                this.$refs.spiderHdPublish.initThisVue()
                this.$refs.spiderPipeline.closeJenkinsStatus()
            } else if (tab_name === 'spider_prod_publish') {
                iterConfirmStatusApi(store.state.iterationID).then(res => {
                    if (res.data.status === 'success') {
                        iterPublishAllowApi(store.state.iterationID).then(res => {
                            if (res.data.status === 'success') {
                                this.$refs.spiderProdPublish.initThisVue()
                            } else {
                                alert(res.data.msg)
                                this.changeTab('spider_branch_manager')
                            }
                            this.$refs.spiderPipeline.closeJenkinsStatus()
                        })
                    } else {
                        alert(res.data.msg)
                        this.changeTab('spider_branch_manager')

                        this.$refs.spiderPipeline.closeJenkinsStatus()
                    }
                })
            } else if (tab_name === 'prod_publish') {
                // this.showProdPublish先设置为false，再设置为true，是为了解决切换tab时，prod页面不刷新的问题
                // this.showProdPublish = false
                // this.showProdPublish = true
                this.showProdPublish = false
                this.$nextTick(() => {
                    this.showProdPublish = true
                })
                iterConfirmStatusApi(store.state.iterationID).then(res => {
                    if (res.data.status === 'success') {
                        iterPublishAllowApi(store.state.iterationID).then(res => {
                            if (res.data.status === 'success') {
                                this.is_prod_refresh = true
                                this.$refs.prodPublish.initThisVue()
                            } else {
                                alert(res.data.msg)
                                this.changeTab('spider_branch_manager')
                            }
                            this.$refs.spiderPipeline.closeJenkinsStatus()
                        })
                    } else {
                        alert(res.data.msg)
                        this.changeTab('spider_branch_manager')

                        this.$refs.spiderPipeline.closeJenkinsStatus()
                    }
                })
            }
            //是否在prod页面
            if (tab_name != 'prod_publish') {
                this.is_prod_refresh = false
            }

            this.tab_name = tab_name
        }
    },
    watch: {},
    computed: {
        tab_disable() {
            if (store.state.iterationID == '' || store.state.iterationID == undefined) {
                return true
            } else {
                return false
            }
        }
    }
}
</script>

<style lang="less"></style>
