<style scoped>
.card {
  padding-top: 10px;
  padding-left: 10px
}

.formItem {
  width: 50%;
}

.formButton {
  width: 50%;
  display: flex;
  justify-content: flex-end
}

.pag {
  display: flex;
  justify-content: center;
  /* margin-top: 24px; */
}

/* .env_application /deep/ .ivu-input-group-prepend, .ivu-input-group-append{
    background-color: #2d8cf0;
    color: #ffffff;
}
.env_application /deep/ .ivu-input-group-prepend, .ivu-input-group-append .ivu-select, .ivu-select-arrow{
    color: #ffffff;
}
.env_application /deep/ .ivu-input-group-prepend .ivu-select{
    color: #ffffff;
} */
</style>
<style lang='less'>
.ivu-modal-header {
  text-align: center;
}
</style>
<template>
  <div class="env_application">
    <Card class="card">
      <h2>测试环境申请</h2>
      <div class="header" style="margin-top: 10px;">
        <div>
          <a href="https://www.processon.com/view/link/5fab3daf6376893d444d80eb" target="abc">配置替换规则说明
            <Icon type="ios-alert-outline" size="18"/>
          </a>&nbsp;&nbsp;&nbsp;&nbsp;
          <a
            href="http://paas.hongkou.howbuy.com/report/#/notebook/2FQR8B4T3/paragraph/20201119-142029_72964243?asIframe"
            target="efg">替换结果验证报表
            <Icon type="ios-alert-outline" size="18"/>
          </a>
        </div>
      </div>
      <Divider/>
      <Form ref="form" :model="form" :rules="rule" :label-width="120">
        <Row>
          <i-col span="12">
            <FormItem label="申请人" prop="applicant" class="formItem">
              <Input v-model="form.applicant" placeholder="输入测试环境申请人" disabled></Input>
            </FormItem>
          </i-col>
          <i-col span="12">
            <FormItem label="截止日期" prop="invalid_at" class="formItem">
              <DatePicker type="datetime" :options="dateOption" placeholder="选择环境使用截止日期"
                          v-model="form.invalid_at"></DatePicker>
            </FormItem>
          </i-col>
        </Row>
        <Row>
          <i-col span="12">
            <FormItem label="测试环境" prop="env_id" class="formItem">
              <Input v-model="form.env_value" placeholder="选择要申请的测试环境" disabled>
                <Button slot="append" icon="ios-search" @click="searchEnv()">
                </Button>
              </Input>
            </FormItem>
          </i-col>
          <i-col span="12">
            <FormItem label="测试环境用途" prop="apply_reason" class="formItem">
              <Input v-model="form.apply_reason" type="textarea" placeholder="输入测试环境用途"></Input>
            </FormItem>
          </i-col>
        </Row>
        <div
          style="border-width: 1px;border-color: black;border-style: solid;background-color: #f3f3f3;margin-left: 30px;margin-right: 30px;">
          <Row>
            <i-col span="22">
              <FormItem prop="template_name" label="模板名">
                <RadioGroup v-model="form.template_id" @on-change="template_select_change">
                  <Radio v-for="item in template_item_list" :label="item.id">
                    {{ item.template_name }}
                  </Radio>
                </RadioGroup>
              </FormItem>
            </i-col>
            <i-col span="2">
              <i-button :icon="collepseAll? 'ios-arrow-up':'ios-arrow-down'" type="text"
                        @click="allHandleToggleCollepse()">{{ collepseAll ? '收起' : '展开' }}
              </i-button>
            </i-col>
          </Row>
          <div
            style="border-width: medium;border-color: black;border-style: dotted; margin-right: 20px; margin-left: 20px; margin-bottom:20px; margin-top: 10px;background-color: white;"
            v-show="collepseAll">
            <FormItem prop="pipeline_list" label="数据库初始化">
              <RadioGroup v-model="form.init_db">
                <Radio label="it300">it300</Radio>
                <Radio label="tp_basic">tp_basic</Radio>
                <Radio label="None">None</Radio>
              </RadioGroup>
            </FormItem>
            <FormItem prop="pipeline_list" label="新数据库初始化">
              <Select v-model="form.bis_pipeline_id" filterable clearable size="small" @on-change="getBaseDbInfo"
                      style="width: 300px">
                <Option
                  v-for="item in biz_br_name_list"
                  :value="item.value"
                  :key="item.value"
                >{{ item.label }}
                </Option>
              </Select>
              <span style="text-align: left; display: inline-block;margin-left: 10px">业务基础库集: {{
                  form.biz_base_db_code
                }}</span>
            </FormItem>
            <FormItem prop="pipeline_list" label="是否初始化CCMS">
              <RadioGroup v-model="form.ccms_type">
                <Radio label="1">是</Radio>
                <Radio label="0">否</Radio>
              </RadioGroup>
            </FormItem>
            <FormItem prop="docker_list" label="及时雨">
              <CheckboxGroup v-model="form.h5_list" @on-change="get_need_mock_app_list">
                <Checkbox v-for="h5_item in h5_item_list" :label="h5_item" :key="h5_item">
                  {{ h5_item }}
                </Checkbox>
              </CheckboxGroup>
            </FormItem>
            <FormItem prop="vm_list" label="达摩院">
              <CheckboxGroup v-model="form.tms_list" @on-change="get_need_mock_app_list">
                <Checkbox v-for="tms_item in tms_item_list" :label="tms_item" :key="tms_item">
                  {{ tms_item }}
                </Checkbox>
              </CheckboxGroup>
            </FormItem>
            <FormItem prop="vm_list" label="藏经阁">
              <CheckboxGroup v-model="form.tp_list" @on-change="get_need_mock_app_list">
                <Checkbox v-for="tp_item in tp_item_list" :label="tp_item" :key="tp_item">
                  {{ tp_item }}
                </Checkbox>
              </CheckboxGroup>
            </FormItem>
            <FormItem prop="vm_list" label="宙斯盾">
              <CheckboxGroup v-model="form.pa_list" @on-change="get_need_mock_app_list">
                <Checkbox v-for="pa_item in pa_item_list" :label="pa_item" :key="pa_item">
                  {{ pa_item }}
                </Checkbox>
              </CheckboxGroup>
            </FormItem>
            <FormItem prop="ta_list" label="罗刹堂">
              <CheckboxGroup v-model="form.app_ops_list" @on-change="get_need_mock_app_list">
                <Checkbox v-for="app_ops_item in app_ops_item_list" :label="app_ops_item" :key="app_ops_item">
                  {{ app_ops_item }}
                </Checkbox>
              </CheckboxGroup>
            </FormItem>
            <FormItem prop="ta_list" label="爱玛士">
              <CheckboxGroup v-model="form.fpc_list" @on-change="get_need_mock_app_list">
                <Checkbox v-for="fpc_item in fpc_item_list" :label="fpc_item" :key="fpc_item">
                  {{ fpc_item }}
                </Checkbox>
              </CheckboxGroup>
            </FormItem>
            <FormItem prop="ta_list" label="六扇门">
              <CheckboxGroup v-model="form.crm_list" @on-change="get_need_mock_app_list">
                <Checkbox v-for="crm_item in crm_item_list" :label="crm_item" :key="crm_item">
                  {{ crm_item }}
                </Checkbox>
              </CheckboxGroup>
            </FormItem>
            <FormItem prop="ta_list" label="其他">
              <CheckboxGroup v-model="form.other_list" @on-change="get_need_mock_app_list">
                <Checkbox v-for="other_item in other_item_list" :label="other_item" :key="other_item">
                  {{ other_item }}
                </Checkbox>
              </CheckboxGroup>
            </FormItem>
            <Row>
              <i-col span="12">
                <FormItem label="设置环境时间" class="formItem">
                  <DatePicker type="datetime" placeholder="选择环境使用截止日期"
                              v-model="form.use_env_time"></DatePicker>
                </FormItem>
              </i-col>
              <i-col span="12">
                <FormItem label="清理缓存" class="formItem">
                  <RadioGroup v-model="form.clean_cache">
                    <Radio label="1">是</Radio>
                    <Radio label="0">否</Radio>
                  </RadioGroup>
                </FormItem>
              </i-col>
            </Row>
            <Row>
              <i-col span="24">
                <FormItem label="需要mock的应用列表" class="formItem">
                  <Select
                    placeholder="应用列表"
                    style="margin-top: 5px"
                    v-model="form.need_mock_app_list"
                    filterable
                    multiple
                  >
                    <Option v-for="item in mock_app_list"
                            :value="item"
                            :label="item"
                            :key="item">
                      {{ item }}
                    </Option>
                  </Select>
                </FormItem>
              </i-col>
            </Row>
            <Row>
              <i-col span="24">
                <FormItem label="邮件列表" class="formItem">
                  <Select
                    placeholder="邮箱地址"
                    style="margin-top: 5px"
                    v-model="form.allSelectedMails"
                    filterable
                    multiple
                  >
                    <Option v-for="item in allFilterMails"
                            :value="item"
                            :label="item"
                            :key="item">
                      {{ item }}
                    </Option>
                  </Select>
                </FormItem>
              </i-col>
            </Row>
            <Row v-show="test_id_show">
              <i-col span="12">
                <FormItem label="测试集ID" class="formItem">
                  <Input v-model="form.test_set_id" placeholder="输入测试集ID"></Input>
                </FormItem>
              </i-col>
            </Row>
            <Row style="margin-bottom: 10px">
              <i-col span="2" offset="21">
                <Button @click="save_modal_show=true">保存为模板</Button>
              </i-col>
            </Row>
          </div>

        </div>

        <FormItem class="formButton">
          <Button @click="handleReset('form')">重置申请</Button>
          <Button type="primary" style="margin-left: 8px" @click="handleSubmit('form')">确认申请</Button>
          <Button type="primary" style="margin-left: 8px" @click="updateSubmit('form')">环境更新</Button>
        </FormItem>
      </Form>
      <Modal
        title="测试环境使用情况"
        v-model="modal.show"
        width="1080"
        :mask-closable="true"
      >
        <Table :loading="tableLoading"
               :columns="tableColumns"
               :data="tableData"
               :no-data-text="noDataText"
        >
        </Table>
        <div slot="footer">
          <Page class="pag"
                :total="pagination.total"
                :current="pagination.page"
                :page-size="pagination.size"
                show-total
                show-elevator
                show-sizer
                @on-change="pageChange"
                @on-page-size-change="pageSizeChange"
          />
        </div>
      </Modal>
      <Modal
        title="保存模板"
        width="400"
        v-model="save_modal_show"
        :mask-closable="true"
        @on-ok="save_template">
        <Form>
          <Row>
            <i-col span="24">
              <FormItem label="新模板名称">
                <Input v-model="form.new_template_name" placeholder="输入新模板名称"></Input>
              </FormItem>
            </i-col>
          </Row>
          <Row>
            <i-col span="24">
              <FormItem label="替换模板名称">
                <Select
                  placeholder="选择替换模板"
                  style="margin-top: 5px"
                  v-model="form.change_template_id"
                  clearable
                >
                  <Option v-for="item in template_item_list"
                          :value="item.id"
                          :key="item.id">
                    {{ item.template_name }}
                  </Option>
                </Select>
              </FormItem>
            </i-col>
          </Row>
        </Form>
      </Modal>
      <Modal
        title="保存模板"
        width="400"
        v-model="jenkins_modal_show"
        :mask-closable="true"
        ok-text="查看详情"
        @on-ok="jenkins_info">
        <div>
          {{ jenkins_msg }}
        </div>
      </Modal>
    </Card>
  </div>
</template>
<script>
import {
  getAllEnvInfo,
  getTemplateInfo,
  getAppsByTemplate,
  updateTestEnvApplyInfo,
  updateTemplateInfo,
  callJenkins,
  getJenkinsLog,
  callUpdateJenkins,
  getManualTestingEnvNotApply
} from '@/api/test-env'
import { getEmailAddresses } from '@/spider-api/iter-plan'
import time from '@/mixin/time'
import isEmpty from '@/mixin/isEmpty'
import { getSuiteBindApp, get_need_mock_app_list_by_env, getJavaAppList } from '@/spider-api/env-init'
import { get_base_db_bind, get_test_iter_list } from '@/spider-api/biz-mgt'

export default {
  mixins: [time, isEmpty],
  data () {
    return {
      save_modal_show: false,
      jenkins_modal_show: false,
      jenkins_msg: '',
      jenkins_url: '',
      test_id_show: false,
      collepseAll: false,
      pagination: {
        page: 1,
        size: 10,
        total: 0
      },
      form: {
        template_id: 1,
        applicant: this.$store.state.user.userName,
        new_template_name: '',
        change_template_id: null,
        env_id: null,
        env_value: '',
        apply_reason: null,
        invalid_at: null,
        clean_cache: '1',
        test_set_id: '',
        init_db: 'None',
        biz_base_db_code: '',
        bis_pipeline_id: '',
        ccms_type: '1',
        use_env_time: null,
        template_name: '',
        need_mock_app_list: [],
        allSelectedMails: [],
        h5_list: [],
        tms_list: [],
        tp_list: [],
        pa_list: [],
        app_ops_list: [],
        fpc_list: [],
        crm_list: [],
        other_list: []
      },
      rule: {
        applicant: [
          { required: true, message: '申请人不能为空', trigger: 'blur' }
        ],
        env_id: [
          { type: 'number', required: true, message: '测试环境ID不能为空', trigger: 'blur' }
        ],
        apply_reason: [
          { required: true, message: '测试环境用途不能为空', trigger: 'blur' },
          { max: 100, message: '用途描述不能超过100个字节', trigger: 'blur' }
        ],
        invalid_at: [
          { type: 'date', required: true, message: '环境失效时间不能为空', trigger: 'change' }
        ]
      },
      dateOption: {
        disabledDate (date) {
          return date && date.valueOf() < Date.now() - 86400000
        }
      },
      modal: {
        show: false,
        title: ''
      },
      noDataText: '无可用环境，可去环境信息页面回收',
      tableLoading: false,
      tableData: [],
      tableColumns: [
        {
          title: '环境套名',
          key: 'suite_code',
          align: 'center'
        },
        // 虚机测试环境全部下线，该列不显示 20220906 by fwm
        // {
        //   title: '虚拟机',
        //   key: 'node_ip',
        //   align: 'center',
        // },
        {
          title: '容器',
          key: 'node_docker',
          align: 'center'
        },
        {
          title: '申请人',
          key: 'apply_user',
          align: 'center'
        },
        {
          title: '用途',
          key: 'apply_reason',
          align: 'center'
        },
        {
          title: '截止日期',
          key: 'invalid_at',
          align: 'center',
          width: 180
        },
        {
          title: '操作',
          align: 'center',
          fixed: 'right',
          render: (h, params) => {
            if (params.row.apply_user == null || params.row.apply_user === this.$store.state.user.userName) {
              if (params.row.suite_code.indexOf('tms') > -1) {
                return h('Button', {
                  props: {
                    type: 'primary',
                    size: 'small',
                    disabled: true
                  },
                  on: {
                    click: () => {
                      this.selectEnv(params)
                    }
                  }
                }, '选择申请')
              } else {
                return h('Button', {
                  props: {
                    type: 'primary',
                    size: 'small'
                  },
                  on: {
                    click: () => {
                      this.selectEnv(params)
                    }
                  }
                }, '选择申请')
              }
            }
          }
        }
      ],

      // 添加新的页面元素
      template_item_list: [],
      app_list: [],
      h5_item_list: [],
      tms_item_list: [],
      tp_item_list: [],
      pa_item_list: [],
      app_ops_item_list: [],
      fpc_item_list: [],
      crm_item_list: [],
      other_item_list: [],
      allFilterMails: [],
      mock_app_list: [],
      biz_br_name_list: []
    }
  },
  mounted () {
    this.initThisVue()
  },
  methods: {
    // 展开收起
    allHandleToggleCollepse () {
      this.collepseAll = !this.collepseAll
    },
    setPagParams (page, size, total) {
      /**
       * 设置分页参数
       */
      if (!this.isEmpty(page)) {
        this.pagination.page = page
      }
      if (!this.isEmpty(size)) {
        this.pagination.size = size
      }
      if (!this.isEmpty(total)) {
        this.pagination.total = total
      }
    },
    initThisVue () {
      getEmailAddresses().then(res => {
        this.allFilterMails = res.data.data
      })
      getJavaAppList().then(res => {
        this.mock_app_list = res.data.data
      })
      get_test_iter_list().then(res => {
        let data_list = res.data.data
        if (data_list) {
          this.biz_br_name_obj_list = data_list
          // console.log("this.biz_br_name_obj_list====" + JSON.stringify(this.biz_br_name_obj_list))
          this.biz_br_name_list = data_list.map((item) => {
            // return {
            //   value: item.biz_test_iter_id,
            //   label: item.biz_test_iter_br
            // }
            return {
              value: item.biz_test_iter_id,
              label: item.biz_test_iter_id
            }
          })
        } else {
          this.$Message.error('业务分支获取失败！')
        }
      }).catch(err => {
        console.log(err)
      })
    },
    getBaseDbInfo () {
      let biz_code = ''
      for (let bizObj in this.biz_br_name_obj_list) {
        if (this.biz_br_name_obj_list[bizObj].biz_test_iter_id === this.form.bis_pipeline_id) {
          biz_code = this.biz_br_name_obj_list[bizObj].biz_code
          // console.log("biz_code====" + biz_code)
          get_base_db_bind(biz_code).then(res => {
            if (res.data.status === 'success') {
              if (res.data.data) {
                this.form.biz_base_db_code = res.data.data['biz_base_db_code']
              } else {
                this.form.biz_base_db_code = 0
              }
            }
          })
        }
      }
    },
    getEnvData (data) {
      let vm = this
      vm.tableLoading = true
      getManualTestingEnvNotApply(data)
        .then(function (response) {
          vm.setPagParams('', '', response.data.data.count)
          vm.tableData = vm.dataSerializer(response.data.data.results)
          vm.tableLoading = false
          vm.showModal()
        })
        .catch(function (error) {
          vm.tableLoading = false
          vm.$Message.error('获取环境使用情况信息失败')
          console.log(error)
        })
    },
    searchEnv () {
      let data = {
        page: this.pagination.page,
        size: this.pagination.size,
        type_name: 1
      }
      this.getEnvData(data)
    },
    selectEnv (params) {
      this.form.env_id = params.row.suite_id
      this.form.env_value = params.row.suite_code
      this.modal.show = false
      this.init_Template()
      this.init_app_checkbox(params.row.suite_id)
    },
    showModal () {
      this.modal.show = true
    },
    pageChange (page) {
      /**
       * 分页中改变size触发的函数
       */
      this.setPagParams(page, '', '')
      let data = {
        page: this.pagination.page,
        size: this.pagination.size,
        type_name: 1
      }
      this.getEnvData(data)
    },
    pageSizeChange (size) {
      this.setPagParams('', size, '')
      let data = {
        page: 1,
        size: this.pagination.size,
        type_name: 1
      }
      this.getEnvData(data)
    },
    // 确认申请
    handleSubmit (name) {
      let vm = this
      this.$refs[name].validate((valid) => {
        if (valid) {
          // 调用更新环境申请记录信息
          updateTestEnvApplyInfo(this.form).then(res => {
            console.log('更新环境申请记录信息成功')
          }).catch(function (error) {
            vm.$Message.error('更新环境申请记录信息失败')
            console.log(error)
          })
          // 调用jenkins
          callJenkins(this.form).then(res => {
            vm.jenkins_url = res.data.data.jenkins_url
            vm.jenkins_msg = res.data.msg
            vm.jenkins_modal_show = true
          }).catch(function (error) {
            vm.$Message.error('调用jenkins失败')
            console.log(error)
          })
        } else {
          vm.$Message.error('测试环境申请表单提交失败')
        }
      })
    },

    updateSubmit (name) {
      let vm = this
      this.$refs[name].validate((valid) => {
        if (valid) {
          // 调用jenkins
          callUpdateJenkins(this.form).then(res => {
            vm.jenkins_url = res.data.data.jenkins_url
            vm.jenkins_msg = res.data.msg
            vm.jenkins_modal_show = true
          }).catch(function (error) {
            vm.$Message.error('调用jenkins失败')
            console.log(error)
          })
        } else {
          vm.$Message.error('测试环境申请表单提交失败')
        }
      })
    },

    // 重置申请
    handleReset (name) {
      this.$refs[name].resetFields()
    },
    dataSerializer (data) {
      let vm = this
      if (data == null || data.length === 0) {
        return []
      } else {
        data.forEach(element => {
          if (!vm.isEmpty(element.invalid_at)) {
            element.invalid_at = vm.UtcDateToLocalDate(new Date(element.invalid_at))
          }
        })
        return data.filter(function (val) {
          return val.apply_user == null
        })
      }
    },
    // 「模块」改变
    template_select_change () {
      if (this.form.template_id !== null && this.form.template_id !== undefined && this.form.template_id !== '') {
        let vm = this
        this.form.template_name = this.template_item_list.filter(function (val) {
          return val.id == vm.form.template_id
        })[0].template_name
        let data = {
          'template_id': this.form.template_id
        }
        getAppsByTemplate(data).then(res => {
          this.app_list = res.data.data.app_list
          this.form.clean_cache = res.data.data.template_info.clean_cache
          this.form.test_set_id = res.data.data.template_info.test_set_id
          this.form.init_db = res.data.data.template_info.init_db
          let ccms_type = res.data.data.template_info.ccms_type != null ? res.data.data.template_info.ccms_type.toString() : '1'
          this.form.ccms_type = ccms_type
          this.form.use_env_time = res.data.data.template_info.use_env_time
          if (res.data.data.template_info.all_selected_mails != null) {
            this.form.allSelectedMails = res.data.data.template_info.all_selected_mails.split(',')
          } else {
            this.form.allSelectedMails = []
          }
          this.form.h5_list = res.data.data.app_list.filter(function (val) {
            return vm.h5_item_list.indexOf(val) > -1
          })
          this.form.tms_list = res.data.data.app_list.filter(function (val) {
            return vm.tms_item_list.indexOf(val) > -1
          })
          this.form.tp_list = res.data.data.app_list.filter(function (val) {
            return vm.tp_item_list.indexOf(val) > -1
          })
          this.form.pa_list = res.data.data.app_list.filter(function (val) {
            return vm.pa_item_list.indexOf(val) > -1
          })
          this.form.app_ops_list = res.data.data.app_list.filter(function (val) {
            return vm.app_ops_item_list.indexOf(val) > -1
          })
          this.form.fpc_list = res.data.data.app_list.filter(function (val) {
            return vm.fpc_item_list.indexOf(val) > -1
          })
          this.form.crm_list = res.data.data.app_list.filter(function (val) {
            return vm.crm_item_list.indexOf(val) > -1
          })
          this.form.other_list = res.data.data.app_list.filter(function (val) {
            return vm.other_item_list.indexOf(val) > -1
          })
          this.get_need_mock_app_list()
        })
      } else {
        this.form.pipeline_list = []
        this.form.h5_list = []
        this.form.tms_list = []
        this.form.tp_list = []
        this.form.pa_list = []
        this.form.app_ops_list = []
        this.form.fpc_list = []
        this.form.crm_list = []
        this.form.other_list = []
      }
    },
    // 初始化加载模板信息
    init_Template () {
      getTemplateInfo().then(res => {
        this.template_item_list = res.data.data['data_list']
      })
      // this.init_template_name_default()
    },
    // 初始化加载复选框可选应用信息
    init_app_checkbox (suite_id) {
      getSuiteBindApp({ 'suite_id': suite_id }).then(res => {
        this.h5_item_list = res.data.data['h5_list']
        this.tms_item_list = res.data.data['tms_list']
        this.tp_item_list = res.data.data['tp_list']
        this.pa_item_list = res.data.data['pa_list']
        this.app_ops_item_list = res.data.data['app_ops_list']
        this.fpc_item_list = res.data.data['fpc_list']
        this.crm_item_list = res.data.data['crm_list']
        this.other_item_list = res.data.data['other_list']
        this.template_select_change()
      })
    },
    // 动态设置need_mock_app_list
    get_need_mock_app_list () {
      get_need_mock_app_list_by_env(this.form).then(res => {
        console.log('获取需要mock的app列表')
        this.form.need_mock_app_list = res.data.data
      }).catch(function (error) {
        this.$Message.error('获取测试环境下的mock应用列表失败')
      })
    },
    // 保存模板
    save_template () {
      if ((this.form.new_template_name == null || this.form.new_template_name === '') && (this.form.change_template_id == null || this.form.change_template_id === '')) {
        this.$Message.error('请至少填写新模板名或选择要替换模板')
      } else {
        // 调用更新模板详情
        updateTemplateInfo(this.form).then(res => {
          this.$Message.success('保存模板成功')
          this.init_Template()
          this.init_app_checkbox(params.row.suite_id)
        }).catch(function (error) {
          this.$Message.error('保存模板失败')
          console.log(error)
        })
      }
    },
    // 查看jenkins详情
    jenkins_info () {
      let vm = this
      getJenkinsLog({ 'job_business_id': this.jenkins_url }).then(res => {
        window.open(
          res.data.data
        )
      }).catch(function (error) {
        vm.$Message.error('查询jenkins日志失败')
        console.log(error)
      })
    }

  }

}
</script>
