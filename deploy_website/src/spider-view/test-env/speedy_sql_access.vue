<template>
  <Card shadow style="height: 100%">
    <p slot="title" style="width: 100%;text-align: center;">
          SQL快速通道
        </p>
    <Form
          :label-width="120"
          style="text-align: left"
        >
    <FormItem v-show="br_show" label="应用：" prop="app_name" >
            <Row>
              <Col span="7">
              <Select placeholder="选择应用" v-model="sql_info_dict.app_name" filterable clearable style="width:200px" @on-change="get_app_info" >
          <Option v-for="item in app_info_list"
                  :value="item.module_name"
                  :key="item.module_name"
                  :label="item.module_name">
            {{ item.module_name }}
          </Option>
        </Select>
              </Col>
            </Row>
          </FormItem>
    <FormItem v-show="br_show" label="产线迭代版本：" prop="pipeline_id">
      <Row>
        <i-col span="7">
          <span style="text-align: left; display: inline-block;">{{sql_info_dict.pipeline_id}}</span>
        </i-col>
      </Row>
    </FormItem>
    <FormItem v-show="br_show" label="数据库：" prop="db_name">
            <Row>
              <Col span="7">
              <Select placeholder="选择数据库" v-model="sql_info_dict.db_name" filterable clearable style="width:200px" @on-change="select_db_name">
          <Option v-for="item in db_name_list"
                  :value="item"
                  :key="item"
                  :label="item">
            {{ item }}
          </Option>
        </Select>
              </Col>
            </Row>
    </FormItem>
    <FormItem v-show="br_show" label="DDL：" prop="DDL_sql">
            <Row>
              <Col span="7">
              <Input v-model="sql_info_dict.sql_dict.DDL" placeholder="填DDL,分号;分割" type="textarea" style="width:600px" />
              </Col>
            </Row>
    </FormItem>
    <FormItem v-show="br_show" label="DML：" prop="DML_sql">
            <Row>
              <Col span="7">
              <Input v-model="sql_info_dict.sql_dict.DML" placeholder="填DML,分号;分割" type="textarea" style="width:600px" />
              </Col>
            </Row>
    </FormItem>
    <FormItem>
        <Button type="primary" @click="handleSubmit()" :loading="btnDisabled">保存并提交</Button>
    </FormItem>
    </Form>

  </Card>
</template>

<script>
  import TestDataMgtPlanTable from "@/spider-components/test-data-mgt-plan-table";
  import Tables from "@/components/tables";
  import {getAppModule, get_app_info_detail, save_quick_sql_info, commit_quick_sql} from "@/spider-api/mgt-app";
  export default {
    name: "speedy_sql_access",
    components: {
      TestDataMgtPlanTable,
      Tables
    },
    data() {
      return {
        br_show:true,
        btnDisabled:false,
        sql_info_dict:{
          app_name:'',
          pipeline_id:'',
          db_name:'',
          sql_dict:{
            DDL:'',
            DML:''
          }
        },
        db_name_list:[],
        app_info_list:[],
      };
    },

    computed: {

    },

    methods: {
      get_app_name () {
          getAppModule().then(res => {
          this.app_info_list = res.data.data["data_list"]
        })
      },
      get_app_info () {
        get_app_info_detail(this.sql_info_dict.app_name).then(res => {
          console.log("选中的应用名为： "+this.sql_info_dict.app_name)
          console.log("pipeline_id: "+res.data.data["pipeline_id"])
          this.sql_info_dict.pipeline_id = res.data.data["pipeline_id"]
          this.db_name_list = res.data.data["db_info_list"]
        })
      },
      select_pipeline_id(){
    },
      select_db_name(){

      },

      init () {
      },

      handleSubmit () {
        this.btnDisabled = true
        save_quick_sql_info(this.sql_info_dict).then(res => {
          if (res.data.status === "success") {
            let real_br_name = res.data.data["branch_name"]
            let quick_sql_info = {"app_name":this.sql_info_dict.app_name,
                                  "db_name":this.sql_info_dict.db_name,
                                  "branch_name":real_br_name}
            console.log("quick_sql_info==="+quick_sql_info)
            commit_quick_sql(quick_sql_info).then(res => {
              if (res.data.status === "success") {
                this.btnDisabled = false
                alert("快速sql提交成功，详情请在archery查看")
              }
              else {
                this.btnDisabled = false
                alert("快速SQL提交失败, 失败详情:"+res.data.msg)
              }
            })
          }
          else {
            this.btnDisabled = false
            alert("保存失败！")
          }
        })
      },

    },

    created () {
        this.get_app_name()
      },
  };
</script>

<style scoped>
  .demo-spin-icon-load {
    animation: ani-demo-spin 1s linear infinite;
  }
</style>
