<style scoped>
  .card {
    padding-top: 10px;
    padding-left: 10px
  }

  .formItem {
    width: 50%;
  }

  .formButton {
    width: 50%;
    display: flex;
    justify-content: flex-end
  }

  .pag {
    display: flex;
    justify-content: center;
    /* margin-top: 24px; */
  }

</style>
<style lang='less'>
  .ivu-modal-header {
    text-align: center;
  }
</style>
<template>
  <div class="env_application">
    <Card class="card">
      <h2>快速SQL通道</h2>

      <Divider/>


    </Card>
    <Tabs v-model="tab_name" ref="tabs" @on-click="changeTab">
      <TabPane label="快速SQL提交" name="speedy_sql_access">
        <SpeedySqlAccess ref="SpeedySqlAccess"></SpeedySqlAccess>
      </TabPane>
      <TabPane label="快速SQL归档" name="speedy_sql_archive" :disabled="tab_disable">
        <SpeedySqlArchive ref="SpeedySqlAccess"></SpeedySqlArchive>
      </TabPane>
    </Tabs>
  </div>
</template>
<script>

  import SpeedySqlAccess from "@/spider-components/speedy-sql-access/speedy_sql_access";
  import SpeedySqlArchive from "@/spider-components/speedy-sql-archive/speedy_sql_archive";
  import time from '@/mixin/time'
  import isEmpty from '@/mixin/isEmpty'
  export default {
    mixins: [time, isEmpty],
    data() {
      return {
        tab_disable:false,
        tab_name:'speedy_sql_access'
      }
    },
    components: {
    SpeedySqlAccess,
    SpeedySqlArchive
  },
    mounted() {


    },
    methods: {
     changeTab(tab_name){
      if(tab_name === 'speedy_sql_access'){
        this.$refs.SpeedySqlAccess.init()
      }
     }
    }

  }
</script>
