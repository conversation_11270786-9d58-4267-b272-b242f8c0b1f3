/*
 * @Author: chao.bao <EMAIL>
 * @Date: 2025-01-22 13:18:25
 * @LastEditors: chao.bao <EMAIL>
 * @LastEditTime: 2025-01-23 09:53:57
 * @FilePath: /website_web/deploy_website/src/spider-view/releaseGroup/api.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import axios from '@/libs/api.request'

// 获取用户迭代分组接口
export const queryGroupInfo = data => {
    return axios.request({
        url: '/spider/publish/get_user_gitlab_group_info/',
        params: data,
        method: 'get'
    })
}
// 查询组发布全局策略
export const queryGroupStrategy = data => {
    return axios.request({
        url: '/spider/publish/get_group_publish_global_strategy_info/',
        params: data,
        method: 'get'
    })
}
// 保存组发布全局策略
export const saveGroupStrategy = data => {
    return axios.request({
        url: '/spider/publish/get_group_publish_global_strategy_info/',
        data,
        method: 'post'
    })
}
