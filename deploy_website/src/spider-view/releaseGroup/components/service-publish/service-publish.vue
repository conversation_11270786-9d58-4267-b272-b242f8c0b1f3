<template>
    <Card class="service-publish-card">
        <div class="card-body">
            <div :class="isDragged ? 'service-app-list dragged' : 'service-app-list'" @dragover="allowDrop">
                <div
                    v-for="(item, index) in appList"
                    :key="index"
                    class="item"
                    draggable="true"
                    @dragstart="drag($event, item)"
                >
                    <div>{{ item.app_name }}</div>
                </div>
            </div>
            <div class="service-publish-config">
                <div class="main-container" @dragover="allowDrop">
                    <div
                        v-for="container in containers"
                        :key="container.id"
                        class="container-box"
                        @drop="drop($event, container)"
                        @dragover="allowDrop"
                    >
                        <div>顺序{{ container.id }}</div>
                        <div :class="isDragged ? 'container dragged' : 'container'">
                            <div
                                v-for="(item, index) in container.items"
                                :key="index"
                                class="item"
                                draggable="true"
                                @dragstart="drag($event, item, container)"
                                @dragend="dragEnd"
                            >
                                <div>{{ item.app_name }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="save_btn">
            <Button type="primary" :loading="loading" @click="saveGroup">保存</Button>
        </div>
    </Card>
</template>

<script>
import { saveGroupStrategy } from '../../api'
export default {
    props: {
        git_group_name: {
            type: String,
            required: true,
            default: ''
        }
    },
    data() {
        return {
            loading: false,
            // 上方容器数据
            appList: [],
            // 下方容器数据
            containers: [],
            isDragged: false,
            draggedItem: null,
            draggedFromContainer: null
        }
    },
    beforeDestroy() {},
    methods: {
        initData(dragData) {
            this.appList = []
            this.containers = []
            dragData.forEach((item, index) => {
                this.containers.push({
                    id: index + 1,
                    items: []
                })
            })
            dragData.forEach(item => {
                if (item.step_num === 0) {
                    this.appList.push(item)
                } else {
                    // 如果容器中已经有数据则push，否则新增数据
                    const res = this.containers.find(container => container.id === item.step_num)
                    this.containers[res.id - 1].items.push(item)
                }
            })
        },
        saveGroup() {
            this.loading = true
            // todo - 保存数据
            const params = {
                git_group_name: this.git_group_name,
                app_step_dict_list: []
            }
            this.containers.map(container => {
                container.items.map(item => {
                    params.app_step_dict_list.push({
                        app_name: item.app_name,
                        step_num: container.id
                    })
                })
            })
            params.app_step_dict_list = [...params.app_step_dict_list, ...this.appList]

            saveGroupStrategy(params).then(res => {
                if (res.data.code === '0000') {
                    this.$Message.success(res.data.message)
                    this.loading = false
                } else {
                    this.$Message.error(res.data.message)
                    this.loading = false
                }
            })
        },
        allowDrop(event) {
            console.log('allowDrop-----拖拽中')

            event.preventDefault()
        },
        drag(event, item, container) {
            console.log('drag-----拖拽开始', event, item, container)

            this.isDragged = true
            this.draggedItem = item
            this.draggedFromContainer = container
            event.dataTransfer.setData('item', JSON.stringify(item))
            if (container) {
                event.dataTransfer.setData('containerId', container.id)
            }
        },
        drop(event, targetContainer, n) {
            // 上面往下面拖拽！！或者容器间互相拖拽
            console.log('drop-----拖拽鼠标释放', targetContainer)
            event.preventDefault()
            this.isDragged = false
            // 拖拽的应用数据
            const itemData = JSON.parse(event.dataTransfer.getData('item'))
            // 拖拽的容器id
            const sourceContainerId = event.dataTransfer.getData('containerId')

            if (!sourceContainerId && sourceContainerId !== '0') {
                // 从顶部移动下来
                console.log('情况1 - 从顶部移动下来', itemData)
                itemData.step_num = targetContainer.id
                targetContainer.items.push(itemData)
                // 将当前应用从容器中移除
                this.appList = this.appList.filter(app => app.app_name !== itemData.app_name)
            } else {
                // 容器间互相拖拽
                console.log('情况2 - 容器间互相拖拽')
                // 从源容器移除item
                this.containers.forEach(container => {
                    container.items = container.items.filter(item => item.app_name !== itemData.app_name)
                })
                // 添加item到目标容器
                this.containers.forEach(container => {
                    if (container.id === targetContainer.id) {
                        itemData.step_num = targetContainer.id
                        container.items.push(itemData)
                    }
                })
            }
            this.draggedItem = null
            this.draggedFromContainer = null
        },
        dragEnd(event) {
            // 下面往上面拖拽！！
            console.log('dragEnd-----拖拽结束', this.draggedItem, this.draggedFromContainer)
            this.isDragged = false
            if (this.draggedItem && this.draggedFromContainer) {
                const mainContainer = this.$el.querySelector('.main-container')
                const mainContainerRect = mainContainer.getBoundingClientRect()
                const isOutside =
                    event.clientX < mainContainerRect.left ||
                    event.clientX > mainContainerRect.right ||
                    event.clientY < mainContainerRect.top ||
                    event.clientY > mainContainerRect.bottom

                if (isOutside) {
                    // 将应用添加到上面的容器
                    this.draggedItem.step_num = 0
                    this.appList.push(this.draggedItem)
                    // 将当前应用从容器中移除
                    this.containers.forEach(container => {
                        container.items = container.items.filter(item => item.app_name !== this.draggedItem.app_name)
                    })
                }

                this.draggedItem = null
                this.draggedFromContainer = null
            }
        }
    }
}
</script>

<style lang="less" scoped>
.service-publish-card {
    height: 100%;
    background-color: #fff;
    margin-bottom: 20px;
    border: 1px solid #ddd;
    display: block;
    .card-body {
        // padding: 10px;
        background-color: #fff;
        // margin-bottom: 20px;
        margin-bottom: 10px;

        .service-app-list {
            display: flex;
            padding: 10px;
            overflow: auto;
            min-height: 65px;
            margin-bottom: 10px;
            margin-top: 10px;
            border: 1px dashed #fff;
        }

        .dragged {
            border: 1px dashed red;
        }
    }
}
.main-container {
    display: flex;
    border: 1px solid #00000014;
    border-radius: 4px;
    padding: 10px;
    overflow: auto;
}
.container-box {
    text-align: center;
}
.container {
    border: 1px solid #ccc;
    margin: 5px;
    padding: 10px;
    min-width: 200px;
    min-height: 345px;
    max-height: 345px;
    overflow-y: auto;
}
.item {
    border: 1px solid #00000008;
    background-color: #ddd;
    border-radius: 4px;
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.1);
    padding: 5px;
    margin: 5px;
    cursor: grab;
    min-width: 140px;
}
.save_btn {
    display: flex;
    justify-content: flex-end;
    margin-right: 10px;
}
</style>
