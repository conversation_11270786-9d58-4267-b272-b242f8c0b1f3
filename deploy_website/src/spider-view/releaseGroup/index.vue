<template>
    <div class="warapper">
        <Spin size="large" fix v-if="loadding"></Spin>
        <Tabs v-model="currentTab" @on-click="changeTab">
            <TabPane v-for="tabName in tabData" :key="tabName" :label="tabName" :name="tabName"> </TabPane>
        </Tabs>
        <div class="tab_body">
            <servicePublish ref="dragRef" :git_group_name="currentTab" />
        </div>
    </div>
</template>

<script>
import servicePublish from './components/service-publish'
import { queryGroupInfo, queryGroupStrategy } from './api'
export default {
    components: {
        servicePublish
    },
    data() {
        return {
            loadding: false,
            tabData: [],
            currentTab: ''
        }
    },
    methods: {
        changeTab(name) {
            this.loadding = true
            // 切换tab，重新请求数据
            queryGroupStrategy({ git_group_name: this.currentTab }).then(res => {
                if (res.data.code === '0000') {
                    this.$refs.dragRef.initData(res.data.data)
                    this.loadding = false
                }
            })
        }
    },
    mounted() {
        this.loadding = true
        queryGroupInfo({}).then(res => {
            if (res.data.code === '0000') {
                this.tabData = res.data.data
                this.currentTab = this.tabData[0] || ''
                // 查询第一个tab的拖拽数据
                queryGroupStrategy({ git_group_name: this.currentTab }).then(res => {
                    if (res.data.code === '0000') {
                        this.$refs.dragRef.initData(res.data.data)
                        this.loadding = false
                    }
                })
            } else {
                this.$Message.error(res.data.message)
            }
        })
    }
}
</script>

<style lang="less" scoped>
.warapper {
    height: 100%;
    position: relative;
    .tab_body {
        height: 600px;
        border: 1px solid #ebeef5;
    }
}
</style>
