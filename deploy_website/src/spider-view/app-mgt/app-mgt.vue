<template>
  <div>
    <div style="margin-bottom: 15px">
      <h2>应用管理</h2>
    </div>
   <Card>
    <i-col>
        <Select v-model="module_name_search" filterable clearable style="width:200px">
          <Option v-for="item in module_item_list"
                  :value="item.module_name"
                  :label="item.module_name"
                  :key="item.module_name">
            {{ item.module_name }}
          </Option>
        </Select>

        <Button @click="getTable(1)" type="info">查询</Button>

      </i-col>
      <i-col >
        <Table :columns="columns" :data="dataList"></Table>
      </i-col>
     <i-col>
        <Page style="margin: 5px;" :total="pageTotal" :current="pageNum" @page-size="pageSize"
              @on-change="changePage" show-total></Page>
      </i-col>
      </Card>
<Modal
        v-model="showEditAppInfotable" width="580px">
        <p slot="header" style="color:gray;">
          <Icon type="md-clipboard"></Icon>
          <span>应用信息编辑</span>
        </p>
<Form ref="app_mgt_edit" :model="app_mgt_edit"  :label-width="80">
        <FormItem label="应用名" prop="app_name">
            <Input v-model="app_mgt_edit.module_name" placeholder="Enter your app_name"/>
        </FormItem>
        <FormItem label="仓库地址" prop="url_path">
            <Input v-model="app_mgt_edit.url_path" placeholder="Enter your url"/>
        </FormItem>

        <FormItem label="仓库类型">
          <RadioGroup  v-model="app_mgt_edit.platform_type" style="width: 19em; font-size: 1rem; padding-left: 1.0em; color: #2d8cf0;margin-bottom：0em">
        <Radio align="right" :label='0'>
            <span align="right" >SVN</span>
        </Radio >
        <Radio  :label='1'>
            <span align="left" >GIT</span>
        </Radio>
    </RadioGroup>
    </FormItem>

        <FormItem label="url" prop="url">
            <Input v-model="app_mgt_edit.url" placeholder="Enter your url"/>
        </FormItem>
        <FormItem label="path" prop="path">
            <Input v-model="app_mgt_edit.path" placeholder="Enter your url"/>
        </FormItem>
        <FormItem label="包类型" prop="app_type">
            <Input v-model="app_mgt_edit.app_type" placeholder="Enter your app_type"/>
        </FormItem>
        <FormItem label="包名" prop="package_name">
            <Input v-model="app_mgt_edit.package_name" placeholder="Enter your package_name"/>
        </FormItem>
        <FormItem label="JDK版本" prop="app_jdk_version">
            <Input v-model="app_mgt_edit.app_jdk_version" placeholder="Enter your app_jdk_version"/>
        </FormItem>
        <FormItem label="制品库" prop="lib_repo">
            <Input v-model="app_mgt_edit.lib_repo" placeholder="Enter your lib_repo"/>
        </FormItem>
        <FormItem label="团队" prop="team_name">
            <Input v-model="app_mgt_edit.team_name" placeholder="Enter your team_name"/>
        </FormItem>
        <FormItem label="是否需要维护">
          <RadioGroup  v-model="app_mgt_edit.need_check" style="width: 19em; font-size: 1rem; padding-left: 1.0em; color: #2d8cf0;margin-bottom：0em">
        <Radio align="right" :label="1">
            <span align="right" >是</span>
        </Radio >
        <Radio  :label="0">
            <span align="left" >否</span>
        </Radio>
    </RadioGroup>
    </FormItem>

    <FormItem label="是否需要上线">
          <RadioGroup  v-model="app_mgt_edit.need_online" style="width: 19em; font-size: 1rem; padding-left: 1.0em; color: #2d8cf0;margin-bottom：0em">
        <Radio align="right" :label="1">
            <span align="right" >是</span>
        </Radio >
        <Radio  :label="0">
            <span align="left" >否</span>
        </Radio>
    </RadioGroup>
    </FormItem>

    <FormItem label="是否接入宙斯">
          <RadioGroup  v-model="app_mgt_edit.zeus_type" style="width: 19em; font-size: 1rem; padding-left: 1.0em; color: #2d8cf0;margin-bottom：0em">
        <Radio align="right" :label="1">
            <span align="right" >是</span>
        </Radio >
        <Radio  :label="0">
            <span align="left" >否</span>
        </Radio>
    </RadioGroup>
    </FormItem>

        <FormItem label="应用端口" prop="app_port">
            <Input v-model="app_mgt_edit.app_port" placeholder="Enter your app_port"/>
        </FormItem>
        <FormItem label="容器名" prop="container_name">
            <Input v-model="app_mgt_edit.container_name" placeholder="Enter your container_name"/>
        </FormItem>
        <FormItem label="打包地址" prop="create_path">
            <Input v-model="app_mgt_edit.create_path" placeholder="Enter your create_path"/>
        </FormItem>
        <FormItem label="发布地址" prop="deploy_path">
            <Input v-model="app_mgt_edit.deploy_path" placeholder="Enter your deploy_path"/>
        </FormItem>

    </Form>
    <div slot="footer">
          <Button @click="editAppInfocancel">关闭</Button>
          <Button type="primary" @click="editAppInfoComfirm()" :disabled="false">确定</Button>
        </div>
</Modal>

  </div>
</template>
<script>
import { getAppModule, getAppInfo, editAppModule } from '@/spider-api/mgt-app'

export default {
    data() {
        return {
            showEditAppInfotable: false,
            app_mgt_edit: {
                app_name: '',
                url_path: '',
                url: '',
                path: '',
                app_type: '',
                package_name: '',
                app_jdk_version: '',
                lib_repo: '',
                team_name: '',
                platform_type: '',
                zeus_type: '',
                need_online: '',
                need_check: '',
                app_port: '',
                container_name: '',
                create_path: '',
                deploy_path: '',
                isneedcheck: '',
                isneedonline: '',
                iszeus: '',
                module_name_search: ''
            },
            module_item_list: '',
            module_name_search: '',
            pageNum: 1,
            pageSize: 10,
            pageTotal: 1080,
            iszeus: 'true',
            columns: [
                {
                    title: '应用名',
                    key: 'module_name',
                    width: 90, //列宽
                    fixed: 'left', //列是否固定在左侧或者右侧
                    sortable: true //对应列是否可以排序
                },
                { title: '仓库地址', key: 'url_path' },
                { title: '包类型', key: 'app_type' },
                { title: '包名', key: 'package_name' },
                { title: 'JDK版本', key: 'app_jdk_version' },
                { title: '制品库', key: 'lib_repo' },
                { title: '团队', key: 'team_name' },
                { title: '平台接入类型', key: 'platform_type' },
                { title: '是否接入宙斯', key: 'zeus_type' },
                { title: '是否需要上线', key: 'need_online' },
                { title: '是否需要维护', key: 'need_check' },
                { title: '应用端口', key: 'app_port' },
                { title: '容器名', key: 'container_name' },
                { title: '打包路径', key: 'create_path' },
                { title: '发布路径', key: 'deploy_path' },
                {
                    title: '操作',
                    key: 'handle',
                    width: 70,
                    render: (h, params) => {
                        return h('div', [
                            h(
                                'Button',
                                {
                                    attrs: {
                                        class: 'ivu-btn ivu-btn-primary ivu-btn-small'
                                    },
                                    props: {
                                        disabled: this.access_status,
                                        type: 'primary',
                                        size: 'small'
                                    },
                                    style: {
                                        marginRight: '6px'
                                    },
                                    on: {
                                        click: () => {
                                            this.showEditAppInfo(params)
                                            // this.rewireOrderRequest(params)
                                        }
                                    }
                                },
                                '编辑'
                            )
                        ])
                    }
                }
            ],

            tableHeight: window.innerHeight,
            //这里的200是自己根据实际需求进行定义的，此处的200是因为该table上方有个form表单，减去form表单的高度
            //https://www.cnblogs.com/Charles-Yuan/p/11294592.html

            dataList: []
        }
    },

    methods: {
        showEditAppInfo(params) {
            this.showEditAppInfotable = true
            this.app_mgt_edit.module_name = params.row.module_name
            this.app_mgt_edit.url_path = params.row.url_path
            this.app_mgt_edit.app_type = params.row.app_type
            this.app_mgt_edit.package_name = params.row.package_name
            this.app_mgt_edit.app_jdk_version = params.row.app_jdk_version
            this.app_mgt_edit.lib_repo = params.row.lib_repo
            this.app_mgt_edit.team_name = params.row.team_name
            this.app_mgt_edit.platform_type = params.row.platform_type
            this.app_mgt_edit.zeus_type = params.row.zeus_type
            this.app_mgt_edit.need_online = params.row.need_online
            this.app_mgt_edit.need_check = params.row.need_check
            this.app_mgt_edit.app_port = params.row.app_port
            this.app_mgt_edit.container_name = params.row.container_name
            this.app_mgt_edit.create_path = params.row.create_path
            this.app_mgt_edit.deploy_path = params.row.deploy_path
            this.app_mgt_edit.url = params.row.url
            this.app_mgt_edit.path = params.row.path
        },
        changePage(idx) {
            this.pageNum = idx
            this.getTable(idx)
        },
        editAppInfoComfirm() {
            this.showEditAppInfotable = false
            editAppModule(this.app_mgt_edit).then(res => {
                if (res.data['status'] == 'success') {
                    this.$Message.success(res.data['msg'])
                } else if (res.data['status'] == 'failed') {
                    alert(res.data['msg'])
                } else {
                    this.$Message.error(res.data.msg)
                }
            })
        },
        editAppInfocancel() {
            this.showEditAppInfotable = false
        },
        getTable(idx) {
            let data = {
                module_name: this.module_name_search,
                node_ip: this.node_ip_text,
                suite_code: this.suite_code_search,
                node_status: this.node_status_search,
                pageNum: idx,
                pageSize: this.pageSize,
                pageTotal: this.pageTotal,
                access_status: this.access_status
            }
            getAppInfo(data).then(res => {
                let page = res.data.data['page']
                this.pageNum = page['num']
                this.pageSize = page['size']
                this.pageTotal = page['total']

                this.dataList = res.data.data['data_list']
            })
        }
    },

    mounted() {
        getAppModule().then(res => {
            this.module_item_list = res.data.data['data_list']
        }),
            getAppInfo().then(res => {
                let page = res.data.data['page']
                this.pageNum = page['num']
                this.pageSize = page['size']
                this.pageTotal = page['total']
                this.dataList = res.data.data['data_list']
            })
    } //mounted
} //default
</script>

<style>
</style>
