<template>
  <card>
    <h2 style="margin: 10px">新增应用</h2>
    <Row style="margin-left: 1em">
      <i-col align="right" style="margin: 1em" span="2">
        <span style="text-align: left; display: inline-block;">应用分类</span>
      </i-col>
      <i-col style="margin-top: 1em" span="5">
        <RadioGroup @on-change="refresh_app_data" v-model="app_category">
          <Radio label="JAVA"></Radio>
          <Radio label="非JAVA"></Radio>
        </RadioGroup>
      </i-col>
    </Row>
    <Row style="margin-left: 1em">
      <i-col align="right" style="margin: 1em" span="2">
        <span style="color: red">*&nbsp;</span>
        <span style="text-align: left; display: inline-block;">选择团队</span>
      </i-col>
      <i-col style="margin-top: 1em" span="5">
        <Select v-model="team" span="5" style="width: 300px">
          <Option v-for="item in ft_list" :value="item.value" :key="item.value">{{ item.label }}</Option>
        </Select>
      </i-col>
    </Row>
    <Row style="margin-left: 1em">
      <i-col align="right" style="margin: 1em" span="2">
        <span style="text-align: left; display: inline-block;">代码仓库类型</span>
      </i-col>
      <i-col style="margin-top: 1em" span="5">
        <RadioGroup v-model="repo_type">
          <Radio label="GIT"></Radio>
          <Radio label="SVN"></Radio>
        </RadioGroup>
      </i-col>
    </Row>
    <Row style="margin-left: 1em">
      <i-col align="right" style="margin: 1em" span="2">
        <span style="color: red">*&nbsp;</span>
        <span style="text-align: left; display: inline-block;">{{repo_type}}地址</span>
      </i-col>
      <i-col style="margin-top: 1em" span="5">
        <Input
          v-if="repo_type == 'GIT'"
          v-model="trunk_path"
          :placeholder="git_prompt"
          style="width: 300px"
        />
        <Input
          v-if="repo_type == 'SVN'"
          type="textarea"
          v-model="trunk_path"
          :placeholder="svn_prompt"
          :rows="4"
          style="width: 300px"
        />
      </i-col>
    </Row>
    <Row v-if="app_category == '非JAVA'" style="margin-left: 1em">
      <i-col align="right" style="margin: 1em" span="2">
        <span style="color: red">*&nbsp;</span>
        <span style="text-align: left; display: inline-block;">应用名</span>
      </i-col>
      <i-col style="margin-top: 1em" span="5">
        <Input v-model="app_name" placeholder="应用名" style="width: 300px" />
      </i-col>
    </Row>
    <Row v-if="app_category == '非JAVA'" style="margin-left: 1em">
      <i-col align="right" style="margin: 1em" span="2">
        <span style="text-align: left; display: inline-block;">应用中文名</span>
      </i-col>
      <i-col style="margin-top: 1em" span="5">
        <Input v-model="app_name_cn" placeholder="应用中文名" style="width: 300px" />
      </i-col>
    </Row>

    <Button
      v-if="app_category == 'JAVA'"
      :disabled="parse_running_stat"
      type="primary"
      @click="do_app_parse"
      style="margin-top:2em;margin-left:4em;"
    >解析</Button>
    <Table
      v-if="app_category == 'JAVA'"
      :loading="app_load"
      style="margin-top: 2em"
      border
      :columns="app_columns"
      :data="app_data"
    ></Table>

    <Button
      type="primary"
      :disabled="add_running_stat"
      @click="save_app_info"
      style="margin-top:2em;margin-left:4em;"
    >新增</Button>

    <Modal v-model="modal_app">
      <p slot="header" style="color:gray;">
        <Icon type="md-clipboard"></Icon>
        <span>{{m_app_name}} 应用信息编辑</span>
      </p>
      <div>
        <p align="right" style="font-size: 1rem; width: 5em; display:inline-block;">类型</p>
        <Input
          style="width: 22em; font-size: 1rem; padding-left: 1em; color: #2d8cf0;"
          v-model="m_app_type"
        />
      </div>
      <div style="margin-top: 1em">
        <p align="right" style="font-size: 1rem; width: 5em; display:inline-block;">jar包名</p>
        <Input
          style="width: 22em; font-size: 1rem; padding-left: 1em; color: #2d8cf0;"
          v-model="m_jar_name"
        />
      </div>
      <div style="margin-top: 1em">
        <p align="right" style="font-size: 1rem; width: 5em; display:inline-block;">jdk版本</p>
        <Input
          style="width: 22em; font-size: 1rem; padding-left: 1em; color: #2d8cf0;"
          v-model="m_jdk_version"
        />
      </div>
      <div style="margin-top: 1em">
        <p align="right" style="font-size: 1rem; width: 5em; display:inline-block;">仓库地址</p>
        <Input
          style="width: 22em; font-size: 1rem; padding-left: 1em; color: #2d8cf0;"
          type="textarea"
          autosize
          v-model="m_trunk_path"
        />
      </div>
      <div style="margin-top: 1em">
        <p align="right" style="font-size: 1rem; width: 5em; display:inline-block;">制品库地址</p>
        <Input
          style="width: 22em; font-size: 1rem; padding-left: 1em; color: #2d8cf0;"
          v-model="m_repo_path"
        />
      </div>
      <div style="margin-top: 1em">
        <p align="right" style="font-size: 1rem; width: 5em; display:inline-block;">团队</p>
        <Input
          style="width: 22em; font-size: 1rem; padding-left: 1em; color: #2d8cf0;"
          v-model="m_team"
        />
      </div>
      <div style="margin-top: 1em">
        <p align="right" style="font-size: 1rem; width: 5em; display:inline-block;">应用中文名</p>
        <Input
          style="width: 22em; font-size: 1rem; padding-left: 1em; color: #2d8cf0;"
          v-model="m_app_name_cn"
        />
      </div>
      <div slot="footer">
        <Button type="primary" @click="app_ack">确定</Button>
        <Button @click="cancel">关闭</Button>
      </div>
    </Modal>
  </card>
</template>

<script>
import { getAppInfo, addAppInfo, parseAppInfo } from "@/spider-api/app-mgt";

export default {
  name: "team_initial",
  data() {
    return {
      add_running_stat: false,
      parse_running_stat: false,
      app_category: "JAVA",
      repo_type: "GIT",
      svn_prompt:
        "svn://svn.howbuy.test/usr/local/subversion-1.4.4/repos/Trade/trunk/Sources/acc/acc-center/",
      git_prompt: "asset/asset-batch-center",
      trunk_path: "",
      app_name: "",
      app_name_cn: "",
      parse_btn: false,
      app_load: false,
      team: "",
      ft_list: [
        { value: "tms", label: "tms" },
        { value: "tp", label: "tp" },
        { value: "crm", label: "crm" },
        { value: "fpc", label: "fpc" },
        { value: "h5", label: "h5" },
        { value: "pa", label: "pa" },
        { value: "scm", label: "scm" },
        { value: "ios", label: "ios" },
        { value: "安卓", label: "安卓" }
      ],
      modal_app: false,
      m_app_name: "",
      m_app_type: "",
      m_jar_name: "",
      m_jdk_version: "",
      m_trunk_path: "",
      m_repo_path: "",
      m_team: "",
      m_app_name_cn: "",
      app_columns: [
        {
          title: "应用名",
          key: "app_name",
          // width: 150
        },
        {
          title: "类型",
          key: "app_type",
          width: 70
        },
        {
          title: "包名",
          key: "jar_name",
          // width: 150
        },
        {
          title: "仓库地址",
          key: "trunk_path",
          width: 220
        },
        {
          title: "JDK版本",
          key: "jdk_version",
          width: 90
        },
        {
          title: "制品库地址",
          key: "repo_path",
          // width: 150
        },
        {
          title: "团队",
          key: "team",
          width: 70
        },
        {
          title: "应用中文名",
          key: "app_name_cn",
          // width: 120
        },
        {
          title: "操作",
          // fixed: "right",
          width: 160,
          key: "handle",
          render: (h, params) => {
            return h("div", [
              h(
                "Button",
                {
                  attrs: {
                    class: "ivu-btn ivu-btn-primary ivu-btn-small"
                  },
                  props: {},
                  style: {
                    marginRight: "6px"
                  },
                  on: {
                    click: () => {
                      this.modal_app = true;
                      this.m_app_name = params.row.app_name;
                      this.m_app_type = params.row.app_type;
                      this.m_jar_name = params.row.jar_name;
                      this.m_jdk_version = params.row.jdk_version;
                      this.m_trunk_path = params.row.trunk_path;
                      this.m_repo_path = params.row.repo_path;
                      this.m_team = params.row.team;
                      this.m_app_name_cn = params.row.app_name_cn;
                    }
                  }
                },
                "编辑"
              ),
              h(
                "Button",
                {
                  attrs: {
                    class: "ivu-btn ivu-btn-error ivu-btn-small"
                  },
                  props: {},
                  style: {
                    marginRight: "6px"
                  }
                },
                [
                  h(
                    "Poptip",
                    {
                      props: {
                        confirm: true,
                        transfer: true,
                        title: "删除",
                        size: "small"
                      },
                      on: {
                        "on-ok": () => {
                          var app_name = params.row["app_name"];
                          this.deleteData(app_name);
                        },
                        "on-cancel": () => {
                          this.$Message.info("取消");
                        }
                      }
                    },
                    "删除"
                  )
                ]
              )
            ]);
          }
        }
      ],
      app_data: [],
      clone_app_data: []
    };
  },
  methods: {
    refresh_app_data() {
      this.app_data = [];
    },
    do_app_parse() {
      if (!this.valid_java_data()) {
        this.$Message.error("请填写必填字段");
        return false;
      }
      var data = {
        team: this.team,
        trunk_path: this.trunk_path,
        repo_type: this.repo_type
      };
      this.parse_running_stat = true;
      parseAppInfo(data)
        .then(res => {
          if (res.data.status === "success") {
            this.app_data = res.data.data;
            for (let item in this.app_data) {
              this.app_data[item].editting = false;
              this.app_data[item].saving = false;
            }
            this.parse_running_stat = false;
            this.$Message.success(res.data.msg);
          } else {
            this.parse_running_stat = false;
            this.$Message.error(res.data.msg);
          }
        })
        .catch(err => {
          this.parse_running_stat = false;
          this.$Message.error(err.response.data.msg);
        });
    },
    valid_non_java_data() {
      if (this.team == "" || this.trunk_path == "" || this.app_name == "") {
        return false;
      }
      return true;
    },
    valid_java_data() {
      if (this.team == "" || this.trunk_path == "") {
        return false;
      }
      return true;
    },
    valid_data() {
      if (this.app_category == "非JAVA") {
        if (!this.valid_non_java_data()) {
          this.$Message.error("请填写必填字段");
          return false;
        }
        this.app_data = [
          {
            app_category: this.app_category,
            team: this.team,
            repo_type: this.repo_type,
            trunk_path: this.trunk_path,
            app_name: this.app_name,
            app_name_cn: this.app_name_cn
          }
        ];
      } else {
        if (this.app_data.length === 0) {
          this.$Message.error("没有需要新增的应用信息");
          return false;
        }
      }
      return true;
    },
    save_app_info() {
      if (
        this.$store.state.user.team == "app" ||
        this.$store.state.user.role == "ops"
      ) {
        this.$Message.error("请联系配管进行操作");
        return;
      }
      if (!this.valid_data()) {
        return;
      }
      this.add_running_stat = true;
      addAppInfo(this.app_data)
        .then(res => {
          if (res.data.status === "success") {
            this.$Message.success(res.data.msg);
          } else {
            this.$Message.error(res.data.msg);
          }
          this.add_running_stat = false;
        })
        .catch(err => {
          this.$Message.error(err.response.data.msg);
          this.add_running_stat = false;
        });
    },
    app_ack() {
      var tmp_data = [];
      for (var item in this.app_data) {
        if (this.app_data[item]["app_name"] === this.m_app_name) {
          tmp_data.push({
            app_name: this.m_app_name,
            app_type: this.m_app_type,
            jar_name: this.m_jar_name,
            jdk_version: this.m_jdk_version,
            trunk_path: this.m_trunk_path,
            repo_path: this.m_repo_path,
            team: this.m_team,
            app_name_cn: this.m_app_name_cn
          });
        } else {
          tmp_data.push(this.app_data[item]);
        }
      }
      this.app_data = tmp_data;
      this.$Message.success("编辑成功");
      this.modal_app = false;
    },
    cancel() {
      this.modal_app = false;
    },
    deleteData(app_name) {
      var tmp_data = [];
      for (var item in this.app_data) {
        if (this.app_data[item]["app_name"] !== app_name) {
          tmp_data.push(this.app_data[item]);
        }
      }
      this.app_data = tmp_data;
      this.$Message.success("删除成功");
    }
  },
  mounted() {},
  destroyed() {}
};
</script>

<style scoped>
</style>
