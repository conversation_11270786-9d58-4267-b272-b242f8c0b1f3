<template>
    <card>
      <h2>SVN账号申请</h2>
      <Divider/>
      <row style="margin: 30px">
        <col span="6" style="text-align: right; margin-right: 10px">输入svn专用密码:</col>
        <col span="4">
          <Input v-model="password" type="password"></Input>
        </col>
      </row>
      <row style="margin: 30px">
        <col span="6" style="text-align: right; margin-right: 10px">确认svn专用密码:</col>
        <col span="4">
          <Input v-model="confirmPassword" type="password"></Input>
        </col>
      </row>
      <row style="margin: 30px">
        <col offset="6">
          <Button @click="submit">申请</Button>
        </col>
      </row>
    </card>
  </template>
  
  <script>
  export default {
    name: 'password-input',
    data() {
      return {
        password: '',
        confirmPassword: ''
      }
    },
    methods: {
      submit() {
        if (this.password.length < 6) {
          alert('密码长度必须大于6位')
        } else if (this.password === this.confirmPassword) {
          alert('申请成功')
        } else {
          alert('两次输入的密码不一致，请重新输入')
        }
        this.password = ''
        this.confirmPassword = ''
      }
    }
  }
  </script>