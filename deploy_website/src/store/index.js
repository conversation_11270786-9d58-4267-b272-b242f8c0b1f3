import Vue from 'vue'
import Vuex from 'vuex'

import user from './module/user'
import app from './module/app'

import config from '@/config'
const { socket_host } = config

Vue.use(Vuex)

export default new Vuex.Store({
  state: {
    businessID: '',
    operator: '',
    iterationID:'',
    socket_host: socket_host,
    gSocket: null,
    compile_done_list: []
  },
  mutations: {
    setGSocket (state, gSocket) {
      state.gSocket = gSocket
    },
    setBusinessID (state, businessID) {
      state.businessID = businessID
    },
     iterationID (state, businessID) {
      state.businessID = iterationID
    },
    setOperator (state, operator) {
      state.operator = operator
    },
    notice_compile_done (state, businessID) {
      state.compile_done_list.push(businessID)
    },
    reset_compile_status (state, businessID) {
      let del_index = state.compile_done_list.indexOf(businessID)
      state.compile_done_list.splice(del_index, 1)
    }
  },
  actions: {
    //
  },
  modules: {
    user,
    app
  }
})
