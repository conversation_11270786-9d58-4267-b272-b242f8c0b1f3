import {
  login,
  spiderLogin,
  logout,
  getUserInfo,
  getMessage,
  getContentByMsgId,
  hasRead,
  removeReaded,
  restoreTrash,
  getUnreadCount,
  spiderLoginOut
} from '@/api/user'
import config from '@/config'
import {setToken, getToken, setUsernameToCookies, getUserName} from '@/libs/util'
import {setUser} from '@/libs/spider-utils'

export default {
  state: {
    userName: getUserName(),
    userId: '',
    avatorImgPath: '',
    token: getToken(),
    access: [],
    team: '',
    role: '',
    hasGetInfo: false,
    unreadCount: 0,
    messageUnreadList: [],
    messageReadedList: [],
    messageTrashList: [],
    messageContentStore: {}
  },
  mutations: {
    setAvator(state, avatorPath) {
      state.avatorImgPath = avatorPath
    },
    setUserId(state, id) {
      state.userId = id
    },

    setUserName(state, name) {
      state.userName = name
      setUsernameToCookies(name)
    },
    setAccess(state, access) {

      state.access = access
    },
    setTeam(state, team) {
      state.team = team
    },
    setRole(state, role) {
      state.role = role
    },
    setRoleRole(state, role_role) {
      state.role_role = role_role
    },
    setRoleProject(state, role_project) {
      state.role_project = role_project
    },
    setToken(state, token) {
      setToken(token)
    },
    setHasGetInfo(state, status) {
      state.hasGetInfo = status
    },
    setMessageCount(state, count) {
      state.unreadCount = count
    },
    setMessageUnreadList(state, list) {
      state.messageUnreadList = list
    },
    setMessageReadedList(state, list) {
      state.messageReadedList = list
    },
    setLoginUser(state, uname) {
      setUsernameToCookies(uname)
    },
    setMessageTrashList(state, list) {
      state.messageTrashList = list
    },
    updateMessageContentStore(state, {msg_id, content}) {
      state.messageContentStore[msg_id] = content
    },
    moveMsg(state, {from, to, msg_id}) {
      const index = state[from].findIndex(_ => _.msg_id === msg_id)
      const msgItem = state[from].splice(index, 1)[0]
      msgItem.loading = false
      state[to].unshift(msgItem)
    }
  },
  getters: {
    messageUnreadCount: state => state.messageUnreadList.length,
    messageReadedCount: state => state.messageReadedList.length,
    messageTrashCount: state => state.messageTrashList.length
  },
  actions: {
    // 登录
    handleLogin({commit}, {userName, password}) {
      userName = userName.trim()
      return new Promise((resolve, reject) => {
        // 只需要从spider登录 20220302 by fwm
        spiderLogin({userName, password}).then(res => {
          const data = res.data
          if (data['access']) {
            commit('setToken', data['access'])
            commit('setHasGetInfo', true)
            commit('setLoginUser', userName)
            commit('setUserName', userName)
            commit('setAvator', '../../img/nezha.jpg')
            resolve(true)
          } else {
            resolve(false)
          }
        }).catch(err => {
          reject(err)
        })
      })
    },

    // 退出登录
    handleLogOut({state, commit, spider_store}) {
      return new Promise((resolve, reject) => {
        commit('setToken', '')
        commit('setUserName', '')
        commit('setUserId', '')
        commit('setAvator', '')
        commit('setTeam', '')
        commit('setRole', '')
        commit('setRoleRole', '')
        commit('setRoleProject', [])
        commit('setAccess', [])
        commit('setLoginUser', '')
        resolve()
      })
    },
    // 获取用户相关信息
    getUserInfo({state, commit}) {
      return new Promise((resolve, reject) => {
        try {
          getUserInfo(state.token).then(res => {
            let data = res.data
            if (data['code'] === 0) {
              data = data['data']
            }
            commit('setAvator', data.avator)
            commit('setUserName', data.name)
            commit('setUserId', data.user_id)
            commit('setAccess', data.access)
            commit('setTeam', data.team)
            commit('setRole', data.role)
            commit('setRoleRole', data.role_role)
            commit('setRoleProject', data.role_project)
            commit('setHasGetInfo', true)
            resolve(data)
          }).catch(err => {
            reject(err)
          })
        } catch (error) {
          reject(error)
        }
      })
    },
    // 此方法用来获取未读消息条数，接口只返回数值，不返回消息列表
    getUnreadMessageCount({state, commit}) {
      getUnreadCount().then(res => {
        const {data} = res
        commit('setMessageCount', data)
      })
    },
    // 获取消息列表，其中包含未读、已读、回收站三个列表
    getMessageList({state, commit}) {
      return new Promise((resolve, reject) => {
        getMessage().then(res => {
          const {unread, readed, trash} = res.data
          commit('setMessageUnreadList', unread.sort((a, b) => new Date(b.create_time) - new Date(a.create_time)))
          commit('setMessageReadedList', readed.map(_ => {
            _.loading = false
            return _
          }).sort((a, b) => new Date(b.create_time) - new Date(a.create_time)))
          commit('setMessageTrashList', trash.map(_ => {
            _.loading = false
            return _
          }).sort((a, b) => new Date(b.create_time) - new Date(a.create_time)))
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    },
    // 根据当前点击的消息的id获取内容
    getContentByMsgId({state, commit}, {msg_id}) {
      return new Promise((resolve, reject) => {
        let contentItem = state.messageContentStore[msg_id]
        if (contentItem) {
          resolve(contentItem)
        } else {
          getContentByMsgId(msg_id).then(res => {
            const content = res.data
            commit('updateMessageContentStore', {msg_id, content})
            resolve(content)
          })
        }
      })
    },
    // 把一个未读消息标记为已读
    hasRead({state, commit}, {msg_id}) {
      return new Promise((resolve, reject) => {
        hasRead(msg_id).then(() => {
          commit('moveMsg', {
            from: 'messageUnreadList',
            to: 'messageReadedList',
            msg_id
          })
          commit('setMessageCount', state.unreadCount - 1)
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    },
    // 删除一个已读消息到回收站
    removeReaded({commit}, {msg_id}) {
      return new Promise((resolve, reject) => {
        removeReaded(msg_id).then(() => {
          commit('moveMsg', {
            from: 'messageReadedList',
            to: 'messageTrashList',
            msg_id
          })
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    },
    // 还原一个已删除消息到已读消息
    restoreTrash({commit}, {msg_id}) {
      return new Promise((resolve, reject) => {
        restoreTrash(msg_id).then(() => {
          commit('moveMsg', {
            from: 'messageTrashList',
            to: 'messageReadedList',
            msg_id
          })
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    }
  }
}
