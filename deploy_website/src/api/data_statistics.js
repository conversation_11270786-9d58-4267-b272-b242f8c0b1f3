import axios from '@/libs/api.request'
import store from '../store'

export const getPublishHistoryData = (time_zone) => {
  const data  = {
    "time_zone": time_zone
  }
  return axios.request({
    url: 'data_statistics/release_history_query',
    headers: { 'token': store.state.user.token },
    method: 'post',
    data: data,
  })
};

export const updateMaintainStatus = (businessID) => {
  return axios.request({
    url: 'env/base_repo/update',
    headers: { 'token': store.state.user.token },
    method: 'put',
    data: {"businessID": businessID}
  })
};
