import axios from '@/libs/api.request'
import store from '../store'

export const getEmailAddresses = () => {
  return axios.request({
    url: 'emergency_service/email_addresses',
    headers: { 'token': store.state.user.token },
    method: 'get'
  })
}

export const sendEmailNotify = (data) => {
  return axios.request({
    url: 'emergency_service/email_notify',
    headers: { 'token': store.state.user.token },
    data: {
      'subject': data['subject'],
      'content': data['content'],
      'addresses': data['addresses']
    },
    method: 'post'
  })
}
