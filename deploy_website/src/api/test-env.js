import store from '../store'
import axios from '@/libs/api.request'
import spider_axios from '@/libs/spider_api.request'


// 测试环境申请
export const testEnvApplication = (data) => {
  return axios.request({
    url: 'env/test_env/application',
    headers: {'token': store.state.user.token},
    data: {
      "applicant": data['applicant'],
      "env_id": data['env_id'],
      "env_use": data['env_use'],
      "start_time": data['start_time'],
      "end_time": data['end_time'],
      "ta_time": data['ta_time'],
      "is_update_db": data['is_update_db'],
      "env_owner": data['env_owner'],
      "has_tp": data['has_tp']
    },
    method: 'post'
  })
};

// 获取测试环境使用情况
export const getEnvUtilization = () => {
  return axios.request({
    url: 'env/test_env/utilization',
    headers: {'token': store.state.user.token},
    method: 'post'
  })
};

// 获取环境处理进度
export const getEnvProgress = () => {
  return axios.request({
    url: 'env/test_env/progress',
    headers: {'token': store.state.user.token},
    method: 'post'
  })
};

// 下一步
export const nextProgress = (data) => {
  return axios.request({
    url: 'env/test_env/next_progress',
    headers: {'token': store.state.user.token},
    data: {
      "current_progress": data['current_progress'],
      "order": data['order'],
    },
    method: 'post'
  })
};

// 处理优先级调序申请
export const changeOrderApplication = (data) => {
  return axios.request({
    url: 'env/test_env/change_order_application',
    headers: {'token': store.state.user.token},
    data: {
      "change_order": data['change_order'],
    },
    method: 'post'
  })
};

// 延长测试环境使用期限
export const envExtend = (data) => {
  return axios.request({
    url: 'env/test_env/env_extend',
    headers: {'token': store.state.user.token},
    data: {
      "newDate": data['newDate'],
      "envID": data['envID']
    },
    method: 'post'
  })
};

// 环境回收申请
export const envRecycleApplication = (data) => {
  return axios.request({
    url: 'env/test_env/env_recycle_application',
    headers: {'token': store.state.user.token},
    data: {
      "envID": data['envID']
    },
    method: 'post'
  })
}

// 环境回收
export const envRecycle = (data) => {
  return axios.request({
    url: 'env/test_env/env_recycle',
    headers: {'token': store.state.user.token},
    data: {
      "envID": data['envID']
    },
    method: 'post'
  })
}


/* 获取所有测试环境 */
export const getEnv = (data) => {
  return axios.request({
    url: 'env/envs/',
    headers: {'token': store.state.user.token},
    params: data,
    method: 'get'
  })
}

/* 创建测试环境申请订单 */
export const createApplicationOrder = (data) => {
  return axios.request({
    url: 'env/orders/',
    headers: {'token': store.state.user.token},
    data: data,
    method: 'post'
  })
}

/* 创建测试环境申请订单 + 初始化 */
export const createApplicationOrderTest = (data) => {
  return axios.request({
    url: 'env/test_orders/',
    headers: {'token': store.state.user.token},
    data: data,
    method: 'post'
  })
}

/* 测试环境延期申请 */
export const delayEnv = (id, data) => {
  return axios.request({
    url: 'env/envs/' + id + '/action/',
    headers: {'token': store.state.user.token},
    data: data,
    method: 'post'
  })
}

/* 测试环境回收申请 */
export const recoverEnv = (id, data) => {
  return axios.request({
    url: 'env/envs/' + id + '/action/',
    headers: {'token': store.state.user.token},
    data: data,
    method: 'post'
  })
}

/* 测试环境初始化日志 */
export const getEnvLog = (data = {}) => {
  return axios.request({
    url: 'record/script_jobs/',
    headers: {'token': store.state.user.token},
    params: data,
    method: 'get'
  })
}

/* 测试环境下的应用 */
export const getEnvApp = (data) => {
  return axios.request({
    url: 'env/applications/',
    headers: {'token': store.state.user.token},
    params: data,
    method: 'get'
  })
}

/* 测试环境应用重启日志 */
export const getAppRestartLog = (sid) => {
  return axios.request({
    url: 'record/script_jobs/' + sid + '/',
    headers: {'token': store.state.user.token},
    method: 'get'
  })
}

/* 测试环境应用重启日志 */
export const getDockerState = (data) => {
  return axios.request({
    url: 'env/base_repo/docker_state/',
    headers: {'token': store.state.user.token},
    params: data,
    method: 'get'
  })
}


/*新增的方法对标spider*/
/* 初始化获取所有测试环境详情 */
export const getAllEnvInfo = (data) => {
  return spider_axios.request({
    url: '/spider/test_env_mgt/get_all_env_apply_info',
    params: data,
    method: 'get'
  })
}

// 获取环境「所有用途」列表。zt@2024-05-08
export const getSuiteUseAsList = (data) => {
  return spider_axios.request({
    url: '/spider/test_env_mgt/get_suite_use_as_list',
    params: data,
    method: 'get'
  })
}

export const updSuiteUseBind = (bind_id, use_as_id) => {
  return spider_axios.request({
    url: 'spider/test_env_mgt/upd_suite_use_bind',
    data: {
      'bind_id': bind_id,
      'use_as_id': use_as_id,
    },
    method: 'post'
  })
}
//查询用户是否有修改环境权限
export const getEditEnvAuth = (data) => {
  return spider_axios.request({
    url: 'spider/test_env_mgt/upd_suite_use_bind',
    params: data,
    method: 'get'
  })
}

// 获取「所有用途」环境列表。zt@2024-05-08
export const getAllTestingEnv = (data) => {
  return spider_axios.request({
    url: '/spider/test_env_mgt/get_all_testing_env',
    params: data,
    method: 'get'
  })
}

// 获取「人工测试」环境列表。zt@2024-05-06
export const getManualTestingEnv = (data) => {
  return spider_axios.request({
    url: '/spider/test_env_mgt/get_manual_testing_env',
    params: data,
    method: 'get'
  })
}

// 获取未申请的「人工测试」环境列表。zt@2024-05-07
export const getManualTestingEnvNotApply = (data) => {
  return spider_axios.request({
    url: '/spider/test_env_mgt/get_manual_testing_env_not_apply',
    params: data,
    method: 'get'
  })
}

// 获取「自动化测试」环境列表。zt@2024-05-07
export const getAutoTestingEnv = (data) => {
  return spider_axios.request({
    url: '/spider/test_env_mgt/get_auto_testing_env',
    params: data,
    method: 'get'
  })
}

// 获取「数据开发」环境列表。zt@2024-05-08
export const getDataDevelopmentEnv = (data) => {
  return spider_axios.request({
    url: '/spider/test_env_mgt/get_data_development_env',
    params: data,
    method: 'get'
  })
}

/* 初始化获取所有可选中复选框应用 */
export const getAllCanCheckboxApp = (data) => {
  return spider_axios.request({
    url: '/spider/test_env_mgt/get_all_can_checkbox_app',
    params: data,
    method: 'get'
  })
}
// 获取「测试环境发布模板」列表
export const getTemplateInfo = () => {
  return spider_axios.request({
    url: '/spider/test_env_mgt/get_template_info_api',
    params: {'template_is_active': 1},
    method: 'get'
  })
}
// 根据模板查询应用信息
export const getAppsByTemplate = (data) => {
  return spider_axios.request({
    url: '/spider/test_env_mgt/get_all_app_by_template',
    params: data,
    method: 'get'
  })
}
// 更新测试环境申请记录
export const updateTestEnvApplyInfo = (data) => {
  let d = new Date(data.invalid_at);
  let datetime = d.getFullYear() + '-' + (d.getMonth() + 1) + '-' + d.getDate() + ' ' + d.getHours() + ':' + d.getMinutes() + ':' + d.getSeconds();
  const info = new URLSearchParams();
  info.append('env_id', data.env_id);
  info.append('applicant', data.applicant);
  info.append('apply_reason', data.apply_reason);
  info.append('invalid_at', datetime);
  return spider_axios.request({
    url: '/spider/test_env_mgt/update_test_env_apply_info',
    params: info,
    method: 'post'
  })
}
// 更新模板信息
export const updateTemplateInfo = (data) => {
  let d = new Date(data.use_env_time)
  let datetime
  if (isNaN(d.getFullYear())) {
    datetime = null
  } else {
    datetime = d.getFullYear() + '-' + (d.getMonth() + 1) + '-' + d.getDate() + ' ' + d.getHours() + ':' + d.getMinutes() + ':' + d.getSeconds()
  }
  const info = new URLSearchParams()
  info.append('template_id', data.template_id)
  info.append('template_name', data.template_name)
  info.append('new_template_name', data.new_template_name)
  info.append('change_template_id', data.change_template_id)
  info.append('env_id', data.env_id)
  info.append('init_db', data.init_db)
  info.append('ccms_type', data.ccms_type)
  info.append('use_env_time', datetime)
  info.append('clean_cache', data.clean_cache)
  info.append('test_set_id', data.test_set_id)
  info.append('applicant', data.applicant)
  info.append('allSelectedMails', data.allSelectedMails)
  info.append('h5_list', data.h5_list)
  info.append('tms_list', data.tms_list)
  info.append('tp_list', data.tp_list)
  info.append('pa_list', data.pa_list)
  info.append('app_ops_list', data.app_ops_list)
  info.append('fpc_list', data.fpc_list)
  info.append('crm_list', data.crm_list)
  info.append('other_list', data.other_list)
  return spider_axios.request({
    url: '/spider/test_env_mgt/update_template_info',
    params: info,
    method: 'post'
  })
}
// 调用jenkins
export const callJenkins = (data) => {
  let d = new Date(data.use_env_time)
  let datetime
  if (isNaN(d.getFullYear())) {
    datetime = null
  } else {
    datetime = d.getFullYear() + '-' + (d.getMonth() + 1) + '-' + d.getDate() + ' ' + d.getHours() + ':' + d.getMinutes() + ':' + d.getSeconds()
  }
  const info = new URLSearchParams()
  info.append('env_id', data.env_id)
  info.append('env_name', data.env_value)
  info.append('init_db', data.init_db)
  info.append('biz_base_db_code', data.biz_base_db_code)
  info.append('bis_pipeline_id', data.bis_pipeline_id)
  info.append('ccms_type', data.ccms_type)
  info.append('use_env_time', datetime)
  info.append('clean_cache', data.clean_cache)
  info.append('test_set_id', data.test_set_id)
  info.append('h5_list', data.h5_list)
  info.append('tms_list', data.tms_list)
  info.append('tp_list', data.tp_list)
  info.append('pa_list', data.pa_list)
  info.append('app_ops_list', data.app_ops_list)
  info.append('fpc_list', data.fpc_list)
  info.append('crm_list', data.crm_list)
  info.append('other_list', data.other_list)
  info.append('need_mock_app_list', data.need_mock_app_list)
  return spider_axios.request({
    url: '/spider/test_env_mgt/call_jenkins',
    params: info,
    method: 'post'
  })
}

export const callUpdateJenkins = (data) =>{
  let d = new Date(data.use_env_time)
  let datetime
  if (isNaN(d.getFullYear())) {
    datetime = null
  } else {
    datetime = d.getFullYear() + '-' + (d.getMonth() + 1) + '-' + d.getDate() + ' ' + d.getHours() + ':' + d.getMinutes() + ':' + d.getSeconds()
  }
  const info = new URLSearchParams()
  info.append('env_id', data.env_id)
  info.append('env_name', data.env_value)
  info.append('init_db', data.init_db)
  info.append('biz_base_db_code', data.biz_base_db_code)
  info.append('bis_pipeline_id', data.bis_pipeline_id)
  info.append('ccms_type', data.ccms_type)
  info.append('use_env_time', datetime)
  info.append('clean_cache', data.clean_cache)
  info.append('test_set_id', data.test_set_id)
  info.append('h5_list', data.h5_list)
  info.append('tms_list', data.tms_list)
  info.append('tp_list', data.tp_list)
  info.append('pa_list', data.pa_list)
  info.append('app_ops_list', data.app_ops_list)
  info.append('fpc_list', data.fpc_list)
  info.append('crm_list', data.crm_list)
  info.append('other_list', data.other_list)
  info.append('need_mock_app_list', data.need_mock_app_list)
  return spider_axios.request({
    url: '/spider/test_env_mgt/call_update_jenkins',
    params: info,
    method: 'post'
  })
}

//查询jenkins日志地址
export const getJenkinsLog = (data) => {
  return spider_axios.request({
    url: '/spider/test_env_mgt/call_jenkins',
    params: data,
    method: 'get'
  })
}
/* 测试环境延期修改 */
export const updateTestEnv = (id, apply_user, update_time, apply_reason, invalid_at) => {
  const info = new URLSearchParams();
  info.append('env_id', id);
  info.append('apply_user', apply_user);
  info.append('update_time', update_time);
  info.append('apply_reason', apply_reason);
  info.append('invalid_at', invalid_at);
  return spider_axios.request({
    url: '/spider/test_env_mgt/delay_test_env',
    data: info,
    method: 'post'
  })
}
/* 测试重设时间重启应用 */
export const setTimeTestEnv = (suite_id,suite_code, data) => {
  let d = new Date(data);
  const info = new URLSearchParams();
  info.append('suite_id', suite_id);
  info.append('suite_code', suite_code);
  info.append('target_time', data);
  return spider_axios.request({
    url: '/spider/test_env_mgt/set_time_af_restart',
    data: info,
    method: 'post'
  })
}

/* 测试重设时间重启应用 */
export const k8sEnvRecover = (suite_code, operator) => {
  const info = new URLSearchParams();

  info.append('suite_code', suite_code);
  info.append('action', 'recycle');
  info.append('operator', operator);
  return spider_axios.request({
    url: '/spider/test_env_mgt/call_k8s_env_recover',
    data: info,
    method: 'post'
  })
}

/* 测试环境回收 */
export const recycleTestEnv = (id) => {
  const info = new URLSearchParams();
  info.append('env_id', id);
  return spider_axios.request({
    url: '/spider/test_env_mgt/recycle_test_env',
    data: info,
    method: 'post'
  })
}

