import axios from '@/libs/api.request'
import store from '../store'

export const getGitRopesApi = (req) => {
  // 返回一个promise对象
  return axios.request({
    url: 'git_iterative/git_repos_tree_api',
    headers: { 'token': store.state.user.token },
    method: 'get',
     params:{
      'sync_gitlab': req['sync_gitlab'],
    },
  })
}

export const getIterativeGitRopesApi = (req) => {
  // 返回一个promise对象
  return axios.request({
    url: 'git_iterative/git_repos_info_api',
    headers: { 'token': store.state.user.token },
    method: 'get',
     params:{
      'pipeline_id': req['pipeline_id'],
    },
  })
}

export const getGitGroupRopesApi = (req) => {
  // 返回一个promise对象
  return axios.request({
    url: 'git_iterative/git_group_repo_api',
    headers: { 'token': store.state.user.token },
    method: 'get',
     params:{
      'pipeline_id': req['pipeline_id'],
    },
  })
}

export const getGitlabMenberApi = (req) => {
  // 返回一个promise对象
  return axios.request({
    url: 'git_iterative/rsync_gitlab_members',
    headers: { 'token': store.state.user.token },
    method: 'get',
     params:{

    },
  })
}

export const setIterAppJdk = (data) => {
  return axios.request({
    url: 'git_iterative/set_iter_app_jdk',
    headers: { 'token': store.state.user.token },
    data: {
      'iterative_name': data['iterative_name'],
      'app_name': data['app_name'],
      'jdk_version': data['jdk_version']
    },
    method: 'put'
  })
};

export const getIterAppJdk = (data) => {
  return axios.request({
    url: 'git_iterative/get_iter_app_jdk',
    headers: { 'token': store.state.user.token },
    params: {
      'iterative_name': data['iterative_name'],
      'app_name': data['app_name'],
    },
    method: 'get'
  })
};
