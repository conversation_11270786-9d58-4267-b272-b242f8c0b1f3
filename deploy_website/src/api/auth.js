import axios from '@/libs/api.request'
import store from '../store'

// 根据角色名获取对应的列表
export const getRoleInfo = (data) => {
  return axios.request({
    params: {
      'role_name': data['role_name'],
      'page': data['current_page'],
      'page_size': data['page_size']
    },
    url: 'role/user_role',
    headers: { 'token': store.state.user.token },
    method: 'get'
  })
};

// 根据角色名获取对应的列表
export const getNoRoleInfo = (data) => {
  return axios.request({
    url: 'role/no_role_user',
    params: {
      'page': data['current_page'],
      'page_size': data['page_size']
    },
    headers: { 'token': store.state.user.token },
    method: 'get'
  })
};

// 获取角色列表
export const getRoleList = () => {
  return axios.request({
    url: 'role/role',
    headers: { 'token': store.state.user.token },
    method: 'get'
  })
};

// 删除选中用户的该页面角色
export const deleteRole = (data) => {
  return axios.request({
    url: 'role/user_role',
    params: {
      'role_name': data['role_name'],
      'username': data['username']
    },
    headers: { 'token': store.state.user.token },
    method: 'delete'
  })
};

// 更新个人角色
export const updateRole = (data) => {
  const _data = new URLSearchParams();
  _data.append('username', data['username']);
  _data.append('role_name', data['role_name']);
  return axios.request({
    url: 'role/user_role',
    data: _data,
    headers: { 'token': store.state.user.token },
    method: 'post'
  })
};

// 角色页面用户查询
export const searchUser = (data) => {
  return axios.request({
    url: 'role/user_role',
    params: {
      'username': data['username'],
      'page': data['page'],
      'page_size': data['page_size']
    },
    headers: { 'token': store.state.user.token },
    method: 'get'
  })
};

// 增加角色列表
export const addRoleList = (data) => {
    const _data = new URLSearchParams();
    _data.append('role_name', data['role_name']);
    return axios.request({
      url: 'role/role',
      data: _data,
      headers: { 'token': store.state.user.token },
      method: 'post'
    })
  };

// 删除角色列表
export const deleteRoleList = (data) => {
  return axios.request({
    url: 'role/role',
    params: {
      'role_name': data['role_name'],
    },
    headers: { 'token': store.state.user.token },
    method: 'delete'
  })
};


// ----------------- 角色分配 End -------------------

// 根据组名获取信息
export const getAuthGroupInfo = (data) => {
  return axios.request({
    params: {
      'role_name': data['role_name'],
      'page': data['current_page'],
      'page_size': data['page_size']
    },
    url: 'role/auth_group',
    headers: { 'token': store.state.user.token },
    method: 'get'
  })
};

// 获取权限组列表
export const getAuthGroupList = (data) => {
  return axios.request({
    url: 'role/auth_group',
    headers: { 'token': store.state.user.token },
    method: 'get'
  })
};

// 增加权限组列表
export const addAuthGroupList = (data) => {
  const _data = new URLSearchParams();
  _data.append('auth_group_name', data['auth_group_name']);
  return axios.request({
    url: 'role/auth_group',
    data: _data,
    headers: { 'token': store.state.user.token },
    method: 'post'
  })
};

// 删除权限组列表
export const deleteGroupList = (data) => {
  return axios.request({
    url: 'role/auth_group',
    params: {
      'auth_group_name': data['auth_group_name'],
    },
    headers: { 'token': store.state.user.token },
    method: 'delete'
  })
};

//
export const authGroupSearch = (data) => {
  return axios.request({
    url: 'role/auth_group',
    params: {
      'auth_group_name': data['auth_group_name'],
    },
    headers: { 'token': store.state.user.token },
    method: 'get'
  })
};

export const getApplist = () => {
  return axios.request({
    url: 'role/all_app',
    headers: { 'token': store.state.user.token },
    method: 'get'
  })
};

export const searchAppAuthGroup = (data) => {
  return axios.request({
    url: 'role/app_auth_group',
    params: {
      'auth_group_name': data['auth_group_name'],
    },
    headers: { 'token': store.state.user.token },
    method: 'get'
  })
};

// 更新权限组
export const groupUpdate = (data) => {
  const _data = new URLSearchParams();
  _data.append('app_name', data['app_name']);
  _data.append('auth_group_name', data['auth_group_name']);
  return axios.request({
    url: 'role/app_auth_group',
    data: _data,
    headers: { 'token': store.state.user.token },
    method: 'post'
  })
};


// ----------------- 权限分组 End -------------------

export const getRoleAuthGroupInfo = (data) => {
  return axios.request({
    params: {
      'role_name': data['role_name'],
      'auth_group_name': data['auth_group_name'],
      'page': data['page'],
    },
    url: 'role/role_auth_group',
    headers: { 'token': store.state.user.token },
    method: 'get'
  })
};

export const updateRoleAuthGroup = (data) => {
    const _data = new URLSearchParams();
    _data.append('role_name', data['role_name']);
    _data.append('auth_group_name', data['auth_group_name']);
    return axios.request({
      url: 'role/role_auth_group',
      data: _data,
      headers: { 'token': store.state.user.token },
      method: 'post'
    })
  };

export const deleteRoleAuthGroup = (data) => {
  return axios.request({
    url: 'role/role_auth_group',
    params: {
      'role_name': data['role_name'],
      'auth_group_name': data['auth_group_name']
    },
    headers: { 'token': store.state.user.token },
    method: 'delete'
  })
};

export const searchRole = (data) => {
  return axios.request({
    url: 'role/role_auth_group',
    params: {
      'role_name': data['role_name'],
    },
    headers: { 'token': store.state.user.token },
    method: 'get'
  })
};
