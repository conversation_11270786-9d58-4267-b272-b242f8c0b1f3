import axios from '@/libs/api.request'
import store from '../store'


export const getProdPublishData = (id) => {
  return axios.request({
    url: 'git_iterative/git_prod_api',
    headers: { 'token': store.state.user.token },
    params: { id },
    method: 'get'
  })
};

export const uatPublishOperate = (data) => {
  return axios.request({
    url: 'salt/git_uat_deploy_api',
    headers: { 'token': store.state.user.token },
    params: {
      'type': data['type'],
      'ip': data['ip'],
      'appName': data['appName'],
      'businessID': data['businessID'],
      'user': data['user'],
    },
    method: 'get'
  })
}

export const PublishCheck = (data) => {
  return axios.request({
    url: 'git_iterative/publish_check_api',
    headers: { 'token': store.state.user.token },
    params: {
      'appName': data['app_name'],
      'businessID': data['businessID'],
      'env': data['env'],
    },
    method: 'get'
  })
}

export const prodPublishOperate = (data) => {
  return axios.request({
    url: 'salt/git_prod_deploy_api',
    headers: { 'token': store.state.user.token },
    params: {
      'type': data['type'],
      'ip': data['ip'],
      'appName': data['appName'],
      'businessID': data['businessID'],
    },
    method: 'get'
  })
}

export const getGitRollbackStat = (app_name, ip) => {
  return axios.request({
    url: 'git_iterative/git_rollback_status',
    headers: { 'token': store.state.user.token },
    params: { "app_name": app_name, "ip": ip },
    method: 'get'
  })
}
