import axios from '@/libs/api.request'
import store from '../store'
import spider_axios from '@/libs/spider_api.request'

export const login = ({ userName, password }) => {
  const data = new URLSearchParams()
  data.append('userName', userName)
  data.append('password', password)
  return axios.request({
    url: 'api/login',
    data,
    timeout: 3000,
    method: 'post'
  })
}

export const spiderLogin = ({ userName, password }) => {
  const data = new URLSearchParams()
  data.append('username', userName)
  data.append('userName', userName)
  data.append('password', password)
  return spider_axios.request({
    url: 'spider/user/login/',
    data,
    timeout: 3000,
    method: 'post'
  })
}

export const spiderLoginOut = () => {
  return spider_axios.request({
    url: 'spider/user/login_out/',
    timeout: 3000,
    method: 'get'
  })
}

// 获取用户信息，控制用户权限可用 注释by scm
export const getUserInfo = (token) => {
  return axios.request({
    url: 'api/get_info',
    headers: { 'token': store.state.user.token },
    params: {
      token
    },
    method: 'get'
  })
}

export const logout = (token) => {
  return axios.request({
    url: 'logout',
    headers: { 'token': store.state.user.token },
    method: 'post'
  })
}

export const getUnreadCount = () => {
  return axios.request({
    url: 'message/count',
    method: 'get'
  })
}

export const getMessage = () => {
  return axios.request({
    url: 'message/init',
    method: 'get'
  })
}

export const getContentByMsgId = msg_id => {
  return axios.request({
    url: 'message/content',
    method: 'get',
    params: {
      msg_id
    }
  })
}

export const hasRead = msg_id => {
  return axios.request({
    url: 'message/has_read',
    method: 'post',
    data: {
      msg_id
    }
  })
}

export const removeReaded = msg_id => {
  return axios.request({
    url: 'message/remove_readed',
    method: 'post',
    data: {
      msg_id
    }
  })
}

export const restoreTrash = msg_id => {
  return axios.request({
    url: 'message/restore',
    method: 'post',
    data: {
      msg_id
    }
  })
}

export const getSpiderIp = () => {
  return spider_axios.request({
    url: 'spider/user/get_spider_ip/',
    method: 'get'
  })
}
