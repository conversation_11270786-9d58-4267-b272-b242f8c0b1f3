import axios from '@/libs/api.request'
import store from '../store'

export const createBranch = (req) => {
  // 返回一个promise对象
  return axios.request({
    url: 'git_iterative/git_analysis_branch_api',
    headers: { 'token': store.state.user.token },
    method: 'post',
    data: {
      'branch_name': req['branch_name'],
      'branch_style': req['branch_style'],
      'deadline': req['deadline'],
      'desc': req['desc'],
      'project_list': req['project_list'],
    },
  })
}

export const addSys = (req) => {
  // 返回一个promise对象
  return axios.request({
    url: 'git_iterative/git_add_repo_api',
    headers: { 'token': store.state.user.token },
    method: 'post',
    data: {
      'repos_list': req['repos_list'],
      'pipeline_id': req['pipeline_id'],
    },
  })
}


export const delBranch = (req) => {
  // 返回一个promise对象
  return axios.request({
    url: 'git_iterative/git_del_branch_api',
    headers: { 'token': store.state.user.token },
    method: 'post',
    data: {
      'gitRepo': req['gitRepo'],
      'pipeline_id': req['pipeline_id'],
       'proposer': req['proposer'],
    },
  })
}
