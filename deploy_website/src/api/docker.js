import axios from '@/libs/api.request'
import store from '../store'

export const containerHandle = (ACTION_TYPE, ENV_NAME, IMG_NAME_LIST, DATE_VALUE, WORKSPACE) => {
  const info = new URLSearchParams()
  info.append('ACTION_TYPE', ACTION_TYPE)
  info.append('ENV_NAME', ENV_NAME)
  info.append('IMG_NAME_LIST', IMG_NAME_LIST)
  info.append('DATE_VALUE', DATE_VALUE)
  info.append('WORKSPACE', '/Users/<USER>/docker')

  return axios.request({
    url: '/docker/container_handle',
    headers: { 'token': store.state.user.token },
    data: info,
    method: 'post'
  })
};

export const envInit = (ACTION_TYPE, ENV_NAME, IMG_NAME_LIST, TP_ENV, IS_UPDATE_CONF, WORKSPACE) => {
  const info = new URLSearchParams()
  info.append('ACTION_TYPE', ACTION_TYPE)
  info.append('ENV_NAME', ENV_NAME)
  info.append('IMG_NAME_LIST', IMG_NAME_LIST)
  info.append('TP_ENV', TP_ENV)
  info.append('IS_UPDATE_CONF', IS_UPDATE_CONF)
  info.append('WORKSPACE', '/Users/<USER>/docker')

  return axios.request({
    url: '/docker/env_init',
    headers: { 'token': store.state.user.token },
    data: info,
    method: 'post'
  })
};
