import axios from '@/libs/api.request'
import store from '../store'

export const getTableData = () => {
  return axios.request({
    url: 'git_iterative/iterative_list_api',
    headers: {'token': store.state.user.token},
    method: 'get'
  })
};

export const getDragList = () => {
  return axios.request({
    url: 'get_drag_list',
    headers: {'token': store.state.user.token},
    method: 'get'
  })
};

export const getInstalledList = () => {
  return axios.request({
    url: 'get_Installed_list',
    headers: {'token': store.state.user.token},
    method: 'get'
  })
};

export const getIterativeSyslist = () => {
  return axios.request({
    url: 'get_iterative_syslist',
    headers: {'token': store.state.user.token},
    method: 'get'
  })
};

export const errorReq = () => {
  return axios.request({
    url: 'error_url',
    headers: {'token': store.state.user.token},
    method: 'post'
  })
};

/*export const saveErrorLogger = info => {
  return axios.request({
    url: 'save_error_logger',
    headers: { 'token': store.state.user.token },
    data: info,
    method: 'post'
  })
};*/

export const uploadImg = formData => {
  return axios.request({
    url: 'image/upload',
    headers: {'token': store.state.user.token},
    data: formData
  })
};

export const getOrgData = () => {
  return axios.request({
    url: 'get_org_data',
    headers: {'token': store.state.user.token},
    method: 'get'
  })
};

export const h5publishInfo = (data) => {
  return axios.request({
    url: 'data_statistics/h5_publish_info_api',
    headers: {'token': store.state.user.token},
    params: {
      'is_only_full': data['is_only_full'],
      'app_id': data['app_id'],
      'date_range': data['date_range']
    },
    method: 'get'
  })
};

export const h5eslintInfo = (data) => {
  return axios.request({
    url: 'data_statistics/h5_eslint_info_api',
    headers: {'token': store.state.user.token},
    params: {
      'versionSearch': data['versionSearch'],
      "desSearch": data['desSearch'],
      "date_range": data['date_range'],
      "ruleSearch": data['ruleSearch'],
      "page": data['page'],
      "pageSize": data['pageSize']
    },
    method: 'get'
  })
};

export const h5StatChange = (data) => {
  return axios.request({
    url: 'data_statistics/h5_stat_change_api',
    headers: {'token': store.state.user.token},
    params: {
      'ver': data['ver'],
      'status': data['status'],
    },
    method: 'get'
  })
};

export const h5publishHistory = () => {
  return axios.request({
    url: 'h5publishHistory',
    headers: {'token': store.state.user.token},
    method: 'get'
  })
};

export const getUatPublishData = (id) => {
  return axios.request({
    url: 'emergency_service/emergency_uat_api',
    headers: {'token': store.state.user.token},
    params: {id},
    method: 'get'
  })
};

export const getProdHistoryData = (id, appName) => {
  return axios.request({
    url: 'emergency_service/emergency_prod_stat_api',
    headers: {'token': store.state.user.token},
    params: {id, appName},
    method: 'get'
  })
};

export const getDockerImg = (team) => {
  return axios.request({
    url: 'get_docker_img',
    headers: {'token': store.state.user.token},
    data: {"team": team},
    method: 'get'
  })
}

export const compileHistory = (businessID) => {
  // const data = new URLSearchParams()
  // data.append('businessID', businessID)
  return axios.request({
    url: 'compileHistory',
    headers: {'token': store.state.user.token},
    data: {"businessID": businessID},
    method: 'post'
  })
};


export const execHistory = (businessID, compileEnv = "prod") => {
  const data = new URLSearchParams()
  data.append('businessID', businessID)
  data.append('compileEnv', compileEnv)
  return axios.request({
    url: '/common_service/get_exec_history',
    headers: {'token': store.state.user.token},
    data: data,
    method: 'post'
  })
};

export const resetEvent = (businessID, compileEnv = "prod") => {
  const data = new URLSearchParams()
  data.append('businessID', businessID)
  data.append('compileEnv', compileEnv)
  return axios.request({
    url: '/common_service/reset',
    headers: {'token': store.state.user.token},
    data: data,
    method: 'get'
  })
}

export const getFullLog = (businessID, buildID, compileEnv = "prod") => {
  return axios.request({
    url: '/common_service/get_full_log',
    headers: {'token': store.state.user.token},
    params: {businessID, buildID, compileEnv},
    method: 'get'
  })
}

export const getAppVersionInfo = (type, business_id) => {
  return axios.request({
    url: '/common_service/app_version_info',
    headers: {'token': store.state.user.token},
    params: {type: type, business_id: business_id},
    method: 'get'
  })
}

export const getAgentVersionInfo = (agent_name) => {
  return axios.request({
    url: '/spider/iter_mgt/get_agent_branch_version',
    headers: {'token': store.state.user.token},
    params: {module_name: agent_name},
    method: 'get'
  })
}

export const getAppSqlCheckSuiteCode = (app_name) => {
  return axios.request({
    url: '/spider/iter_mgt/get_app_sql_check_suite_code',
    params: {app_name: app_name},
    method: 'get'
  })
}

export const saveSqlCheckSuite = (app_name, check_suite_code) => {
  const data = new URLSearchParams()
  data.append('app_name', app_name)
  data.append('check_suite_code', check_suite_code)
  return axios.request({
    url: '/spider/iter_mgt/save_sql_check_suite/',
    data: data,
    method: 'post'
  })
}

export const getTestSuiteCode = (app_name) => {
  return axios.request({
    url: '/spider/iter_mgt/get_test_suite_code',
    params: {app_name: app_name},
    method: 'get'
  })
}

export const checkAppSqlCheckSuiteApi = (app_name) => {
  return axios.request({
    url: '/spider/iter_mgt/check_app_sql_check_suite',
    params: {app_name: app_name},
    method: 'get'
  })
}
