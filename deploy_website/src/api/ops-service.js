import axios from '@/libs/api.request'
import store from '../store'

export const getPublishStatData = () => {
  return axios.request({
    url: 'emergency_service/publish_stat_api',
    headers: { 'token': store.state.user.token },
    method: 'get'
  })
};

export const setPublishStatData = (data) => {
  return axios.request({
    url: 'emergency_service/publish_stat_api',
    headers: { 'token': store.state.user.token },
    data: {
      'pk': data['pk']
    },
    method: 'put'
  })
};

export const getProdNodeData = () => {
  return axios.request({
    url: 'common_service/prod_app_info_api',
    headers: { 'token': store.state.user.token },
    method: 'get'
  })
};

export const getProdAppData = (data) => {
  return axios.request({
    url: 'common_service/prod_app_api',
    headers: { 'token': store.state.user.token },
    params: {
      'search': data['search']
    },
    method: 'get'
  })
};

export const chgProdAppGroup = (data) => {
  return axios.request({
    url: 'common_service/prod_app_api',
    headers: { 'token': store.state.user.token },
    data: {
      'appName': data['appName'],
      'ip': data['ip'],
      'toGroup': data['toGroup']
    },
    method: 'put'
  })
};

export const getProdGroup = (data) => {
  return axios.request({
    url: 'common_service/prod_group_info_api',
    headers: { 'token': store.state.user.token },
    params: {
      'appName': data['appName']
    },
    method: 'get'
  })
};

export const addProdGroup = (data) => {
  return axios.request({
    url: 'common_service/prod_group_info_api',
    headers: { 'token': store.state.user.token },
    data: {
      'appName': data['appName'],
      'group_name': data['group_name'],
    },
    method: 'post'
  })
};

export const setProdGroup = (data) => {
  return axios.request({
    url: 'common_service/prod_group_info_api',
    headers: { 'token': store.state.user.token },
    data: {
      'appName': data['appName'],
      'cur_name': data['cur_name'],
      'new_name': data['new_name'],
    },
    method: 'put'
  })
};

export const delProdGroup = (data) => {
  return axios.request({
    url: 'common_service/prod_group_info_api',
    headers: { 'token': store.state.user.token },
    params: {
      'appName': data['appName'],
      'group_name': data['group_name'],
    },
    method: 'delete'
  })
};

export const setProdNodeData = (data) => {
  return axios.request({
    url: 'common_service/prod_app_info_api',
    headers: { 'token': store.state.user.token },
    data: {
      'pk': data['pk'],
      'group_name': data['group_name'],
    },
    method: 'put'
  })
};

export const delProdNodeData = (pk) => {
  return axios.request({
    url: 'common_service/prod_app_info_api',
    headers: { 'token': store.state.user.token },
    params: { pk },
    method: 'delete'
  })
};

export const getScriptManagementData = () => {
  return axios.request({
    url: 'script_management/index_api',
    headers: { 'token': store.state.user.token },
    method: 'get'
  })
};

export const addScriptManagementData = (data) => {
  return axios.request({
    url: 'script_management/index_api',
    headers: { 'token': store.state.user.token },
    data: {
      'id': data['id'],
      'ip': data['ip'],
      'ft': data['ft'],
      'cmd': data['cmd'],
      'mod': data['mod'],
    },
    method: 'post'
  })
};

export const setScriptManagementData = (data) => {
  return axios.request({
    url: 'script_management/index_api',
    headers: { 'token': store.state.user.token },
    data: {
      'id': data['id'],
      'ip': data['ip'],
      'ft': data['ft'],
      'cmd': data['cmd'],
      'mod': data['mod'],
    },
    method: 'put'
  })
};

export const SimulateSQLExecuted = (data) => {
  return axios.request({
    url: 'emergency_service/simulate_sql_executed',
    headers: { 'token': store.state.user.token },
    params: {
      'business_id': data['business_id'],
    },
    method: 'get'
  })
};

export const ReleaseSQLExecuted = (data) => {
  return axios.request({
    url: 'emergency_service/release_sql_executed',
    headers: { 'token': store.state.user.token },
    params: {
      'business_id': data['business_id'],
    },
    method: 'get'
  })
};

export const getOpsOperateData = (data) => {
  return axios.request({
    url: 'common_service/ops_operate_api',
    headers: { 'token': store.state.user.token },
    params: {
      'app_name': data['app_name']
    },
    method: 'get'
  })
};

export const getOpsOperateCommand = (data) => {
  return axios.request({
    url: 'common_service/ops_operate_command',
    headers: { 'token': store.state.user.token },
    params: {
      'app_name': data['app_name'],
      'ip': data['ip']
    },
    method: 'get'
  })
};

export const saveOpsOperateCommand = (data) => {
  return axios.request({
    url: 'common_service/ops_operate_command',
    headers: { 'token': store.state.user.token },
    data: {
      'minion_id': data['minion_id'],
      'opt_type': data['opt_type'],
      'exec_cmd': data['exec_cmd'],
      'app_name': data['app_name'],
      'salt_func': data['salt_func'],
    },
    method: 'post'
  })
};

export const doOpsOperate = (data) => {
  return axios.request({
    url: 'common_service/ops_operate_api',
    headers: { 'token': store.state.user.token },
    data: {
      'ips': data['ips'],
      'app_name': data['app_name'],
      'optType': data['optType'],
    },
    method: 'post'
  })
};

export const getPomParseData = (data) => {
  return axios.request({
    url: 'common_service/pom_parse',
    headers: { 'token': store.state.user.token },
    params: {
      'team': data['team'],
      'trunk_path': data['trunk_path']
    },
    method: 'get'
  })
};

export const savePomParseData = (data) => {
  return axios.request({
    url: 'common_service/pom_parse',
    headers: { 'Content-Type': 'application/json' ,'token': store.state.user.token },
    data: JSON.stringify(data),
    method: 'post'
  })
};

export const getAppPublishHistory = () => {
  return axios.request({
    url: 'data_statistics/publish_history',
    headers: { 'token': store.state.user.token },
    method: 'get'
  })
};

export const doZaibeiUpdate = (data) => {
  return axios.request({
    url: 'common_service/zaibei_update',
    headers: { 'token': store.state.user.token },
    data: {
      'app_name': data['app_name'],
    },
    method: 'post'
  })
};

export const getGroupIpsData = (data) => {
  return axios.request({
    url: 'emergency_service/get_group_ips_api',
    headers: { 'token': store.state.user.token },
    params: {
      'app_name': data['app_name'],
      'group_name': data['group_name']
    },
    method: 'get'
  })
};
