import axios from '@/libs/api.request'
import store from '../store'

export const getPipelineData = () => {
  return axios.request({
    url: 'emergency_service/pipeline_api',
    headers: { 'token': store.state.user.token },
    method: 'get'
  })
};

export const getVerInfoData = (id) => {
  return axios.request({
    url: 'emergency_service/emergency_ver_info_query_api',
    headers: { 'token': store.state.user.token },
    params: { id },
    method: 'get'
  })
};

export const addEmergencyBranch = (data) => {
  return axios.request({
    url: 'emergency_service/create_emergency_branch_api',
    headers: { 'token': store.state.user.token },
    data: {
      'id': data['id'],
      'des': data['des'],
      'app': data['app'],
      'operator': data['operator'],
    },
    method: 'post'
  })
};

// 创建迭代分支

export const createIterativeBranch = (data) => {
  return axios.request({
    url: 'pipeline/release_ver_info/create_app_branch',
    headers: { 'token': store.state.user.token },
    data: {
      'businessID': data['id'],
      'appList': data['app'],
    },
    method: 'put'
  })
};
export const getUatPublishData = (id) => {
  return axios.request({
    url: 'emergency_service/emergency_uat_api',
    headers: { 'token': store.state.user.token },
    params: { id },
    method: 'get'
  })
};

export const uatPublishOperate = (data) => {
  return axios.request({
    url: 'salt/uat_deploy_api',
    headers: { 'token': store.state.user.token },
    params: {
      'type': data['type'],
      'ip': data['ip'],
      'appName': data['appName'],
      'businessID': data['businessID'],
      'user': data['user'],
    },
    method: 'get'
  })
}

export const getProdPublishData = (id) => {
  return axios.request({
    url: 'emergency_service/emergency_prod_api',
    headers: { 'token': store.state.user.token },
    params: { id },
    method: 'get'
  })
};

export const getProdHistoryData = (id, appName) => {
  return axios.request({
    url: 'emergency_service/emergency_prod_stat_api',
    headers: { 'token': store.state.user.token },
    params: { id, appName },
    method: 'get'
  })
};

export const prodPublishOperate = (data) => {
  return axios.request({
    url: 'salt/prod_deploy_api',
    headers: { 'token': store.state.user.token },
    params: {
      'type': data['type'],
      'ip': data['ip'],
      'appName': data['appName'],
      'businessID': data['businessID'],
    },
    method: 'get'
  })
}

export const getBusinessCreator = (data) => {
  return axios.request({
    url: 'emergency_service/business_creator',
    headers: { 'token': store.state.user.token },
    params: {
      'business_id': data['business_id']
    },
    method: 'get'
  })
}

export const createIterativeAPI = (data) => {
  return axios.request({
    url: 'pipeline/release_ver_info/create_interative',
    headers: { 'token': store.state.user.token },
    params: {
      'businessID': data['businessID'],
    },
    data: {
      'featureTeam': data['featureTeam'],
      'businessID': data['businessID'],
      'description': data['description']
    },
    method: 'put'
  })
}

export const linkBranchAPI = (data) => {
  return axios.request({
    url: 'emergency_service/link_branch_api',
    headers: { 'token': store.state.user.token },
    data: {
      'featureTeam': data['featureTeam'],
      'businessID': data['businessID'],
      'svnPath': data['svnPath'],
      'description': data['description']
    },
    method: 'post'
  })
}

export const getIdAboutMe = (operator) => {
  return axios.request({
    url: 'emergency_service/get_id_about_me',
    headers: { 'token': store.state.user.token },
    params: {"operator": operator},
    method: 'get'
  })
}

export const getSVNRollbackStat = (app_name, ip) => {
  return axios.request({
    url: 'common_service/svn_rollback_status',
    headers: { 'token': store.state.user.token },
    params: {"app_name": app_name, "ip": ip},
    method: 'get'
  })
}
