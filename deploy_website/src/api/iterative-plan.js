import axios from '@/libs/api.request'
import store from '../store'

export const getIterativePlan = (data) => {
  return axios.request({
    url: 'emergency_service/iterative_plan',
    headers: { 'token': store.state.user.token },
    params: {
      'businessID': data['businessID']
    },
    method: 'get'
  })
}

export const editIterativePlan = (data) => {
  return axios.request({
    url: 'emergency_service/iterative_plan',
    headers: { 'token': store.state.user.token },
    data: {
      'businessID': data['businessID'],
      'date': data['date'],
      'time': data['time'],
      'sql_content': data['sql_content'],
      'release_notice': data['release_notice'],
      'release_description': data['release_description'],
      'receivers': data['receivers'],
    },
    method: 'post'
  })
}

export const setAppConfig = (data) => {
  return axios.request({
    url: 'emergency_service/iterative_plan',
    headers: { 'token': store.state.user.token },
    data: {
      'business_id': data['business_id'],
      'app_name': data['app_name'],
      'app_config': data['app_config'],
    },
    method: 'put'
  })
}

export const getGitDiff = (data) => {
  return axios.request({
    url: 'emergency_service/git_diff_api',
    headers: { 'token': store.state.user.token },
    params: {
      'business_id': data['business_id'],
      'app_name': data['app_name'],
      'app_git': data['app_git']
    },
    method: 'get'
  })
}

export const getEmailAddresses = () => {
  return axios.request({
    url: 'emergency_service/email_addresses',
    headers: { 'token': store.state.user.token },
    method: 'get'
  })
}

export const getEmailPreview = (data) => {
  return axios.request({
    url: 'emergency_service/email_preview',
    headers: { 'token': store.state.user.token },
    params: {
      'business_id': data['business_id'],
      'stage': data['stage'],
    },
    method: 'get'
  })
}


export const sendEmailNotify = (data) => {
  return axios.request({
    url: 'emergency_service/email_notify',
    headers: { 'token': store.state.user.token },
    data: {
      'addresses': data['addresses'],
      'business_id': data['business_id'],
      'stage': data['stage'],
    },
    method: 'post'
  })
}

export const saveEmailAddress = (data) => {
  return axios.request({
    url: 'emergency_service/email_addresses',
    headers: { 'token': store.state.user.token },
    data: {
      'addresses': data['addresses'],
      'business_id': data['business_id'],
    },
    method: 'post'
  })
}
