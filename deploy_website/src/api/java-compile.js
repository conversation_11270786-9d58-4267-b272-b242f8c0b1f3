import store from '../store'
import axios from '@/libs/api.request'

// 获取用户信息，控制用户权限可用 注释by scm
export const getCompileStatus = (token) => {
  return axios.request({
    url: 'get_compile_status',
    headers: { 'token': store.state.user.token },
    params: {
      token
    },
    method: 'get'
  })
}

export const compileHandle = (businessID, appList) => {
  const info = new URLSearchParams()
  info.append('businessID', businessID)
  info.append('appList', appList)

  return axios.request({
    url: '/common_service/java_compile',
    headers: { 'token': store.state.user.token },
    data: info,
    method: 'post'
  })
}


export const gitcompileHandle = (appList, compile_env) => {
  const info = new URLSearchParams()
  info.append('businessID', store.state.businessID)
  info.append('appList', appList)
  info.append('compile_env', compile_env)

  return axios.request({
    url: '/git_iterative/git_java_compile_api',
    headers: { 'token': store.state.user.token },
    data: info,
    method: 'post'
  })
}

export const isParentPom = (appList) => {
  const info = new URLSearchParams()
  info.append('appList', appList)
  return axios.request({
    url: '/common_service/is_parent_pom',
    headers: { 'token': store.state.user.token },
    data: info,
    method: 'post'
  })
}

export const resetProcess = (businessID,compileEnv="prod") => {
  const info = new URLSearchParams()
  info.append('businessID', businessID)
  info.append('compileEnv', compileEnv)
  return axios.request({
    url: '/common_service/reset_process',
    headers: { 'token': store.state.user.token },
    data: info,
    method: 'post'
  })
}

export const getCompileEditList = (businessID) => {
  return axios.request({
    url: '/emergency_service/get_compile_edit_list',
    headers: { 'token': store.state.user.token },
    params: { businessID },
    method: 'get'
  })
}

export const getGitCompileTree = () => {
  return axios.request({
    url: '/git_iterative/git_build_tree_api',
    headers: { 'token': store.state.user.token },
    params:  {'pipeline_id':store.state.businessID},
    method: 'get'
  })
}

export const getGitCompileCheck = () => {
  return axios.request({
    url: '/git_iterative/git_java_compile_check_api',
    headers: { 'token': store.state.user.token },
    params:  {'businessID':store.state.businessID},
    method: 'get'
  })
}

export const getJavaCompileInfo = (page, compile_id=null) => {
  let url = '/data_statistics/java_compile_info_api'
  if (compile_id) {
    url = '/data_statistics/java_compile_info_api/' + compile_id
  }
  return axios.request({
    url: url,
    headers: { 'token': store.state.user.token },
    params: { page },
    method: 'get'
  })
}

export const getH5CompileInfo = (page) => {
  let url = '/data_statistics/h5_compile_info_api'
  return axios.request({
    url: url,
    headers: { 'token': store.state.user.token },
    params: { page },
    method: 'get'
  })
}

export const getH5PackageInfo = (page) => {
  let url = '/data_statistics/h5_package_info_api'
  return axios.request({
    url: url,
    headers: { 'token': store.state.user.token },
    params: { page },
    method: 'get'
  })
}

export const getH5Statistics = (date) => {
  let url = '/data_statistics/h5_compile_info_statistics_api'
  return axios.request({
    url: url,
    headers: { 'token': store.state.user.token },
    params: { "date":date,},
    method: 'get'
  })
}
