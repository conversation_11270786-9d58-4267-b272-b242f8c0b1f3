import axios from '@/libs/api.request'
import store from '../store'

export const getIterativePlan = (data) => {
  return axios.request({
    url: 'git_iterative/git_iterative_plan_api',
    headers: { 'token': store.state.user.token },
    params: {
      'businessID': data['businessID']
    },
    method: 'get'
  })
}

export const editIterativePlan = (data) => {
  return axios.request({
    url: 'git_iterative/git_iterative_plan_api',
    headers: { 'token': store.state.user.token },
    data: {
      'businessID': data['businessID'],
      'date': data['date'],
      'time': data['time'],
      'sql_content': data['sql_content'],
      'release_notice': data['release_notice'],
      'release_description': data['release_description'],
      'receivers': data['receivers'],
    },
    method: 'post'
  })
}

export const setAppConfig = (data) => {
  return axios.request({
    url: 'git_iterative/git_iterative_plan_api',
    headers: { 'token': store.state.user.token },
    data: {
      'business_id': data['business_id'],
      'app_name': data['app_name'],
      'app_config': data['app_config'],
    },
    method: 'put'
  })
}

export const getEmailPreview = (data) => {
  return axios.request({
    url: 'git_iterative/git_email_preview',
    headers: { 'token': store.state.user.token },
    params: {
      'business_id': data['business_id'],
      'stage': data['stage'],
    },
    method: 'get'
  })
}

export const sendEmailNotify = (data) => {
  return axios.request({
    url: 'git_iterative/git_email_notify',
    headers: { 'token': store.state.user.token },
    data: {
      'addresses': data['addresses'],
      'business_id': data['business_id'],
      'stage': data['stage'],
    },
    method: 'post'
  })
}
