import Main from '@/components/main'
import parentView from '@/components/parent-view'

/**
 * iview-admin中meta除了原生参数外可配置的参数:
 * meta: {
 *  title: { String|Number|Function }
 *         显示在侧边栏、面包屑和标签栏的文字
 *         使用'{{ 多语言字段 }}'形式结合多语言使用，例子看多语言的路由配置;
 *         可以传入一个回调函数，参数是当前路由对象，例子看动态路由和带参路由
 *  hideInBread: (false) 设为true后此级路由将不会出现在面包屑中，示例看QQ群路由配置
 *  hideInMenu: (false) 设为true后在左侧菜单不会显示该页面选项
 *  notCache: (false) 设为true后页面在切换标签后不会缓存，如果需要缓存，无需设置这个字段，而且需要设置页面组件name属性和路由配置的name一致
 *  access: (null) 可访问该页面的权限数组，当前路由设置的权限会影响子路由
 *  icon: (-) 该页面在左侧菜单、面包屑和标签导航处显示的图标，如果是自定义图标，需要在图标名称前加下划线'_'
 *  beforeCloseName: (-) 设置该字段，则在关闭当前tab页时会去'@/router/before-close.js'里寻找该字段名对应的方法，作为关闭前的钩子函数
 * }
 */

export default [
    {
        path: '/login',
        name: 'login',
        meta: {
            title: 'Login - 登录',
            hideInMenu: true
        },
        component: () => import('@/view/login/login.vue')
    },
    {
        path: '/',
        name: '_home',
        redirect: '/home',
        component: Main,
        meta: {
            hideInMenu: true,
            notCache: true
        },
        children: [
            {
                path: '/home',
                name: 'home',
                meta: {
                    hideInMenu: true,
                    title: '首页',
                    notCache: true,
                    icon: 'md-home'
                },
                component: () => import('@/view/components/home/<USER>')
            }
        ]
    },
    // 跳转宙斯 zt@2021-12-20
    {
        path: '/goto_zeus',
        name: 'goto_zeus',
        meta: {
            icon: 'ios-redo-outline',
            notCache: true,
            title: '跳转宙斯'
        },
        component: () => open('http://zeus.howbuy.pa/')
    },
    {
        path: '/personal',
        name: 'personal',
        meta: {
            icon: 'md-person',
            notCache: true,
            title: '个人面板'
        },
        component: Main,
        children: [
            {
                path: 'quality',
                name: 'quality',
                meta: {
                    icon: 'ios-paper-outline',
                    title: '我的质量报告'
                },
                component: () => import('@/view/personal/quality/index.vue')
            },
            {
                path: 'effect',
                name: 'effect',
                meta: {
                    icon: 'ios-paper-outline',
                    title: '我的研效'
                },
                component: () => import('@/view/personal/effect/index.vue')
            },
            {
                path: 'clipboard',
                name: 'clipboard',
                meta: {
                    icon: 'ios-paper-plane-outline',
                    title: '我的待办'
                },
                component: () => import('@/view/personal/clipboard/index.vue')
            }
        ]
    },
    {
        path: '/quality_detail',
        name: 'quality_detail',
        meta: {
            hideInMenu: true,
            notCache: true
        },
        component: () => import('@/view/personal/quality_detail/index.vue')
    },
    {
        path: '/quality_detail_email',
        name: 'quality_detail_email',
        meta: {
            hideInMenu: true,
            notCache: true
        },
        component: () => import('@/view/personal/quality_detail_emial/index.vue')
    },
    {
        path: '/git_components',
        name: 'pipeline_service',
        meta: {
            icon: 'logo-buffer',
            notCache: true,
            title: '研发迭代管理'
        },
        component: Main,
        children: [
            // {
            //   path: 'my_pipeline',
            //   name: 'my_pipeline',
            //   meta: {
            //     icon: 'md-walk',
            //     title: '我的流水线'
            //   },
            //   component: () => import('@/spider-view/iter-mgt/my-pipeline.vue')
            // },
            {
                path: 'iter_apply',
                name: 'iter_apply',
                meta: {
                    icon: 'md-git-branch',
                    title: '分支申请'
                },
                component: () => import('@/spider-view/iter-mgt/iter-apply.vue')
            },
            {
                path: 'iter_list',
                name: 'iter_list',
                meta: {
                    icon: 'md-list',
                    title: '迭代列表',
                    keepAlive: true
                },
                component: () => import('@/spider-view/iter-mgt/iter-list.vue')
            },
            // {
            //   path: 'iter_publish_plan_config',
            //   name: 'iter_publish_plan_config',
            //   meta: {
            //     hideInMenu: false,
            //     notCache: true,
            //     icon: 'md-flower',
            //     title: '发布计划配置'
            //   },
            //   component: () => import('@/spider-components/spider-iter-publish-plan/iter-publish-plan-config.vue')
            // },

            {
                path: 'iter_publish_plan_detail',
                name: 'iter_publish_plan_detail',
                meta: {
                    hideInMenu: true,
                    notCache: true,
                    icon: 'md-list',
                    title: '发布计划详情'
                },
                component: () => import('@/spider-components/spider-iter-publish-plan/iter-publish-plan-detail.vue')
            },
            {
                path: 'pipeline_page',
                meta: {
                    // hideInMenu: true,
                    icon: 'md-trending-up',
                    notCache: true,
                    title: '流水线发布'
                },
                name: 'pipeline_page',
                // component: (resolve) => require(['@/view/i18n/i18n-page.vue'],resolve),
                component: () => import('@/spider-view/spider_pipeline_service.vue'),
                props: route => ({
                    iteration_id: route.params.iterative_name,
                    iterative_type: route.params.iterative_type
                })
            },
            // {
            //     path: 'mobile_pipeline',
            //     name: 'mobile_pipeline',
            //     meta: {
            //         icon: 'md-paw',
            //         title: '移动端流水线'
            //         // beforeCloseName: 'before_close_normal'
            //     },
            //     component: () => import('@/spider-view/ci-cd-mgt/mobile/mobile'),
            //     props: route => ({ project_group: route.query.project_group, branch_name: route.query.branch_name })
            // },
            {
                path: 'mobile_pipeline',
                name: 'h5_mobile_pipeline',
                meta: {
                    icon: 'md-paw',
                    title: '移动端流水线'
                    // beforeCloseName: 'before_close_normal'
                },
                component: () => import('@/spider-view/ci-cd-mgt/mobile_h5/index'),
                props: route => ({ project_group: route.query.project_group, branch_name: route.query.branch_name })
            },
            {
                path: 'app_mobile_pipeline',
                name: 'app_mobile_pipeline',
                meta: {
                    icon: 'md-paw',
                    title: '移动端流水线'
                    // beforeCloseName: 'before_close_normal'
                },
                component: () => import('@/spider-view/ci-cd-mgt/mobile_app/index'),
                props: route => ({ project_group: route.query.project_group, branch_name: route.query.branch_name })
            },
            {
                path: 'python_pipeline',
                name: 'python_pipeline',
                meta: {
                    icon: 'md-paw',
                    title: 'Python工程'
                },
                component: () => import('@/spider-view/ci-cd-mgt/python_pipeline/index'),
                props: route => ({ project_group: route.query.project_group, branch_name: route.query.branch_name })
            },
            {
                path: 'iter_quality_report',
                name: 'iter_quality_report',
                meta: {
                    hideInMenu: false,
                    notCache: true,
                    icon: 'ios-stats-outline',
                    title: '迭代质量报告'
                },
                component: () => import('@/spider-components/spider-iter-quality-report/iter-quality-report.vue')
            },
            {
                path: 'app_apply',
                name: 'app_apply',
                meta: {
                    icon: 'md-list',
                    title: '制品库列表'
                },
                component: () => import('@/spider-view/app-mgt/app-apply.vue')
            },
            {
                path: 'srv_pipeline_guide',
                name: 'srv_pipeline_guide',
                meta: {
                    icon: 'md-pulse',
                    title: '服务端流水线指南'
                },
                component: () => import('@/spider-view/help-mgt/srv_pipeline_guide.vue')
            },
            {
                path: 'app_pipeline_guide',
                name: 'app_pipeline_guide',
                meta: {
                    icon: 'md-pulse',
                    title: '移动端流水线指南'
                },
                component: () => import('@/spider-view/help-mgt/app_pipeline_guide.vue')
            },
            {
                path: 'speedy_sql',
                name: 'speedy_sql',
                meta: {
                    icon: 'md-git-branch',
                    title: 'sql快速通道'
                },
                component: () => import('@/spider-view/test-env/speedy_sql.vue')
            }
        ]
    },
    {
        path: '/base_management',
        name: 'base_management',
        meta: {
            icon: 'ios-boat-outline',
            notCache: true,
            title: '基础信息管理'
        },
        component: Main,
        children: [
            // {
            //   path: 'app_mgt',
            //   name: 'app_mgt',
            //   meta: {
            //     icon: 'md-paw',
            //     title: '应用管理'
            //   },
            //   component: () => import('@/spider-view/app-mgt/app-mgt.vue')
            // },
            // {
            //   path: 'app_regist',
            //   name: 'app_regist',
            //   meta: {
            //     icon: 'md-add',
            //     title: '新增应用'
            //   },
            //   component: () => import('@/spider-view/app-mgt/app-regist.vue')
            // },
            {
                path: 'env_mgt',
                name: 'env_mgt',
                meta: {
                    icon: 'md-paw',
                    title: '环境管理'
                },
                component: () => import('@/spider-view/env-mgt/env-mgt.vue')
            },
            {
                path: 'node_apply',
                name: 'node_apply',
                meta: {
                    icon: 'md-paw',
                    title: '订单管理'
                },
                component: () => import('@/spider-view/env-mgt/node-apply.vue')
            },
            {
                path: 'node_recycle',
                name: 'node_recycle',
                meta: {
                    icon: 'md-paw',
                    title: '回收单管理'
                },
                component: () => import('@/spider-view/env-mgt/node-recycle.vue')
            },
            {
                path: 'node_mgt',
                name: 'node_mgt',
                meta: {
                    icon: 'md-paw',
                    title: '节点管理'
                },
                component: () => import('@/spider-view/env-mgt/node-mgt.vue')
            },
            {
                path: 'app_mgt_interface',
                name: 'app_mgt_interface',
                meta: {
                    icon: 'md-paw',
                    title: '应用接口管理',
                    keepAlive: true,
                    isLogin: false,
                    noNeedLogin: true
                    // requireAuth: true,
                },
                component: () => import('@/spider-view/env-mgt/app_mgt_interface.vue')
            },
            {
                path: 'iter_publish_plan_config',
                name: 'iter_publish_plan_config',
                meta: {
                    hideInMenu: false,
                    notCache: true,
                    icon: 'md-flower',
                    title: '发布计划配置'
                },
                component: () => import('@/spider-view/env-mgt/iter-publish-plan-config.vue')
            },
            {
                path: 'biz_mgt_app_bind',
                name: 'biz_mgt_app_bind',
                meta: {
                    icon: 'ios-git-compare',
                    title: '业务关联应用管理'
                },
                component: () => import('@/spider-view/biz-iter-mgt/biz_app_relation')
            },
            {
                path: 'release_group',
                name: 'release_group',
                meta: {
                    icon: 'ios-git-compare',
                    title: '组发布策略管理'
                },
                component: () => import('@/spider-view/releaseGroup')
            },
            {
                path: 'agent_bing',
                name: 'agent_bing',
                meta: {
                    icon: 'ios-git-compare',
                    title: 'agent绑定管理'
                },
                component: () => import('@/spider-view/agentList')
            }
        ]
    },
    {
        path: '/test_env',
        name: 'test_env',
        meta: {
            icon: 'ios-flask-outline',
            title: 'test_env'
        },
        component: Main,
        children: [
            // 测试环境新加的router
            {
                path: 'env_information_new',
                name: 'env_information_new',
                meta: {
                    icon: 'md-paw',
                    title: '环境信息'
                },
                component: () => import('@/view/test_env/env_information_new')
            },
            {
                path: 'env_init',
                name: 'env_init',
                meta: {
                    icon: 'ios-send-outline',
                    title: '环境初始化'
                },
                component: () => import('@/spider-view/test-env/env_init')
            }
        ]
    },
    {
        path: '/biz-iter-mgt',
        name: 'biz_iter_mgt',
        meta: {
            icon: 'ios-bicycle',
            title: 'biz_iter_mgt'
        },
        component: Main,
        children: [
            {
                path: 'biz_auto_test_define',
                name: 'biz_auto_test_define',
                meta: {
                    icon: 'ios-git-network',
                    title: '业务自动化编排'
                },
                component: () => import('@/spider-view/biz-iter-mgt/test_define')
            },
            {
                path: 'test_data_exec_new',
                name: 'test_data_exec_new',
                meta: {
                    icon: 'md-alarm',
                    title: '业务迭代自动化'
                },
                component: () => import('@/spider-view/biz-iter-mgt/test_data_exec_config_new')
            },
            {
                path: 'auto_exec_record',
                name: 'auto_exec_record',
                meta: {
                    icon: 'ios-pulse',
                    title: '自动化执行记录'
                },
                component: () => import('@/spider-view/biz-iter-mgt/auto_exec_record')
            },
            {
                path: 'test_data_dev_new',
                name: 'test_data_dev_new',
                meta: {
                    icon: 'ios-grid-outline',
                    title: '业务测试数据开发'
                },
                component: () => import('@/spider-view/biz-iter-mgt/test_data_dev_new')
            },
            {
                path: 'es_backup_mgt',
                name: 'ES备份管理',
                meta: {
                    icon: 'ios-cloud-outline',
                    title: 'ES备份管理'
                },
                component: () => import('@/spider-view/biz-iter-mgt/es-backup-mgt')
            },
            {
                path: 'dupReset',
                name: 'dupReset',
                meta: {
                    icon: 'ios-grid-outline',
                    title: 'dump还原'
                },
                component: () => import('@/spider-view/biz-iter-mgt/dupReset')
            }
            // {
            //   path: 'test_data_dump',
            //   name: 'test_data_dump',
            //   meta: {
            //     icon: 'ios-grid-outline',
            //     title: 'dump还原'
            //   },
            //   component: () => import('@/spider-view/biz-iter-mgt/test_data_dump')
            // }
        ]
    },
    {
        path: '/ops_service',
        name: 'ops_service',
        meta: {
            icon: 'md-cloud-upload',
            notCache: true,
            title: '运维专属服务'
        },
        component: Main,
        children: [
            {
                path: 'prod_rmq_apply',
                name: 'prod_rmq_apply',
                meta: {
                    icon: 'md-globe',
                    notCache: true,
                    title: '产线组RMQ申请'
                },
                component: () => import('@/spider-view/env-mgt/prod_rmq_apply.vue')
            },

            {
                path: 'node_group_mgt',
                name: 'node_group_mgt',
                meta: {
                    icon: 'md-git-compare',
                    notCache: true,
                    title: '分组管理'
                },
                component: () => import('@/spider-view/publish-mgt/node-group-mgt/node-group-mgt.vue')
            },
            {
                path: 'publish_service',
                name: 'publish_service',
                meta: {
                    icon: 'md-globe',
                    notCache: true,
                    title: '发布服务'
                },
                component: () => import('@/spider-view/publish-mgt/publish-service/publish-service.vue')
            },
            {
                path: 'publish_cmd',
                name: 'publish_cmd',
                meta: {
                    icon: 'ios-construct-outline',
                    notCache: true,
                    title: '运维salt维护'
                },
                component: () => import('@/spider-view/publish-mgt/publish-cmd-mgt/publish-cmd.vue')
            },
            {
                path: 'ops_publish_guide',
                name: 'ops_publish_guide',
                meta: {
                    icon: 'md-pulse',
                    title: '应用发布操作指南'
                },
                component: () => import('@/view/ops-service/ops-publish/ops-publish-guide.vue')
            },
            {
                path: 'simulate_sql_executed',
                name: 'simulate_sql_executed',
                meta: {
                    hideInMenu: true,
                    hideInBread: true,
                    title: '仿真SQL执行',
                    notCache: true
                },
                component: () => import('@/view/ops-service/simulate_sql_executed/simulate_sql_executed.vue')
            },
            {
                path: 'release_sql_executed',
                name: 'release_sql_executed',
                meta: {
                    hideInMenu: true,
                    hideInBread: true,
                    title: '产线SQL执行',
                    notCache: true
                },
                component: () => import('@/view/ops-service/release_sql_executed/release_sql_executed.vue')
            },
            {
                path: 'server_batch_reset',
                name: 'server_batch_reset',
                meta: {
                    icon: 'md-pulse',
                    title: '服务端批量重启'
                },
                component: () => import('@/view/server_batch_reset/index.vue')
            },
            {
                path: 'server_batch_edit',
                name: 'server_batch_edit',
                meta: {
                    hideInMenu: true,
                    hideInBread: true,
                    icon: 'md-pulse',
                    title: '服务端批量重启'
                },
                component: () => import('@/view/server_batch_edit/index.vue')
            }
        ]
    },

    {
        path: '/auth_apply_service',
        name: 'auth_apply_service',
        meta: {
            icon: 'md-cloud-upload',
            notCache: true,
            title: '权限申请管理'
        },
        component: Main,
        children: [
            {
                path: 'rsync_gitlab_info',
                name: 'rsync_gitlab_info',
                meta: {
                    icon: 'md-list',
                    title: '同步gitlab信息'
                },
                component: () => import('@/spider-view/iter-mgt/rsync-gitlab-info.vue')
            },
            {
                path: 'svn_account_apply',
                name: 'svn_account_apply',
                meta: {
                    icon: 'md-list',
                    title: 'svn账号开通申请'
                },
                component: () => import('@/spider-view/auth-mgt/svn_account_apply.vue')
            }
        ]
    },

    {
        path: '/argu',
        name: 'argu',
        meta: {
            hideInMenu: true
        },
        component: Main,
        children: [
            {
                path: 'params/:id',
                name: 'params',
                meta: {
                    icon: 'md-flower',
                    title: route => `{{ params }}-${route.params.id}`,
                    notCache: true,
                    beforeCloseName: 'before_close_normal'
                },
                component: () => import('@/view/argu-page/params.vue')
            },
            {
                path: 'query',
                name: 'query',
                meta: {
                    icon: 'md-flower',
                    title: route => `{{ query }}-${route.query.id}`,
                    notCache: true
                },
                component: () => import('@/view/argu-page/query.vue')
            }
        ]
    },
    {
        path: '/401',
        name: 'error_401',
        meta: {
            hideInMenu: true
        },
        component: () => import('@/view/error-page/401.vue')
    },
    {
        path: '/500',
        name: 'error_500',
        meta: {
            hideInMenu: true
        },
        component: () => import('@/view/error-page/500.vue')
    },
    {
        path: '/log',
        name: 'log',
        meta: {
            hideInMenu: true
        },
        component: () => import('@/view/ops-service/log/log.vue')
    }
]
