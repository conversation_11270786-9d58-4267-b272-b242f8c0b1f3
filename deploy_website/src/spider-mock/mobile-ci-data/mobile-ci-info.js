import Mock from 'mockjs'


export const generateMobileCiData= req => {
  let table_info = [{"app_name": "fund",
    "begin_ver": null,
    "br_name": "h5-pkg-fund_0622_zhongou",
    "end_ver": null,
    "git_path": "h5/h5-pkg-fund",
    "job_url": "http://192.168.208.40:8080/jenkins/job/h5_h5-pkg-fund_0622_zhongou_fund/2/display/redirect",
    "message": "Exit",
    "need_online": 1,
    "need_ops": 1,
    "operate_time": "2021-08-11T20:04:59.542001",
    "package_type": "dist",
    "status": "compile_failure",
    "status_display": "编译失败",
    "suite_name": "",
    "username": "刘帅",
    "_checked":false},
    {"app_name": "piggy",
    "begin_ver": null,
    "br_name": "h5-pkg-fund_0622_zhongou",
    "end_ver": null,
    "git_path": "h5/h5-pkg-fund",
    "job_url": "http://192.168.208.40:8080/jenkins/job/h5_h5-pkg-fund_0622_zhongou_piggy/2/display/redirect",
    "message": "Exit",
    "need_online": 1,
    "need_ops": 1,
    "operate_time": "2021-08-11T20:04:59.542001",
    "package_type": "dist",
    "status": "compile_failure",
    "status_display": "编译失败",
    "suite_name": "",
    "username": "刘帅",
    "_checked":false}]

  let ci_info = {"data":{"ci_info":table_info,"msg":"正在处理中"}}
  console.log(ci_info)
  return ci_info;
}
