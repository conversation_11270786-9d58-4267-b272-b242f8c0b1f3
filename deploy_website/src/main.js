// The Vue build version to load with the `import` command
// (runtime-only or standalone) has been set in webpack.base.conf with an alias.
import Vue from 'vue'
import App from './App'
import router from './router'
import store from './store'
import iView from 'iview'
import i18n from '@/locale'
import config from '@/config'
//import axios from 'axios'
import importDirective from '@/directive'
import { directive as clickOutside } from 'v-click-outside-x'
import installPlugin from '@/plugin'
import './index.less'
import '@/assets/icons/iconfont.css'
import TreeTable from 'tree-table-vue'
import VOrgTree from 'v-org-tree'
import 'v-org-tree/dist/v-org-tree.css'
import VueResource from 'vue-resource'
import VuePipeline from 'vue-pipeline'
import Contextmenu from "vue-contextmenujs"

// 实际打包时应该不引入mock
/* eslint-disable */
//去掉mock设置 20220302 by fwm
//if (process.env.NODE_ENV !== 'production') require('@/mock')
//if (process.env.NODE_ENV !== 'production') require('@/spider-mock')
// 引入echarts
import echarts from 'echarts'
Vue.prototype.$echarts = echarts

//require('/config/serverconfig.js')
//import {spider_url, website_rul} from '/config/serverconfig.js'


// console.log(spider_url)
// console.log(website_rul)
// Vue.prototype.getConfigJson = function () {
//      // localStorage.setItem("spider_url", appData.spider_url) //接口地址
//      // localStorage.setItem("website_rul", appData.website_rul) //静态资源地址
//
//      axios.get("/config/serverconfig.js").then((res) => {
//        console.log(res.data)
//      localStorage.setItem("spider_url", res.data.spider_url) //接口地址
//      localStorage.setItem("website_rul", res.data.website_rul) //静态资源地址
//
//      }).catch((error) => {
//      console.log('未获取到接口地址', error)
//  })
//
// }

Vue.use(VuePipeline)
Vue.use(Contextmenu);
Vue.use(iView, {
  i18n: (key, value) => i18n.t(key, value)
})
Vue.use(TreeTable)
Vue.use(VOrgTree)
/**
 * @description 注册admin内置插件
 */
installPlugin(Vue)
/**
 * @description 生产环境关掉提示
 */
Vue.config.productionTip = false
/**
 * @description 全局注册应用配置
 */
Vue.prototype.$config = config
Vue.prototype.console = { log: console.log }
/**
 * 注册指令
 */
importDirective(Vue)
Vue.directive('clickOutside', clickOutside)

/* eslint-disable no-new */
new Vue({
  el: '#app',
  router,
  i18n,
  store,
  render: h => h(App)
})

//在整个项目中全局配置，需要在main.js中写入

Vue.prototype.$Message.config({
  duration: 5
});


// Vue.prototype.$Notice.config({
//   top: 50,
//   duration: 3
// });
