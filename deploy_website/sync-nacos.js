const NacosConfigClient = require('nacos').NacosConfigClient;
const logger = console;
const fs = require('fs')
const path = require('path')

const configClient = new NacosConfigClient({

  serverAddr: 'hk.nacos.howbuy.com:80', // replace to real nacos serverList
  namespace: 'dev',
});

configClient.getConfig('spider.properties', 'hm-1.01.0').then(res =>{
  //console.log(res);
  res_list = res.split("\n")
  for (row in res_list){
    if (res_list[row].startsWith("#") || res_list[row]==""){
        //  console.log(row)
    }
    else{
      console.log(res_list[row])
    }

  }
  let file = path.resolve(__dirname, './file.txt')
  fs.writeFile(file, res, { encoding: 'utf8' }, err => {})
  console.log('getConfig = ',res);
})

