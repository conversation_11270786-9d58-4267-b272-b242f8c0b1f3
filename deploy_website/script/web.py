#
from flask import Flask, request, send_file, send_from_directory, render_template, redirect
from werkzeug.routing import BaseConverter


class RegexConverter(BaseConverter):
    def __init__(self, url_map, *items):
        super(RegexConverter, self).__init__(url_map)
        self.regex = items[0]


app = Flask(__name__)
app.url_map.converters['regex'] = RegexConverter
#@app.route('/<regex(".*"):id>')

@app.route('/js/<path:path>')
def send_js(path):
    return send_from_directory('js', path)

@app.route('/css/<path:path>')
def send_css(path):
    return send_from_directory('css', path)

@app.route('/fonts/<path:path>')
def send_fonts(path):
    return send_from_directory('fonts', path)

@app.route('/img/<path:path>')
def send_img(path):
    return send_from_directory('img', path)

@app.route('/favicon.ico')
def send_favicon():
    return send_from_directory('', 'favicon.ico')

@app.route('/')
def index():
    return send_file('index.html')

@app.errorhandler(404)
def page_not_found(error):
    return redirect('/')


if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=80)

