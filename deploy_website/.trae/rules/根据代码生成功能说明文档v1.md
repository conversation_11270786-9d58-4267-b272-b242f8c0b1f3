你是一名资深前端架构师和代码分析专家。请基于我提供的"现有代码"，生成一份结构化的功能说明文档（Markdown），用于后续代码优化和功能改造。

总体目标：

-   **自动功能识别**：从代码中自动“识别与归纳功能”，并形成“以功能为目录”的结构化文档
-   **功能详细描述**：每个功能需包含：交互（含交互图）、触发事件、调用的接口、相关变量、关键代码、异常与校验、依赖关系、可配置项、待优化建议
-   **智能功能识别**：自动识别代码中的业务功能模块，按用户价值和技术实现进行分类
-   **完整技术映射**：全面梳理每个功能的技术实现细节，包括数据流、控制流、依赖关系、性能优化点
-   **可操作性文档**：生成的文档必须能够直接用于指导代码重构、性能优化和功能扩展
-   **二次处理友好**：文档结构标准化，便于 AI 模型理解和处理，支持自动化代码生成

适配项目与自动识别要点（若能自动判断则不要向我提问）：

-   技术栈自动识别（如 Vue2/Vue3、Element UI/Element Plus、axios、Vuex/Pinia、Router 等）
-   代码入口、路由、视图、组件（.vue/.tsx）、store、api、utils、指令、mixin、hooks、样式（重要的 CSS 类与 BEM 语义）
-   常见交互绑定与事件：template 中的 @click/@change 等、\$emit/defineEmits、自定义事件总线、window/document 事件、watch/生命周期钩子、定时器
-   后端接口：api 目录、axios 封装、请求方法/URL/入参/出参、错误码、重试/超时、鉴权
-   重要状态：props、data/ref、computed、watch、store 中的 state/getters/actions/mutations、依赖注入/provide-inject、全局变量
-   代码中的关键“功能开关/变量透传”（例如形如 :is_publish_apply_refresh="xxx" 的属性绑定）

提示信息：

-   import 引入的文件路径中的'@'符号表示'src'目录

输出语言：中文

输出格式（Markdown，必须按下面结构组织，便于后续二次处理）：

# 功能说明（基于现有代码自动生成）

-   代码范围：<自动填写（仓库根/我提供的目录或文件清单）>
-   技术栈与运行环境：<自动识别并简述>

## 目录（按功能归纳）

-   [功能 A - 简要一句话概述](#功能a)
-   [功能 B - 简要一句话概述](#功能b)
-   ...

## 全局概览

-   路由与页面拓扑
-   主要模块与依赖关系（组件树/依赖图）
-   全局状态管理（Store 模块、关键状态、持久化）
-   全局异常/鉴权/拦截器/国际化/主题配置（若有）

---

## 功能 A

### 1. 功能概述

-   目标与用户价值：<简述>
-   入口与展示位置：<页面/组件路径、选择器或路由>
-   权限/前置条件：<如需登录/角色/开关>

### 2. 交互说明

-   交互步骤说明（逐步描述）
-   流程图

### 3. 触发事件

-   DOM/组件事件：<例如 @click/@change/@submit 等，列出事件名 + 所在文件:行号（尽量） + 处理函数>
-   组件通信：<$emit/$on/defineEmits，事件名，传参结构>
-   全局事件/总线：<eventBus、window/document 事件、addEventListener 类型等>
-   定时器/异步触发：<setInterval/setTimeout、轮询等>

### 4. 调用的接口

以表格形式列出（尽量从代码静态解析，还可结合 axios 拦截器）：
| 名称 | 方法 | URL | 入参 | 出参(关键字段) | 错误码/异常 | 所在文件 |
| ---- | ---- | --- | ---- | ------------- | ----------- | -------- |
| 发布 | POST | /publish | { id, payload } | { code, msg } | 401/403/500 | src/api/publish.js |

### 5. 相关变量与状态

-   props：<名称:类型:用途:是否必填:默认值>
-   data/ref：<名称:类型:用途:初始值>
-   computed：<名称:依赖:用途>
-   store：<模块:state/getters/actions/mutations:用途>
-   重要透传/开关：<例如 is_publish_apply_refresh，来源与去向、改变时机、影响范围>

### 6. 关键代码

-   精选片段（带文件路径与尽量准确的起止行号）
-   对复杂逻辑做行内说明（为何这么写、关键分支含义）

### 7. 校验/异常/边界

-   表单与参数校验
-   接口错误处理与重试
-   空数据/分页边界/权限失效/网络异常

### 8. 性能与可维护性

-   可能的性能风险点：<重复渲染/大列表/未取消请求/深度 watch 等>
-   优化建议：<简要可操作建议>

### 9. 配置与国际化（如有）

-   可配置项（来源、默认值、如何覆盖）
-   i18n key 与文案来源

### 10. 依赖关系

-   组件依赖与复用关系（Mermaid 组件关系图可选）
-   与其他功能的耦合点

---

## 功能 B

<同“功能 A”结构填充>

---

## 待优化清单（跨功能）

-   问题/风险项 + 影响范围 + 建议

生成要求与约束：

-   严格使用 Markdown 标题与表格
-   交互包含 时序图/流程/状态图，按需选择
-   代码块标注语言（js/vue/ts/json）
-   尽量写明“文件路径 + 行号”，若无法定位精确行号可标注函数名/选择器/注释附近特征
-   不允许臆测：无法确定的信息以 TODO 标注，并写出如何在代码中进一步确认
-   先全局扫描，再按功能分组，避免遗漏“隐形触发点”（watch/生命周期/拦截器）
-   分析代码时不要遗漏入口文件引入的任何组件或模块，任何一行代码，包括但不限于：组件、路由、store、api、指令、mixin、hooks、样式（重要的 CSS 类与 BEM 语义）
-   对于注释掉的代码，定义了但没有使用的代码，直接过滤掉不做分析
-   生成的文档存放在代码文件所在目录下

输入即将提供：

-   代码范围：<在此处自动替换为我实际附带的代码根目录或文件列表>
-   注意：请自动识别 Vue2/Vue3，并正确解析 SFC（template/script/style）、路由、store、api
