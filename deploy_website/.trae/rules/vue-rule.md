---
description: 
globs: *.vue
alwaysApply: false
---
# 你是精通html，css，js，typescript，vue等框架的前端专家，编码vue文件时，遵循以下规范

## 组件名为多个单词

组件名应该始终是多个单词的，根组件 `App` 以及 `<transition>`、`<component>` 之类的 `Vue` 内置组件除外。

例如：`TreeSelect` `TableColumn`


## 紧密耦合的组件命名

和父组件紧密耦合的子组件应该以父组件名作为前缀命名

```sh
# 不推荐
components/
|- TodoList.vue
|- TodoItem.vue
|- TodoButton.vue

# 推荐
components/
|- TodoList.vue
|- TodoListItem.vue
|- TodoListItemButton.vue

```

## 推荐 Vue-Router 写法

路由全部采用懒加载的方式导入（除了登录布局等需要网站一打开就需要加载的页面）， 保证代码分割和高效加载。

# Vue组件规范-vue文件

### 顶级标签顺序
顺序如下，且标签之间留有空行。

### 顶部注释
* 简要说明组件的功能和作用。例如：`<!-- 下拉选择组件 -->`
* 其他信息如作者、修改时间等不用写，git日志都有。

### 代码行数
1. 尽量减少行数，但要保证代码的可读性，避免出现过长的行。
2. 代码行数最好不超过500行，超过需要拆分。


# Vue组件规范-`<template>`模板规范

1. 单一根元素，且根元素尽量不使用`v-if`、`v-for`等指令。
2. 根元素的class名尽量以组件功能释义命名，尽量保持唯一性，如ams_select、sm_fund_search
3. ‌类名、‌ID等应遵循统一的命名规范
4. ‌对复杂逻辑或特殊结构进行注释
5. 模板中尽量不使用表达式，尽量使用计算属性、方法、插槽等。
6. 引用组件时使用驼峰，无嵌套内容的标签用闭合单标签
7. 为 v-for 设置键值；在组件上总是必须用 key 配合 v-for，以便维护内部组件及其子树的状态。

# Vue组件规范-`<script>`逻辑规范

## props

小驼峰命名。内容尽量详细，至少有类型和默认值，顺序是`type` `default`

对象和数组默认值不要使用 `[]` `{}` 应该使用函数 `() => []` `() => ({})`

## data 数据

选项式组件的 `data` 必须是一个函数,并且建议在此不使用箭头函数

## import 引入顺序
  同等类型的放一起，优先级如下
- 1. 优先放第三方的模块 如`vue` `lodash`
- 2. 然后放`@/` 下的 `components`，`hooks`，`utils` 等模块。
- 3. 最后放`./`或者`../`下的模块。

## js逻辑`<script>`部分
- `prop`要设置默认值和写注释。
- `data`要写注释。
- 变量和方法都要写注释
- 尽量使用`async await`同步写法，避免代码嵌套。
- 使用`computed`、`watch`、`watchEffect`、`watchPostEffect`、`watchSyncEffect`等方法，不要使用`v-model`。
- 用`composition api`的思想来组织代码，按功能块写，避免将同一类声明全部写在一个地方。

# Vue组件规范-`<style>`样式规范

1. 加作用域`scoped`，样式只对当前组件生效，避免其他重名class冲突。
2. 尽量使用less语法，结构层级的样式要嵌套写。
3. class命名不要使用class缩略&_等拼接符。 命名规则：BEM @*BEM* 或中杠或下划线无具体限制。
1. 重复属性尽量使用变量，不要直接写颜色、字体大小等。








