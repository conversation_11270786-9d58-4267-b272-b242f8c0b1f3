#!/bin/bash
# -----------------
# Program: website auto script
# Author:
# Email:
# -----------------


if [ $(whoami) != root ];then
    echo "[ ERROR ]: The User Is Not ROOT !"
    exit 0
fi


start(){
    npm run dev -- --port 80 &> /dev/null &
}


stop(){
    pkill -f 'node'
}


usage() {
    echo -e "Usage:\n\t$(basename $0) <start|restart|stop>"
    exit 0
}


case $1 in
    start)
        start
    ;;
    restart)
        stop
        start
    ;;
    stop)
        stop
    ;;
    *)
        usage
    ;;
esac

