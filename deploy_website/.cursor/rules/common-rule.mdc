<!--
 * @Author: chao.bao <EMAIL>
 * @Date: 2025-08-07 10:22:15
 * @LastEditors: chao.bao <EMAIL>
 * @LastEditTime: 2025-08-12 10:07:36
 * @FilePath: /website_web/deploy_website/.trae/rules/common-rule.md
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->

---

description:
globs:
alwaysApply: true

---

# 你是前端开发专家，在编写 vue 代码时需要遵循以下公共组件和方法的规范

## 公共组件使用规范

### 1. 项目技术站 vue@2.5.10 + iview@3.4.2

项目中使用 vue@2.5.10 + iview@3.4.2 来编写 vue 相关的代码

### 2. iview 组件库

项目中使用 `iview`的 3.4.2 版本 作为基础 UI 组件库

### 3. vue 路由配置文件

项目中使用 vue-router 来配置路由，路由配置文件为 `/deploy_website/src/router/routers.js`

### 4. 接口请求

项目中使用 axios 来请求接口，接口请求的基础配置在 `/deploy_website/src/spider-api` 中，所有的接口请求都需要通过该文件来发送。

### 5. 公共组件

项目中使用的公共组件都在 `/deploy_website/src/components` 和 `/deploy_website/src/spider-components` 目录下

## 使用建议

1. 在开发新功能时，优先使用已有的公共组件和方法，避免重复开发
2. 使用组件时，注意查看组件的文档和示例，确保正确使用
3. 如果现有组件不能满足需求，可以考虑扩展现有组件或开发新组件
4. 开发新组件时，遵循项目的组件开发规范
5. 使用工具方法时，注意方法的参数和返回值类型
6. 对于复杂的业务逻辑，建议封装成独立的工具方法
7. 保持代码风格的一致性，遵循项目的编码规范
