/*
 * @Author: chao.bao <EMAIL>
 * @Date: 2024-08-06 14:42:47
 * @LastEditors: chao.bao <EMAIL>
 * @LastEditTime: 2024-09-24 14:50:00
 * @FilePath: /website_web/deploy_website/vue.config.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
const path = require('path')
// const GenerateAssetPlugin = require('generate-asset-webpack-plugin');
const resolve = dir => {
  return path.join(__dirname, dir)
}
// var appData = require('./src/config/serverconfig.json')
// var createServerConfig = function(compilation){
//      // 配置需要的api接口
//
//   return JSON.stringify(appData);
// }
// 项目部署基础
// 默认情况下，我们假设你的应用将被部署在域的根目录下,
// 例如：https://www.my-app.com/
// 默认：'/'
// 如果您的应用程序部署在子路径中，则需要在这指定子路径
// 例如：https://www.foobar.com/my-app/
// 需要将它改为'/my-app/'
// iview-admin线上演示打包路径： https://file.iviewui.com/admin-dist/
const BASE_URL = process.env.NODE_ENV === 'production'
  ? '/'
  : '/'

module.exports = {
  configureWebpack: {
    devtool: 'source-map'
  },
  // outputDir: process.env.outputDir,
  // publicPath: '/',
  outputDir: 'deploy-vue',
  assetsDir: process.env.VUE_APP_ENV + '/static',
  // build:{
  //   index: resolve('../dist/index.html'),
  //   assetsRoot: resolve('../dist'),
  //   assetsSubDirectory: 'static',
  //   assetsPublicPath: './',
  // },
  // Project deployment base
  // By default we assume your app will be deployed at the root of a domain,
  // e.g. https://www.my-app.com/
  // If your app is deployed at a sub-path, you will need to specify that
  // sub-path here. For example, if your app is deployed at
  // https://www.foobar.com/my-app/
  // then change this to '/my-app/'
  baseUrl: BASE_URL,
  // tweak internal webpack configuration.
  // see https://github.com/vuejs/vue-cli/blob/dev/docs/webpack.md
  // 如果你不需要使用eslint，把lintOnSave设为false即可
  lintOnSave: false,
  chainWebpack: config => {
    config.resolve.alias
      .set('@', resolve('src')) // key,value自行定义，比如.set('@@', resolve('src/components'))
      .set('_c', resolve('src/components'))
  },
  // configureWebpack: {
  //   plugins: [
  //     new GenerateAssetPlugin({
  //       filename:  process.env.VUE_APP_ENV+'/static/config/serverconfig.json',
  //       fn: (compilation, cb)=>{
  //         cb(null, createServerConfig(compilation));
  //       },
  //       //fn: (compilation, cb){
  //      // cb(null, createConfig(compilation))
  //       //},
  //            })
  //         ]
  //         },
  // 设为false打包时不生成.map文件
  productionSourceMap: false,
  // 这里写你调用接口的基础路径，来解决跨域，如果设置了代理，那你本地开发环境的axios的baseUrl要写为 '' ，即空字符串
  devServer: {
  	// proxy: '***************',
    disableHostCheck: true
  }
}
