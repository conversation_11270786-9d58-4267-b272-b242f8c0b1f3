/*
 * @Author: chao.bao <EMAIL>
 * @Date: 2024-09-27 10:15:19
 * @LastEditors: chao.bao <EMAIL>
 * @LastEditTime: 2024-10-12 17:58:44
 * @FilePath: /website_web/deploy_website/.eslintrc.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
module.exports = {
    root: true,
    extends: ['plugin:vue/essential', '@vue/standard', 'eslint:recommended', 'plugin:prettier/recommended'],
    plugins: [
        // ...
        'prettier'
    ],
    rules: {
        // 'prettier/prettier': 'error',
        'prettier/prettier': ['error', { endOfLine: 'auto' }],
        // allow async-await
        'generator-star-spacing': 'off',
        // allow debugger during development
        'no-debugger': process.env.NODE_ENV === 'production' ? 'error' : 'off',
        'vue/no-parsing-error': [
            2,
            {
                'x-invalid-end-tag': false
            }
        ],
        'no-undef': 'off',
        camelcase: 'off'
    },
    parserOptions: {
        parser: 'babel-eslint'
    }
}
