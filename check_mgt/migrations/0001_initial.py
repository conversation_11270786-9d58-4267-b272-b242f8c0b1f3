# Generated by Django 3.2 on 2022-05-07 17:36

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='CheckSwitch',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('check_name', models.CharField(max_length=100, verbose_name='模块名')),
                ('switch', models.BooleanField(default=1, verbose_name='控制开关')),
            ],
            options={
                'verbose_name': '校验开关',
                'db_table': 'check_mgt_check_switch',
            },
        ),
    ]
