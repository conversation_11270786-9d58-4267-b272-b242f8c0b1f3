import datetime
import time
import json
import socket

from spider.settings import audit_logger, logger


class AuditLogMiddleware(object):
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        param = None
        ignore = False
        try:
            # 抽成配置
            ignore_api_list = ["/test_mgt", "app_interface_api"]
            for item in ignore_api_list:
                if item in request.path:
                    ignore = True
                    break
            if not ignore:
                if request.method in ['GET', 'DELETE']:
                    param = request.GET.dict()
                elif request.method in ['POST', 'PUT']:
                    if request.content_type in ['application/json']:
                        param = request.body.decode('utf-8')
                    else:
                        param = request.POST.dict()
        except Exception as e:
            logger.error(e)
        start_time = time.time()
        response = self.get_response(request)
        end_time = time.time()
        elapsed_time = end_time - start_time
        request_user = str(request.user)
        try:
            if ignore:
                log_dict = {
                    "request_user": request_user,
                    'remote_addr': request.META.get('REMOTE_ADDR'),
                    'res_addr': self.get_local_ip(),
                    'request_method': request.method,
                    'request_path': request.path,
                    'response_status': response.status_code,
                    'response_content_type': response.get('Content-Type'),
                    'request_param': "app_interface_api/noparam",
                    'response_body': "app_interface_api/nobody",
                    'request_took': str(elapsed_time) + " s",
                    'time': datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S.%S")
                }
            else:
                # 记录请求与响应信息
                log_dict = {
                    "request_user": request_user,
                    'remote_addr': request.META.get('REMOTE_ADDR'),
                    'res_addr': self.get_local_ip(),
                    'request_method': request.method,
                    'request_path': request.path,
                    'response_status': response.status_code,
                    'response_content_type': response.get('Content-Type'),
                    'request_param': param,
                    'response_body': response.content.decode("utf-8"),
                    'request_took': str(elapsed_time) + " s",
                    'time': datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S.%S")
                }
            audit_logger.error(json.dumps(log_dict, ensure_ascii=False))
        except Exception as ex:
            logger.error(ex)
        finally:
            return response

    def get_local_ip(self):
        ip_list = []
        # 获取本机名
        hostname = socket.gethostname()
        # 获取本机IP地址
        ip_addresses = socket.getaddrinfo(hostname, None)
        for address in ip_addresses:
            if address[0] == socket.AF_INET:
                ip = address[4][0]
                if ip != '127.0.0.1' and ip not in ip_list:
                    ip_list.append(ip)
        return ",".join(ip_list)
