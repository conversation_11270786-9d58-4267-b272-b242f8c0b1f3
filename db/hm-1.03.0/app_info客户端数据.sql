/*
SQLyog Ultimate v12.08 (64 bit)
MySQL - 5.7.17 
*********************************************************************
*/
/*!40101 SET NAMES utf8 */;

insert into `app_mgt_app_info` (`app_name`, `app_cname`, `app_status`, `git_url`, `git_path`, `svn_url`, `svn_path`, `app_jdk_version`, `app_desc`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `lib_location`, `platform_type`, `platform_time`, `third_party_middleware`) values('fund-ios','ios掌机','1','howbuy_ios','/fund','','','','','shuai.liu','2021-03-23 16:24:46','shuai.liu','2021-03-23 16:24:46','0','ios',NULL,NULL,NULL);
insert into `app_mgt_app_info` (`app_name`, `app_cname`, `app_status`, `git_url`, `git_path`, `svn_url`, `svn_path`, `app_jdk_version`, `app_desc`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `lib_location`, `platform_type`, `platform_time`, `third_party_middleware`) values('piggy-ios','IOS储蓄罐','1','howbuy_ios','/pigg','','','','','shuai.liu','2021-03-23 17:28:28','shuai.liu','2021-03-23 17:28:28','0','ios',NULL,NULL,NULL);
insert into `app_mgt_app_info` (`app_name`, `app_cname`, `app_status`, `git_url`, `git_path`, `svn_url`, `svn_path`, `app_jdk_version`, `app_desc`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `lib_location`, `platform_type`, `platform_time`, `third_party_middleware`) values('crm-ios','IOSCRM','1','howbuy_ios','/crm','','','','','shuai.liu','2021-03-23 17:29:25','shuai.liu','2021-03-23 17:29:25','0','ios',NULL,NULL,NULL);
insert into `app_mgt_app_info` (`app_name`, `app_cname`, `app_status`, `git_url`, `git_path`, `svn_url`, `svn_path`, `app_jdk_version`, `app_desc`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `lib_location`, `platform_type`, `platform_time`, `third_party_middleware`) values('fund-ios-public','ios-public','1','howbuy_ios','/fund_public','','','','','shuai.liu','2021-03-23 17:35:52','shuai.liu','2021-03-23 17:35:52','0','ios',NULL,NULL,NULL);
insert into `app_mgt_app_info` (`app_name`, `app_cname`, `app_status`, `git_url`, `git_path`, `svn_url`, `svn_path`, `app_jdk_version`, `app_desc`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `lib_location`, `platform_type`, `platform_time`, `third_party_middleware`) values('fund-ios-private','fund_private','1','howbuy_ios','/fund_private','','','','','shuai.liu','2021-03-23 17:37:29','shuai.liu','2021-03-23 17:37:29','0','ios',NULL,NULL,NULL);
insert into `app_mgt_app_info` (`app_name`, `app_cname`, `app_status`, `git_url`, `git_path`, `svn_url`, `svn_path`, `app_jdk_version`, `app_desc`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `lib_location`, `platform_type`, `platform_time`, `third_party_middleware`) values('fund-ios-accountcenter','fund-ios-accountcenter','1','howbuy_ios','/fund_accountcenter','','','','','shuai.liu','2021-03-23 17:39:44','shuai.liu','2021-03-23 17:39:44','0','ios',NULL,NULL,NULL);
insert into `app_mgt_app_info` (`app_name`, `app_cname`, `app_status`, `git_url`, `git_path`, `svn_url`, `svn_path`, `app_jdk_version`, `app_desc`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `lib_location`, `platform_type`, `platform_time`, `third_party_middleware`) values('library','library','1','howbuy_ios','/library_sourcecode','','','','','shuai.liu','2021-03-23 17:42:10','shuai.liu','2021-03-23 17:42:10','0','ios',NULL,NULL,NULL);
insert into `app_mgt_app_info` (`app_name`, `app_cname`, `app_status`, `git_url`, `git_path`, `svn_url`, `svn_path`, `app_jdk_version`, `app_desc`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `lib_location`, `platform_type`, `platform_time`, `third_party_middleware`) values('fund-android','安卓掌机','1','howbuy_android','/fund','','','','','shuai.liu','2021-03-23 17:46:12','shuai.liu','2021-03-23 17:46:12','0','安卓',NULL,NULL,NULL);
insert into `app_mgt_app_info` (`app_name`, `app_cname`, `app_status`, `git_url`, `git_path`, `svn_url`, `svn_path`, `app_jdk_version`, `app_desc`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `lib_location`, `platform_type`, `platform_time`, `third_party_middleware`) values('piggy-android','安卓储蓄罐','1','howbuy_android','/piggy','','','','','shuai.liu','2021-03-23 17:46:48','shuai.liu','2021-03-23 17:46:48','0','安卓',NULL,NULL,NULL);
insert into `app_mgt_app_info` (`app_name`, `app_cname`, `app_status`, `git_url`, `git_path`, `svn_url`, `svn_path`, `app_jdk_version`, `app_desc`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `lib_location`, `platform_type`, `platform_time`, `third_party_middleware`) values('crm-android','安卓CRM','1','howbuy_android','/howbuycrm','','','','','shuai.liu','2021-03-23 18:19:32','shuai.liu','2021-03-23 18:19:32','0','安卓',NULL,NULL,NULL);
