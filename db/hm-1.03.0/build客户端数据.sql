/*
SQLyog Ultimate v12.08 (64 bit)
MySQL - 5.7.17 
*********************************************************************
上线的时候 要改为实际的app id
*/
/*!40101 SET NAMES utf8 */;

insert into `app_mgt_app_build` (`app_id`, `module_name`, `module_code`, `module_version`, `package_type`, `package_name`, `package_full`, `build_jdk_version`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `need_mock`, `mock_build_cmd`, `build_cmd`) values('618','crm-android','','','android','','1','','shuai.liu','2021-03-23 18:19:32','shuai.liu','2021-03-23 18:19:32','0',NULL,'','');
insert into `app_mgt_app_build` (`app_id`, `module_name`, `module_code`, `module_version`, `package_type`, `package_name`, `package_full`, `build_jdk_version`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `need_mock`, `mock_build_cmd`, `build_cmd`) values('611','crm-ios','','','ios','','1','','shuai.liu','2021-03-23 17:29:25','shuai.liu','2021-03-23 17:29:25','0',NULL,'','');
insert into `app_mgt_app_build` (`app_id`, `module_name`, `module_code`, `module_version`, `package_type`, `package_name`, `package_full`, `build_jdk_version`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `need_mock`, `mock_build_cmd`, `build_cmd`) values('616','fund-android','','','android','','1','','shuai.liu','2021-03-23 17:46:12','shuai.liu','2021-03-23 17:46:12','0',NULL,'','');
insert into `app_mgt_app_build` (`app_id`, `module_name`, `module_code`, `module_version`, `package_type`, `package_name`, `package_full`, `build_jdk_version`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `need_mock`, `mock_build_cmd`, `build_cmd`) values('609','fund-ios','','','ios','','1','','shuai.liu','2021-03-23 16:24:46','shuai.liu','2021-03-23 16:24:46','0',NULL,'','');
insert into `app_mgt_app_build` (`app_id`, `module_name`, `module_code`, `module_version`, `package_type`, `package_name`, `package_full`, `build_jdk_version`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `need_mock`, `mock_build_cmd`, `build_cmd`) values('614','fund-ios-accountcenter','','','ios','','1','','shuai.liu','2021-03-23 17:39:44','shuai.liu','2021-03-23 17:39:44','0',NULL,'','');
insert into `app_mgt_app_build` (`app_id`, `module_name`, `module_code`, `module_version`, `package_type`, `package_name`, `package_full`, `build_jdk_version`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `need_mock`, `mock_build_cmd`, `build_cmd`) values('613','fund-ios-private','','','ios','','1','','shuai.liu','2021-03-23 17:37:29','shuai.liu','2021-03-23 17:37:29','0',NULL,'','');
insert into `app_mgt_app_build` (`app_id`, `module_name`, `module_code`, `module_version`, `package_type`, `package_name`, `package_full`, `build_jdk_version`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `need_mock`, `mock_build_cmd`, `build_cmd`) values('612','fund-ios-public','','','ios','','1','','shuai.liu','2021-03-23 17:35:52','shuai.liu','2021-03-23 17:35:52','0',NULL,'','');
insert into `app_mgt_app_build` (`app_id`, `module_name`, `module_code`, `module_version`, `package_type`, `package_name`, `package_full`, `build_jdk_version`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `need_mock`, `mock_build_cmd`, `build_cmd`) values('615','library','','','ios','','1','','shuai.liu','2021-03-23 17:42:10','shuai.liu','2021-03-23 17:42:10','0',NULL,'','');
insert into `app_mgt_app_build` (`app_id`, `module_name`, `module_code`, `module_version`, `package_type`, `package_name`, `package_full`, `build_jdk_version`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `need_mock`, `mock_build_cmd`, `build_cmd`) values('617','piggy-android','','','android','','1','','shuai.liu','2021-03-23 17:46:48','shuai.liu','2021-03-23 17:46:48','0',NULL,'','');
insert into `app_mgt_app_build` (`app_id`, `module_name`, `module_code`, `module_version`, `package_type`, `package_name`, `package_full`, `build_jdk_version`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `need_mock`, `mock_build_cmd`, `build_cmd`) values('610','piggy-ios','','','ios','','1','','shuai.liu','2021-03-23 17:28:28','shuai.liu','2021-03-23 17:28:28','0',NULL,'','');
