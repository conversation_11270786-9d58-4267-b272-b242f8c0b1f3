

insert into `script_management_nodecommandcustom` (`execCmd`, `operateType`, `minion_id`, `Operator`, `operateTime`, `appName`, `saltFunc`) values('{transit_ip}::all_code/nf-newpig/ /data/app/nf-newpig/ delete=True','code_update','w-ehowbuy-nginx-10-12-110-100','chi.he','2021-01-14 17:44:43','nf-newpig','rsync.rsync');
insert into `script_management_nodecommandcustom` (`execCmd`, `operateType`, `minion_id`, `Operator`, `operateTime`, `appName`, `saltFunc`) values('{transit_ip}::all_code/nf-newpig/ /data/app/nf-newpig/ delete=True','code_update','w-ehowbuy-nginx-10-12-110-101','chi.he','2021-01-14 17:44:43','nf-newpig','rsync.rsync');
insert into `script_management_nodecommandcustom` (`execCmd`, `operateType`, `minion_id`, `Operator`, `operateTime`, `appName`, `saltFunc`) values('{transit_ip}::all_code/nf-newpig/ /data/app/nf-newpig/ delete=True','code_update','w-ehowbuy-nginx-10-12-110-102','chi.he','2021-01-14 17:44:43','nf-newpig','rsync.rsync');
insert into `script_management_nodecommandcustom` (`execCmd`, `operateType`, `minion_id`, `Operator`, `operateTime`, `appName`, `saltFunc`) values('{transit_ip}::all_code/nf-newpig/ /data/app/nf-newpig/ delete=True','code_update','w-ehowbuy-nginx-10-12-110-99','chi.he','2021-01-14 17:44:43','nf-newpig','rsync.rsync');

insert into `script_management_nodecommandcustom` (`execCmd`, `operateType`, `minion_id`, `Operator`, `operateTime`, `appName`, `saltFunc`) values('{transit_ip}::all_code/nf-newpig/ /data/app/nf-newpig/ delete=True','code_update','j-ehowbuy-nginx-10-11-20-109','chi.he','2021-01-14 17:44:43','nf-newpig','rsync.rsync');
insert into `script_management_nodecommandcustom` (`execCmd`, `operateType`, `minion_id`, `Operator`, `operateTime`, `appName`, `saltFunc`) values('{transit_ip}::all_code/nf-newpig/ /data/app/nf-newpig/ delete=True','code_update','j-ehowbuy-nginx-10-11-21-17','chi.he','2021-01-14 17:44:43','nf-newpig','rsync.rsync');

insert into `script_management_nodecommandcustom` (`execCmd`, `operateType`, `minion_id`, `Operator`, `operateTime`, `appName`, `saltFunc`) values('{transit_ip}::all_code/nf-newpig/ /data/vsftpd-data/activity/gray/nf-newpig/ delete=True','code_update','hm-222-235','chi.he','2021-01-14 17:44:43','nf-newpig','rsync.rsync');


insert into `script_management_nodecommandcustom` (`execCmd`, `operateType`, `minion_id`, `Operator`, `operateTime`, `appName`, `saltFunc`) values('{transit_ip}::all_code/nf-newwap/ /data/app/nf-newwap/ delete=True','code_update','w-ehowbuy-nginx-10-12-110-100','chi.he','2021-01-14 17:44:43','nf-newwap','rsync.rsync');
insert into `script_management_nodecommandcustom` (`execCmd`, `operateType`, `minion_id`, `Operator`, `operateTime`, `appName`, `saltFunc`) values('{transit_ip}::all_code/nf-newwap/ /data/app/nf-newwap/ delete=True','code_update','w-ehowbuy-nginx-10-12-110-101','chi.he','2021-01-14 17:44:43','nf-newwap','rsync.rsync');
insert into `script_management_nodecommandcustom` (`execCmd`, `operateType`, `minion_id`, `Operator`, `operateTime`, `appName`, `saltFunc`) values('{transit_ip}::all_code/nf-newwap/ /data/app/nf-newwap/ delete=True','code_update','w-ehowbuy-nginx-10-12-110-102','chi.he','2021-01-14 17:44:43','nf-newwap','rsync.rsync');
insert into `script_management_nodecommandcustom` (`execCmd`, `operateType`, `minion_id`, `Operator`, `operateTime`, `appName`, `saltFunc`) values('{transit_ip}::all_code/nf-newwap/ /data/app/nf-newwap/ delete=True','code_update','w-ehowbuy-nginx-10-12-110-99','chi.he','2021-01-14 17:44:43','nf-newwap','rsync.rsync');

insert into `script_management_nodecommandcustom` (`execCmd`, `operateType`, `minion_id`, `Operator`, `operateTime`, `appName`, `saltFunc`) values('{transit_ip}::all_code/nf-newwap/ /data/app/nf-newwap/ delete=True','code_update','j-ehowbuy-nginx-10-11-20-109','chi.he','2021-01-14 17:44:43','nf-newwap','rsync.rsync');
insert into `script_management_nodecommandcustom` (`execCmd`, `operateType`, `minion_id`, `Operator`, `operateTime`, `appName`, `saltFunc`) values('{transit_ip}::all_code/nf-newwap/ /data/app/nf-newwap/ delete=True','code_update','j-ehowbuy-nginx-10-11-21-17','chi.he','2021-01-14 17:44:43','nf-newwap','rsync.rsync');

insert into `script_management_nodecommandcustom` (`execCmd`, `operateType`, `minion_id`, `Operator`, `operateTime`, `appName`, `saltFunc`) values('{transit_ip}::all_code/nf-newwap/ /data/vsftpd-data/activity/gray/nf-newwap/ delete=True','code_update','hm-222-235','chi.he','2021-01-14 17:44:43','nf-newwap','rsync.rsync');


insert into `script_management_nodecommandcustom` (`execCmd`, `operateType`, `minion_id`, `Operator`, `operateTime`, `appName`, `saltFunc`) values('{transit_ip}::all_code/nf-smasset/ /data/app/nf-smasset/ delete=True','code_update','w-ehowbuy-nginx-10-12-110-100','chi.he','2021-01-14 17:44:43','nf-smasset','rsync.rsync');
insert into `script_management_nodecommandcustom` (`execCmd`, `operateType`, `minion_id`, `Operator`, `operateTime`, `appName`, `saltFunc`) values('{transit_ip}::all_code/nf-smasset/ /data/app/nf-smasset/ delete=True','code_update','w-ehowbuy-nginx-10-12-110-101','chi.he','2021-01-14 17:44:43','nf-smasset','rsync.rsync');
insert into `script_management_nodecommandcustom` (`execCmd`, `operateType`, `minion_id`, `Operator`, `operateTime`, `appName`, `saltFunc`) values('{transit_ip}::all_code/nf-smasset/ /data/app/nf-smasset/ delete=True','code_update','w-ehowbuy-nginx-10-12-110-102','chi.he','2021-01-14 17:44:43','nf-smasset','rsync.rsync');
insert into `script_management_nodecommandcustom` (`execCmd`, `operateType`, `minion_id`, `Operator`, `operateTime`, `appName`, `saltFunc`) values('{transit_ip}::all_code/nf-smasset/ /data/app/nf-smasset/ delete=True','code_update','w-ehowbuy-nginx-10-12-110-99','chi.he','2021-01-14 17:44:43','nf-smasset','rsync.rsync');

insert into `script_management_nodecommandcustom` (`execCmd`, `operateType`, `minion_id`, `Operator`, `operateTime`, `appName`, `saltFunc`) values('{transit_ip}::all_code/nf-smasset/ /data/app/nf-smasset/ delete=True','code_update','j-ehowbuy-nginx-10-11-20-109','chi.he','2021-01-14 17:44:43','nf-smasset','rsync.rsync');
insert into `script_management_nodecommandcustom` (`execCmd`, `operateType`, `minion_id`, `Operator`, `operateTime`, `appName`, `saltFunc`) values('{transit_ip}::all_code/nf-smasset/ /data/app/nf-smasset/ delete=True','code_update','j-ehowbuy-nginx-10-11-21-17','chi.he','2021-01-14 17:44:43','nf-smasset','rsync.rsync');

insert into `script_management_nodecommandcustom` (`execCmd`, `operateType`, `minion_id`, `Operator`, `operateTime`, `appName`, `saltFunc`) values('{transit_ip}::all_code/nf-smasset/ /data/vsftpd-data/activity/gray/nf-smasset/ delete=True','code_update','hm-222-235','chi.he','2021-01-14 17:44:43','nf-smasset','rsync.rsync');



insert into `script_management_nodecommandcustom` (`execCmd`, `operateType`, `minion_id`, `Operator`, `operateTime`, `appName`, `saltFunc`) values('{transit_ip}::all_code/nv-vendor/ /data/app/nv-vendor/ delete=True','code_update','w-ehowbuy-nginx-10-12-110-100','chi.he','2021-01-14 17:44:43','nv-vendor','rsync.rsync');
insert into `script_management_nodecommandcustom` (`execCmd`, `operateType`, `minion_id`, `Operator`, `operateTime`, `appName`, `saltFunc`) values('{transit_ip}::all_code/nv-vendor/ /data/app/nv-vendor/ delete=True','code_update','w-ehowbuy-nginx-10-12-110-101','chi.he','2021-01-14 17:44:43','nv-vendor','rsync.rsync');
insert into `script_management_nodecommandcustom` (`execCmd`, `operateType`, `minion_id`, `Operator`, `operateTime`, `appName`, `saltFunc`) values('{transit_ip}::all_code/nv-vendor/ /data/app/nv-vendor/ delete=True','code_update','w-ehowbuy-nginx-10-12-110-102','chi.he','2021-01-14 17:44:43','nv-vendor','rsync.rsync');
insert into `script_management_nodecommandcustom` (`execCmd`, `operateType`, `minion_id`, `Operator`, `operateTime`, `appName`, `saltFunc`) values('{transit_ip}::all_code/nv-vendor/ /data/app/nv-vendor/ delete=True','code_update','w-ehowbuy-nginx-10-12-110-99','chi.he','2021-01-14 17:44:43','nv-vendor','rsync.rsync');

insert into `script_management_nodecommandcustom` (`execCmd`, `operateType`, `minion_id`, `Operator`, `operateTime`, `appName`, `saltFunc`) values('{transit_ip}::all_code/nv-vendor/ /data/app/nv-vendor/ delete=True','code_update','j-ehowbuy-nginx-10-11-20-109','chi.he','2021-01-14 17:44:43','nv-vendor','rsync.rsync');
insert into `script_management_nodecommandcustom` (`execCmd`, `operateType`, `minion_id`, `Operator`, `operateTime`, `appName`, `saltFunc`) values('{transit_ip}::all_code/nv-vendor/ /data/app/nv-vendor/ delete=True','code_update','j-ehowbuy-nginx-10-11-21-17','chi.he','2021-01-14 17:44:43','nv-vendor','rsync.rsync');

insert into `script_management_nodecommandcustom` (`execCmd`, `operateType`, `minion_id`, `Operator`, `operateTime`, `appName`, `saltFunc`) values('{transit_ip}::all_code/nv-vendor/ /data/vsftpd-data/activity/gray/nv-vendor/ delete=True','code_update','hm-222-235','chi.he','2021-01-14 17:44:43','nv-vendor','rsync.rsync');

