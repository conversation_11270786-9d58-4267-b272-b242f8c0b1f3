/*
SQLyog Ultimate v12.08 (64 bit)
MySQL - 5.7.17 
*********************************************************************
上线的时候 要改为实际的app id
*/
/*!40101 SET NAMES utf8 */;

insert into `app_mgt_app_module` (`app_id`, `module_name`, `module_code`, `module_status`, `module_desc`, `module_svn_path`, `module_jdk_version`, `need_online`, `need_check`, `app_port`, `container_name`, `create_path`, `lib_repo`, `deploy_path`, `extend_attr`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `zeus_type`, `need_ops`, `third_party_middleware`) values('618','crm-android','','1','安卓CRM','','','1','1',NULL,'','','','',NULL,'shuai.liu','2021-03-23 18:19:32','shuai.liu','2021-03-23 18:19:32','0','0','1',NULL);
insert into `app_mgt_app_module` (`app_id`, `module_name`, `module_code`, `module_status`, `module_desc`, `module_svn_path`, `module_jdk_version`, `need_online`, `need_check`, `app_port`, `container_name`, `create_path`, `lib_repo`, `deploy_path`, `extend_attr`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `zeus_type`, `need_ops`, `third_party_middleware`) values('611','crm-ios','','1','IOSCRM','','','1','1',NULL,'','','','',NULL,'shuai.liu','2021-03-23 17:29:25','shuai.liu','2021-03-23 17:29:25','0','0','1','0');
insert into `app_mgt_app_module` (`app_id`, `module_name`, `module_code`, `module_status`, `module_desc`, `module_svn_path`, `module_jdk_version`, `need_online`, `need_check`, `app_port`, `container_name`, `create_path`, `lib_repo`, `deploy_path`, `extend_attr`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `zeus_type`, `need_ops`, `third_party_middleware`) values('616','fund-android','','1','安卓掌机','','','1','1',NULL,'','','','',NULL,'shuai.liu','2021-03-23 17:46:12','shuai.liu','2021-03-23 17:46:12','0','0','1','0');
insert into `app_mgt_app_module` (`app_id`, `module_name`, `module_code`, `module_status`, `module_desc`, `module_svn_path`, `module_jdk_version`, `need_online`, `need_check`, `app_port`, `container_name`, `create_path`, `lib_repo`, `deploy_path`, `extend_attr`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `zeus_type`, `need_ops`, `third_party_middleware`) values('609','fund-ios','','1','ios掌机','','','1','1',NULL,'','','','',NULL,'shuai.liu','2021-03-23 16:24:46','shuai.liu','2021-03-23 16:24:46','0','0','1','0');
insert into `app_mgt_app_module` (`app_id`, `module_name`, `module_code`, `module_status`, `module_desc`, `module_svn_path`, `module_jdk_version`, `need_online`, `need_check`, `app_port`, `container_name`, `create_path`, `lib_repo`, `deploy_path`, `extend_attr`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `zeus_type`, `need_ops`, `third_party_middleware`) values('614','fund-ios-accountcenter','','1','fund-ios-accountcenter','','','0','1',NULL,'','','','',NULL,'shuai.liu','2021-03-23 17:39:44','shuai.liu','2021-03-23 17:39:44','0','0','0','0');
insert into `app_mgt_app_module` (`app_id`, `module_name`, `module_code`, `module_status`, `module_desc`, `module_svn_path`, `module_jdk_version`, `need_online`, `need_check`, `app_port`, `container_name`, `create_path`, `lib_repo`, `deploy_path`, `extend_attr`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `zeus_type`, `need_ops`, `third_party_middleware`) values('613','fund-ios-private','','1','fund_private','','','0','1',NULL,'','','','',NULL,'shuai.liu','2021-03-23 17:37:29','shuai.liu','2021-03-23 17:37:29','0','0','0','0');
insert into `app_mgt_app_module` (`app_id`, `module_name`, `module_code`, `module_status`, `module_desc`, `module_svn_path`, `module_jdk_version`, `need_online`, `need_check`, `app_port`, `container_name`, `create_path`, `lib_repo`, `deploy_path`, `extend_attr`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `zeus_type`, `need_ops`, `third_party_middleware`) values('612','fund-ios-public','','1','ios-public','','','0','1',NULL,'','','','',NULL,'shuai.liu','2021-03-23 17:35:52','shuai.liu','2021-03-23 17:35:52','0','0','0','0');
insert into `app_mgt_app_module` (`app_id`, `module_name`, `module_code`, `module_status`, `module_desc`, `module_svn_path`, `module_jdk_version`, `need_online`, `need_check`, `app_port`, `container_name`, `create_path`, `lib_repo`, `deploy_path`, `extend_attr`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `zeus_type`, `need_ops`, `third_party_middleware`) values('615','library','','1','library','','','0','1',NULL,'','','','',NULL,'shuai.liu','2021-03-23 17:42:10','shuai.liu','2021-03-23 17:42:10','0','0','0','0');
insert into `app_mgt_app_module` (`app_id`, `module_name`, `module_code`, `module_status`, `module_desc`, `module_svn_path`, `module_jdk_version`, `need_online`, `need_check`, `app_port`, `container_name`, `create_path`, `lib_repo`, `deploy_path`, `extend_attr`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `zeus_type`, `need_ops`, `third_party_middleware`) values('617','piggy-android','','1','安卓储蓄罐','','','1','1',NULL,'','','','',NULL,'shuai.liu','2021-03-23 17:46:48','shuai.liu','2021-03-23 17:46:48','0','0','1','0');
insert into `app_mgt_app_module` (`app_id`, `module_name`, `module_code`, `module_status`, `module_desc`, `module_svn_path`, `module_jdk_version`, `need_online`, `need_check`, `app_port`, `container_name`, `create_path`, `lib_repo`, `deploy_path`, `extend_attr`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `zeus_type`, `need_ops`, `third_party_middleware`) values('610','piggy-ios','','1','IOS储蓄罐','','','1','1',NULL,'','','','',NULL,'shuai.liu','2021-03-23 17:28:28','shuai.liu','2021-03-23 17:28:28','0','0','1','0');
