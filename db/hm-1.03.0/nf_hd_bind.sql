
INSERT INTO `env_mgt_node_bind` (`module_name`, `suite_id`, `node_id`, `node_port`, `node_docker`, `node_bind_desc`, `deploy_group`, `deploy_type`, `deploy_path`, `health_check_url`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `tomcat_path`, `script_path`, `script_name`, `config_path`, `log_path`, `enable_bind`, `target_base_path`, `lib_repo_info_id`) VALUES('nf-newpig','113','3016','0',NULL,NULL,NULL,'2',NULL,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1',NULL,'508');
INSERT INTO `env_mgt_node_bind` (`module_name`, `suite_id`, `node_id`, `node_port`, `node_docker`, `node_bind_desc`, `deploy_group`, `deploy_type`, `deploy_path`, `health_check_url`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `tomcat_path`, `script_path`, `script_name`, `config_path`, `log_path`, `enable_bind`, `target_base_path`, `lib_repo_info_id`) VALUES('nf-newpig','114','3016','0',NULL,NULL,NULL,'2',NULL,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1',NULL,'910');
INSERT INTO `env_mgt_node_bind` (`module_name`, `suite_id`, `node_id`, `node_port`, `node_docker`, `node_bind_desc`, `deploy_group`, `deploy_type`, `deploy_path`, `health_check_url`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `tomcat_path`, `script_path`, `script_name`, `config_path`, `log_path`, `enable_bind`, `target_base_path`, `lib_repo_info_id`) VALUES('nf-newpig','115','3016','0',NULL,NULL,NULL,'2',NULL,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1',NULL,NULL);

INSERT INTO `env_mgt_node_bind` (`module_name`, `suite_id`, `node_id`, `node_port`, `node_docker`, `node_bind_desc`, `deploy_group`, `deploy_type`, `deploy_path`, `health_check_url`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `tomcat_path`, `script_path`, `script_name`, `config_path`, `log_path`, `enable_bind`, `target_base_path`, `lib_repo_info_id`) VALUES('nf-vendor','113','3016','0',NULL,NULL,NULL,'2',NULL,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1',NULL,'508');
INSERT INTO `env_mgt_node_bind` (`module_name`, `suite_id`, `node_id`, `node_port`, `node_docker`, `node_bind_desc`, `deploy_group`, `deploy_type`, `deploy_path`, `health_check_url`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `tomcat_path`, `script_path`, `script_name`, `config_path`, `log_path`, `enable_bind`, `target_base_path`, `lib_repo_info_id`) VALUES('nf-vendor','114','3016','0',NULL,NULL,NULL,'2',NULL,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1',NULL,'910');
INSERT INTO `env_mgt_node_bind` (`module_name`, `suite_id`, `node_id`, `node_port`, `node_docker`, `node_bind_desc`, `deploy_group`, `deploy_type`, `deploy_path`, `health_check_url`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `tomcat_path`, `script_path`, `script_name`, `config_path`, `log_path`, `enable_bind`, `target_base_path`, `lib_repo_info_id`) VALUES('nf-vendor','115','3016','0',NULL,NULL,NULL,'2',NULL,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1',NULL,NULL);

INSERT INTO `env_mgt_node_bind` (`module_name`, `suite_id`, `node_id`, `node_port`, `node_docker`, `node_bind_desc`, `deploy_group`, `deploy_type`, `deploy_path`, `health_check_url`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `tomcat_path`, `script_path`, `script_name`, `config_path`, `log_path`, `enable_bind`, `target_base_path`, `lib_repo_info_id`) VALUES('nf-newwap','113','3016','0',NULL,NULL,NULL,'2',NULL,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1',NULL,'508');
INSERT INTO `env_mgt_node_bind` (`module_name`, `suite_id`, `node_id`, `node_port`, `node_docker`, `node_bind_desc`, `deploy_group`, `deploy_type`, `deploy_path`, `health_check_url`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `tomcat_path`, `script_path`, `script_name`, `config_path`, `log_path`, `enable_bind`, `target_base_path`, `lib_repo_info_id`) VALUES('nf-newwap','114','3016','0',NULL,NULL,NULL,'2',NULL,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1',NULL,'910');
INSERT INTO `env_mgt_node_bind` (`module_name`, `suite_id`, `node_id`, `node_port`, `node_docker`, `node_bind_desc`, `deploy_group`, `deploy_type`, `deploy_path`, `health_check_url`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `tomcat_path`, `script_path`, `script_name`, `config_path`, `log_path`, `enable_bind`, `target_base_path`, `lib_repo_info_id`) VALUES('nf-newwap','115','3016','0',NULL,NULL,NULL,'2',NULL,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1',NULL,NULL);

INSERT INTO `env_mgt_node_bind` (`module_name`, `suite_id`, `node_id`, `node_port`, `node_docker`, `node_bind_desc`, `deploy_group`, `deploy_type`, `deploy_path`, `health_check_url`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `tomcat_path`, `script_path`, `script_name`, `config_path`, `log_path`, `enable_bind`, `target_base_path`, `lib_repo_info_id`) VALUES('nf-smasset','113','3016','0',NULL,NULL,NULL,'2',NULL,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1',NULL,'508');
INSERT INTO `env_mgt_node_bind` (`module_name`, `suite_id`, `node_id`, `node_port`, `node_docker`, `node_bind_desc`, `deploy_group`, `deploy_type`, `deploy_path`, `health_check_url`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `tomcat_path`, `script_path`, `script_name`, `config_path`, `log_path`, `enable_bind`, `target_base_path`, `lib_repo_info_id`) VALUES('nf-smasset','114','3016','0',NULL,NULL,NULL,'2',NULL,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1',NULL,'910');
INSERT INTO `env_mgt_node_bind` (`module_name`, `suite_id`, `node_id`, `node_port`, `node_docker`, `node_bind_desc`, `deploy_group`, `deploy_type`, `deploy_path`, `health_check_url`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `tomcat_path`, `script_path`, `script_name`, `config_path`, `log_path`, `enable_bind`, `target_base_path`, `lib_repo_info_id`) VALUES('nf-smasset','115','3016','0',NULL,NULL,NULL,'2',NULL,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1',NULL,NULL);
