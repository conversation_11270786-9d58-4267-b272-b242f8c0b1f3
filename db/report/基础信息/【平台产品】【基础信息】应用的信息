%spider



select * from
(
select
a.module_name as '应用名',

case a.package_type
when 'jar' then (case when a.package_full = '1' then '一个jar' else '一个jar+零散libs' end)
when 'war' then 'war包解压零散class'
when 'remote' then 'h5-服务端部署'
when 'dist' then 'h5-APP嵌入'
when 'static' then '静态资源'
when 'h5' then '静态资源'
when 'py' then 'python静态文件'
else '不明确！！！'
end as '制品库【包形态】',

case when a.platform_type='1' then
(
case when a.br_name is not null then '新流水线部署' else '新流水线（未上线）' end
)
else '其他' end as '构建集成工具',

concat('git-code@192.168.222.153:/data/git-code/', a.lib_repo, '.git') as '包制品库路径',

case when a.br_name is not null then a.br_name else 'MASTER' end as '包上线版本',

case when a.lib_repo_config_path is not null then a.lib_repo_config_path else
(
case a.team_alias
when '达摩院' then concat('【未维护】 ec/', a.lib_repo)
when '爱码仕' then concat('【未维护】 ec/', a.lib_repo)
when '藏经阁' then concat('【未维护】 tp/', a.lib_repo)
when '六扇门' then concat('【未维护】 crm/', a.lib_repo)
else '不明确！！！'
end
)
end as '产线外移文件库',

concat('/', a.module_name, '/') as '测试外移文件库',

case when a.zeus_type='0' then '搞不清楚！！！'
     when a.zeus_type='1' then '新配置系统-宙斯'
     when a.zeus_type='2' then '配置魔方1代'
     when a.zeus_type='3' then '配置魔方2代'
     when a.zeus_type='4' then 'CCMS'
     when a.zeus_type='5' then '本地外移文件'
     else '其他！！！'
     end as '配置系统',

case when a.zeus_type='0' then '【不支持！！！】'
     when a.zeus_type='1' then '【源_产线外移库】 【操作_改版本和环境】 【部署平台】'
     when a.zeus_type='2' then '【不支持！！！】'
     when a.zeus_type='3' then '【不支持！！！】'
     when a.zeus_type='4' then '【源_产线外移库】 【操作_根据规则替换】 【宙斯接口】'
     when a.zeus_type='5' then '【源_测试外移库】 【操作_根据规则替换】 【宙斯接口】'
     else '其他！！！'
     end as '一键外移文件处理',

case when a.zeus_type='0' then '【不处理！！！】'
     when a.zeus_type='1' then '【调宙斯接口】'
     when a.zeus_type='2' then '【不处理！！！】'
     when a.zeus_type='3' then '【不处理！！！】'
     when a.zeus_type='4' then '【源_产线CCMS导出】 【操作_根据规则替换】 【宙斯接口】'
     when a.zeus_type='5' then '【不处理！！！】'
     else '其他！！！'
     end as '配置系统KV处理',

case when a.deploy_type in ('1','3') then
-- 包默认部署路径============================================================================================================================
(
case when a.deploy_path is not null then concat('【包默认路径*全局指定】 ',a.deploy_path) else
(
case when a.package_type = 'war' then
(
case when a.container_name is not null then concat('【包默认路径*全局指定】 ','/data/app/',a.container_name,'/',a.module_name,'/') else concat('【包默认路径*全局指定】 ','/data/app/',a.module_name,'/',a.module_name,'/') end
)
else concat('【包默认路径*通用】 ','/data/app/',a.module_name,'/lib/') end
)
end
)
-- 包默认部署路径============================================================================================================================
else
(
case when a.deploy_type = '2' then
-- K8s镜像工厂路径============================================================================================================================
(
case when a.deploy_path is not null then concat('【K8s镜像工厂路径】 ',a.deploy_path) else
(
case when a.container_name is not null then
(
case when a.package_type = 'h5'
then concat('【K8s镜像工厂路径】 ','/home/<USER>/img-factory-online/【环境套】/',a.container_name,'/',a.package_name,'/')
else concat('【K8s镜像工厂路径】 ','/home/<USER>/img-factory-online/【环境套】/',a.container_name,'/',a.container_name,'/')
end
)
else
(
case when a.package_type = 'h5'
then concat('【K8s镜像工厂路径】 ','/home/<USER>/img-factory-online/【环境套】/',a.module_name,'/',a.package_name,'/')
else concat('【K8s镜像工厂路径】 ','/home/<USER>/img-factory-online/【环境套】/',a.module_name,'/',a.module_name,'/')
end
)
end
)
end
)
-- K8s镜像工厂路径============================================================================================================================
else '不明确！！！' end
)
end as '默认程序包路径',

case when a.deploy_type in ('1','3') then
-- 外移文件部署路径============================================================================================================================
(
case when a.config_path is not null then concat('【外移文件路径*全局指定】 ',a.config_path) else
(
case when a.package_type = 'war' then
(
case when a.container_name is not null then concat('【外移文件路径*全局指定】 ','/data/app_resource/',a.container_name,'/',a.module_name,'/') else concat('【外移文件路径*全局指定】 ','/data/app_resource/',a.module_name,'/',a.module_name,'/') end
)
else concat('【外移文件路径*通用】 ','/data/app_resource/',a.module_name,'/') end
)
end
)
-- 外移文件部署路径============================================================================================================================
else
(
case when a.deploy_type = '2' then
-- K8s_CM工厂路径============================================================================================================================
(
case when a.deploy_path is not null then concat('【K8s_CM工厂路径】 ',a.deploy_path) else
(
case when a.container_name is not null then
(
case when a.package_type = 'h5' then concat('【K8s_CM工厂路径】 ','/home/<USER>/config/【环境套】/',a.container_name,'/',a.package_name,'/')
     when a.package_type = 'war' then concat('【K8s_CM工厂路径】 ','/home/<USER>/config/【环境套】/tomcat_',a.container_name,'/WEB-INF/classes/')
     else concat('【K8s_CM工厂路径】 ','/home/<USER>/config/【环境套】/remote_',a.container_name,'/')
end
)
else
(
case when a.package_type = 'h5' then concat('【K8s_CM工厂路径】 ','/home/<USER>/config/【环境套】/',a.module_name,'/',a.package_name,'/')
     when a.package_type = 'war' then concat('【K8s_CM工厂路径】 ','/home/<USER>/config/【环境套】/tomcat_',a.module_name,'/WEB-INF/classes/')
     else concat('【K8s_CM工厂路径】 ','/home/<USER>/config/【环境套】/remote_',a.module_name,'/')
end
)
end
)
end
)
-- K8s_CM工厂路径============================================================================================================================
else '不明确！！！' end
)
end as '默认外移文件路径',

case when a.auto_deploy_tenv='1' then
(
case when a.deploy_type is not null then '支持并运行' else '还未启用' end
)
else '不支持' end as '一键部测试环境',

/*
case when a.platform_type='1' then '研发团队' else
(case when a.deploy_type = '2' then '运维团队' else '研发团队' end)
end as '【外移文件】谁维护',

case when a.platform_type='1' and a.zeus_type='1' then '【新流水线】自动部' else
(
case when a.deploy_type = '2' then '【运维】人工配K8s' else  '【研发】人工部' end
)
end as '【外移文件】部署方式',

case when a.platform_type='1' then '【git】by【运维团队】' else
(case when a.deploy_type = '2' then '【svn】by【运维团队】' else '不指定' end)
end as '【外移文件】仓库位置',

case when a.deploy_type = '2' then '【运维】人工配K8s' else '【研发】人工部' end as '【外移文件】首次部署',
*/

case when a.deploy_type in ('1','3') then 'IP节点' else
(
case when a.deploy_type = '2' then 'K8s容器' else '不明确！！！' end
)
end as '测试部署形态',

case when a.git_url is not null and a.git_url <> '' then concat('【GIT】 http://gitlab-code.howbuy.pa/',a.git_url,a.git_path) else
(
case when a.trunkPath is not null then concat('【SVN】 ',a.trunkPath) else concat('【SVN】 ',a.svn_path) end
)
end as '代码地址',

case when a.module_jdk_version is not null and a.module_jdk_version <> '' then a.module_jdk_version else a.app_jdk_version end as '编译JDK版本',

case a.third_party_middleware
when '0' then '自研'
when '1' then '第三方'
else '不明确！！！'
end as '软件性质',

a.team_alias as '所属团队',

concat('【', (ifnull(a.need_check, 0)*10 + ifnull(a.app_status, 0)*100 + ifnull(a.need_ops, 0)), '】', ifnull(a.package_name, '不明确包名！')) as '还有效|在维护|产线阶段',

case when a.package_type in ('jar','war') then 'JAVA类型' else
(
case when a.package_type in ('remote', 'dist', 'static', 'h5') then '静态类型' else '其他' end
)
end as '应用类型',

need_online as '是否上线模块'

from
(
-- ============================================================================================================================


SELECT
	distinct
	am.module_name, am.zeus_type, am.lib_repo, am.module_jdk_version, am.need_check, am.need_ops, am.need_online,
	ai.git_url, ai.git_path, ai.svn_url, ai.svn_path, ai.platform_type, ai.third_party_middleware, ai.app_jdk_version, ai.app_status,
	ab.package_type, ab.package_full, ab.package_name, ab.build_jdk_version,
	nb.deploy_type,
	a.team_alias,
	b.auto_deploy_tenv,
	sa.trunkPath,
	di.container_name, di.deploy_path, di.config_path, di.lib_repo_config_path,
	op.br_name
FROM
	spider.app_mgt_app_module am
inner join
spider.app_mgt_app_info ai
on
am.app_id=ai.id

-- 拼接部署形态============================================================================================================================
left outer join
(
select distinct nb.module_name, nb.deploy_type
from spider.env_mgt_node_bind nb,spider.env_mgt_suite s
where nb.suite_id = s.id and s.region_id = '2' and nb.enable_bind = '1'
) nb
on
am.module_name=nb.module_name
-- 拼接部署形态============================================================================================================================

-- 拼接制品形态============================================================================================================================
left outer join
spider.app_mgt_app_build ab
on
am.app_id=ab.app_id and am.module_name=ab.module_name
-- 拼接制品形态============================================================================================================================

-- 拼接所属团队============================================================================================================================
LEFT OUTER JOIN
(
select  distinct am.module_name as app_name,
(
select group_concat(distinct ti.team_alias order by team_alias) as team_alias
from spider.app_mgt_app_team at, spider.tool_mgt_team_info ti
where at.app_id=ai.id and at.team_id=ti.id
group by am.module_name
order by team_alias
) as team_alias
from spider.app_mgt_app_info ai, spider.app_mgt_app_module am
where am.app_id=ai.id and am.need_online='1'
) a
on am.module_name = a.app_name
-- 拼接所属团队============================================================================================================================

-- 拼接是否一键部署测试环境============================================================================================================================
left outer join
(
SELECT distinct tt.module_name, '1' as auto_deploy_tenv FROM spider.app_mgt_test_template tt
) b
on am.module_name = b.module_name
-- 拼接是否一键部署测试环境============================================================================================================================

-- 拼接老部署平台的SVN主干地址============================================================================================================================
left outer join
django_scm.common_service_artifactinfo sa
on sa.appName = am.module_name
-- 拼接老部署平台的SVN主干地址============================================================================================================================


-- 拼接测试环境部署路径默认值============================================================================================================================
left outer join
spider.publish_deploy_info di
on am.module_name = di.module_name

-- 拼接测试环境部署路径默认值============================================================================================================================

left outer join
(
SELECT v.module_name, m.br_name FROM iter_mgt_publish_application m
INNER JOIN
(
SELECT	am.module_name, max(pa.id) as max_pub_id
FROM iter_mgt_publish_application pa, app_mgt_app_module am
WHERE pa.env = 'prod' and pa.appName like concat('%', am.module_name, '%') and am.need_online = '1'
GROUP BY am.module_name
) v
ON v.max_pub_id = m.id
) op
on op.module_name = am.module_name



where am.need_online in ('1','0')


-- ============================================================================================================================
) a

) b




where
-- 选团队大类============================================================================================================================
(
( '${可选择【团队大类】：=,|仅研发部门|仅其他部门}' = '仅研发部门' and b.所属团队 in ('达摩院','爱码仕','藏经阁','六扇门','解忧杂货铺','及时雨','码上飘') )
or
( '${可选择【团队大类】：=,|仅研发部门|仅其他部门}' = '仅其他部门' and b.所属团队 not in ('达摩院','爱码仕','藏经阁','六扇门','解忧杂货铺','及时雨','码上飘') )
or
'${可选择【团队大类】：=,|仅研发部门|仅其他部门}' = ''
)
-- 选团队大类============================================================================================================================

-- 选所属团队============================================================================================================================
and
(
(b.所属团队 like '${可选择【团队】：=,|达摩院|爱码仕|藏经阁|六扇门|宙斯盾|罗刹堂|解忧杂货铺|毗沙门|银河战队|及时雨|码上飘|MACH}%' and '${可选择【团队】：=,|达摩院|爱码仕|藏经阁|六扇门|宙斯盾|罗刹堂|解忧杂货铺|毗沙门|银河战队|及时雨|码上飘|MACH}' <> '')
or
('${可选择【团队】：=,|达摩院|爱码仕|藏经阁|六扇门|宙斯盾|罗刹堂|解忧杂货铺|毗沙门|银河战队|及时雨|码上飘|MACH}' = '')
)
-- 选所属团队============================================================================================================================

-- 选JAVA服务============================================================================================================================
and
(
( '${可选应用类型：=,|JAVA类型|静态类型|其他}' <> '' and b.应用类型 like '${可选应用类型：=,|JAVA类型|静态类型|其他}')
or
'${可选应用类型：=,|JAVA类型|静态类型|其他}' = ''
)
-- 选JAVA服务============================================================================================================================

-- 仅上线模块============================================================================================================================
and
(
( '${是否仅上线模块：=,|1|0}' <> '' and b.是否上线模块 like '${是否仅上线模块：=,|1|0}')
or
'${是否仅上线模块：=,|1|0}' = ''
)
-- 仅上线模块============================================================================================================================


-- 选部署工具============================================================================================================================
and
(
('${可选【CI工具】：=,|新流水线部署|新流水线（未上线）|其他}' <> '' and b.构建集成工具 like '${可选【CI工具】：=,|新流水线部署|新流水线（未上线）|其他}%')
or
'${可选【CI工具】：=,|新流水线部署|新流水线（未上线）|其他}' = ''
)
-- 选部署工具============================================================================================================================

-- 选是否一键部署============================================================================================================================
and b.一键部测试环境 like '${可选【是否支持一键部署】：=,|支持并运行|还未启用|不支持}%'
-- 选是否一键部署============================================================================================================================

-- 选部署形式============================================================================================================================
and b.测试部署形态 like '${可选【测试部署形态】：=,|IP节点|K8s容器|不明确！！！}%'
-- 选部署形式============================================================================================================================

and b.软件性质 like '${【自研】【第三方】：=,|自研|第三方|不明确！！！}%'

and b.配置系统 like '${【配置系统】：=,|新配置系统-宙斯|配置魔方|CCMS|本地外移文件|搞不清楚！！！|其他！！！}%'

and lower(b.应用名) like lower('%${可输入应用【关键词】：}%')

and b.是否上线模块 like '${是否上线模块：=,|1|0}%'


order by b.应用名, b.测试部署形态 desc


