%spider



select * from
(
select
a.module_name as '应用名',

case when a.deploy_type in ('1','3') then concat('【IP节点】 ', a.deploy_location) else
(
case when a.deploy_type = '2' then concat('【K8s容器】 ', a.deploy_location) else '不明确！！！' end
)
end as '测试部署位置',

case when a.deploy_type in ('1','3') then
-- 包路径============================================================================================================================
(
case when a.suite_deploy_path is not null then concat('【包路径*环境指定】 ',a.suite_deploy_path,'/') else
(
case when a.deploy_path is not null then concat('【包路径*全局指定】 ',a.deploy_path,'/') else
(
case when a.package_type = 'war' then
(
case when a.container_name is not null then concat('【包路径*全局指定】 ','/data/app/',a.container_name,'/',a.module_name,'/') else concat('【包路径*通用】 ','/data/app/',a.module_name,'/',a.module_name,'/') end
)
else concat('【包路径*通用】 ','/data/app/',a.module_name,'/lib/') end
)
end
)
end
)
-- 包路径============================================================================================================================
else
(
case when a.deploy_type = '2' then
-- K8s镜像名============================================================================================================================
(
case when a.container_name is not null then
(
case when a.package_type = 'h5'
then concat('【K8s镜像名】 ',a.package_name)
else concat('【K8s镜像名】 ',a.container_name)
end
)
else
(
case when a.package_type = 'h5'
then concat('【K8s镜像名】 ',a.package_name)
else concat('【K8s镜像名】 ',a.module_name)
end
)
end
)
-- K8s镜像名============================================================================================================================
else '不明确！！！' end
)
end as '程序包路径',

case when a.deploy_type in ('1','3') then
-- 外移文件部署路径============================================================================================================================
(
case when a.suite_config_path is not null then concat('【外移文件*环境指定】 ',a.suite_config_path,'/') else
(
case when a.config_path is not null then concat('【外移文件*全局指定】 ',a.config_path,'/') else
(
case when a.package_type = 'war' then
(
case when a.container_name is not null then concat('【外移文件*全局指定】 ','/data/app_resource/',a.container_name,'/',a.module_name,'/') else concat('【外移文件*通用】 ','/data/app_resource/',a.module_name,'/',a.module_name,'/') end
)
else concat('【外移文件*通用】 ','/data/app_resource/',a.module_name,'/') end
)
end
)
end
)
-- 外移文件部署路径============================================================================================================================
else
(
case when a.deploy_type = '2' then
-- K8s_CM命名============================================================================================================================
(
case when a.container_name is not null then concat('【K8s_configmap*全局指定】 ',a.container_name) else concat('【K8s_configmap*通用】 ',a.module_name) end
)
-- K8s_CM命名============================================================================================================================
else '不明确！！！' end
)
end as '外移文件路径',

case when a.deploy_type in ('1','3') then
-- 启动脚本路径============================================================================================================================
(
case when a.suite_script_path is not null then concat('【脚本路径*环境指定】 ',a.suite_script_path,'/') else
(
case when a.script_path is not null then concat('【脚本路径*全局指定】 ',a.script_path,'/') else
(
case when a.package_type = 'war' then
(
case when a.container_name is not null then concat('【脚本路径*全局指定】 ','/user/local/',a.container_name,'/bin/') else concat('【脚本路径*通用】 ','/user/local/tomcat-',a.module_name,'/bin/') end
)
else concat('【脚本路径*通用】 ','/data/app/',a.module_name,'/bin/') end
)
end
)
end
)
-- 启动脚本路径============================================================================================================================
else
(
case when a.deploy_type = '2' then
-- K8s内启动脚本路径============================================================================================================================
(
case when a.container_name is not null then concat('【K8s内部路径*全局指定】 ','/data/app/',a.container_name,'/bin/') else concat('【K8s内部路径*通用】 ','/data/app/',a.module_name,'/bin/') end
)
-- K8s内启动脚本路径============================================================================================================================
else '不明确！！！' end
)
end as '启动脚本路径',

case when a.deploy_type in ('1','3') then
-- 启动脚本文件============================================================================================================================
(
case when a.suite_script_name is not null then concat('【脚本文件*环境指定】 ',a.suite_script_name,'/') else
(
case when a.script_name is not null then concat('【脚本文件*全局指定】 ',a.script_name,'/') else
(
case when a.package_type = 'war' then concat('【脚本文件*通用】 ','startup.sh') else concat('【脚本文件*通用】 ','control.sh') end
)
end
)
end
)
-- 启动脚本文件============================================================================================================================
else
(
-- K8s内启动脚本============================================================================================================================
case when a.deploy_type = '2' then concat('【K8s内脚本文件*通用】 ','start.sh') else '不明确！！！' end
-- K8s内启动脚本============================================================================================================================
)
end as '启动脚本文件',

case a.package_type
when 'jar' then (case when a.package_full = '1' then '一个jar' else '一个jar+零散libs' end)
when 'war' then 'war包解压零散class'
when 'h5' then 'h5静态文件'
when 'py' then 'python静态文件'
else '不明确！！！'
end as '制品库【包形态】',

-- case when a.br_name is not null then a.br_name else 'MASTER' end as '包上线版本',

case when a.zeus_type='0' then '搞不清楚！！！'
     when a.zeus_type='1' then '新配置系统-宙斯'
     when a.zeus_type='2' then '配置魔方1代'
     when a.zeus_type='3' then '配置魔方2代'
     when a.zeus_type='4' then 'CCMS'
     when a.zeus_type='5' then '本地外移文件'
     else '其他！！！'
     end as '配置系统',

case when a.platform_type='1' then
(
case when a.br_name is not null then '新流水线部署' else '新流水线（未上线）' end
)
else '其他' end as 'CI工具',

case when a.auto_deploy_tenv='1' then
(
case when a.deploy_type is not null then '支持并运行' else '还未启用' end
)
else '不支持' end as '一键部测试环境',

a.suite_name as '部署环境',

case a.third_party_middleware
when '0' then '自研'
when '1' then '第三方'
else '不明确！！！'
end as '软件性质',

a.team_alias as '所属团队'

from
(
-- ============================================================================================================================


SELECT
	distinct
	am.module_name, am.zeus_type, am.lib_repo, am.module_jdk_version,
	ai.platform_type, ai.third_party_middleware, ai.app_jdk_version,
	ab.package_type, ab.package_full, ab.package_name,
	a.team_alias,
	b.auto_deploy_tenv,
	di.container_name, di.deploy_path, di.config_path, di.lib_repo_config_path, di.script_path, di.script_name,
	op.br_name,
	c.suite_name, c.deploy_type, c.deploy_path as suite_deploy_path, c.config_path as suite_config_path, c.deploy_location, c.script_path as suite_script_path, c.script_name as suite_script_name
FROM
	spider.app_mgt_app_module am
inner join
spider.app_mgt_app_info ai
on
am.app_id=ai.id

-- 拼接制品形态============================================================================================================================
left outer join
spider.app_mgt_app_build ab
on
am.app_id=ab.app_id and am.module_name=ab.module_name
-- 拼接制品形态============================================================================================================================

-- 拼接所属团队============================================================================================================================
LEFT OUTER JOIN
(
SELECT distinct ti.team_alias, am.module_name as app_name
from app_mgt_app_team at, spider.tool_mgt_team_info ti, app_mgt_app_info ai, spider.app_mgt_app_module am
where at.app_id=ai.id and at.team_id=ti.id and ai.app_status = '1' and am.app_id=ai.id
and concat(at.team_id,'|',at.app_id) in
-- 应用只属于更早创建的那个团队============================================================================================================================
(select concat(min(at.team_id),'|',at.app_id) from app_mgt_app_team at group by at.app_id)
-- 应用只属于更早创建的那个团队============================================================================================================================
) a
on am.module_name = a.app_name
-- 拼接所属团队============================================================================================================================

-- 拼接是否一键部署测试环境============================================================================================================================
left outer join
(
SELECT distinct tt.module_name, '1' as auto_deploy_tenv FROM spider.app_mgt_test_template tt
) b
on am.module_name = b.module_name
-- 拼接是否一键部署测试环境============================================================================================================================


-- 拼接测试环境部署路径默认值============================================================================================================================
left outer join
spider.publish_deploy_info di
on am.module_name = di.module_name
-- 拼接测试环境部署路径默认值============================================================================================================================

-- 拼接具体环境套和环境============================================================================================================================
left outer join
(
select distinct
nb.module_name, nb.deploy_path, nb.config_path, nb.deploy_type, nb.script_path, nb.script_name,
case when nb.deploy_type = '2' then nb.node_docker else n.node_ip end as deploy_location,
s.suite_name, s.suite_code
from spider.env_mgt_node_bind nb
inner join spider.env_mgt_suite s
on s.suite_is_active = '1' and nb.suite_id = s.id
left outer join spider.env_mgt_node n
on n.node_status = '0' and n.id = nb.node_id and nb.deploy_type = '1'
where nb.enable_bind = '1'
) c
on am.module_name = c.module_name
-- 拼接具体环境套和环境============================================================================================================================

left outer join
(
SELECT v.module_name, m.br_name FROM iter_mgt_publish_application m
INNER JOIN
(
SELECT	am.module_name, max(pa.id) as max_pub_id
FROM iter_mgt_publish_application pa, app_mgt_app_module am
WHERE pa.env = 'prod' and pa.appName like concat('%', am.module_name, '%') and am.need_online = '1'
GROUP BY am.module_name
) v
ON v.max_pub_id = m.id
) op
on op.module_name = am.module_name


where am.need_online = '1' and am.need_check = '1'


-- ============================================================================================================================
) a

) b




where
-- 选团队大类============================================================================================================================
(
( '${可选择【团队大类】：=,|仅研发部门|仅其他部门}' = '仅研发部门' and b.所属团队 in ('达摩院','爱码仕','藏经阁','六扇门','解忧杂货铺','及时雨','码上飘') )
or
( '${可选择【团队大类】：=,|仅研发部门|仅其他部门}' = '仅其他部门' and b.所属团队 not in ('达摩院','爱码仕','藏经阁','六扇门','解忧杂货铺','及时雨','码上飘') )
or
'${可选择【团队大类】：=,|仅研发部门|仅其他部门}' = ''
)
-- 选团队大类============================================================================================================================

-- 选所属团队============================================================================================================================
and
(
(b.所属团队 like '${可选择【团队】：=,|达摩院|爱码仕|藏经阁|六扇门|宙斯盾|罗刹堂|解忧杂货铺|毗沙门|银河战队|及时雨|码上飘|MACH}%' and '${可选择【团队】：=,|达摩院|爱码仕|藏经阁|六扇门|宙斯盾|罗刹堂|解忧杂货铺|毗沙门|银河战队|及时雨|码上飘|MACH}' <> '')
or
('${可选择【团队】：=,|达摩院|爱码仕|藏经阁|六扇门|宙斯盾|罗刹堂|解忧杂货铺|毗沙门|银河战队|及时雨|码上飘|MACH}' = '')
)
-- 选所属团队============================================================================================================================

-- 选JAVA服务============================================================================================================================
and
(
( '${可选【JAVA服务/非JAVA服务】：=,|JAVA服务|非JAVA服务}' = 'JAVA服务' and b.制品库【包形态】 in ('war包解压零散class','一个jar','一个jar+零散libs') )
or
( '${可选【JAVA服务/非JAVA服务】：=,|JAVA服务|非JAVA服务}' = '非JAVA服务' and b.制品库【包形态】 not in ('war包解压零散class','一个jar','一个jar+零散libs') )
or
'${可选【JAVA服务/非JAVA服务】：=,|JAVA服务|非JAVA服务}' = ''
)
-- 选JAVA服务============================================================================================================================

-- 选部署工具============================================================================================================================
and
(
(
b.部署环境 in ('外高桥生产环境','ucloud-prod','虹口内网生产环境') and '${【部署环境】默认产线：=,|ms01|tms01|tms02|tms03|tms04|tms05|tms06|tms07|tms08|tms09|tms10|tms11|tms12|tms14|tms15|tms16|tms17|tms18|tp01|tp02|tp03|tp04|tp05|tp06|tp07|tp08|tp09|tp11|tp12|ucloud-prod|唐镇仿真环境|唐镇灾备环境|外高桥灰度环境|外高桥生产环境|虹口内网生产环境|默认开发环境|默认测试环境}' = '' and '${可输入节点【IP/环境】：}' = ''
)
or
(
(
'' <>
'${【部署环境】默认产线：=,|ms01|tms01|tms02|tms03|tms04|tms05|tms06|tms07|tms08|tms09|tms10|tms11|tms12|tms14|tms15|tms16|tms17|tms18|tp01|tp02|tp03|tp04|tp05|tp06|tp07|tp08|tp09|tp11|tp12|ucloud-prod|唐镇仿真环境|唐镇灾备环境|外高桥灰度环境|外高桥生产环境|虹口内网生产环境|默认开发环境|默认测试环境}'
or
'${可输入节点【IP/环境】：}' <> ''
)
and
b.部署环境 like
'${【部署环境】默认产线：=,|ms01|tms01|tms02|tms03|tms04|tms05|tms06|tms07|tms08|tms09|tms10|tms11|tms12|tms14|tms15|tms16|tms17|tms18|tp01|tp02|tp03|tp04|tp05|tp06|tp07|tp08|tp09|tp11|tp12|ucloud-prod|唐镇仿真环境|唐镇灾备环境|外高桥灰度环境|外高桥生产环境|虹口内网生产环境|默认开发环境|默认测试环境}%'
)
)
-- 选部署工具============================================================================================================================

-- 选部署工具============================================================================================================================
and
(
('${可选【CI工具】：=,|新流水线部署|新流水线（未上线）|其他}' <> '' and b.CI工具 like '${可选【CI工具】：=,|新流水线部署|新流水线（未上线）|其他}%')
or
'${可选【CI工具】：=,|新流水线部署|新流水线（未上线）|其他}' = ''
)
-- 选部署工具============================================================================================================================

-- 选是否一键部署============================================================================================================================
and b.一键部测试环境 like '${可选【是否支持一键部署】：=,|支持并运行|还未启用|不支持}%'
-- 选是否一键部署============================================================================================================================

-- 选部署形式============================================================================================================================
and b.测试部署位置 like '${可选【测试部署形态】：=,|【IP节点】|【K8s容器】|不明确！！！}%'
-- 选部署形式============================================================================================================================

and b.软件性质 like '${【自研】【第三方】：=,|自研|第三方|不明确！！！}%'

and b.配置系统 like '${【配置系统：=,|新配置系统-宙斯|配置魔方|CCMS|本地外移文件|搞不清楚！！！|其他！！！}%'

and lower(b.应用名) like lower('%${可输入应用【关键词】：}%')

and b.测试部署位置 like '%${可输入节点【IP/环境】：}%'


order by b.应用名, b.测试部署位置 desc


