%spider


SELECT
distinct
node_id,
node_ip as 节点地址,
node_status as 节点是否启用,
case when enable_bind = '是' and node_status = '是' then module_name else '没有部署应用' end as 应用名,
case when enable_bind = '是' and node_status = '是' then team_alias else '现不属于任何团队' end as 团队名,
case when enable_bind = '是' and node_status = '是' then third_party_middleware else '没有部署应用' end as 应用是否第三方,
case when enable_bind = '是' and node_status = '是' then need_check else '没有部署应用' end as 应用是否在维护,
case when enable_bind = '是' and node_status = '是' then suite_name else '没有部署应用' end as 绑定环境套名,
case when enable_bind = '是' and node_status = '是' then suite_is_active else '没有部署应用' end as 环境套有效性,
deploy_path as 部署路径,
node_bind_id as 节点绑定id,
group_name as 节点集群,
region_name as 机房类型编号
from
(

select x.* from
(
SELECT
	distinct
	n.node_ip, n.id as node_id,
	case when n.node_status = 0 then '是' else '否' end as node_status,
	nb.id as node_bind_id, nb.module_name, nb.deploy_path,
	case when nb.enable_bind = 1 then '是' else '否' end as enable_bind,
	aa.third_party_middleware, aa.need_check,
	s.suite_code, s.suite_name,
	case when s.suite_is_active = 1 then '有效' else '无效' end as suite_is_active,
	case when r2.region_group is null then r1.region_group else r2.region_group end as region_group,
	case when r2.region_name is null then r1.region_name else r2.region_name end as region_name,
	case when r2.region_is_active is null then r1.region_is_active else r2.region_is_active end as region_is_active,
	sc.group_name,
	a.team_alias
FROM
	spider.env_mgt_node n

inner join
spider.env_mgt_region r1
on n.region_id = r1.id

-- =========================================================================
left outer join
spider.env_mgt_node_bind nb
on n.id = nb.node_id
-- =========================================================================

-- =========================================================================
left outer join
spider.env_mgt_suite s
on nb.suite_id = s.id
-- =========================================================================

-- =========================================================================
left outer join
spider.env_mgt_region r2
on s.region_id = r2.id
-- =========================================================================

-- =========================================================================
left outer join
spider.env_mgt_node_bind_svc_cluster sc
on nb.id = sc.node_bind_id
-- =========================================================================

-- =========================================================================
left outer join
(
SELECT
	distinct am.module_name, ai.app_name, ai.third_party_middleware, am.need_check
FROM
	spider.app_mgt_app_module am, spider.app_mgt_app_info ai
where am.app_id = ai.id
and am.need_online = '1'
) aa
on aa.module_name = nb.module_name
-- =========================================================================
LEFT OUTER JOIN
-- 团队和应用关系拼接==============================================================
(
select  distinct am.module_name as app_name,
(
select group_concat(distinct ti.team_alias) as team_alias
from spider.app_mgt_app_team at, spider.tool_mgt_team_info ti
where at.app_id=ai.id and at.team_id=ti.id
group by am.module_name
order by team_alias
) as team_alias
from spider.app_mgt_app_info ai, spider.app_mgt_app_module am
where am.app_id=ai.id and am.need_online='1'
) a
-- 团队和应用关系拼接==============================================================
on nb.module_name=a.app_name
-- =========================================================================

) x

where 1=1
-- and x.node_status = '0'
-- and x.suite_is_active = '1'
and x.region_is_active = '1'

and x.node_status like '${节点本身是否有效：=,|是|否}%'

and
(
('${绑定状态是否有效：=,|是|否}' = '')
or
('${绑定状态是否有效：=,|是|否}' <> '' and x.enable_bind like '${绑定状态是否有效：=,|是|否}%')
)

and
(
('${可查询IP：}' = '' and '${环境套名模糊：}' ='' and '${查询应用名【*null代表不绑应用】：}' = '' and '${可选择阶段：=,|prod|hd|uat|test|dev}' = '' and '${可选择团队：=,|达摩院|爱码仕|藏经阁|六扇门|宙斯盾|罗刹堂|解忧杂货铺|毗沙门|银河战队|及时雨|码上飘|MACH}' = '' and x.region_group in ('prod','hd','uat'))
or
(
('${可查询IP：}' <> '' or '${环境套名模糊：}' <> '' or '${查询应用名【*null代表不绑应用】：}' <> '' or '${可选择阶段：=,|prod|hd|uat|test|dev}' <> '' or '${可选择团队：=,|达摩院|爱码仕|藏经阁|六扇门|宙斯盾|罗刹堂|解忧杂货铺|毗沙门|银河战队|及时雨|码上飘|MACH}' <> '')
and x.region_group like '${可选择阶段：=,|prod|hd|uat|test|dev}%'
)
-- or
-- ('${可选择阶段：=,|prod|hd|uat|test|dev}' <> '' and r.region_group like '${可选择阶段：=,|prod|hd|uat|test|dev}%')
)

and
(
'${【0自研】【1第三方】：=,|0|1}' = '' or ('${【0自研】【1第三方】：=,|0|1}' <> '' and x.third_party_middleware like '${【0自研】【1第三方】：=,|0|1}%'  and x.enable_bind = '是')
)

and
(
'${可选择团队：=,|达摩院|爱码仕|藏经阁|六扇门|宙斯盾|罗刹堂|解忧杂货铺|毗沙门|银河战队|及时雨|码上飘|MACH}' = ''
or
(
'${可选择团队：=,|达摩院|爱码仕|藏经阁|六扇门|宙斯盾|罗刹堂|解忧杂货铺|毗沙门|银河战队|及时雨|码上飘|MACH}' <> ''
and
x.team_alias like '%${可选择团队：=,|达摩院|爱码仕|藏经阁|六扇门|宙斯盾|罗刹堂|解忧杂货铺|毗沙门|银河战队|及时雨|码上飘|MACH}%'
and x.enable_bind = '是'
)
)

and
(
'${查询应用名【*null代表不绑应用】：}' = ''
or
('${查询应用名【*null代表不绑应用】：}' = '*null' and x.module_name is null )
or
('${查询应用名【*null代表不绑应用】：}' <> '' and '${查询应用名【*null代表不绑应用】：}' <> '*null' and lower(x.module_name) like lower('%${查询应用名【*null代表不绑应用】：}%') and x.enable_bind = '是' )
)

and ( x.node_ip like '%${可查询IP：}%' or '${可查询IP：}' REGEXP concat('[[:<:]]', x.node_ip, '[[:>:]]') )

and
(
'${环境套名模糊：}' = ''
or
('${环境套名模糊：}' <> '' and x.suite_name like '%${环境套名模糊：}%')
)


order by region_group, suite_code, module_name, group_name, node_ip
) a