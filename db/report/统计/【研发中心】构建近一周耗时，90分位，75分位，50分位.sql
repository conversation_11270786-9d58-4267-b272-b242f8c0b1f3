select 构建对象, 执行阶段, concat(总执行次数, ' 次') as 总执行次数,
concat('<b>',


case when (构建对象 like '纯静态%' or 构建对象 like '静态包%' or 执行阶段 = '打包' or 执行阶段 like '仅编译%') then '＜2分59秒'
else
(
case when 构建对象 in ('*客户端全过程') or 执行阶段 in ('RELEASE') then '＜19分59秒' else '＜10分59秒' end
)
end,

'</b>') as 中位数预警值,






case when (构建对象 like '纯静态%' or 构建对象 like '静态包%' or 执行阶段 = '打包' or 执行阶段 like '仅编译%') then
(
-- 大于2分59秒标红部分
case when (case when ifnull(quantile_50, 0)/60 < 90 then ifnull(quantile_50, 0) else 90*60 end)/60 >=3
then concat( '<font color = "red">', floor( (case when ifnull(quantile_50, 0)/60 < 90 then ifnull(quantile_50, 0) else 90*60 end)/60), ' 分 ', MOD( (case when ifnull(quantile_50, 0)/60 < 90 then ifnull(quantile_50, 0) else 90*60 end), 60), ' 秒</font>')
else concat( floor( (case when ifnull(quantile_50, 0)/60 < 90 then ifnull(quantile_50, 0) else 90*60 end)/60), ' 分 ', MOD( (case when ifnull(quantile_50, 0)/60 < 90 then ifnull(quantile_50, 0) else 90*60 end), 60), ' 秒') end
-- 大于2分59秒标红部分
)
else
(
case when 构建对象 in ('*客户端全过程') or 执行阶段 in ('RELEASE') then
(
-- 大于19分59秒标红部分
case when (case when ifnull(quantile_50, 0)/60 < 90 then ifnull(quantile_50, 0) else 90*60 end)/60 >=20
then concat( '<font color = "red">', floor( (case when ifnull(quantile_50, 0)/60 < 90 then ifnull(quantile_50, 0) else 90*60 end)/60), ' 分 ', MOD( (case when ifnull(quantile_50, 0)/60 < 90 then ifnull(quantile_50, 0) else 90*60 end), 60), ' 秒</font>')
else concat( floor( (case when ifnull(quantile_50, 0)/60 < 90 then ifnull(quantile_50, 0) else 90*60 end)/60), ' 分 ', MOD( (case when ifnull(quantile_50, 0)/60 < 90 then ifnull(quantile_50, 0) else 90*60 end), 60), ' 秒') end
-- 大于19分59秒标红部分
)
else
(
-- 大于10分59秒标红部分
case when (case when ifnull(quantile_50, 0)/60 < 90 then ifnull(quantile_50, 0) else 90*60 end)/60 >=11
then concat( '<font color = "red">', floor( (case when ifnull(quantile_50, 0)/60 < 90 then ifnull(quantile_50, 0) else 90*60 end)/60), ' 分 ', MOD( (case when ifnull(quantile_50, 0)/60 < 90 then ifnull(quantile_50, 0) else 90*60 end), 60), ' 秒</font>')
else concat( floor( (case when ifnull(quantile_50, 0)/60 < 90 then ifnull(quantile_50, 0) else 90*60 end)/60), ' 分 ', MOD( (case when ifnull(quantile_50, 0)/60 < 90 then ifnull(quantile_50, 0) else 90*60 end), 60), ' 秒') end
-- 大于10分59秒标红部分
)
end
)
end as 50分位耗时,


concat( floor( (case when ifnull(quantile_75, 0)/60 < 90 then ifnull(quantile_75, 0) else 90*60 end)/60), ' 分 ', MOD( (case when ifnull(quantile_75, 0)/60 < 90 then ifnull(quantile_75, 0) else 90*60 end), 60), ' 秒') as 75分位耗时,
concat( floor( (case when ifnull(quantile_90, 0)/60 < 90 then ifnull(quantile_90, 0) else 90*60 end)/60), ' 分 ', MOD( (case when ifnull(quantile_90, 0)/60 < 90 then ifnull(quantile_90, 0) else 90*60 end), 60), ' 秒') as 90分位耗时,
concat( floor( (case when ifnull(quantile_99, 0)/60 < 90 then ifnull(quantile_99, 0) else 90*60 end)/60), ' 分 ', MOD( (case when ifnull(quantile_99, 0)/60 < 90 then ifnull(quantile_99, 0) else 90*60 end), 60), ' 秒') as 99分位耗时





from
(




select
case package_type
when 'android' then 'APP 安卓包'
when 'ios' then 'APP iOS包'
when 'dist' then 'H5 APP资源包'
when 'remote' then '静态包 编译'
when 'h5-remote' then 'H5 服务器部署'
when 'static' then '纯静态 不编译'
when 'dist-nframe' then 'H5 APP(nf)资源包'
when 'param-remote' then '静态包 小程序'
end as 构建对象,
case package_type
when 'android' then '编译+打包'
when 'ios' then '编译+打包'
else operation end as 执行阶段,
total as 总执行次数,
-- ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
(
select elapse_secs from
(
select o.*,
case when @current = concat(operation, package_type) then @seq:=@seq+1 else @seq:=1 end as seq,
@current:=concat(operation, package_type)
from
(
select
operation,
app,
package_type,
case when elapse_sec/60 < 90 then elapse_sec else 90*60 end as elapse_secs,
date_format(start_time, '%Y-%m-%d') as day
from
(
-- ====================全部数据========================================
select
distinct om.*,
-- om.app_name, om.branch_name, om.created_at, om.env, om.id, om.request_params, om.start_time, om.status, om.end_time,
UNIX_TIMESTAMP(om.end_time) - UNIX_TIMESTAMP(om.start_time) as elapse_sec,
case when r.package_type = 'dist' and r.app_name like '%nf-%' then 'dist-nframe' else
(
case when r.package_type = 'remote' and om.job_name like 'h5_h5-%' then 'h5-remote' else r.package_type end
) end as package_type,
concat('（',
case when r.package_type = 'dist' and r.app_name like '%nf-%' then 'dist-nframe' else
(
case when r.package_type = 'remote' and om.job_name like 'h5_h5-%' then 'h5-remote' else r.package_type end
) end
, '）', r.app_name) as app,
case when om.request_params like '%build_cmd%' then '编译' else
(
case when om.exec_type = 'compile' or om.job_name not like '%publish_%' then '编译' else
(case when om.request_params like "%'apk': '0'%" or om.request_params not like "%'apk':%" then
(
case when om.job_name like 'publish_%' then 'RELEASE' else '打包' end
)
else '打包-app换壳' end )
end
)
end as operation
from
(select om.app_name, om.branch_name, om.created_at, om.env, om.id, om.exec_type, om.request_params, om.start_time, om.status,
case when om.end_time < oi.end_time then oi.end_time else om.end_time end as end_time,
om.job_name, om.job_url
from spider.hm_optlog_main om
left outer join
(SELECT max(end_time) as end_time, mid FROM spider.hm_optlog_item group by mid) oi
on om.id = oi.mid) om

inner join
(
select distinct om.id, group_concat(distinct ab.module_name order by ab.module_name) as app_name, ab.package_type
from
spider.hm_optlog_main om, spider.app_mgt_app_build ab
where om.created_at > DATE_ADD(now(), INTERVAL -7 DAY)
and ab.package_type in ('remote', 'dist', 'static', 'ios', 'android', 'param-remote')
and concat(',', om.app_name, ',') like concat('%,', ab.module_name, ',%')
group by om.id, ab.package_type
) r
on om.id = r.id

where om.created_at > DATE_ADD(now(), INTERVAL -7 DAY) and om.status = 'success'
and DATE_FORMAT(om.end_time,'%Y-%m-%d') = DATE_FORMAT(om.start_time,'%Y-%m-%d')
-- ========================================全部数据====================
) a
order by operation, package_type, elapse_secs, day
) o, (select @current:=null) p, (select @seq:=1) q
) q99
where q99.operation = x.operation and q99.package_type = x.package_type and q99.seq = x.quantile_99
) as quantile_99,
-- ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
(
select elapse_secs from
(
select o.*,
case when @current = concat(operation, package_type) then @seq:=@seq+1 else @seq:=1 end as seq,
@current:=concat(operation, package_type)
from
(
select
operation,
app,
package_type,
case when elapse_sec/60 < 90 then elapse_sec else 90*60 end as elapse_secs,
date_format(start_time, '%Y-%m-%d') as day
from
(
-- ====================全部数据========================================
select
distinct om.*,
-- om.app_name, om.branch_name, om.created_at, om.env, om.id, om.request_params, om.start_time, om.status, om.end_time,
UNIX_TIMESTAMP(om.end_time) - UNIX_TIMESTAMP(om.start_time) as elapse_sec,
case when r.package_type = 'dist' and r.app_name like '%nf-%' then 'dist-nframe' else
(
case when r.package_type = 'remote' and om.job_name like 'h5_h5-%' then 'h5-remote' else r.package_type end
) end as package_type,
concat('（',
case when r.package_type = 'dist' and r.app_name like '%nf-%' then 'dist-nframe' else
(
case when r.package_type = 'remote' and om.job_name like 'h5_h5-%' then 'h5-remote' else r.package_type end
) end
, '）', r.app_name) as app,
case when om.request_params like '%build_cmd%' then '编译' else
(
case when om.exec_type = 'compile' or om.job_name not like '%publish_%' then '编译' else
(case when om.request_params like "%'apk': '0'%" or om.request_params not like "%'apk':%" then
(
case when om.job_name like 'publish_%' then 'RELEASE' else '打包' end
)
else '打包-app换壳' end )
end
)
end as operation
from
(select om.app_name, om.branch_name, om.created_at, om.env, om.id, om.exec_type, om.request_params, om.start_time, om.status,
case when om.end_time < oi.end_time then oi.end_time else om.end_time end as end_time,
om.job_name, om.job_url
from spider.hm_optlog_main om
left outer join
(SELECT max(end_time) as end_time, mid FROM spider.hm_optlog_item group by mid) oi
on om.id = oi.mid) om

inner join
(
select distinct om.id, group_concat(distinct ab.module_name order by ab.module_name) as app_name, ab.package_type
from
spider.hm_optlog_main om, spider.app_mgt_app_build ab
where om.created_at > DATE_ADD(now(), INTERVAL -7 DAY)
and ab.package_type in ('remote', 'dist', 'static', 'ios', 'android', 'param-remote')
and concat(',', om.app_name, ',') like concat('%,', ab.module_name, ',%')
group by om.id, ab.package_type
) r
on om.id = r.id

where om.created_at > DATE_ADD(now(), INTERVAL -7 DAY) and om.status = 'success'
and DATE_FORMAT(om.end_time,'%Y-%m-%d') = DATE_FORMAT(om.start_time,'%Y-%m-%d')
-- ========================================全部数据====================
) a
order by operation, package_type, elapse_secs, day
) o, (select @current:=null) p, (select @seq:=1) q
) q90
where q90.operation = x.operation and q90.package_type = x.package_type and q90.seq = x.quantile_90
) as quantile_90,
-- ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
(
select elapse_secs from
(
select o.*,
case when @current = concat(operation, package_type) then @seq:=@seq+1 else @seq:=1 end as seq,
@current:=concat(operation, package_type)
from
(
select
operation,
app,
package_type,
case when elapse_sec/60 < 90 then elapse_sec else 90*60 end as elapse_secs,
date_format(start_time, '%Y-%m-%d') as day
from
(
-- ====================全部数据========================================
select
distinct om.*,
-- om.app_name, om.branch_name, om.created_at, om.env, om.id, om.request_params, om.start_time, om.status, om.end_time,
UNIX_TIMESTAMP(om.end_time) - UNIX_TIMESTAMP(om.start_time) as elapse_sec,
case when r.package_type = 'dist' and r.app_name like '%nf-%' then 'dist-nframe' else
(
case when r.package_type = 'remote' and om.job_name like 'h5_h5-%' then 'h5-remote' else r.package_type end
) end as package_type,
concat('（',
case when r.package_type = 'dist' and r.app_name like '%nf-%' then 'dist-nframe' else
(
case when r.package_type = 'remote' and om.job_name like 'h5_h5-%' then 'h5-remote' else r.package_type end
) end
, '）', r.app_name) as app,
case when om.request_params like '%build_cmd%' then '编译' else
(
case when om.exec_type = 'compile' or om.job_name not like '%publish_%' then '编译' else
(case when om.request_params like "%'apk': '0'%" or om.request_params not like "%'apk':%" then
(
case when om.job_name like 'publish_%' then 'RELEASE' else '打包' end
)
else '打包-app换壳' end )
end
)
end as operation
from
(select om.app_name, om.branch_name, om.created_at, om.env, om.id, om.exec_type, om.request_params, om.start_time, om.status,
case when om.end_time < oi.end_time then oi.end_time else om.end_time end as end_time,
om.job_name, om.job_url
from spider.hm_optlog_main om
left outer join
(SELECT max(end_time) as end_time, mid FROM spider.hm_optlog_item group by mid) oi
on om.id = oi.mid) om

inner join
(
select distinct om.id, group_concat(distinct ab.module_name order by ab.module_name) as app_name, ab.package_type
from
spider.hm_optlog_main om, spider.app_mgt_app_build ab
where om.created_at > DATE_ADD(now(), INTERVAL -7 DAY)
and ab.package_type in ('remote', 'dist', 'static', 'ios', 'android', 'param-remote')
and concat(',', om.app_name, ',') like concat('%,', ab.module_name, ',%')
group by om.id, ab.package_type
) r
on om.id = r.id

where om.created_at > DATE_ADD(now(), INTERVAL -7 DAY) and om.status = 'success'
and DATE_FORMAT(om.end_time,'%Y-%m-%d') = DATE_FORMAT(om.start_time,'%Y-%m-%d')
-- ========================================全部数据====================
) a
order by operation, package_type, elapse_secs, day
) o, (select @current:=null) p, (select @seq:=1) q
) q75
where q75.operation = x.operation and q75.package_type = x.package_type and q75.seq = x.quantile_75
) as quantile_75,
-- ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
(
select elapse_secs from
(
select o.*,
case when @current = concat(operation, package_type) then @seq:=@seq+1 else @seq:=1 end as seq,
@current:=concat(operation, package_type)
from
(
select
operation,
app,
package_type,
case when elapse_sec/60 < 90 then elapse_sec else 90*60 end as elapse_secs,
date_format(start_time, '%Y-%m-%d') as day
from
(
-- ====================全部数据========================================
select
distinct om.*,
-- om.app_name, om.branch_name, om.created_at, om.env, om.id, om.request_params, om.start_time, om.status, om.end_time,
UNIX_TIMESTAMP(om.end_time) - UNIX_TIMESTAMP(om.start_time) as elapse_sec,
case when r.package_type = 'dist' and r.app_name like '%nf-%' then 'dist-nframe' else
(
case when r.package_type = 'remote' and om.job_name like 'h5_h5-%' then 'h5-remote' else r.package_type end
) end as package_type,
concat('（',
case when r.package_type = 'dist' and r.app_name like '%nf-%' then 'dist-nframe' else
(
case when r.package_type = 'remote' and om.job_name like 'h5_h5-%' then 'h5-remote' else r.package_type end
) end
, '）', r.app_name) as app,
case when om.request_params like '%build_cmd%' then '编译' else
(
case when om.exec_type = 'compile' or om.job_name not like '%publish_%' then '编译' else
(case when om.request_params like "%'apk': '0'%" or om.request_params not like "%'apk':%" then
(
case when om.job_name like 'publish_%' then 'RELEASE' else '打包' end
)
else '打包-app换壳' end )
end
)
end as operation
from
(select om.app_name, om.branch_name, om.created_at, om.env, om.id, om.exec_type, om.request_params, om.start_time, om.status,
case when om.end_time < oi.end_time then oi.end_time else om.end_time end as end_time,
om.job_name, om.job_url
from spider.hm_optlog_main om
left outer join
(SELECT max(end_time) as end_time, mid FROM spider.hm_optlog_item group by mid) oi
on om.id = oi.mid) om

inner join
(
select distinct om.id, group_concat(distinct ab.module_name order by ab.module_name) as app_name, ab.package_type
from
spider.hm_optlog_main om, spider.app_mgt_app_build ab
where om.created_at > DATE_ADD(now(), INTERVAL -7 DAY)
and ab.package_type in ('remote', 'dist', 'static', 'ios', 'android', 'param-remote')
and concat(',', om.app_name, ',') like concat('%,', ab.module_name, ',%')
group by om.id, ab.package_type
) r
on om.id = r.id

where om.created_at > DATE_ADD(now(), INTERVAL -7 DAY) and om.status = 'success'
and DATE_FORMAT(om.end_time,'%Y-%m-%d') = DATE_FORMAT(om.start_time,'%Y-%m-%d')
-- ========================================全部数据====================
) a
order by operation, package_type, elapse_secs, day
) o, (select @current:=null) p, (select @seq:=1) q
) q50
where q50.operation = x.operation and q50.package_type = x.package_type and q50.seq = x.quantile_50
) as quantile_50
-- ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

from

(
select package_type, operation, count(0) as total, floor(count(0)*99/100)+1 as quantile_99, floor(count(0)*90/100)+1 as quantile_90, floor(count(0)*75/100)+1 as quantile_75, floor(count(0)*50/100)+1 as quantile_50 from
(
-- ====================全部数据========================================
select
distinct om.*,
-- om.app_name, om.branch_name, om.created_at, om.env, om.id, om.request_params, om.start_time, om.status, om.end_time,
UNIX_TIMESTAMP(om.end_time) - UNIX_TIMESTAMP(om.start_time) as elapse_sec,
case when r.package_type = 'dist' and r.app_name like '%nf-%' then 'dist-nframe' else
(
case when r.package_type = 'remote' and om.job_name like 'h5_h5-%' then 'h5-remote' else r.package_type end
) end as package_type,
concat('（',
case when r.package_type = 'dist' and r.app_name like '%nf-%' then 'dist-nframe' else
(
case when r.package_type = 'remote' and om.job_name like 'h5_h5-%' then 'h5-remote' else r.package_type end
) end
, '）', r.app_name) as app,
case when om.request_params like '%build_cmd%' then '编译' else
(
case when om.exec_type = 'compile' or om.job_name not like '%publish_%' then '编译' else
(case when om.request_params like "%'apk': '0'%" or om.request_params not like "%'apk':%" then
(
case when om.job_name like 'publish_%' then 'RELEASE' else '打包' end
)
else '打包-app换壳' end )
end
)
end as operation
from
(select om.app_name, om.branch_name, om.created_at, om.env, om.id, om.exec_type, om.request_params, om.start_time, om.status,
case when om.end_time < oi.end_time then oi.end_time else om.end_time end as end_time,
om.job_name, om.job_url
from spider.hm_optlog_main om
left outer join
(SELECT max(end_time) as end_time, mid FROM spider.hm_optlog_item group by mid) oi
on om.id = oi.mid) om

inner join
(
select distinct om.id, group_concat(distinct ab.module_name order by ab.module_name) as app_name, ab.package_type
from
spider.hm_optlog_main om, spider.app_mgt_app_build ab
where om.created_at > DATE_ADD(now(), INTERVAL -7 DAY)
and ab.package_type in ('remote', 'dist', 'static', 'ios', 'android', 'param-remote')
and concat(',', om.app_name, ',') like concat('%,', ab.module_name, ',%')
group by om.id, ab.package_type
) r
on om.id = r.id

where om.created_at > DATE_ADD(now(), INTERVAL -7 DAY) and om.status = 'success'
and DATE_FORMAT(om.end_time,'%Y-%m-%d') = DATE_FORMAT(om.start_time,'%Y-%m-%d')
-- ========================================全部数据====================
) a
group by package_type, operation
) x















union all
















select * from
(
select '*客户端全过程' as 构建对象, '从H5到iOS' as 执行阶段, min(总执行次数) as 总执行次数,
sum(quantile_99) as quantile_99,
sum(quantile_90) as quantile_90,
sum(quantile_75) as quantile_75,
sum(quantile_50) as quantile_50
from
(

select * from
(
select
case package_type
when 'android' then 'APP 安卓包'
when 'ios' then 'APP iOS包'
when 'dist' then 'H5 APP资源包'
when 'remote' then '静态包 编译'
when 'static' then '纯静态 不编译'
when 'dist-nframe' then 'H5 APP(nf)资源包'
when 'param-remote' then '静态包 小程序'
end as 构建对象,
case package_type
when 'android' then '编译+打包'
when 'ios' then '编译+打包'
else operation end as 执行阶段,
total as 总执行次数,
-- ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
(
select elapse_secs from
(
select o.*,
case when @current = concat(operation, package_type) then @seq:=@seq+1 else @seq:=1 end as seq,
@current:=concat(operation, package_type)
from
(
select
operation,
app,
package_type,
case when elapse_sec/60 < 90 then elapse_sec else 90*60 end as elapse_secs,
date_format(start_time, '%Y-%m-%d') as day
from
(
-- ====================全部数据========================================
select
distinct om.*,
-- om.app_name, om.branch_name, om.created_at, om.env, om.id, om.request_params, om.start_time, om.status, om.end_time,
UNIX_TIMESTAMP(om.end_time) - UNIX_TIMESTAMP(om.start_time) as elapse_sec,
case when r.package_type = 'dist' and r.app_name like '%nf-%' then 'dist-nframe' else r.package_type end as package_type,
concat('（', r.package_type, '）', r.app_name) as app,
case when om.request_params like '%build_cmd%' then '编译' else
(
case when om.exec_type = 'compile' or om.job_name not like '%publish_%' then '编译' else
(case when om.request_params like "%'apk': '0'%" or om.request_params not like "%'apk':%" then
(
case when om.job_name like 'publish_%' then 'RELEASE' else '打包' end
)
else '打包-app换壳' end )
end
)
end as operation
from
(select om.app_name, om.branch_name, om.created_at, om.env, om.id, om.exec_type, om.request_params, om.start_time, om.status,
case when om.end_time < oi.end_time then oi.end_time else om.end_time end as end_time,
om.job_name
from spider.hm_optlog_main om
left outer join
(SELECT max(end_time) as end_time, mid FROM spider.hm_optlog_item group by mid) oi
on om.id = oi.mid) om

inner join
(
select distinct om.id, group_concat(distinct ab.module_name order by ab.module_name) as app_name, ab.package_type
from
spider.hm_optlog_main om, spider.app_mgt_app_build ab
where om.created_at > DATE_ADD(now(), INTERVAL -7 DAY)
and ab.package_type in ('remote', 'dist', 'static', 'ios', 'android', 'param-remote')
and concat(',', om.app_name, ',') like concat('%,', ab.module_name, ',%')
group by om.id, ab.package_type
) r
on om.id = r.id

where om.created_at > DATE_ADD(now(), INTERVAL -7 DAY) and om.status = 'success'
and DATE_FORMAT(om.end_time,'%Y-%m-%d') = DATE_FORMAT(om.start_time,'%Y-%m-%d')
-- ========================================全部数据====================
) a
order by operation, package_type, elapse_secs, day
) o, (select @current:=null) p, (select @seq:=1) q
) q99
where q99.operation = x.operation and q99.package_type = x.package_type and q99.seq = x.quantile_99
) as quantile_99,
-- ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
(
select elapse_secs from
(
select o.*,
case when @current = concat(operation, package_type) then @seq:=@seq+1 else @seq:=1 end as seq,
@current:=concat(operation, package_type)
from
(
select
operation,
app,
package_type,
case when elapse_sec/60 < 90 then elapse_sec else 90*60 end as elapse_secs,
date_format(start_time, '%Y-%m-%d') as day
from
(
-- ====================全部数据========================================
select
distinct om.*,
-- om.app_name, om.branch_name, om.created_at, om.env, om.id, om.request_params, om.start_time, om.status, om.end_time,
UNIX_TIMESTAMP(om.end_time) - UNIX_TIMESTAMP(om.start_time) as elapse_sec,
case when r.package_type = 'dist' and r.app_name like '%nf-%' then 'dist-nframe' else r.package_type end as package_type,
concat('（', r.package_type, '）', r.app_name) as app,
case when om.request_params like '%build_cmd%' then '编译' else
(
case when om.exec_type = 'compile' or om.job_name not like '%publish_%' then '编译' else
(case when om.request_params like "%'apk': '0'%" or om.request_params not like "%'apk':%" then
(
case when om.job_name like 'publish_%' then 'RELEASE' else '打包' end
)
else '打包-app换壳' end )
end
)
end as operation
from
(select om.app_name, om.branch_name, om.created_at, om.env, om.id, om.exec_type, om.request_params, om.start_time, om.status,
case when om.end_time < oi.end_time then oi.end_time else om.end_time end as end_time,
om.job_name
from spider.hm_optlog_main om
left outer join
(SELECT max(end_time) as end_time, mid FROM spider.hm_optlog_item group by mid) oi
on om.id = oi.mid) om

inner join
(
select distinct om.id, group_concat(distinct ab.module_name order by ab.module_name) as app_name, ab.package_type
from
spider.hm_optlog_main om, spider.app_mgt_app_build ab
where om.created_at > DATE_ADD(now(), INTERVAL -7 DAY)
and ab.package_type in ('remote', 'dist', 'static', 'ios', 'android', 'param-remote')
and concat(',', om.app_name, ',') like concat('%,', ab.module_name, ',%')
group by om.id, ab.package_type
) r
on om.id = r.id

where om.created_at > DATE_ADD(now(), INTERVAL -7 DAY) and om.status = 'success'
and DATE_FORMAT(om.end_time,'%Y-%m-%d') = DATE_FORMAT(om.start_time,'%Y-%m-%d')
-- ========================================全部数据====================
) a
order by operation, package_type, elapse_secs, day
) o, (select @current:=null) p, (select @seq:=1) q
) q90
where q90.operation = x.operation and q90.package_type = x.package_type and q90.seq = x.quantile_90
) as quantile_90,
-- ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
(
select elapse_secs from
(
select o.*,
case when @current = concat(operation, package_type) then @seq:=@seq+1 else @seq:=1 end as seq,
@current:=concat(operation, package_type)
from
(
select
operation,
app,
package_type,
case when elapse_sec/60 < 90 then elapse_sec else 90*60 end as elapse_secs,
date_format(start_time, '%Y-%m-%d') as day
from
(
-- ====================全部数据========================================
select
distinct om.*,
-- om.app_name, om.branch_name, om.created_at, om.env, om.id, om.request_params, om.start_time, om.status, om.end_time,
UNIX_TIMESTAMP(om.end_time) - UNIX_TIMESTAMP(om.start_time) as elapse_sec,
case when r.package_type = 'dist' and r.app_name like '%nf-%' then 'dist-nframe' else r.package_type end as package_type,
concat('（', r.package_type, '）', r.app_name) as app,
case when om.request_params like '%build_cmd%' then '编译' else
(
case when om.exec_type = 'compile' or om.job_name not like '%publish_%' then '编译' else
(case when om.request_params like "%'apk': '0'%" or om.request_params not like "%'apk':%" then
(
case when om.job_name like 'publish_%' then 'RELEASE' else '打包' end
)
else '打包-app换壳' end )
end
)
end as operation
from
(select om.app_name, om.branch_name, om.created_at, om.env, om.id, om.exec_type, om.request_params, om.start_time, om.status,
case when om.end_time < oi.end_time then oi.end_time else om.end_time end as end_time,
om.job_name
from spider.hm_optlog_main om
left outer join
(SELECT max(end_time) as end_time, mid FROM spider.hm_optlog_item group by mid) oi
on om.id = oi.mid) om

inner join
(
select distinct om.id, group_concat(distinct ab.module_name order by ab.module_name) as app_name, ab.package_type
from
spider.hm_optlog_main om, spider.app_mgt_app_build ab
where om.created_at > DATE_ADD(now(), INTERVAL -7 DAY)
and ab.package_type in ('remote', 'dist', 'static', 'ios', 'android', 'param-remote')
and concat(',', om.app_name, ',') like concat('%,', ab.module_name, ',%')
group by om.id, ab.package_type
) r
on om.id = r.id

where om.created_at > DATE_ADD(now(), INTERVAL -7 DAY) and om.status = 'success'
and DATE_FORMAT(om.end_time,'%Y-%m-%d') = DATE_FORMAT(om.start_time,'%Y-%m-%d')
-- ========================================全部数据====================
) a
order by operation, package_type, elapse_secs, day
) o, (select @current:=null) p, (select @seq:=1) q
) q75
where q75.operation = x.operation and q75.package_type = x.package_type and q75.seq = x.quantile_75
) as quantile_75,
-- ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
(
select elapse_secs from
(
select o.*,
case when @current = concat(operation, package_type) then @seq:=@seq+1 else @seq:=1 end as seq,
@current:=concat(operation, package_type)
from
(
select
operation,
app,
package_type,
case when elapse_sec/60 < 90 then elapse_sec else 90*60 end as elapse_secs,
date_format(start_time, '%Y-%m-%d') as day
from
(
-- ====================全部数据========================================
select
distinct om.*,
-- om.app_name, om.branch_name, om.created_at, om.env, om.id, om.request_params, om.start_time, om.status, om.end_time,
UNIX_TIMESTAMP(om.end_time) - UNIX_TIMESTAMP(om.start_time) as elapse_sec,
case when r.package_type = 'dist' and r.app_name like '%nf-%' then 'dist-nframe' else r.package_type end as package_type,
concat('（', r.package_type, '）', r.app_name) as app,
case when om.request_params like '%build_cmd%' then '编译' else
(
case when om.exec_type = 'compile' or om.job_name not like '%publish_%' then '编译' else
(case when om.request_params like "%'apk': '0'%" or om.request_params not like "%'apk':%" then
(
case when om.job_name like 'publish_%' then 'RELEASE' else '打包' end
)
else '打包-app换壳' end )
end
)
end as operation
from
(select om.app_name, om.branch_name, om.created_at, om.env, om.id, om.exec_type, om.request_params, om.start_time, om.status,
case when om.end_time < oi.end_time then oi.end_time else om.end_time end as end_time,
om.job_name
from spider.hm_optlog_main om
left outer join
(SELECT max(end_time) as end_time, mid FROM spider.hm_optlog_item group by mid) oi
on om.id = oi.mid) om

inner join
(
select distinct om.id, group_concat(distinct ab.module_name order by ab.module_name) as app_name, ab.package_type
from
spider.hm_optlog_main om, spider.app_mgt_app_build ab
where om.created_at > DATE_ADD(now(), INTERVAL -7 DAY)
and ab.package_type in ('remote', 'dist', 'static', 'ios', 'android', 'param-remote')
and concat(',', om.app_name, ',') like concat('%,', ab.module_name, ',%')
group by om.id, ab.package_type
) r
on om.id = r.id

where om.created_at > DATE_ADD(now(), INTERVAL -7 DAY) and om.status = 'success'
and DATE_FORMAT(om.end_time,'%Y-%m-%d') = DATE_FORMAT(om.start_time,'%Y-%m-%d')
-- ========================================全部数据====================
) a
order by operation, package_type, elapse_secs, day
) o, (select @current:=null) p, (select @seq:=1) q
) q50
where q50.operation = x.operation and q50.package_type = x.package_type and q50.seq = x.quantile_50
) as quantile_50
-- ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

from

(
select package_type, operation, count(0) as total, floor(count(0)*99/100)+1 as quantile_99, floor(count(0)*90/100)+1 as quantile_90, floor(count(0)*75/100)+1 as quantile_75, floor(count(0)*50/100)+1 as quantile_50 from
(
-- ====================全部数据========================================
select
distinct om.*,
-- om.app_name, om.branch_name, om.created_at, om.env, om.id, om.request_params, om.start_time, om.status, om.end_time,
UNIX_TIMESTAMP(om.end_time) - UNIX_TIMESTAMP(om.start_time) as elapse_sec,
case when r.package_type = 'dist' and r.app_name like '%nf-%' then 'dist-nframe' else r.package_type end as package_type,
concat('（', r.package_type, '）', r.app_name) as app,
case when om.request_params like '%build_cmd%' then '编译' else
(
case when om.exec_type = 'compile' or om.job_name not like '%publish_%' then '编译' else
(case when om.request_params like "%'apk': '0'%" or om.request_params not like "%'apk':%" then
(
case when om.job_name like 'publish_%' then 'RELEASE' else '打包' end
)
else '打包-app换壳' end )
end
)
end as operation
from
(select om.app_name, om.branch_name, om.created_at, om.env, om.id, om.exec_type, om.request_params, om.start_time, om.status,
case when om.end_time < oi.end_time then oi.end_time else om.end_time end as end_time,
om.job_name
from spider.hm_optlog_main om
left outer join
(SELECT max(end_time) as end_time, mid FROM spider.hm_optlog_item group by mid) oi
on om.id = oi.mid) om

inner join
(
select distinct om.id, group_concat(distinct ab.module_name order by ab.module_name) as app_name, ab.package_type
from
spider.hm_optlog_main om, spider.app_mgt_app_build ab
where om.created_at > DATE_ADD(now(), INTERVAL -7 DAY)
and ab.package_type in ('remote', 'dist', 'static', 'ios', 'android', 'param-remote')
and concat(',', om.app_name, ',') like concat('%,', ab.module_name, ',%')
group by om.id, ab.package_type
) r
on om.id = r.id

where om.created_at > DATE_ADD(now(), INTERVAL -7 DAY) and om.status = 'success'
and DATE_FORMAT(om.end_time,'%Y-%m-%d') = DATE_FORMAT(om.start_time,'%Y-%m-%d')
-- ========================================全部数据====================
) a
group by package_type, operation
) x


) yyy

where 构建对象 in ('APP iOS包', 'H5 APP资源包') and 执行阶段 in ('打包', '编译', '编译+打包')

) zzz


) y








union all











select '*S服务端JAVA应用', main.types, main.total,

(
select elapse_secs from
(
select o.*,
case when @current = types then @seq99:=@seq99+1 else @seq99:=1 end as sequence,
@current:=types
from
(
-- ====================基础数据========================================
SELECT
	ma.sid, timestampdiff(second, ma.start_at, mis.compile_end) as elapse_secs,
	case when entire.sid is null then '仅构建' else '构建+部署' end as types
FROM
	spider.pipeline_log_main ma
inner join
(
SELECT
	max(mi.end_at) as compile_end, mi.sid
FROM
	spider.pipeline_log_minor mi
where mi.`log` = '流水线完成'
and mi.start_at > DATE_ADD(now(), INTERVAL -7 DAY)
group by mi.sid
) mis
on ma.sid = mis.sid
left outer join
(
SELECT
	distinct sid
FROM
	spider.pipeline_log_minor mi
where mi.`log` = '检查启动结果完成'
and mi.start_at > DATE_ADD(now(), INTERVAL -7 DAY)
group by mi.sid
) entire
on entire.sid = ma.sid
left outer join
(select distinct mi.sid from spider.pipeline_log_minor mi where mi.step = 'analyze_test_report') ut
on ma.sid = ut.sid
where ma.status = 'success' and ma.app_name is not null
and ma.start_at > DATE_ADD(now(), INTERVAL -7 DAY)
-- ========================================基础数据====================
order by types, elapse_secs
) o, (select @current:=null) p, (select @seq99:=1) q
) q99
where q99.sequence = main.q_99 and q99.types = main.types
) as quantile_99,


(
select elapse_secs from
(
select o.*,
case when @current = types then @seq90:=@seq90+1 else @seq90:=1 end as sequence,
@current:=types
from
(
-- ====================基础数据========================================
SELECT
	ma.sid, timestampdiff(second, ma.start_at, mis.compile_end) as elapse_secs,
	case when entire.sid is null then '仅构建' else '构建+部署' end as types
FROM
	spider.pipeline_log_main ma
inner join
(
SELECT
	max(mi.end_at) as compile_end, mi.sid
FROM
	spider.pipeline_log_minor mi
where mi.`log` = '流水线完成'
and mi.start_at > DATE_ADD(now(), INTERVAL -7 DAY)
group by mi.sid
) mis
on ma.sid = mis.sid
left outer join
(
SELECT
	distinct sid
FROM
	spider.pipeline_log_minor mi
where mi.`log` = '检查启动结果完成'
and mi.start_at > DATE_ADD(now(), INTERVAL -7 DAY)
group by mi.sid
) entire
on entire.sid = ma.sid
left outer join
(select distinct mi.sid from spider.pipeline_log_minor mi where mi.step = 'analyze_test_report') ut
on ma.sid = ut.sid
where ma.status = 'success' and ma.app_name is not null
and ma.start_at > DATE_ADD(now(), INTERVAL -7 DAY)
-- ========================================基础数据====================
order by types, elapse_secs
) o, (select @current:=null) p, (select @seq90:=1) q
) q90
where q90.sequence = main.q_90 and q90.types = main.types
) as quantile_90,


(
select elapse_secs from
(
select o.*,
case when @current = types then @seq75:=@seq75+1 else @seq75:=1 end as sequence,
@current:=types
from
(
-- ====================基础数据========================================
SELECT
	ma.sid, timestampdiff(second, ma.start_at, mis.compile_end) as elapse_secs,
	case when entire.sid is null then '仅构建' else '构建+部署' end as types
FROM
	spider.pipeline_log_main ma
inner join
(
SELECT
	max(mi.end_at) as compile_end, mi.sid
FROM
	spider.pipeline_log_minor mi
where mi.`log` = '流水线完成'
and mi.start_at > DATE_ADD(now(), INTERVAL -7 DAY)
group by mi.sid
) mis
on ma.sid = mis.sid
left outer join
(
SELECT
	distinct sid
FROM
	spider.pipeline_log_minor mi
where mi.`log` = '检查启动结果完成'
and mi.start_at > DATE_ADD(now(), INTERVAL -7 DAY)
group by mi.sid
) entire
on entire.sid = ma.sid
left outer join
(select distinct mi.sid from spider.pipeline_log_minor mi where mi.step = 'analyze_test_report') ut
on ma.sid = ut.sid
where ma.status = 'success' and ma.app_name is not null
and ma.start_at > DATE_ADD(now(), INTERVAL -7 DAY)
-- ========================================基础数据====================
order by types, elapse_secs
) o, (select @current:=null) p, (select @seq75:=1) q
) q75
where q75.sequence = main.q_75 and q75.types = main.types
) as quantile_75,


(
select elapse_secs from
(
select o.*,
case when @current = types then @seq50:=@seq50+1 else @seq50:=1 end as sequence,
@current:=types
from
(
-- ====================基础数据========================================
SELECT
	ma.sid, timestampdiff(second, ma.start_at, mis.compile_end) as elapse_secs,
	case when entire.sid is null then '仅构建' else '构建+部署' end as types
FROM
	spider.pipeline_log_main ma
inner join
(
SELECT
	max(mi.end_at) as compile_end, mi.sid
FROM
	spider.pipeline_log_minor mi
where mi.`log` = '流水线完成'
and mi.start_at > DATE_ADD(now(), INTERVAL -7 DAY)
group by mi.sid
) mis
on ma.sid = mis.sid
left outer join
(
SELECT
	distinct sid
FROM
	spider.pipeline_log_minor mi
where mi.`log` = '检查启动结果完成'
and mi.start_at > DATE_ADD(now(), INTERVAL -7 DAY)
group by mi.sid
) entire
on entire.sid = ma.sid
left outer join
(select distinct mi.sid from spider.pipeline_log_minor mi where mi.step = 'analyze_test_report') ut
on ma.sid = ut.sid
where ma.status = 'success' and ma.app_name is not null
and ma.start_at > DATE_ADD(now(), INTERVAL -7 DAY)
-- ========================================基础数据====================
order by types, elapse_secs
) o, (select @current:=null) p, (select @seq50:=1) q
) q50
where q50.sequence = main.q_50 and q50.types = main.types
) as quantile_50

from
(
select types, count(0) as total
, floor(count(0)/2)+1 as q_50, floor(count(0)/4*3)+1 as q_75, floor(count(0)/10*9)+1 as q_90, floor(count(0)/100*99)+1 as q_99
from
(
-- ====================基础数据========================================
SELECT
	ma.sid, timestampdiff(second, ma.start_at, mis.compile_end) as elapse_secs,
	case when entire.sid is null then '仅构建' else '构建+部署' end as types
FROM
	spider.pipeline_log_main ma
inner join
(
SELECT
	max(mi.end_at) as compile_end, mi.sid
FROM
	spider.pipeline_log_minor mi
where mi.`log` = '流水线完成'
and mi.start_at > DATE_ADD(now(), INTERVAL -7 DAY)
group by mi.sid
) mis
on ma.sid = mis.sid
left outer join
(
SELECT
	distinct sid
FROM
	spider.pipeline_log_minor mi
where mi.`log` = '检查启动结果完成'
and mi.start_at > DATE_ADD(now(), INTERVAL -7 DAY)
group by mi.sid
) entire
on entire.sid = ma.sid
left outer join
(select distinct mi.sid from spider.pipeline_log_minor mi where mi.step = 'analyze_test_report') ut
on ma.sid = ut.sid
where ma.status = 'success' and ma.app_name is not null
and ma.start_at > DATE_ADD(now(), INTERVAL -7 DAY)
-- ========================================基础数据====================
) a
group by a.types

) main











) xxx




where '${按回车刷新：}' = '${按回车刷新：}'

order by 构建对象, 执行阶段 desc