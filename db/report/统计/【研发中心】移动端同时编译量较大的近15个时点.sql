select * from


(
select concat(idx, 'x') as idx, count(id) as '10分钟内并发的编译(个数)'
from

(

select
substring(DATE_FORMAT(date_add(now(), interval -seq.number*10 minute), '%y-%m-%d %H:%i'), 1, 13) as idx,
data.*
from mantis.sequence seq

left outer join
(

-- ====================全部数据========================================
select
distinct om.*,
-- om.app_name, om.branch_name, om.created_at, om.env, om.id, om.request_params, om.start_time, om.status, om.end_time,
UNIX_TIMESTAMP(om.end_time) - UNIX_TIMESTAMP(om.start_time) as elapse_sec,
case when r.package_type = 'dist' and r.app_name like '%nf-%' then 'dist-nframe' else
(
case when r.package_type = 'remote' and om.job_name like 'h5_h5-%' then 'h5-remote' else r.package_type end
) end as package_type,
concat('（',
case when r.package_type = 'dist' and r.app_name like '%nf-%' then 'dist-nframe' else
(
case when r.package_type = 'remote' and om.job_name like 'h5_h5-%' then 'h5-remote' else r.package_type end
) end
, '）', r.app_name) as app,
case when om.request_params like '%build_cmd%' then '编译' else
(
case when om.exec_type = 'compile' or om.job_name not like '%publish_%' then '编译' else
(case when om.request_params like "%'apk': '0'%" or om.request_params not like "%'apk':%" then
(
case when om.job_name like 'publish_%' then 'RELEASE' else '打包' end
)
else '打包-app换壳' end )
end
)
end as operation
from
(select om.app_name, om.branch_name, om.created_at, om.env, om.id, om.exec_type, om.request_params, om.start_time, om.status,
case when om.end_time < oi.end_time then oi.end_time else om.end_time end as end_time,
om.job_name, om.job_url
from spider.hm_optlog_main om
left outer join
(SELECT max(end_time) as end_time, mid FROM spider.hm_optlog_item group by mid) oi
on om.id = oi.mid) om

inner join
(
select distinct om.id, group_concat(distinct ab.module_name order by ab.module_name) as app_name, ab.package_type
from
spider.hm_optlog_main om, spider.app_mgt_app_build ab
where om.created_at > DATE_ADD(now(), INTERVAL -7 DAY)
and ab.package_type in ('remote', 'dist', 'static', 'ios', 'android', 'param-remote')
and concat(',', om.app_name, ',') like concat('%,', ab.module_name, ',%')
group by om.id, ab.package_type
) r
on om.id = r.id

where om.created_at > DATE_ADD(now(), INTERVAL -7 DAY)
and DATE_FORMAT(om.end_time,'%Y-%m-%d') = DATE_FORMAT(om.start_time,'%Y-%m-%d')
-- ========================================全部数据====================

) data

on data.operation = '编译'
and
(
substring(DATE_FORMAT(data.start_time, '%y-%m-%d %H:%i'), 1, 13) = substring(DATE_FORMAT(date_add(now(), interval -seq.number*10 minute), '%y-%m-%d %H:%i'), 1, 13)
or
substring(DATE_FORMAT(data.end_time, '%y-%m-%d %H:%i'), 1, 13) = substring(DATE_FORMAT(date_add(now(), interval -seq.number*10 minute), '%y-%m-%d %H:%i'), 1, 13)
or
(
substring(DATE_FORMAT(data.start_time, '%y-%m-%d %H:%i'), 1, 13) < substring(DATE_FORMAT(date_add(now(), interval -seq.number*10 minute), '%y-%m-%d %H:%i'), 1, 13)
and substring(DATE_FORMAT(data.end_time, '%y-%m-%d %H:%i'), 1, 13) > substring(DATE_FORMAT(date_add(now(), interval -seq.number*10 minute), '%y-%m-%d %H:%i'), 1, 13)
)
)
) stat

group by idx

having count(id) > 6

order by idx desc

limit 15


) c

order by idx