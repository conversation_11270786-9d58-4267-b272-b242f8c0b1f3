%spider


select * from

(
select
distinct

'应用模块表sql语句【app_mgt_app_module】' as 类型,

case when a.id is not null then
'暂不支持UPDATE语句生成'
else
(
case when '${应用app_id：}' = '' or '${模块标准名：}' = '' or '${是否上线包【1上线包 0依赖包】：=,|1|0}' = '' or '${制品库名：}' = '' or '${配置系统【1宙斯 4CCMS 0不接】：=,|1|4|0}' = '' or '${进OPS阶段【1是 0否】：=,|1|0}' = '' then '缺少生成INSERT语句的要素' else
concat(
" INSERT INTO spider.app_mgt_app_module " ,
" (app_id, module_name, module_code, module_status, module_jdk_version, need_online, need_check, lib_repo, create_user, create_time, stamp, zeus_type, need_ops) " ,
" VALUES " ,
" ( " ,
" '${应用app_id：}', "
" '${模块标准名：}', " ,
case when '${Resource_Code：}' = '' then " null, " else " '${Resource_Code：}', " end ,
" '1', " ,
case when '${JDK版本号：}' = '' then " null, " else " '${JDK版本号：}', " end ,
" '${是否上线包【1上线包 0依赖包】：=,|1|0}', " ,
" '1', " ,
" '${制品库名：}', " ,
" 'yixiang.zhang', " ,
" now(), " ,
" '0', " ,
" '${配置系统【1宙斯 4CCMS 0不接】：=,|1|4|0}', " ,
" '${进OPS阶段【1是 0否】：=,|1|0}' "
" ); "
)
end
)
end as 内容

from spider.tool_mgt_git_server

left outer join
(select id from spider.app_mgt_app_module am where am.module_name = '${模块标准名：}') a
on 1=1


) a


union all


select * from

(

select
distinct

'模块编译表sql语句【app_mgt_app_build】' as 类型,

case when a.id is not null then
'暂不支持UPDATE语句生成'
else
(
case when '${应用app_id：}' = '' or '${模块标准名：}' = '' or '${包类型：jar,war,pom...}' = '' or '${制品库名：}' = '' or '${是否整包【0散包 1整包】：=,|0|1}' = '' then '缺少生成INSERT语句的要素' else
concat(
" INSERT INTO spider.app_mgt_app_build " ,
" (app_id, module_name, module_code, package_type, package_name, package_full, build_jdk_version, create_user, create_time, stamp) " ,
" VALUES " ,
" ( " ,
" '${应用app_id：}', "
" '${模块标准名：}', " ,
case when '${Resource_Code：}' = '' then " null, " else " '${Resource_Code：}', " end ,
" '${包类型：jar,war,pom...}', " ,
" '${模块标准名：}*.${包类型：jar,war,pom...}', " ,
" '${是否整包【0散包 1整包】：=,|0|1}', " ,
case when '${JDK版本号：}' = '' then " null, " else " '${JDK版本号：}', " end ,
" 'yixiang.zhang', " ,
" now(), " ,
" '0' " ,
" ); "
)
end
)
end as 内容

from spider.tool_mgt_git_server

left outer join
(select id from spider.app_mgt_app_build ab where ab.module_name = '${模块标准名：}') a
on 1=1


) b


