%spider



select '已存在app_id' as 类型, id as 内容

from spider.app_mgt_app_info ai

where ai.app_name = '${应用标准名：}'

union all


select * from
(

select
distinct

'应用基础表sql语句【app_mgt_app_info】',

case when a.id is not null then
'暂不支持UPDATE语句生成'
else
(
case when '${应用标准名：}' = '' or '${应用中文名：}' = '' or '${git组名：}' = '' or '${git仓库名：}' = '' or '${制品本地目录：}' = '' or '${是否第三方【 0自研 1第三方】：=,|0|1}' = '' then '缺少生成INSERT语句的要素' else
concat(
" INSERT INTO spider.app_mgt_app_info " ,
" (app_name, app_cname, app_status, git_url, git_path, app_jdk_version, create_user, create_time, stamp, lib_location, platform_type, platform_time, third_party_middleware) " ,
" VALUES " ,
" ( " ,
" '${应用标准名：}', " ,
" '${应用中文名：}', " ,
" '1', " ,
" '${git组名：}', " ,
" '/${git仓库名：}', " ,
case when '${JDK版本号：}' = '' then " null, " else " '${JDK版本号：}', " end ,
" 'yixiang.zhang', " ,
" now(), " ,
" '0', " ,
" '${制品本地目录：}', " ,
" '1', " ,
" now(), " ,
" '${是否第三方【0自研 1第三方】：=,|0|1}' " ,
" ); "

)
end
)
end

from spider.tool_mgt_git_server

left outer join
(select id from spider.app_mgt_app_info ai where ai.app_name = '${应用标准名：}') a
on 1=1

) x


union all


select * from
(

select
distinct

'团队关联表sql语句【app_mgt_app_team】',

case when a.app_id is null or b.at_id is not null then
'无法生成SQL语句，已经存在数据，或还没执行基础应用信息'
else
(
case when '${应用标准名：}' = '' or '${团队名：=,|crm|tp|tms|h5|安卓|ios|fpc|pa}' = '' then '缺少生成INSERT语句的要素' else
concat(
" INSERT INTO spider.app_mgt_app_team " ,
" (app_id, team_id, create_user, create_time, stamp) " ,
" VALUES " ,
" ( " ,
a.app_id ,
" , " ,
(select id from spider.tool_mgt_team_info where team_short_name = '${团队名：=,|crm|tp|tms|h5|安卓|ios|fpc|pa}') ,
" , ",
" 'yixiang.zhang', " ,
" now(), " ,
" '0' " ,
" ); "

)
end
)
end

from spider.tool_mgt_git_server

left outer join
(select id as app_id from spider.app_mgt_app_info ai where ai.app_name = '${应用标准名：}') a
on 1=1

left outer join
(select at.id as at_id from spider.app_mgt_app_team at, spider.app_mgt_app_info ai where at.app_id = ai.id and ai.app_name = '${应用标准名：}') b
on 1=1

) y


union all



select * from
(

select
distinct

'新增git组sql语句【tool_mgt_git_url】',

case when a.id is not null then
'已经创建过git组'
else
(
case when '${git组名：}' = '' then '缺少生成INSERT语句的要素' else
concat(
" INSERT INTO spider.tool_mgt_git_url " ,
" (git_url, git_name, git_server_id, git_url_desc, create_user, create_time, stamp) " ,
" VALUES " ,
" ( " ,
" '${git组名：}', " ,
" 'git-${git组名：}', " ,
" '1', " ,
" '${git组名：}', " ,
" 'yixiang.zhang', " ,
" now(), " ,
" '0' " ,
" ); "

)
end
)
end

from spider.tool_mgt_git_server

left outer join
(select id from spider.tool_mgt_git_url gu where gu.git_url = '${git组名：}') a
on 1=1

) z


