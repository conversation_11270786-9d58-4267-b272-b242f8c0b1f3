%spider



select
team_alias as '团队关联团队名',
app_name as '团队关联应用名',
real_app_name as '绑定节点的应用名',
ip as '节点IP',
cpu as '处理器核数',
round(memory/1024,0) as '内存容量GB',
region_name as '节点机房环境'

from
(
-- 核心逻辑==============================================================

SELECT DISTINCT
case when a.team_alias is null then 'UNDEF' else a.team_alias end as team_alias,
case when a.app_name is null then 'UNDEF' else a.app_name end as app_name,
case when s.counts = 1 then (case when s.app_name is null then 'UNDEF' else s.app_name end) else s.memo end as real_app_name,
s.ip,
case when s.region_name is null then 'UNDEF' else concat(r.addr_name,'-',r.type_name) end as region_name,
s.cpu,
s.memory
FROM
(
-- 筛选符合要求的记录==============================================================
select concat(min(s.app_name),'等',cast(count(0) as char),'个应用') as memo,min(s.app_name) as app_name,count(0) as counts, s.ip, s.day, s.region_name, s.cpu, s.memory
from spider.node_info_snapshot s
where s.day =
(
-- 不选择日期就默认从今天往前==============================================================
select max(s.day) from spider.node_info_snapshot s
where
s.day <= CONVERT(date_format(now(), '%Y%m%d'),unsigned)
-- 不选择日期就默认从今天往前==============================================================
)
group by s.ip, s.day, s.region_name, s.cpu, s.memory
-- 筛选符合要求的记录==============================================================
) s
-- 用来确定环境名称和是否使用==============================================================
LEFT OUTER JOIN
spider.env_mgt_region r
on r.region_name = s.region_name and r.region_is_active = '1'
-- 用来确定环境名称和是否使用==============================================================
LEFT OUTER JOIN
-- 团队和应用关系拼接==============================================================
(
SELECT distinct ti.team_alias, am.module_name as app_name
from app_mgt_app_team at, spider.tool_mgt_team_info ti, app_mgt_app_info ai, spider.app_mgt_app_module am
where at.app_id=ai.id and at.team_id=ti.id and ai.app_status = '1' and am.app_id=ai.id and am.need_online='1'
and concat(at.team_id,'|',at.app_id) in
-- 应用只属于更早创建的那个团队==============================================================
(select concat(min(at.team_id),'|',at.app_id) from app_mgt_app_team at group by at.app_id)
-- 应用只属于更早创建的那个团队==============================================================
) a
-- 团队和应用关系拼接==============================================================
on s.app_name=a.app_name


-- 核心逻辑==============================================================
where
('${查询类型：=,|只查询【已绑定应用但不明确团队】的节点|只查询【没有绑定应用】的节点|只查询【基础信息不准确】的节点|只查询【绑定应用并明确所属团队】的节点}' = '')
OR
('${查询类型：=,|只查询【已绑定应用但不明确团队】的节点|只查询【没有绑定应用】的节点|只查询【基础信息不准确】的节点|只查询【绑定应用并明确所属团队】的节点}' = '只查询【没有绑定应用】的节点' and s.app_name is null)
OR
('${查询类型：=,|只查询【已绑定应用但不明确团队】的节点|只查询【没有绑定应用】的节点|只查询【基础信息不准确】的节点|只查询【绑定应用并明确所属团队】的节点}' = '只查询【已绑定应用但不明确团队】的节点' and a.app_name is null and s.app_name is not null)
OR
('${查询类型：=,|只查询【已绑定应用但不明确团队】的节点|只查询【没有绑定应用】的节点|只查询【基础信息不准确】的节点|只查询【绑定应用并明确所属团队】的节点}' = '只查询【绑定应用并明确所属团队】的节点' and a.app_name is not null)
OR
('${查询类型：=,|只查询【已绑定应用但不明确团队】的节点|只查询【没有绑定应用】的节点|只查询【基础信息不准确】的节点|只查询【绑定应用并明确所属团队】的节点}' = '只查询【基础信息不准确】的节点' and (cpu = '0' or cpu is null or memory = '0' or memory is null))


) o

where team_alias like '${可选择团队：=,|达摩院|爱码仕|藏经阁|六扇门|宙斯盾|罗刹堂|解忧杂货铺|毗沙门|银河战队|及时雨|码上飘|MACH}%'
and region_name like '${可选择机房-环境：=,|外高桥-产线|外高桥-灰度|优刻得-产线|宝山-产线|宝山-灾备|宝山-仿真|虹口-产线|虹口-测试|虹口-开发|UNDEF}%'
and ip like '%${可输入IP【模糊】：}%' and real_app_name like '%${可输入应用【模糊】：}%'

order by region_name,real_app_name,ip



