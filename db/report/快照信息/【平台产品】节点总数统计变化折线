%spider






select STR_TO_DATE(x.day, '%Y%m%d') as day, x.count_day, y.counts, y.region_name,
'${可选择阶段：=,|prod|hd|uat|test|dev}%' as chosen_region_group,
'${可选择机房-环境：=,|外高桥-产线|外高桥-灰度|优刻得-产线|唐镇-产线|唐镇-灾备|唐镇-仿真|虹口-产线|虹口-测试|虹口-开发|UNDEF}%' as chosen_region_name
from
(
select b.day,
(
select max(s.day) from spider.node_info_snapshot s
where
s.day <= b.day
) as count_day
from
(

select day from
(
select date_format(adddate( date_format(now(), '%Y-%m-%d'), -nums),  '%Y%m%d') as day from
(
select @num:=@num+1 as nums from node_info_snapshot s,(select @num:=-1) n
) a
) b
where day >=
(
select min(s.day) from spider.node_info_snapshot s
)
order by day desc
limit 365

) b

) x



inner join



(
select count(0) as counts, day, region_name, region_group from
(
-- 核心逻辑==============================================================
SELECT
distinct
case when a.team_alias is null then 'UNDEF' else a.team_alias end as team_alias,
case when a.app_name is null then 'UNDEF' else a.app_name end as app_name,
case when s.counts = 1 then (case when s.app_name is null then 'UNDEF' else s.app_name end) else s.memo end as real_app_name,
s.ip,
case when s.region_name is null then 'UNDEF' else concat(r.addr_name,'-',r.type_name) end as region_name,
s.cpu,
s.memory,
s.day,
r.region_group
FROM
(
-- 筛选符合要求的记录==============================================================
select concat(min(s.app_name),'等',cast(count(0) as char),'个应用') as memo,min(s.app_name) as app_name,count(0) as counts, s.ip, s.day, s.region_name, s.cpu, s.memory
from spider.node_info_snapshot s
group by s.ip, s.day, s.region_name, s.cpu, s.memory
-- 筛选符合要求的记录==============================================================
) s
-- 用来确定环境名称和是否使用==============================================================
LEFT OUTER JOIN
spider.env_mgt_region r
on r.region_name = s.region_name and r.region_is_active = '1'
-- 用来确定环境名称和是否使用==============================================================
LEFT OUTER JOIN
-- 团队和应用关系拼接==============================================================
(
SELECT distinct ti.team_alias, am.module_name as app_name
from app_mgt_app_team at, spider.tool_mgt_team_info ti, app_mgt_app_info ai, spider.app_mgt_app_module am
where at.app_id=ai.id and at.team_id=ti.id and am.app_id=ai.id and am.need_online='1'
-- and ai.app_status = '1'
and concat(at.team_id,'|',at.app_id) in
-- 应用只属于更早创建的那个团队==============================================================
(select concat(min(at.team_id),'|',at.app_id) from app_mgt_app_team at group by at.app_id)
-- 应用只属于更早创建的那个团队==============================================================
) a
-- 团队和应用关系拼接==============================================================
on s.app_name=a.app_name
-- 核心逻辑==============================================================

) b

where team_alias like '${可选择团队：=,|达摩院|爱码仕|藏经阁|六扇门|宙斯盾|罗刹堂|解忧杂货铺|毗沙门|银河战队|及时雨|码上飘|MACH|UNDEF}%'
and (app_name = '${可输入应用【精确】：}'  or '${可输入应用【精确】：}' = '' )

group by day, region_name, region_group

) y

on x.count_day=y.day

where region_group like '${可选择阶段：=,|prod|hd|uat|test|dev}%'
and region_name like '${可选择机房-环境：=,|外高桥-产线|外高桥-灰度|优刻得-产线|唐镇-产线|唐镇-灾备|唐镇-仿真|虹口-产线|虹口-测试|虹口-开发|UNDEF}%'

order by x.day,region_name desc




