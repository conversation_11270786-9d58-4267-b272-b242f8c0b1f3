%spider


select
day as '当次快照日期',
ip as '当次IP',
cpu as '当次CPU',
round(memory/1024,0) as '当次内存GB',
region_name as '当次所属',
old_day as '前次快照日期',
old_ip as '前次IP',
old_cpu as '前次CPU',
round(old_memory/1024,0) as '前次内存GB',
old_region_name as '前次所属',
team_alias as '团队',
real_app_name as '应用名称'


from

(


-- 【以最新一次为准，和上一次对比】============================================================================================================================

Select
x.day, y.day as old_day,x.team_alias,x.app_name,x.real_app_name,
x.ip,x.cpu,x.memory,x.region_name,
y.ip as old_ip, y.cpu as old_cpu, y.memory as old_memory,y.region_name as old_region_name,
x.region_group
from

(

-- 核心逻辑==============================================================

SELECT
distinct
case when a.team_alias is null then 'UNDEF' else a.team_alias end as team_alias,
case when a.app_name is null then 'UNDEF' else a.app_name end as app_name,
case when s.counts = 1 then (case when s.app_name is null then 'UNDEF' else s.app_name end) else s.memo end as real_app_name,
s.ip,
case when s.region_name is null then 'UNDEF' else concat(r.addr_name,'-',r.type_name) end as region_name,
s.cpu,
s.memory,
s.day,
r.region_group
FROM
(
-- 筛选符合要求的记录==============================================================
select concat(min(s.app_name),'等',cast(count(0) as char),'个应用') as memo,min(s.app_name) as app_name,count(0) as counts, s.ip, s.day, s.region_name, s.cpu, s.memory
from spider.node_info_snapshot s
where s.day =
(
-- 不选择日期就默认从今天往前==============================================================
select max(s.day) from spider.node_info_snapshot s
where
('${可输入日期yyyyMMdd，如20200801：}' = '' and s.day <= CONVERT(date_format(now(), '%Y%m%d'),unsigned)
or
('${可输入日期yyyyMMdd，如20200801：}' <> '' and s.day <= '${可输入日期yyyyMMdd，如20200801：}'))
-- 不选择日期就默认从今天往前==============================================================
)
group by s.ip, s.day, s.region_name, s.cpu, s.memory
-- 筛选符合要求的记录==============================================================
) s
-- 用来确定环境名称和是否使用==============================================================
LEFT OUTER JOIN
spider.env_mgt_region r
on r.region_name = s.region_name and r.region_is_active = '1'
-- 用来确定环境名称和是否使用==============================================================
LEFT OUTER JOIN
-- 团队和应用关系拼接==============================================================
(
SELECT distinct ti.team_alias, am.module_name as app_name
from app_mgt_app_team at, spider.tool_mgt_team_info ti, app_mgt_app_info ai, spider.app_mgt_app_module am
where at.app_id=ai.id and at.team_id=ti.id and ai.app_status = '1' and am.app_id=ai.id and am.need_online='1'
and concat(at.team_id,'|',at.app_id) in
-- 应用只属于更早创建的那个团队==============================================================
(select concat(min(at.team_id),'|',at.app_id) from app_mgt_app_team at group by at.app_id)
-- 应用只属于更早创建的那个团队==============================================================
) a
-- 团队和应用关系拼接==============================================================
on s.app_name=a.app_name


-- 核心逻辑==============================================================


) x

left outer join

(


-- 核心逻辑==============================================================

SELECT
distinct
case when a.team_alias is null then 'UNDEF' else a.team_alias end as team_alias,
case when a.app_name is null then 'UNDEF' else a.app_name end as app_name,
case when s.counts = 1 then (case when s.app_name is null then 'UNDEF' else s.app_name end) else s.memo end as real_app_name,
s.ip,
case when s.region_name is null then 'UNDEF' else concat(r.addr_name,'-',r.type_name) end as region_name,
s.cpu,
s.memory,
s.day
FROM
(
-- 筛选符合要求的记录==============================================================
select concat(min(s.app_name),'等',cast(count(0) as char),'个应用') as memo,min(s.app_name) as app_name,count(0) as counts, s.ip, s.day, s.region_name, s.cpu, s.memory
from spider.node_info_snapshot s
where s.day =
(

-- 选择前一次日期的逻辑==============================================================
select max(s.day) from spider.node_info_snapshot s
where
(
(
'${前一次不早于日期yyyyMMdd，如20200801：}' = '' or '${前一次不早于日期yyyyMMdd，如20200801：}' >=
(
select max(s.day) from spider.node_info_snapshot s
where
('${可输入日期yyyyMMdd，如20200801：}' = '' and s.day <= CONVERT(date_format(now(), '%Y%m%d'),unsigned)
or
('${可输入日期yyyyMMdd，如20200801：}' <> '' and s.day <= '${可输入日期yyyyMMdd，如20200801：}'))
)
)
and s.day <
(
select max(s.day) from spider.node_info_snapshot s
where
('${可输入日期yyyyMMdd，如20200801：}' = '' and s.day <= CONVERT(date_format(now(), '%Y%m%d'),unsigned)
or
('${可输入日期yyyyMMdd，如20200801：}' <> '' and s.day <= '${可输入日期yyyyMMdd，如20200801：}'))
)
)

or

(
('${前一次不早于日期yyyyMMdd，如20200801：}' <
(
select max(s.day) from spider.node_info_snapshot s
where
('${可输入日期yyyyMMdd，如20200801：}' = '' and s.day <= CONVERT(date_format(now(), '%Y%m%d'),unsigned)
or
('${可输入日期yyyyMMdd，如20200801：}' <> '' and s.day <= '${可输入日期yyyyMMdd，如20200801：}'))
)
)
and s.day <=
(
select max(s.day) from spider.node_info_snapshot s where s.day <= '${前一次不早于日期yyyyMMdd，如20200801：}'
)
)
-- 选择前一次日期的逻辑==============================================================

)
group by s.ip, s.day, s.region_name, s.cpu, s.memory
-- 筛选符合要求的记录==============================================================
) s
-- 用来确定环境名称和是否使用==============================================================
LEFT OUTER JOIN
spider.env_mgt_region r
on r.region_name = s.region_name and r.region_is_active = '1'
-- 用来确定环境名称和是否使用==============================================================
LEFT OUTER JOIN
-- 团队和应用关系拼接==============================================================
(
SELECT distinct ti.team_alias, am.module_name as app_name
from app_mgt_app_team at, spider.tool_mgt_team_info ti, app_mgt_app_info ai, spider.app_mgt_app_module am
where at.app_id=ai.id and at.team_id=ti.id and ai.app_status = '1' and am.app_id=ai.id and am.need_online='1'
and concat(at.team_id,'|',at.app_id) in
-- 应用只属于更早创建的那个团队==============================================================
(select concat(min(at.team_id),'|',at.app_id) from app_mgt_app_team at group by at.app_id)
-- 应用只属于更早创建的那个团队==============================================================
) a
-- 团队和应用关系拼接==============================================================
on s.app_name=a.app_name


-- 核心逻辑==============================================================

) y

on x.ip=y.ip

-- 【以最新一次为准，和上一次对比】============================================================================================================================


union all


-- 【以上一次为准，和最新一次对比】============================================================================================================================
Select
null as day , z.day as old_day,z.team_alias,z.app_name,z.real_app_name,
null as ip,null as cpu,null as memory,null as region_name,
z.ip as old_ip, z.cpu as old_cpu, z.memory as old_memory,z.region_name as old_region_name,
z.region_group
from

(

-- 核心逻辑==============================================================

SELECT
distinct
case when a.team_alias is null then 'UNDEF' else a.team_alias end as team_alias,
case when a.app_name is null then 'UNDEF' else a.app_name end as app_name,
case when s.counts = 1 then (case when s.app_name is null then 'UNDEF' else s.app_name end) else s.memo end as real_app_name,
s.ip,
case when s.region_name is null then 'UNDEF' else concat(r.addr_name,'-',r.type_name) end as region_name,
s.cpu,
s.memory,
s.day,
r.region_group
FROM
(
-- 筛选符合要求的记录==============================================================
select concat(min(s.app_name),'等',cast(count(0) as char),'个应用') as memo,min(s.app_name) as app_name,count(0) as counts, s.ip, s.day, s.region_name, s.cpu, s.memory
from spider.node_info_snapshot s
where s.day =
(



-- 选择前一次日期的逻辑==============================================================
select max(s.day) from spider.node_info_snapshot s
where
(
(
'${前一次不早于日期yyyyMMdd，如20200801：}' = '' or '${前一次不早于日期yyyyMMdd，如20200801：}' >=
(
select max(s.day) from spider.node_info_snapshot s
where
('${可输入日期yyyyMMdd，如20200801：}' = '' and s.day <= CONVERT(date_format(now(), '%Y%m%d'),unsigned)
or
('${可输入日期yyyyMMdd，如20200801：}' <> '' and s.day <= '${可输入日期yyyyMMdd，如20200801：}'))
)
)
and s.day <
(
select max(s.day) from spider.node_info_snapshot s
where
('${可输入日期yyyyMMdd，如20200801：}' = '' and s.day <= CONVERT(date_format(now(), '%Y%m%d'),unsigned)
or
('${可输入日期yyyyMMdd，如20200801：}' <> '' and s.day <= '${可输入日期yyyyMMdd，如20200801：}'))
)
)

or

(
('${前一次不早于日期yyyyMMdd，如20200801：}' <
(
select max(s.day) from spider.node_info_snapshot s
where
('${可输入日期yyyyMMdd，如20200801：}' = '' and s.day <= CONVERT(date_format(now(), '%Y%m%d'),unsigned)
or
('${可输入日期yyyyMMdd，如20200801：}' <> '' and s.day <= '${可输入日期yyyyMMdd，如20200801：}'))
)
)
and s.day <=
(
select max(s.day) from spider.node_info_snapshot s where s.day <= '${前一次不早于日期yyyyMMdd，如20200801：}'
)
)
-- 选择前一次日期的逻辑==============================================================



)
group by s.ip, s.day, s.region_name, s.cpu, s.memory
-- 筛选符合要求的记录==============================================================
) s
-- 用来确定环境名称和是否使用==============================================================
LEFT OUTER JOIN
spider.env_mgt_region r
on r.region_name = s.region_name and r.region_is_active = '1'
-- 用来确定环境名称和是否使用==============================================================
LEFT OUTER JOIN
-- 团队和应用关系拼接==============================================================
(
SELECT distinct ti.team_alias, am.module_name as app_name
from app_mgt_app_team at, spider.tool_mgt_team_info ti, app_mgt_app_info ai, spider.app_mgt_app_module am
where at.app_id=ai.id and at.team_id=ti.id and ai.app_status = '1' and am.app_id=ai.id and am.need_online='1'
and concat(at.team_id,'|',at.app_id) in
-- 应用只属于更早创建的那个团队==============================================================
(select concat(min(at.team_id),'|',at.app_id) from app_mgt_app_team at group by at.app_id)
-- 应用只属于更早创建的那个团队==============================================================
) a
-- 团队和应用关系拼接==============================================================
on s.app_name=a.app_name


-- 核心逻辑==============================================================


) z

where z.ip not in
(
-- 核心逻辑==============================================================

SELECT distinct s.ip FROM
(
-- 筛选符合要求的记录==============================================================
select concat(min(s.app_name),'等',cast(count(0) as char),'个应用') as memo,min(s.app_name) as app_name,count(0) as counts, s.ip, s.day, s.region_name, s.cpu, s.memory
from spider.node_info_snapshot s
where s.day =
(
-- 不选择日期就默认从今天往前==============================================================

select max(s.day) from spider.node_info_snapshot s
where
('${可输入日期yyyyMMdd，如20200801：}' = '' and s.day <= CONVERT(date_format(now(), '%Y%m%d'),unsigned)
or
('${可输入日期yyyyMMdd，如20200801：}' <> '' and s.day <= '${可输入日期yyyyMMdd，如20200801：}'))
-- 不选择日期就默认从今天往前==============================================================
)
group by s.ip, s.day, s.region_name, s.cpu, s.memory
-- 筛选符合要求的记录==============================================================
) s
-- 用来确定环境名称和是否使用==============================================================
LEFT OUTER JOIN
spider.env_mgt_region r
on r.region_name = s.region_name and r.region_is_active = '1'
-- 用来确定环境名称和是否使用==============================================================
LEFT OUTER JOIN
-- 团队和应用关系拼接==============================================================
(
SELECT distinct ti.team_alias, am.module_name as app_name
from app_mgt_app_team at, spider.tool_mgt_team_info ti, app_mgt_app_info ai, spider.app_mgt_app_module am
where at.app_id=ai.id and at.team_id=ti.id and ai.app_status = '1' and am.app_id=ai.id and am.need_online='1'
and concat(at.team_id,'|',at.app_id) in
-- 应用只属于更早创建的那个团队==============================================================
(select concat(min(at.team_id),'|',at.app_id) from app_mgt_app_team at group by at.app_id)
-- 应用只属于更早创建的那个团队==============================================================
) a
-- 团队和应用关系拼接==============================================================
on s.app_name=a.app_name


-- 核心逻辑==============================================================
)

-- 【以上一次为准，和最新一次对比】============================================================================================================================


) m

where (ip<>old_ip or ip is null or old_ip is null or cpu<>old_cpu or memory <> old_memory or region_name <> old_region_name )
and region_group like '${可选择阶段：=,|prod|hd|uat|test|dev}%'
and (m.region_name like '${可选择机房-环境：=,|外高桥-产线|外高桥-灰度|优刻得-产线|宝山-产线|宝山-灾备|宝山-仿真|虹口-产线|虹口-测试|虹口-开发|UNDEF}%' or m.region_name is null)

order by region_name desc, ip desc



