%spider



select
'${请选择统计维度：=,|节点个数|CPU核数|内存容量GB}',
'${可选择机房-环境：=,|外高桥-产线|外高桥-灰度|优刻得-产线|宝山-产线|宝山-灾备|宝山-仿真|虹口-产线|虹口-测试|虹口-开发|UNDEF}',
p.*,
concat('(',counts,'/',totals,')') as ratio
from
(

-- ==============================================================统计节点个数==============================================================
select *, '节点个数' as mtype from
(

select counts, region_name, team, totals, stype
from
(
-- 第一大类全部拼接完==============================================================
select m.*,n.totals, '1' as stype from
(
-- 根据机房环境统计==============================================================
select count(0) as counts, region_name,'IT部门' as team
from
(
-- 核心逻辑==============================================================

SELECT
distinct
case when a.team_name is null then 'UNDEF' else a.team_name end as team_name,
case when a.app_name is null then 'UNDEF' else a.app_name end as app_name,
case when s.counts = 1 then (case when s.app_name is null then 'UNDEF' else s.app_name end) else s.memo end as real_app_name,
s.ip,
case when s.region_name is null then 'UNDEF' else concat(r.addr_name,'-',r.type_name) end as region_name
FROM
(
-- 筛选符合要求的记录==============================================================
select concat(min(s.app_name),'等',cast(count(0) as char),'个应用') as memo,min(s.app_name) as app_name,count(0) as counts, s.ip, s.day, s.region_name
from spider.node_info_snapshot s
where s.day =
(
-- 不选择日期就默认从今天往前==============================================================
select max(s.day) from spider.node_info_snapshot s
where
('${可输入日期yyyyMMdd，如20200801：}' = '' and s.day <= CONVERT(date_format(now(), '%Y%m%d'),unsigned)
or
('${可输入日期yyyyMMdd，如20200801：}' <> '' and s.day <= '${可输入日期yyyyMMdd，如20200801：}'))
-- 不选择日期就默认从今天往前==============================================================
)
group by s.ip, s.day, s.region_name
-- 筛选符合要求的记录==============================================================
) s
-- 用来确定环境名称和是否使用==============================================================
LEFT OUTER JOIN
spider.env_mgt_region r
on r.region_name = s.region_name and r.region_is_active = '1'
-- 用来确定环境名称和是否使用==============================================================
LEFT OUTER JOIN
-- 团队和应用关系拼接==============================================================
(
SELECT distinct ti.team_name, am.module_name as app_name
from app_mgt_app_team at, spider.tool_mgt_team_info ti, app_mgt_app_info ai, spider.app_mgt_app_module am
where at.app_id=ai.id and at.team_id=ti.id and ai.app_status = '1' and am.app_id=ai.id and am.need_online='1'
and concat(at.team_id,'|',at.app_id) in
-- 应用只属于更早创建的那个团队==============================================================
(select concat(min(at.team_id),'|',at.app_id) from app_mgt_app_team at group by at.app_id)
-- 应用只属于更早创建的那个团队==============================================================
) a
-- 团队和应用关系拼接==============================================================
on s.app_name=a.app_name

-- 核心逻辑==============================================================
) x
group by region_name
-- 根据机房环境统计==============================================================
) m

left outer join

(
-- 总数统计==============================================================
select count(0) as totals
from
(
-- 核心逻辑==============================================================

SELECT
distinct
case when a.team_name is null then 'UNDEF' else a.team_name end as team_name,
case when a.app_name is null then 'UNDEF' else a.app_name end as app_name,
case when s.counts = 1 then (case when s.app_name is null then 'UNDEF' else s.app_name end) else s.memo end as real_app_name,
s.ip,
case when s.region_name is null then 'UNDEF' else concat(r.addr_name,'-',r.type_name) end as region_name
FROM
(
-- 筛选符合要求的记录==============================================================
select concat(min(s.app_name),'等',cast(count(0) as char),'个应用') as memo,min(s.app_name) as app_name,count(0) as counts, s.ip, s.day, s.region_name
from spider.node_info_snapshot s
where s.day =
(
-- 不选择日期就默认从今天往前==============================================================
select max(s.day) from spider.node_info_snapshot s
where
('${可输入日期yyyyMMdd，如20200801：}' = '' and s.day <= CONVERT(date_format(now(), '%Y%m%d'),unsigned)
or
('${可输入日期yyyyMMdd，如20200801：}' <> '' and s.day <= '${可输入日期yyyyMMdd，如20200801：}'))
-- 不选择日期就默认从今天往前==============================================================
)
group by s.ip, s.day, s.region_name
-- 筛选符合要求的记录==============================================================
) s
-- 用来确定环境名称和是否使用==============================================================
LEFT OUTER JOIN
spider.env_mgt_region r
on r.region_name = s.region_name and r.region_is_active = '1'
-- 用来确定环境名称和是否使用==============================================================
LEFT OUTER JOIN
-- 团队和应用关系拼接==============================================================
(
SELECT distinct ti.team_name, am.module_name as app_name
from app_mgt_app_team at, spider.tool_mgt_team_info ti, app_mgt_app_info ai, spider.app_mgt_app_module am
where at.app_id=ai.id and at.team_id=ti.id and ai.app_status = '1' and am.app_id=ai.id and am.need_online='1'
and concat(at.team_id,'|',at.app_id) in
-- 应用只属于更早创建的那个团队==============================================================
(select concat(min(at.team_id),'|',at.app_id) from app_mgt_app_team at group by at.app_id)
-- 应用只属于更早创建的那个团队==============================================================
) a
-- 团队和应用关系拼接==============================================================
on s.app_name=a.app_name

-- 核心逻辑==============================================================
) x
-- 总数统计==============================================================
) n
on 1=1
-- 第一大类全部拼接完==============================================================
) xx



union all



select counts, region_name, team, totals, stype from
(

-- 第二大类全部拼接完==============================================================
select m.*,n.totals, '2' as stype from
(
-- 根据团队统计==============================================================
select count(0) as counts, '${可选择机房-环境：=,|外高桥-产线|外高桥-灰度|优刻得-产线|宝山-产线|宝山-灾备|宝山-仿真|虹口-产线|虹口-测试|虹口-开发|UNDEF}' as region_name, team_name as team
from
(
-- 核心逻辑==============================================================

SELECT
distinct
case when a.team_name is null then 'UNDEF' else a.team_name end as team_name,
case when a.app_name is null then 'UNDEF' else a.app_name end as app_name,
case when s.counts = 1 then (case when s.app_name is null then 'UNDEF' else s.app_name end) else s.memo end as real_app_name,
s.ip,
case when s.region_name is null then 'UNDEF' else concat(r.addr_name,'-',r.type_name) end as region_name
FROM
(
-- 筛选符合要求的记录==============================================================
select concat(min(s.app_name),'等',cast(count(0) as char),'个应用') as memo,min(s.app_name) as app_name,count(0) as counts, s.ip, s.day, s.region_name
from spider.node_info_snapshot s
where s.day =
(
-- 不选择日期就默认从今天往前==============================================================
select max(s.day) from spider.node_info_snapshot s
where
('${可输入日期yyyyMMdd，如20200801：}' = '' and s.day <= CONVERT(date_format(now(), '%Y%m%d'),unsigned)
or
('${可输入日期yyyyMMdd，如20200801：}' <> '' and s.day <= '${可输入日期yyyyMMdd，如20200801：}'))
-- 不选择日期就默认从今天往前==============================================================
)
group by s.ip, s.day, s.region_name
-- 筛选符合要求的记录==============================================================
) s
-- 用来确定环境名称和是否使用==============================================================
INNER JOIN
spider.env_mgt_region r
on r.region_name = s.region_name and r.region_is_active = '1'
-- 用来确定环境名称和是否使用==============================================================
LEFT OUTER JOIN
-- 团队和应用关系拼接==============================================================
(
SELECT distinct ti.team_name, am.module_name as app_name
from app_mgt_app_team at, spider.tool_mgt_team_info ti, app_mgt_app_info ai, spider.app_mgt_app_module am
where at.app_id=ai.id and at.team_id=ti.id and ai.app_status = '1' and am.app_id=ai.id and am.need_online='1'
and concat(at.team_id,'|',at.app_id) in
-- 应用只属于更早创建的那个团队==============================================================
(select concat(min(at.team_id),'|',at.app_id) from app_mgt_app_team at group by at.app_id)
-- 应用只属于更早创建的那个团队==============================================================
) a
-- 团队和应用关系拼接==============================================================
on s.app_name=a.app_name

-- 核心逻辑==============================================================

) y


where
('${可选择机房-环境：=,|外高桥-产线|外高桥-灰度|优刻得-产线|宝山-产线|宝山-灾备|宝山-仿真|虹口-产线|虹口-测试|虹口-开发|UNDEF}' = '' and region_name='test')
or
('${可选择机房-环境：=,|外高桥-产线|外高桥-灰度|优刻得-产线|宝山-产线|宝山-灾备|宝山-仿真|虹口-产线|虹口-测试|虹口-开发|UNDEF}' <> '' and region_name = '${可选择机房-环境：=,|外高桥-产线|外高桥-灰度|优刻得-产线|宝山-产线|宝山-灾备|宝山-仿真|虹口-产线|虹口-测试|虹口-开发|UNDEF}')
group by team_name
-- 根据团队统计==============================================================
) m

left outer join


(
-- 根据团队统计总数==============================================================
select count(0) as totals
from
(
-- 核心逻辑==============================================================

SELECT
distinct
case when a.team_name is null then 'UNDEF' else a.team_name end as team_name,
case when a.app_name is null then 'UNDEF' else a.app_name end as app_name,
case when s.counts = 1 then (case when s.app_name is null then 'UNDEF' else s.app_name end) else s.memo end as real_app_name,
s.ip,
case when s.region_name is null then 'UNDEF' else concat(r.addr_name,'-',r.type_name) end as region_name
FROM
(
-- 筛选符合要求的记录==============================================================
select concat(min(s.app_name),'等',cast(count(0) as char),'个应用') as memo,min(s.app_name) as app_name,count(0) as counts, s.ip, s.day, s.region_name
from spider.node_info_snapshot s
where s.day =
(
-- 不选择日期就默认从今天往前==============================================================
select max(s.day) from spider.node_info_snapshot s
where
('${可输入日期yyyyMMdd，如20200801：}' = '' and s.day <= CONVERT(date_format(now(), '%Y%m%d'),unsigned)
or
('${可输入日期yyyyMMdd，如20200801：}' <> '' and s.day <= '${可输入日期yyyyMMdd，如20200801：}'))
-- 不选择日期就默认从今天往前==============================================================
)
group by s.ip, s.day, s.region_name
-- 筛选符合要求的记录==============================================================
) s
-- 用来确定环境名称和是否使用==============================================================
INNER JOIN
spider.env_mgt_region r
on r.region_name = s.region_name and r.region_is_active = '1'
-- 用来确定环境名称和是否使用==============================================================
LEFT OUTER JOIN
-- 团队和应用关系拼接==============================================================
(
SELECT distinct ti.team_name, am.module_name as app_name
from app_mgt_app_team at, spider.tool_mgt_team_info ti, app_mgt_app_info ai, spider.app_mgt_app_module am
where at.app_id=ai.id and at.team_id=ti.id and ai.app_status = '1' and am.app_id=ai.id and am.need_online='1'
and concat(at.team_id,'|',at.app_id) in
-- 应用只属于更早创建的那个团队==============================================================
(select concat(min(at.team_id),'|',at.app_id) from app_mgt_app_team at group by at.app_id)
-- 应用只属于更早创建的那个团队==============================================================
) a
-- 团队和应用关系拼接==============================================================
on s.app_name=a.app_name

-- 核心逻辑==============================================================

) y


where
('${可选择机房-环境：=,|外高桥-产线|外高桥-灰度|优刻得-产线|宝山-产线|宝山-灾备|宝山-仿真|虹口-产线|虹口-测试|虹口-开发|UNDEF}' = '' and region_name='test')
or
('${可选择机房-环境：=,|外高桥-产线|外高桥-灰度|优刻得-产线|宝山-产线|宝山-灾备|宝山-仿真|虹口-产线|虹口-测试|虹口-开发|UNDEF}' <> '' and region_name = '${可选择机房-环境：=,|外高桥-产线|外高桥-灰度|优刻得-产线|宝山-产线|宝山-灾备|宝山-仿真|虹口-产线|虹口-测试|虹口-开发|UNDEF}')
-- 根据团队统计总数==============================================================
) n
on 1=1


-- 第二大类全部拼接完==============================================================
) yy


) o
-- ==============================================================统计节点个数==============================================================



union all


-- ==============================================================统计CPU核数==============================================================
select *, 'CPU核数' as mtype from
(




select counts, region_name, team, totals, stype
from
(
-- 第一大类全部拼接完==============================================================
select m.*,n.totals, '1' as stype from
(
-- 根据机房环境统计==============================================================
select sum(cpu) as counts, region_name,'IT部门' as team
from
(
-- 核心逻辑==============================================================

SELECT
distinct
case when a.team_name is null then 'UNDEF' else a.team_name end as team_name,
case when a.app_name is null then 'UNDEF' else a.app_name end as app_name,
case when s.counts = 1 then (case when s.app_name is null then 'UNDEF' else s.app_name end) else s.memo end as real_app_name,
s.ip,
case when s.region_name is null then 'UNDEF' else concat(r.addr_name,'-',r.type_name) end as region_name
,s.cpu
FROM
(
-- 筛选符合要求的记录==============================================================
select concat(min(s.app_name),'等',cast(count(0) as char),'个应用') as memo,min(s.app_name) as app_name,count(0) as counts, s.ip, s.day, s.region_name
,s.cpu
from spider.node_info_snapshot s
where s.day =
(
-- 不选择日期就默认从今天往前==============================================================
select max(s.day) from spider.node_info_snapshot s
where
('${可输入日期yyyyMMdd，如20200801：}' = '' and s.day <= CONVERT(date_format(now(), '%Y%m%d'),unsigned)
or
('${可输入日期yyyyMMdd，如20200801：}' <> '' and s.day <= '${可输入日期yyyyMMdd，如20200801：}'))
-- 不选择日期就默认从今天往前==============================================================
)
group by s.ip, s.day, s.region_name
,s.cpu
-- 筛选符合要求的记录==============================================================
) s
-- 用来确定环境名称和是否使用==============================================================
LEFT OUTER JOIN
spider.env_mgt_region r
on r.region_name = s.region_name and r.region_is_active = '1'
-- 用来确定环境名称和是否使用==============================================================
LEFT OUTER JOIN
-- 团队和应用关系拼接==============================================================
(
SELECT distinct ti.team_name, am.module_name as app_name
from app_mgt_app_team at, spider.tool_mgt_team_info ti, app_mgt_app_info ai, spider.app_mgt_app_module am
where at.app_id=ai.id and at.team_id=ti.id and ai.app_status = '1' and am.app_id=ai.id and am.need_online='1'
and concat(at.team_id,'|',at.app_id) in
-- 应用只属于更早创建的那个团队==============================================================
(select concat(min(at.team_id),'|',at.app_id) from app_mgt_app_team at group by at.app_id)
-- 应用只属于更早创建的那个团队==============================================================
) a
-- 团队和应用关系拼接==============================================================
on s.app_name=a.app_name

-- 核心逻辑==============================================================
) x
group by region_name
-- 根据机房环境统计==============================================================
) m

left outer join

(
-- 总数统计==============================================================
select sum(cpu) as totals
from
(
-- 核心逻辑==============================================================

SELECT
distinct
case when a.team_name is null then 'UNDEF' else a.team_name end as team_name,
case when a.app_name is null then 'UNDEF' else a.app_name end as app_name,
case when s.counts = 1 then (case when s.app_name is null then 'UNDEF' else s.app_name end) else s.memo end as real_app_name,
s.ip,
case when s.region_name is null then 'UNDEF' else concat(r.addr_name,'-',r.type_name) end as region_name
,s.cpu
FROM
(
-- 筛选符合要求的记录==============================================================
select concat(min(s.app_name),'等',cast(count(0) as char),'个应用') as memo,min(s.app_name) as app_name,count(0) as counts, s.ip, s.day, s.region_name
,s.cpu
from spider.node_info_snapshot s
where s.day =
(
-- 不选择日期就默认从今天往前==============================================================
select max(s.day) from spider.node_info_snapshot s
where
('${可输入日期yyyyMMdd，如20200801：}' = '' and s.day <= CONVERT(date_format(now(), '%Y%m%d'),unsigned)
or
('${可输入日期yyyyMMdd，如20200801：}' <> '' and s.day <= '${可输入日期yyyyMMdd，如20200801：}'))
-- 不选择日期就默认从今天往前==============================================================
)
group by s.ip, s.day, s.region_name
,s.cpu
-- 筛选符合要求的记录==============================================================
) s
-- 用来确定环境名称和是否使用==============================================================
LEFT OUTER JOIN
spider.env_mgt_region r
on r.region_name = s.region_name and r.region_is_active = '1'
-- 用来确定环境名称和是否使用==============================================================
LEFT OUTER JOIN
-- 团队和应用关系拼接==============================================================
(
SELECT distinct ti.team_name, am.module_name as app_name
from app_mgt_app_team at, spider.tool_mgt_team_info ti, app_mgt_app_info ai, spider.app_mgt_app_module am
where at.app_id=ai.id and at.team_id=ti.id and ai.app_status = '1' and am.app_id=ai.id and am.need_online='1'
and concat(at.team_id,'|',at.app_id) in
-- 应用只属于更早创建的那个团队==============================================================
(select concat(min(at.team_id),'|',at.app_id) from app_mgt_app_team at group by at.app_id)
-- 应用只属于更早创建的那个团队==============================================================
) a
-- 团队和应用关系拼接==============================================================
on s.app_name=a.app_name

-- 核心逻辑==============================================================
) x
-- 总数统计==============================================================
) n
on 1=1
-- 第一大类全部拼接完==============================================================
) xx



union all




select counts, region_name, team, totals, stype from
(

-- 第二大类全部拼接完==============================================================
select m.*,n.totals, '2' as stype from
(
-- 根据团队统计==============================================================
select sum(cpu) as counts, '${可选择机房-环境：=,|外高桥-产线|外高桥-灰度|优刻得-产线|宝山-产线|宝山-灾备|宝山-仿真|虹口-产线|虹口-测试|虹口-开发|UNDEF}' as region_name, team_name as team
from
(
-- 核心逻辑==============================================================

SELECT
distinct
case when a.team_name is null then 'UNDEF' else a.team_name end as team_name,
case when a.app_name is null then 'UNDEF' else a.app_name end as app_name,
case when s.counts = 1 then (case when s.app_name is null then 'UNDEF' else s.app_name end) else s.memo end as real_app_name,
s.ip,
case when s.region_name is null then 'UNDEF' else concat(r.addr_name,'-',r.type_name) end as region_name
,s.cpu
FROM
(
-- 筛选符合要求的记录==============================================================
select concat(min(s.app_name),'等',cast(count(0) as char),'个应用') as memo,min(s.app_name) as app_name,count(0) as counts, s.ip, s.day, s.region_name
,s.cpu
from spider.node_info_snapshot s
where s.day =
(
-- 不选择日期就默认从今天往前==============================================================
select max(s.day) from spider.node_info_snapshot s
where
('${可输入日期yyyyMMdd，如20200801：}' = '' and s.day <= CONVERT(date_format(now(), '%Y%m%d'),unsigned)
or
('${可输入日期yyyyMMdd，如20200801：}' <> '' and s.day <= '${可输入日期yyyyMMdd，如20200801：}'))
-- 不选择日期就默认从今天往前==============================================================
)
group by s.ip, s.day, s.region_name
,s.cpu
-- 筛选符合要求的记录==============================================================
) s
-- 用来确定环境名称和是否使用==============================================================
INNER JOIN
spider.env_mgt_region r
on r.region_name = s.region_name and r.region_is_active = '1'
-- 用来确定环境名称和是否使用==============================================================
LEFT OUTER JOIN
-- 团队和应用关系拼接==============================================================
(
SELECT distinct ti.team_name, am.module_name as app_name
from app_mgt_app_team at, spider.tool_mgt_team_info ti, app_mgt_app_info ai, spider.app_mgt_app_module am
where at.app_id=ai.id and at.team_id=ti.id and ai.app_status = '1' and am.app_id=ai.id and am.need_online='1'
and concat(at.team_id,'|',at.app_id) in
-- 应用只属于更早创建的那个团队==============================================================
(select concat(min(at.team_id),'|',at.app_id) from app_mgt_app_team at group by at.app_id)
-- 应用只属于更早创建的那个团队==============================================================
) a
-- 团队和应用关系拼接==============================================================
on s.app_name=a.app_name

-- 核心逻辑==============================================================

) y


where
('${可选择机房-环境：=,|外高桥-产线|外高桥-灰度|优刻得-产线|宝山-产线|宝山-灾备|宝山-仿真|虹口-产线|虹口-测试|虹口-开发|UNDEF}' = '' and region_name='test')
or
('${可选择机房-环境：=,|外高桥-产线|外高桥-灰度|优刻得-产线|宝山-产线|宝山-灾备|宝山-仿真|虹口-产线|虹口-测试|虹口-开发|UNDEF}' <> '' and region_name = '${可选择机房-环境：=,|外高桥-产线|外高桥-灰度|优刻得-产线|宝山-产线|宝山-灾备|宝山-仿真|虹口-产线|虹口-测试|虹口-开发|UNDEF}')
group by team_name
-- 根据团队统计==============================================================
) m

left outer join


(
-- 根据团队统计总数==============================================================
select sum(cpu) as totals
from
(
-- 核心逻辑==============================================================

SELECT
distinct
case when a.team_name is null then 'UNDEF' else a.team_name end as team_name,
case when a.app_name is null then 'UNDEF' else a.app_name end as app_name,
case when s.counts = 1 then (case when s.app_name is null then 'UNDEF' else s.app_name end) else s.memo end as real_app_name,
s.ip,
case when s.region_name is null then 'UNDEF' else concat(r.addr_name,'-',r.type_name) end as region_name
,s.cpu
FROM
(
-- 筛选符合要求的记录==============================================================
select concat(min(s.app_name),'等',cast(count(0) as char),'个应用') as memo,min(s.app_name) as app_name,count(0) as counts, s.ip, s.day, s.region_name
,s.cpu
from spider.node_info_snapshot s
where s.day =
(
-- 不选择日期就默认从今天往前==============================================================
select max(s.day) from spider.node_info_snapshot s
where
('${可输入日期yyyyMMdd，如20200801：}' = '' and s.day <= CONVERT(date_format(now(), '%Y%m%d'),unsigned)
or
('${可输入日期yyyyMMdd，如20200801：}' <> '' and s.day <= '${可输入日期yyyyMMdd，如20200801：}'))
-- 不选择日期就默认从今天往前==============================================================
)
group by s.ip, s.day, s.region_name
,cpu
-- 筛选符合要求的记录==============================================================
) s
-- 用来确定环境名称和是否使用==============================================================
INNER JOIN
spider.env_mgt_region r
on r.region_name = s.region_name and r.region_is_active = '1'
-- 用来确定环境名称和是否使用==============================================================
LEFT OUTER JOIN
-- 团队和应用关系拼接==============================================================
(
SELECT distinct ti.team_name, am.module_name as app_name
from app_mgt_app_team at, spider.tool_mgt_team_info ti, app_mgt_app_info ai, spider.app_mgt_app_module am
where at.app_id=ai.id and at.team_id=ti.id and ai.app_status = '1' and am.app_id=ai.id and am.need_online='1'
and concat(at.team_id,'|',at.app_id) in
-- 应用只属于更早创建的那个团队==============================================================
(select concat(min(at.team_id),'|',at.app_id) from app_mgt_app_team at group by at.app_id)
-- 应用只属于更早创建的那个团队==============================================================
) a
-- 团队和应用关系拼接==============================================================
on s.app_name=a.app_name

-- 核心逻辑==============================================================

) y


where
('${可选择机房-环境：=,|外高桥-产线|外高桥-灰度|优刻得-产线|宝山-产线|宝山-灾备|宝山-仿真|虹口-产线|虹口-测试|虹口-开发|UNDEF}' = '' and region_name='test')
or
('${可选择机房-环境：=,|外高桥-产线|外高桥-灰度|优刻得-产线|宝山-产线|宝山-灾备|宝山-仿真|虹口-产线|虹口-测试|虹口-开发|UNDEF}' <> '' and region_name = '${可选择机房-环境：=,|外高桥-产线|外高桥-灰度|优刻得-产线|宝山-产线|宝山-灾备|宝山-仿真|虹口-产线|虹口-测试|虹口-开发|UNDEF}')
-- 根据团队统计总数==============================================================
) n
on 1=1


-- 第二大类全部拼接完==============================================================
) yy


) o
-- ==============================================================统计CPU核数==============================================================



union all





-- ==============================================================统计内存容量MB==============================================================
select *, '内存容量GB' as mtype from
(




select counts, region_name, team, totals, stype
from
(
-- 第一大类全部拼接完==============================================================
select m.*,n.totals, '1' as stype from
(
-- 根据机房环境统计==============================================================
select sum(memory) as counts, region_name,'IT部门' as team
from
(
-- 核心逻辑==============================================================

SELECT
distinct
case when a.team_name is null then 'UNDEF' else a.team_name end as team_name,
case when a.app_name is null then 'UNDEF' else a.app_name end as app_name,
case when s.counts = 1 then (case when s.app_name is null then 'UNDEF' else s.app_name end) else s.memo end as real_app_name,
s.ip,
case when s.region_name is null then 'UNDEF' else concat(r.addr_name,'-',r.type_name) end as region_name
,round(s.memory/1024,0) as memory
FROM
(
-- 筛选符合要求的记录==============================================================
select concat(min(s.app_name),'等',cast(count(0) as char),'个应用') as memo,min(s.app_name) as app_name,count(0) as counts, s.ip, s.day, s.region_name
,s.memory
from spider.node_info_snapshot s
where s.day =
(
-- 不选择日期就默认从今天往前==============================================================
select max(s.day) from spider.node_info_snapshot s
where
('${可输入日期yyyyMMdd，如20200801：}' = '' and s.day <= CONVERT(date_format(now(), '%Y%m%d'),unsigned)
or
('${可输入日期yyyyMMdd，如20200801：}' <> '' and s.day <= '${可输入日期yyyyMMdd，如20200801：}'))
-- 不选择日期就默认从今天往前==============================================================
)
group by s.ip, s.day, s.region_name
,s.memory
-- 筛选符合要求的记录==============================================================
) s
-- 用来确定环境名称和是否使用==============================================================
LEFT OUTER JOIN
spider.env_mgt_region r
on r.region_name = s.region_name and r.region_is_active = '1'
-- 用来确定环境名称和是否使用==============================================================
LEFT OUTER JOIN
-- 团队和应用关系拼接==============================================================
(
SELECT distinct ti.team_name, am.module_name as app_name
from app_mgt_app_team at, spider.tool_mgt_team_info ti, app_mgt_app_info ai, spider.app_mgt_app_module am
where at.app_id=ai.id and at.team_id=ti.id and ai.app_status = '1' and am.app_id=ai.id and am.need_online='1'
and concat(at.team_id,'|',at.app_id) in
-- 应用只属于更早创建的那个团队==============================================================
(select concat(min(at.team_id),'|',at.app_id) from app_mgt_app_team at group by at.app_id)
-- 应用只属于更早创建的那个团队==============================================================
) a
-- 团队和应用关系拼接==============================================================
on s.app_name=a.app_name

-- 核心逻辑==============================================================
) x
group by region_name
-- 根据机房环境统计==============================================================
) m

left outer join

(
-- 总数统计==============================================================
select sum(memory) as totals
from
(
-- 核心逻辑==============================================================

SELECT
distinct
case when a.team_name is null then 'UNDEF' else a.team_name end as team_name,
case when a.app_name is null then 'UNDEF' else a.app_name end as app_name,
case when s.counts = 1 then (case when s.app_name is null then 'UNDEF' else s.app_name end) else s.memo end as real_app_name,
s.ip,
case when s.region_name is null then 'UNDEF' else concat(r.addr_name,'-',r.type_name) end as region_name
,round(s.memory/1024,0) as memory
FROM
(
-- 筛选符合要求的记录==============================================================
select concat(min(s.app_name),'等',cast(count(0) as char),'个应用') as memo,min(s.app_name) as app_name,count(0) as counts, s.ip, s.day, s.region_name
,s.memory
from spider.node_info_snapshot s
where s.day =
(
-- 不选择日期就默认从今天往前==============================================================
select max(s.day) from spider.node_info_snapshot s
where
('${可输入日期yyyyMMdd，如20200801：}' = '' and s.day <= CONVERT(date_format(now(), '%Y%m%d'),unsigned)
or
('${可输入日期yyyyMMdd，如20200801：}' <> '' and s.day <= '${可输入日期yyyyMMdd，如20200801：}'))
-- 不选择日期就默认从今天往前==============================================================
)
group by s.ip, s.day, s.region_name
,s.memory
-- 筛选符合要求的记录==============================================================
) s
-- 用来确定环境名称和是否使用==============================================================
LEFT OUTER JOIN
spider.env_mgt_region r
on r.region_name = s.region_name and r.region_is_active = '1'
-- 用来确定环境名称和是否使用==============================================================
LEFT OUTER JOIN
-- 团队和应用关系拼接==============================================================
(
SELECT distinct ti.team_name, am.module_name as app_name
from app_mgt_app_team at, spider.tool_mgt_team_info ti, app_mgt_app_info ai, spider.app_mgt_app_module am
where at.app_id=ai.id and at.team_id=ti.id and ai.app_status = '1' and am.app_id=ai.id and am.need_online='1'
and concat(at.team_id,'|',at.app_id) in
-- 应用只属于更早创建的那个团队==============================================================
(select concat(min(at.team_id),'|',at.app_id) from app_mgt_app_team at group by at.app_id)
-- 应用只属于更早创建的那个团队==============================================================
) a
-- 团队和应用关系拼接==============================================================
on s.app_name=a.app_name

-- 核心逻辑==============================================================
) x
-- 总数统计==============================================================
) n
on 1=1
-- 第一大类全部拼接完==============================================================
) xx



union all




select counts, region_name, team, totals, stype from
(

-- 第二大类全部拼接完==============================================================
select m.*,n.totals, '2' as stype from
(
-- 根据团队统计==============================================================
select sum(memory) as counts, '${可选择机房-环境：=,|外高桥-产线|外高桥-灰度|优刻得-产线|宝山-产线|宝山-灾备|宝山-仿真|虹口-产线|虹口-测试|虹口-开发|UNDEF}' as region_name, team_name as team
from
(
-- 核心逻辑==============================================================

SELECT
distinct
case when a.team_name is null then 'UNDEF' else a.team_name end as team_name,
case when a.app_name is null then 'UNDEF' else a.app_name end as app_name,
case when s.counts = 1 then (case when s.app_name is null then 'UNDEF' else s.app_name end) else s.memo end as real_app_name,
s.ip,
case when s.region_name is null then 'UNDEF' else concat(r.addr_name,'-',r.type_name) end as region_name
,round(s.memory/1024,0) as memory
FROM
(
-- 筛选符合要求的记录==============================================================
select concat(min(s.app_name),'等',cast(count(0) as char),'个应用') as memo,min(s.app_name) as app_name,count(0) as counts, s.ip, s.day, s.region_name
,s.memory
from spider.node_info_snapshot s
where s.day =
(
-- 不选择日期就默认从今天往前==============================================================
select max(s.day) from spider.node_info_snapshot s
where
('${可输入日期yyyyMMdd，如20200801：}' = '' and s.day <= CONVERT(date_format(now(), '%Y%m%d'),unsigned)
or
('${可输入日期yyyyMMdd，如20200801：}' <> '' and s.day <= '${可输入日期yyyyMMdd，如20200801：}'))
-- 不选择日期就默认从今天往前==============================================================
)
group by s.ip, s.day, s.region_name
,s.memory
-- 筛选符合要求的记录==============================================================
) s
-- 用来确定环境名称和是否使用==============================================================
INNER JOIN
spider.env_mgt_region r
on r.region_name = s.region_name and r.region_is_active = '1'
-- 用来确定环境名称和是否使用==============================================================
LEFT OUTER JOIN
-- 团队和应用关系拼接==============================================================
(
SELECT distinct ti.team_name, am.module_name as app_name
from app_mgt_app_team at, spider.tool_mgt_team_info ti, app_mgt_app_info ai, spider.app_mgt_app_module am
where at.app_id=ai.id and at.team_id=ti.id and ai.app_status = '1' and am.app_id=ai.id and am.need_online='1'
and concat(at.team_id,'|',at.app_id) in
-- 应用只属于更早创建的那个团队==============================================================
(select concat(min(at.team_id),'|',at.app_id) from app_mgt_app_team at group by at.app_id)
-- 应用只属于更早创建的那个团队==============================================================
) a
-- 团队和应用关系拼接==============================================================
on s.app_name=a.app_name

-- 核心逻辑==============================================================

) y


where
('${可选择机房-环境：=,|外高桥-产线|外高桥-灰度|优刻得-产线|宝山-产线|宝山-灾备|宝山-仿真|虹口-产线|虹口-测试|虹口-开发|UNDEF}' = '' and region_name='test')
or
('${可选择机房-环境：=,|外高桥-产线|外高桥-灰度|优刻得-产线|宝山-产线|宝山-灾备|宝山-仿真|虹口-产线|虹口-测试|虹口-开发|UNDEF}' <> '' and region_name = '${可选择机房-环境：=,|外高桥-产线|外高桥-灰度|优刻得-产线|宝山-产线|宝山-灾备|宝山-仿真|虹口-产线|虹口-测试|虹口-开发|UNDEF}')
group by team_name
-- 根据团队统计==============================================================
) m

left outer join


(
-- 根据团队统计总数==============================================================
select sum(memory) as totals
from
(
-- 核心逻辑==============================================================

SELECT
distinct
case when a.team_name is null then 'UNDEF' else a.team_name end as team_name,
case when a.app_name is null then 'UNDEF' else a.app_name end as app_name,
case when s.counts = 1 then (case when s.app_name is null then 'UNDEF' else s.app_name end) else s.memo end as real_app_name,
s.ip,
case when s.region_name is null then 'UNDEF' else concat(r.addr_name,'-',r.type_name) end as region_name
,round(s.memory/1024,0) as memory
FROM
(
-- 筛选符合要求的记录==============================================================
select concat(min(s.app_name),'等',cast(count(0) as char),'个应用') as memo,min(s.app_name) as app_name,count(0) as counts, s.ip, s.day, s.region_name
,s.memory
from spider.node_info_snapshot s
where s.day =
(
-- 不选择日期就默认从今天往前==============================================================
select max(s.day) from spider.node_info_snapshot s
where
('${可输入日期yyyyMMdd，如20200801：}' = '' and s.day <= CONVERT(date_format(now(), '%Y%m%d'),unsigned)
or
('${可输入日期yyyyMMdd，如20200801：}' <> '' and s.day <= '${可输入日期yyyyMMdd，如20200801：}'))
-- 不选择日期就默认从今天往前==============================================================
)
group by s.ip, s.day, s.region_name
,s.memory
-- 筛选符合要求的记录==============================================================
) s
-- 用来确定环境名称和是否使用==============================================================
INNER JOIN
spider.env_mgt_region r
on r.region_name = s.region_name and r.region_is_active = '1'
-- 用来确定环境名称和是否使用==============================================================
LEFT OUTER JOIN
-- 团队和应用关系拼接==============================================================
(
SELECT distinct ti.team_name, am.module_name as app_name
from app_mgt_app_team at, spider.tool_mgt_team_info ti, app_mgt_app_info ai, spider.app_mgt_app_module am
where at.app_id=ai.id and at.team_id=ti.id and ai.app_status = '1' and am.app_id=ai.id and am.need_online='1'
and concat(at.team_id,'|',at.app_id) in
-- 应用只属于更早创建的那个团队==============================================================
(select concat(min(at.team_id),'|',at.app_id) from app_mgt_app_team at group by at.app_id)
-- 应用只属于更早创建的那个团队==============================================================
) a
-- 团队和应用关系拼接==============================================================
on s.app_name=a.app_name

-- 核心逻辑==============================================================

) y


where
('${可选择机房-环境：=,|外高桥-产线|外高桥-灰度|优刻得-产线|宝山-产线|宝山-灾备|宝山-仿真|虹口-产线|虹口-测试|虹口-开发|UNDEF}' = '' and region_name='test')
or
('${可选择机房-环境：=,|外高桥-产线|外高桥-灰度|优刻得-产线|宝山-产线|宝山-灾备|宝山-仿真|虹口-产线|虹口-测试|虹口-开发|UNDEF}' <> '' and region_name = '${可选择机房-环境：=,|外高桥-产线|外高桥-灰度|优刻得-产线|宝山-产线|宝山-灾备|宝山-仿真|虹口-产线|虹口-测试|虹口-开发|UNDEF}')
-- 根据团队统计总数==============================================================
) n
on 1=1


-- 第二大类全部拼接完==============================================================
) yy


) o
-- ==============================================================统计内存容量MB==============================================================



) p


where
mtype = '${请选择统计维度：=,|节点个数|CPU核数|内存容量GB}'
and
(
('${可选择机房-环境：=,|外高桥-产线|外高桥-灰度|优刻得-产线|宝山-产线|宝山-灾备|宝山-仿真|虹口-产线|虹口-测试|虹口-开发|UNDEF}' = '' and stype = '1')
or
('${可选择机房-环境：=,|外高桥-产线|外高桥-灰度|优刻得-产线|宝山-产线|宝山-灾备|宝山-仿真|虹口-产线|虹口-测试|虹口-开发|UNDEF}' <> '' and stype = '2')
)

order by p.region_name,p.team