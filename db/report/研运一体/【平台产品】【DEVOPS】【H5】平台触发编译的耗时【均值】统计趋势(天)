%spider


select o.*,
p.elapsed, p.type
from
(
select y.day, y.operation, y.app,
(
select max(x.day) from
(
select distinct date_format(start_time, '%Y-%m-%d') as day, operation, app
from
(
-- ======================基础信息====================
select
distinct om.*,
r.package_type,
concat('（', r.package_type, '）', r.app_name) as app,
case when om.exec_type = 'compile' then '编译' else '打包' end as operation
from
spider.hm_optlog_main om

inner join
(
select distinct om.id, group_concat(distinct ab.module_name order  by ab.module_name) as app_name, ab.package_type
from
spider.hm_optlog_main om, spider.app_mgt_app_build ab
where ab.package_type in ('remote', 'dist', 'static')
and om.app_name REGEXP concat('[[:<:]]', ab.module_name , '[[:>:]]')
group by om.id, ab.package_type
) r
on om.id = r.id

where om.exec_type in ('remote', 'dist', 'static', 'compile')
and om.status = '${状态：=,|success|aborted|failed}'
-- ======================基础信息====================
) m
) x
where x.day <= y.day and x.operation = y.operation and x.app = y.app

) as c_day


from

-- ======================所有天====================
(
select distinct m.operation, m.app, n.day
from
(
-- ======================基础信息====================
select
distinct om.*,
r.package_type,
concat('（', r.package_type, '）', r.app_name) as app,
case when om.exec_type = 'compile' then '编译' else '打包' end as operation
from
spider.hm_optlog_main om

inner join
(
select distinct om.id, group_concat(distinct ab.module_name order  by ab.module_name) as app_name, ab.package_type
from
spider.hm_optlog_main om, spider.app_mgt_app_build ab
where ab.package_type in ('remote', 'dist', 'static')
and om.app_name REGEXP concat('[[:<:]]', ab.module_name , '[[:>:]]')
group by om.id, ab.package_type
) r
on om.id = r.id

where om.exec_type in ('remote', 'dist', 'static', 'compile')
and om.status = '${状态：=,|success|aborted|failed}'
-- ======================基础信息====================
) m
left outer join
(
select day from
(

select date_format(adddate( date_format(now(), '%Y-%m-%d'), -nums),  '%Y-%m-%d') as day from
(
select @num:=@num+1 as nums from spider.app_mgt_app_info,(select @num:=-1) n
) a

) b
where day >=
(
select min(date_format(s.start_time, '%Y-%m-%d')) from spider.hm_optlog_main s
)
order by day desc
limit 365
) n
on 1=1
)  y
-- ======================所有天====================

) o

inner join



(
select
operation,
app,
round(avg(elapsed)/60, 2) as elapsed,
'（分钟）' as type,
count(0) as total,
date_format(start_time, '%Y-%m-%d') as day
from

(
-- ======================基础信息====================
select
distinct om.*,
r.package_type,
concat('（', r.package_type, '）', r.app_name) as app,
case when om.exec_type = 'compile' then '编译' else '打包' end as operation
from
spider.hm_optlog_main om

inner join
(
select distinct om.id, group_concat(distinct ab.module_name order  by ab.module_name) as app_name, ab.package_type
from
spider.hm_optlog_main om, spider.app_mgt_app_build ab
where ab.package_type in ('remote', 'dist', 'static')
and om.app_name REGEXP concat('[[:<:]]', ab.module_name , '[[:>:]]')
group by om.id, ab.package_type
) r
on om.id = r.id

where om.exec_type in ('remote', 'dist', 'static', 'compile')
and om.status = '${状态：=,|success|aborted|failed}'
-- ======================基础信息====================
) a
group by operation, app, date_format(start_time, '%Y-%m-%d')
) p

on o.operation = p.operation and o.app = p.app and o.c_day = p.day



where o.operation = '${操作类型：=,|编译|打包}'

order by app, day

