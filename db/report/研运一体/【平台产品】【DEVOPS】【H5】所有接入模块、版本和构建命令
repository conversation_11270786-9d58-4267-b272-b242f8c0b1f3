%spider



select
distinct
concat(package_type, '【', team_alias, '】') as 类型和所属团队,
module_name as 应用名称,
build_cmd as 构建命令,
node_version as node版本,
ifnull(local_external_config, '没有外移环境变量') as 环境变量文件路径,
build_path as 具体构建路径

from
(
select
package_type,
module_name,
build_cmd,
node_version,
group_concat(distinct concat('{', rk, '：代码构建路径【', build_path, '】入口路由文件【', entrance_file, '】}') order by build_path) as build_path

from
(
select a.*,
case when @bp = module_name then @rank:=@rank+1 else @rank:=1 end as rk,
@bp:=module_name
from
(
SELECT
	hb.module_name, hb.build_cmd, hb.node_version, hb.build_path, hb.entrance_file,
	case when ab.package_type is not null then ab.package_type else '数据错误！！！' end as package_type
FROM
	spider.app_mgt_h5_build hb
left outer join 	spider.app_mgt_app_build ab
on hb.module_name = ab.module_name
order by package_type, module_name, build_path
) a, (select @rank:=1) b
) m
group by package_type, module_name, build_cmd, node_version
) x

-- 拼接所属团队============================================================================================================================
LEFT OUTER JOIN
(
select distinct am.module_name as app_name,
(
select group_concat(distinct ti.team_alias order by team_alias) as team_alias
from spider.app_mgt_app_team at, spider.tool_mgt_team_info ti
where at.app_id=ai.id and at.team_id=ti.id
group by am.module_name
order by team_alias
) as team_alias
from spider.app_mgt_app_info ai, spider.app_mgt_app_module am
where am.app_id=ai.id and am.need_online='1'
) a
on x.module_name = a.app_name
-- 拼接所属团队============================================================================================================================

left outer join
spider.app_mgt_h5_apply ha
on x.module_name = ha.app_name

where lower(x.module_name) like lower('%${可输入应用【关键词】：}%')
