%spider





select base.time, base.module_name, base.counts as total, succ.counts as success, base.counts - succ.counts as failed, concat(round(ifnull(succ.counts, 0)/base.counts*100, 2), '%') as success_ratio from

(

select ba.module_name, ba.time, count(0) as counts  from
(
select distinct pa.id as pa_id, am.module_name, pa.appName, pa.pipeline_id, sr.status, sr.id as sr_id, am.time
from spider.iter_mgt_publish_application pa, spider.task_mgt_service_results sr,

(
-- ======================================找出全部要统计的内容======================================
select ba.module_name, count(0) as time from
(
select distinct pa.id as pa_id, am.module_name, pa.appName, pa.pipeline_id
from spider.iter_mgt_publish_application pa,  spider.app_mgt_app_module am, spider.app_mgt_app_build ab
where pa.appName REGEXP concat('[[:<:]]', am.module_name, '[[:>:]]')
and pa.apply_at > '${根据年-月-日后查询：}' and pa.apply_at > '2020-10-01'
and am.app_id=ab.app_id and am.module_name=ab.module_name
and ab.package_type in ('jar','war')
) ba
group by ba.module_name
order by time desc limit 10
-- ======================================找出全部要统计的内容======================================
) am

where sr.script_params like concat('%"iteration_id": "', pa.pipeline_id, '"%')
and sr.script_params like concat('%"env": "', pa.env, '"%')
and pa.appName REGEXP concat('[[:<:]]', am.module_name, '[[:>:]]')
and pa.apply_at >= sr.start_at
and sr.business_name = 'publish_apply_check'
and pa.apply_at > '${根据年-月-日后查询：}' and sr.start_at > '${根据年-月-日后查询：}'
and pa.apply_at > '2020-10-01' and sr.start_at > '2020-10-01'
) ba
group by ba.module_name, ba.time
order by count(0) desc, ba.module_name limit 20

) base

left outer join
(
select ba.module_name, count(0) as counts  from
(

select distinct pa.id as pa_id, am.module_name, pa.appName, pa.pipeline_id, sr.status, sr.id as sr_id
from spider.iter_mgt_publish_application pa, spider.task_mgt_service_results sr,

(
-- ======================================找出全部要统计的内容======================================
select ba.module_name, count(0) as time from
(
select distinct pa.id as pa_id, am.module_name, pa.appName, pa.pipeline_id
from spider.iter_mgt_publish_application pa,  spider.app_mgt_app_module am, spider.app_mgt_app_build ab
where pa.appName REGEXP concat('[[:<:]]', am.module_name, '[[:>:]]')
and pa.apply_at > '${根据年-月-日后查询：}' and pa.apply_at > '2020-10-01'
and am.app_id=ab.app_id and am.module_name=ab.module_name
and ab.package_type in ('jar','war')
) ba
group by ba.module_name
order by time desc limit 10
-- ======================================找出全部要统计的内容======================================
) am

where sr.script_params like concat('%"iteration_id": "', pa.pipeline_id, '"%')
and sr.script_params like concat('%"env": "', pa.env, '"%')
and pa.appName REGEXP concat('[[:<:]]', am.module_name, '[[:>:]]')
and pa.apply_at >= sr.start_at
and sr.business_name = 'publish_apply_check'
and sr.status = 'success'
and pa.apply_at > '${根据年-月-日后查询：}' and sr.start_at > '${根据年-月-日后查询：}'
and pa.apply_at > '2020-10-01' and sr.start_at > '2020-10-01'

) ba
group by ba.module_name
) succ
on base.module_name = succ.module_name

order by time desc

