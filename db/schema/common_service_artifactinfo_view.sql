-- ----------------------------
-- 创建common_service_artifactinfo表的替代视图
-- zt@2020-04-03 第7版
-- ----------------------------
CREATE OR REPLACE view common_service_artifactinfo
as
select m.id,
m.module_name as appName,
IFNULL(m.container_name,CONCAT('----',b.package_type,'----')) as containerName,
b.package_name as packageName,
null as accessUrl,
IFNULL(m.module_jdk_version, a.app_jdk_version) as jdkVersion,
IFNULL(m.deploy_path,CONCAT('----',b.package_type,'----')) as deployPath,
CASE
	WHEN b.package_type = 'jar' and m.need_online = 1 THEN
		'dubbo'
	WHEN b.package_type = 'jar' and a.git_url is null and ac.need_online_count = 0 THEN
		'common_jar'
	ELSE
		b.package_type
END appType,
IFNULL(m.module_desc, a.app_cname) as appCnName,
m.app_port as appPort,
IF(a.svn_url is null, null, CONCAT(ss.svn_addr, su.svn_url, IFNULL(a.svn_path, ''), IFNULL(m.module_svn_path, CONCAT('/', m.module_name)))) as trunkPath,
m.lib_repo as gitRepo,
a.lib_location as featureTeam,
IF(a.git_url is null, null, CONCAT(a.git_url, a.git_path)) as gitCodePath,
m.create_path as createPath,
m.module_code as resourceCode,
null as devTeam
from ztst_app_module m
inner join ztst_app a on a.id = m.app_id
inner join ztst_app_build b on b.app_id = m.app_id and b.module_name = m.module_name
inner join (
	select a.id,
	sum(
	CASE 
		WHEN b.package_type = 'war' or b.package_type = 'tar'	THEN 1
		WHEN b.package_type = 'jar' and m.need_online = 1	THEN 1
		ELSE 0
	END)as need_online_count
	from ztst_app a
	inner join ztst_app_module m on m.app_id = a.id
	inner join ztst_app_build b on b.app_id = a.id and b.module_name = m.module_name
	group by a.id
)ac on ac.id = a.id
left join ztst_git_url gu on a.git_url = gu.git_url
left join ztst_git_server gs on gu.git_server_id = gs.id
left join ztst_svn_url su on a.svn_url = su.svn_name
left join ztst_svn_server ss on su.svn_server_id = ss.id
where m.module_name != 'howbuy-quartz'
order by m.id