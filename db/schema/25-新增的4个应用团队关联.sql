-- ----------------------------
-- 新增4条应用和团队关联关系
-- for伟敏
-- zt@2020-04-28
-- ----------------------------
insert into app_mgt_app_team(app_id, team_id, create_user, create_time, update_user, update_time, stamp) values (314, 3, 'huaitian.zhang', '2020-04-28 10:11:12', 'huaitian.zhang', '2020-04-28 10:11:12', 0);
insert into app_mgt_app_team(app_id, team_id, create_user, create_time, update_user, update_time, stamp) values (500, 3, 'huaitian.zhang', '2020-04-28 10:11:12', 'huaitian.zhang', '2020-04-28 10:11:12', 0);
insert into app_mgt_app_team(app_id, team_id, create_user, create_time, update_user, update_time, stamp) values (501, 3, 'huaitian.zhang', '2020-04-28 10:11:12', 'huaitian.zhang', '2020-04-28 10:11:12', 0);
insert into app_mgt_app_team(app_id, team_id, create_user, create_time, update_user, update_time, stamp) values (510, 7, 'huaitian.zhang', '2020-04-28 10:11:12', 'huaitian.zhang', '2020-04-28 10:11:12', 0);

-- ----------------------------
-- 应用上线问题解决
-- for伟杰
-- zt@2020-04-28
-- ----------------------------
update app_mgt_app_module set container_name = null, deploy_path = null where module_name = 'calc-offline-high';
update app_mgt_app_module set create_path = null, lib_repo = null where module_name = 'fpc-common';

-- ----------------------------
-- website_web修改类型为vue
-- for戈翔
-- zt@2020-04-28
-- ----------------------------
update app_mgt_app_build set package_type = 'vue' where module_name = 'website_web';

-- ----------------------------
-- 环境可用性修改
-- for戈翔
-- zt@2020-04-28
-- ----------------------------
update env_mgt_suite set suite_is_active = 0 where id = 47;
update env_mgt_suite set suite_is_active = 0 where id = 48;
update env_mgt_suite set suite_is_active = 0 where id = 49;
update env_mgt_suite set suite_is_active = 0 where id = 50;