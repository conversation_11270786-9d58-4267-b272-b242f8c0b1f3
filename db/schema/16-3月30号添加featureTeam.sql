-- ----------------------------
-- 应用表添加制品本地推送目录：ztst_app.lib_location
-- ----------------------------
alter table ztst_app add column lib_location VARCHAR(100) COMMENT '制品本地推送目录';

-- 视图修正
CREATE OR REPLACE view common_service_artifactinfo
as
select m.id,
m.module_name as appName,
IFNULL(m.container_name,CONCAT('----',b.package_type,'----')) as containerName,
b.package_name as packageName,
null as accessUrl,
IFNULL(m.module_jdk_version, a.app_jdk_version) as jdkVersion,
IFNULL(m.deploy_path,CONCAT('----',b.package_type,'----')) as deployPath,
CASE
	WHEN m.need_online = 1 THEN
		'dubbo'
	WHEN b.package_type = 'jar' and ac.need_online_count = 0 THEN
		'common_jar'
	ELSE
		b.package_type
END appType,
IFNULL(m.module_desc, a.app_cname) as appCnName,
m.app_port as appPort,
IF(a.svn_url is null, null, CONCAT(ss.svn_addr, su.svn_url, IFNULL(a.svn_path, ''), IFNULL(m.module_svn_path, CONCAT('/', m.module_name)))) as trunkPath,
m.lib_repo as gitRepo,
a.lib_location as featureTeam,
IF(a.git_url is null, null, CONCAT(a.git_url, a.git_path)) as gitCodePath,
m.create_path as createPath,
m.module_code as resourceCode,
null as devTeam
from ztst_app_module m
inner join ztst_app a on a.id = m.app_id
inner join ztst_app_build b on b.app_id = m.app_id and b.module_name = m.module_name
inner join (
	select a.id,
	sum(
	CASE 
		WHEN b.package_type = 'war' or b.package_type = 'tar'	THEN 1
		WHEN b.package_type = 'jar' and m.need_online = 1	THEN 1
		ELSE 0
	END)as need_online_count
	from ztst_app a
	inner join ztst_app_module m on m.app_id = a.id
	inner join ztst_app_build b on b.app_id = a.id and b.module_name = m.module_name
	group by a.id
)ac on ac.id = a.id
left join ztst_git_url gu on a.git_url = gu.git_url
left join ztst_git_server gs on gu.git_server_id = gs.id
left join ztst_svn_url su on a.svn_url = su.svn_name
left join ztst_svn_server ss on su.svn_server_id = ss.id
order by m.id

-- 工具sql
select distinct
a.id,a.app_name,
bak.featureTeam,
CONCAT('update ztst_app set lib_location=\'',bak.featureTeam,'\' where id=', a.id, ';') as sql_str
from ztst_app a
inner join ztst_app_module m on m.app_id = a.id
inner join ztst_app_build b on b.app_id = m.app_id and b.module_name = m.module_name
inner join common_service_artifactinfo_bak0329 bak on bak.appName = m.module_name
where bak.featureTeam is not null and TRIM(bak.featureTeam) != '' and featureTeam != '(\'null\', \'unknown\')'
order by a.id

-- 需要更新
update ztst_app set lib_location='crm' where id=8;
update ztst_app set lib_location='crm' where id=9;
update ztst_app set lib_location='crm' where id=10;
update ztst_app set lib_location='crm' where id=13;
update ztst_app set lib_location='crm' where id=14;
update ztst_app set lib_location='crm' where id=15;
update ztst_app set lib_location='crm' where id=16;
update ztst_app set lib_location='crm' where id=17;
update ztst_app set lib_location='crm' where id=18;
update ztst_app set lib_location='crm' where id=19;
update ztst_app set lib_location='tp' where id=20;
update ztst_app set lib_location='tp' where id=21;
update ztst_app set lib_location='tp' where id=22;
update ztst_app set lib_location='tp' where id=23;
update ztst_app set lib_location='tp' where id=24;
update ztst_app set lib_location='tp' where id=25;
update ztst_app set lib_location='tp' where id=26;
update ztst_app set lib_location='tp' where id=27;
update ztst_app set lib_location='tp' where id=28;
update ztst_app set lib_location='tp' where id=29;
update ztst_app set lib_location='tp' where id=30;
update ztst_app set lib_location='tp' where id=31;
update ztst_app set lib_location='tp' where id=32;
update ztst_app set lib_location='tp' where id=33;
update ztst_app set lib_location='tp' where id=34;
update ztst_app set lib_location='tp' where id=35;
update ztst_app set lib_location='tp' where id=36;
update ztst_app set lib_location='tp' where id=37;
update ztst_app set lib_location='tp' where id=38;
update ztst_app set lib_location='tp' where id=39;
update ztst_app set lib_location='tp' where id=40;
update ztst_app set lib_location='tp' where id=41;
update ztst_app set lib_location='tp' where id=42;
update ztst_app set lib_location='tp' where id=43;
update ztst_app set lib_location='tp' where id=44;
update ztst_app set lib_location='tp' where id=45;
update ztst_app set lib_location='tp' where id=46;
update ztst_app set lib_location='tp' where id=47;
update ztst_app set lib_location='tp' where id=48;
update ztst_app set lib_location='tp' where id=49;
update ztst_app set lib_location='tp' where id=50;
update ztst_app set lib_location='tp' where id=51;
update ztst_app set lib_location='tms' where id=52;
update ztst_app set lib_location='tp' where id=53;
update ztst_app set lib_location='tp' where id=54;
update ztst_app set lib_location='tp' where id=55;
update ztst_app set lib_location='tp' where id=56;
update ztst_app set lib_location='tp' where id=57;
update ztst_app set lib_location='tp' where id=58;
update ztst_app set lib_location='tp' where id=60;
update ztst_app set lib_location='otc' where id=61;
update ztst_app set lib_location='cc' where id=62;
update ztst_app set lib_location='cc' where id=63;
update ztst_app set lib_location='cc' where id=64;
update ztst_app set lib_location='cc' where id=65;
update ztst_app set lib_location='cc' where id=66;
update ztst_app set lib_location='cc' where id=67;
update ztst_app set lib_location='cc' where id=68;
update ztst_app set lib_location='cc' where id=69;
update ztst_app set lib_location='cc' where id=70;
update ztst_app set lib_location='web' where id=71;
update ztst_app set lib_location='web' where id=72;
update ztst_app set lib_location='web' where id=74;
update ztst_app set lib_location='fpc' where id=75;
update ztst_app set lib_location='cc' where id=76;
update ztst_app set lib_location='cc' where id=77;
update ztst_app set lib_location='cc' where id=78;
update ztst_app set lib_location='tp' where id=79;
update ztst_app set lib_location='cc' where id=80;
update ztst_app set lib_location='cc' where id=81;
update ztst_app set lib_location='cc' where id=83;
update ztst_app set lib_location='cc' where id=84;
update ztst_app set lib_location='cc' where id=85;
update ztst_app set lib_location='cc' where id=87;
update ztst_app set lib_location='tms' where id=92;
update ztst_app set lib_location='tms' where id=93;
update ztst_app set lib_location='web' where id=94;
update ztst_app set lib_location='tms' where id=94;
update ztst_app set lib_location='tms' where id=95;
update ztst_app set lib_location='tms' where id=98;
update ztst_app set lib_location='web' where id=99;
update ztst_app set lib_location='tms' where id=99;
update ztst_app set lib_location='tms' where id=100;
update ztst_app set lib_location='tms' where id=101;
update ztst_app set lib_location='tms' where id=102;
update ztst_app set lib_location='tms' where id=103;
update ztst_app set lib_location='tms' where id=104;
update ztst_app set lib_location='tms' where id=105;
update ztst_app set lib_location='tms' where id=106;
update ztst_app set lib_location='tms' where id=107;
update ztst_app set lib_location='web' where id=108;
update ztst_app set lib_location='web' where id=109;
update ztst_app set lib_location='tms' where id=112;
update ztst_app set lib_location='tms' where id=113;
update ztst_app set lib_location='tms' where id=114;
update ztst_app set lib_location='tms' where id=115;
update ztst_app set lib_location='tms' where id=116;
update ztst_app set lib_location='tms' where id=117;
update ztst_app set lib_location='tms' where id=118;
update ztst_app set lib_location='tms' where id=119;
update ztst_app set lib_location='tms' where id=120;
update ztst_app set lib_location='tms' where id=121;
update ztst_app set lib_location='tms' where id=123;
update ztst_app set lib_location='coop' where id=124;
update ztst_app set lib_location='web' where id=126;
update ztst_app set lib_location='fpc' where id=127;
update ztst_app set lib_location='web' where id=144;
update ztst_app set lib_location='fpc' where id=147;
update ztst_app set lib_location='fpc' where id=150;
update ztst_app set lib_location='fpc' where id=151;
update ztst_app set lib_location='fpc' where id=152;
update ztst_app set lib_location='crm' where id=156;
update ztst_app set lib_location='fpc' where id=157;
update ztst_app set lib_location='web' where id=158;
update ztst_app set lib_location='fpc' where id=159;
update ztst_app set lib_location='web' where id=161;
update ztst_app set lib_location='fpc' where id=162;
update ztst_app set lib_location='FPS' where id=163;
update ztst_app set lib_location='FPS' where id=164;
update ztst_app set lib_location='tms' where id=165;
update ztst_app set lib_location='web' where id=166;
update ztst_app set lib_location='web' where id=167;
update ztst_app set lib_location='web' where id=168;
update ztst_app set lib_location='web' where id=169;
update ztst_app set lib_location='web' where id=170;
update ztst_app set lib_location='web' where id=172;
update ztst_app set lib_location='web' where id=173;
update ztst_app set lib_location='fpc' where id=174;
update ztst_app set lib_location='web' where id=175;
update ztst_app set lib_location='ds' where id=176;
update ztst_app set lib_location='ds' where id=177;
update ztst_app set lib_location='fpc' where id=194;
update ztst_app set lib_location='web' where id=195;
update ztst_app set lib_location='web' where id=196;
update ztst_app set lib_location='tms' where id=197;
update ztst_app set lib_location='web' where id=198;
update ztst_app set lib_location='fpc' where id=201;
update ztst_app set lib_location='fpc' where id=202;
update ztst_app set lib_location='crm' where id=203;
update ztst_app set lib_location='fpc' where id=205;
update ztst_app set lib_location='fpc' where id=207;
update ztst_app set lib_location='web' where id=208;
update ztst_app set lib_location='web' where id=209;
update ztst_app set lib_location='fpc' where id=210;
update ztst_app set lib_location='ds' where id=211;
update ztst_app set lib_location='otc' where id=220;
update ztst_app set lib_location='otc' where id=221;
update ztst_app set lib_location='otc' where id=222;
update ztst_app set lib_location='otc' where id=223;
update ztst_app set lib_location='otc' where id=224;
update ztst_app set lib_location='otc' where id=225;
update ztst_app set lib_location='otc' where id=226;
update ztst_app set lib_location='otc' where id=227;
update ztst_app set lib_location='otc' where id=228;
update ztst_app set lib_location='tp' where id=229;
update ztst_app set lib_location='tp' where id=230;
update ztst_app set lib_location='crm' where id=235;
update ztst_app set lib_location='scheduler' where id=244;
update ztst_app set lib_location='scheduler' where id=246;
update ztst_app set lib_location='scheduler' where id=247;
update ztst_app set lib_location='mofang' where id=256;
update ztst_app set lib_location='mofang' where id=257;
update ztst_app set lib_location='coop' where id=260;
update ztst_app set lib_location='mojie' where id=260;
update ztst_app set lib_location='coop' where id=261;
update ztst_app set lib_location='mojie' where id=262;
update ztst_app set lib_location='mojie' where id=263;
update ztst_app set lib_location='mojie' where id=264;
update ztst_app set lib_location='mojie' where id=265;
update ztst_app set lib_location='mojie' where id=266;
update ztst_app set lib_location='mojie' where id=267;
update ztst_app set lib_location='crm' where id=277;
update ztst_app set lib_location='fpc' where id=278;
update ztst_app set lib_location='fpc' where id=280;
update ztst_app set lib_location='crm' where id=281;
update ztst_app set lib_location='fpc' where id=282;
update ztst_app set lib_location='fpc' where id=283;
update ztst_app set lib_location='crm' where id=284;
update ztst_app set lib_location='tms' where id=285;
update ztst_app set lib_location='ds' where id=287;
update ztst_app set lib_location='web' where id=288;
update ztst_app set lib_location='tp' where id=291;
update ztst_app set lib_location='tp' where id=293;
update ztst_app set lib_location='crm' where id=296;
update ztst_app set lib_location='tp' where id=297;
update ztst_app set lib_location='fpc' where id=298;
update ztst_app set lib_location='fpc' where id=299;
update ztst_app set lib_location='fpc' where id=300;
update ztst_app set lib_location='mojie' where id=301;
update ztst_app set lib_location='devops' where id=302;
update ztst_app set lib_location='devops' where id=303;
update ztst_app set lib_location='mojie' where id=304;
update ztst_app set lib_location='webtest' where id=304;
update ztst_app set lib_location='webtest' where id=305;
update ztst_app set lib_location='middleware' where id=306;
update ztst_app set lib_location='middleware' where id=307;
update ztst_app set lib_location='web' where id=308;
update ztst_app set lib_location='fpc' where id=311;
update ztst_app set lib_location='fpc' where id=313;
update ztst_app set lib_location='tms' where id=314;
update ztst_app set lib_location='tp' where id=325;
update ztst_app set lib_location='mtx' where id=339;
update ztst_app set lib_location='tp' where id=400;
update ztst_app set lib_location='tp' where id=402;
update ztst_app set lib_location='tp' where id=403;
update ztst_app set lib_location='crm' where id=404;
update ztst_app set lib_location='cc' where id=405;
update ztst_app set lib_location='cc' where id=406;
update ztst_app set lib_location='tp' where id=408;
update ztst_app set lib_location='tp' where id=409;
update ztst_app set lib_location='tp' where id=410;
update ztst_app set lib_location='crm' where id=412;
update ztst_app set lib_location='crm' where id=414;
update ztst_app set lib_location='tp' where id=416;
update ztst_app set lib_location='tp' where id=417;
update ztst_app set lib_location='tp' where id=418;
update ztst_app set lib_location='tp' where id=419;
update ztst_app set lib_location='fpc' where id=420;
update ztst_app set lib_location='tp' where id=421;
update ztst_app set lib_location='tp' where id=423;
update ztst_app set lib_location='web' where id=426;
update ztst_app set lib_location='web' where id=430;
update ztst_app set lib_location='tms' where id=432;
update ztst_app set lib_location='tms' where id=433;
update ztst_app set lib_location='tms' where id=434;
update ztst_app set lib_location='tms' where id=435;
update ztst_app set lib_location='tp' where id=437;
update ztst_app set lib_location='tms' where id=438;
update ztst_app set lib_location='tms' where id=439;
update ztst_app set lib_location='mofang' where id=440;
update ztst_app set lib_location='pa' where id=440;
update ztst_app set lib_location='ds' where id=444;
update ztst_app set lib_location='crm' where id=445;
update ztst_app set lib_location='crm' where id=447;
update ztst_app set lib_location='mofang' where id=449;
update ztst_app set lib_location='ds' where id=452;
update ztst_app set lib_location='otc' where id=453;
update ztst_app set lib_location='otc' where id=454;
update ztst_app set lib_location='otc' where id=455;
update ztst_app set lib_location='otc' where id=456;
update ztst_app set lib_location='otc' where id=457;
update ztst_app set lib_location='otc' where id=459;
update ztst_app set lib_location='crm' where id=460;
update ztst_app set lib_location='crm' where id=461;
update ztst_app set lib_location='tp' where id=462;
update ztst_app set lib_location='scheduler' where id=467;
update ztst_app set lib_location='fpc' where id=469;
update ztst_app set lib_location='tp' where id=470;
update ztst_app set lib_location='tms' where id=473;
update ztst_app set lib_location='tms' where id=474;
update ztst_app set lib_location='tms-common' where id=475;
update ztst_app set lib_location='tms' where id=476;
update ztst_app set lib_location='tms' where id=477;
update ztst_app set lib_location='tp' where id=478;
update ztst_app set lib_location='crm' where id=487;
update ztst_app set lib_location='asset' where id=500;
update ztst_app set lib_location='asset' where id=501;
update ztst_app set lib_location='fpc' where id=502;