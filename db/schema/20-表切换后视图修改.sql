-- ----------------------------
-- 表修改后对比视图修改
-- zt@2020-04-16
-- ----------------------------
CREATE OR REPLACE view ztst_artifact_comp_view
AS
select o.appName as module_name,
if(o.id is null or trim(o.id) = '' or IFNULL(o.id,'NVL') = IFNULL(n.id,'NVL'), null, CONCAT(IFNULL(o.id,'NVL'),' -> ',IFNULL(n.id,'NVL'))) as id,
if(o.appName is null or trim(o.appName) = '' or IFNULL(o.appName,'NVL') = IFNULL(n.appName,'NVL'), null, CONCAT(IFNULL(o.appName,'NVL'),' -> ',IFNULL(n.appName,'NVL'))) as appName,
if(o.containerName is null or trim(o.containerName) = '' or o.containerName = 'temp' or IFNULL(o.containerName,'NVL') = IFNULL(n.containerName,'NVL'), null, CONCAT(IFNULL(o.containerName,'NVL'),' -> ',IFNULL(n.containerName,'NVL'))) as containerName,
if(o.packageName is null or trim(o.packageName) = '' or IFNULL(o.packageName,'NVL') = IFNULL(n.packageName,'NVL'), null, CONCAT(IFNULL(o.packageName,'NVL'),' -> ',IFNULL(n.packageName,'NVL'))) as packageName,
if(o.accessUrl is null or trim(o.accessUrl) = '' or o.accessUrl = 'null' or IFNULL(o.accessUrl,'NVL') = IFNULL(n.accessUrl,'NVL'), null, CONCAT(IFNULL(o.accessUrl,'NVL'),' -> ',IFNULL(n.accessUrl,'NVL'))) as accessUrl,
if(o.jdkVersion is null or trim(o.jdkVersion) = '' or o.jdkVersion = 'null' or IFNULL(o.jdkVersion,'NVL') = IFNULL(n.jdkVersion,'NVL'), null, CONCAT(IFNULL(o.jdkVersion,'NVL'),' -> ',IFNULL(n.jdkVersion,'NVL'))) as jdkVersion,
if(o.deployPath is null or trim(o.deployPath) = '' or o.deployPath = 'null' or IFNULL(o.deployPath,'NVL') = IFNULL(n.deployPath,'NVL'), null, CONCAT(IFNULL(o.deployPath,'NVL'),' -> ',IFNULL(n.deployPath,'NVL'))) as deployPath,
if(o.appType is null or trim(o.appType) = '' or IFNULL(o.appType,'NVL') = IFNULL(n.appType,'NVL'), null, CONCAT(IFNULL(o.appType,'NVL'),' -> ',IFNULL(n.appType,'NVL'))) as appType,
if(o.appCnName is null or trim(o.appCnName) = '' or o.appCnName = 'null' or IFNULL(o.appCnName,'NVL') = IFNULL(n.appCnName,'NVL'), null, CONCAT(IFNULL(o.appCnName,'NVL'),' -> ',IFNULL(n.appCnName,'NVL'))) as appCnName,
if(o.appPort is null or o.appPort = 0 or IFNULL(o.appPort,'NVL') = IFNULL(n.appPort,'NVL'), null, CONCAT(IFNULL(o.appPort,'NVL'),' -> ',IFNULL(n.appPort,'NVL'))) as appPort,
if(o.trunkPath is null or trim(o.trunkPath) = '' 
	or INSTR(replace(o.trunkPath, '192.168.220.100', 'svn.howbuy.test'), IFNULL(n.trunkPath,'NVL')) = 1 
	or INSTR(IFNULL(n.trunkPath,'NVL'), replace(o.trunkPath, '192.168.220.100', 'svn.howbuy.test')) = 1, null, CONCAT(IFNULL(o.trunkPath,'NVL'),' -> ',IFNULL(n.trunkPath,'NVL'))) as trunkPath,
if(o.gitRepo is null or trim(o.gitRepo) = '' or IFNULL(o.gitRepo,'NVL') = IFNULL(n.gitRepo,'NVL'), null, CONCAT(IFNULL(o.gitRepo,'NVL'),' -> ',IFNULL(n.gitRepo,'NVL'))) as gitRepo,
if(o.featureTeam is null or trim(o.featureTeam) = '' or IFNULL(o.featureTeam,'NVL') = IFNULL(n.featureTeam,'NVL'), null, CONCAT(IFNULL(o.featureTeam,'NVL'),' -> ',IFNULL(n.featureTeam,'NVL'))) as featureTeam,
if(o.gitCodePath is null or trim(o.gitCodePath) = '' or IFNULL(o.gitCodePath,'NVL') = IFNULL(n.gitCodePath,'NVL'), null, CONCAT(IFNULL(o.gitCodePath,'NVL'),' -> ',IFNULL(n.gitCodePath,'NVL'))) as gitCodePath,
if(o.createPath is null or trim(o.createPath) = '' or IFNULL(o.createPath,'NVL') = IFNULL(n.createPath,'NVL'), null, CONCAT(IFNULL(o.createPath,'NVL'),' -> ',IFNULL(n.createPath,'NVL'))) as createPath,
if(o.resourceCode is null or trim(o.resourceCode) = '' or IFNULL(o.resourceCode,'NVL') = IFNULL(n.resourceCode,'NVL'), null, CONCAT(IFNULL(o.resourceCode,'NVL'),' -> ',IFNULL(n.resourceCode,'NVL'))) as resourceCode,
if(o.devTeam is null or trim(o.devTeam) = '' or IFNULL(o.devTeam,'NVL') = IFNULL(n.devTeam,'NVL'), null, CONCAT(IFNULL(o.devTeam,'NVL'),' -> ',IFNULL(n.devTeam,'NVL'))) as devTeam
from common_service_artifactinfo_bak0401 o
left join ztst_artifact_view n on n.appName = o.appName
order by o.appName