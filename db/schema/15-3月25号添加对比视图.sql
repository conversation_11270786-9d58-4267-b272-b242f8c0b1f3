-- ----------------------------
-- artifact对应视图
-- zt@2020-03-25
-- ----------------------------
CREATE OR REPLACE view ztst_artifact_view
as
select m.id,
m.module_name as appName,
m.container_name as containerName,
b.package_name as packageName,
null as accessUrl,
IFNULL(m.module_jdk_version, a.app_jdk_version) as jdkVersion,
m.deploy_path as deployPath,
if(m.need_online = 1, 'dubbo', b.package_type) as appType,
IFNULL(m.module_desc, a.app_cname) as appCnName,
m.app_port as appPort,
IF(a.svn_url is null, null, CONCAT(ss.svn_addr, su.svn_url, IFNULL(a.svn_path, ''), IFNULL(m.module_svn_path, CONCAT('/', m.module_name)))) as trunkPath,
m.lib_repo as gitRepo,
null as featureTeam,
IF(a.git_url is null, null, CONCAT(a.git_url, a.git_path)) as gitCodePath,
m.create_path as createPath,
m.module_code as resourceCode,
null as devTeam
from ztst_app_module m
inner join ztst_app a on a.id = m.app_id
inner join ztst_app_build b on b.app_id = m.app_id and b.module_name = m.module_name
left join ztst_git_url gu on a.git_url = gu.git_url
left join ztst_git_server gs on gu.git_server_id = gs.id
left join ztst_svn_url su on a.svn_url = su.svn_name
left join ztst_svn_server ss on su.svn_server_id = ss.id
where 1=1
order by m.id

-- 填充后
CREATE OR REPLACE view ztst_artifact_view
as
select m.id,
m.module_name as appName,
IFNULL(m.container_name,CONCAT('----',if(m.need_online = 1, 'dubbo', b.package_type),'----')) as containerName,
b.package_name as packageName,
null as accessUrl,
IFNULL(m.module_jdk_version, a.app_jdk_version) as jdkVersion,
IFNULL(m.deploy_path,CONCAT('----',if(m.need_online = 1, 'dubbo', b.package_type),'----')) as deployPath,
if(m.need_online = 1, 'dubbo', b.package_type) as appType,
IFNULL(m.module_desc, a.app_cname) as appCnName,
m.app_port as appPort,
IF(a.svn_url is null, null, CONCAT(ss.svn_addr, su.svn_url, IFNULL(a.svn_path, ''), IFNULL(m.module_svn_path, CONCAT('/', m.module_name)))) as trunkPath,
m.lib_repo as gitRepo,
null as featureTeam,
IF(a.git_url is null, null, CONCAT(a.git_url, a.git_path)) as gitCodePath,
m.create_path as createPath,
m.module_code as resourceCode,
null as devTeam
from ztst_app_module m
inner join ztst_app a on a.id = m.app_id
inner join ztst_app_build b on b.app_id = m.app_id and b.module_name = m.module_name
left join ztst_git_url gu on a.git_url = gu.git_url
left join ztst_git_server gs on gu.git_server_id = gs.id
left join ztst_svn_url su on a.svn_url = su.svn_name
left join ztst_svn_server ss on su.svn_server_id = ss.id
where 1=1
order by m.id

-- 第3版
CREATE OR REPLACE view ztst_artifact_view
as
select m.id,
m.module_name as appName,
IFNULL(m.container_name,CONCAT('----',b.package_type,'----')) as containerName,
b.package_name as packageName,
null as accessUrl,
IFNULL(m.module_jdk_version, a.app_jdk_version) as jdkVersion,
IFNULL(m.deploy_path,CONCAT('----',b.package_type,'----')) as deployPath,
CASE
	WHEN m.need_online = 1 THEN
		'dubbo'
	WHEN b.package_type = 'jar' and ac.need_online_count = 0 THEN
		'common_jar'
	ELSE
		b.package_type
END appType,
IFNULL(m.module_desc, a.app_cname) as appCnName,
m.app_port as appPort,
IF(a.svn_url is null, null, CONCAT(ss.svn_addr, su.svn_url, IFNULL(a.svn_path, ''), IFNULL(m.module_svn_path, CONCAT('/', m.module_name)))) as trunkPath,
m.lib_repo as gitRepo,
null as featureTeam,
IF(a.git_url is null, null, CONCAT(a.git_url, a.git_path)) as gitCodePath,
m.create_path as createPath,
m.module_code as resourceCode,
null as devTeam
from ztst_app_module m
inner join ztst_app a on a.id = m.app_id
inner join ztst_app_build b on b.app_id = m.app_id and b.module_name = m.module_name
inner join (
	select a.id,
	sum(
	CASE 
		WHEN b.package_type = 'war' or b.package_type = 'tar'	THEN 1
		WHEN b.package_type = 'jar' and m.need_online = 1	THEN 1
		ELSE 0
	END)as need_online_count
	from ztst_app a
	inner join ztst_app_module m on m.app_id = a.id
	inner join ztst_app_build b on b.app_id = a.id and b.module_name = m.module_name
	group by a.id
)ac on ac.id = a.id
left join ztst_git_url gu on a.git_url = gu.git_url
left join ztst_git_server gs on gu.git_server_id = gs.id
left join ztst_svn_url su on a.svn_url = su.svn_name
left join ztst_svn_server ss on su.svn_server_id = ss.id
order by m.id


-- ----------------------------
-- artifact纵向对比视图
-- zt@2020-03-25
-- ----------------------------
CREATE OR REPLACE view ztst_artifact_diff_view
AS
select v.* from(
	select CONCAT('old',': ',o.appName) as diff,
	o.id, o.appName, o.containerName, o.packageName, o.accessUrl, o.jdkVersion, o.deployPath, o.appType, o.appCnName, o. appPort, o.trunkPath,
	o.gitRepo, o.featureTeam, o.gitCodePath, o.createPath, o.resourceCode, o.devTeam
	from common_service_artifactinfo o
	UNION ALL
	select CONCAT('new',': ',n.appName) as diff,
	n.id, n.appName, n.containerName, n.packageName, n.accessUrl, n.jdkVersion, n.deployPath, n.appType, n.appCnName, n. appPort, n.trunkPath,
	n.gitRepo, n.featureTeam, n.gitCodePath, n.createPath, n.resourceCode, n.devTeam
	from ztst_artifact_view n
)v order by v.appName

-- ----------------------------
-- artifact横向对比视图
-- zt@2020-03-25
-- ----------------------------
CREATE OR REPLACE view ztst_artifact_comp_view
AS
select o.appName as module_name,
if(IF(o.id is null or trim(o.id) = '','NVL', o.id) = IFNULL(n.id,'NVL'), null, CONCAT(IFNULL(o.id,'NVL'),' -> ',IFNULL(n.id,'NVL'))) as id,
if(IF(o.appName is null or trim(o.appName) = '','NVL', o.appName) = IFNULL(n.appName,'NVL'), null, CONCAT(IFNULL(o.appName,'NVL'),' -> ',IFNULL(n.appName,'NVL'))) as appName,
if(IF(o.containerName is null or trim(o.containerName) = '','NVL', o.containerName) = IFNULL(n.containerName,'NVL'), null, CONCAT(IFNULL(o.containerName,'NVL'),' -> ',IFNULL(n.containerName,'NVL'))) as containerName,
if(IF(o.packageName is null or trim(o.packageName) = '','NVL', o.packageName) = IFNULL(n.packageName,'NVL'), null, CONCAT(IFNULL(o.packageName,'NVL'),' -> ',IFNULL(n.packageName,'NVL'))) as packageName,
if(IF(o.accessUrl is null or trim(o.accessUrl) = '' or o.accessUrl = 'null','NVL', o.accessUrl) = IFNULL(n.accessUrl,'NVL'), null, CONCAT(IFNULL(o.accessUrl,'NVL'),' -> ',IFNULL(n.accessUrl,'NVL'))) as accessUrl,
if(IF(o.jdkVersion is null or trim(o.jdkVersion) = '' or o.jdkVersion = 'null','NVL', o.jdkVersion) = IFNULL(n.jdkVersion,'NVL'), null, CONCAT(IFNULL(o.jdkVersion,'NVL'),' -> ',IFNULL(n.jdkVersion,'NVL'))) as jdkVersion,
if(IF(o.deployPath is null or trim(o.deployPath) = '' or o.deployPath = 'null','NVL', o.deployPath) = IFNULL(n.deployPath,'NVL'), null, CONCAT(IFNULL(o.deployPath,'NVL'),' -> ',IFNULL(n.deployPath,'NVL'))) as deployPath,
if(IF(o.appType is null or trim(o.appType) = '','NVL', o.appType) = IFNULL(n.appType,'NVL'), null, CONCAT(IFNULL(o.appType,'NVL'),' -> ',IFNULL(n.appType,'NVL'))) as appType,
if(IF(o.appCnName is null or trim(o.appCnName) = '','NVL', o.appCnName) = IFNULL(n.appCnName,'NVL'), null, CONCAT(IFNULL(o.appCnName,'NVL'),' -> ',IFNULL(n.appCnName,'NVL'))) as appCnName,
if(IF(o.appPort is null or trim(o.appPort) = '' or o.appPort = 0,'NVL', o.appPort) = IFNULL(n.appPort,'NVL'), null, CONCAT(IFNULL(o.appPort,'NVL'),' -> ',IFNULL(n.appPort,'NVL'))) as appPort,
if(INSTR(replace(IF(o.trunkPath is null or trim(o.trunkPath) = '','NVL', o.trunkPath), '192.168.220.100', 'svn.howbuy.test'), IFNULL(n.trunkPath,'NVL')) = 1 
	or INSTR(IFNULL(n.trunkPath,'NVL'), replace(IF(o.trunkPath is null or trim(o.trunkPath) = '','NVL', o.trunkPath), '192.168.220.100', 'svn.howbuy.test')) = 1, null, CONCAT(IFNULL(o.trunkPath,'NVL'),' -> ',IFNULL(n.trunkPath,'NVL'))) as trunkPath,
if(IF(o.gitRepo is null or trim(o.gitRepo) = '','NVL', o.gitRepo) = IFNULL(n.gitRepo,'NVL'), null, CONCAT(IFNULL(o.gitRepo,'NVL'),' -> ',IFNULL(n.gitRepo,'NVL'))) as gitRepo,
if(IF(o.featureTeam is null or trim(o.featureTeam) = '','NVL', o.featureTeam) = IFNULL(n.featureTeam,'NVL'), null, CONCAT(IFNULL(o.featureTeam,'NVL'),' -> ',IFNULL(n.featureTeam,'NVL'))) as featureTeam,
if(IF(o.gitCodePath is null or trim(o.gitCodePath) = '','NVL', o.gitCodePath) = IFNULL(n.gitCodePath,'NVL'), null, CONCAT(IFNULL(o.gitCodePath,'NVL'),' -> ',IFNULL(n.gitCodePath,'NVL'))) as gitCodePath,
if(IF(o.createPath is null or trim(o.createPath) = '','NVL', o.createPath) = IFNULL(n.createPath,'NVL'), null, CONCAT(IFNULL(o.createPath,'NVL'),' -> ',IFNULL(n.createPath,'NVL'))) as createPath,
if(IF(o.resourceCode is null or trim(o.resourceCode) = '','NVL', o.resourceCode) = IFNULL(n.resourceCode,'NVL'), null, CONCAT(IFNULL(o.resourceCode,'NVL'),' -> ',IFNULL(n.resourceCode,'NVL'))) as resourceCode,
if(IF(o.devTeam is null or trim(o.devTeam) = '','NVL', o.devTeam) = IFNULL(n.devTeam,'NVL'), null, CONCAT(IFNULL(o.devTeam,'NVL'),' -> ',IFNULL(n.devTeam,'NVL'))) as devTeam
from common_service_artifactinfo o
left join ztst_artifact_view n on n.appName = o.appName
order by o.appName

-- 填充优化后
CREATE OR REPLACE view ztst_artifact_comp_view
AS
select o.appName as module_name,
if(o.id is null or trim(o.id) = '' or IFNULL(o.id,'NVL') = IFNULL(n.id,'NVL'), null, CONCAT(IFNULL(o.id,'NVL'),' -> ',IFNULL(n.id,'NVL'))) as id,
if(o.appName is null or trim(o.appName) = '' or IFNULL(o.appName,'NVL') = IFNULL(n.appName,'NVL'), null, CONCAT(IFNULL(o.appName,'NVL'),' -> ',IFNULL(n.appName,'NVL'))) as appName,
if(o.containerName is null or trim(o.containerName) = '' or o.containerName = 'temp' or IFNULL(o.containerName,'NVL') = IFNULL(n.containerName,'NVL'), null, CONCAT(IFNULL(o.containerName,'NVL'),' -> ',IFNULL(n.containerName,'NVL'))) as containerName,
if(o.packageName is null or trim(o.packageName) = '' or IFNULL(o.packageName,'NVL') = IFNULL(n.packageName,'NVL'), null, CONCAT(IFNULL(o.packageName,'NVL'),' -> ',IFNULL(n.packageName,'NVL'))) as packageName,
if(o.accessUrl is null or trim(o.accessUrl) = '' or o.accessUrl = 'null' or IFNULL(o.accessUrl,'NVL') = IFNULL(n.accessUrl,'NVL'), null, CONCAT(IFNULL(o.accessUrl,'NVL'),' -> ',IFNULL(n.accessUrl,'NVL'))) as accessUrl,
if(o.jdkVersion is null or trim(o.jdkVersion) = '' or o.jdkVersion = 'null' or IFNULL(o.jdkVersion,'NVL') = IFNULL(n.jdkVersion,'NVL'), null, CONCAT(IFNULL(o.jdkVersion,'NVL'),' -> ',IFNULL(n.jdkVersion,'NVL'))) as jdkVersion,
if(o.deployPath is null or trim(o.deployPath) = '' or o.deployPath = 'null' or IFNULL(o.deployPath,'NVL') = IFNULL(n.deployPath,'NVL'), null, CONCAT(IFNULL(o.deployPath,'NVL'),' -> ',IFNULL(n.deployPath,'NVL'))) as deployPath,
if(o.appType is null or trim(o.appType) = '' or IFNULL(o.appType,'NVL') = IFNULL(n.appType,'NVL'), null, CONCAT(IFNULL(o.appType,'NVL'),' -> ',IFNULL(n.appType,'NVL'))) as appType,
if(o.appCnName is null or trim(o.appCnName) = '' or o.appCnName = 'null' or IFNULL(o.appCnName,'NVL') = IFNULL(n.appCnName,'NVL'), null, CONCAT(IFNULL(o.appCnName,'NVL'),' -> ',IFNULL(n.appCnName,'NVL'))) as appCnName,
if(o.appPort is null or o.appPort = 0 or IFNULL(o.appPort,'NVL') = IFNULL(n.appPort,'NVL'), null, CONCAT(IFNULL(o.appPort,'NVL'),' -> ',IFNULL(n.appPort,'NVL'))) as appPort,
if(o.trunkPath is null or trim(o.trunkPath) = '' 
	or INSTR(replace(o.trunkPath, '192.168.220.100', 'svn.howbuy.test'), IFNULL(n.trunkPath,'NVL')) = 1 
	or INSTR(IFNULL(n.trunkPath,'NVL'), replace(o.trunkPath, '192.168.220.100', 'svn.howbuy.test')) = 1, null, CONCAT(IFNULL(o.trunkPath,'NVL'),' -> ',IFNULL(n.trunkPath,'NVL'))) as trunkPath,
if(o.gitRepo is null or trim(o.gitRepo) = '' or IFNULL(o.gitRepo,'NVL') = IFNULL(n.gitRepo,'NVL'), null, CONCAT(IFNULL(o.gitRepo,'NVL'),' -> ',IFNULL(n.gitRepo,'NVL'))) as gitRepo,
if(o.featureTeam is null or trim(o.featureTeam) = '' or IFNULL(o.featureTeam,'NVL') = IFNULL(n.featureTeam,'NVL'), null, CONCAT(IFNULL(o.featureTeam,'NVL'),' -> ',IFNULL(n.featureTeam,'NVL'))) as featureTeam,
if(o.gitCodePath is null or trim(o.gitCodePath) = '' or IFNULL(o.gitCodePath,'NVL') = IFNULL(n.gitCodePath,'NVL'), null, CONCAT(IFNULL(o.gitCodePath,'NVL'),' -> ',IFNULL(n.gitCodePath,'NVL'))) as gitCodePath,
if(o.createPath is null or trim(o.createPath) = '' or IFNULL(o.createPath,'NVL') = IFNULL(n.createPath,'NVL'), null, CONCAT(IFNULL(o.createPath,'NVL'),' -> ',IFNULL(n.createPath,'NVL'))) as createPath,
if(o.resourceCode is null or trim(o.resourceCode) = '' or IFNULL(o.resourceCode,'NVL') = IFNULL(n.resourceCode,'NVL'), null, CONCAT(IFNULL(o.resourceCode,'NVL'),' -> ',IFNULL(n.resourceCode,'NVL'))) as resourceCode,
if(o.devTeam is null or trim(o.devTeam) = '' or IFNULL(o.devTeam,'NVL') = IFNULL(n.devTeam,'NVL'), null, CONCAT(IFNULL(o.devTeam,'NVL'),' -> ',IFNULL(n.devTeam,'NVL'))) as devTeam
from common_service_artifactinfo o
left join ztst_artifact_view n on n.appName = o.appName
order by o.appName

-- ----------------------------
-- 原表添加修改时间
-- zt@2020-03-25
-- ----------------------------
alter table common_service_artifactinfo add COLUMN `update_time` timestamp not null default current_timestamp on update current_timestamp comment '修改时间';


-- ----------------------------
-- 查看所有差异
-- zt@2020-03-25
-- 忽略情况
-- 1、空白
-- 2、null字符
-- 3、0
-- 忽略字段：
-- 1、accessUrl
-- 2、appCnName
-- 3、featureTeam
-- 4、devTeam
-- 填充逻辑：
-- 1、appType -> containerName
-- 2、appType -> deployPath
-- ----------------------------
select *
from ztst_artifact_comp_view
where containerName is not NULL
or packageName is not null
or jdkVersion is not NULL
or deployPath is not NULL
or appType is not NULL
or appPort is not NULL
or trunkPath is not NULL
or gitRepo is not NULL
or gitCodePath is not NULL
or createPath is not NULL
or resourceCode is not NULL

-- 刷新成上线数据
bill-report-service
center-activity-boot
center-api-boot
center-feature-service
center-member-service

cgi

cim-account-server
cim-advert-server
cim-hbsite-server
cim-ucenter-server
cim-web-server

message-consumer
message-job-server
message-server
message-xbox