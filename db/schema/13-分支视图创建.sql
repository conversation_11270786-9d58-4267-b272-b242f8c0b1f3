-- ----------------------------
-- git分支信息整合视图：ztst_git_br_view
-- br: branch
-- zt@2020-03-18
-- ----------------------------
CREATE OR REPLACE view ztst_git_br_view
as
select gv.module_name, gv.br_name,
IF(a.git_url is null, null, CONCAT(gs.git_addr, a.git_url, a.git_path)) as git_addr,
gv.update_time
from(
  select m.app_id, m.module_name,br.br_name, b.update_time
	from ztst_app_module m
	inner join ztst_app_build b on b.app_id = m.app_id and b.module_name = m.module_name and (b.package_type = 'war' or b.package_type = 'tar' or (b.package_type = 'jar' and m.need_online = 1))
	inner join iter_mgt_iter_app_info bs on bs.appName = m.module_name
	inner join iter_mgt_iter_info br on br.pipeline_id = bs.pipeline_id
	where br.br_name is not null
	UNION
	select m.app_id, m.module_name, 'master', b.update_time
	from ztst_app_module m
	inner join ztst_app a on a.id = m.app_id and a.git_path is not null
	inner join ztst_app_build b on b.app_id = m.app_id and b.module_name = m.module_name and (b.package_type = 'war' or b.package_type = 'tar' or (b.package_type = 'jar' and m.need_online = 1))
	inner join iter_mgt_iter_app_info bs on bs.appName = m.module_name
)gv
left join ztst_app a on a.id = gv.app_id
left join ztst_git_url gu on a.git_url = gu.git_url
left join ztst_git_server gs on gu.git_server_id = gs.id
order by gv.module_name, gv.br_name, gv.update_time desc;

-- ----------------------------
-- svn分支信息整合视图：ztst_svn_br_view
-- br: branch
-- zt@2020-03-18
-- ----------------------------
CREATE OR REPLACE view ztst_svn_br_view
as
select sv.module_name, sv.br_name, sv.svn_addr, sv.update_time
from(
  select m.module_name, br.br_name, br.svn_addr, b.update_time
	from ztst_app_module m
	inner join ztst_app_build b on b.app_id = m.app_id and b.module_name = m.module_name and (b.package_type = 'war' or b.package_type = 'tar' or (b.package_type = 'jar' and m.need_online = 1))
	inner join (
		select
		SUBSTRING_INDEX(SUBSTRING_INDEX(m.appName,',',idx.id),',',-1) as module_name,
		m.businessID as br_name,
		REPLACE(m.svnPath,'***************','svn.howbuy.test') as svn_addr
		from pipeline_releaseverinfo m
		inner join ztst_app_module idx on idx.id < LENGTH(m.appName)-LENGTH(REPLACE(m.appName,',',''))+2
		where m.appName is not null and m.appName <> ''
	)br on br.module_name = m.module_name
	UNION
  select m.module_name, 'master',
    IF(a.svn_url is null, null, CONCAT(ss.svn_addr, su.svn_url, IFNULL(a.svn_path, ''), IFNULL(m.module_svn_path, CONCAT('/', m.module_name)))),
	b.update_time
	from ztst_app_module m
	inner join ztst_app a on a.id = m.app_id and a.git_path is null and a.svn_path is not null
	inner join ztst_app_build b on b.app_id = m.app_id and b.module_name = m.module_name and (b.package_type = 'war' or b.package_type = 'tar' or (b.package_type = 'jar' and m.need_online = 1))
	left join ztst_svn_url su on a.svn_url = su.svn_name
    left join ztst_svn_server ss on su.svn_server_id = ss.id
)sv
order by sv.module_name, sv.br_name, sv.update_time desc;

-- ----------------------------
-- 分支信息整合视图：ztst_br_view
-- br: branch
-- zt@2020-03-18
-- ----------------------------
CREATE OR REPLACE view ztst_br_view
as
select distinct
m.module_code, m.module_name, v.br_name, v.vcs_addr,
a.app_cname as app_desc,
IFNULL(m.module_desc, a.app_cname) as module_desc,
v.update_time
from ztst_app_module m
inner join ztst_app a on a.id = m.app_id
inner join (
	select module_name, br_name, git_addr as vcs_addr, update_time
	from ztst_git_br_view
	UNION
	select module_name, br_name, svn_addr, update_time
	from ztst_svn_br_view
)v on v.module_name = m.module_name
order by m.module_name, v.br_name desc, v.update_time desc;

-- ----------------------------
-- svn上线模块的主干信息：ztst_svn_trunk_view
-- zt@2020-03-23
-- ----------------------------
CREATE OR REPLACE view ztst_svn_trunk_view
as
select m.module_name as appName,
CONCAT(ss.svn_addr, su.svn_url, IFNULL(a.svn_path, ''), IFNULL(m.module_svn_path, CONCAT('/', m.module_name))) as trunkPath
from ztst_app_module m
inner join ztst_app a on a.id = m.app_id and a.git_path is null and a.svn_path is not null
inner join ztst_app_build b on b.app_id = m.app_id and b.module_name = m.module_name and (b.package_type = 'war' or b.package_type = 'tar' or (b.package_type = 'jar' and m.need_online = 1))
left join ztst_svn_url su on a.svn_url = su.svn_name
left join ztst_svn_server ss on su.svn_server_id = ss.id
where m.need_check = 1
order by m.module_svn_path

-- ----------------------------
-- git上线模块的maeter信息：ztst_git_master_view
-- zt@2020-03-23
-- ----------------------------
CREATE OR REPLACE view ztst_git_master_view
as
select m.module_name as appName,
CONCAT(gs.git_addr, a.git_url, a.git_path) as gitCodePath
from ztst_app_module m
inner join ztst_app a on a.id = m.app_id and a.git_path is not null
inner join ztst_app_build b on b.app_id = m.app_id and b.module_name = m.module_name and (b.package_type = 'war' or b.package_type = 'tar' or (b.package_type = 'jar' and m.need_online = 1))
left join ztst_git_url gu on a.git_url = gu.git_url
left join ztst_git_server gs on gu.git_server_id = gs.id
where m.need_check = 1
order by a.git_url, a.git_path
