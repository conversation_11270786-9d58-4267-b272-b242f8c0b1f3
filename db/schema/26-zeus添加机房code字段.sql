-- ----------------------------
-- zeus_env表添加room_code字段 zt@2020-05-06
-- 测试库：zeus_nacos_new
-- 生产库：zeus_nacos
-- ----------------------------
alter table zeus_env add column room_code VARCHAR(100) COMMENT '机房编码（后续用来取代jf_id）';

-- ID数据备份
-- update zeus_env set id = 2001151111111111684 where tenant_id = 'dev';
-- update zeus_env set id = 2001151111111111685 where tenant_id = 'test';
-- update zeus_env set id = 2004161021106821058 where tenant_id = 'tms05';
-- update zeus_env set id = 2004161020326761054 where tenant_id = 'tms06';
-- update zeus_env set id = 2004161021488151053 where tenant_id = 'tms07';
-- update zeus_env set id = 3001151111111111688 where tenant_id = 'hk-prod';
-- update zeus_env set id = 3001151111111111686 where tenant_id = 'uat';
-- update zeus_env set id = 3001151111111111689 where tenant_id = 'prod';
-- update zeus_env set id = 2004161019468201054 where tenant_id = 'tms-dev';


-- ----------------------------
-- zeus_env测试环境整理 zt@2020-05-07
-- ----------------------------
-- 默认开发环境 dev 2001151111111 --> 1
-- 默认测试环境 test 2001151111114 --> 11
-- 宝山仿真环境 uat 2001151111115 --> 77
-- 外高桥生产环境 prod 2004031534037101324 --> 99
-- 魔戒开发环境 dev1 2004101752578931325 --> 清理
-- 唐传广本地开发环境 dev-tcg 2004131511236031320 --> 清理

-- select * from zeus_tag_val where env_id in(2001151111111, 2001151111114, 2001151111115);
-- select * from zeus_config_instance where env_id in(2001151111111, 2001151111114, 2001151111115);
-- select * from zeus_config_instance_assemble where env_id in(2001151111111, 2001151111114, 2001151111115);
-- select * from zeus_config_instance_release where env_id in(2001151111111, 2001151111114, 2001151111115);

-- update zeus_tag_val set env_id = 1 where env_id = 2001151111111;
-- update zeus_tag_val set env_id = 11 where env_id = 2001151111114;
-- update zeus_tag_val set env_id = 77 where env_id = 2001151111115;
-- update zeus_tag_val set env_id = 99 where env_id = 2004031534037101324;
-- delete from zeus_tag_val where env_id = 2004101752578931325;
-- delete from zeus_tag_val where env_id = 2004131511236031320;

-- update zeus_config_instance set env_id = 1 where env_id = 2001151111111;
-- update zeus_config_instance set env_id = 11 where env_id = 2001151111114;
-- update zeus_config_instance set env_id = 77 where env_id = 2001151111115;
-- update zeus_config_instance set env_id = 99 where env_id = 2004031534037101324;
-- delete from zeus_config_instance where env_id = 2004101752578931325;
-- delete from zeus_config_instance where env_id = 2004131511236031320;

-- update zeus_config_instance_assemble set env_id = 1 where env_id = 2001151111111;
-- update zeus_config_instance_assemble set env_id = 11 where env_id = 2001151111114;
-- update zeus_config_instance_assemble set env_id = 77 where env_id = 2001151111115;
-- update zeus_config_instance_assemble set env_id = 99 where env_id = 2004031534037101324;
-- delete from zeus_config_instance_assemble where env_id = 2004101752578931325;
-- delete from zeus_config_instance_assemble where env_id = 2004131511236031320;

-- update zeus_config_instance_release set env_id = 1 where env_id = 2001151111111;
-- update zeus_config_instance_release set env_id = 11 where env_id = 2001151111114;
-- update zeus_config_instance_release set env_id = 77 where env_id = 2001151111115;
-- update zeus_config_instance_release set env_id = 99 where env_id = 2004031534037101324;
-- delete from zeus_config_instance_release where env_id = 2004101752578931325;
-- delete from zeus_config_instance_release where env_id = 2004131511236031320;

-- delete from zeus_env where 1=1;