-- ----------------------------
-- 针对5月13号 2.1.6 上线整理：
-- 1、首先需要先停掉所有应用。防止新生成数据。（实测生产库转出、转入共需要10分钟）
-- 2、需要备份django_scm、spider库，因为需要清理所有的表。
-- 3、执行数据迁库脚本。
-- 4、启动应用。
-- 注：本次迁库、改表名，一定要注意django_scm、spider两个库！
-- ----------------------------

-- ----------------------------
-- 应用表改名：app_mgt_ztst_app -> app_mgt_app_info zt@2020-05-12
-- ----------------------------
alter table spider.app_mgt_ztst_app rename to app_mgt_app_info;

-- ----------------------------
-- 团队表改名：tool_mgt_ztst_team -> tool_mgt_team_info zt@2020-05-12
-- ----------------------------
alter table spider.tool_mgt_ztst_team rename to tool_mgt_team_info;

-- ----------------------------
-- 迁表后新应用视图重建：spider_app_view
-- zt@2020-05-12 第5版
-- ----------------------------
CREATE OR REPLACE VIEW spider.spider_app_view
AS
SELECT
m.app_id,
m.id,
m.module_code as resourceCode,
m.module_name as appName,
IFNULL(m.module_jdk_version, a.app_jdk_version) AS jdkVersion,
b.package_name as packageName,
b.package_type,
m.need_online,
IFNULL(m.module_desc, a.app_cname) AS appCnName,
IF(a.svn_url is null, null, CONCAT(ss.svn_addr, su.svn_url, IFNULL(a.svn_path, ''), IFNULL(m.module_svn_path, CONCAT('/', m.module_name)))) AS trunkPath,
IF(a.git_url is null, null, CONCAT(a.git_url, a.git_path)) AS gitCodePath,
IF(a.git_url is null, null, CONCAT(gs.git_addr, a.git_url, a.git_path)) AS git_absolute_path,
m.create_path as createPath,
m.lib_repo as gitRepo,
a.lib_location as featureTeam,
m.container_name as containerName,
m.app_port as appPort,
m.deploy_path as deployPath
FROM spider.app_mgt_app_module m
INNER JOIN spider.app_mgt_app_info a ON a.id = m.app_id
INNER JOIN spider.app_mgt_app_build b ON b.app_id = m.app_id AND b.module_name = m.module_name
LEFT JOIN spider.tool_mgt_git_url gu ON a.git_url = gu.git_url
LEFT JOIN spider.tool_mgt_git_server gs ON gu.git_server_id = gs.id
LEFT JOIN spider.tool_mgt_svn_url su ON a.svn_url = su.svn_name
LEFT JOIN spider.tool_mgt_svn_server ss ON su.svn_server_id = ss.id
ORDER BY m.app_id, m.module_name;


-- ----------------------------
-- 迁表后老视图重建：common_service_artifactinfo
-- zt@2020-05-12 第9版
-- 注：此视图在 django_scm 老库里
-- ----------------------------
CREATE OR REPLACE view django_scm.common_service_artifactinfo
as
select m.id,
m.module_name as appName,
IFNULL(m.container_name,CONCAT('----',b.package_type,'----')) as containerName,
b.package_name as packageName,
null as accessUrl,
IFNULL(m.module_jdk_version, a.app_jdk_version) as jdkVersion,
IFNULL(m.deploy_path,CONCAT('----',b.package_type,'----')) as deployPath,
CASE
	WHEN b.package_type = 'jar' and m.need_online = 1 THEN
		'dubbo'
	WHEN b.package_type = 'jar' and a.git_url is null and ac.need_online_count = 0 THEN
		'common_jar'
	ELSE
		b.package_type
END appType,
IFNULL(m.module_desc, a.app_cname) as appCnName,
m.app_port as appPort,
IF(a.svn_url is null, null, CONCAT(ss.svn_addr, su.svn_url, IFNULL(a.svn_path, ''), IFNULL(m.module_svn_path, CONCAT('/', m.module_name)))) as trunkPath,
m.lib_repo as gitRepo,
a.lib_location as featureTeam,
IF(a.git_url is null, null, CONCAT(a.git_url, a.git_path)) as gitCodePath,
m.create_path as createPath,
m.module_code as resourceCode,
null as devTeam
from spider.app_mgt_app_module m
inner join spider.app_mgt_app_info a on a.id = m.app_id
inner join spider.app_mgt_app_build b on b.app_id = m.app_id and b.module_name = m.module_name
inner join (
	select a.id,
	sum(
	CASE
		WHEN b.package_type = 'war' or b.package_type = 'tar'	THEN 1
		WHEN b.package_type = 'jar' and m.need_online = 1	THEN 1
		ELSE 0
	END)as need_online_count
	from spider.app_mgt_app_info a
	inner join spider.app_mgt_app_module m on m.app_id = a.id
	inner join spider.app_mgt_app_build b on b.app_id = a.id and b.module_name = m.module_name
	group by a.id
)ac on ac.id = a.id
left join spider.tool_mgt_git_url gu on a.git_url = gu.git_url
left join spider.tool_mgt_git_server gs on gu.git_server_id = gs.id
left join spider.tool_mgt_svn_url su on a.svn_url = su.svn_name
left join spider.tool_mgt_svn_server ss on su.svn_server_id = ss.id
where m.module_name != 'howbuy-quartz'
order by m.id;


-- ----------------------------
-- 库分离后git分支信息整合视图修改（包含master）
-- br: branch
-- zt@2020-05-12
-- ----------------------------
CREATE OR REPLACE view spider.zeus_git_br_view
as
select gv.module_name, gv.br_name,
IF(a.git_url is null, null, CONCAT(gs.git_addr, a.git_url, a.git_path)) as git_addr,
gv.update_time
from(
  select m.app_id, m.module_name,br.br_name, b.update_time
	from spider.app_mgt_app_module m
	inner join spider.app_mgt_app_build b on b.app_id = m.app_id and b.module_name = m.module_name and (b.package_type = 'war' or b.package_type = 'tar' or (b.package_type = 'jar' and m.need_online = 1))
	inner join spider.iter_mgt_iter_app_info bs on bs.appName = m.module_name
	inner join spider.iter_mgt_iter_info br on br.pipeline_id = bs.pipeline_id
	where br.br_name is not null
	UNION
	select m.app_id, m.module_name, 'master', b.update_time
	from spider.app_mgt_app_module m
	inner join spider.app_mgt_app_info a on a.id = m.app_id and a.git_path is not null
	inner join spider.app_mgt_app_build b on b.app_id = m.app_id and b.module_name = m.module_name and (b.package_type = 'war' or b.package_type = 'tar' or (b.package_type = 'jar' and m.need_online = 1))
	inner join spider.iter_mgt_iter_app_info bs on bs.appName = m.module_name
)gv
left join spider.app_mgt_app_info a on a.id = gv.app_id
left join spider.tool_mgt_git_url gu on a.git_url = gu.git_url
left join spider.tool_mgt_git_server gs on gu.git_server_id = gs.id
order by gv.module_name, gv.br_name, gv.update_time desc;


-- ----------------------------
-- 库分离后svn分支信息整合视图修改（包含master）
-- br: branch
-- zt@2020-05-12
-- ----------------------------
CREATE OR REPLACE view spider.zeus_svn_br_view
as
select sv.module_name, sv.br_name, sv.svn_addr, sv.update_time
from(
  select m.module_name, br.br_name, br.svn_addr, b.update_time
	from spider.app_mgt_app_module m
	inner join spider.app_mgt_app_build b on b.app_id = m.app_id and b.module_name = m.module_name and (b.package_type = 'war' or b.package_type = 'tar' or (b.package_type = 'jar' and m.need_online = 1))
	inner join (
		select
		SUBSTRING_INDEX(SUBSTRING_INDEX(m.appName,',',idx.id),',',-1) as module_name,
		m.businessID as br_name,
		REPLACE(m.svnPath,'***************','svn.howbuy.test') as svn_addr
		from spider.pipeline_releaseverinfo m
		inner join spider.app_mgt_app_module idx on idx.id < LENGTH(m.appName)-LENGTH(REPLACE(m.appName,',',''))+2
		where m.appName is not null and m.appName <> ''
	)br on br.module_name = m.module_name
	UNION
  select m.module_name, 'master',
    IF(a.svn_url is null, null, CONCAT(ss.svn_addr, su.svn_url, IFNULL(a.svn_path, ''), IFNULL(m.module_svn_path, CONCAT('/', m.module_name)))),
	b.update_time
	from spider.app_mgt_app_module m
	inner join spider.app_mgt_app_info a on a.id = m.app_id and a.git_path is null and a.svn_path is not null
	inner join spider.app_mgt_app_build b on b.app_id = m.app_id and b.module_name = m.module_name and (b.package_type = 'war' or b.package_type = 'tar' or (b.package_type = 'jar' and m.need_online = 1))
	left join spider.tool_mgt_svn_url su on a.svn_url = su.svn_name
  left join spider.tool_mgt_svn_server ss on su.svn_server_id = ss.id
)sv
order by sv.module_name, sv.br_name, sv.update_time desc;


-- ----------------------------
-- 分支信息整合视图：zeus_br_view
-- br: branch
-- zt@2020-05-12
-- ----------------------------
CREATE OR REPLACE view spider.zeus_br_view
as
select distinct
m.module_code, m.module_name, v.br_name, v.vcs_addr,
a.app_cname as app_desc,
IFNULL(m.module_desc, a.app_cname) as module_desc,
v.update_time
from spider.app_mgt_app_module m
inner join spider.app_mgt_app_info a on a.id = m.app_id
inner join (
	select module_name, br_name, git_addr as vcs_addr, update_time
	from zeus_git_br_view
	UNION
	select module_name, br_name, svn_addr, update_time
	from zeus_svn_br_view
)v on v.module_name = m.module_name
order by m.module_name, v.br_name desc, v.update_time desc;


-- ----------------------------
-- git上线模块的master信息：zeus_git_master_view
-- zt@2020-05-12
-- ----------------------------
CREATE OR REPLACE view spider.zeus_git_master_view
as
select m.module_name as appName,
CONCAT(gs.git_addr, a.git_url, a.git_path) as gitCodePath
from spider.app_mgt_app_module m
inner join spider.app_mgt_app_info a on a.id = m.app_id and a.git_path is not null
inner join spider.app_mgt_app_build b on b.app_id = m.app_id and b.module_name = m.module_name and (b.package_type = 'war' or b.package_type = 'tar' or (b.package_type = 'jar' and m.need_online = 1))
left join spider.tool_mgt_git_url gu on a.git_url = gu.git_url
left join spider.tool_mgt_git_server gs on gu.git_server_id = gs.id
where m.need_check = 1
order by a.git_url, a.git_path;


-- ----------------------------
-- svn上线模块的主干信息：zeus_svn_trunk_view
-- zt@2020-05-12
-- ----------------------------
CREATE OR REPLACE view spider.zeus_svn_trunk_view
as
select m.module_name as appName,
CONCAT(ss.svn_addr, su.svn_url, IFNULL(a.svn_path, ''), IFNULL(m.module_svn_path, CONCAT('/', m.module_name))) as trunkPath
from spider.app_mgt_app_module m
inner join spider.app_mgt_app_info a on a.id = m.app_id and a.git_path is null and a.svn_path is not null
inner join spider.app_mgt_app_build b on b.app_id = m.app_id and b.module_name = m.module_name and (b.package_type = 'war' or b.package_type = 'tar' or (b.package_type = 'jar' and m.need_online = 1))
left join spider.tool_mgt_svn_url su on a.svn_url = su.svn_name
left join spider.tool_mgt_svn_server ss on su.svn_server_id = ss.id
where m.need_check = 1
order by m.module_svn_path;


-- ----------------------------
-- 老库 django_scm 中视图清理
-- zt@2020-05-12
-- ----------------------------
drop view django_scm.ztst_app_view;
drop view django_scm.ztst_artifact_comp_view;
drop view django_scm.ztst_artifact_comp_view_bak0401;
drop view django_scm.ztst_artifact_diff_view;
drop view django_scm.ztst_artifact_view;
drop view django_scm.ztst_br_view;
drop view django_scm.ztst_csai_view;
drop view django_scm.ztst_git_br_view;
drop view django_scm.ztst_git_master_view;
drop view django_scm.ztst_svn_br_view;
drop view django_scm.ztst_svn_trunk_view;

-- ----------------------------
-- 老库 django_scm 中表清理
-- zt@2020-05-12
-- ----------------------------
drop table django_scm.env_mgt_deploy_group;
drop table django_scm.env_mgt_test_group;
drop table django_scm.ztst_app;
drop table django_scm.ztst_app_build;
drop table django_scm.ztst_app_module;
drop table django_scm.ztst_app_team;
drop table django_scm.ztst_app_user;
drop table django_scm.ztst_git_server;
drop table django_scm.ztst_git_url;
drop table django_scm.ztst_svn_server;
drop table django_scm.ztst_svn_url;
drop table django_scm.ztst_team;

-- ----------------------------
-- 新库 spider 中视图清理
-- zt@2020-05-12
-- ----------------------------
drop view spider.ztst_app_view;
drop view spider.ztst_br_view;
drop view spider.ztst_git_br_view;
drop view spider.ztst_git_master_view;
drop view spider.ztst_svn_br_view;
drop view spider.ztst_svn_trunk_view;
drop view spider.zeus_app_view;
