-- ----------------------------
-- module表添加方式字段 zt@2020-04-27
-- ----------------------------
alter table app_mgt_app_module add column prop_type TINYINT(1) COMMENT '配置方式：1 宙斯 2 配置魔方 3 CCMS 4 本地配置文件 5其它';


-- ----------------------------
-- 配置文件路径表 zt@2020-04-27
-- ----------------------------
drop table if exists app_mgt_prop_path;
create table app_mgt_prop_path(
  id BIGINT(11) auto_increment,
  module_name VARCHAR(100) COMMENT '模块名',
  module_ver VARCHAR(100) COMMENT '版本',
  module_env VARCHAR(100) COMMENT '环境',
  prop_path VARCHAR(999) COMMENT 'prop配置文件地址',
  create_user VARCHAR(20) COMMENT '创建人',
  create_time DATETIME(0) COMMENT '创建时间',
  update_user VARCHAR(20) COMMENT '修改人',
  update_time DATETIME(0) COMMENT '修改时间',
  stamp BIGINT(20) COMMENT '版本',
  PRIMARY KEY (id)
)COMMENT = '配置文件路径表';
