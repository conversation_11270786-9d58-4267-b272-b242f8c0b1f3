-- ----------------------------
-- Database: ztst
-- ----------------------------
-- use ztst;

-- ----------------------------
-- GIT服务器表：ztst_git_server
-- ----------------------------
drop table if exists ztst_git_server;
create table ztst_git_server(
  id BIGINT(11) auto_increment,
	git_ip VARCHAR(100) COMMENT 'git主机ip',
	git_port INT(5) COMMENT 'git端口',
	git_domain VARCHAR(255) COMMENT 'git域名',
	git_addr VARCHAR(999) COMMENT 'git地址',
	git_desc VARCHAR(255) COMMENT 'git服务器说明',
  create_user VARCHAR(20) COMMENT '创建人',
  create_time DATETIME(0) COMMENT '创建时间',
  update_user VARCHAR(20) COMMENT '修改人',
  update_time DATETIME(0) COMMENT '修改时间',
  stamp BIGINT(20) COMMENT '版本',
  PRIMARY KEY (id)
)COMMENT = 'GIT服务器表';

-- ----------------------------
-- SVN服务器表：ztst_svn_server
-- ----------------------------
drop table if exists ztst_svn_server;
create table ztst_svn_server(
  id BIGINT(11) auto_increment,
	svn_ip VARCHAR(100) COMMENT 'svn主机ip',
	svn_port INT(5) COMMENT 'svn端口',
	svn_domain VARCHAR(255) COMMENT 'svn域名',
	svn_addr VARCHAR(999) COMMENT 'svn地址',
	svn_desc VARCHAR(255) COMMENT 'svn服务器说明',
  create_user VARCHAR(20) COMMENT '创建人',
  create_time DATETIME(0) COMMENT '创建时间',
  update_user VARCHAR(20) COMMENT '修改人',
  update_time DATETIME(0) COMMENT '修改时间',
  stamp BIGINT(20) COMMENT '版本',
  PRIMARY KEY (id)
)COMMENT = 'SVN服务器表';

-- ----------------------------
-- GIT路径表：ztst_git_url
-- ----------------------------
drop table if exists ztst_git_url;
create table ztst_git_url(
  id BIGINT(11) auto_increment,
	git_url VARCHAR(999) COMMENT 'git路径',
	git_name VARCHAR(100) COMMENT 'git名称：直观显示用',
	git_server_id BIGINT(11) COMMENT 'git主机id',
	git_url_desc VARCHAR(255) COMMENT 'git路径说明',
  create_user VARCHAR(20) COMMENT '创建人',
  create_time DATETIME(0) COMMENT '创建时间',
  update_user VARCHAR(20) COMMENT '修改人',
  update_time DATETIME(0) COMMENT '修改时间',
  stamp BIGINT(20) COMMENT '版本',
  PRIMARY KEY (id)
)COMMENT = 'GIT路径表';

-- ----------------------------
-- SVN路径表：ztst_svn_url
-- ----------------------------
drop table if exists ztst_svn_url;
create table ztst_svn_url(
  id BIGINT(11) auto_increment,
	svn_url VARCHAR(999) COMMENT 'svn路径',
	svn_name VARCHAR(100) COMMENT 'svn名称：直观显示用',
	svn_server_id BIGINT(11) COMMENT 'svn主机id',
	svn_url_desc VARCHAR(255) COMMENT 'svn路径说明',
  create_user VARCHAR(20) COMMENT '创建人',
  create_time DATETIME(0) COMMENT '创建时间',
  update_user VARCHAR(20) COMMENT '修改人',
  update_time DATETIME(0) COMMENT '修改时间',
  stamp BIGINT(20) COMMENT '版本',
  PRIMARY KEY (id)
)COMMENT = 'SVN路径表';

-- ----------------------------
-- 团队表：ztst_team
-- ----------------------------
drop table if exists ztst_team;
create table ztst_team(
  id BIGINT(11) auto_increment,
	team_name VARCHAR(100) COMMENT '团队名',
	team_short_name VARCHAR(100) COMMENT '团队简称',
	team_alias VARCHAR(100) COMMENT '团队别名',
	team_owner VARCHAR(100) COMMENT '团队负责人',
	team_mail VARCHAR(100) COMMENT '团队邮箱',
	team_desc VARCHAR(255) COMMENT '团队说明',
  create_user VARCHAR(20) COMMENT '创建人',
  create_time DATETIME(0) COMMENT '创建时间',
  update_user VARCHAR(20) COMMENT '修改人',
  update_time DATETIME(0) COMMENT '修改时间',
  stamp BIGINT(20) COMMENT '版本',
  PRIMARY KEY (id)
)COMMENT = '团队表';

-- ----------------------------
-- 应用表：ztst_app
-- ----------------------------
drop table if exists ztst_app;
create table ztst_app(
  id BIGINT(11) auto_increment,
	app_name VARCHAR(100) COMMENT '英文名',
	app_cname VARCHAR(100) COMMENT '中文名',
	app_status TINYINT(1) COMMENT '应用状态：0-已废弃，1-使用中',
  git_url VARCHAR(999) COMMENT 'git编码',
	git_path VARCHAR(999) COMMENT 'git路径',
	svn_url VARCHAR(999) COMMENT 'svn编码',
	svn_path VARCHAR(999) COMMENT 'svn路径',
  app_jdk_version VARCHAR(100) COMMENT 'JDK版本',
	app_desc VARCHAR(255) COMMENT '应用说明',
  create_user VARCHAR(20) COMMENT '创建人',
  create_time DATETIME(0) COMMENT '创建时间',
  update_user VARCHAR(20) COMMENT '修改人',
  update_time DATETIME(0) COMMENT '修改时间',
  stamp BIGINT(20) COMMENT '版本',
  PRIMARY KEY (id)
)COMMENT = '应用表';

-- ----------------------------
-- 应用团队表：ztst_app_team
-- ----------------------------
drop table if exists ztst_app_team;
create table ztst_app_team(
  id BIGINT(11) auto_increment,
	app_id BIGINT(11) COMMENT '应用ID',
	team_id BIGINT(11) COMMENT '团队ID',
  create_user VARCHAR(20) COMMENT '创建人',
  create_time DATETIME(0) COMMENT '创建时间',
  update_user VARCHAR(20) COMMENT '修改人',
  update_time DATETIME(0) COMMENT '修改时间',
  stamp BIGINT(20) COMMENT '版本',
  PRIMARY KEY (id)
)COMMENT = '应用团队表';

-- ----------------------------
-- 应用人员表：ztst_app_user
-- ----------------------------
drop table if exists ztst_app_user;
create table ztst_app_user(
  id BIGINT(11) auto_increment,
	app_id BIGINT(11) COMMENT '应用ID',
	user_type TINYINT(1) COMMENT '人员类型',
	user_name VARCHAR(100) COMMENT '用户名',
  create_user VARCHAR(20) COMMENT '创建人',
  create_time DATETIME(0) COMMENT '创建时间',
  update_user VARCHAR(20) COMMENT '修改人',
  update_time DATETIME(0) COMMENT '修改时间',
  stamp BIGINT(20) COMMENT '版本',
  PRIMARY KEY (id)
)COMMENT = '应用人员表';

-- ----------------------------
-- 模块表：ztst_app_module
-- ----------------------------
drop table if exists ztst_app_module;
create table ztst_app_module(
  id BIGINT(11) auto_increment,
	app_id BIGINT(11) COMMENT '应用ID',
	module_name VARCHAR(100) COMMENT '模块名',
	module_code VARCHAR(100) COMMENT '模块编码',
  module_status TINYINT(1) COMMENT '模块状态：0-已废弃，1-使用中',
  module_desc VARCHAR(255) COMMENT '模块说明',
  module_svn_path VARCHAR(999) COMMENT 'svn路径',
  module_jdk_version VARCHAR(100) COMMENT '指定JDK版本',
	need_online TINYINT(1) COMMENT 'jar是否需要上线',
	need_check TINYINT(1) COMMENT '是否需要维护',
	app_port INT(5) COMMENT '应用端口',
	container_name VARCHAR(255) COMMENT '容器名',
	create_path VARCHAR(100) COMMENT '打包路径',
	lib_repo VARCHAR(100) COMMENT '制品库',
	deploy_path VARCHAR(999) COMMENT '发布路径',
  extend_attr JSON COMMENT '扩展属性：dubbo端口等',
  create_user VARCHAR(20) COMMENT '创建人',
  create_time DATETIME(0) COMMENT '创建时间',
  update_user VARCHAR(20) COMMENT '修改人',
  update_time DATETIME(0) COMMENT '修改时间',
  stamp BIGINT(20) COMMENT '版本',
  PRIMARY KEY (id)
)COMMENT = '模块表';

-- ----------------------------
-- 构建表：ztst_app_build
-- ----------------------------
drop table if exists ztst_app_build;
create table ztst_app_build(
  id BIGINT(11) auto_increment,
	app_id BIGINT(11) COMMENT '应用ID',
	module_name VARCHAR(100) COMMENT '模块名',
	module_code VARCHAR(100) COMMENT '模块编码',
	module_version VARCHAR(100) COMMENT '模块版本',
	package_type VARCHAR(100) COMMENT '包类型：pom、war、jar、tar',
	package_name VARCHAR(255) COMMENT '包名',
	package_full TINYINT(1) COMMENT '是否完整包',
	build_jdk_version VARCHAR(100) COMMENT '编译JDK版本',
  create_user VARCHAR(20) COMMENT '创建人',
  create_time DATETIME(0) COMMENT '创建时间',
  update_user VARCHAR(20) COMMENT '修改人',
  update_time DATETIME(0) COMMENT '修改时间',
  stamp BIGINT(20) COMMENT '版本',
  PRIMARY KEY (id)
)COMMENT = '构建表';

-- ----------------------------
-- 唯一约束：ztst_app、ztst_app_module和ztst_app_build添加唯一约束
-- ----------------------------
ALTER TABLE `ztst_app` ADD UNIQUE(`app_name`);
ALTER TABLE `ztst_app_module` ADD UNIQUE(`module_name`);
ALTER TABLE `ztst_app_build` ADD UNIQUE(`module_name`);

-- ----------------------------
-- 自动增长起始值：auto_increment
-- ----------------------------
ALTER TABLE ztst_app auto_increment = 1000;
ALTER TABLE ztst_app_module auto_increment = 2000;
ALTER TABLE ztst_app_build auto_increment = 2000;