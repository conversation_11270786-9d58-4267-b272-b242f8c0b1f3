-- ----------------------------
-- 测试视图：ztst_app_view
-- zt@2020-04-02 第2版
-- ----------------------------
CREATE OR REPLACE VIEW ztst_app_view
AS
SELECT
m.app_id,
a.app_name,
m.id,
m.module_code,
m.module_name,
IFNULL(m.module_jdk_version, a.app_jdk_version) AS jdk_version,
b.package_name,
b.package_type,
m.need_online,
IFNULL(m.module_desc, a.app_cname) AS app_cname,
IF(a.svn_url is null, null, CONCAT(ss.svn_addr, su.svn_url, IFNULL(a.svn_path, ''), IFNULL(m.module_svn_path, CONCAT('/', m.module_name)))) AS svn_path,
IF(a.git_url is null, null, CONCAT(a.git_url, a.git_path)) AS git_path,
IF(a.git_url is null, null, CONCAT(gs.git_addr, a.git_url, a.git_path)) AS git_absolute_path,
m.create_path,
m.lib_repo,
a.lib_location,
m.container_name
FROM ztst_app_module m
INNER JOIN ztst_app a ON a.id = m.app_id
INNER JOIN ztst_app_build b ON b.app_id = m.app_id AND b.module_name = m.module_name
LEFT JOIN ztst_git_url gu ON a.git_url = gu.git_url
LEFT JOIN ztst_git_server gs ON gu.git_server_id = gs.id
LEFT JOIN ztst_svn_url su ON a.svn_url = su.svn_name
LEFT JOIN ztst_svn_server ss ON su.svn_server_id = ss.id
ORDER BY m.app_id, m.module_name

-- ----------------------------
-- 测试视图：ztst_app_view
-- zt@2020-04-02 第3版
-- ----------------------------
CREATE OR REPLACE VIEW ztst_app_view
AS
SELECT
m.app_id,
m.id,
m.module_code as resourceCode,
m.module_name as appName,
IFNULL(m.module_jdk_version, a.app_jdk_version) AS jdkVersion,
b.package_name as packageName,
b.package_type,
m.need_online,
IFNULL(m.module_desc, a.app_cname) AS appCnName,
IF(a.svn_url is null, null, CONCAT(ss.svn_addr, su.svn_url, IFNULL(a.svn_path, ''), IFNULL(m.module_svn_path, CONCAT('/', m.module_name)))) AS trunkPath,
IF(a.git_url is null, null, CONCAT(a.git_url, a.git_path)) AS gitCodePath,
IF(a.git_url is null, null, CONCAT(gs.git_addr, a.git_url, a.git_path)) AS git_absolute_path,
m.create_path as createPath,
m.lib_repo as gitRepo,
a.lib_location as featureTeam,
m.container_name as containerName,
m.app_port as appPort,
m.deploy_path as deployPath
FROM ztst_app_module m
INNER JOIN ztst_app a ON a.id = m.app_id
INNER JOIN ztst_app_build b ON b.app_id = m.app_id AND b.module_name = m.module_name
LEFT JOIN ztst_git_url gu ON a.git_url = gu.git_url
LEFT JOIN ztst_git_server gs ON gu.git_server_id = gs.id
LEFT JOIN ztst_svn_url su ON a.svn_url = su.svn_name
LEFT JOIN ztst_svn_server ss ON su.svn_server_id = ss.id
ORDER BY m.app_id, m.module_name