-- ----------------------------
-- 添加编译JDK字段build_jdk_version：数据初始化
-- ----------------------------
update ztst_app_build b, ztst_app_module m set m.module_jdk_version = b.build_jdk_version where b.app_id = m.app_id and b.module_name = m.module_name;

-- ----------------------------
-- 测试视图：ztst_app_view
-- ----------------------------
CREATE OR REPLACE view ztst_app_view
as
select a.id as app_id, a.app_name, a.app_cname,
m.module_name, m.module_code,
IF(a.git_url is null, null, CONCAT(gs.git_addr, a.git_url, a.git_path)) as git_addr,
IF(a.svn_url is null, null, CONCAT(ss.svn_addr, su.svn_url, IFNULL(a.svn_path, ''), IFNULL(m.module_svn_path, CONCAT('/', m.module_name)))) as svn_addr,
IFNULL(m.module_jdk_version, a.app_jdk_version) as jdk_version,
b.package_type, b.package_name
from ztst_app a
inner join ztst_app_module m on m.app_id = a.id
left join ztst_app_build b on b.app_id = a.id and b.module_name = m.module_name
left join ztst_git_url gu on a.git_url = gu.git_url
left join ztst_git_server gs on gu.git_server_id = gs.id
left join ztst_svn_url su on a.svn_url = su.svn_name
left join ztst_svn_server ss on su.svn_server_id = ss.id
order by a.app_name, m.module_name;

-- ----------------------------
-- 自动增长列：AUTO_INCREMENT
-- ----------------------------
ALTER TABLE ztst_app auto_increment = 1000;
ALTER TABLE ztst_app_module auto_increment = 2000;
ALTER TABLE ztst_app_build auto_increment = 2000;

-- ----------------------------
-- 流水线视图：ztst_csai_view
-- csai：common_service_artifact_info
-- ----------------------------
CREATE OR REPLACE view ztst_csai_view
as
select 
a.id as app_id, a.app_name, a.app_cname, m.id as module_id,
m.module_name as appName, 
IF(a.git_url is null, null, CONCAT(a.git_url, a.git_path)) as gitCodePath,
if(m.need_online = 1, 'dubbo', b.package_type) as appType,
IFNULL(m.module_jdk_version, a.app_jdk_version) as jdkVersion,
b.package_name as packageName,
m.lib_repo as gitRepo
from ztst_app a
inner join ztst_app_module m on m.app_id = a.id
left join ztst_app_build b on b.app_id = a.id and b.module_name = m.module_name
left join ztst_git_url gu on a.git_url = gu.git_url
left join ztst_git_server gs on gu.git_server_id = gs.id
left join ztst_svn_url su on a.svn_url = su.svn_name
left join ztst_svn_server ss on su.svn_server_id = ss.id
order by m.module_name;

-- ----------------------------
-- 添加应用状态字段：app_status，值全部使用默认1
-- ----------------------------
update ztst_app set app_status=1 where 1=1;