-- 关联dump文件「基础非交易-金融服务-数据服务」_for_远相。zt@2025-03-05
-- 1、「业务」（类型不关注）：
select biz.*
from biz_base_info biz
where biz.biz_name like '%数据服务%' or biz.biz_scenario_name like '数据服务'
;
-- FPC-DATA
-- 2、业务关联的「应用」
select biz_app_bind.*
from biz_app_bind
where biz_app_bind.biz_code = 'FPC-DATA'
order by biz_app_bind.app_module_name
;
-- 3、dump文件：
select biz.biz_code,
       biz.biz_name,
       CONCAT(biz_type.biz_type_department, biz_type.biz_type_transaction, '-', biz.biz_name, '-', biz.biz_scenario_name) as biz_info_name,
       biz_app_bind.biz_app_bind_type, biz_app_bind.app_module_name,
       app_i.git_url,   -- 找到库关联的多个应用，是不是属于同一个组
       domain.domain_name, domain.domain_desc, domain.db_alias,
       logic_info.id as logic_id, logic_info.logic_db_name,
       suite_bind.db_bind_suite_code,
       db_info.id as db_info_id, db_info.suite_db_name, db_info.db_info_username, db_info.db_info_password,
       db_srv.db_srv_hosts, db_srv.db_srv_username, db_srv.db_srv_password, db_srv.db_srv_socket_path, db_srv.data_dump_dir,
       db_group.db_group_name,
       biz_iter.biz_test_iter_id,
       dump.id as dump_id, dump.db_info_id, dump_suite_bind.db_bind_suite_code, dump.dump_file_name
from biz_base_info biz
inner join biz_base_type biz_type on biz_type.id = biz.biz_type
left join biz_app_bind on biz_app_bind.biz_code = biz.biz_code              -- 业务绑定的应用
left join app_mgt_app_module app_m on app_m.module_name = biz_app_bind.app_module_name  -- 这个库关联的多个应用是不是在一个组里
left join app_mgt_app_info app_i on app_i.id = app_m.app_id
left join db_mgt_app_bind app_bind on app_bind.app_module_name = biz_app_bind.app_module_name       -- 应用绑定的库（总）
left join db_mgt_domain domain on domain.id = app_bind.db_domain_id                                 -- 1、库信息（主要关联）：领域->逻辑库->环境->物理库->服务器
left join db_mgt_logic_info logic_info on logic_info.db_domain_id = app_bind.db_domain_id           -- 都绑定「domain_id」（可跳过）
left join db_mgt_suite_bind suite_bind on suite_bind.db_logic_id = logic_info.id                    -- 新建「逻辑表」
left join db_mgt_info db_info on db_info.id = suite_bind.db_info_id
left join db_mgt_srv db_srv on db_srv.id = db_info.db_srv_id
left join db_mgt_group db_group on db_group.id = domain.db_group_id         -- 2、组信息
left join biz_test_iter biz_iter on biz_iter.biz_code = biz.biz_code        -- 3、业务迭代（已定死：dev longterm master）
left join db_mgt_dump_file dump on dump.biz_test_iter_id = biz_iter.biz_test_iter_id
                                       and dump.db_logic_id = logic_info.id   -- 4、业务迭代对应的dump文件（已唯一不用max）
left join db_mgt_info dump_db_info on dump_db_info.id = dump.db_info_id     -- 5、确定dump出自哪个环境
left join db_mgt_suite_bind dump_suite_bind on dump_suite_bind.db_info_id = dump_db_info.id
where 1=1
and biz.biz_code = 'FPC-DATA'
# and biz.biz_code in ('RETAIL-T0-20221105','RETAIL-T0-20221106','RETAIL-T1-20221107','RETAIL-T2-20221108','RETAIL-T3-20221109', 'RETAIL-T4-20221110')
# and biz.biz_name like '%储蓄罐%'
and logic_info.logic_db_name in('support')
and suite_bind.db_bind_suite_code in ('it46') -- it58
and biz_iter.biz_test_iter_br in('dev', 'master')
# and biz_iter.biz_test_iter_br in('master')
order by biz.biz_type, biz.biz_code,
    biz_app_bind.biz_app_bind_type, biz_app_bind.app_module_name,
    domain.domain_name
;

-- ------------------------------
-- 问题处理记录
-- ------------------------------
-- 使用其它业务的dump文件，绑定到新的业务上：
-- 1、dump文件的生成信息：
select * from db_mgt_dump_file where db_mgt_dump_file.biz_test_iter_id like 'FPC-DATA_master';
select max(id) as dump_max_id from db_mgt_dump_file; -- 1750 --> 1751
select * from db_mgt_dump_file order by id desc;
-- 2、迭代信息：FPC
select c.app_module_name,
       CONCAT(c.time_batch,'-',c.batch_number) as batch_number,
       c.archive_br,
       c.online_br
from app_br_cache c
inner join(
    select time_batch,
           max(batch_number) as max_batch_num
    from app_br_cache
    where time_batch = (select max(time_batch) from app_br_cache)
)v_max on (v_max.time_batch, v_max.max_batch_num) = (c.time_batch, c.batch_number)
where 1=1
and c.app_module_name in('fpc-manage-data', 'howbuy-data-ants', 'howbuy-fpc-reptile', 'fpc-manage-console')
;
select * from iter_mgt_iter_info where pipeline_id like 'FPC%';
select iter_i.pipeline_id, iter_i.project_group, iter_i.br_end_date,
       iter_a.appName
from iter_mgt_iter_info iter_i
inner join iter_mgt_iter_app_info iter_a on iter_a.pipeline_id = iter_i.pipeline_id
where iter_i.project_group = 'FPC'
and iter_i.br_status = 'close'
order by iter_i.br_end_date desc, iter_a.appName;
/* 找最新归档：
pipeline_id = FPC_bugfix-20250304
 */
-- FPC-DATA_master
-- va_jd	***************	/data/dmp/mysql/docker_it999_va_jd_db_BACKUP_20250305162001.sql;
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-03-05 10:11:12', 'huaitian.zhang', '2025-03-05 10:11:12', 0, 1821, 'FPC_bugfix-20250304', 'FPC-DATA_master', 306, 12322, 1, 'docker_it999_va_jd_db_BACKUP_20250305162001.sql', 1, '关联dump文件「基础非交易-金融服务-数据服务」_for_远相。zt@2025-03-05');
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-03-05 10:11:12', 'huaitian.zhang', '2025-03-05 10:11:12', 0, 1822, 'FPC_bugfix-20250304', 'FPC-DATA_dev', 306, 12322, 1, 'docker_it999_va_jd_db_BACKUP_20250305162001.sql', 1, '关联dump文件「基础非交易-金融服务-数据服务」_for_远相。zt@2025-03-05');
select * from db_mgt_dump_file where id in (1821, 1822);

-- va_fund	***************	/data/dmp/mysql/docker_it999_va_fund_db_BACKUP_20250305162001.sql;
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-03-05 10:11:12', 'huaitian.zhang', '2025-03-05 10:11:12', 0, 1819, 'FPC_bugfix-20250304', 'FPC-DATA_master', 303, 12246, 1, 'docker_it999_va_fund_db_BACKUP_20250305162001.sql', 1, '关联dump文件「基础非交易-金融服务-数据服务」_for_远相。zt@2025-03-05');
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-03-05 10:11:12', 'huaitian.zhang', '2025-03-05 10:11:12', 0, 1820, 'FPC_bugfix-20250304', 'FPC-DATA_dev', 303, 12246, 1, 'docker_it999_va_fund_db_BACKUP_20250305162001.sql', 1, '关联dump文件「基础非交易-金融服务-数据服务」_for_远相。zt@2025-03-05');
select * from db_mgt_dump_file where id in (1819, 1820);

-- st_otc	***************	/data/dmp/mysql/docker_it999_st_otc_db_BACKUP_20250305162001.sql;
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-03-05 10:11:12', 'huaitian.zhang', '2025-03-05 10:11:12', 0, 1817, 'FPC_bugfix-20250304', 'FPC-DATA_master', 336, 21188, 1, 'docker_it999_st_otc_db_BACKUP_20250305162001.sql', 1, '关联dump文件「基础非交易-金融服务-数据服务」_for_远相。zt@2025-03-05');
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-03-05 10:11:12', 'huaitian.zhang', '2025-03-05 10:11:12', 0, 1818, 'FPC_bugfix-20250304', 'FPC-DATA_dev', 336, 21188, 1, 'docker_it999_st_otc_db_BACKUP_20250305162001.sql', 1, '关联dump文件「基础非交易-金融服务-数据服务」_for_远相。zt@2025-03-05');
select * from db_mgt_dump_file where id in (1817, 1818);

-- st_fund	***************	/data/dmp/mysql/docker_it999_st_fund_db_BACKUP_20250305162001.sql;
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-03-05 10:11:12', 'huaitian.zhang', '2025-03-05 10:11:12', 0, 1815, 'FPC_bugfix-20250304', 'FPC-DATA_master', 294, 7306, 1, 'docker_it999_st_fund_db_BACKUP_20250305162001.sql', 1, '关联dump文件「基础非交易-金融服务-数据服务」_for_远相。zt@2025-03-05');
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-03-05 10:11:12', 'huaitian.zhang', '2025-03-05 10:11:12', 0, 1816, 'FPC_bugfix-20250304', 'FPC-DATA_dev', 294, 7306, 1, 'docker_it999_st_fund_db_BACKUP_20250305162001.sql', 1, '关联dump文件「基础非交易-金融服务-数据服务」_for_远相。zt@2025-03-05');
select * from db_mgt_dump_file where id in (1815, 1816);

-- va_pj	**************	/data/dmp/mysql/docker_it999_va_pj_db_BACKUP_20250305162001.sql;
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-03-05 10:11:12', 'huaitian.zhang', '2025-03-05 10:11:12', 0, 1813, 'FPC_bugfix-20250304', 'FPC-DATA_master', 334, 21034, 1, 'docker_it999_va_pj_db_BACKUP_20250305162001.sql', 1, '关联dump文件「基础非交易-金融服务-数据服务」_for_远相。zt@2025-03-05');
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-03-05 10:11:12', 'huaitian.zhang', '2025-03-05 10:11:12', 0, 1814, 'FPC_bugfix-20250304', 'FPC-DATA_dev', 334, 21034, 1, 'docker_it999_va_pj_db_BACKUP_20250305162001.sql', 1, '关联dump文件「基础非交易-金融服务-数据服务」_for_远相。zt@2025-03-05');
select * from db_mgt_dump_file where id in (1813, 1814);

-- va_osf	**************	/data/dmp/mysql/docker_it999_va_osf_db_BACKUP_20250305162001.sql;
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-03-05 10:11:12', 'huaitian.zhang', '2025-03-05 10:11:12', 0, 1811, 'FPC_bugfix-20250304', 'FPC-DATA_master', 331, 20880, 1, 'docker_it999_va_osf_db_BACKUP_20250305162001.sql', 1, '关联dump文件「基础非交易-金融服务-数据服务」_for_远相。zt@2025-03-05');
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-03-05 10:11:12', 'huaitian.zhang', '2025-03-05 10:11:12', 0, 1812, 'FPC_bugfix-20250304', 'FPC-DATA_dev', 331, 20880, 1, 'docker_it999_va_osf_db_BACKUP_20250305162001.sql', 1, '关联dump文件「基础非交易-金融服务-数据服务」_for_远相。zt@2025-03-05');
select * from db_mgt_dump_file where id in (1811, 1812);

-- va_high	**************	/data/dmp/mysql/docker_it999_va_high_db_BACKUP_20250305162001.sql;
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-03-05 10:11:12', 'huaitian.zhang', '2025-03-05 10:11:12', 0, 1809, 'FPC_bugfix-20250304', 'FPC-DATA_master', 304, 12018, 1, 'docker_it999_va_high_db_BACKUP_20250305162001.sql', 1, '关联dump文件「基础非交易-金融服务-数据服务」_for_远相。zt@2025-03-05');
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-03-05 10:11:12', 'huaitian.zhang', '2025-03-05 10:11:12', 0, 1810, 'FPC_bugfix-20250304', 'FPC-DATA_dev', 304, 12018, 1, 'docker_it999_va_high_db_BACKUP_20250305162001.sql', 1, '关联dump文件「基础非交易-金融服务-数据服务」_for_远相。zt@2025-03-05');
select * from db_mgt_dump_file where id in (1809, 1810);

-- va_fof	**************	/data/dmp/mysql/docker_it999_va_fof_db_BACKUP_20250305162001.sql;
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-03-05 10:11:12', 'huaitian.zhang', '2025-03-05 10:11:12', 0, 1807, 'FPC_bugfix-20250304', 'FPC-DATA_master', 302, 11942, 1, 'docker_it999_va_fof_db_BACKUP_20250305162001.sql', 1, '关联dump文件「基础非交易-金融服务-数据服务」_for_远相。zt@2025-03-05');
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-03-05 10:11:12', 'huaitian.zhang', '2025-03-05 10:11:12', 0, 1808, 'FPC_bugfix-20250304', 'FPC-DATA_dev', 302, 11942, 1, 'docker_it999_va_fof_db_BACKUP_20250305162001.sql', 1, '关联dump文件「基础非交易-金融服务-数据服务」_for_远相。zt@2025-03-05');
select * from db_mgt_dump_file where id in (1807, 1808);

-- st_pj	**************	/data/dmp/mysql/docker_it999_st_pj_db_BACKUP_20250305162001.sql;
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-03-05 10:11:12', 'huaitian.zhang', '2025-03-05 10:11:12', 0, 1805, 'FPC_bugfix-20250304', 'FPC-DATA_master', 335, 21111, 1, 'docker_it999_st_pj_db_BACKUP_20250305162001.sql', 1, '关联dump文件「基础非交易-金融服务-数据服务」_for_远相。zt@2025-03-05');
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-03-05 10:11:12', 'huaitian.zhang', '2025-03-05 10:11:12', 0, 1806, 'FPC_bugfix-20250304', 'FPC-DATA_dev', 335, 21111, 1, 'docker_it999_st_pj_db_BACKUP_20250305162001.sql', 1, '关联dump文件「基础非交易-金融服务-数据服务」_for_远相。zt@2025-03-05');
select * from db_mgt_dump_file where id in (1805, 1806);

-- st_pe	**************	/data/dmp/mysql/docker_it999_st_pe_db_BACKUP_20250305162001.sql;
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-03-05 10:11:12', 'huaitian.zhang', '2025-03-05 10:11:12', 0, 1803, 'FPC_bugfix-20250304', 'FPC-DATA_master', 300, 7534, 1, 'docker_it999_st_pe_db_BACKUP_20250305162001.sql', 1, '关联dump文件「基础非交易-金融服务-数据服务」_for_远相。zt@2025-03-05');
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-03-05 10:11:12', 'huaitian.zhang', '2025-03-05 10:11:12', 0, 1804, 'FPC_bugfix-20250304', 'FPC-DATA_dev', 300, 7534, 1, 'docker_it999_st_pe_db_BACKUP_20250305162001.sql', 1, '关联dump文件「基础非交易-金融服务-数据服务」_for_远相。zt@2025-03-05');
select * from db_mgt_dump_file where id in (1803, 1804);

-- st_osf	**************	/data/dmp/mysql/docker_it999_st_osf_db_BACKUP_20250305162001.sql;
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-03-05 10:11:12', 'huaitian.zhang', '2025-03-05 10:11:12', 0, 1801, 'FPC_bugfix-20250304', 'FPC-DATA_master', 333, 20957, 1, 'docker_it999_st_osf_db_BACKUP_20250305162001.sql', 1, '关联dump文件「基础非交易-金融服务-数据服务」_for_远相。zt@2025-03-05');
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-03-05 10:11:12', 'huaitian.zhang', '2025-03-05 10:11:12', 0, 1802, 'FPC_bugfix-20250304', 'FPC-DATA_dev', 333, 20957, 1, 'docker_it999_st_osf_db_BACKUP_20250305162001.sql', 1, '关联dump文件「基础非交易-金融服务-数据服务」_for_远相。zt@2025-03-05');
select * from db_mgt_dump_file where id in (1801, 1802);

-- st_high	**************	/data/dmp/mysql/docker_it999_st_high_db_BACKUP_20250305162001.sql;
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-03-05 10:11:12', 'huaitian.zhang', '2025-03-05 10:11:12', 0, 1799, 'FPC_bugfix-20250304', 'FPC-DATA_master', 296, 7686, 1, 'docker_it999_st_high_db_BACKUP_20250305162001.sql', 1, '关联dump文件「基础非交易-金融服务-数据服务」_for_远相。zt@2025-03-05');
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-03-05 10:11:12', 'huaitian.zhang', '2025-03-05 10:11:12', 0, 1800, 'FPC_bugfix-20250304', 'FPC-DATA_dev', 296, 7686, 1, 'docker_it999_st_high_db_BACKUP_20250305162001.sql', 1, '关联dump文件「基础非交易-金融服务-数据服务」_for_远相。zt@2025-03-05');
select * from db_mgt_dump_file where id in (1799, 1800);

-- st_hedge	**************	/data/dmp/mysql/docker_it999_st_hedge_db_BACKUP_20250305162001.sql;
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-03-05 10:11:12', 'huaitian.zhang', '2025-03-05 10:11:12', 0, 1797, 'FPC_bugfix-20250304', 'FPC-DATA_master', 295, 7458, 1, 'docker_it999_st_hedge_db_BACKUP_20250305162001.sql', 1, '关联dump文件「基础非交易-金融服务-数据服务」_for_远相。zt@2025-03-05');
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-03-05 10:11:12', 'huaitian.zhang', '2025-03-05 10:11:12', 0, 1798, 'FPC_bugfix-20250304', 'FPC-DATA_dev', 295, 7458, 1, 'docker_it999_st_hedge_db_BACKUP_20250305162001.sql', 1, '关联dump文件「基础非交易-金融服务-数据服务」_for_远相。zt@2025-03-05');
select * from db_mgt_dump_file where id in (1797, 1798);

-- st_fof	**************	/data/dmp/mysql/docker_it999_st_fof_db_BACKUP_20250305162001.sql;
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-03-05 10:11:12', 'huaitian.zhang', '2025-03-05 10:11:12', 0, 1795, 'FPC_bugfix-20250304', 'FPC-DATA_master', 293, 7762, 1, 'docker_it999_st_fof_db_BACKUP_20250305162001.sql', 1, '关联dump文件「基础非交易-金融服务-数据服务」_for_远相。zt@2025-03-05');
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-03-05 10:11:12', 'huaitian.zhang', '2025-03-05 10:11:12', 0, 1796, 'FPC_bugfix-20250304', 'FPC-DATA_dev', 293, 7762, 1, 'docker_it999_st_fof_db_BACKUP_20250305162001.sql', 1, '关联dump文件「基础非交易-金融服务-数据服务」_for_远相。zt@2025-03-05');
select * from db_mgt_dump_file where id in (1795, 1796);

-- st_fixed	**************	/data/dmp/mysql/docker_it999_st_fixed_db_BACKUP_20250305162001.sql;
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-03-05 10:11:12', 'huaitian.zhang', '2025-03-05 10:11:12', 0, 1793, 'FPC_bugfix-20250304', 'FPC-DATA_master', 292, 8066, 1, 'docker_it999_st_fixed_db_BACKUP_20250305162001.sql', 1, '关联dump文件「基础非交易-金融服务-数据服务」_for_远相。zt@2025-03-05');
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-03-05 10:11:12', 'huaitian.zhang', '2025-03-05 10:11:12', 0, 1794, 'FPC_bugfix-20250304', 'FPC-DATA_dev', 292, 8066, 1, 'docker_it999_st_fixed_db_BACKUP_20250305162001.sql', 1, '关联dump文件「基础非交易-金融服务-数据服务」_for_远相。zt@2025-03-05');
select * from db_mgt_dump_file where id in (1793, 1794);

-- smpp	***************	/data/dmp/mysql/docker_it999_smpp_db_BACKUP_20250305162001.sql;
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-03-05 10:11:12', 'huaitian.zhang', '2025-03-05 10:11:12', 0, 1791, 'FPC_bugfix-20250304', 'FPC-DATA_master', 290, 11410, 1, 'docker_it999_smpp_db_BACKUP_20250305162001.sql', 1, '关联dump文件「基础非交易-金融服务-数据服务」_for_远相。zt@2025-03-05');
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-03-05 10:11:12', 'huaitian.zhang', '2025-03-05 10:11:12', 0, 1792, 'FPC_bugfix-20250304', 'FPC-DATA_dev', 290, 11410, 1, 'docker_it999_smpp_db_BACKUP_20250305162001.sql', 1, '关联dump文件「基础非交易-金融服务-数据服务」_for_远相。zt@2025-03-05');
select * from db_mgt_dump_file where id in (1791, 1792);
-- st_portfolio	**************	/data/dmp/mysql/docker_it999_st_portfolio_db_BACKUP_20250305162001.sql;
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-03-05 10:11:12', 'huaitian.zhang', '2025-03-05 10:11:12', 0, 1789, 'FPC_bugfix-20250304', 'FPC-DATA_master', 301, 7990, 1, 'docker_it999_st_portfolio_db_BACKUP_20250305162001.sql', 1, '关联dump文件「基础非交易-金融服务-数据服务」_for_远相。zt@2025-03-05');
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-03-05 10:11:12', 'huaitian.zhang', '2025-03-05 10:11:12', 0, 1790, 'FPC_bugfix-20250304', 'FPC-DATA_dev', 301, 7990, 1, 'docker_it999_st_portfolio_db_BACKUP_20250305162001.sql', 1, '关联dump文件「基础非交易-金融服务-数据服务」_for_远相。zt@2025-03-05');
select * from db_mgt_dump_file where id in (1789, 1790);
-- hsjy_jj	**************	/data/dmp/mysql/docker_it999_hsjy_jj_db_BACKUP_20250305162001.sql;
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-03-05 10:11:12', 'huaitian.zhang', '2025-03-05 10:11:12', 0, 1787, 'FPC_bugfix-20250304', 'FPC-DATA_master', 288, 12170, 1, 'docker_it999_hsjy_jj_db_BACKUP_20250305162001.sql', 1, '关联dump文件「基础非交易-金融服务-数据服务」_for_远相。zt@2025-03-05');
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-03-05 10:11:12', 'huaitian.zhang', '2025-03-05 10:11:12', 0, 1788, 'FPC_bugfix-20250304', 'FPC-DATA_dev', 288, 12170, 1, 'docker_it999_hsjy_jj_db_BACKUP_20250305162001.sql', 1, '关联dump文件「基础非交易-金融服务-数据服务」_for_远相。zt@2025-03-05');
select * from db_mgt_dump_file where id in (1787, 1788);
-- hsjy_gg	**************	/data/dmp/mysql/docker_it999_hsjy_gg_db_BACKUP_20250305162001.sql;
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-03-05 10:11:12', 'huaitian.zhang', '2025-03-05 10:11:12', 0, 1785, 'FPC_bugfix-20250304', 'FPC-DATA_master', 332, 20816, 1, 'docker_it999_hsjy_gg_db_BACKUP_20250305162001.sql', 1, '关联dump文件「基础非交易-金融服务-数据服务」_for_远相。zt@2025-03-05');
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-03-05 10:11:12', 'huaitian.zhang', '2025-03-05 10:11:12', 0, 1786, 'FPC_bugfix-20250304', 'FPC-DATA_dev', 332, 20816, 1, 'docker_it999_hsjy_gg_db_BACKUP_20250305162001.sql', 1, '关联dump文件「基础非交易-金融服务-数据服务」_for_远相。zt@2025-03-05');
select * from db_mgt_dump_file where id in (1785, 1786);
-- va_market	**************	/data/dmp/mysql/docker_it999_va_market_db_BACKUP_20250305162001.sql;
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-03-05 10:11:12', 'huaitian.zhang', '2025-03-05 10:11:12', 0, 1783, 'FPC_bugfix-20250304', 'FPC-DATA_master', 308, 11866, 1, 'docker_it999_va_market_db_BACKUP_20250305162001.sql', 1, '关联dump文件「基础非交易-金融服务-数据服务」_for_远相。zt@2025-03-05');
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-03-05 10:11:12', 'huaitian.zhang', '2025-03-05 10:11:12', 0, 1784, 'FPC_bugfix-20250304', 'FPC-DATA_dev', 308, 11866, 1, 'docker_it999_va_market_db_BACKUP_20250305162001.sql', 1, '关联dump文件「基础非交易-金融服务-数据服务」_for_远相。zt@2025-03-05');
select * from db_mgt_dump_file where id in (1783, 1784);
-- va_main	**************	/data/dmp/mysql/docker_it999_va_main_db_BACKUP_20250305162001.sql;
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-03-05 10:11:12', 'huaitian.zhang', '2025-03-05 10:11:12', 0, 1781, 'FPC_bugfix-20250304', 'FPC-DATA_master', 307, 11790, 1, 'docker_it999_va_main_db_BACKUP_20250305162001.sql', 1, '关联dump文件「基础非交易-金融服务-数据服务」_for_远相。zt@2025-03-05');
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-03-05 10:11:12', 'huaitian.zhang', '2025-03-05 10:11:12', 0, 1782, 'FPC_bugfix-20250304', 'FPC-DATA_dev', 307, 11790, 1, 'docker_it999_va_main_db_BACKUP_20250305162001.sql', 1, '关联dump文件「基础非交易-金融服务-数据服务」_for_远相。zt@2025-03-05');
select * from db_mgt_dump_file where id in (1781, 1782);
-- va_insurance	**************	/data/dmp/mysql/docker_it999_va_insurance_db_BACKUP_20250305162001.sql;
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-03-05 10:11:12', 'huaitian.zhang', '2025-03-05 10:11:12', 0, 1779, 'FPC_bugfix-20250304', 'FPC-DATA_master', 305, 11714, 1, 'docker_it999_va_insurance_db_BACKUP_20250305162001.sql', 1, '关联dump文件「基础非交易-金融服务-数据服务」_for_远相。zt@2025-03-05');
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-03-05 10:11:12', 'huaitian.zhang', '2025-03-05 10:11:12', 0, 1780, 'FPC_bugfix-20250304', 'FPC-DATA_dev', 305, 11714, 1, 'docker_it999_va_insurance_db_BACKUP_20250305162001.sql', 1, '关联dump文件「基础非交易-金融服务-数据服务」_for_远相。zt@2025-03-05');
select * from db_mgt_dump_file where id in (1779, 1780);
-- support	**************	/data/dmp/mysql/docker_it999_support_db_BACKUP_20250305162001.sql;
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-03-05 10:11:12', 'huaitian.zhang', '2025-03-05 10:11:12', 0, 1777, 'FPC_bugfix-20250304', 'FPC-DATA_master', 271, 7154, 1, 'docker_it999_support_db_BACKUP_20250305162001.sql', 1, '关联dump文件「基础非交易-金融服务-数据服务」_for_远相。zt@2025-03-05');
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-03-05 10:11:12', 'huaitian.zhang', '2025-03-05 10:11:12', 0, 1778, 'FPC_bugfix-20250304', 'FPC-DATA_dev', 271, 7154, 1, 'docker_it999_support_db_BACKUP_20250305162001.sql', 1, '关联dump文件「基础非交易-金融服务-数据服务」_for_远相。zt@2025-03-05');
select * from db_mgt_dump_file where id in (1777, 1778);

-- st_market	**************	/data/dmp/mysql/docker_it999_st_market_db_BACKUP_20250305162001.sql;
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-03-05 10:11:12', 'huaitian.zhang', '2025-03-05 10:11:12', 0, 1775, 'FPC_bugfix-20250304', 'FPC-DATA_master', 299, 7838, 1, 'docker_it999_st_market_db_BACKUP_20250305162001.sql', 1, '关联dump文件「基础非交易-金融服务-数据服务」_for_远相。zt@2025-03-05');
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-03-05 10:11:12', 'huaitian.zhang', '2025-03-05 10:11:12', 0, 1776, 'FPC_bugfix-20250304', 'FPC-DATA_dev', 299, 7838, 1, 'docker_it999_st_market_db_BACKUP_20250305162001.sql', 1, '关联dump文件「基础非交易-金融服务-数据服务」_for_远相。zt@2025-03-05');
select * from db_mgt_dump_file where id in (1775, 1776);

-- st_main	**************	/data/dmp/mysql/docker_it999_st_main_db_BACKUP_20250305162001.sql;
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-03-05 10:11:12', 'huaitian.zhang', '2025-03-05 10:11:12', 0, 1773, 'FPC_bugfix-20250304', 'FPC-DATA_master', 298, 7230, 1, 'docker_it999_st_main_db_BACKUP_20250305162001.sql', 1, '关联dump文件「基础非交易-金融服务-数据服务」_for_远相。zt@2025-03-05');
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-03-05 10:11:12', 'huaitian.zhang', '2025-03-05 10:11:12', 0, 1774, 'FPC_bugfix-20250304', 'FPC-DATA_dev', 298, 7230, 1, 'docker_it999_st_main_db_BACKUP_20250305162001.sql', 1, '关联dump文件「基础非交易-金融服务-数据服务」_for_远相。zt@2025-03-05');
select * from db_mgt_dump_file where id in (1773, 1774);

-- st_insurance	**************	/data/dmp/mysql/docker_it999_st_insurance_db_BACKUP_20250305162001.sql;
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-03-05 10:11:12', 'huaitian.zhang', '2025-03-05 10:11:12', 0, 1771, 'FPC_bugfix-20250304', 'FPC-DATA_master', 297, 7382, 1, 'docker_it999_st_insurance_db_BACKUP_20250305162001.sql', 1, '关联dump文件「基础非交易-金融服务-数据服务」_for_远相。zt@2025-03-05');
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-03-05 10:11:12', 'huaitian.zhang', '2025-03-05 10:11:12', 0, 1772, 'FPC_bugfix-20250304', 'FPC-DATA_dev', 297, 7382, 1, 'docker_it999_st_insurance_db_BACKUP_20250305162001.sql', 1, '关联dump文件「基础非交易-金融服务-数据服务」_for_远相。zt@2025-03-05');
select * from db_mgt_dump_file where id in (1771, 1772);
-- st_ashare	**************	/data/dmp/mysql/docker_it999_st_ashare_db_BACKUP_20250305162001.sql;
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-03-05 10:11:12', 'huaitian.zhang', '2025-03-05 10:11:12', 0, 1769, 'FPC_bugfix-20250304', 'FPC-DATA_master', 291, 7610, 1, 'docker_it999_st_ashare_db_BACKUP_20250305162001.sql', 1, '关联dump文件「基础非交易-金融服务-数据服务」_for_远相。zt@2025-03-05');
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-03-05 10:11:12', 'huaitian.zhang', '2025-03-05 10:11:12', 0, 1770, 'FPC_bugfix-20250304', 'FPC-DATA_dev', 291, 7610, 1, 'docker_it999_st_ashare_db_BACKUP_20250305162001.sql', 1, '关联dump文件「基础非交易-金融服务-数据服务」_for_远相。zt@2025-03-05');
select * from db_mgt_dump_file where id in (1769, 1770);
-- fpc_sync	**************	/data/dmp/mysql/docker_it999_fpc_sync_db_BACKUP_20250305162001.sql;
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-03-05 10:11:12', 'huaitian.zhang', '2025-03-05 10:11:12', 0, 176, 'FPC_bugfix-20250304', 'FPC-DATA_master', 286, 11638, 1, 'docker_it999_fpc_sync_db_BACKUP_20250305162001.sql', 1, '关联dump文件「基础非交易-金融服务-数据服务」_for_远相。zt@2025-03-05');
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-03-05 10:11:12', 'huaitian.zhang', '2025-03-05 10:11:12', 0, 1768, 'FPC_bugfix-20250304', 'FPC-DATA_dev', 286, 11638, 1, 'docker_it999_fpc_sync_db_BACKUP_20250305162001.sql', 1, '关联dump文件「基础非交易-金融服务-数据服务」_for_远相。zt@2025-03-05');
select * from db_mgt_dump_file where id in (1767, 1768);
-- fpc_manage	**************	/data/dmp/mysql/docker_it999_fpc_manage_db_BACKUP_20250305162001.sql;
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-03-05 10:11:12', 'huaitian.zhang', '2025-03-05 10:11:12', 0, 1765, 'FPC_bugfix-20250304', 'FPC-DATA_master', 284, 11486, 1, 'docker_it999_fpc_manage_db_BACKUP_20250305162001.sql', 1, '关联dump文件「基础非交易-金融服务-数据服务」_for_远相。zt@2025-03-05');
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-03-05 10:11:12', 'huaitian.zhang', '2025-03-05 10:11:12', 0, 1766, 'FPC_bugfix-20250304', 'FPC-DATA_dev', 284, 11486, 1, 'docker_it999_fpc_manage_db_BACKUP_20250305162001.sql', 1, '关联dump文件「基础非交易-金融服务-数据服务」_for_远相。zt@2025-03-05');
select * from db_mgt_dump_file where id in (1765, 1766);
-- ac_wind	***************	/data/dmp/mysql/docker_it999_ac_wind_db_BACKUP_20250305162001.sql;
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-03-05 10:11:12', 'huaitian.zhang', '2025-03-05 10:11:12', 0, 1763, 'FPC_bugfix-20250304', 'FPC-DATA_master', 320, 11106, 1, 'docker_it999_ac_wind_db_BACKUP_20250305162001.sql', 1, '关联dump文件「基础非交易-金融服务-数据服务」_for_远相。zt@2025-03-05');
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-03-05 10:11:12', 'huaitian.zhang', '2025-03-05 10:11:12', 0, 1764, 'FPC_bugfix-20250304', 'FPC-DATA_dev', 320, 11106, 1, 'docker_it999_ac_wind_db_BACKUP_20250305162001.sql', 1, '关联dump文件「基础非交易-金融服务-数据服务」_for_远相。zt@2025-03-05');
select * from db_mgt_dump_file where id in (1763, 1764);
-- ac_smpp	***************	/data/dmp/mysql/docker_it999_ac_smpp_db_BACKUP_20250305162001.sql;
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-03-05 10:11:12', 'huaitian.zhang', '2025-03-05 10:11:12', 0, 1761, 'FPC_bugfix-20250304', 'FPC-DATA_master', 319, 11030, 1, 'docker_it999_ac_smpp_db_BACKUP_20250305162001.sql', 1, '关联dump文件「基础非交易-金融服务-数据服务」_for_远相。zt@2025-03-05');
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-03-05 10:11:12', 'huaitian.zhang', '2025-03-05 10:11:12', 0, 1762, 'FPC_bugfix-20250304', 'FPC-DATA_dev', 319, 11030, 1, 'docker_it999_ac_smpp_db_BACKUP_20250305162001.sql', 1, '关联dump文件「基础非交易-金融服务-数据服务」_for_远相。zt@2025-03-05');
select * from db_mgt_dump_file where id in (1761, 1762);
-- ac_reptile	***************	/data/dmp/mysql/docker_it999_ac_reptile_db_BACKUP_20250305162001.sql;
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-03-05 10:11:12', 'huaitian.zhang', '2025-03-05 10:11:12', 0, 1759, 'FPC_bugfix-20250304', 'FPC-DATA_master', 318, 9966, 1, 'docker_it999_ac_reptile_db_BACKUP_20250305162001.sql', 1, '关联dump文件「基础非交易-金融服务-数据服务」_for_远相。zt@2025-03-05');
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-03-05 10:11:12', 'huaitian.zhang', '2025-03-05 10:11:12', 0, 1760, 'FPC_bugfix-20250304', 'FPC-DATA_dev', 318, 9966, 1, 'docker_it999_ac_reptile_db_BACKUP_20250305162001.sql', 1, '关联dump文件「基础非交易-金融服务-数据服务」_for_远相。zt@2025-03-05');
select * from db_mgt_dump_file where id in (1759, 1760);
-- ac_hsjyjj	***************	/data/dmp/mysql/docker_it999_ac_hsjyjj_db_BACKUP_20250305162001.sql;
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-03-05 10:11:12', 'huaitian.zhang', '2025-03-05 10:11:12', 0, 1757, 'FPC_bugfix-20250304', 'FPC-DATA_master', 317, 10954, 1, 'docker_it999_ac_hsjyjj_db_BACKUP_20250305162001.sql', 1, '关联dump文件「基础非交易-金融服务-数据服务」_for_远相。zt@2025-03-05');
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-03-05 10:11:12', 'huaitian.zhang', '2025-03-05 10:11:12', 0, 1758, 'FPC_bugfix-20250304', 'FPC-DATA_dev', 317, 10954, 1, 'docker_it999_ac_hsjyjj_db_BACKUP_20250305162001.sql', 1, '关联dump文件「基础非交易-金融服务-数据服务」_for_远相。zt@2025-03-05');
select * from db_mgt_dump_file where id in (1757, 1758);
-- ac_hsjygg: logic_id = 316, db_info_id = 10878,
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-03-05 10:11:12', 'huaitian.zhang', '2025-03-05 10:11:12', 0, 1755, 'FPC_bugfix-20250304', 'FPC-DATA_master', 316, 10878, 1, 'docker_it999_ac_hsjygg_db_BACKUP_20250305162001.sql', 1, '关联dump文件「基础非交易-金融服务-数据服务」_for_远相。zt@2025-03-05');
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-03-05 10:11:12', 'huaitian.zhang', '2025-03-05 10:11:12', 0, 1756, 'FPC_bugfix-20250304', 'FPC-DATA_dev', 316, 10878, 1, 'docker_it999_ac_hsjygg_db_BACKUP_20250305162001.sql', 1, '关联dump文件「基础非交易-金融服务-数据服务」_for_远相。zt@2025-03-05');
select * from db_mgt_dump_file where id in (1755, 1756);
-- ac_fof: logic_id = 315, db_info_id = 9890,
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-03-05 10:11:12', 'huaitian.zhang', '2025-03-05 10:11:12', 0, 1753, 'FPC_bugfix-20250304', 'FPC-DATA_master', 315, 9890, 1, 'docker_it999_ac_fof_db_BACKUP_20250305162001.sql', 1, '关联dump文件「基础非交易-金融服务-数据服务」_for_远相。zt@2025-03-05');
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-03-05 10:11:12', 'huaitian.zhang', '2025-03-05 10:11:12', 0, 1754, 'FPC_bugfix-20250304', 'FPC-DATA_dev', 315, 9890, 1, 'docker_it999_ac_fof_db_BACKUP_20250305162001.sql', 1, '关联dump文件「基础非交易-金融服务-数据服务」_for_远相。zt@2025-03-05');
select * from db_mgt_dump_file where id in (1753, 1754);
-- ac_fina: logic_id = 314, db_info_id = 10726,
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-03-05 10:11:12', 'huaitian.zhang', '2025-03-05 10:11:12', 0, 1751, 'FPC_bugfix-20250304', 'FPC-DATA_master', 314, 10726, 1, 'docker_it999_ac_fina_db_BACKUP_20250305162001.sql', 1, '关联dump文件「基础非交易-金融服务-数据服务」_for_远相。zt@2025-03-05');
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-03-05 10:11:12', 'huaitian.zhang', '2025-03-05 10:11:12', 0, 1752, 'FPC_bugfix-20250304', 'FPC-DATA_dev', 314, 10726, 1, 'docker_it999_ac_fina_db_BACKUP_20250305162001.sql', 1, '关联dump文件「基础非交易-金融服务-数据服务」_for_远相。zt@2025-03-05');
select * from db_mgt_dump_file where id in (1751, 1752);







