INSERT INTO `env_mgt_node_bind` (`module_name`, `suite_id`, `node_id`, `node_port`, `node_docker`, `node_bind_desc`, `deploy_group`, `deploy_type`, `deploy_path`, `health_check_url`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `tomcat_path`, `script_path`, `script_name`, `config_path`, `log_path`, `enable_bind`) VALUES('nf-newpig','35',NULL,'0','tms02',NULL,NULL,'2',NULL,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1');
INSERT INTO `env_mgt_node_bind` (`module_name`, `suite_id`, `node_id`, `node_port`, `node_docker`, `node_bind_desc`, `deploy_group`, `deploy_type`, `deploy_path`, `health_check_url`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `tomcat_path`, `script_path`, `script_name`, `config_path`, `log_path`, `enable_bind`) VALUES('nf-newpig','36',NULL,'0','tms03',NULL,NULL,'2',NULL,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1');
INSERT INTO `env_mgt_node_bind` (`module_name`, `suite_id`, `node_id`, `node_port`, `node_docker`, `node_bind_desc`, `deploy_group`, `deploy_type`, `deploy_path`, `health_check_url`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `tomcat_path`, `script_path`, `script_name`, `config_path`, `log_path`, `enable_bind`) VALUES('nf-newpig','37',NULL,'0','tms04',NULL,NULL,'2',NULL,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1');
INSERT INTO `env_mgt_node_bind` (`module_name`, `suite_id`, `node_id`, `node_port`, `node_docker`, `node_bind_desc`, `deploy_group`, `deploy_type`, `deploy_path`, `health_check_url`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `tomcat_path`, `script_path`, `script_name`, `config_path`, `log_path`, `enable_bind`) VALUES('nf-newpig','38',NULL,'0','tms05',NULL,NULL,'2',NULL,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1');
INSERT INTO `env_mgt_node_bind` (`module_name`, `suite_id`, `node_id`, `node_port`, `node_docker`, `node_bind_desc`, `deploy_group`, `deploy_type`, `deploy_path`, `health_check_url`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `tomcat_path`, `script_path`, `script_name`, `config_path`, `log_path`, `enable_bind`) VALUES('nf-newpig','39',NULL,'0','tms06',NULL,NULL,'2',NULL,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1');
INSERT INTO `env_mgt_node_bind` (`module_name`, `suite_id`, `node_id`, `node_port`, `node_docker`, `node_bind_desc`, `deploy_group`, `deploy_type`, `deploy_path`, `health_check_url`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `tomcat_path`, `script_path`, `script_name`, `config_path`, `log_path`, `enable_bind`) VALUES('nf-newpig','40',NULL,'0','tms07',NULL,NULL,'2',NULL,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1');
INSERT INTO `env_mgt_node_bind` (`module_name`, `suite_id`, `node_id`, `node_port`, `node_docker`, `node_bind_desc`, `deploy_group`, `deploy_type`, `deploy_path`, `health_check_url`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `tomcat_path`, `script_path`, `script_name`, `config_path`, `log_path`, `enable_bind`) VALUES('nf-newpig','41',NULL,'0','tms08',NULL,NULL,'2',NULL,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1');
INSERT INTO `env_mgt_node_bind` (`module_name`, `suite_id`, `node_id`, `node_port`, `node_docker`, `node_bind_desc`, `deploy_group`, `deploy_type`, `deploy_path`, `health_check_url`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `tomcat_path`, `script_path`, `script_name`, `config_path`, `log_path`, `enable_bind`) VALUES('nf-newpig','42',NULL,'0','tms09',NULL,NULL,'2',NULL,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1');
INSERT INTO `env_mgt_node_bind` (`module_name`, `suite_id`, `node_id`, `node_port`, `node_docker`, `node_bind_desc`, `deploy_group`, `deploy_type`, `deploy_path`, `health_check_url`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `tomcat_path`, `script_path`, `script_name`, `config_path`, `log_path`, `enable_bind`) VALUES('nf-newpig','43',NULL,'0','tms10',NULL,NULL,'2',NULL,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1');
INSERT INTO `env_mgt_node_bind` (`module_name`, `suite_id`, `node_id`, `node_port`, `node_docker`, `node_bind_desc`, `deploy_group`, `deploy_type`, `deploy_path`, `health_check_url`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `tomcat_path`, `script_path`, `script_name`, `config_path`, `log_path`, `enable_bind`) VALUES('nf-newpig','44',NULL,'0','tms11',NULL,NULL,'2',NULL,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1');
INSERT INTO `env_mgt_node_bind` (`module_name`, `suite_id`, `node_id`, `node_port`, `node_docker`, `node_bind_desc`, `deploy_group`, `deploy_type`, `deploy_path`, `health_check_url`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `tomcat_path`, `script_path`, `script_name`, `config_path`, `log_path`, `enable_bind`) VALUES('nf-newpig','45',NULL,'0','tms12',NULL,NULL,'2',NULL,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1');
INSERT INTO `env_mgt_node_bind` (`module_name`, `suite_id`, `node_id`, `node_port`, `node_docker`, `node_bind_desc`, `deploy_group`, `deploy_type`, `deploy_path`, `health_check_url`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `tomcat_path`, `script_path`, `script_name`, `config_path`, `log_path`, `enable_bind`) VALUES('nf-newpig','46',NULL,'0','tms13',NULL,NULL,'2',NULL,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1');
INSERT INTO `env_mgt_node_bind` (`module_name`, `suite_id`, `node_id`, `node_port`, `node_docker`, `node_bind_desc`, `deploy_group`, `deploy_type`, `deploy_path`, `health_check_url`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `tomcat_path`, `script_path`, `script_name`, `config_path`, `log_path`, `enable_bind`) VALUES('nf-newpig','47',NULL,'0','tms14',NULL,NULL,'2',NULL,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1');
INSERT INTO `env_mgt_node_bind` (`module_name`, `suite_id`, `node_id`, `node_port`, `node_docker`, `node_bind_desc`, `deploy_group`, `deploy_type`, `deploy_path`, `health_check_url`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `tomcat_path`, `script_path`, `script_name`, `config_path`, `log_path`, `enable_bind`) VALUES('nf-newpig','48',NULL,'0','tms15',NULL,NULL,'2',NULL,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1');
INSERT INTO `env_mgt_node_bind` (`module_name`, `suite_id`, `node_id`, `node_port`, `node_docker`, `node_bind_desc`, `deploy_group`, `deploy_type`, `deploy_path`, `health_check_url`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `tomcat_path`, `script_path`, `script_name`, `config_path`, `log_path`, `enable_bind`) VALUES('nf-newpig','49',NULL,'0','tms16',NULL,NULL,'2',NULL,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1');
INSERT INTO `env_mgt_node_bind` (`module_name`, `suite_id`, `node_id`, `node_port`, `node_docker`, `node_bind_desc`, `deploy_group`, `deploy_type`, `deploy_path`, `health_check_url`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `tomcat_path`, `script_path`, `script_name`, `config_path`, `log_path`, `enable_bind`) VALUES('nf-newpig','50',NULL,'0','tms17',NULL,NULL,'2',NULL,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1');
INSERT INTO `env_mgt_node_bind` (`module_name`, `suite_id`, `node_id`, `node_port`, `node_docker`, `node_bind_desc`, `deploy_group`, `deploy_type`, `deploy_path`, `health_check_url`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `tomcat_path`, `script_path`, `script_name`, `config_path`, `log_path`, `enable_bind`) VALUES('nf-newpig','101',NULL,'0','tms18',NULL,NULL,'2',NULL,'','shuai.liu','2020-10-20 15:07:46','shuai.liu','2020-10-20 15:07:46',NULL,NULL,NULL,NULL,NULL,NULL,'1');

INSERT INTO `env_mgt_node_bind` (`module_name`, `suite_id`, `node_id`, `node_port`, `node_docker`, `node_bind_desc`, `deploy_group`, `deploy_type`, `deploy_path`, `health_check_url`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `tomcat_path`, `script_path`, `script_name`, `config_path`, `log_path`, `enable_bind`) VALUES('nf-newwap','35',NULL,'0','tms02',NULL,NULL,'2',NULL,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1');
INSERT INTO `env_mgt_node_bind` (`module_name`, `suite_id`, `node_id`, `node_port`, `node_docker`, `node_bind_desc`, `deploy_group`, `deploy_type`, `deploy_path`, `health_check_url`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `tomcat_path`, `script_path`, `script_name`, `config_path`, `log_path`, `enable_bind`) VALUES('nf-newwap','36',NULL,'0','tms03',NULL,NULL,'2',NULL,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1');
INSERT INTO `env_mgt_node_bind` (`module_name`, `suite_id`, `node_id`, `node_port`, `node_docker`, `node_bind_desc`, `deploy_group`, `deploy_type`, `deploy_path`, `health_check_url`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `tomcat_path`, `script_path`, `script_name`, `config_path`, `log_path`, `enable_bind`) VALUES('nf-newwap','37',NULL,'0','tms04',NULL,NULL,'2',NULL,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1');
INSERT INTO `env_mgt_node_bind` (`module_name`, `suite_id`, `node_id`, `node_port`, `node_docker`, `node_bind_desc`, `deploy_group`, `deploy_type`, `deploy_path`, `health_check_url`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `tomcat_path`, `script_path`, `script_name`, `config_path`, `log_path`, `enable_bind`) VALUES('nf-newwap','38',NULL,'0','tms05',NULL,NULL,'2',NULL,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1');
INSERT INTO `env_mgt_node_bind` (`module_name`, `suite_id`, `node_id`, `node_port`, `node_docker`, `node_bind_desc`, `deploy_group`, `deploy_type`, `deploy_path`, `health_check_url`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `tomcat_path`, `script_path`, `script_name`, `config_path`, `log_path`, `enable_bind`) VALUES('nf-newwap','39',NULL,'0','tms06',NULL,NULL,'2',NULL,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1');
INSERT INTO `env_mgt_node_bind` (`module_name`, `suite_id`, `node_id`, `node_port`, `node_docker`, `node_bind_desc`, `deploy_group`, `deploy_type`, `deploy_path`, `health_check_url`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `tomcat_path`, `script_path`, `script_name`, `config_path`, `log_path`, `enable_bind`) VALUES('nf-newwap','40',NULL,'0','tms07',NULL,NULL,'2',NULL,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1');
INSERT INTO `env_mgt_node_bind` (`module_name`, `suite_id`, `node_id`, `node_port`, `node_docker`, `node_bind_desc`, `deploy_group`, `deploy_type`, `deploy_path`, `health_check_url`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `tomcat_path`, `script_path`, `script_name`, `config_path`, `log_path`, `enable_bind`) VALUES('nf-newwap','41',NULL,'0','tms08',NULL,NULL,'2',NULL,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1');
INSERT INTO `env_mgt_node_bind` (`module_name`, `suite_id`, `node_id`, `node_port`, `node_docker`, `node_bind_desc`, `deploy_group`, `deploy_type`, `deploy_path`, `health_check_url`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `tomcat_path`, `script_path`, `script_name`, `config_path`, `log_path`, `enable_bind`) VALUES('nf-newwap','42',NULL,'0','tms09',NULL,NULL,'2',NULL,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1');
INSERT INTO `env_mgt_node_bind` (`module_name`, `suite_id`, `node_id`, `node_port`, `node_docker`, `node_bind_desc`, `deploy_group`, `deploy_type`, `deploy_path`, `health_check_url`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `tomcat_path`, `script_path`, `script_name`, `config_path`, `log_path`, `enable_bind`) VALUES('nf-newwap','43',NULL,'0','tms10',NULL,NULL,'2',NULL,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1');
INSERT INTO `env_mgt_node_bind` (`module_name`, `suite_id`, `node_id`, `node_port`, `node_docker`, `node_bind_desc`, `deploy_group`, `deploy_type`, `deploy_path`, `health_check_url`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `tomcat_path`, `script_path`, `script_name`, `config_path`, `log_path`, `enable_bind`) VALUES('nf-newwap','44',NULL,'0','tms11',NULL,NULL,'2',NULL,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1');
INSERT INTO `env_mgt_node_bind` (`module_name`, `suite_id`, `node_id`, `node_port`, `node_docker`, `node_bind_desc`, `deploy_group`, `deploy_type`, `deploy_path`, `health_check_url`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `tomcat_path`, `script_path`, `script_name`, `config_path`, `log_path`, `enable_bind`) VALUES('nf-newwap','45',NULL,'0','tms12',NULL,NULL,'2',NULL,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1');
INSERT INTO `env_mgt_node_bind` (`module_name`, `suite_id`, `node_id`, `node_port`, `node_docker`, `node_bind_desc`, `deploy_group`, `deploy_type`, `deploy_path`, `health_check_url`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `tomcat_path`, `script_path`, `script_name`, `config_path`, `log_path`, `enable_bind`) VALUES('nf-newwap','46',NULL,'0','tms13',NULL,NULL,'2',NULL,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1');
INSERT INTO `env_mgt_node_bind` (`module_name`, `suite_id`, `node_id`, `node_port`, `node_docker`, `node_bind_desc`, `deploy_group`, `deploy_type`, `deploy_path`, `health_check_url`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `tomcat_path`, `script_path`, `script_name`, `config_path`, `log_path`, `enable_bind`) VALUES('nf-newwap','47',NULL,'0','tms14',NULL,NULL,'2',NULL,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1');
INSERT INTO `env_mgt_node_bind` (`module_name`, `suite_id`, `node_id`, `node_port`, `node_docker`, `node_bind_desc`, `deploy_group`, `deploy_type`, `deploy_path`, `health_check_url`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `tomcat_path`, `script_path`, `script_name`, `config_path`, `log_path`, `enable_bind`) VALUES('nf-newwap','48',NULL,'0','tms15',NULL,NULL,'2',NULL,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1');
INSERT INTO `env_mgt_node_bind` (`module_name`, `suite_id`, `node_id`, `node_port`, `node_docker`, `node_bind_desc`, `deploy_group`, `deploy_type`, `deploy_path`, `health_check_url`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `tomcat_path`, `script_path`, `script_name`, `config_path`, `log_path`, `enable_bind`) VALUES('nf-newwap','49',NULL,'0','tms16',NULL,NULL,'2',NULL,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1');
INSERT INTO `env_mgt_node_bind` (`module_name`, `suite_id`, `node_id`, `node_port`, `node_docker`, `node_bind_desc`, `deploy_group`, `deploy_type`, `deploy_path`, `health_check_url`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `tomcat_path`, `script_path`, `script_name`, `config_path`, `log_path`, `enable_bind`) VALUES('nf-newwap','50',NULL,'0','tms17',NULL,NULL,'2',NULL,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1');
INSERT INTO `env_mgt_node_bind` (`module_name`, `suite_id`, `node_id`, `node_port`, `node_docker`, `node_bind_desc`, `deploy_group`, `deploy_type`, `deploy_path`, `health_check_url`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `tomcat_path`, `script_path`, `script_name`, `config_path`, `log_path`, `enable_bind`) VALUES('nf-newwap','101',NULL,'0','tms18',NULL,NULL,'2',NULL,'','shuai.liu','2020-10-20 15:07:46','shuai.liu','2020-10-20 15:07:46',NULL,NULL,NULL,NULL,NULL,NULL,'1');

INSERT INTO `env_mgt_node_bind` (`module_name`, `suite_id`, `node_id`, `node_port`, `node_docker`, `node_bind_desc`, `deploy_group`, `deploy_type`, `deploy_path`, `health_check_url`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `tomcat_path`, `script_path`, `script_name`, `config_path`, `log_path`, `enable_bind`) VALUES('nf-vendor','35',NULL,'0','tms02',NULL,NULL,'2',NULL,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1');
INSERT INTO `env_mgt_node_bind` (`module_name`, `suite_id`, `node_id`, `node_port`, `node_docker`, `node_bind_desc`, `deploy_group`, `deploy_type`, `deploy_path`, `health_check_url`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `tomcat_path`, `script_path`, `script_name`, `config_path`, `log_path`, `enable_bind`) VALUES('nf-vendor','36',NULL,'0','tms03',NULL,NULL,'2',NULL,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1');
INSERT INTO `env_mgt_node_bind` (`module_name`, `suite_id`, `node_id`, `node_port`, `node_docker`, `node_bind_desc`, `deploy_group`, `deploy_type`, `deploy_path`, `health_check_url`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `tomcat_path`, `script_path`, `script_name`, `config_path`, `log_path`, `enable_bind`) VALUES('nf-vendor','37',NULL,'0','tms04',NULL,NULL,'2',NULL,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1');
INSERT INTO `env_mgt_node_bind` (`module_name`, `suite_id`, `node_id`, `node_port`, `node_docker`, `node_bind_desc`, `deploy_group`, `deploy_type`, `deploy_path`, `health_check_url`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `tomcat_path`, `script_path`, `script_name`, `config_path`, `log_path`, `enable_bind`) VALUES('nf-vendor','38',NULL,'0','tms05',NULL,NULL,'2',NULL,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1');
INSERT INTO `env_mgt_node_bind` (`module_name`, `suite_id`, `node_id`, `node_port`, `node_docker`, `node_bind_desc`, `deploy_group`, `deploy_type`, `deploy_path`, `health_check_url`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `tomcat_path`, `script_path`, `script_name`, `config_path`, `log_path`, `enable_bind`) VALUES('nf-vendor','39',NULL,'0','tms06',NULL,NULL,'2',NULL,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1');
INSERT INTO `env_mgt_node_bind` (`module_name`, `suite_id`, `node_id`, `node_port`, `node_docker`, `node_bind_desc`, `deploy_group`, `deploy_type`, `deploy_path`, `health_check_url`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `tomcat_path`, `script_path`, `script_name`, `config_path`, `log_path`, `enable_bind`) VALUES('nf-vendor','40',NULL,'0','tms07',NULL,NULL,'2',NULL,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1');
INSERT INTO `env_mgt_node_bind` (`module_name`, `suite_id`, `node_id`, `node_port`, `node_docker`, `node_bind_desc`, `deploy_group`, `deploy_type`, `deploy_path`, `health_check_url`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `tomcat_path`, `script_path`, `script_name`, `config_path`, `log_path`, `enable_bind`) VALUES('nf-vendor','41',NULL,'0','tms08',NULL,NULL,'2',NULL,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1');
INSERT INTO `env_mgt_node_bind` (`module_name`, `suite_id`, `node_id`, `node_port`, `node_docker`, `node_bind_desc`, `deploy_group`, `deploy_type`, `deploy_path`, `health_check_url`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `tomcat_path`, `script_path`, `script_name`, `config_path`, `log_path`, `enable_bind`) VALUES('nf-vendor','42',NULL,'0','tms09',NULL,NULL,'2',NULL,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1');
INSERT INTO `env_mgt_node_bind` (`module_name`, `suite_id`, `node_id`, `node_port`, `node_docker`, `node_bind_desc`, `deploy_group`, `deploy_type`, `deploy_path`, `health_check_url`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `tomcat_path`, `script_path`, `script_name`, `config_path`, `log_path`, `enable_bind`) VALUES('nf-vendor','43',NULL,'0','tms10',NULL,NULL,'2',NULL,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1');
INSERT INTO `env_mgt_node_bind` (`module_name`, `suite_id`, `node_id`, `node_port`, `node_docker`, `node_bind_desc`, `deploy_group`, `deploy_type`, `deploy_path`, `health_check_url`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `tomcat_path`, `script_path`, `script_name`, `config_path`, `log_path`, `enable_bind`) VALUES('nf-vendor','44',NULL,'0','tms11',NULL,NULL,'2',NULL,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1');
INSERT INTO `env_mgt_node_bind` (`module_name`, `suite_id`, `node_id`, `node_port`, `node_docker`, `node_bind_desc`, `deploy_group`, `deploy_type`, `deploy_path`, `health_check_url`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `tomcat_path`, `script_path`, `script_name`, `config_path`, `log_path`, `enable_bind`) VALUES('nf-vendor','45',NULL,'0','tms12',NULL,NULL,'2',NULL,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1');
INSERT INTO `env_mgt_node_bind` (`module_name`, `suite_id`, `node_id`, `node_port`, `node_docker`, `node_bind_desc`, `deploy_group`, `deploy_type`, `deploy_path`, `health_check_url`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `tomcat_path`, `script_path`, `script_name`, `config_path`, `log_path`, `enable_bind`) VALUES('nf-vendor','46',NULL,'0','tms13',NULL,NULL,'2',NULL,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1');
INSERT INTO `env_mgt_node_bind` (`module_name`, `suite_id`, `node_id`, `node_port`, `node_docker`, `node_bind_desc`, `deploy_group`, `deploy_type`, `deploy_path`, `health_check_url`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `tomcat_path`, `script_path`, `script_name`, `config_path`, `log_path`, `enable_bind`) VALUES('nf-vendor','47',NULL,'0','tms14',NULL,NULL,'2',NULL,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1');
INSERT INTO `env_mgt_node_bind` (`module_name`, `suite_id`, `node_id`, `node_port`, `node_docker`, `node_bind_desc`, `deploy_group`, `deploy_type`, `deploy_path`, `health_check_url`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `tomcat_path`, `script_path`, `script_name`, `config_path`, `log_path`, `enable_bind`) VALUES('nf-vendor','48',NULL,'0','tms15',NULL,NULL,'2',NULL,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1');
INSERT INTO `env_mgt_node_bind` (`module_name`, `suite_id`, `node_id`, `node_port`, `node_docker`, `node_bind_desc`, `deploy_group`, `deploy_type`, `deploy_path`, `health_check_url`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `tomcat_path`, `script_path`, `script_name`, `config_path`, `log_path`, `enable_bind`) VALUES('nf-vendor','49',NULL,'0','tms16',NULL,NULL,'2',NULL,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1');
INSERT INTO `env_mgt_node_bind` (`module_name`, `suite_id`, `node_id`, `node_port`, `node_docker`, `node_bind_desc`, `deploy_group`, `deploy_type`, `deploy_path`, `health_check_url`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `tomcat_path`, `script_path`, `script_name`, `config_path`, `log_path`, `enable_bind`) VALUES('nf-vendor','50',NULL,'0','tms17',NULL,NULL,'2',NULL,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1');
INSERT INTO `env_mgt_node_bind` (`module_name`, `suite_id`, `node_id`, `node_port`, `node_docker`, `node_bind_desc`, `deploy_group`, `deploy_type`, `deploy_path`, `health_check_url`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `tomcat_path`, `script_path`, `script_name`, `config_path`, `log_path`, `enable_bind`) VALUES('nf-vendor','101',NULL,'0','tms18',NULL,NULL,'2',NULL,'','shuai.liu','2020-10-20 15:07:46','shuai.liu','2020-10-20 15:07:46',NULL,NULL,NULL,NULL,NULL,NULL,'1');

INSERT INTO `env_mgt_node_bind` (`module_name`, `suite_id`, `node_id`, `node_port`, `node_docker`, `node_bind_desc`, `deploy_group`, `deploy_type`, `deploy_path`, `health_check_url`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `tomcat_path`, `script_path`, `script_name`, `config_path`, `log_path`, `enable_bind`) VALUES('nf-smasset','35',NULL,'0','tms02',NULL,NULL,'2',NULL,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1');
INSERT INTO `env_mgt_node_bind` (`module_name`, `suite_id`, `node_id`, `node_port`, `node_docker`, `node_bind_desc`, `deploy_group`, `deploy_type`, `deploy_path`, `health_check_url`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `tomcat_path`, `script_path`, `script_name`, `config_path`, `log_path`, `enable_bind`) VALUES('nf-smasset','36',NULL,'0','tms03',NULL,NULL,'2',NULL,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1');
INSERT INTO `env_mgt_node_bind` (`module_name`, `suite_id`, `node_id`, `node_port`, `node_docker`, `node_bind_desc`, `deploy_group`, `deploy_type`, `deploy_path`, `health_check_url`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `tomcat_path`, `script_path`, `script_name`, `config_path`, `log_path`, `enable_bind`) VALUES('nf-smasset','37',NULL,'0','tms04',NULL,NULL,'2',NULL,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1');
INSERT INTO `env_mgt_node_bind` (`module_name`, `suite_id`, `node_id`, `node_port`, `node_docker`, `node_bind_desc`, `deploy_group`, `deploy_type`, `deploy_path`, `health_check_url`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `tomcat_path`, `script_path`, `script_name`, `config_path`, `log_path`, `enable_bind`) VALUES('nf-smasset','38',NULL,'0','tms05',NULL,NULL,'2',NULL,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1');
INSERT INTO `env_mgt_node_bind` (`module_name`, `suite_id`, `node_id`, `node_port`, `node_docker`, `node_bind_desc`, `deploy_group`, `deploy_type`, `deploy_path`, `health_check_url`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `tomcat_path`, `script_path`, `script_name`, `config_path`, `log_path`, `enable_bind`) VALUES('nf-smasset','39',NULL,'0','tms06',NULL,NULL,'2',NULL,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1');
INSERT INTO `env_mgt_node_bind` (`module_name`, `suite_id`, `node_id`, `node_port`, `node_docker`, `node_bind_desc`, `deploy_group`, `deploy_type`, `deploy_path`, `health_check_url`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `tomcat_path`, `script_path`, `script_name`, `config_path`, `log_path`, `enable_bind`) VALUES('nf-smasset','40',NULL,'0','tms07',NULL,NULL,'2',NULL,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1');
INSERT INTO `env_mgt_node_bind` (`module_name`, `suite_id`, `node_id`, `node_port`, `node_docker`, `node_bind_desc`, `deploy_group`, `deploy_type`, `deploy_path`, `health_check_url`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `tomcat_path`, `script_path`, `script_name`, `config_path`, `log_path`, `enable_bind`) VALUES('nf-smasset','41',NULL,'0','tms08',NULL,NULL,'2',NULL,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1');
INSERT INTO `env_mgt_node_bind` (`module_name`, `suite_id`, `node_id`, `node_port`, `node_docker`, `node_bind_desc`, `deploy_group`, `deploy_type`, `deploy_path`, `health_check_url`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `tomcat_path`, `script_path`, `script_name`, `config_path`, `log_path`, `enable_bind`) VALUES('nf-smasset','42',NULL,'0','tms09',NULL,NULL,'2',NULL,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1');
INSERT INTO `env_mgt_node_bind` (`module_name`, `suite_id`, `node_id`, `node_port`, `node_docker`, `node_bind_desc`, `deploy_group`, `deploy_type`, `deploy_path`, `health_check_url`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `tomcat_path`, `script_path`, `script_name`, `config_path`, `log_path`, `enable_bind`) VALUES('nf-smasset','43',NULL,'0','tms10',NULL,NULL,'2',NULL,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1');
INSERT INTO `env_mgt_node_bind` (`module_name`, `suite_id`, `node_id`, `node_port`, `node_docker`, `node_bind_desc`, `deploy_group`, `deploy_type`, `deploy_path`, `health_check_url`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `tomcat_path`, `script_path`, `script_name`, `config_path`, `log_path`, `enable_bind`) VALUES('nf-smasset','44',NULL,'0','tms11',NULL,NULL,'2',NULL,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1');
INSERT INTO `env_mgt_node_bind` (`module_name`, `suite_id`, `node_id`, `node_port`, `node_docker`, `node_bind_desc`, `deploy_group`, `deploy_type`, `deploy_path`, `health_check_url`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `tomcat_path`, `script_path`, `script_name`, `config_path`, `log_path`, `enable_bind`) VALUES('nf-smasset','45',NULL,'0','tms12',NULL,NULL,'2',NULL,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1');
INSERT INTO `env_mgt_node_bind` (`module_name`, `suite_id`, `node_id`, `node_port`, `node_docker`, `node_bind_desc`, `deploy_group`, `deploy_type`, `deploy_path`, `health_check_url`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `tomcat_path`, `script_path`, `script_name`, `config_path`, `log_path`, `enable_bind`) VALUES('nf-smasset','46',NULL,'0','tms13',NULL,NULL,'2',NULL,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1');
INSERT INTO `env_mgt_node_bind` (`module_name`, `suite_id`, `node_id`, `node_port`, `node_docker`, `node_bind_desc`, `deploy_group`, `deploy_type`, `deploy_path`, `health_check_url`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `tomcat_path`, `script_path`, `script_name`, `config_path`, `log_path`, `enable_bind`) VALUES('nf-smasset','47',NULL,'0','tms14',NULL,NULL,'2',NULL,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1');
INSERT INTO `env_mgt_node_bind` (`module_name`, `suite_id`, `node_id`, `node_port`, `node_docker`, `node_bind_desc`, `deploy_group`, `deploy_type`, `deploy_path`, `health_check_url`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `tomcat_path`, `script_path`, `script_name`, `config_path`, `log_path`, `enable_bind`) VALUES('nf-smasset','48',NULL,'0','tms15',NULL,NULL,'2',NULL,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1');
INSERT INTO `env_mgt_node_bind` (`module_name`, `suite_id`, `node_id`, `node_port`, `node_docker`, `node_bind_desc`, `deploy_group`, `deploy_type`, `deploy_path`, `health_check_url`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `tomcat_path`, `script_path`, `script_name`, `config_path`, `log_path`, `enable_bind`) VALUES('nf-smasset','49',NULL,'0','tms16',NULL,NULL,'2',NULL,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1');
INSERT INTO `env_mgt_node_bind` (`module_name`, `suite_id`, `node_id`, `node_port`, `node_docker`, `node_bind_desc`, `deploy_group`, `deploy_type`, `deploy_path`, `health_check_url`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `tomcat_path`, `script_path`, `script_name`, `config_path`, `log_path`, `enable_bind`) VALUES('nf-smasset','50',NULL,'0','tms17',NULL,NULL,'2',NULL,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'1');
INSERT INTO `env_mgt_node_bind` (`module_name`, `suite_id`, `node_id`, `node_port`, `node_docker`, `node_bind_desc`, `deploy_group`, `deploy_type`, `deploy_path`, `health_check_url`, `create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `tomcat_path`, `script_path`, `script_name`, `config_path`, `log_path`, `enable_bind`) VALUES('nf-smasset','101',NULL,'0','tms18',NULL,NULL,'2',NULL,'','shuai.liu','2020-10-20 15:07:46','shuai.liu','2020-10-20 15:07:46',NULL,NULL,NULL,NULL,NULL,NULL,'1');

