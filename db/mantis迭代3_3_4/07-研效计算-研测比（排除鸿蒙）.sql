-- *****项目中测试人员投入工时（计算研测比的）***************
-- 1、改造1：测试设计 --> 手工测试设计

-- 测试交付工时类型：
select * from dev_effic_code code where code_type >= 200  and code_type < 300 order by id;
/*
20001	200	知识图谱	1
20002	200	测试设计 --> 手工测试设计	2
20003	200	自动化测试设计	3
20004	200	手工执行	4
20005	200	自动化案例开发	5
20006	200	自动化执行	6
20007	200	灰度环境测试	7
20008	200	业务联测	8
 */

-- 测试项目 --> 测试报告 --> 测试任务：
-- 1、测试项目 & 报告
select proj.id as proj_id,
       proj.name as proj_name,
       proj.owner as proj_owner,
       proj.actual_end as proj_actual_end,
       repo.id as repo_id,
       repo.title as repo_title,
       repo.status as repo_status,
       repo.creator as repo_creator,
       repo.approver as repo_approver
from dev_effic_test_project proj
inner join dev_effic_test_report repo on repo.project_id = proj.id
where repo.status = 'success'
and proj.actual_end >= '2025-04-01'
and proj.actual_end <= '2025-04-30'
and (proj.owner like '%许福培%' or proj.create_user like '%许福培%')
and proj.id not in (select code_val from dev_effic_code where code_type = 201)
;

select proj.id as proj_id,
       proj.name as proj_name,
       proj.owner as proj_owner,
       proj.actual_end as proj_actual_end,
       repo.id as repo_id,
       repo.title as repo_title,
       repo.status as repo_status,
       repo.creator as repo_creator,
       repo.approver as repo_approver
from dev_effic_test_project proj
inner join dev_effic_test_report repo on repo.project_id = proj.id
where proj.id in(1503,1439,1402,1354,1237, 1236, 1230, 1186, 1160, 1112, 1089);
select * from dev_effic_test_project proj where proj.id in(1503,1439,1402,1354,1237, 1236, 1230, 1186, 1160, 1112, 1089);
select * from dev_effic_test_report repo where repo.project_id in(1503,1439,1402,1354,1237, 1236, 1230, 1186, 1160, 1112, 1089);

-- 研测比排除鸿蒙项目：201（01~11 zt@2025-05-06）
INSERT INTO dev_effic_code(create_user, create_time, update_user, update_time, stamp, id, code_type, code_val, code_idx, code_is_active, code_desc)
VALUES('huaitian.zhang', '2025-05-06 10:11:12', 'huaitian.zhang', '2025-05-06 10:11:12', 0, 20101, 201, '1089', 1, True, '鸿蒙项目（1089）：鸿蒙二阶段（赵丹东）');
INSERT INTO dev_effic_code(create_user, create_time, update_user, update_time, stamp, id, code_type, code_val, code_idx, code_is_active, code_desc)
VALUES('huaitian.zhang', '2025-05-06 10:11:12', 'huaitian.zhang', '2025-05-06 10:11:12', 0, 20102, 201, '1112', 1, True, '鸿蒙项目（1112）：鸿蒙与android的服务端兼容（许福培）');
INSERT INTO dev_effic_code(create_user, create_time, update_user, update_time, stamp, id, code_type, code_val, code_idx, code_is_active, code_desc)
VALUES('huaitian.zhang', '2025-05-06 10:11:12', 'huaitian.zhang', '2025-05-06 10:11:12', 0, 20103, 201, '1160', 1, True, '鸿蒙项目（1160）：鸿蒙cgi兼容性测试（许福培）');
INSERT INTO dev_effic_code(create_user, create_time, update_user, update_time, stamp, id, code_type, code_val, code_idx, code_is_active, code_desc)
VALUES('huaitian.zhang', '2025-05-06 10:11:12', 'huaitian.zhang', '2025-05-06 10:11:12', 0, 20104, 201, '1186', 1, True, '鸿蒙项目（1186）：鸿蒙公募二期（许福培）');
INSERT INTO dev_effic_code(create_user, create_time, update_user, update_time, stamp, id, code_type, code_val, code_idx, code_is_active, code_desc)
VALUES('huaitian.zhang', '2025-05-06 10:11:12', 'huaitian.zhang', '2025-05-06 10:11:12', 0, 20105, 201, '1230', 1, True, '鸿蒙项目（1230）：鸿蒙私募一期一阶段（首页+合规+私募优选）（葛雷）');
INSERT INTO dev_effic_code(create_user, create_time, update_user, update_time, stamp, id, code_type, code_val, code_idx, code_is_active, code_desc)
VALUES('huaitian.zhang', '2025-05-06 10:11:12', 'huaitian.zhang', '2025-05-06 10:11:12', 0, 20106, 201, '1236', 1, True, '鸿蒙项目（1236）：鸿蒙私募一期二阶段（阳光私募档案页+固收档案页）（葛雷）');
INSERT INTO dev_effic_code(create_user, create_time, update_user, update_time, stamp, id, code_type, code_val, code_idx, code_is_active, code_desc)
VALUES('huaitian.zhang', '2025-05-06 10:11:12', 'huaitian.zhang', '2025-05-06 10:11:12', 0, 20107, 201, '1237', 1, True, '鸿蒙项目（1237）：鸿蒙私募一期三阶段（分期1其他所有）（葛雷）');
INSERT INTO dev_effic_code(create_user, create_time, update_user, update_time, stamp, id, code_type, code_val, code_idx, code_is_active, code_desc)
VALUES('huaitian.zhang', '2025-05-06 10:11:12', 'huaitian.zhang', '2025-05-06 10:11:12', 0, 20108, 201, '1354', 1, True, '鸿蒙项目（1354）：鸿蒙3阶段--零售（档案页完善）（王梅）');
INSERT INTO dev_effic_code(create_user, create_time, update_user, update_time, stamp, id, code_type, code_val, code_idx, code_is_active, code_desc)
VALUES('huaitian.zhang', '2025-05-06 10:11:12', 'huaitian.zhang', '2025-05-06 10:11:12', 0, 20109, 201, '1402', 1, True, '鸿蒙项目（1402）：鸿蒙3阶段--【高端】鸿蒙私募二期（葛雷）');
INSERT INTO dev_effic_code(create_user, create_time, update_user, update_time, stamp, id, code_type, code_val, code_idx, code_is_active, code_desc)
VALUES('huaitian.zhang', '2025-05-06 10:11:12', 'huaitian.zhang', '2025-05-06 10:11:12', 0, 20110, 201, '1439', 1, True, '鸿蒙项目（1439）：鸿蒙H5-折叠屏兼容（李远相）');
INSERT INTO dev_effic_code(create_user, create_time, update_user, update_time, stamp, id, code_type, code_val, code_idx, code_is_active, code_desc)
VALUES('huaitian.zhang', '2025-05-06 10:11:12', 'huaitian.zhang', '2025-05-06 10:11:12', 0, 20111, 201, '1503', 1, True, '鸿蒙项目（1503）：鸿蒙私募三期（许福培）');


-- 2、测试任务：
select task.id as task_id,
       task.project_id as proj_id,
       task.name as task_name,
       task.status as task_status,
       task.task_type,
       task.owner,
       task.creator,
       task.created,
       task.completed,
       task.effort_completed
from dev_effic_test_task task
where task.project_id in (1503, 1512, 1518, 1518)
;


-- ***************项目中测试人员投入工时（计算研测比的）*****
-- 研测比新需求_for_远相。zt@2025-05-06
select test_user.user_name,
               test_user.cn_name,
               test_user.main_skill,
               test_user.team_id,
               test_user.team_name,
               test_user.p_team_id,
               test_user.p_team_name,
               round(sum(dev_time) / sum(effort_completed), 2) as user_value
        from (
            select
                r.ratio,
                round(
                        case when c.divided = 1
                            then JSON_EXTRACT(m_r.division_ratio, concat('$."', p_th.proc, '"') )*c.coe
                            else r.ratio
                            end, 2) as manual_ratio, -- 优先以填的比例为准
                c.coe,
                c.division_ratio,
                c.divided,
                tp.id,
                tp.actual_start,
                tp.actual_end,
                tp.name,
                tp.tapd_iteration_id,
                p_th.proc,
                ifnull(p_th.effort_completed, 0) as effort_completed,
                ifnull(
                        round(p_dt.t_exhausted*
                              case when c.divided = 1
                                  then JSON_EXTRACT(m_r.division_ratio, concat('$."', p_th.proc, '"') )*c.coe
                                  else r.ratio
                                  end , 0),
                        0) as dev_time, -- 已经分好了的研发时间
                tp.team,
                tp.business_system
            from dev_effic_test_project tp
                inner join dev_effic_test_report tr on tr.project_id = tp.id and tr.status = 'success'
                left outer join (
                    -- *****项目中测试人员投入工时（计算研测比的）***************
                    SELECT
                        project_id,
                        case when owner != '' and owner is not null
                            then owner
                            else creator
                            end as proc,
                        sum(
                                case when tt.task_type in ('自动化测试设计', '自动化案例开发')
                                    then round(ifnull(effort_completed, 0)/2, 2)
                                    else effort_completed
                                    end
                        ) as effort_completed
                    FROM
                        dev_effic_test_task tt
                    where tt.project_id is not null
                      and tt.effort_completed is not null
                      and tt.effort_completed > 0
                      and tt.task_type in ('知识图谱', '测试设计', '手工执行', '自动化测试设计', '自动化案例开发', '自动化执行')
                      and (owner  = '许福培' or creator = '许福培') -- 这里是变量
                    group by project_id, proc
                    -- ***************项目中测试人员投入工时（计算研测比的）*****
                    ) p_th on p_th.project_id = tp.id
                left outer join (
                    -- *****项目中研发投入时间***************
                    select
                        project_id,
                        sum(exhausted) as t_exhausted
                    from (
                        select
                            project_id,
                            tapd_story_id,
                            round(sum(tapd_task_effort_completed)/total, 2) as exhausted
                        from (
                            SELECT distinct
                                tds.project_id,
                                ds.tapd_story_id,
                                sta.total,
                                tet.tapd_entry_id as task_id,
                                tet.tapd_task_effort_completed
                            FROM dev_effic_test_dev_submit tds
                                inner join dev_effic_test_dev_submit_story tdss on tds.id = tdss.dev_submit_id
                                inner join dev_effic_dev_story ds on ds.id = tdss.dev_story_id
                                inner join tapd_entry_task tet on tet.tapd_task_story_id = ds.tapd_story_id
                                                                             and (
                                                                                 trim(SUBSTRING_INDEX(tet.tapd_task_custom_field_one, '（', 1)) in ('设计', '研发', '测试支持', '自测', 'code-review')
                                                                                     or
                                                                                 trim(SUBSTRING_INDEX(tet.tapd_task_custom_field_two, '（', 1)) in ('设计', '研发', '测试支持', '自测', 'code-review')
                                                                                     or
                                                                                 trim(SUBSTRING_INDEX(tet.tapd_task_custom_field_four, '（', 1)) in ('设计', '研发', '测试支持', '自测', 'code-review')
                                                                                     or
                                                                                 trim(SUBSTRING_INDEX(tet.tapd_task_custom_field_eight, '（', 1)) in ('设计', '研发', '测试支持', '自测', 'code-review')
                                                                                 )
                                inner join (
                                    select
                                        tdss.dev_story_id,
                                        count(0) as total
                                    from dev_effic_test_dev_submit_story tdss, dev_effic_test_dev_submit tds
                                    where tdss.dev_submit_id = tds.id and tds.tapd_test_plan_status != 0
                                    group by dev_story_id
                                    ) sta on sta.dev_story_id = tdss.dev_story_id
                                inner join dev_effic_test_project p on p.id = tds.project_id
                                                                                  and p.actual_end > '2025-04-01'
                                                                                  and p.actual_end < '2025-04-30' -- 这里是变量
                            where project_id is not null and tds.tapd_test_plan_status != 0
                            ) t
                        group by project_id, tapd_story_id
                        ) t_t
                    group by project_id
                    -- ***************项目中研发投入时间*****
                    ) p_dt on p_dt.project_id = tp.id
                inner join  (
                    -- *****按参与人的分工比例***************
                    select
                        project_id,
                        round(1/count(0), 2) as ratio
                    from (
                        SELECT distinct
                            project_id,
                            case when tt.owner != '' and tt.owner is not null
                                then tt.owner
                                else tt.creator
                                end as proc
                        FROM dev_effic_test_task tt
                            inner join dev_effic_test_project p on p.id = tt.project_id
                                                                              and p.actual_end > '2025-04-01'
                                                                              and p.actual_end < '2025-04-30' -- 这里是变量
                        where tt.project_id is not null
                          and tt.effort_completed is not null
                          and tt.effort_completed > 0
                          and tt.task_type in ('自动化测试设计', '自动化案例开发', '自动化执行', '知识图谱', '测试设计', '手工执行', '灰度环境测试')
                        ) d
                    group by project_id
                    -- ***************按参与人的分工比例*****
                    ) r on tp.id = r.project_id
                left outer join (
                    -- *****是否有手填的分工比例（优先以手填为准）***************
                    SELECT
                        case when task_division_ratio like '{{%' and JSON_VALID( replace( replace( task_division_ratio, '：', ':'), '，', ',') ) = 1
                            then 1
                            else 0
                            end as divided,
                        replace( replace( task_division_ratio, '：', ':'), '，', ',') as division_ratio,
                        p.id
                    FROM dev_effic_test_project p
                    where p.actual_end > '2025-04-01'
                      and p.actual_end < '2025-04-30' -- 这里是变量
                    -- ***************是否有手填的分工比例（优先以手填为准）*****
                    ) m_r on m_r.id = tp.id and division_ratio like concat('%', p_th.proc, '%')
                left outer join (
                    -- *****计算出有比例的真实比例（因为有些填的不足100%）***************
                    select
                        id as project_id,
                        divided,
                        division_ratio,
                        -- ==把json中每一个人的比例取出加总后计算整体比例======
                        1/sum(JSON_EXTRACT(division_ratio,
                                           concat('$.',
                                                  JSON_EXTRACT(
                                                          JSON_KEYS(division_ratio),
                                                          concat('$[', se.`number` - 1, ']')
                                                  )
                                           )
                              )
                          ) as coe
                        -- ======把json中每一个人的比例取出加总后计算整体比例==
                    from (
                        SELECT
                            case when task_division_ratio like '{{%' and JSON_VALID( replace( replace( task_division_ratio, '：', ':'), '，', ',') ) = 1
                                then 1
                                else 0
                                end as divided,
                            replace( replace( task_division_ratio, '：', ':'), '，', ',') as division_ratio,
                            p.id
                        FROM dev_effic_test_project p
                        where p.actual_end > '2025-04-01'
                          and p.actual_end < '2025-04-30' -- 这里是变量
                        ) d
                        inner join sequence se on se.`number` <= JSON_LENGTH(division_ratio) and divided = 1
                    group by id
                    -- ***************计算出有比例的真实比例（因为有些填的不足100%）*****
                    ) c on c.project_id = tp.id
            where tp.actual_end > '2025-04-01'
              and tp.actual_end < '2025-04-30'
              and proc = '许福培' -- 这里是变量
              and tp.id not in (
                select
                    code201.code_val
                from dev_effic_code code201
                where code201.code_type = 201)
            ) data
            inner join team_mgt_user_info test_user on test_user.cn_name = data.proc
        ;

