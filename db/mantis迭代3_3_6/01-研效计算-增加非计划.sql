-- *****项目中测试人员投入工时（计算研测比的）***************
-- 1、改造1：测试设计 --> 手工测试设计

-- 测试交付工时类型：
select * from dev_effic_code code where code_type >= 200  and code_type < 300 order by id;
/*
20001	200	知识图谱	1
20002	200	测试设计 --> 手工测试设计	2
20003	200	自动化测试设计	3
20004	200	手工执行	4
20005	200	自动化案例开发	5
20006	200	自动化执行	6
20007	200	灰度环境测试	7
20008	200	业务联测	8
 */
-- 1、禁用「灰度环境测试」：
update dev_effic_code
set code_is_active = 0,
    update_user    = 'huaitian.zhang',
    update_time    = '2025-06-09 10:11:12'
where id = 20007
  and code_type = 200
  and code_val = '灰度环境测试';

-- 2、新增「非计划」：
insert into dev_effic_code(create_user, create_time, update_user, update_time, stamp, id, code_type, code_val, code_idx,
                           code_is_active, code_desc)
VALUES ('huaitian.zhang', '2025-06-09 10:11:12', 'huaitian.zhang', '2025-06-09 10:11:12', 0,
        20009, 200, '非计划', 9, 1, '测试任务类型（200）：非计划（09）');


-- 测试项目 --> 测试报告 --> 测试任务：
-- 1、测试项目 & 报告
select proj.id as proj_id,
       proj.name as proj_name,
       proj.owner as proj_owner,
       proj.actual_end as proj_actual_end,
       repo.id as repo_id,
       repo.title as repo_title,
       repo.status as repo_status,
       repo.creator as repo_creator,
       repo.approver as repo_approver
from dev_effic_test_project proj
inner join dev_effic_test_report repo on repo.project_id = proj.id
where repo.status = 'success'
and proj.actual_end >= '2025-05-01'
and proj.actual_end <= '2025-05-30'
and (proj.owner like '%许福培%' or proj.create_user like '%许福培%')
and proj.id not in (select code_val from dev_effic_code where code_type = 201)
;
