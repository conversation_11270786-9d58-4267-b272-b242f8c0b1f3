-- jenkins：Gateway_to_Biz_Low_sync_tapdData_to_Biz(ETL：task、bug、launch_form)
/*
1.sync tapdTask to testTask --> sync_data_from_gateway/?entry_type=task&env_type=test
2.sync tapdTask to devTask --> sync_data_from_gateway/?entry_type=task&env_type=dev
3.sync tapdBug to testBug --> sync_data_from_gateway/?entry_type=bug&env_type=test
4.sync tapdLaunchForm to bizLaunchForm --> sync_data_from_gateway/?entry_type=launch_form&env_type=test
5.sync tapdStroy to devStory --> sync_data_from_gateway/?entry_type=story&env_type=dev
6.sync teststory to dev submit --> sync_data_from_gateway/?entry_type=story&env_type=test
 */

-- 实体表：
select * from tapd_entry_timesheet order by id desc;

-- 一、创建表
-- ----------------------------
-- 1、研效开发任务工时：dev_effic_dev_task_timesheet
-- 说明：
-- ----------------------------
select * from mantis.dev_effic_dev_task_timesheet;
DROP TABLE IF EXISTS mantis.dev_effic_dev_task_timesheet;
CREATE TABLE mantis.dev_effic_dev_task_timesheet
(
    create_user                   VARCHAR(20) COMMENT '创建人',
    create_time                   DATETIME(0) COMMENT '创建时间',
    update_user                   VARCHAR(20) COMMENT '修改人',
    update_time                   DATETIME(0) COMMENT '修改时间',
    stamp                         BIGINT(20) DEFAULT 0 COMMENT '版本',
    id                            BIGINT(20) AUTO_INCREMENT,
    dev_task_timesheet_id         BIGINT(20)  NOT NULL comment 'tapd耗时ID',
    dev_workspace_id              BIGINT(20) comment 'tapd项目ID',
    dev_task_id                   BIGINT(20) comment 'tapd任务ID',
    dev_task_timesheet_owner      varchar(50) null comment '所有者',
    dev_task_timesheet_created    varchar(20) null comment '创建时间',
    dev_task_timesheet_modified   varchar(20) null comment '修改时间',
    dev_task_timesheet_spentdate  varchar(20) null comment '花费日期',
    dev_task_timesheet_timespent  varchar(20) null comment '花费工时',
    dev_task_timesheet_timeremain varchar(20) null comment '剩余工时',
    dev_task_timesheet_memo       text        null comment '花费描述',
    dev_task_timesheet_desc       varchar(255) COMMENT '研发任务工时说明',
    PRIMARY KEY (id),
    UNIQUE KEY udx_timesheet_id (dev_task_timesheet_id)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '研效开发任务工时';


-- ----------------------------
-- 2、研效测试任务工时：dev_effic_test_task_timesheet
-- 说明：
-- ----------------------------
select * from mantis.dev_effic_test_task_timesheet;
DROP TABLE IF EXISTS mantis.dev_effic_test_task_timesheet;
CREATE TABLE mantis.dev_effic_test_task_timesheet
(
    create_user                    VARCHAR(20) COMMENT '创建人',
    create_time                    DATETIME(0) COMMENT '创建时间',
    update_user                    VARCHAR(20) COMMENT '修改人',
    update_time                    DATETIME(0) COMMENT '修改时间',
    stamp                          BIGINT(20) DEFAULT 0 COMMENT '版本',
    id                             BIGINT(20) AUTO_INCREMENT,
    test_task_timesheet_id         BIGINT(20)  NOT NULL comment 'tapd耗时ID',
    test_workspace_id              BIGINT(20) comment 'tapd项目ID',
    test_task_id                   BIGINT(20) comment 'tapd任务ID',
    test_task_timesheet_owner      varchar(50) null comment '所有者',
    test_task_timesheet_created    varchar(20) null comment '创建时间',
    test_task_timesheet_modified   varchar(20) null comment '修改时间',
    test_task_timesheet_spentdate  varchar(20) null comment '花费日期',
    test_task_timesheet_timespent  varchar(20) null comment '花费工时',
    test_task_timesheet_timeremain varchar(20) null comment '剩余工时',
    test_task_timesheet_memo       text        null comment '花费描述',
    test_task_timesheet_desc       varchar(255) COMMENT '研发任务工时说明',
    PRIMARY KEY (id),
    UNIQUE KEY udx_timesheet_id (test_task_timesheet_id)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '研效测试任务工时';


-- 二、开发实现
-- 配置表：
select * from tapd_workspace;
-- 1、开发任务工时：
SELECT tapd_timesheet.entry_status,
       tapd_timesheet.entry_desc,
       tapd_timesheet.tapd_entry_id,
       tapd_timesheet.tapd_workspace_id,
       tapd_timesheet.tapd_time_sheet_entity_id,
       tapd_timesheet.tapd_time_sheet_entity_type,
       tapd_timesheet.tapd_time_sheet_owner,
       tapd_timesheet.tapd_time_sheet_created,
       tapd_timesheet.tapd_time_sheet_modified,
       tapd_timesheet.tapd_time_sheet_spentdate,
       tapd_timesheet.tapd_time_sheet_timespent,
       tapd_timesheet.tapd_time_sheet_timeremain,
       tapd_timesheet.tapd_time_sheet_memo,
       dev_timesheet.id,
       dev_timesheet.dev_workspace_id,
       dev_timesheet.dev_task_id,
       dev_timesheet.dev_task_timesheet_id,
       dev_timesheet.dev_task_timesheet_owner,
       dev_timesheet.dev_task_timesheet_created,
       dev_timesheet.dev_task_timesheet_modified,
       dev_timesheet.dev_task_timesheet_spentdate,
       dev_timesheet.dev_task_timesheet_timespent,
       dev_timesheet.dev_task_timesheet_timeremain,
       dev_timesheet.dev_task_timesheet_memo
FROM tapd_entry_timesheet tapd_timesheet
         INNER JOIN tapd_workspace workspace ON workspace.tapd_workspace_id = tapd_timesheet.tapd_workspace_id
         LEFT JOIN dev_effic_dev_task_timesheet dev_timesheet
                   ON dev_timesheet.dev_task_timesheet_id = tapd_timesheet.tapd_entry_id
where workspace.workspace_type = 4
  AND workspace.workspace_is_active = 1
  AND (tapd_timesheet.update_time between '2025-01-08 13:40:00' AND '2025-01-08 13:59:56'
    OR tapd_timesheet.del_time between '2025-01-08 13:40:00' AND '2025-01-08 13:59:56')
;

-- 2、测试任务工时：
SELECT tapd_timesheet.entry_status,
       tapd_timesheet.entry_desc,
       tapd_timesheet.tapd_entry_id,
       tapd_timesheet.tapd_workspace_id,
       tapd_timesheet.tapd_time_sheet_entity_id,
       tapd_timesheet.tapd_time_sheet_entity_type,
       tapd_timesheet.tapd_time_sheet_owner,
       tapd_timesheet.tapd_time_sheet_created,
       tapd_timesheet.tapd_time_sheet_modified,
       tapd_timesheet.tapd_time_sheet_spentdate,
       tapd_timesheet.tapd_time_sheet_timespent,
       tapd_timesheet.tapd_time_sheet_timeremain,
       tapd_timesheet.tapd_time_sheet_memo,
       test_timesheet.id,
       test_timesheet.test_workspace_id,
       test_timesheet.test_task_id,
       test_timesheet.test_task_timesheet_id,
       test_timesheet.test_task_timesheet_owner,
       test_timesheet.test_task_timesheet_created,
       test_timesheet.test_task_timesheet_modified,
       test_timesheet.test_task_timesheet_spentdate,
       test_timesheet.test_task_timesheet_timespent,
       test_timesheet.test_task_timesheet_timeremain,
       test_timesheet.test_task_timesheet_memo
FROM tapd_entry_timesheet tapd_timesheet
         INNER JOIN tapd_workspace workspace ON workspace.tapd_workspace_id = tapd_timesheet.tapd_workspace_id
         LEFT JOIN dev_effic_test_task_timesheet test_timesheet
                   ON test_timesheet.test_task_timesheet_id = tapd_timesheet.tapd_entry_id
where workspace.workspace_type = 4
  AND workspace.workspace_is_active = 1
  AND (tapd_timesheet.update_time between '2025-01-08 13:40:00' AND '2025-01-08 13:59:56'
    OR tapd_timesheet.del_time between '2025-01-08 13:40:00' AND '2025-01-08 13:59:56')
;


-- 三、测试数据联调：
select * from tapd_data_sync_log where req_module = 'timesheet' and workspace_id = 4 and req_type = 'sync_data_to_gateway';
-- 1、开发工时：
-- 从产线拉10条数据：select * from tapd_entry_timesheet order by id desc limit 1,10;
insert into tapd_entry_timesheet (create_user, create_time, update_user, update_time, stamp, id, ins_batch_number, upd_batch_number, del_batch_number, del_user, del_time, rec_batch_number, rec_user, rec_time, entry_status, entry_desc, res_json, tapd_workspace_id, tapd_entry_id, tapd_time_sheet_entity_id, tapd_time_sheet_entity_type, tapd_time_sheet_owner, tapd_time_sheet_created, tapd_time_sheet_modified, tapd_time_sheet_spentdate, tapd_time_sheet_timespent, tapd_time_sheet_timeremain, tapd_time_sheet_memo, tapd_time_sheet_is_delete, tapd_time_sheet_custom_field_one, tapd_time_sheet_custom_field_two, tapd_time_sheet_custom_field_three, tapd_time_sheet_custom_field_four, tapd_time_sheet_custom_field_five, tapd_time_sheet_custom_field_six, tapd_time_sheet_custom_field_seven, tapd_time_sheet_custom_field_eight, tapd_time_sheet_custom_field_9, tapd_time_sheet_custom_field_10, tapd_time_sheet_custom_field_11, tapd_time_sheet_custom_field_12, tapd_time_sheet_custom_field_13, tapd_time_sheet_custom_field_14, tapd_time_sheet_custom_field_15, tapd_time_sheet_custom_field_16, tapd_time_sheet_custom_field_17, tapd_time_sheet_custom_field_18, tapd_time_sheet_custom_field_19, tapd_time_sheet_custom_field_20, tapd_time_sheet_custom_field_21, tapd_time_sheet_custom_field_22, tapd_time_sheet_custom_field_23, tapd_time_sheet_custom_field_24, tapd_time_sheet_custom_field_25, tapd_time_sheet_custom_field_26, tapd_time_sheet_custom_field_27, tapd_time_sheet_custom_field_28, tapd_time_sheet_custom_field_29, tapd_time_sheet_custom_field_30, tapd_time_sheet_custom_field_31, tapd_time_sheet_custom_field_32, tapd_time_sheet_custom_field_33, tapd_time_sheet_custom_field_34, tapd_time_sheet_custom_field_35, tapd_time_sheet_custom_field_36, tapd_time_sheet_custom_field_37, tapd_time_sheet_custom_field_38, tapd_time_sheet_custom_field_39, tapd_time_sheet_custom_field_40, tapd_time_sheet_custom_field_41, tapd_time_sheet_custom_field_42, tapd_time_sheet_custom_field_43, tapd_time_sheet_custom_field_44, tapd_time_sheet_custom_field_45, tapd_time_sheet_custom_field_46, tapd_time_sheet_custom_field_47, tapd_time_sheet_custom_field_48, tapd_time_sheet_custom_field_49, tapd_time_sheet_custom_field_50)
values  ('howbuyscm', '2025-01-08 10:11:10', 'howbuyscm', '2025-01-08 10:11:10', 0, 53196, 202501081010000022, null, null, '', null, null, '', null, 1, '', null, 36225275, 1136225275001235181, 1136225275001252727, 'task', '吴朝辉', '2025-01-08 08:59:17', '2025-01-08 08:59:17', '2025-01-07', '1', '', null, 0, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null),
        ('howbuyscm', '2025-01-08 10:11:10', 'howbuyscm', '2025-01-08 10:11:10', 0, 53195, 202501081010000022, null, null, '', null, null, '', null, 1, '', null, 36225275, 1136225275001235190, 1136225275001253261, 'task', '吴朝辉', '2025-01-08 09:02:02', '2025-01-08 09:02:37', '2025-01-07', '4', '', '环境打通,确定联调账号等.目前账户体系已成功.', 0, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null),
        ('howbuyscm', '2025-01-08 10:11:10', 'howbuyscm', '2025-01-08 10:11:10', 0, 53194, 202501081010000022, null, null, '', null, null, '', null, 1, '', null, 36225275, 1136225275001235219, 1136225275001252678, 'task', '王进_20230821111240', '2025-01-08 09:08:26', '2025-01-08 09:08:26', '2025-01-06', '2', '', null, 0, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null),
        ('howbuyscm', '2025-01-08 10:11:10', 'howbuyscm', '2025-01-08 10:11:10', 0, 53193, 202501081010000022, null, null, '', null, null, '', null, 1, '', null, 36225275, 1136225275001235221, 1136225275001252675, 'task', '王进_20230821111240', '2025-01-08 09:08:45', '2025-01-08 09:08:45', '2025-01-06', '2', '', null, 0, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null),
        ('howbuyscm', '2025-01-08 10:11:09', 'howbuyscm', '2025-01-08 10:11:09', 0, 53192, 202501081010000020, null, null, '', null, null, '', null, 1, '', null, 63231183, 1163231183001235176, 1163231183001251053, 'task', '季秋亮', '2025-01-08 08:57:53', '2025-01-08 08:57:53', '2025-01-07', '3', '', null, 0, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null),
        ('howbuyscm', '2025-01-08 10:11:09', 'howbuyscm', '2025-01-08 10:11:09', 0, 53191, 202501081010000020, null, null, '', null, null, '', null, 1, '', null, 63231183, 1163231183001235184, 1163231183001252454, 'task', '韩春生', '2025-01-08 09:00:15', '2025-01-08 09:00:15', '2025-01-07', '4', '', null, 0, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null),
        ('howbuyscm', '2025-01-08 10:11:09', 'howbuyscm', '2025-01-08 10:11:09', 0, 53190, 202501081010000020, null, null, '', null, null, '', null, 1, '', null, 63231183, 1163231183001235185, 1163231183001252456, 'task', '韩春生', '2025-01-08 09:00:33', '2025-01-08 09:00:46', '2025-01-07', '4', '', null, 0, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null),
        ('howbuyscm', '2025-01-08 10:11:09', 'howbuyscm', '2025-01-08 10:11:09', 0, 53189, 202501081010000020, null, null, '', null, null, '', null, 1, '', null, 63231183, 1163231183001235189, 1163231183001252936, 'task', '周祥', '2025-01-08 09:01:55', '2025-01-08 09:01:55', '2025-01-07', '1', '', null, 0, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null),
        ('howbuyscm', '2025-01-08 10:11:09', 'howbuyscm', '2025-01-08 10:11:09', 0, 53188, 202501081010000020, null, null, '', null, null, '', null, 1, '', null, 63231183, 1163231183001235192, 1163231183001253270, 'task', '周祥', '2025-01-08 09:02:17', '2025-01-08 09:02:17', '2025-01-07', '6', '', null, 0, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null),
        ('howbuyscm', '2025-01-08 10:11:09', 'howbuyscm', '2025-01-08 10:11:09', 0, 53187, 202501081010000020, null, null, '', null, null, '', null, 1, '', null, 63231183, 1163231183001235198, 1163231183001252461, 'task', '潘安军', '2025-01-08 09:03:04', '2025-01-08 09:03:17', '2025-01-07', '5', '', '', 0, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null);
update tapd_entry_timesheet set update_user = 'huaitian.zhang', update_time = '2025-01-08 14:05:00', entry_status = 3, tapd_time_sheet_memo = '变更测试。zt@2025-01-08' where id in(53187, 53188, 53189, 53190, 53191, 53192, 53193, 53194, 53195, 53196);
select * from tapd_entry_timesheet where id in(53187, 53188, 53189, 53190, 53191, 53192, 53193, 53194, 53195, 53196);
select * from dev_effic_dev_task_timesheet;

-- 2、测试工时：
-- 从产线拉10条数据：
-- timesheet同步--测试工时：
select timesheet.*
from tapd_entry_timesheet timesheet
inner join tapd_workspace workspace on workspace.tapd_workspace_id = timesheet.tapd_workspace_id and workspace.workspace_type = 3
where 1=1
order by timesheet.id desc
limit 1, 10;
-- 批量插入：
insert into tapd_entry_timesheet (create_user, create_time, update_user, update_time, stamp, id, ins_batch_number, upd_batch_number, del_batch_number, del_user, del_time, rec_batch_number, rec_user, rec_time, entry_status, entry_desc, res_json, tapd_workspace_id, tapd_entry_id, tapd_time_sheet_entity_id, tapd_time_sheet_entity_type, tapd_time_sheet_owner, tapd_time_sheet_created, tapd_time_sheet_modified, tapd_time_sheet_spentdate, tapd_time_sheet_timespent, tapd_time_sheet_timeremain, tapd_time_sheet_memo, tapd_time_sheet_is_delete, tapd_time_sheet_custom_field_one, tapd_time_sheet_custom_field_two, tapd_time_sheet_custom_field_three, tapd_time_sheet_custom_field_four, tapd_time_sheet_custom_field_five, tapd_time_sheet_custom_field_six, tapd_time_sheet_custom_field_seven, tapd_time_sheet_custom_field_eight, tapd_time_sheet_custom_field_9, tapd_time_sheet_custom_field_10, tapd_time_sheet_custom_field_11, tapd_time_sheet_custom_field_12, tapd_time_sheet_custom_field_13, tapd_time_sheet_custom_field_14, tapd_time_sheet_custom_field_15, tapd_time_sheet_custom_field_16, tapd_time_sheet_custom_field_17, tapd_time_sheet_custom_field_18, tapd_time_sheet_custom_field_19, tapd_time_sheet_custom_field_20, tapd_time_sheet_custom_field_21, tapd_time_sheet_custom_field_22, tapd_time_sheet_custom_field_23, tapd_time_sheet_custom_field_24, tapd_time_sheet_custom_field_25, tapd_time_sheet_custom_field_26, tapd_time_sheet_custom_field_27, tapd_time_sheet_custom_field_28, tapd_time_sheet_custom_field_29, tapd_time_sheet_custom_field_30, tapd_time_sheet_custom_field_31, tapd_time_sheet_custom_field_32, tapd_time_sheet_custom_field_33, tapd_time_sheet_custom_field_34, tapd_time_sheet_custom_field_35, tapd_time_sheet_custom_field_36, tapd_time_sheet_custom_field_37, tapd_time_sheet_custom_field_38, tapd_time_sheet_custom_field_39, tapd_time_sheet_custom_field_40, tapd_time_sheet_custom_field_41, tapd_time_sheet_custom_field_42, tapd_time_sheet_custom_field_43, tapd_time_sheet_custom_field_44, tapd_time_sheet_custom_field_45, tapd_time_sheet_custom_field_46, tapd_time_sheet_custom_field_47, tapd_time_sheet_custom_field_48, tapd_time_sheet_custom_field_49, tapd_time_sheet_custom_field_50)
values  ('howbuyscm', '2025-01-08 10:11:02', 'howbuyscm', '2025-01-08 10:11:02', 0, 53102, 202501081010000006, null, null, '', null, null, '', null, 1, '', null, 55014084, 1155014084001235166, 1155014084001253243, 'task', '刘彬彬', '2025-01-08 08:40:51', '2025-01-08 08:40:51', '2025-01-07', '5', '', null, 0, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null),
        ('howbuyscm', '2025-01-08 10:11:02', 'howbuyscm', '2025-01-08 10:11:02', 0, 53101, 202501081010000006, null, null, '', null, null, '', null, 1, '', null, 55014084, 1155014084001235220, 1155014084001252723, 'task', '王宇', '2025-01-08 09:08:38', '2025-01-08 09:08:43', '2025-01-07', '3', '', null, 0, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null),
        ('howbuyscm', '2025-01-08 10:11:02', 'howbuyscm', '2025-01-08 10:11:02', 0, 53100, 202501081010000006, null, null, '', null, null, '', null, 1, '', null, 55014084, 1155014084001235222, 1155014084001252724, 'task', '王宇', '2025-01-08 09:08:52', '2025-01-08 09:08:55', '2025-01-08', '3', '', null, 0, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null),
        ('howbuyscm', '2025-01-08 10:11:02', 'howbuyscm', '2025-01-08 10:11:02', 0, 53099, 202501081010000006, null, null, '', null, null, '', null, 1, '', null, 55014084, 1155014084001235223, 1155014084001252725, 'task', '王宇', '2025-01-08 09:09:06', '2025-01-08 09:09:10', '2025-01-06', '3', '', null, 0, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null),
        ('howbuyscm', '2025-01-08 10:11:02', 'howbuyscm', '2025-01-08 10:11:02', 0, 53098, 202501081010000006, null, null, '', null, null, '', null, 1, '', null, 55014084, 1155014084001235225, 1155014084001253313, 'task', '王宇', '2025-01-08 09:09:54', '2025-01-08 09:09:54', '2025-01-06', '6', '', null, 0, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null),
        ('howbuyscm', '2025-01-08 10:11:02', 'howbuyscm', '2025-01-08 10:11:02', 0, 53097, 202501081010000006, null, null, '', null, null, '', null, 1, '', null, 55014084, 1155014084001235238, 1155014084001253334, 'task', '刘密密', '2025-01-08 09:17:42', '2025-01-08 09:17:42', '2025-01-07', '8', '', null, 0, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null),
        ('howbuyscm', '2025-01-08 10:11:02', 'howbuyscm', '2025-01-08 10:11:02', 0, 53096, 202501081010000006, null, null, '', null, null, '', null, 1, '', null, 55014084, 1155014084001235248, 1155014084001252781, 'task', '陆文龙', '2025-01-08 09:22:58', '2025-01-08 09:22:58', '2025-01-07', '4', '', null, 0, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null),
        ('howbuyscm', '2025-01-08 10:11:02', 'howbuyscm', '2025-01-08 10:11:02', 0, 53095, 202501081010000006, null, null, '', null, null, '', null, 1, '', null, 55014084, 1155014084001235253, 1155014084001253343, 'task', '陆文龙', '2025-01-08 09:24:46', '2025-01-08 09:24:46', '2025-01-07', '6', '', null, 0, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null),
        ('howbuyscm', '2025-01-08 10:11:02', 'howbuyscm', '2025-01-08 10:11:02', 0, 53094, 202501081010000006, null, null, '', null, null, '', null, 1, '', null, 55014084, 1155014084001235254, 1155014084001252146, 'task', '刘梦会', '2025-01-08 09:27:06', '2025-01-08 09:27:06', '2025-01-06', '3', '', null, 0, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null),
        ('howbuyscm', '2025-01-08 10:11:02', 'howbuyscm', '2025-01-08 10:11:02', 0, 53093, 202501081010000006, null, null, '', null, null, '', null, 1, '', null, 55014084, 1155014084001235256, 1155014084001252144, 'task', '刘梦会', '2025-01-08 09:27:17', '2025-01-08 09:27:17', '2025-01-06', '2', '', null, 0, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null);
-- 批量更新：
update tapd_entry_timesheet set update_user = 'huaitian.zhang', update_time = '2025-01-08 14:53:00'where id in(53093, 53094, 53095, 53096, 53097, 53098, 53099, 53100, 53101, 53102);
update tapd_entry_timesheet set update_user = 'huaitian.zhang', update_time = '2025-01-08 15:08:00', entry_status = 3, tapd_time_sheet_memo = '变更测试。zt@2025-01-08' where id in(53093, 53094, 53095, 53096, 53097, 53098, 53099, 53100, 53101, 53102);
select * from tapd_entry_timesheet where id in (53093, 53094, 53095, 53096, 53097, 53098, 53099, 53100, 53101, 53102);
select * from dev_effic_test_task_timesheet;

-- 四、刷历史数据：
-- 1、所有研发的工时数据，刷入新的业务表：(先重建表，保持ID从1开始。)
select timesheet.*
from tapd_entry_timesheet timesheet
inner join tapd_workspace workspace on workspace.tapd_workspace_id = timesheet.tapd_workspace_id
where workspace.workspace_type = 4;
select * from dev_effic_dev_task_timesheet;
INSERT INTO dev_effic_dev_task_timesheet (create_user, create_time, update_user, update_time, stamp,
                                          dev_task_timesheet_id, dev_workspace_id, dev_task_id,
                                          dev_task_timesheet_owner, dev_task_timesheet_created, dev_task_timesheet_modified, dev_task_timesheet_spentdate,
                                          dev_task_timesheet_timespent, dev_task_timesheet_timeremain, dev_task_timesheet_memo, dev_task_timesheet_desc)
SELECT t.create_user,
       t.create_time,
       t.update_user,
       t.update_time,
       t.stamp,
       t.tapd_entry_id,
       t.tapd_workspace_id,
       t.tapd_time_sheet_entity_id,
       t.tapd_time_sheet_owner,
       t.tapd_time_sheet_created,
       t.tapd_time_sheet_modified,
       t.tapd_time_sheet_spentdate,
       t.tapd_time_sheet_timespent,
       t.tapd_time_sheet_timeremain,
       t.tapd_time_sheet_memo,
       '初始化批量刷入。zt@2025-01-08'
FROM tapd_entry_timesheet t
INNER JOIN tapd_workspace w ON w.tapd_workspace_id = t.tapd_workspace_id
WHERE w.workspace_type = 4
ORDER BY t.id;

-- 2、测试工时，初始化刷入数据：
select * from dev_effic_test_task_timesheet;
INSERT INTO dev_effic_test_task_timesheet (create_user, create_time, update_user, update_time, stamp,
                                           test_task_timesheet_id,
                                           test_workspace_id,
                                           test_task_id,
                                           test_task_timesheet_owner,
                                           test_task_timesheet_created,
                                           test_task_timesheet_modified,
                                           test_task_timesheet_spentdate,
                                           test_task_timesheet_timespent,
                                           test_task_timesheet_timeremain,
                                           test_task_timesheet_memo,
                                           test_task_timesheet_desc)
SELECT t.create_user,
       t.create_time,
       t.update_user,
       t.update_time,
       t.stamp,
       t.tapd_entry_id,
       t.tapd_workspace_id,
       t.tapd_time_sheet_entity_id,
       t.tapd_time_sheet_owner,
       t.tapd_time_sheet_created,
       t.tapd_time_sheet_modified,
       t.tapd_time_sheet_spentdate,
       t.tapd_time_sheet_timespent,
       t.tapd_time_sheet_timeremain,
       t.tapd_time_sheet_memo,
       '初始化批量刷入。zt@2025-01-08'
FROM tapd_entry_timesheet t
INNER JOIN tapd_workspace w ON w.tapd_workspace_id = t.tapd_workspace_id
WHERE w.workspace_type = 3
ORDER BY t.id;