-- 禁用唐镇灾备4个可用区_for_李佳。zt@2025-01-20
select * from node_apply_zone where relative_region_code = 'bs-zb';

-- 查看可用区的所有机型、绑定等信息：
select zone.id as zone_id,
       zone.zone_idx,
       zone.zone_code,
       zone.relative_region_code,
       zone.zone_is_active,
       zone.zone_bind_desc,
       vm_bind.id as bind_id,
       vm_bind.bind_is_active,
       vm_bind.bind_desc,
       vm_type.id as vm_id,
       vm_type.vm_name, vm_type.vm_alias, vm_type.vm_is_active, vm_type.vm_desc
from node_apply_zone zone
left join node_apply_vm_bind vm_bind on vm_bind.zone_id = zone.id and vm_bind.bind_is_active = 1
left join node_apply_vm_type vm_type on vm_type.id = vm_bind.vm_id
where relative_region_code = 'bs-zb'
;

-- 只禁用可用区下「已启用」的节点类型绑定，不去处理「可用区」：
update node_apply_vm_bind set bind_is_active = FALSE, update_user = 'huaitian.zhang', update_time = '2025-01-20 10:11:12', bind_desc = '禁用灾备4个可用区_for_李佳。zt@2025-01-20' where id = 1424 and bind_is_active = 1;
update node_apply_vm_bind set bind_is_active = FALSE, update_user = 'huaitian.zhang', update_time = '2025-01-20 10:11:12', bind_desc = '禁用灾备4个可用区_for_李佳。zt@2025-01-20' where id = 1425 and bind_is_active = 1;

