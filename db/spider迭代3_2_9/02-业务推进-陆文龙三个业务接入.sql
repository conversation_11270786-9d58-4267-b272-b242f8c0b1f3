-- 参考「下单和批处理」，新建三个储蓄罐的业务_for_陆文龙。zt@2025-01-21
-- 1-1、找帅新建3个业务，并使用Jenkins同步：
--  http://jkp-master.howbuy.pa/jenkins/blue/organizations/jenkins/upd_biz_name_list_from_scene/detail/upd_biz_name_list_from_scene/142/pipeline/

-- 1-2、确定同步的业务数据：
select * from biz_base_info order by id desc;
select * from biz_base_type where id = 123;
/*
12408	123	RETAIL-T2-20231106		中台交易-储蓄罐-储蓄罐自动化T2-20231106
12407	123	RETAIL-WEEKEND-20231104-05		中台交易-储蓄罐-储蓄罐自动化周末-20231104-05
12406	123	RETAIL-T1-20231103		中台交易-储蓄罐-储蓄罐自动化T1-20231103
 */
select * from biz_base_info where biz_scenario_name like '%下单和批处理';
-- 10436	106	FTX-BATCH-ORDER	FTX	储蓄罐	BATCH-ORDER	下单和批处理

-- 1-3、基础库集（虽然已改造，但基础库集还影响定时，暂时加上）
-- 49	Howbuy-ALL	howbuy-all
select * from biz_base_db_bind where biz_code in ('FTX-BATCH-ORDER'); -- it304
INSERT INTO biz_base_db_bind (biz_code, biz_base_db_code, biz_base_db_bind_is_active, biz_base_db_bind_desc, create_user, create_time, update_user, update_time, stamp)
VALUES ('RETAIL-T2-20231106', 'it304', 1, '绑定基础库集「it304」。zt@2025-01-21」', 'huaitian.zhang', '2025-01-21 10:11:12', 'huaitian.zhang', '2025-01-21 10:11:12', 0);
INSERT INTO biz_base_db_bind (biz_code, biz_base_db_code, biz_base_db_bind_is_active, biz_base_db_bind_desc, create_user, create_time, update_user, update_time, stamp)
VALUES ('RETAIL-WEEKEND-20231104-05', 'it304', 1, '绑定基础库集「it304」。zt@2025-01-21」', 'huaitian.zhang', '2025-01-21 10:11:12', 'huaitian.zhang', '2025-01-21 10:11:12', 0);
INSERT INTO biz_base_db_bind (biz_code, biz_base_db_code, biz_base_db_bind_is_active, biz_base_db_bind_desc, create_user, create_time, update_user, update_time, stamp)
VALUES ('RETAIL-T1-20231103', 'it304', 1, '绑定基础库集「it304」。zt@2025-01-21」', 'huaitian.zhang', '2025-01-21 10:11:12', 'huaitian.zhang', '2025-01-21 10:11:12', 0);


-- 2、3个迭代数据：
-- RETAIL-T2-20231106
insert into biz_test_iter(biz_test_iter_id, biz_code, biz_test_iter_br, br_status, br_start_time, br_end_time, br_from, create_user, create_time, update_user, update_time, stamp, pause_test_data_recording)
VALUES ('RETAIL-T2-20231106_dev', 'RETAIL-T2-20231106', 'dev', 'open', '2025-01-21 10:11:12', NULL, NULL, 'huaitian.zhang', '2025-01-21 10:11:12', 'huaitian.zhang', '2025-01-21 10:11:12', 0, 0);
insert into biz_test_iter(biz_test_iter_id, biz_code, biz_test_iter_br, br_status, br_start_time, br_end_time, br_from, create_user, create_time, update_user, update_time, stamp, pause_test_data_recording)
VALUES ('RETAIL-T2-20231106_master', 'RETAIL-T2-20231106', 'master', 'open', '2025-01-21 10:11:12', NULL, NULL, 'huaitian.zhang', '2025-01-21 10:11:12', 'huaitian.zhang', '2025-01-21 10:11:12', 0, 0);
insert into biz_test_iter(biz_test_iter_id, biz_code, biz_test_iter_br, br_status, br_start_time, br_end_time, br_from, create_user, create_time, update_user, update_time, stamp, pause_test_data_recording)
VALUES ('RETAIL-T2-20231106_longterm', 'RETAIL-T2-20231106', 'longterm', 'open', '2025-01-21 10:11:12', NULL, NULL, 'huaitian.zhang', '2025-01-21 10:11:12', 'huaitian.zhang', '2025-01-21 10:11:12', 0, 0);
-- RETAIL-WEEKEND-20231104-05
insert into biz_test_iter(biz_test_iter_id, biz_code, biz_test_iter_br, br_status, br_start_time, br_end_time, br_from, create_user, create_time, update_user, update_time, stamp, pause_test_data_recording)
VALUES ('RETAIL-WEEKEND-20231104-05_dev', 'RETAIL-WEEKEND-20231104-05', 'dev', 'open', '2025-01-21 10:11:12', NULL, NULL, 'huaitian.zhang', '2025-01-21 10:11:12', 'huaitian.zhang', '2025-01-21 10:11:12', 0, 0);
insert into biz_test_iter(biz_test_iter_id, biz_code, biz_test_iter_br, br_status, br_start_time, br_end_time, br_from, create_user, create_time, update_user, update_time, stamp, pause_test_data_recording)
VALUES ('RETAIL-WEEKEND-20231104-05_master', 'RETAIL-WEEKEND-20231104-05', 'master', 'open', '2025-01-21 10:11:12', NULL, NULL, 'huaitian.zhang', '2025-01-21 10:11:12', 'huaitian.zhang', '2025-01-21 10:11:12', 0, 0);
insert into biz_test_iter(biz_test_iter_id, biz_code, biz_test_iter_br, br_status, br_start_time, br_end_time, br_from, create_user, create_time, update_user, update_time, stamp, pause_test_data_recording)
VALUES ('RETAIL-WEEKEND-20231104-05_longterm', 'RETAIL-WEEKEND-20231104-05', 'longterm', 'open', '2025-01-21 10:11:12', NULL, NULL, 'huaitian.zhang', '2025-01-21 10:11:12', 'huaitian.zhang', '2025-01-21 10:11:12', 0, 0);
-- RETAIL-T1-20231103
insert into biz_test_iter(biz_test_iter_id, biz_code, biz_test_iter_br, br_status, br_start_time, br_end_time, br_from, create_user, create_time, update_user, update_time, stamp, pause_test_data_recording)
VALUES ('RETAIL-T1-20231103_dev', 'RETAIL-T1-20231103', 'dev', 'open', '2025-01-21 10:11:12', NULL, NULL, 'huaitian.zhang', '2025-01-21 10:11:12', 'huaitian.zhang', '2025-01-21 10:11:12', 0, 0);
insert into biz_test_iter(biz_test_iter_id, biz_code, biz_test_iter_br, br_status, br_start_time, br_end_time, br_from, create_user, create_time, update_user, update_time, stamp, pause_test_data_recording)
VALUES ('RETAIL-T1-20231103_master', 'RETAIL-T1-20231103', 'master', 'open', '2025-01-21 10:11:12', NULL, NULL, 'huaitian.zhang', '2025-01-21 10:11:12', 'huaitian.zhang', '2025-01-21 10:11:12', 0, 0);
insert into biz_test_iter(biz_test_iter_id, biz_code, biz_test_iter_br, br_status, br_start_time, br_end_time, br_from, create_user, create_time, update_user, update_time, stamp, pause_test_data_recording)
VALUES ('RETAIL-T1-20231103_longterm', 'RETAIL-T1-20231103', 'longterm', 'open', '2025-01-21 10:11:12', NULL, NULL, 'huaitian.zhang', '2025-01-21 10:11:12', 'huaitian.zhang', '2025-01-21 10:11:12', 0, 0);


-- 3、对应的dump文件数据：
-- 3-1、用户手动关联应用
select biz_app_bind.*
from biz_app_bind
where biz_app_bind.biz_code in ('RETAIL-T2-20231106', 'RETAIL-WEEKEND-20231104-05', 'RETAIL-T1-20231103')
order by biz_app_bind.app_module_name
;

/*
docker_it24_zhzx_0 --> PC_DOCKER_IT29_ZHZX_0_db_BACKUP_20240710160128.sql
docker_it24_zhzx_index --> PC_DOCKER_IT29_ZHZX_INDEX_db_BACKUP_20240710160128.sql
docker_it24_zhzx_1 --> PC_DOCKER_IT29_ZHZX_1_db_BACKUP_20240710160128.sql
docker_it24_deal_0 --> PC_DOCKER_IT29_DEAL_0_db_BACKUP_20240710160128.sql
docker_it24_deal_1 --> PC_DOCKER_IT29_DEAL_1_db_BACKUP_20240710160128.sql

DOCKER_IT24_TRADE --> PC_DOCKER_IT29_TRADE_db_BACKUP_20240710160128.dmp
DOCKER_IT24_PAY --> PC_DOCKER_IT29_PAY_db_BACKUP_20240710160128.dmp

docker_it24_his --> PC_DOCKER_IT29_HIS_db_BACKUP_20240710160128.sql
docker_it24_report --> PC_DOCKER_IT29_REPORT_db_BACKUP_20240710160128.sql
docker_it24_settle --> PC_DOCKER_IT29_SETTLE_db_BACKUP_20240710160128.sql
docker_it24_message --> PC_DOCKER_IT29_MESSAGE_db_BACKUP_20240710160128.sql
docker_it24_param --> PC_DOCKER_IT29_PARAM_db_BACKUP_20240710160128.sql
docker_it24_product --> PC_DOCKER_IT29_PRODUCT_db_BACKUP_20240710160128.sql

docker_it24_tmsmock --> PC_DOCKER_IT29_TMSMOCK_db_BACKUP_20240710160128.sql
howbuy_quartz_it24 --> HOWBUY_QUARTZ_IT24_20241212101112001.sql

RETAIL-T2-20231106 --> 20250123042257958
RETAIL-WEEKEND-20231104-05 --> 20250123050227886
RETAIL-T1-20231103 --> 20250123050934418

PC_DOCKER_IT29_DEAL_0_db_BACKUP_20250123042257958
 */
-- 查询：
select biz.biz_code,
       biz.biz_name,
       CONCAT(biz_type.biz_type_department, biz_type.biz_type_transaction, '-', biz.biz_name, '-', biz.biz_scenario_name) as biz_info_name,
       biz_app_bind.biz_app_bind_type, biz_app_bind.app_module_name,
       domain.domain_name, domain.domain_desc, domain.db_alias,
       logic_info.id as logic_id, logic_info.logic_db_name,
       suite_bind.db_bind_suite_code,
       db_info.id as db_info_id, db_info.suite_db_name, db_info.db_info_username, db_info.db_info_password,
       db_srv.db_srv_hosts, db_srv.db_srv_username, db_srv.db_srv_password, db_srv.db_srv_socket_path, db_srv.data_dump_dir,
       db_group.db_group_name,
       biz_iter.biz_test_iter_id,
       dump.id as dump_id, dump.db_info_id, dump_suite_bind.db_bind_suite_code, dump.dump_file_name
from biz_base_info biz
inner join biz_base_type biz_type on biz_type.id = biz.biz_type
left join biz_app_bind on biz_app_bind.biz_code = biz.biz_code              -- 业务绑定的应用
left join db_mgt_app_bind app_bind on app_bind.app_module_name = biz_app_bind.app_module_name       -- 应用绑定的库（总）
left join db_mgt_domain domain on domain.id = app_bind.db_domain_id                                 -- 1、库信息（主要关联）：领域->逻辑库->环境->物理库->服务器
left join db_mgt_logic_info logic_info on logic_info.db_domain_id = app_bind.db_domain_id           -- 都绑定「domain_id」（可跳过）
left join db_mgt_suite_bind suite_bind on suite_bind.db_logic_id = logic_info.id                    -- 新建「逻辑表」
left join db_mgt_info db_info on db_info.id = suite_bind.db_info_id
left join db_mgt_srv db_srv on db_srv.id = db_info.db_srv_id
left join db_mgt_group db_group on db_group.id = domain.db_group_id         -- 2、组信息
left join biz_test_iter biz_iter on biz_iter.biz_code = biz.biz_code        -- 3、业务迭代（已定死：dev longterm master）
left join db_mgt_dump_file dump on dump.biz_test_iter_id = biz_iter.biz_test_iter_id
                                       and dump.db_logic_id = logic_info.id   -- 4、业务迭代对应的dump文件（已唯一不用max）
left join db_mgt_info dump_db_info on dump_db_info.id = dump.db_info_id     -- 5、确定dump出自哪个环境
left join db_mgt_suite_bind dump_suite_bind on dump_suite_bind.db_info_id = dump_db_info.id
where 1=1
and biz.biz_code = 'RETAIL-T2-20231106'
and suite_bind.db_bind_suite_code = 'it24'
and biz_iter.biz_test_iter_br in('master')
# and biz_iter.biz_test_iter_br in('dev', 'master')
order by biz.biz_type, biz.biz_code,
    biz_app_bind.biz_app_bind_type, biz_app_bind.app_module_name,
    domain.domain_name
;
select * from db_mgt_dump_file where id in(1475, 1493);
-- 使用其它业务的dump文件，绑定到新的业务上：
-- 1、dump文件的生成信息：
select * from db_mgt_dump_file where db_mgt_dump_file.biz_test_iter_id like 'RETAIL-T2-20231106_master';
select max(id) as dump_max_id from db_mgt_dump_file;
-- RETAIL-T2-20231106
-- deal
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-01-24 10:11:12', 'huaitian.zhang', '2025-01-24 10:11:12', 0, 1705, 'ftx_20240708', 'RETAIL-T2-20231106_master', 237, 10165, 1, '储蓄罐1102_DOCKER_IT24_DEAL_0_20250123042257.sql', 1, '参考「下单和批处理」，新建三个储蓄罐的业务_for_陆文龙。zt@2025-01-24');
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-01-24 10:11:12', 'huaitian.zhang', '2025-01-24 10:11:12', 0, 1706, 'ftx_20240708', 'RETAIL-T2-20231106_master', 238, 10241, 1, '储蓄罐1102_DOCKER_IT24_DEAL_1_20250123042257.sql', 1, '参考「下单和批处理」，新建三个储蓄罐的业务_for_陆文龙。zt@2025-01-24');
-- his
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-01-24 10:11:12', 'huaitian.zhang', '2025-01-24 10:11:12', 0, 1707, 'ftx_20240708', 'RETAIL-T2-20231106_master', 241, 10317, 1, '储蓄罐1102_DOCKER_IT24_HIS_20250123042257.sql', 1, '参考「下单和批处理」，新建三个储蓄罐的业务_for_陆文龙。zt@2025-01-24');
-- settle
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-01-24 10:11:12', 'huaitian.zhang', '2025-01-24 10:11:12', 0, 1708, 'ftx_20240708', 'RETAIL-T2-20231106_master', 259, 10773, 1, '储蓄罐1102_DOCKER_IT24_SETTLE_20250123042257.sql', 1, '参考「下单和批处理」，新建三个储蓄罐的业务_for_陆文龙。zt@2025-01-24');
-- report
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-01-24 10:11:12', 'huaitian.zhang', '2025-01-24 10:11:12', 0, 1709, 'ftx_20240708', 'RETAIL-T2-20231106_master', 327, 13053, 1, '储蓄罐1102_DOCKER_IT24_REPORT_20250123042257.sql', 1, '参考「下单和批处理」，新建三个储蓄罐的业务_for_陆文龙。zt@2025-01-24');
-- zhzx
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-01-24 10:11:12', 'huaitian.zhang', '2025-01-24 10:11:12', 0, 1710, 'acc_3.5.2', 'RETAIL-T2-20231106_master', 262, 6669, 1, '储蓄罐1102_DOCKER_IT24_ZHZX_0_20250123042257.sql', 1, '参考「下单和批处理」，新建三个储蓄罐的业务_for_陆文龙。zt@2025-01-24');
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-01-24 10:11:12', 'huaitian.zhang', '2025-01-24 10:11:12', 0, 1711, 'acc_3.5.2', 'RETAIL-T2-20231106_master', 263, 6745, 1, '储蓄罐1102_DOCKER_IT24_ZHZX_1_20250123042257.sql', 1, '参考「下单和批处理」，新建三个储蓄罐的业务_for_陆文龙。zt@2025-01-24');
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-01-24 10:11:12', 'huaitian.zhang', '2025-01-24 10:11:12', 0, 1712, 'acc_3.5.2', 'RETAIL-T2-20231106_master', 264, 6821, 1, '储蓄罐1102_DOCKER_IT24_ZHZX_INDEX_20250123042257.sql', 1, '参考「下单和批处理」，新建三个储蓄罐的业务_for_陆文龙。zt@2025-01-24');
-- message
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-01-24 10:11:12', 'huaitian.zhang', '2025-01-24 10:11:12', 0, 1713, 'tms-public_4.5.66', 'RETAIL-T2-20231106_master', 243, 5453, 1, '储蓄罐1102_DOCKER_IT24_MESSAGE_20250123042257.sql', 1, '参考「下单和批处理」，新建三个储蓄罐的业务_for_陆文龙。zt@2025-01-24');
-- param
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-01-24 10:11:12', 'huaitian.zhang', '2025-01-24 10:11:12', 0, 1714, 'tradenew_3.32.3', 'RETAIL-T2-20231106_master', 255, 6289, 1, '储蓄罐1102_DOCKER_IT24_PARAM_20250123042257.sql', 1, '参考「下单和批处理」，新建三个储蓄罐的业务_for_陆文龙。zt@2025-01-24');
-- trade
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-01-24 10:11:12', 'huaitian.zhang', '2025-01-24 10:11:12', 0, 1715, 'tradenew_3.32.3', 'RETAIL-T2-20231106_master', 261, 8645, 1, '储蓄罐1102_DOCKER_IT24_TRADE_20250123042257.dmp', 1, '参考「下单和批处理」，新建三个储蓄罐的业务_for_陆文龙。zt@2025-01-24');
-- pay
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-01-24 10:11:12', 'huaitian.zhang', '2025-01-24 10:11:12', 0, 1716, 'pay_20240528', 'RETAIL-T2-20231106_master', 256, 8721, 1, '储蓄罐1102_DOCKER_IT24_PAY_20250123042257.dmp', 1, '参考「下单和批处理」，新建三个储蓄罐的业务_for_陆文龙。zt@2025-01-24');
-- product
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-01-24 10:11:12', 'huaitian.zhang', '2025-01-24 10:11:12', 0, 1717, 'product-center_4.5.43', 'RETAIL-T2-20231106_master', 258, 6441, 1, '储蓄罐1102_DOCKER_IT24_PAY_20250123042257.dmp', 1, '参考「下单和批处理」，新建三个储蓄罐的业务_for_陆文龙。zt@2025-01-24');
-- tms-mock
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-01-24 10:11:12', 'huaitian.zhang', '2025-01-24 10:11:12', 0, 1718, 'tms-mock_4.0.82', 'RETAIL-T2-20231106_master', 260, 6517, 1, '储蓄罐1102_DOCKER_IT24_TMSMOCK_20250123042257.sql', 1, '参考「下单和批处理」，新建三个储蓄罐的业务_for_陆文龙。zt@2025-01-24');
-- quartz
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-01-24 10:11:12', 'huaitian.zhang', '2025-01-24 10:11:12', 0, 1719, 'schedule_3.6.0', 'RETAIL-T2-20231106_master', 326, 10616, 1, '储蓄罐1102_HOWBUY_QUARTZ_IT24_20250123042257.sql', 1, '参考「下单和批处理」，新建三个储蓄罐的业务_for_陆文龙。zt@2025-01-24');
-- RETAIL-T2-20231106_master --> RETAIL-T1-20231103_master
update db_mgt_dump_file set biz_test_iter_id = 'RETAIL-T1-20231103_master' where id between 1705 and 1719 and biz_test_iter_id = 'RETAIL-T2-20231106_master';
update db_mgt_dump_file set dump_file_name = '储蓄罐1102_DOCKER_IT24_PRODUCT_20250123042257.sql' where id = 1717 and biz_test_iter_id = 'RETAIL-T1-20231103_master' and dump_file_name = '储蓄罐1102_DOCKER_IT24_PAY_20250123042257.dmp';
select * from db_mgt_dump_file where id = 1717;


-- RETAIL-WEEKEND-20231104-05
select max(id) as dump_max_id from db_mgt_dump_file;
-- deal
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-01-24 10:11:12', 'huaitian.zhang', '2025-01-24 10:11:12', 0, 1720, 'ftx_20240708', 'RETAIL-WEEKEND-20231104-05_master', 237, 10165, 1, '储蓄罐1103_DOCKER_IT24_DEAL_0_20250123050227.sql', 1, '参考「下单和批处理」，新建三个储蓄罐的业务_for_陆文龙。zt@2025-01-24');
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-01-24 10:11:12', 'huaitian.zhang', '2025-01-24 10:11:12', 0, 1721, 'ftx_20240708', 'RETAIL-WEEKEND-20231104-05_master', 238, 10241, 1, '储蓄罐1103_DOCKER_IT24_DEAL_1_20250123050227.sql', 1, '参考「下单和批处理」，新建三个储蓄罐的业务_for_陆文龙。zt@2025-01-24');
-- his
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-01-24 10:11:12', 'huaitian.zhang', '2025-01-24 10:11:12', 0, 1722, 'ftx_20240708', 'RETAIL-WEEKEND-20231104-05_master', 241, 10317, 1, '储蓄罐1103_DOCKER_IT24_HIS_20250123050227.sql', 1, '参考「下单和批处理」，新建三个储蓄罐的业务_for_陆文龙。zt@2025-01-24');
-- settle
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-01-24 10:11:12', 'huaitian.zhang', '2025-01-24 10:11:12', 0, 1723, 'ftx_20240708', 'RETAIL-WEEKEND-20231104-05_master', 259, 10773, 1, '储蓄罐1103_DOCKER_IT24_SETTLE_20250123050227.sql', 1, '参考「下单和批处理」，新建三个储蓄罐的业务_for_陆文龙。zt@2025-01-24');
-- report
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-01-24 10:11:12', 'huaitian.zhang', '2025-01-24 10:11:12', 0, 1724, 'ftx_20240708', 'RETAIL-WEEKEND-20231104-05_master', 327, 13053, 1, '储蓄罐1103_DOCKER_IT24_REPORT_20250123050227.sql', 1, '参考「下单和批处理」，新建三个储蓄罐的业务_for_陆文龙。zt@2025-01-24');
-- zhzx
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-01-24 10:11:12', 'huaitian.zhang', '2025-01-24 10:11:12', 0, 1725, 'acc_3.5.2', 'RETAIL-WEEKEND-20231104-05_master', 262, 6669, 1, '储蓄罐1103_DOCKER_IT24_ZHZX_0_20250123050227.sql', 1, '参考「下单和批处理」，新建三个储蓄罐的业务_for_陆文龙。zt@2025-01-24');
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-01-24 10:11:12', 'huaitian.zhang', '2025-01-24 10:11:12', 0, 1726, 'acc_3.5.2', 'RETAIL-WEEKEND-20231104-05_master', 263, 6745, 1, '储蓄罐1103_DOCKER_IT24_ZHZX_1_20250123050227.sql', 1, '参考「下单和批处理」，新建三个储蓄罐的业务_for_陆文龙。zt@2025-01-24');
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-01-24 10:11:12', 'huaitian.zhang', '2025-01-24 10:11:12', 0, 1727, 'acc_3.5.2', 'RETAIL-WEEKEND-20231104-05_master', 264, 6821, 1, '储蓄罐1103_DOCKER_IT24_ZHZX_INDEX_20250123050227.sql', 1, '参考「下单和批处理」，新建三个储蓄罐的业务_for_陆文龙。zt@2025-01-24');
-- message
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-01-24 10:11:12', 'huaitian.zhang', '2025-01-24 10:11:12', 0, 1728, 'tms-public_4.5.66', 'RETAIL-WEEKEND-20231104-05_master', 243, 5453, 1, '储蓄罐1103_DOCKER_IT24_MESSAGE_20250123050227.sql', 1, '参考「下单和批处理」，新建三个储蓄罐的业务_for_陆文龙。zt@2025-01-24');
-- param
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-01-24 10:11:12', 'huaitian.zhang', '2025-01-24 10:11:12', 0, 1729, 'tradenew_3.32.3', 'RETAIL-WEEKEND-20231104-05_master', 255, 6289, 1, '储蓄罐1103_DOCKER_IT24_PARAM_20250123050227.sql', 1, '参考「下单和批处理」，新建三个储蓄罐的业务_for_陆文龙。zt@2025-01-24');
-- trade
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-01-24 10:11:12', 'huaitian.zhang', '2025-01-24 10:11:12', 0, 1730, 'tradenew_3.32.3', 'RETAIL-WEEKEND-20231104-05_master', 261, 8645, 1, '储蓄罐1103_DOCKER_IT24_TRADE_20250123050227.dmp', 1, '参考「下单和批处理」，新建三个储蓄罐的业务_for_陆文龙。zt@2025-01-24');
-- pay
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-01-24 10:11:12', 'huaitian.zhang', '2025-01-24 10:11:12', 0, 1731, 'pay_20240528', 'RETAIL-WEEKEND-20231104-05_master', 256, 8721, 1, '储蓄罐1102_DOCKER_IT24_PAY_20250123042257.dmp', 1, '参考「下单和批处理」，新建三个储蓄罐的业务_for_陆文龙。zt@2025-01-24');
-- product
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-01-24 10:11:12', 'huaitian.zhang', '2025-01-24 10:11:12', 0, 1732, 'product-center_4.5.43', 'RETAIL-WEEKEND-20231104-05_master', 258, 6441, 1, '储蓄罐1103_DOCKER_IT24_PRODUCT_20250123050227.sql', 1, '参考「下单和批处理」，新建三个储蓄罐的业务_for_陆文龙。zt@2025-01-24');
-- tms-mock
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-01-24 10:11:12', 'huaitian.zhang', '2025-01-24 10:11:12', 0, 1733, 'tms-mock_4.0.82', 'RETAIL-WEEKEND-20231104-05_master', 260, 6517, 1, '储蓄罐1103_DOCKER_IT24_TMSMOCK_20250123050227.sql', 1, '参考「下单和批处理」，新建三个储蓄罐的业务_for_陆文龙。zt@2025-01-24');
-- quartz
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-01-24 10:11:12', 'huaitian.zhang', '2025-01-24 10:11:12', 0, 1734, 'schedule_3.6.0', 'RETAIL-WEEKEND-20231104-05_master', 326, 10616, 1, '储蓄罐1103_HOWBUY_QUARTZ_IT24_20250123050227.sql', 1, '参考「下单和批处理」，新建三个储蓄罐的业务_for_陆文龙。zt@2025-01-24');


-- RETAIL-T2-20231106
select max(id) as dump_max_id from db_mgt_dump_file;
-- deal
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-01-24 10:11:12', 'huaitian.zhang', '2025-01-24 10:11:12', 0, 1735, 'ftx_20240708', 'RETAIL-T2-20231106_master', 237, 10165, 1, '储蓄罐1105_DOCKER_IT24_DEAL_0_20250123050934.sql', 1, '参考「下单和批处理」，新建三个储蓄罐的业务_for_陆文龙。zt@2025-01-24');
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-01-24 10:11:12', 'huaitian.zhang', '2025-01-24 10:11:12', 0, 1736, 'ftx_20240708', 'RETAIL-T2-20231106_master', 238, 10241, 1, '储蓄罐1105_DOCKER_IT24_DEAL_1_20250123050934.sql', 1, '参考「下单和批处理」，新建三个储蓄罐的业务_for_陆文龙。zt@2025-01-24');
-- his
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-01-24 10:11:12', 'huaitian.zhang', '2025-01-24 10:11:12', 0, 1737, 'ftx_20240708', 'RETAIL-T2-20231106_master', 241, 10317, 1, '储蓄罐1105_DOCKER_IT24_HIS_20250123050934.sql', 1, '参考「下单和批处理」，新建三个储蓄罐的业务_for_陆文龙。zt@2025-01-24');
-- settle
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-01-24 10:11:12', 'huaitian.zhang', '2025-01-24 10:11:12', 0, 1738, 'ftx_20240708', 'RETAIL-T2-20231106_master', 259, 10773, 1, '储蓄罐1105_DOCKER_IT24_SETTLE_20250123050934.sql', 1, '参考「下单和批处理」，新建三个储蓄罐的业务_for_陆文龙。zt@2025-01-24');
-- report
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-01-24 10:11:12', 'huaitian.zhang', '2025-01-24 10:11:12', 0, 1739, 'ftx_20240708', 'RETAIL-T2-20231106_master', 327, 13053, 1, '储蓄罐1105_DOCKER_IT24_REPORT_20250123050934.sql', 1, '参考「下单和批处理」，新建三个储蓄罐的业务_for_陆文龙。zt@2025-01-24');
-- zhzx
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-01-24 10:11:12', 'huaitian.zhang', '2025-01-24 10:11:12', 0, 1740, 'acc_3.5.2', 'RETAIL-T2-20231106_master', 262, 6669, 1, '储蓄罐1105_DOCKER_IT24_ZHZX_0_20250123050934.sql', 1, '参考「下单和批处理」，新建三个储蓄罐的业务_for_陆文龙。zt@2025-01-24');
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-01-24 10:11:12', 'huaitian.zhang', '2025-01-24 10:11:12', 0, 1741, 'acc_3.5.2', 'RETAIL-T2-20231106_master', 263, 6745, 1, '储蓄罐1105_DOCKER_IT24_ZHZX_1_20250123050934.sql', 1, '参考「下单和批处理」，新建三个储蓄罐的业务_for_陆文龙。zt@2025-01-24');
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-01-24 10:11:12', 'huaitian.zhang', '2025-01-24 10:11:12', 0, 1742, 'acc_3.5.2', 'RETAIL-T2-20231106_master', 264, 6821, 1, '储蓄罐1105_DOCKER_IT24_ZHZX_INDEX_20250123050934.sql', 1, '参考「下单和批处理」，新建三个储蓄罐的业务_for_陆文龙。zt@2025-01-24');
-- message
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-01-24 10:11:12', 'huaitian.zhang', '2025-01-24 10:11:12', 0, 1743, 'tms-public_4.5.66', 'RETAIL-T2-20231106_master', 243, 5453, 1, '储蓄罐1105_DOCKER_IT24_MESSAGE_20250123050934.sql', 1, '参考「下单和批处理」，新建三个储蓄罐的业务_for_陆文龙。zt@2025-01-24');
-- param
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-01-24 10:11:12', 'huaitian.zhang', '2025-01-24 10:11:12', 0, 1744, 'tradenew_3.32.3', 'RETAIL-T2-20231106_master', 255, 6289, 1, '储蓄罐1105_DOCKER_IT24_PARAM_20250123050934.sql', 1, '参考「下单和批处理」，新建三个储蓄罐的业务_for_陆文龙。zt@2025-01-24');
-- trade
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-01-24 10:11:12', 'huaitian.zhang', '2025-01-24 10:11:12', 0, 1745, 'tradenew_3.32.3', 'RETAIL-T2-20231106_master', 261, 8645, 1, '储蓄罐1105_DOCKER_IT24_TRADE_20250123050934.dmp', 1, '参考「下单和批处理」，新建三个储蓄罐的业务_for_陆文龙。zt@2025-01-24');
-- pay
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-01-24 10:11:12', 'huaitian.zhang', '2025-01-24 10:11:12', 0, 1746, 'pay_20240528', 'RETAIL-T2-20231106_master', 256, 8721, 1, '储蓄罐1105_DOCKER_IT24_PAY_20250123050934.dmp', 1, '参考「下单和批处理」，新建三个储蓄罐的业务_for_陆文龙。zt@2025-01-24');
-- product
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-01-24 10:11:12', 'huaitian.zhang', '2025-01-24 10:11:12', 0, 1747, 'product-center_4.5.43', 'RETAIL-T2-20231106_master', 258, 6441, 1, '储蓄罐1105_DOCKER_IT24_PRODUCT_20250123050934.sql', 1, '参考「下单和批处理」，新建三个储蓄罐的业务_for_陆文龙。zt@2025-01-24');
-- tms-mock
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-01-24 10:11:12', 'huaitian.zhang', '2025-01-24 10:11:12', 0, 1748, 'tms-mock_4.0.82', 'RETAIL-T2-20231106_master', 260, 6517, 1, '储蓄罐1105_DOCKER_IT24_TMSMOCK_20250123050934.sql', 1, '参考「下单和批处理」，新建三个储蓄罐的业务_for_陆文龙。zt@2025-01-24');
-- quartz
insert into db_mgt_dump_file (create_user, create_time, update_user, update_time, stamp, id, pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_source, dump_file_name, dump_file_is_active, dump_file_desc)
VALUES ('huaitian.zhang', '2025-01-24 10:11:12', 'huaitian.zhang', '2025-01-24 10:11:12', 0, 1749, 'schedule_3.6.0', 'RETAIL-T2-20231106_master', 326, 10616, 1, '储蓄罐1105_HOWBUY_QUARTZ_IT24_20250123050934.sql', 1, '参考「下单和批处理」，新建三个储蓄罐的业务_for_陆文龙。zt@2025-01-24');

