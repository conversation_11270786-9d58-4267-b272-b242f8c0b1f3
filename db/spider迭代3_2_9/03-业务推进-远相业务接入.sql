-- 新建「中台交易-营销-任务」业务_for_远相。zt@2025-01-21
-- 1、新增同步业务
-- 2、确定业务
select * from biz_base_info where biz_scenario_name like '%任务' or biz_name = '中台交易-营销-任务';
-- 12405	123	TMS-ACTIVITY-TASK		中台交易-营销-任务

-- 3、基础库集（虽然已改造，但基础库集还影响定时，暂时加上）
-- 49	Howbuy-ALL	howbuy-all
select * from biz_base_db_bind where biz_code in ('Howbuy-ALL', 'TMS-ACTIVITY-TASK'); -- howbuy-all
INSERT INTO biz_base_db_bind (biz_code, biz_base_db_code, biz_base_db_bind_is_active, biz_base_db_bind_desc, create_user, create_time, update_user, update_time, stamp)
VALUES ('TMS-ACTIVITY-TASK', 'howbuy-all', 1, '绑定基础库集「howbuy-all」。zt@2025-01-21」', 'huaitian.zhang', '2025-01-21 10:11:12', 'huaitian.zhang', '2025-01-21 10:11:12', 0);

-- 4、3个迭代数据：
-- TMS-ACTIVITY-TASK
insert into biz_test_iter(biz_test_iter_id, biz_code, biz_test_iter_br, br_status, br_start_time, br_end_time, br_from, create_user, create_time, update_user, update_time, stamp, pause_test_data_recording)
VALUES ('TMS-ACTIVITY-TASK_dev', 'TMS-ACTIVITY-TASK', 'dev', 'open', '2025-01-21 10:11:12', NULL, NULL, 'huaitian.zhang', '2025-01-21 10:11:12', 'huaitian.zhang', '2025-01-21 10:11:12', 0, 0);
insert into biz_test_iter(biz_test_iter_id, biz_code, biz_test_iter_br, br_status, br_start_time, br_end_time, br_from, create_user, create_time, update_user, update_time, stamp, pause_test_data_recording)
VALUES ('TMS-ACTIVITY-TASK_master', 'TMS-ACTIVITY-TASK', 'master', 'open', '2025-01-21 10:11:12', NULL, NULL, 'huaitian.zhang', '2025-01-21 10:11:12', 'huaitian.zhang', '2025-01-21 10:11:12', 0, 0);
insert into biz_test_iter(biz_test_iter_id, biz_code, biz_test_iter_br, br_status, br_start_time, br_end_time, br_from, create_user, create_time, update_user, update_time, stamp, pause_test_data_recording)
VALUES ('TMS-ACTIVITY-TASK_longterm', 'TMS-ACTIVITY-TASK', 'longterm', 'open', '2025-01-21 10:11:12', NULL, NULL, 'huaitian.zhang', '2025-01-21 10:11:12', 'huaitian.zhang', '2025-01-21 10:11:12', 0, 0);

-- 5、绑定「dump」：
-- 查询：
select biz.biz_code,
       biz.biz_name,
       CONCAT(biz_type.biz_type_department, biz_type.biz_type_transaction, '-', biz.biz_name, '-', biz.biz_scenario_name) as biz_info_name,
       biz_app_bind.biz_app_bind_type, biz_app_bind.app_module_name,
       domain.domain_name, domain.domain_desc, domain.db_alias,
       logic_info.id as logic_id, logic_info.logic_db_name,
       suite_bind.db_bind_suite_code,
       db_info.id as db_info_id, db_info.suite_db_name, db_info.db_info_username, db_info.db_info_password,
       db_srv.db_srv_hosts, db_srv.db_srv_username, db_srv.db_srv_password, db_srv.db_srv_socket_path, db_srv.data_dump_dir,
       db_group.db_group_name,
       biz_iter.biz_test_iter_id,
       dump.id as dump_id, dump.db_info_id, dump_suite_bind.db_bind_suite_code, dump.dump_file_name
from biz_base_info biz
inner join biz_base_type biz_type on biz_type.id = biz.biz_type
left join biz_app_bind on biz_app_bind.biz_code = biz.biz_code              -- 业务绑定的应用
left join db_mgt_app_bind app_bind on app_bind.app_module_name = biz_app_bind.app_module_name       -- 应用绑定的库（总）
left join db_mgt_domain domain on domain.id = app_bind.db_domain_id                                 -- 1、库信息（主要关联）：领域->逻辑库->环境->物理库->服务器
left join db_mgt_logic_info logic_info on logic_info.db_domain_id = app_bind.db_domain_id           -- 都绑定「domain_id」（可跳过）
left join db_mgt_suite_bind suite_bind on suite_bind.db_logic_id = logic_info.id                    -- 新建「逻辑表」
left join db_mgt_info db_info on db_info.id = suite_bind.db_info_id
left join db_mgt_srv db_srv on db_srv.id = db_info.db_srv_id
left join db_mgt_group db_group on db_group.id = domain.db_group_id         -- 2、组信息
left join biz_test_iter biz_iter on biz_iter.biz_code = biz.biz_code        -- 3、业务迭代（已定死：dev longterm master）
left join db_mgt_dump_file dump on dump.biz_test_iter_id = biz_iter.biz_test_iter_id
                                       and dump.db_logic_id = logic_info.id   -- 4、业务迭代对应的dump文件（已唯一不用max）
left join db_mgt_info dump_db_info on dump_db_info.id = dump.db_info_id     -- 5、确定dump出自哪个环境
left join db_mgt_suite_bind dump_suite_bind on dump_suite_bind.db_info_id = dump_db_info.id
where 1=1
and biz.biz_code = 'TMS-ACTIVITY-TASK'
and suite_bind.db_bind_suite_code = 'it21'
and biz_iter.biz_test_iter_br in('dev', 'master')
order by biz.biz_type, biz.biz_code,
    biz_app_bind.biz_app_bind_type, biz_app_bind.app_module_name,
    domain.domain_name
;
