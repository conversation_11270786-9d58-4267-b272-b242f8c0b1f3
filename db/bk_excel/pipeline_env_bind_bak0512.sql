CREATE TABLE spider.pipeline_env_bind_bak0512 (
	id int(10) NOT NULL auto_increment,
	pipeline_id char(48) NOT NULL,
	app_name varchar(255) NOT NULL,
	env varchar(255),
	operator char(18),
	datetime datetime DEFAULT CURRENT_TIMESTAMP NOT NULL,
	PRIMARY KEY (id)
) ENGINE=InnoDB;
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (337, 'mojie_5.3.0', 'mring-itest-service', '**************', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (338, 'mojie_5.3.0', 'mring', '**************', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (339, 'mojie_5.3.1', 'mring-itest-service', '**************', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (340, 'mojie_5.3.1', 'mring', '**************', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (341, 'mojie_5.4.0', 'mring-itest-service', '**************', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (342, 'mojie_5.4.0', 'mring', '**************', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (343, 'mojie_5.4.0', 'mring-performance-service', '**************', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (344, 'mojie_5.4.1', 'mring-itest-service', '**************', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (345, 'mojie_5.4.1', 'mring', '**************', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (346, 'asset_0.0.8', 'asset-batch-center-remote', 'tms06', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (347, 'adviser_0.0.1', 'adviser-batch-center-remote', 'tms08', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (348, 'mtx_1.8.0', 'zeus-service', 'test', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (349, 'mtx_1.9.0', 'zeus-service', 'test', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (350, 'tenpay_1.2.12', 'lct-console', 'test', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (351, 'tenpay_1.2.12', 'lct-online', 'test', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (352, 'adviser_0.0.2', 'adviser-batch-center-remote', 'tms08', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (353, 'mtx_1.10.0', 'zeus-service', 'test', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (354, 'mtx_1.11.0', 'zeus-service', 'test', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (355, 'mtx_1.11.1', 'zeus-service', 'test', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (356, 'mojie_5.5.0', 'mring-itest-service', 'test', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (357, 'mojie_5.5.0', 'mring', 'test', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (358, 'mtx_1.12.0', 'zeus-service', 'test', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (359, 'asset_0.1.0', 'asset-batch-center-remote', 'tms06', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (360, 'asset_0.1.0', 'asset-center-remote', 'tms06', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (361, 'asset_0.1.1', 'asset-batch-center-remote', 'tms06', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (362, 'asset_0.1.1', 'asset-center-remote', 'tms06', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (363, 'asset_0.1.3', 'asset-batch-center-remote', 'tms06', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (364, 'asset_0.1.3', 'asset-center-remote', 'tms06', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (365, 'ftx_1.1.35.1', 'ftx-console-web', 'tms02', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (366, 'asset_0.1.7', 'asset-batch-center-remote', 'tms06', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (367, 'ftx_1.1.36', 'ftx-online-search-web', 'tms02', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (368, 'ftx_1.1.36', 'ftx-online-web', 'tms02', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (369, 'ftx_1.1.36', 'ftx-console-web', 'tms02', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (370, 'mojie_5.6.0', 'mring-itest-service', 'test', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (371, 'mojie_5.6.0', 'mring', 'test', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (372, 'mtx_1.13.0', 'zeus-service', 'test', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (373, 'tradenew_1.0.0', 'param-center-web', 'tp02', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (374, 'asset_0.1.8', 'asset-center-remote', 'tms06', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (375, 'adviser_0.0.3', 'adviser-batch-center-remote', 'tms12', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (376, 'mtx_1.14.0', 'zeus-service', 'test', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (377, 'ftx_1.1.36', 'stp-console-web', 'tms02', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (378, 'ftx_1.1.36', 'gateway-web', 'tms02', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (379, 'tenpay_1.2.14', 'lct-console', 'tms04', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (380, 'tenpay_1.2.14', 'lct-online', 'tms04', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (381, 'tenpay_1.2.15', 'lct-console', 'tms02', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (382, 'tenpay_1.2.15', 'lct-online', 'tms02', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (383, 'schedule_1.0.0', 'schedule-ec-console', 'tms18', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (384, 'schedule_1.0.0', 'schedule-ec-server', 'tms18', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (385, 'tradenew_3.0.1', 'param-center-web', 'tp02', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (386, 'otc_2.0.1', 'otc-center-search-remote', 'tp12', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (387, 'otc_2.0.1', 'otc-web', 'tp12', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (388, 'otc_2.0.1', 'otc-batch-remote', 'tp12', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (389, 'otc_2.0.1', 'otc-web-static', 'tp12', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (390, 'otc_1.0.0', 'otc-batch-mock-web', 'tp09', 'shuangqing.sun', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (391, 'otc_2.0.1', 'otc-counter', 'tp12', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (392, 'otc_2.0.1', 'fisp-pre-remote', 'tp12', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (393, 'otc_2.0.1', 'otc-console', 'tp12', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (394, 'ftx_1.2.0', 'ftx-online-search-web', 'tms02', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (395, 'ftx_1.2.0', 'ftx-online-web', 'tms02', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (396, 'ftx_1.2.0', 'ftx-console-web', 'tms02', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (397, 'ftx_1.2.0', 'ftx-counter-web', 'tms02', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (398, 'tms-cgi_0.0.1', 'cgi-gateway', 'tms14', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (399, 'otc_2.0.1', 'otc-center-remote', 'tp12', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (400, 'otc_MT1.0.0', 'otc-batch-remote', 'tp11', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (401, 'tradenew_3.0.2', 'param-center-web', 'tp02', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (402, 'tradenew_3.0.2', 'param-static', 'tp02', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (403, 'otc_MT1.0.0', 'otc-center-search-remote', 'tp11', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (404, 'otc_MT1.0.0', 'otc-center-remote', 'tp11', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (405, 'otc_MT1.0.0', 'otc-web', 'tp11', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (406, 'otc_MT1.0.0', 'otc-console', 'tp11', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (407, 'otc_MT1.0.0', 'otc-counter', 'tp11', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (408, 'otc_MT1.0.1', 'otc-batch-remote', 'tp11', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (409, 'mtx_1.15.0', 'zeus-service', 'test', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (410, 'otc_MT1.0.1', 'otc-center-search-remote', 'tp11', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (411, 'tms-cgi_1.0.0', 'cgi-gateway-test', 'tms14', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (412, 'tms-cgi_1.0.0', 'cgi-gateway-mock', 'tms14', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (413, 'AMS_1.0.2', 'howbuy-ams-server', 'ms01', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (414, 'tms-cgi_1.0.0', 'cgi-gateway', 'tms14', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (415, 'ftx_1.2.0', 'gateway-web', 'tms02', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (416, 'tradenew_3.0.4', 'param-center-web', 'tp02', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (417, 'tradenew_3.0.4', 'param-static', 'tp02', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (418, 'tenpay_1.2.18', 'lct-console', 'tms02', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (419, 'tenpay_1.2.18', 'lct-online', 'tms02', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (420, 'asset_0.1.8', 'asset-batch-center-remote', 'tms06', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (421, 'ftx_1.2.0.2', 'ftx-console-web', 'tms02', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (422, 'ftx_1.2.0.2', 'ftx-online-web', 'tms02', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (423, 'fin_2.1.2', 'fin-console-web', 'tp03', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (424, 'otc_2.1.0', 'otc-center-search-remote', 'tp12', 'shuangqing.sun', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (425, 'otc_2.1.0', 'otc-center-remote', 'tp12', 'jiangwei.ji', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (426, 'fin_2.1.3', 'fin-console-web', 'tp03', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (427, 'otc_2.1.0', 'otc-investor-web', 'tp12', 'weimin.feng', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (428, 'otc_2.1.0', 'otc-investor-static', 'tp12', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (429, 'otc_20201203', 'otc-batch-remote', 'tp12', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (430, 'AMS_1.0.3', 'howbuy-ams-server', 'FPS01', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (431, 'otc_2.1.0', 'otc-investor-pre-remote', 'tp12', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (432, 'otc_2.1.0', 'otc-batch-remote', 'tp12', 'ya.miao', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (433, 'otc_2.1.0', 'otc-counter', 'tp12', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (434, 'tenpay_1.2.18', 'tenpay', 'tms02', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (435, 'tms-cgi_1.0.1', 'trade-cgi-gateway', 'tms14', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (436, 'tms-cgi_1.0.1', 'cgi-gateway', 'tms13,tms14', 'yang.zhou', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (437, 'otc_2.1.0', 'otc-console', 'tp12', 'yanwei.shen', '2020-12-30 12:19:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (464, 'fin_2.1.5', 'fin-console-web', 'tp03', 'haoran.zhang', '2020-12-31 10:43:36.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (465, 'otc_MT1.0.2', 'otc-batch-remote', 'tp09', 'shuangqing.sun', '2020-12-31 15:56:24.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (466, 'otc_MT1.0.2', 'otc-console', 'tp09', 'shuangqing.sun', '2020-12-31 16:21:25.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (467, 'otc_MT1.0.2', 'otc-counter', 'tp09', 'shuangqing.sun', '2020-12-31 16:30:00.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (468, 'otc_MT1.0.2', 'otc-web', 'tp09', 'shuangqing.sun', '2021-01-01 16:25:06.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (469, 'otc_MT1.0.2', 'fisp-pre-remote', 'tp09', 'shuangqing.sun', '2021-01-01 16:29:38.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (470, 'otc_2.0.9', 'otc-web', 'tms17', 'shuangqing.sun', '2021-01-04 10:54:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (471, 'otc_2.0.9', 'otc-batch-remote', 'tms17', 'shuangqing.sun', '2021-01-04 10:54:18.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (472, 'fin_2.1.6', 'fin-console-web', 'tp03', 'haoran.zhang', '2021-01-04 11:15:33.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (473, 'tradenew_3.0.7', 'param-console', 'tp02', 'yanlin.guo', '2021-01-04 13:35:09.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (474, 'tradenew_3.0.7', 'param-static', 'tp02', 'yanlin.guo', '2021-01-04 13:35:36.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (475, 'tradenew_3.0.7', 'param-center-web', 'tp02', 'yanlin.guo', '2021-01-04 13:35:42.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (476, 'auth_2.0.0', 'howbuy-auth-service', 'tms18', 'bin.ji', '2021-01-04 15:27:48.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (477, 'otc_MT1.0.2', 'otc-batch-mock-web', 'tp09', 'shuangqing.sun', '2021-01-05 15:28:47.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (478, 'ftx_1.2.2', 'ftx-console-web', 'tms02,tms09', 'binbin.liu', '2021-01-05 16:40:43.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (479, 'otc_MT1.0.2', 'o32-pre-web', 'tp09', 'shuangqing.sun', '2021-01-05 18:06:29.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (480, 'otc_MT1.0.2', 'otc-center-search-remote', 'tp09', 'shuangqing.sun', '2021-01-06 10:12:31.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (481, 'otc_2.0.9', 'otc-cms', 'tms17', 'shuangqing.sun', '2021-01-06 14:36:57.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (482, 'otc_2.0.9', 'otc-console', 'tms17', 'shuangqing.sun', '2021-01-06 14:37:02.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (483, 'otc_2.0.9', 'otc-center-search-remote', 'tms17', 'shuangqing.sun', '2021-01-06 14:37:07.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (484, 'otc_2.0.9', 'otc-counter', 'tms17', 'shuangqing.sun', '2021-01-06 14:37:10.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (485, 'member-center_1.0.8', 'center-member-service', 'CC02', 'weimin.feng', '2021-01-07 16:24:26.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (486, 'schedule_0.0.1', 'schedule-ec-server', 'tms19', 'wei.liu', '2021-01-08 10:26:14.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (487, 'schedule_0.0.1', 'schedule-ec-console', 'tms19', 'wei.liu', '2021-01-08 11:10:38.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (488, 'AMS_1.0.5', 'howbuy-ams-server', 'FPS01', 'weimin.feng', '2021-01-08 18:36:50.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (489, 'asset_0.1.9', 'asset-batch-center-remote', 'tms03', 'yang.zhou', '2021-01-11 13:40:58.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (490, 'asset_0.1.9', 'asset-center-remote', 'tms03', 'yang.zhou', '2021-01-11 13:41:05.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (491, 'tradenew_3.0.9', 'param-center-web', 'tp02', 'jiong.peng', '2021-01-11 14:31:53.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (492, 'fin_2.1.6', 'fin-online-web', 'tp03', 'haoran.zhang', '2021-01-11 15:01:48.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (493, 'AMS_1.0.4', 'howbuy-ams-server', 'FPS01,FPS02', 'weimin.feng', '2021-01-12 16:07:33.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (494, 'otc_MT1.0.2', 'otc-center-remote', 'tp09', 'shuangqing.sun', '2021-01-14 11:34:56.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (495, 'otc_1.0.0.3', 'otc-batch-remote', 'tp09,tp11', 'shuangqing.sun', '2021-01-14 14:46:10.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (496, 'otc_1.0.0.3', 'otc-center-search-remote', 'tp09', 'shuangqing.sun', '2021-01-14 14:51:30.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (497, 'otc_1.0.0.3', 'otc-center-remote', 'tp09,tp11', 'shuangqing.sun', '2021-01-14 14:51:42.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (498, 'asset_0.2.1', 'asset-batch-center-remote', 'tms06', 'gang.li01', '2021-01-14 15:16:17.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (499, 'asset_0.2.1', 'asset-center-remote', 'tms06', 'gang.li01', '2021-01-14 15:16:23.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (500, 'otc_2.1.1', 'otc-console', 'tms17,tp09', 'shuangqing.sun', '2021-01-15 13:59:09.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (501, 'asset_0.2.2', 'asset-batch-center-remote', 'tms06', 'gang.li01', '2021-01-15 15:10:52.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (502, 'asset_0.2.2', 'asset-center-remote', 'tms06', 'gang.li01', '2021-01-15 15:10:59.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (503, 'AMS_1.1.0', 'howbuy-ams-server', 'FPS02,FPS01', 'duobao.zhang', '2021-01-18 14:10:20.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (504, 'CRM_0.0.1', 'crm-sys-webapp', 'CRM02', 'wei.liu', '2021-01-18 17:16:09.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (505, 'CRM_0.0.1', 'crm-hb-webapp', 'CRM02', 'wei.liu', '2021-01-19 18:13:10.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (506, 'CRM_0.0.1', 'crm-mobile-webapp', 'CRM02', 'wei.liu', '2021-01-19 19:36:48.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (507, 'CRM_0.0.1', 'hbdoc-webapp', 'CRM02', 'wei.liu', '2021-01-19 19:56:49.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (508, 'CRM_0.0.1', 'cs-webapp', 'CRM02', 'wei.liu', '2021-01-20 14:25:53.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (509, 'AMS_1.0.6', 'howbuy-ams-server', 'FPS01', 'xiang.zhou', '2021-01-20 16:30:46.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (510, 'weixin_1.0.0', 'howbuy-trade-weixin', 'CC01', 'xing.zhang', '2021-01-21 15:37:53.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (511, 'CRM_1.0.0', 'crm-core-server', '', 'weimin.feng', '2021-01-22 16:26:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (512, 'ftx_1.2.2', 'ftx-online-search-web', 'tms02,tms09', 'binbin.liu', '2021-01-25 09:48:29.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (513, 'ftx_1.2.2', 'ftx-online-web', 'tms02,tms09', 'binbin.liu', '2021-01-25 09:48:48.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (514, 'fin_2.1.7', 'fin-console-web', 'tp03', 'haoran.zhang', '2021-01-25 10:34:32.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (515, 'fin_2.1.7', 'fin-online-web', 'tp03', 'haoran.zhang', '2021-01-25 10:34:39.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (516, 'otc_1.0.0-1', 'otc-batch-mock-web', 'tp09', 'jiangwei.ji', '2021-01-25 14:13:10.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (517, 'otc_2.1.1', 'otc-batch-remote', 'tp12', 'shuangqing.sun', '2021-01-25 16:54:11.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (518, 'otc_2.1.1', 'otc-cms', 'tms17', 'shuangqing.sun', '2021-01-25 16:54:15.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (519, 'otc_2.1.1', 'otc-counter', 'tms17,tp09', 'shuangqing.sun', '2021-01-25 16:54:19.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (520, 'otc_2.1.1', 'otc-web', 'tms17', 'shuangqing.sun', '2021-01-25 16:54:23.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (521, 'ftx_1.2.2', 'ftx-counter-web', 'tms02', 'binbin.liu', '2021-01-26 16:01:22.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (522, 'ftx_1.2.2', 'stp-console-web', 'tms02', 'binbin.liu', '2021-01-26 16:01:47.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (523, 'ftx_1.2.2', 'gateway-web', 'tms02', 'binbin.liu', '2021-01-26 16:01:54.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (524, 'otc_2.1.1', 'otc-center-search-remote', 'tms17,tp09,tp12', 'shuangqing.sun', '2021-01-27 13:46:38.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (525, 'otc_2.1.1', 'otc-center-remote', 'tms17,tp12', 'shuangqing.sun', '2021-01-27 14:06:53.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (526, 'message-center_3.0.0', 'message-manage-server', 'CC02', 'xing.zhang', '2021-01-27 15:04:36.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (527, 'message-center_3.0.0', 'message-xbox-service', 'CC02', 'xing.zhang', '2021-01-27 15:04:49.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (528, 'message-center_3.0.0', 'message-server', 'CC02', 'xing.zhang', '2021-01-27 15:23:39.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (529, 'message-center_3.0.0', 'message-monitor', 'CC02', 'xing.zhang', '2021-01-27 15:32:44.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (530, 'message-center_3.0.0', 'message-consumer', 'CC02', 'xing.zhang', '2021-01-27 15:33:30.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (531, 'mtx_1.15.2', 'zeus-service', 'test', 'wancheng.zhao', '2021-01-27 19:31:04.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (532, 'pa-management_0.0.8', 'pa-management-sprint-service', '', 'weimin.feng', '2021-01-27 20:10:14.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (533, 'message-center_1.0.0', 'message-xbox-service', 'CC02', 'wei.liu', '2021-01-28 13:45:31.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (534, 'member-center_1.0.0', 'center-member-service', 'CC01', 'yang.zhou', '2021-01-28 13:46:06.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (535, 'member-center_1.0.0', 'center-api-boot', 'CC01', 'yang.zhou', '2021-01-28 14:23:56.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (536, 'center-sale_1.0.0', 'center-sale-batch', 'CC02', 'wei.liu', '2021-01-29 13:24:43.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (537, 'center-sale_1.0.0', 'center-sale-service', 'CC02', 'wei.liu', '2021-01-29 13:24:52.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (538, 'member-center_1.0.0', 'center-activity-boot', 'CC02', 'yanwei.shen', '2021-01-29 14:10:52.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (539, 'member-center_1.0.0', 'center-platform-boot', 'CC02', 'yanwei.shen', '2021-01-29 14:11:06.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (540, 'center-asset_1.0.0', 'center-asset-batch', 'CC02', 'wei.liu', '2021-01-29 14:35:38.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (541, 'center-asset_1.0.0', 'center-asset-service', 'CC02', 'wei.liu', '2021-01-29 14:35:43.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (542, 'act_1.0.0', 'howbuy-act-remote', 'CC02', 'wei.liu', '2021-01-29 14:50:30.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (543, 'act_0.0.5', 'howbuy-act-remote', 'CC02', 'wei.liu', '2021-02-01 19:15:48.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (544, 'AMS_1.1.1', 'howbuy-ams-server', 'FPS02,FPS01', 'duobao.zhang', '2021-02-02 09:45:48.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (545, 'otc_2.1.2', 'otc-batch-remote', 'tp12,tp09', 'ya.miao', '2021-02-03 14:17:27.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (546, 'auth_2.0.1', 'howbuy-auth-service', 'tms18', 'bin.ji', '2021-02-03 14:19:27.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (547, 'ftx_1.2.1.4', 'ftx-online-search-web', 'tms11', 'binbin.liu', '2021-02-03 15:29:11.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (548, 'otc_1.0.0', 'otc-batch-remote', 'tp09', 'linnan.zhang', '2021-02-04 16:18:41.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (549, 'fin_2.1.9', 'fin-console-web', 'tms11', 'bin.ji', '2021-02-04 18:23:56.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (550, 'otc_2.1.2', 'otc-counter', 'tp12,tp09', 'linnan.zhang', '2021-02-07 09:42:25.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (551, 'otc_2.1.2', 'otc-web-static', 'tp09', 'shuangqing.sun', '2021-02-07 09:42:28.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (552, 'otc_2.1.2', 'otc-investor-static', 'tp09', 'shuangqing.sun', '2021-02-07 09:42:35.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (553, 'otc_2.1.2', 'otc-center-remote', 'tp09', 'shuangqing.sun', '2021-02-07 11:14:24.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (554, 'otc_2.1.3', 'otc-cms', 'tp12', 'linnan.zhang', '2021-02-07 16:25:11.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (555, 'otc_2.1.3', 'otc-web', 'tp12', 'linnan.zhang', '2021-02-07 16:26:23.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (556, 'otc_2.1.3', 'otc-center-search-remote', 'tp12', 'linnan.zhang', '2021-02-07 16:27:18.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (557, 'otc_2.1.3', 'otc-center-remote', 'tp12', 'linnan.zhang', '2021-02-07 16:28:16.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (558, 'ftx_1.2.1.4', 'ftx-console-web', 'tms11', 'binbin.liu', '2021-02-08 09:33:58.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (559, 'otc_2.1.2', 'fisp-pre-remote', 'tp09', 'shuangqing.sun', '2021-02-09 10:08:41.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (560, 'otc_2.1.2', 'otc-center-search-remote', 'tp09', 'linnan.zhang', '2021-02-22 11:13:30.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (561, 'tms-cgi_0.0.4', 'coop-cgi-remote', 'tms14,tms13', 'mingming.xu', '2021-02-22 17:31:55.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (562, 'AMS_1.3.0', 'howbuy-ams-server', 'FPS01,FPS02', 'xiang.zhou', '2021-02-23 13:05:44.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (563, 'ftx_1.2.3', 'ftx-online-search-web', 'tms02', 'binbin.liu', '2021-02-24 12:21:43.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (564, 'fin_2.1.8', 'fin-console-web', 'tp03', 'bin.ji', '2021-02-24 15:02:47.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (565, 'CRM_0.0.6', 'howbuy-crm-sync', 'CRM02', 'wei.liu', '2021-02-24 18:15:23.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (566, 'CRM_0.0.7', 'crm-sys-webapp', 'CRM02', 'wei.liu', '2021-02-24 19:00:01.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (567, 'CRM_0.0.7', 'cs-task-server', 'CRM02', 'wei.liu', '2021-02-24 19:00:33.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (568, 'CRM_0.0.7', 'crm-hb-webapp', 'CRM02', 'wei.liu', '2021-02-24 19:05:48.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (569, 'CRM_0.0.7', 'howbuy-crm-sync', 'CRM02', 'wei.liu', '2021-02-24 19:08:31.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (570, 'center-asset_1.0.1', 'center-asset-service', 'CC02', 'gang.li01', '2021-02-27 20:53:28.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (571, 'otc_2.1.4', 'otc-center-search-remote', 'tp12,tms17', 'shuangqing.sun', '2021-03-01 14:22:44.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (572, 'AMS_1.2.0', 'howbuy-ams-server', 'FPS01', 'kechen.qian', '2021-03-02 10:22:58.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (573, 'ftx_1.2.3', 'ftx-online-web', 'tms02', 'binbin.liu', '2021-03-02 13:49:29.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (574, 'ftx_1.2.3', 'ftx-console-web', 'tms02', 'weimin.feng', '2021-03-02 13:49:36.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (575, 'ftx_1.2.3', 'stp-console-web', 'tms02', 'binbin.liu', '2021-03-02 13:49:42.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (576, 'ftx_1.2.3', 'gateway-web', 'tms02', 'binbin.liu', '2021-03-02 13:49:48.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (577, 'ftx_1.2.3', 'ftx-counter-web', 'tms02', 'binbin.liu', '2021-03-02 13:49:53.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (578, 'otc_2.1.2', 'otc-web', 'tp12', 'jiangwei.ji', '2021-03-03 11:04:19.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (579, 'otc_2.1.4', 'otc-cms', 'tp12,tms17', 'shuangqing.sun', '2021-03-03 14:54:15.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (580, 'otc_2.1.4', 'otc-console', 'tp12,tms17', 'shuangqing.sun', '2021-03-03 14:55:09.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (581, 'otc_2.1.4', 'otc-batch-remote', 'tp12,tms17', 'shuangqing.sun', '2021-03-03 15:55:26.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (582, 'otc_2.1.4', 'otc-counter', 'tp12,tms17', 'shuangqing.sun', '2021-03-04 10:38:55.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (583, 'weixin_1.0.1', 'howbuy-trade-weixin', 'CC01', 'yang.zhou', '2021-03-05 11:32:06.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (584, 'otc_2.1.4', 'otc-center-remote', 'tp12', 'dong.zhao', '2021-03-05 18:06:31.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (585, 'CRM_1.0.2', 'crm-nt-server', 'CRM01', 'ting.liang', '2021-03-08 10:46:03.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (586, 'CRM_1.0.2', 'hbdoc-webapp', 'CRM01', 'wei.liu', '2021-03-08 16:13:01.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (587, 'CRM_1.0.2', 'crm-hb-webapp', 'CRM01', 'ting.liang', '2021-03-08 16:57:31.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (588, 'CRM_1.0.2', 'crm-td-server', 'CRM01', 'ting.liang', '2021-03-08 18:05:24.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (589, 'CRM_1.0.2', 'crm-sys-webapp', 'CRM01', 'ting.liang', '2021-03-08 18:29:49.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (590, 'CRM_1.0.2', 'cs-webapp', 'CRM01', 'ting.liang', '2021-03-08 18:31:57.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (591, 'CRM_1.0.2', 'crm-core-server', 'CRM01', 'ting.liang', '2021-03-08 18:32:06.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (592, 'CRM_1.0.2', 'crm-mobile-webapp', 'CRM01', 'ting.liang', '2021-03-08 18:32:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (593, 'CRM_1.0.2', 'howbuy-crm-sync', 'CRM01', 'ting.liang', '2021-03-08 18:32:19.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (594, 'tms-cgi_0.1.0', 'coop-cgi-remote', 'tms13', 'mingming.xu', '2021-03-09 18:59:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (595, 'tms-cgi_1.1.0', 'cgi-gateway', 'tms14,tms13', 'mingming.xu', '2021-03-10 16:21:48.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (596, 'tms-cgi_1.1.0', 'trade-cgi-gateway', 'tms14', 'mingming.xu', '2021-03-10 18:19:18.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (597, 'CRM_1.0.3', 'cs-webapp', 'CRM01', 'ting.liang', '2021-03-11 13:37:37.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (598, 'order-plan_1.0.0', 'order-plan-center-remote', 'tms14', 'mingming.xu', '2021-03-11 17:51:23.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (599, 'fin_2.2.0', 'fin-online-web', 'tp03', 'bin.ji', '2021-03-12 13:16:47.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (600, 'fin_2.2.0', 'fin-console-web', 'tp03', 'bin.ji', '2021-03-12 13:16:52.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (601, 'tenpay_1.2.19', 'lct-console', 'tms02', 'binbin.liu', '2021-03-15 10:27:03.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (602, 'tenpay_1.2.19', 'lct-online', 'tms02', 'binbin.liu', '2021-03-15 10:27:10.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (603, 'AMS_1.4.0', 'howbuy-ams-server', 'FPS01', 'duobao.zhang', '2021-03-15 11:05:01.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (604, 'CRM_1.0.2.1', 'crm-hb-webapp', 'CRM01', 'yu.zhang', '2021-03-15 13:10:04.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (605, 'mtx_1.16.0', 'zeus-service', 'test', 'wei.liu', '2021-03-15 14:44:44.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (606, 'CRM_1.0.3', 'crm-nt-server', 'CRM01', 'ting.liang', '2021-03-16 09:12:23.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (607, 'CRM_1.0.3', 'crm-hb-webapp', 'CRM01', 'ting.liang', '2021-03-16 09:23:50.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (608, 'CRM_1.0.2.3', 'hbdoc-webapp', 'CRM01', 'ting.liang', '2021-03-17 14:32:35.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (609, 'FPC_0.0.1', 'fpc-manage-console', 'FPC01', 'wei.liu', '2021-03-17 15:08:20.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (610, 'FPC_0.0.3', 'fpc-manage-console', 'FPC01', 'wei.liu', '2021-03-17 15:32:33.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (611, 'FPC_0.0.3', 'fpc-manage-gateway', 'FPC01', 'wei.liu', '2021-03-17 15:36:15.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (612, 'FPC_0.0.3', 'fpc-manage-admin', 'FPC01', 'wei.liu', '2021-03-17 15:36:39.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (613, 'FPC_0.0.3', 'fpc-manage-data', 'FPC01', 'wei.liu', '2021-03-17 15:37:11.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (614, 'asset_3.1.0', 'asset-center-remote', 'tms03', 'gang.li01', '2021-03-17 17:27:09.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (615, 'asset_3.1.0', 'asset-batch-center-remote', 'tms03', 'gang.li01', '2021-03-17 17:38:34.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (616, 'AMS_1.4.0-3', 'howbuy-ams-server', 'FPS01', 'duobao.zhang', '2021-03-18 10:12:15.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (617, 'CRM_1.0.2.3', 'crm-td-server', 'CRM01', 'ting.liang', '2021-03-18 10:39:02.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (618, 'CRM_1.0.2.2', 'crm-nt-server', 'CRM01', 'ting.liang', '2021-03-18 11:18:11.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (619, 'CRM_1.0.3', 'howbuy-crm-sync', 'CRM01', 'ting.liang', '2021-03-18 16:42:31.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (620, 'tradenew_4.0.0', 'param-data-sync', 'tp02', 'yanlin.guo', '2021-03-22 16:38:52.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (621, 'otc_2.1.5', 'otc-batch-remote', 'tp09,tp12', 'shuangqing.sun', '2021-03-23 11:01:50.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (622, 'FPC_0.0.3-web', 'fpc-manage-web', 'FPC01', 'hongliang.zhang', '2021-03-23 15:11:11.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (623, 'CRM_1.0.3', 'crm-td-server', 'CRM01', 'ting.liang', '2021-03-23 18:45:22.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (624, 'CRM_1.0.2.5', 'crm-nt-server', 'CRM01', 'yu.zhang', '2021-03-24 11:36:23.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (625, 'CRM_1.0.3', 'crm-sys-webapp', 'CRM01', 'ting.liang', '2021-03-24 14:00:30.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (626, 'ftx_1.2.3.1', 'ftx-online-web', 'tms12', 'yuanxiang.li', '2021-03-24 14:15:15.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (627, 'otc_2.1.5', 'otc-counter', 'tp12', 'jiangwei.ji', '2021-03-24 16:39:01.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (628, 'AMS_1.5.1', 'howbuy-ams-server', 'FPS02', 'xiang.zhou', '2021-03-25 10:20:29.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (629, 'CRM_1.0.4', 'crm-hb-webapp', 'CRM01', 'ting.liang', '2021-03-26 14:37:09.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (630, 'CRM_1.0.4', 'cs-webapp', 'CRM01', 'ting.liang', '2021-03-26 16:02:24.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (631, 'fin_2.2.1', 'fin-console-web', 'tp07', 'bin.ji', '2021-03-29 10:57:56.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (632, 'otc_2.2.0', 'otc-batch-remote', 'tp09,tms17', 'shuangqing.sun', '2021-03-29 13:44:04.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (633, 'otc_2.2.0', 'otc-center-remote', 'tp09,tms17', 'shuangqing.sun', '2021-03-29 13:44:50.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (634, 'otc_2.2.0', 'otc-counter', 'tp09,tms17', 'shuangqing.sun', '2021-03-29 13:44:56.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (635, 'otc_2.2.0', 'otc-web', 'tp09,tms17', 'shuangqing.sun', '2021-03-29 13:45:01.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (636, 'CRM_1.0.4.1', 'crm-hb-webapp', 'CRM01', 'ting.liang', '2021-03-30 11:22:02.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (637, 'otc_2.2.0', 'otc-center-search-remote', 'tp09,tms17', 'shuangqing.sun', '2021-03-30 15:48:57.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (638, 'schedule_1.0.0', 'howbuy-scheduler-server', 'tms19', 'weimin.feng', '2021-03-31 10:00:30.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (639, 'schedule_1.0.0', 'howbuy-scheduler-client', 'tms19', 'weimin.feng', '2021-03-31 10:00:44.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (640, 'fin_2.1.8', 'fin-online-web', 'tp03', 'bin.ji', '2021-04-01 11:21:33.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (641, 'asset_0.3.1', 'asset-batch-center-remote', 'tms03,tms19', 'wei.liu', '2021-04-01 14:13:32.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (642, 'order-plan_0.3.0', 'order-plan-center-remote', 'tms11', 'yanming.gu', '2021-04-01 14:45:22.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (643, 'center-asset_1.0.2', 'center-asset-batch', 'CC01', 'yang.zhou', '2021-04-01 16:12:46.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (644, 'CRM_1.0.4', 'crm-nt-server', 'CRM01', 'ting.liang', '2021-04-02 09:50:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (645, 'AMS_web-1.6.0', 'howbuy-ams-web', 'FPS01', 'duobao.zhang', '2021-04-02 13:57:45.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (646, 'CRM_1.0.4.2', 'cs-webapp', 'CRM01', 'ting.liang', '2021-04-02 14:40:59.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (647, 'AMS_1.6.0', 'howbuy-ams-server', 'FPS01,FPS02', 'duobao.zhang', '2021-04-02 17:16:11.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (648, 'mtx_1.17.0', 'zeus-service', 'test', 'weimin.feng', '2021-04-02 18:02:47.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (649, 'CRM_1.0.4', 'crm-core-server', 'CRM01', 'ting.liang', '2021-04-06 17:33:35.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (650, 'CRM_1.0.4.3', 'crm-nt-server', 'CRM01', 'ting.liang', '2021-04-07 16:41:14.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (651, 'CRM_1.0.4', 'crm-sys-webapp', 'CRM01', 'ting.liang', '2021-04-07 18:42:01.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (652, 'otc_2.2.0', 'otc-console', 'tms17,tp09', 'jiangwei.ji', '2021-04-08 10:53:14.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (653, 'CRM_1.0.5.1', 'crm-hb-webapp', 'CRM01', 'ting.liang', '2021-04-12 13:20:52.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (654, 'CRM_1.0.5', 'crm-hb-webapp', 'CRM01', 'ting.liang', '2021-04-12 13:47:44.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (655, 'CRM_1.0.5', 'cs-webapp', 'CRM01', 'ting.liang', '2021-04-12 13:58:18.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (656, 'ftx_1.2.3.3', 'ftx-online-web', 'tms02', 'binbin.liu', '2021-04-13 09:37:51.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (657, 'ftx_1.2.3.3', 'ftx-console-web', 'tms02', 'binbin.liu', '2021-04-13 09:37:57.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (658, 'CRM_1.0.5.2', 'crm-hb-webapp', 'CRM01', 'yu.zhang', '2021-04-13 10:56:04.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (659, 'asset_3.0.0', 'asset-batch-center-remote', 'tms03', 'gang.li01', '2021-04-13 16:08:30.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (660, 'asset_3.0.0', 'asset-center-remote', 'tms03', 'gang.li01', '2021-04-13 16:08:43.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (661, 'otc_2.2.0', 'otc-investor-web', 'tms17,tp12', 'linnan.zhang', '2021-04-13 19:03:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (662, 'tms-cgi_1.2.0', 'cgi-gateway', 'tms13', 'chuanguang.tang', '2021-04-13 19:12:17.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (663, 'tms-cgi_0.3.0', 'coop-cgi-remote', 'tms13', 'chuanguang.tang', '2021-04-13 19:39:40.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (664, 'ftx_1.2.6', 'ftx-console-web', 'tms03', 'gang.li01', '2021-04-14 17:20:02.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (665, 'CRM_1.0.5.3', 'crm-hb-webapp', 'CRM01', 'ting.liang', '2021-04-15 14:44:47.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (666, 'CRM_1.0.5', 'crm-sys-webapp', 'CRM01', 'ting.liang', '2021-04-15 15:59:12.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (667, 'otc_2.2.1', 'otc-batch-remote', 'tp12,tms17', 'linnan.zhang', '2021-04-15 19:19:03.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (668, 'otc_2.2.1', 'otc-counter', 'tms02,tms17', 'linnan.zhang', '2021-04-15 19:19:15.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (669, 'AMS_1.6.2', 'howbuy-ams-server', 'FPS01', 'kechen.qian', '2021-04-16 10:04:02.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (670, 'CRM_1.0.5.4', 'crm-hb-webapp', 'CRM01', 'ting.liang', '2021-04-16 13:54:17.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (671, 'CRM_1.0.5', 'crm-core-server', 'CRM01', 'ting.liang', '2021-04-16 15:14:27.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (672, 'fin_2.2.2', 'fin-console-web', 'tp03', 'bin.ji', '2021-04-19 17:10:02.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (673, 'fin_2.2.2', 'fin-online-web', 'tp03', 'bin.ji', '2021-04-19 17:10:10.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (674, 'order-plan_1.2.0', 'order-plan-center-remote', 'tms07', 'yang.zhou', '2021-04-20 10:33:11.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (675, 'CRM_1.0.5', 'crm-nt-server', 'CRM01', 'ting.liang', '2021-04-20 13:54:09.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (676, 'AMS_1.6.4', 'howbuy-ams-server', 'FPS02', 'xiang.zhou', '2021-04-20 15:40:42.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (677, 'ftx_1.2.3.4', 'ftx-online-search-web', 'tms07', 'binbin.liu', '2021-04-20 16:45:05.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (678, 'otc_2.2.0', 'otc-investor-pre-remote', 'tp12,tms17', 'jiangwei.ji', '2021-04-20 17:05:37.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (679, 'CRM_1.0.5.5', 'crm-mobile-webapp', 'CRM01', 'ting.liang', '2021-04-20 18:21:14.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (680, 'CRM_1.0.5', 'crm-mobile-webapp', 'CRM01', 'ting.liang', '2021-04-21 10:23:56.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (681, 'CRM_1.0.5', 'hbdoc-webapp', 'CRM01', 'ting.liang', '2021-04-21 11:39:03.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (682, 'otc_2.2.1', 'otc-center-search-remote', 'tp12,tms17', 'linnan.zhang', '2021-04-21 16:33:42.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (683, 'ftx_1.2.5', 'ftx-online-search-web', 'tms02', 'binbin.liu', '2021-04-21 18:35:18.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (684, 'ftx_1.2.5', 'ftx-online-web', 'tms02', 'binbin.liu', '2021-04-21 18:35:26.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (685, 'ftx_1.2.5', 'ftx-console-web', 'tms02', 'binbin.liu', '2021-04-21 18:35:30.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (686, 'ftx_1.2.5', 'ftx-counter-web', 'tms02', 'binbin.liu', '2021-04-21 18:35:34.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (687, 'ftx_1.2.5', 'stp-console-web', 'tms02', 'binbin.liu', '2021-04-21 18:35:38.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (688, 'ftx_1.2.5', 'gateway-web', 'tms02', 'binbin.liu', '2021-04-21 18:35:42.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (689, 'mtx_1.18.0', 'zeus-service', 'test', 'weimin.feng', '2021-04-23 15:06:25.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (690, 'CRM_1.0.6', 'crm-hb-webapp', 'CRM01', 'ting.liang', '2021-04-25 13:27:02.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (691, 'AMS_1.6.6', 'howbuy-ams-server', 'FPS01,FPS02', 'duobao.zhang', '2021-04-25 15:59:39.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (692, 'CRM_1.0.6', 'crm-nt-server', 'CRM01', 'yu.zhang', '2021-04-27 09:56:18.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (693, 'mojie_5.7.0', 'mring-itest-service', 'test', 'weimin.feng', '2021-04-27 10:48:38.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (694, 'FPC_0.1.0', 'fpc-manage-web', 'FPC01', 'yongchang.wang', '2021-04-27 19:38:29.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (695, 'FPC_0.1.0', 'fpc-manage-console', 'FPC01', 'yongchang.wang', '2021-04-27 19:38:34.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (696, 'FPC_0.1.0', 'fpc-manage-gateway', 'FPC01', 'yongchang.wang', '2021-04-27 19:38:41.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (697, 'FPC_0.1.0', 'fpc-manage-report', 'FPC01', 'yongchang.wang', '2021-04-27 19:38:46.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (698, 'FPC_0.1.0', 'fpc-manage-admin', 'FPC01', 'yongchang.wang', '2021-04-27 19:38:51.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (699, 'FPC_0.1.0', 'fpc-manage-data', 'FPC01', 'yongchang.wang', '2021-04-27 19:38:56.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (700, 'CRM_1.0.6', 'cs-webapp', 'CRM01', 'ting.liang', '2021-04-29 09:12:59.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (701, 'CRM_1.0.6', 'hb-wechat-server', 'CRM01', 'ting.liang', '2021-04-29 09:13:06.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (702, 'asset_3.2.3', 'asset-batch-center-remote', 'tms06', 'yang.zhou', '2021-04-29 10:23:08.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (703, 'asset_3.2.3', 'asset-center-remote', 'tms06', 'yang.zhou', '2021-04-29 10:23:23.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (704, 'otc_2.2.1', 'otc-center-remote', 'tp12,tms17', 'linnan.zhang', '2021-04-29 14:12:17.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (705, 'CRM_1.0.6', 'crm-sys-webapp', 'CRM01', 'ting.liang', '2021-04-29 16:25:03.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (706, 'CRM_1.0.6', 'howbuy-crm-sync', 'CRM01', 'ting.liang', '2021-04-29 17:43:59.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (707, 'tms-cgi_1.3.0', 'cgi-gateway', 'tms14', 'mingming.xu', '2021-04-30 13:41:36.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (708, 'FPC_0.1.1', 'fpc-manage-console', 'FPC01', 'yongchang.wang', '2021-04-30 16:16:42.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (709, 'FPC_0.1.1', 'fpc-manage-gateway', 'FPC01', 'yongchang.wang', '2021-04-30 16:16:48.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (710, 'FPC_0.1.1', 'fpc-manage-report', 'FPC01', 'yongchang.wang', '2021-04-30 16:16:54.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (711, 'FPC_0.1.1', 'fpc-manage-admin', 'FPC01', 'yongchang.wang', '2021-04-30 16:16:59.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (712, 'FPC_0.1.1', 'fpc-manage-data', 'FPC01', 'yongchang.wang', '2021-04-30 16:17:03.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (713, 'mtx_1.19.0', 'zeus-service', 'test', 'wei.liu', '2021-05-07 10:21:32.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (714, 'otc_20210507', 'otc-batch-remote', 'tp12', 'jiangwei.ji', '2021-05-07 15:57:57.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (715, 'CRM_1.0.6', 'crm-td-server', 'CRM01', 'ting.liang', '2021-05-08 09:27:13.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (716, 'CRM_1.0.6', 'hbdoc-webapp', 'CRM01', 'ting.liang', '2021-05-08 09:51:29.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (717, 'mojie_5.8.0', 'mring-itest-service', 'test', 'yixiang.zhang', '2021-05-08 10:20:39.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (718, 'otc_2.2.1', 'otc-web', 'tp12,tms17', 'linnan.zhang', '2021-05-10 10:04:16.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (719, 'otc_2.2.1', 'otc-console', 'tp12,tms17', 'linnan.zhang', '2021-05-10 15:26:16.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (720, 'mtx_1.19.1', 'devops-view', 'test', 'yanwei.shen', '2021-05-10 20:59:36.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (721, 'center-asset_1.0.4', 'center-asset-batch', 'CC01', 'gang.li01', '2021-05-12 11:12:04.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (722, 'member-center_1.1.1', 'center-member-service', 'CC01', 'gang.li01', '2021-05-12 11:18:06.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (723, 'center-asset_1.0.4', 'center-asset-service', 'CC01', 'gang.li01', '2021-05-12 11:30:54.0');
INSERT INTO spider.pipeline_env_bind_bak0512(id, pipeline_id, app_name, env, operator, datetime) VALUES (724, 'otc_2.2.3', 'otc-counter', 'tms17', 'shuangqing.sun', '2021-05-12 13:41:47.0');
