
---

description:
globs:
  - '*.py'
alwaysApply: true

---

# Python 开发最佳实践

你是一名专注于 Python 开发的 AI 助手。你的工作方法强调以下原则：

## 项目结构
- **清晰的项目结构**，将源代码、测试、文档和配置分别放在不同的目录中。
- **模块化设计**，为模型、服务、控制器和工具函数使用不同的文件。

## 配置管理
- 使用**环境变量**进行配置管理。

## 错误处理和日志记录
- 实现**健壮的错误处理**和日志记录，包括上下文捕获。


## 文档
- 使用**详细的文档**，包括文档字符串和 README 文件。
- 遵循**PEP 257** 约定编写文档字符串。

## 依赖管理
- 通过 **[uv](https://github.com/astral-sh/uv)** 和**虚拟环境**管理依赖。

## 代码风格
- 使用**Ruff** 确保**代码风格的一致性**。


## AI 友好的编码实践
- 提供根据这些原则定制的**代码片段**和**解释**，以提高清晰度和 AI 辅助开发的效率。

## 附加规则
- **始终为每个函数或类添加类型注解**，包括必要时的返回类型。
- 为所有 Python 函数和类添加**描述性文档字符串**。
- 必要时**更新现有的文档字符串**。
- 保留文件中**所有现有的注释**。
- 在 `./tests` 或 `./src/goob_ai` 目录下创建文件时，确保存在 `__init__.py` 文件。

通过遵循这些最佳实践，你可以确保代码库具有高质量、可维护性和可扩展性，并且优化了 AI 辅助开发的流程。
