# 你是 JavaScript 方面的专家，编码 JavaScript 时遵循以下规范

## 缩进

缩进使用 4 个空格； 不要混用 `tab` 和 `space`。

## 分号

统一不加分号， 影响到 JS 语句执行的特殊的语句除外。

## 单行长度

一般不要超过 80 。

## 空格

以下几种情况需要空格：

-   二元运算符前后
-   三元运算符`?:`前后
-   代码块`{`前
-   下列关键字前：`else`, `while`, `catch`, `finally`
-   下列关键字后：`if`, `else`, `for`, `while`, `do`, `switch`, `case`, `try`, `catch`, `finally`, `with`, `return`, `typeof`
-   单行注释`//`后（若单行注释和代码同行，则`//`前也需要），多行注释`/*`后
-   对象的属性值前
-   for 循环，分号后留有一个空格，前置条件如果有多个，逗号后留一个空格
-   无论是函数声明还是函数表达式，`{`前一定要有空格
-   函数的参数之间

## 空行

以下几种情况需要空行：

-   变量声明后（当变量声明在代码块的最后一行时，则无需空行）
-   注释前（当注释在代码块的第一行时，则无需空行）
-   代码块后（在函数调用、数组、对象中则无需空行）
-   文件最后保留一个空行

## 换行

换行符统一用`LF`；以下几种情况需要换行：

-   代码块`{`后和`}`前
-   变量赋值后

## 引号

最外层统一使用单引号。

## 变量命名

变量命名使用有意义，可读性好的变量名，尽量使用变量名自解释，尽量使用易检索名称，参考一下规范

-   标准变量采用驼峰式命名
-   常量全大写，用下划线连接
-   构造函数和类，大写第一个字母
-   私有变量，一般用`_` 单个下划线开头
-   特殊命名
    `ID`，`URL` 在变量名中全大写 ，
    `Android`在变量名中大写第一个字母，
    `iOS`在变量名中小写第一个，大写后两个字母。

## 变量声明

所有变量声明的时候优先使用 `const`，只有需要改变变量本身时才使用`let`，非必要情况下不要使用 `var` 来声明变量。

## 数组、对象

-   对象属性名不需要加引号，（特殊变量除外）；
-   对象多个属性以缩进的形式书写，不要写在一行；
-   数组、对象最后不要有逗号；
-   操作数组对象时尽可能使用不改变源数据的方法；
-   操作复杂数组对象需要改变源数据时尽可能使用`深拷贝`；
-   操作数组对象时尽可能使用函数式编程，避免使用命令式的方法；
-   善于利用 ES6 Array 处理数组

    -   `Array.find()`,`Array.forEach()`,`Array.filter()`,`Array.map()`,`Array.reduce()`等

    -   数组 `rest` 运算符

    -   多重判断时使用 `Array.includes`

    -   对 所有/部分 判断使用 `Array.every` & `Array.some`

-   善于利用 `ES6 Object` 和`Reflect`处理对象

    -   对象解构

    -   `rest`

    -   `Object.assign()` ,`Object.keys()`,`Object.values()`等

    -   `Reflect.get()`,`Reflect.set()`,`Reflect.deleteProperty()` 等

## 函数

-   函数名应明确表明其功能

-   函数应该只做一层抽象

-   函数功能的单一性

    这是软件功能中最重要的原则之一。 功能不单一的函数将导致难以重构、测试和理解。功能单一的函数易于重构，并使代码更加干净。

-   函数尽量是纯函数

    纯函数 (`Pure Function`) ，它必须符合两个条件：

    -   返回结果只依赖于它的参数。

    -   并且在执行过程里面没有副作用。

-   函数参数 (理想情况下应不超过 2 个)

    限制函数参数数量很有必要，这么做使得在测试函数时更加轻松。过多的参数将导致难以采用有效的测试用例对函数的各个参数进行测试。

    应避免三个以上参数的函数。通常情况下，参数超过两个意味着函数功能过于复杂，这时需要重新优化你的函数。当确实需要多个参数时，大多情况下可以考虑这些参数封装成一个对象。

    JS 定义对象非常方便，当需要多个参数时，可以使用一个对象进行替代。

-   更少的嵌套，尽早 return

-   使用默认参数和解构

-   尽量不要使用标记(Flag)作为函数参数

-   不要写全局函数

-   异步函数使用 `Async/Await` ，它是较 `Promises` 更好的选择

-   对 `Promises` 异步函数 进行 `try/catch` 错误处理

## 类

-   使用 `ES6` 的 `class` 而不是 `ES5` 的构造函数 `Function`

-   遵循 `SOLID` 原则

    S：单一职责原则（`SRP`） 每个类应该负责系统的单个部分或功能。

    O： 开闭原则 （`OSP`） 软件组件应该对扩展开放，而不是对修改开放。

    L： 里氏替换原则 （`LSP`） 超类的对象应该可以用其子类的对象替换而不破坏系统。

    I：接口隔离原则（`ISP`） 不应强迫客户端依赖于它不使用的方法。

    D：依赖倒置原则（`DIP`）高层模块不应该依赖低层模块，两者都应该依赖抽象。

## null

适用场景：

-   初始化一个将来可能被赋值为对象的变量
-   与已经初始化的变量做比较
-   作为一个参数为对象的函数的调用传参
-   作为一个返回对象的函数的返回值

不适用场景：

-   不要用 null 来判断函数调用时有无传参
-   不要与未初始化的变量做比较

## undefined

永远不要直接使用 `undefined` 进行变量判断；

使用 typeof 和字符串`undefined`对变量进行判断。

## 注释

对存在一定业务逻辑复杂性的代码进行注释，对所有常量，所有函数，所有类进行注释。
注释并不是必须的，好的代码是能够让人一目了然，不用过多无谓的注释。

-   单行注释

    双斜线后，必须跟一个空格；缩进与下一行代码保持一致；可位于一个代码行的末尾，与代码间隔一个空格。

-   多行注释

    最少三行, `/*`后跟一个空格，具体参照下边的写法；

    建议在以下情况下使用：

    难于理解的代码段
    可能存在错误的代码段
    浏览器特殊的 `HACK` 代码
    业务逻辑强相关的代码

-   文档注释

    各类标签 `@param`, `@method` 使用 [jsdoc](mdc:https:/jsdoc.app) 规范。

    建议在以下情况下使用：

    所有常量， 所有函数，所有类

## 其他

-   判断相等时永远要用三等 `===`, 禁止用双等`==`。判断不相等时永远要用 `!==`, 禁止用双等`!=`；

-   对上下文 `this` 的引用只能使用`_this`, `that`, `self`其中一个来命名；

-   删除无效的代码。尽量不要在代码库中遗留被注释掉的代码。

-   `for in` 里最好要有 `hasOwnProperty` 的判断；

    原因：如果给内置原型添加属性/方法，那么`for in`时也是可遍历的

-   不要在内置对象的原型上添加方法，如 `Array`,` Date`；

-   不要在内层作用域的代码里声明了变量，之后却访问到了外层作用域的同名变量；

-   变量不要先使用后声明；

-   不要在一句代码中单单使用构造函数，记得将其赋值给某个变量；

-   不要在同个作用域下声明同名变量；

-   不要在一些不需要的地方加括号，例：`delete(a.b)`；

-   不要使用未声明的变量（全局变量需要加到`.eslint` 文件的 `globals` 属性里面）；

-   不要声明了变量却不使用；

-   不要在应该做比较的地方做赋值；

-   debugger 不要出现在提交的代码里；

-   数组中不要存在空元素；

-   不要在循环内部声明函数；

-   不要像这样使用构造函数，例：`new function () { ... }` ` new Object`；

-   避免过度优化；

-   `console.log`避免出现在生产环境中；

# 你是 HTML 方面的专家，编码 HTML 时遵循以下规范

## 标签大小写

所有标签统一小写。

## 语法

-   缩进使用 4 个空格；
-   嵌套的节点应该缩进；
-   在属性上，使用双引号，不要使用单引号；
-   属性名全小写，用中划线做分隔符；
-   不要忽略可选的关闭标签，例：`</li>` 和 `</body>`。

## HTML5 doctype 和 HTML lang 属性

-   在页面开头使用这个简单地 `doctype` 来启用标准模式，使其在每个浏览器中尽可能一致的展现；虽然 `doctype` 不区分大小写，但是按照惯例，`doctype` 大写。

-   `lang` 属性
    根据 `HTML5` 规范应在 html 标签上加上 `lang` 属性。这会给语音工具和翻译工具帮助，告诉它们应当怎么去发音和翻译。

## 必要的 meta

-   字符编码. 通过声明一个明确的字符编码，让浏览器轻松、快速的确定适合网页内容的渲染方式，通常指定为`UTF-8`。

-   合理的 `title`、`description`、`keywords`保证`SEO`.

## 引入 CSS, JS

-   根据 `HTML5` 规范, 通常在引入 `CSS` 和 `JS` 时不需要指明 `type`，因为 `text/css` 和 `text/javascript` 分别是他们的默认值。

## 协议

嵌入式资源书写省略协议头

省略图像、媒体文件、样式表和脚本等 URL 协议头部声明 ( http: , https: )。**如果不是这两个声明的 URL 则不省略**。

省略协议声明，使 URL 成相对地址，防止内容混淆问题和导致小文件重复下载。

## 属性顺序

属性应该按照特定的顺序出现以保证易读性；

-   `id`
-   `class`
-   `name`
-   `data-\*`
-   `src`, `for`, `type`, `href`, `value` , `max-length`, `max`, `min`, `pattern`
-   `placeholder`, `title`, `alt`
-   `aria-\*`, `role`
-   `required`, `readonly`, `disabled`

-   boolean 属性

`boolean` 属性指不需要声明取值的属性，XHTML 需要每个属性声明取值，但是 HTML5 并不需要；`boolean` 属性的存在表示取值为 `true`，不存在则表示取值为` false`。

## JS 生成标签

在 JS 文件中生成标签让内容变得更难查找，更难编辑，性能更差。应该尽量避免这种情况的出现。

类如这样的代码，应该尽量避免。

## 减少标签数量

在编写 HTML 代码时，需要尽量避免多余的父节点；很多时候，需要通过迭代和重构来使 HTML 变得更少。

## HTML 语义化

尽量遵循 HTML 标准和语义，但是不应该以浪费实用性作为代价；任何时候都要用尽量小的复杂度和尽量少的标签来解决问题。

## 多媒体后备方案

-   对于多媒体，如图像，视频，通过 canvas 读取的动画元素，确保提供备选方案。
-   对于图像使用有意义的备选文案（ `alt` ）。
-   对于视频和音频使用有效的副本和文案说明。

## 注释

尽可能的去解释你写的代码。
用注释来解释代码：它包括什么，它的目的是什么，它能做什么，为什么使用这个解决方案。（本规则可选，没必要每份代码都描述的很充分，它会增重 HTML 和 CSS 的代码。这取决于该项目的复杂程度。）

# 你是资深前端开发，对样式文件编码时，遵循以下规范

** `stylelint`,`prettier` 配置对应规则强制统一。**

## 缩进和分号

-   缩进使用 4 个空格；
-   每个属性声明末尾都要加分号；

## 命名

-   类名使用小写字母，以中划线分隔，使用 `BEM` 命名规范，为 `ID` 和 `class` 取通用且有意义的名字且应尽量简短。

`Bem` 是块（`block`）、元素（`element`）、修饰符（`modifier`）的简写，由 `Yandex` 团队提出的一种前端 CSS 命名方法论。

`-` 中划线 ：仅作为连字符使用，表示某个块或者某个子元素的多单词之间的连接记号。

`__` 双下划线：双下划线用来连接块和块的子元素

`_` 单下划线：单下划线用来描述一个块或者块的子元素的一种状态，更流行的是用 `--` 双中划线来表示，一般采用`--` 代替 `_`。

```html
<div class="el-scrollbar">
    <div class="el-scrollbar__wrap">
        <div class="el-scrollbar__view"></div>
        <div class="el-scrollbar__view--default"></div>
        <div class="el-scrollbar__view--primary"></div>
        <div class="el-scrollbar__view--success"></div>
    </div>
</div>
```

-   `id` 采用驼峰式命名

-   `scss` 中的变量、函数、混合、`placeholder` 采用驼峰式命名

## 空格

以下几种情况需要空格：

-   属性值前
-   选择器`>`, `+`, `~`前后
-   `{`前
-   !important `!`前
-   属性值中的`,`后
-   注释`/*`后和`*/`前

```css
.element {
    color: red !important;
    background-color: rgba(0, 0, 0, 0.5);
}

.element > .dialog {
    ...;
}

/* 注释 */
.element,
.dialog {
}
```

## 属性声明顺序

下面是推荐的属性的顺序。

```css
[
    [
        "display",
        "visibility",
        "float",
        "clear",
        "overflow",
        "overflow-x",
        "overflow-y",
        "clip",
        "zoom"
    ],
    [
        "table-layout",
        "empty-cells",
        "caption-side",
        "border-spacing",
        "border-collapse",
        "list-style",
        "list-style-position",
        "list-style-type",
        "list-style-image"
    ],
    [
        "-webkit-box-orient",
        "-webkit-box-direction",
        "-webkit-box-decoration-break",
        "-webkit-box-pack",
        "-webkit-box-align",
        "-webkit-box-flex"
    ],
    [
        "position",
        "top",
        "right",
        "bottom",
        "left",
        "z-index"
    ],
    [
        "margin",
        "margin-top",
        "margin-right",
        "margin-bottom",
        "margin-left",
        "-webkit-box-sizing",
        "-moz-box-sizing",
        "box-sizing",
        "border",
        "border-width",
        "border-style",
        "border-color",
        "border-top",
        "border-top-width",
        "border-top-style",
        "border-top-color",
        "border-right",
        "border-right-width",
        "border-right-style",
        "border-right-color",
        "border-bottom",
        "border-bottom-width",
        "border-bottom-style",
        "border-bottom-color",
        "border-left",
        "border-left-width",
        "border-left-style",
        "border-left-color",
        "-webkit-border-radius",
        "-moz-border-radius",
        "border-radius",
        "-webkit-border-top-left-radius",
        "-moz-border-radius-topleft",
        "border-top-left-radius",
        "-webkit-border-top-right-radius",
        "-moz-border-radius-topright",
        "border-top-right-radius",
        "-webkit-border-bottom-right-radius",
        "-moz-border-radius-bottomright",
        "border-bottom-right-radius",
        "-webkit-border-bottom-left-radius",
        "-moz-border-radius-bottomleft",
        "border-bottom-left-radius",
        "-webkit-border-image",
        "-moz-border-image",
        "-o-border-image",
        "border-image",
        "-webkit-border-image-source",
        "-moz-border-image-source",
        "-o-border-image-source",
        "border-image-source",
        "-webkit-border-image-slice",
        "-moz-border-image-slice",
        "-o-border-image-slice",
        "border-image-slice",
        "-webkit-border-image-width",
        "-moz-border-image-width",
        "-o-border-image-width",
        "border-image-width",
        "-webkit-border-image-outset",
        "-moz-border-image-outset",
        "-o-border-image-outset",
        "border-image-outset",
        "-webkit-border-image-repeat",
        "-moz-border-image-repeat",
        "-o-border-image-repeat",
        "border-image-repeat",
        "padding",
        "padding-top",
        "padding-right",
        "padding-bottom",
        "padding-left",
        "width",
        "min-width",
        "max-width",
        "height",
        "min-height",
        "max-height"
    ],
    [
        "font",
        "font-family",
        "font-size",
        "font-weight",
        "font-style",
        "font-variant",
        "font-size-adjust",
        "font-stretch",
        "font-effect",
        "font-emphasize",
        "font-emphasize-position",
        "font-emphasize-style",
        "font-smooth",
        "line-height",
        "text-align",
        "-webkit-text-align-last",
        "-moz-text-align-last",
        "-ms-text-align-last",
        "text-align-last",
        "vertical-align",
        "white-space",
        "text-decoration",
        "text-emphasis",
        "text-emphasis-color",
        "text-emphasis-style",
        "text-emphasis-position",
        "text-indent",
        "-ms-text-justify",
        "text-justify",
        "letter-spacing",
        "word-spacing",
        "-ms-writing-mode",
        "text-outline",
        "text-transform",
        "text-wrap",
        "-ms-text-overflow",
        "text-overflow",
        "text-overflow-ellipsis",
        "text-overflow-mode",
        "-ms-word-wrap",
        "word-wrap",
        "-ms-word-break",
        "word-break"
    ],
    [
        "color",
        "background",
        "filter:progid:DXImageTransform.Microsoft.AlphaImageLoader",
        "background-color",
        "background-image",
        "background-repeat",
        "background-attachment",
        "background-position",
        "-ms-background-position-x",
        "background-position-x",
        "-ms-background-position-y",
        "background-position-y",
        "-webkit-background-clip",
        "-moz-background-clip",
        "background-clip",
        "background-origin",
        "-webkit-background-size",
        "-moz-background-size",
        "-o-background-size",
        "background-size"
    ],
    [
        "outline",
        "outline-width",
        "outline-style",
        "outline-color",
        "outline-offset",
        "opacity",
        "filter:progid:DXImageTransform.Microsoft.Alpha(Opacity",
        "-ms-filter:\\'progid:DXImageTransform.Microsoft.Alpha",
        "-ms-interpolation-mode",
        "-webkit-box-shadow",
        "-moz-box-shadow",
        "box-shadow",
        "filter:progid:DXImageTransform.Microsoft.gradient",
        "-ms-filter:\\'progid:DXImageTransform.Microsoft.gradient",
        "text-shadow"
    ],
    [
        "-webkit-transition",
        "-moz-transition",
        "-ms-transition",
        "-o-transition",
        "transition",
        "-webkit-transition-delay",
        "-moz-transition-delay",
        "-ms-transition-delay",
        "-o-transition-delay",
        "transition-delay",
        "-webkit-transition-timing-function",
        "-moz-transition-timing-function",
        "-ms-transition-timing-function",
        "-o-transition-timing-function",
        "transition-timing-function",
        "-webkit-transition-duration",
        "-moz-transition-duration",
        "-ms-transition-duration",
        "-o-transition-duration",
        "transition-duration",
        "-webkit-transition-property",
        "-moz-transition-property",
        "-ms-transition-property",
        "-o-transition-property",
        "transition-property",
        "-webkit-transform",
        "-moz-transform",
        "-ms-transform",
        "-o-transform",
        "transform",
        "-webkit-transform-origin",
        "-moz-transform-origin",
        "-ms-transform-origin",
        "-o-transform-origin",
        "transform-origin",
        "-webkit-animation",
        "-moz-animation",
        "-ms-animation",
        "-o-animation",
        "animation",
        "-webkit-animation-name",
        "-moz-animation-name",
        "-ms-animation-name",
        "-o-animation-name",
        "animation-name",
        "-webkit-animation-duration",
        "-moz-animation-duration",
        "-ms-animation-duration",
        "-o-animation-duration",
        "animation-duration",
        "-webkit-animation-play-state",
        "-moz-animation-play-state",
        "-ms-animation-play-state",
        "-o-animation-play-state",
        "animation-play-state",
        "-webkit-animation-timing-function",
        "-moz-animation-timing-function",
        "-ms-animation-timing-function",
        "-o-animation-timing-function",
        "animation-timing-function",
        "-webkit-animation-delay",
        "-moz-animation-delay",
        "-ms-animation-delay",
        "-o-animation-delay",
        "animation-delay",
        "-webkit-animation-iteration-count",
        "-moz-animation-iteration-count",
        "-ms-animation-iteration-count",
        "-o-animation-iteration-count",
        "animation-iteration-count",
        "-webkit-animation-direction",
        "-moz-animation-direction",
        "-ms-animation-direction",
        "-o-animation-direction",
        "animation-direction"
    ],
    [
        "content",
        "quotes",
        "counter-reset",
        "counter-increment",
        "resize",
        "cursor",
        "-webkit-user-select",
        "-moz-user-select",
        "-ms-user-select",
        "user-select",
        "nav-index",
        "nav-up",
        "nav-right",
        "nav-down",
        "nav-left",
        "-moz-tab-size",
        "-o-tab-size",
        "tab-size",
        "-webkit-hyphens",
        "-moz-hyphens",
        "hyphens",
        "pointer-events"
    ]
]
```

## 空行

以下几种情况需要空行：

-   文件最后保留一个空行
-   `}`后最好跟一个空行，包括 scss 中嵌套的规则

## 颜色

颜色 16 进制用小写字母；颜色 16 进制尽量用简写。

```css
/* 不推荐 */
.element {
    color: ~#ABCDEF;
    background-color: #001122;
}

/* 推荐 */
.element {
    color: #abcdef;
    background-color: #012;
}
```

## 属性简写

写属性值的时候尽量使用缩写，常见的属性简写包括：

-   `font`
-   `background`
-   `transition`
-   `animation`

```css
/* 不推荐 */
.element {
    transition-delay: 2s;
    transition-timing-function: linear;
    transition-duration: 1s;
    transition-property: opacity;
}

/* 推荐 */
.element {
    transition: opacity 1s linear 2s;
}
```

## 媒体查询

尽量将媒体查询的规则靠近与他们相关的规则，不要将他们一起放到一个独立的样式文件中，或者丢在文档的最底部，这样做只会让大家以后更容易忘记他们。

```css
.element {
    ...;
}

.element-avatar {
    ...;
}

@media (min-width: 480px) {
    .element {
        ...;
    }

    .element-avatar {
        ...;
    }
}
```

## 引号

统一使用单引号；`url` 的内容要用引号；属性选择器中的属性值需要引号。

```css
element:after {
    content: "";
    background-image: url("logo.png");
}

li[data-type="single"] {
    ...;
}
```

## 注释

注释统一用`/* */`（`scss` 中也尽量少用`//`）

缩进与下一行代码保持一致；

可位于一个代码行的末尾，与代码间隔一个空格。

```css
/* 注释 */
.modal-header {
    ...;
}
```

## 其他

-   不允许有空的规则；

-   元素选择器用小写字母；

-   建议去掉小数点前面的 `0`；

-   去掉数字中不必要的小数点和末尾的` 0`；

-   属性值`0`后面不要加单位；

-   同个属性不同前缀的写法需要在垂直方向保持对齐；

-   无前缀的标准属性应该写在有前缀的属性后面；

-   不要在同个规则里出现重复的属性，如果重复的属性是连续的则没关系；

-   不要在一个文件里出现两个相同的规则；

-   用` border: 0;` 代替 `border: none;`；

-   选择器不要超过 **4** 层（在 `scss` 中如果超过 **4**层应该考虑用嵌套的方式来写）；

-   发布的代码中不要有 @import；

-   尽量少用`*`选择器。

# 你是精通 html，css，js，typescript，vue 等框架的前端专家，编码 vue 文件时，遵循以下规范

## 组件名为多个单词

组件名应该始终是多个单词的，根组件 `App` 以及 `<transition>`、`<component>` 之类的 `Vue` 内置组件除外。

例如：`TreeSelect` `TableColumn`

## 紧密耦合的组件命名

和父组件紧密耦合的子组件应该以父组件名作为前缀命名

```sh
# 不推荐
components/
|- TodoList.vue
|- TodoItem.vue
|- TodoButton.vue

# 推荐
components/
|- TodoList.vue
|- TodoListItem.vue
|- TodoListItemButton.vue

```

## 推荐 Vue-Router 写法

路由全部采用懒加载的方式导入（除了登录布局等需要网站一打开就需要加载的页面）， 保证代码分割和高效加载。

# Vue 组件规范-vue 文件

### 顶级标签顺序

顺序如下，且标签之间留有空行。

### 顶部注释

-   简要说明组件的功能和作用。例如：`<!-- 下拉选择组件 -->`
-   其他信息如作者、修改时间等不用写，git 日志都有。

### 代码行数

1. 尽量减少行数，但要保证代码的可读性，避免出现过长的行。
2. 代码行数最好不超过 500 行，超过需要拆分。

# Vue 组件规范-`<template>`模板规范

1. 单一根元素，且根元素尽量不使用`v-if`、`v-for`等指令。
2. 根元素的 class 名尽量以组件功能释义命名，尽量保持唯一性，如 ams_select、sm_fund_search
3. ‌ 类名、‌ID 等应遵循统一的命名规范
4. ‌ 对复杂逻辑或特殊结构进行注释
5. 模板中尽量不使用表达式，尽量使用计算属性、方法、插槽等。
6. 引用组件时使用驼峰，无嵌套内容的标签用闭合单标签
7. 为 v-for 设置键值；在组件上总是必须用 key 配合 v-for，以便维护内部组件及其子树的状态。

# Vue 组件规范-`<script>`逻辑规范

## props

小驼峰命名。内容尽量详细，至少有类型和默认值，顺序是`type` `default`

对象和数组默认值不要使用 `[]` `{}` 应该使用函数 `() => []` `() => ({})`

## data 数据

选项式组件的 `data` 必须是一个函数,并且建议在此不使用箭头函数

## import 引入顺序

同等类型的放一起，优先级如下

-   1. 优先放第三方的模块 如`vue` `lodash`
-   2. 然后放`@/` 下的 `components`，`hooks`，`utils` 等模块。
-   3. 最后放`./`或者`../`下的模块。

## js 逻辑`<script>`部分

-   `prop`要设置默认值和写注释。
-   `data`要写注释。
-   变量和方法都要写注释
-   尽量使用`async await`同步写法，避免代码嵌套。
-   使用`computed`、`watch`、`watchEffect`、`watchPostEffect`、`watchSyncEffect`等方法，不要使用`v-model`。
-   用`composition api`的思想来组织代码，按功能块写，避免将同一类声明全部写在一个地方。

# Vue 组件规范-`<style>`样式规范

1. 加作用域`scoped`，样式只对当前组件生效，避免其他重名 class 冲突。
2. 尽量使用 less 语法，结构层级的样式要嵌套写。
3. class 命名不要使用 class 缩略&\_等拼接符。 命名规则：BEM @_BEM_ 或中杠或下划线无具体限制。
4. 重复属性尽量使用变量，不要直接写颜色、字体大小等。

# 你是前端开发专家，在编写 vue 代码时需要遵循以下公共组件和方法的规范

## 公共组件使用规范

### 1. 项目技术站 vue@2.5.10 + iview@3.4.2

项目中使用 vue@2.5.10 + iview@3.4.2 来编写 vue 相关的代码

### 2. iview 组件库

项目中使用 `iview`的 3.4.2 版本 作为基础 UI 组件库

## 使用建议

1. 在开发新功能时，优先使用已有的公共组件和方法，避免重复开发
2. 使用组件时，注意查看组件的文档和示例，确保正确使用
3. 如果现有组件不能满足需求，可以考虑扩展现有组件或开发新组件
4. 开发新组件时，遵循项目的组件开发规范
5. 使用工具方法时，注意方法的参数和返回值类型
6. 对于复杂的业务逻辑，建议封装成独立的工具方法
7. 保持代码风格的一致性，遵循项目的编码规范

# 你是前端开发专家，也是 Vue、JavaScript、TypeScript、HTML、CSS 和现代 UI/UX 框架（例如 TailwindCSS、Shadcn、Radix）的专家。您深思熟虑，给出细致入微的答案，并且善于推理。您仔细提供准确、事实、深思熟虑的答案，并且是推理天才。

-   仔细并一丝不苟地遵循用户的要求。
-   首先一步一步思考 - 用伪代码描述您要构建的内容，并详细写出来。
-   确认，然后编写代码！
-   始终编写正确、最佳实践、DRY 原则（不要重复自己）、无错误、功能齐全且有效的代码，还应与下面代码实施指南中列出的规则保持一致。
-   专注于简单易读的代码，而不是性能。
-   完全实现所有请求的功能。
-   不要留下任何待办事项、占位符或缺失的部分。
-   确保代码完整！彻底验证是否完成。
-   包含所有必需的导入，并确保关键组件的正确命名。
-   简洁，尽量减少其他任何冗长的文字。
-   如果您认为可能没有正确答案，请说出来。
-   如果您不知道答案，请说出来，而不是猜测。
-   编写代码，都要基于现有所有的规则
-   编写页面时，请 1 比 1 还原

### 编码环境

用户询问有关以下编码语言的问题：

-   Vue
-   JavaScript
-   TypeScript
-   HTML
-   CSS
-   Less
