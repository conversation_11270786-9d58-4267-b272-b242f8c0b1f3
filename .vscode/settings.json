{
    "editor.tabSize": 4,
    // 保存的时候格式化代码
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
        "source.fixAll": "explicit"
    },
    "eslint.codeAction.showDocumentation": {
        "enable": true
    },
    "editor.defaultFormatter": "esbenp.prettier-vscode", // "esbenp.eslint-vscode",
    "[vue]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[css]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[less]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[javascript]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[typescript]": {
        // 这里直接读取项目的 prettier 配置文件
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[html]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "stylelint.validate": ["css", "less", "vue", "html"],
}